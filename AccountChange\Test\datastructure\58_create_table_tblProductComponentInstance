CREATE TABLE `userdata`.`tblProductComponentInstance` (
  `intProductComponentInstanceID` int(10) unsigned NOT NULL auto_increment,
  `intComponentID` int(10) unsigned NOT NULL default '0',
  `intProductComponentID` int(10) unsigned NOT NULL default '0',
  `intStatusID` int(10) unsigned NOT NULL default '0',
  `intTariffID` int(10) unsigned NOT NULL default '0',
  `dteNextInvoice` date default NULL,
  `dtmStart` datetime NOT NULL default '0000-00-00 00:00:00',
  `dtmEnd` datetime default NULL,
  `stmLastUpdate` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (`intProductComponentInstanceID`),
  <PERSON>EY `intProductComponentID` (`intProductComponentID`),
  K<PERSON>Y `intComponentID` (`intComponentID`),
  KEY `intStatusID` (`intStatusID`),
  K<PERSON>Y `intTariffID` (`intTariffID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1