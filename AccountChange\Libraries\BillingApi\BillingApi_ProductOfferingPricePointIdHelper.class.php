<?php

use Plusnet\BillingApiClient\Entity\AvailableProduct;
use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;

class AccountChange_BillingApi_ProductOfferingPricePointIdHelper
{
    /**
     * Format used for product offering price point id
     *
     * @var string
     */
    const PRODUCT_OFFERING_PRICE_POINT_ID_FORMAT = '%s:%s';

    /**
     * Generates a unique product offering price point id from an instance of
     * Plusnet\BillingApiClient\Entity\AvailableProduct
     *
     * @param Plusnet\BillingApiClient\Entity\AvailableProduct $availableProduct
     *
     * @return string
     */
    public static function generateIdFromAvailableProduct($availableProduct)
    {
        if ($availableProduct === null) {
            return null;
        }

        return sprintf(
            self::PRODUCT_OFFERING_PRICE_POINT_ID_FORMAT,
            (string) $availableProduct->getProductOfferingId(),
            (string) $availableProduct->getPricePointId());
    }

    /**
     * Generates a unique product offering price point id from an instance of
     * Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair
     *
     * @param Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair $productOfferingPricePointPair
     *
     * @return string
     */
    public static function generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair)
    {
        if ($productOfferingPricePointPair === null) {
            return null;
        }

        return sprintf(
            self::PRODUCT_OFFERING_PRICE_POINT_ID_FORMAT,
            $productOfferingPricePointPair->getProductOfferingId(),
            $productOfferingPricePointPair->getPricePointId());
    }
}
