server: coredb
role: slave
rows: single
statement:

SELECT
    CONCAT_WS(" ", u.forenames, u.surname) as name,
    u.email,
    u.mobile AS mobilePhone,
    ad.house AS thoroughfareNumber,
    ad.street AS thoroughfare,
    ad.town AS postTown,
    ad.postcode AS postCode,
    ad.county,
    ad.country
FROM userdata.accounts ac
INNER JOIN userdata.customers c
    ON c.customer_id = ac.customer_id
INNER JOIN userdata.addresses ad
    ON ad.address_id = c.primary_address_id
INNER JOIN userdata.users u
    ON u.user_id = c.primary_user_id
WHERE ac.account_id = :accountId;
