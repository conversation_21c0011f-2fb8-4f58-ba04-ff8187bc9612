<?php
/**
 * Account Change Products Action
 *
 * @category  AccountChnage_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-17
 */
/**
 * Account Change Products Action
 *
 * This class represents the logic for configuring specific proucts
 *
 * e.g. Mailbox, Community, Firewall
 *
 * @category  AccountChange_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Action_Products extends AccountChange_Action
{
    /**
     * Array of component types we simply want to ignore in the legacy change
     * These component types have been dealt with via this app,
     * and therefore do not need to be dealt with by legacy code
     *
     * @var array
     */
    private $arrLegacyComponentTypesToIgnore;

    /**
     * Main execute of the action
     *
     * @return void
     */
    public function execute()
    {
        // Get registry information
        $this->registry = AccountChange_Registry::instance();
        $this->arrLegacyComponentTypesToIgnore = $this->registry->getEntry('arrLegacyComponentTypesToIgnore');

        $this->processMailboxProduct();
        $this->processCommunityProduct();
        $this->processFirewallProduct();

        // Replace registry information
        $this->registry->setEntry('arrLegacyComponentTypesToIgnore', $this->arrLegacyComponentTypesToIgnore);
    }

    /**
     * Process the mailbox product
     *
     * @return void
     */
    protected function processMailboxProduct()
    {
        $arrComponents = array();

        $this->includeLegacyFiles();

        // Legacy call
        $arrComponents = userdata_component_find(
            array(
                'service_id' => $this->intServiceId,
                'type' => array(
                    COMPONENT_EMAIL,
                    COMPONENT_PLUS_EMAIL,
                    COMPONENT_F9_EMAIL,
                    COMPONENT_FOL_EMAIL
                ),
                'status' => array(
                    'unconfigured',
                    'queued-activate',
                    'queued-reactivate',
                    'active'
                )
            )
        );

        if (count($arrComponents) == 1) {

            $objProduct = AccountChange_Product_Manager::factoryUsingComponentId(
                AccountChange_Product_Manager::ACTION_REFRESH,
                $arrComponents[0]['component_id'],
                'Mailbox'
            );

            Dbg_Dbg::write(
                "AccountChange_Action_Products::processMailboxProduct found component " .
                "({$objProduct->getProductId()}) with an instance of ({$objProduct->getComponentId()})",
                'AccountChange'
            );

            $this->arrLegacyComponentTypesToIgnore[] = $objProduct->getProductId();

            $objProduct->execute();
        }
    }

    /**
     * Process the community product
     *
     * @return void
     */
    protected function processCommunityProduct()
    {
        $arrComponents = array();

        $this->includeLegacyFiles();

        // Legacy call
        $arrComponents = userdata_component_find(
            array('service_id' => $this->intServiceId,
            'type' => COMPONENT_COMMUNITY_SITE)
        );

        if (count($arrComponents) == 1) {

            $objProduct = AccountChange_Product_Manager::factoryUsingComponentId(
                AccountChange_Product_Manager::ACTION_REFRESH,
                $arrComponents[0]['component_id'],
                'Community'
            );

            Dbg_Dbg::write(
                "AccountChange_Action_Products::processCommunityProduct found component " .
                "({$objProduct->getProductId()}) with an instance of ({$objProduct->getComponentId()})",
                'AccountChange'
            );

            $this->arrLegacyComponentTypesToIgnore[] = $objProduct->getProductId();

            $objProduct->execute();
        }
    }

    /**
     * Process the firewall product
     *
     * @return void
     */
    protected function processFirewallProduct()
    {
        // P79818: we don't want the system to disable the firewall when the customer goes through an AccountChange
        return;

        $arrComponents = array();

        $this->includeLegacyFiles();

        // Legacy call
        $arrComponents = userdata_component_find(
            array(
                'service_id' => $this->intServiceId,
                'type'       => array(COMPONENT_GENERIC_FIREWALL),
                'status'     => array('unconfigured', 'active')
            )
        );

        if (count($arrComponents) == 1) {

            $objProduct = AccountChange_Product_Manager::factoryUsingComponentId(
                AccountChange_Product_Manager::ACTION_REFRESH,
                $arrComponents[0]['component_id'],
                'Firewall'
            );

            Dbg_Dbg::write(
                "AccountChange_Action_Products::processFirewallProduct found component " .
                "({$objProduct->getProductId()}) with an instance of ({$objProduct->getComponentId()})",
                'AccountChange'
            );

            $this->arrLegacyComponentTypesToIgnore[] = $objProduct->getProductId();

            $objProduct->execute();
        }
    }

    /**
     * Include legacy files in a way that can be mocked
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/class-access.inc';
        require_once '/local/data/mis/database/database_libraries/config-access.inc';
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
        require_once '/local/data/mis/database/database_libraries/cbc-access.inc';
    }
}
