<?php
/**
 * Scheduled Change Wlr Test File
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @link       link
 */
/**
 * Test file for AccountChange_ScheduledChange_Wlr
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @link       link
 */
class AccountChange_ScheduledChange_WlrDbTest extends Plusnet_Database_TestCase
{
    protected $arrServers = array(
        'Coredb_master' => array(),
        'Coredb_slave' => array()
    );

    protected $arrDataStructureDirectories = array('/local/codebase2005/modules/AccountChange/Test/datastructure/');

    protected $dbName = 'dbSystemEvents';

    protected $dataSet = '/local/codebase2005/modules/AccountChange/Test/dataset/ScheduledChangeData.xml';

    /**
     * PHPUnit tearDown functionality
     *
     * (non-PHPdoc)
     *
     * @see Plusnet_Database_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        parent::tearDown();

        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Test that the call to the database is correct
     *
     * @param int    $serviceId   Service Id
     * @param string $productName Product Name
     * @param string $changeDate  Change date
     * @param bool   $hasChange   Is there any scheduled change data available
     *
     * @covers AccountChange_ScheduledChange_Wlr::__construct
     * @covers AccountChange_ScheduledChange_Wlr::getNewProductName
     * @covers AccountChange_ScheduledChange_Wlr::getChangeDate
     * @covers AccountChange_ScheduledChange_Wlr::hasScheduledChange
     * @covers AccountChange_IScheduledChange
     *
     * @dataProvider provideDataForConstructor
     *
     * @return void
     */
    public function testConstructorCorrectlyPullsBackDataFromDatabase($serviceId, $productName, $changeDate, $hasChange)
    {
        $change = new AccountChange_ScheduledChange_Wlr($serviceId);

        $actualProductName = $change->getNewProductName();
        $actualChangeDate = $change->getChangeDate();
        $actualChange = $change->hasScheduledChange();

        $this->assertEquals($productName, $actualProductName);
        $this->assertEquals($changeDate, $actualChangeDate, "Expected to get a date back");
        $this->assertEquals($hasChange, $actualChange);
    }

    /**
     * Data Provider for testConstructorCorrectlyPullsBackDataFromDatabase
     *
     * @return array
     */
    public function provideDataForConstructor()
    {
        // Service Id, New Product Name, Change Date, Is there a change
        return array(
            // Data Set 0
            // Account has scheduled change to Product Two
            array(
                919,
                'Product Two',
                I18n_Date::fromString('2030-09-01', 'UTC'),
                true
            ),

            // Data Set 1
            // Account has a completed scheduled change to Product Two
            array(
                920,
                null,
                null,
                false
            ),
        );
    }

    /**
     * Test for getData
     *
     * @covers AccountChange_ScheduledChange_Adsl::getData
     *
     * @return void
     */
    public function testGetDataReturnsArray()
    {
        $change = new AccountChange_ScheduledChange_Wlr(919);

        $data = $change->getData();

        $this->assertInternalType('array', $data);
        $this->assertEquals(1, $data['intScheduleId']);
    }

    /**
     * Test the cancellation requirement
     *
     * @param int $serviceId Service Id
     *
     * @covers AccountChange_ScheduledChange_Wlr::__construct
     * @covers AccountChange_ScheduledChange_Wlr::cancelChange
     *
     * @dataProvider provideDataForCancel
     *
     * @return void
     */
    public function testCancelMarksDtmCancelledAsDate($serviceId)
    {
        $change = $this->getMock(
            'AccountChange_ScheduledChange_Wlr',
            array('cancelRetentionOffer'),
            array($serviceId)
        );

        $change->expects($this->once())
            ->method('cancelRetentionOffer');

        $change->cancelChange(new Auth_BusinessActor());
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $table = $this->getConnection()
            ->createDataSet()
            ->getTable('tblScheduledEvent');

        $this->assertNotEquals(
            null,
            $this->getConnection()
                ->createDataSet()
                ->getTable('tblScheduledEvent')
                ->getValue(0, 'dtmCancelled')
        );
    }

    /**
     * Data Provider for testCancelMarksAdslEntryAsActiveNoAndChangeCompleteNo
     *
     * @return array
     */
    public function provideDataForCancel()
    {
        // Service Id
        return array(
            // Data Set 0
            // Account has scheduled change to Product Two
            array(
                919
            ),
        );
    }

    /**
     * Test that getProductType returns Broadband
     *
     * @param array              $offers   Array of offer items
     * @param Auth_businessActor $actioner Business actor performing the action
     *
     * @covers AccountChange_ScheduledChange_Wlr::cancelRetentionOffer
     * @dataProvider provideDataForCancelRetentionOffersTest
     *
     * @return void
     */
    public function testCancelRetentionOffer($offers, $actioner)
    {
        $mockDb = $this->getMock(
            'Db_Adaptor',
            array(
                'cancelRetentionOffer'
            ),
            array('RetentionsTool', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockDb->expects($this->any())
            ->method('cancelRetentionOffer');

        Db_Manager::setAdaptor('RetentionsTool', $mockDb);

        $mockAutheDb = $this->getMock(
            'Db_Adaptor',
            array(
                'getActorByExternalUserId',
                'getBusinessActor'
            ),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockAutheDb->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(2222));

        $mockAutheDb->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue(array(array('intActorId' => 999, 'strUsername' => 'TestUser'))));

        Db_Manager::setAdaptor('Auth', $mockAutheDb);

        $mockAccountChangeDb = $this->getMock(
            'Db_Adaptor',
            array(
                'cancelWlrProductChange',
                'getWlrScheduledChange'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockAccountChangeDb->expects($this->any())
            ->method('cancelWlrProductChange');

        $mockAccountChangeDb->expects($this->any())
            ->method('getWlrScheduledChange')
            ->will($this->returnValue(array('intScheduleId'=>1, 'intProductChangeId' => 2)));

        Db_Manager::setAdaptor('AccountChange', $mockAccountChangeDb);

        $offerManager = $this->getMock(
            'RetentionsTool_OfferManager',
            array('cancelOffers'),
            array(new Auth_BusinessActor())
        );

        $offerManager->addOffers($offers);

        $retentionManager = new RetentionsTool_Manager($offerManager, 1, 1);
        $retentionManager->setRetentionOfferId(11);

        $change = $this->getMock(
            'AccountChange_ScheduledChange_Wlr',
            array('getRetentionManager'),
            array(919)
        );

        $change->expects($this->once())
            ->method('getRetentionManager')
            ->will($this->returnValue($retentionManager));

        $change->cancelRetentionOffer($actioner);
    }

    /**
     * Data provider for testCancelRetentionOffers
     *
     * @return array
     */
    public function provideDataForCancelRetentionOffersTest()
    {
        //Create mock RetentionTool_OfferItem object
        $pendingAdslOffer1 = $this->getMock(
            'RetentionsTool_OfferItem',
            array('cancel'),
            array(new RetentionsTool_Offers_ChargableComponent())
        );

        $pendingAdslOffer1->setRetentionOfferId(1);

        $pendingAdslOffer1->setDiscountValueIncVat(100);
        $pendingAdslOffer1->setDiscountDurationMonths(4);
        $pendingAdslOffer1->setComponentId(null);
        $pendingAdslOffer1->setWlrScheduleId(1);

        $pendingAdslOffer1->expects($this->any())
            ->method('cancel');

        //Create mock RetentionTool_OfferItem object
        $pendingAdslOffer2 = $this->getMock(
            'RetentionsTool_OfferItem',
            array('cancel'),
            array(new RetentionsTool_Offers_ChargableComponent())
        );

        $pendingAdslOffer2->setRetentionOfferId(1);

        $pendingAdslOffer2->setDiscountValueIncVat(100);
        $pendingAdslOffer2->setDiscountDurationMonths(4);
        $pendingAdslOffer2->setComponentId(null);
        $pendingAdslOffer2->setWlrScheduleId(2);

        $pendingAdslOffer2->expects($this->any())
            ->method('cancel');

        $actioner = new Auth_BusinessActor();

        return array(
            array(
                array($pendingAdslOffer1), $actioner
            ),
            array(
                array($pendingAdslOffer2), $actioner
            ),
            array(
                array($pendingAdslOffer1, $pendingAdslOffer2), $actioner
            )
        );
    }

    /**
     * Test for getRetentionManager method (from the parent abstract class)
     *
     * @covers AccountChange_ScheduledChange_Abstract::getRetentionManager
     *
     * @return void
     */
    public function testGetRetentionManager()
    {
        $mockDb = $this->getMock(
            'Db_Adaptor',
            array(
                'getRetentionOfferIdByAdslScheduledChangeEvent',
                'getRetentionOfferIdByWlrScheduledChangeEvent',
                'getOffersByRetentionOfferId',
                'getRetentionOfferRow'
            ),
            array('RetentionsTool', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $retentionOfferRow = array(
            'intTicketId' => 99,
            'intRetentionOfferReasonId' => 2,
            'intRetentionOfferResultId' => 3,
            'intMigrationIspId' => 4,
            'txtComment' => 'test comment',
            'intOfferingActorId' => 1234,
            'dtmOffered' => date('Y-m-d'),
            'dtmAccepted' => date('Y-m-d'),
            'intAcceptingActorId' => 2233
        );

        $mockDb->expects($this->any())
            ->method('getRetentionOfferIdByAdslScheduledChangeEvent')
            ->will($this->returnValue(11));

        $mockDb->expects($this->any())
            ->method('getRetentionOfferIdByWlrScheduledChangeEvent')
            ->will($this->returnValue(22));

        $mockDb->expects($this->any())
            ->method('getRetentionOfferRow')
            ->will($this->returnValue($retentionOfferRow));

        $mockDb->expects($this->any())
            ->method('getOffersByRetentionOfferId')
            ->will($this->returnValue(array()));

        Db_Manager::setAdaptor('RetentionsTool', $mockDb);

        $mockAutheDb = $this->getMock(
            'Db_Adaptor',
            array(
                'getActorByExternalUserId',
                'getBusinessActor'
            ),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockAutheDb->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(2222));

        $mockAutheDb->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue(array(array('intActorId' => 999, 'strUsername' => 'TestUser'))));

        Db_Manager::setAdaptor('Auth', $mockAutheDb);

        $wlrScheduledChange = new AccountChange_ScheduledChange_Wlr(919);

        $retentionManager = $wlrScheduledChange->getRetentionManager(new Auth_BusinessActor());

        $this->assertInstanceOf('RetentionsTool_Manager', $retentionManager);
        $this->assertEquals(2, $retentionManager->getReasonId());
        $this->assertEquals(3, $retentionManager->getResultId());
    }

    /**
     * Test that getProductType returns Phone
     *
     * @covers AccountChange_ScheduledChange_Wlr::getProductType
     *
     * @return void
     */
    public function testGetProductTypeReturnsBroadband()
    {
        $change = new AccountChange_ScheduledChange_Wlr(919);
        $actual = $change->getProductType();

        $this->assertEquals('Phone', $actual);
    }
}
