<?php
/**
 * Broadband Product Price Sort Utility
 *
 * Sort the products that are passed in into ascending price order with one exception,
 * the current product will always be last in the list.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2014-04-22
 */

/**
 * Class to sort available broadband products by price for display in Workplace Account Change tool.
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Product_BroadbandProductPriceSort
{
    /**
     * This sorts an array of products by the intProductCost attribute giving lowest value first.
     * It will also move the current product (both solus and dualplay variants) to the end of the
     * list if it is found.
     *
     * @param array $arrProducts The array of products to display
     *
     * @return array $arrProducts sorted
     */
    public static function sort(array $arrProducts)
    {
        usort($arrProducts, "AccountChange_Product_BroadbandProductPriceSort::sortByPrice");
        return $arrProducts;
    }

    /**
     * A comparator implementation that compares two products.
     *
     * @param array $arrFirstProduct  Product to compare
     * @param array $arrSecondProduct Product to compare
     *
     * @return int Returns > 0 if the price of the first product is greater than the
     * price of the second, 0 if they are equal and < 0 if price of second product
     * is greater than first.
     */
    public static function sortByPrice(array $arrFirstProduct, array $arrSecondProduct)
    {
         return $arrFirstProduct['intProductCost']->toDecimal()-$arrSecondProduct['intProductCost']->toDecimal();
    }

    /**
     * Searches for solus and dual play version of a product and, if found, removes
     * from current location and appends to end of list. Used by portal.
     *
     * @param array $arrProducts              The array of products to display
     * @param array $broadbandProductVariants The ID of the current product
     *
     * @return array The updated array with the current product at the end.
     */
    public static function moveCurrentProductVariantsToEndOfList(array $arrProducts, array $broadbandProductVariants)
    {

        $currentProducts = array();
        foreach ($arrProducts as $key => $product) {
            if ((array_key_exists('solus', $broadbandProductVariants)
                    && (int)$product['intSdi'] == (int)$broadbandProductVariants['solus'])
                || (array_key_exists('dualplay', $broadbandProductVariants)
                    && (int)$product['intSdi'] == (int)$broadbandProductVariants['dualplay'])) {
                $currentProducts[] = $product;
                unset($arrProducts[$key]);
            }
        }
        return array_merge($arrProducts, $currentProducts);
    }

    /**
     * Searches for a single product SDI and if found, moves it to the end of the list.
     * Used by workplace.
     *
     * @param array $arrProducts The array of products
     * @param int   $currentSDI  The ID of the current product
     *
     * @return array The updated list of products.
     */
    public static function moveCurrentProductToEndOfList(array $arrProducts, $currentSDI)
    {
        $currentProduct = null;
        foreach ($arrProducts as $key => $product) {
            if ($currentSDI == $product['intSdi']) {
                $currentProduct = $product;
                unset($arrProducts[$key]);
                $arrProducts = array_values($arrProducts);
            }
        }
        if ($currentProduct != null) {
            $arrProducts[] = $currentProduct;
        }
        return $arrProducts;
    }
}
