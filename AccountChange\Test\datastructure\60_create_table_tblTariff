CREATE TABLE `dbProductComponents`.`tblTariff` (
  `intTariffID` int(10) unsigned NOT NULL auto_increment,
  `intProductComponentConfigID` int(10) unsigned NOT NULL default '0',
  `intTariffTypeID` int(10) unsigned NOT NULL default '0',
  `intContractLengthID` tinyint(3) unsigned NOT NULL default '0',
  `intPaymentFrequencyID` tinyint(4) NOT NULL default '0',
  `intQuantityFrom` tinyint(3) unsigned NOT NULL default '0',
  `intQuantityTo` tinyint(3) unsigned NOT NULL default '0',
  `intCostIncVatPence` int(11) NOT NULL default '0',
  `bolAutoRenew` tinyint(1) NOT NULL default '1',
  `intNextTariffID` int(10) unsigned default NULL,
  `intNoticePeriodDays` int(10) unsigned NOT NULL default '0',
  `dtmStart` datetime NOT NULL default '0000-00-00 00:00:00',
  `dtmEnd` datetime default NULL,
  `stmLastUpdate` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (`intTariffID`),
  KEY `idxProductComponentConfigID` (`intProductComponentConfigID`),
  KEY `idxContractLengthID` (`intContractLengthID`),
  KEY `idxPaymentFrequencyID` (`intPaymentFrequencyID`),
  KEY `idxNextTariffID` (`intNextTariffID`),
  KEY `intTariffTypeID` (`intTariffTypeID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1