<?php
/**
 * Product Configuration for Mailbox component
 *
 * Holds information about a mailbox product that an account may have
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_Mailbox.class.php,v 1.2 2009-01-27 07:07:26 bselby Exp $
 * @since     File available since 2008-08-28
 */
/**
 * Product Mailbox class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_Mailbox extends AccountChange_Product_ServiceComponent
{
    /**
     * Is the product a key product
     *
     * @var boolean
     */
    protected $bolKeyProduct = false;

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration the account configuration
     *
     * @return void
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {

            $this->objNewProductConfiguration = $objAccountConfiguration->getProductConfigurationByType(
                AccountChange_Product_Manager::PRODUCT_TYPE_MAILBOX
            );
        }
    }

    /**
     * Perform a refresh on the product component
     *
     * @return void
     */
    protected function refresh()
    {
        Components_Api::queueSignal($this->getComponentId(), 'REFRESH');
        Db_Manager::commit();
    }
}
