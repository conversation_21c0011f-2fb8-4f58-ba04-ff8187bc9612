<?php
/**
 * Account Change Regrade Action
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2009 Plusnet
 * @since      File available since 2009-09-22
 */
use Plusnet\InstallationType\Domain\Model\InstallationTypeHandle;
use Plusnet\InstallationType\Domain\Model\ServiceID;
use Plusnet\InstallationType\Domain\Service\InstallationTypeToServiceMapper;

/**
 * Account Change Regrade Action
 *
 * This class represents the logic surrounding the regrade provisioning
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2009 Plusnet
 */
use Plusnet\Feature\FeatureToggleManager;

class AccountChange_Action_Regrade extends AccountChange_Action_BroadbandProvisioning
{

    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $registry = $this->getAccountChangeRegistry();
        $lineCheckResult = $this->getLineCheckResult($registry);
        $oldSdi = $registry->getEntry('intOldServiceDefinitionId');
        $newSdi = $registry->getEntry('intNewServiceDefinitionId');
        $orderService = $this->getOrderService();
        $appointmentData = $registry->getEntry('appointmentData');

        $orderService->provision(
            $this->intServiceId,
            $lineCheckResult->getLineCheckId(),
            $oldSdi,
            $newSdi,
            (is_null($appointmentData)) ? array() : $appointmentData,
            $registry->getEntry('bolHousemove'),
            empty($registry->getEntry('currentWlrComponentId'))
        );

        if ($orderService->isOrderRequired()) {
            $registry->setEntry('bolOrderNeeded', true);
            $this->markAccountChangeAsRequiringBroadbandOrder($this->intServiceId);
        }
    }

    /**
     * @return AccountChange_Account|AccountChange_Registry
     */
    protected function getAccountChangeRegistry()
    {
        return AccountChange_Registry::instance();
    }

    /**
     * @return Adsl2Orders_OrderService
     */
    protected function getOrderService()
    {
        return new Adsl2Orders_OrderService();
    }

    /**
     * Fetch the engineering details
     *
     * @return array
     */
    protected function getEngineerDetails()
    {
        $appointing = AccountChange_Registry::instance()->getEntry('appointing');

        if (!isset($appointing['appointingType'])) {
            // No engineer appointment type was selected
            return array();
        }

        $serviceId      = new Int($this->intServiceId);
        $appointingType = $appointing['appointingType']['serviceHandle'];
        $service        = $appointing['appointingType']['service'];

        $container = new EngineerAppointmentClient_Service_Container();
        $container->add($service, array());

        $client = new EngineerAppointmentClient_Client(
            $container
        );

        $details = $client->getAppointmentDetails($serviceId);

        return $details[$appointingType];
    }

    /**
     * Determine if we've reverted to 3 slot appointment booking because the line
     * checker was down
     *
     * @return bool
     **/
    protected function wasLiveAppointingDown()
    {
        $appointing = AccountChange_Registry::instance()->getEntry('appointing');
        if (isset($appointing['appointmentdate1']) && !empty($appointing['appointmentdate1'])) {
            return true;
        }
        return false;
    }

    /**
     * Wrapper to the I18n_Date::addWorkingDays
     *
     * @param I18n_Date $date      Unix timestamp of date to add to
     * @param int       $daysToAdd Number of days to add
     *
     * @return uxt
     **/
    protected function addWorkingDays($date, $daysToAdd)
    {
        return I18n_Date::addWorkingDays($date, $daysToAdd);
    }

    /**
     * Wrapper to the I18n_Date::subtractWorkingDays
     *
     * @param I18n_Date $date           Unix timestamp of date to subtract from
     * @param int       $daysToSubtract Number of days to subtract
     *
     * @return integer
     **/
    protected function subtractWorkingDays($date, $daysToSubtract)
    {
        return I18n_Date::subtractWorkingDays($date, $daysToSubtract);
    }

    /**
     * Wrapper to I18n_Date::now
     *
     * @return integer
     **/
    protected function now()
    {
        return I18n_Date::now()->getTimestamp();
    }


    /**
     * Returns a new Core_Service object for the Service ID of the action.
     *
     * @return Core_Service
     */
    protected function getCoreService()
    {
        return new Core_Service($this->intServiceId);
    }

    /**
     * wrapper function for calling the legacy code
     *
     * @param int                               $intServiceId         the service Id
     * @param string                            $orderType            the order type
     * @param array                             $arrOptions           the array of options
     * @param array                             $subOrderLines        sub order lines
     * @param AccountChange_EngineerAppointment $appointment          appointment
     * @param date                              $customerRequiredDate customer required date
     * @param array                             $engineerData         engineer data
     *
     * @return AdslProvideOn21Cn
     */
    protected function adslProvideOn21Cn(
        $intServiceId,
        $orderType,
        $arrOptions,
        $subOrderLines,
        $appointment,
        $customerRequiredDate,
        $engineerData
    ) {
        return AdslProvideOn21Cn(
            $intServiceId,
            $orderType,
            $arrOptions,
            $subOrderLines,
            $appointment,
            $customerRequiredDate,
            $engineerData
        );
    }

    /**
     * Gets the installation type mapper.
     *
     * @return InstallationTypeToServiceMapper
     */
    protected function getInstallationTypeMapper()
    {
        return new InstallationTypeToServiceMapper();
    }

    /**
     * Set the installation type by installation type handle.
     *
     * @param integer $serviceId        Service id
     * @param string  $installationType Installation type handle
     *
     * @return void
     */
    protected function setInstallationTypeByInstallationTypeHandle($serviceId, $installationType)
    {
        $this->getInstallationTypeMapper()->setInstallationTypeByInstallationTypeHandle(
            new InstallationTypeHandle($installationType),
            new ServiceID($serviceId)
        )->save();
    }

    /**
     * Method to check whether any schedule change completed
     * for 20CN product then no Activation should be done
     *
     * @param int $intServiceId service id of the customer
     *
     * @return boolean
     */
    public function checkScheduleChangeCompleted($intServiceId)
    {

        $intScheduleChangeCompleted = false;
        $objDatabase = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
        //check whether any schedule change completed
        $intCount = $objDatabase->getScheduleChangeCompleted($intServiceId);
        if ($intCount >= 1) {
            $intScheduleChangeCompleted = true;
        }
        return $intScheduleChangeCompleted;
    }

    /**
     * @param int $serviceId service ID
     * @return void
     */
    private function markAccountChangeAsRequiringBroadbandOrder($serviceId)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $dbAdaptor->markAccountChangeAsRequiringBroadbandOrderByServiceId($serviceId);
    }
}
