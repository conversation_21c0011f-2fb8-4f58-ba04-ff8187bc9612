<?php

/**
 * <AUTHOR>
 */

class AccountChange_AuthSessionPolicy extends AccountChange_AbstractValidationPolicy
{

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_INVALID_BUSINESS_TIER_SESSION';
    const ERROR_MESSAGE = 'ERROR_INVALID_BUSINESS_TIER_SESSION';

    /**
     * @return bool
     */
    public function validate()
    {
        if (!class_exists('Auth_BusTierSession')) {
            require_once '/local/www/database-admin/phplib/BusTierSession.class.php';
        }
        $busTierSession = $this->getBusinessTierSession();
        $busTierClient = $this->getBusinessTierClient();

        if (!$busTierSession instanceof Auth_Session || !$busTierClient->checkSessionValid($busTierSession)) {
            Log_AuditLog::write('Invalid business tier session detected.', __METHOD__);
            return false;
        }

        return true;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        if ($this->actor->getUserType() !== static::STAFF_USER_TYPE) {
            return self::ERROR_MESSAGE;
        }
    }

    /**
     * Return the current business tier session
     *
     * @return Auth_Session
     */
    protected function getBusinessTierSession()
    {
        return Auth_BusTierSession::getCurrentOrScriptUser();
    }

    /**
     * Return a business tier client
     *
     * @return BusTier_Client
     */
    protected function getBusinessTierClient()
    {
        return BusTier_BusTier::getClient('session');
    }
    
    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
