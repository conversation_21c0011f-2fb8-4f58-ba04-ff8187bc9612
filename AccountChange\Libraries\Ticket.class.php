<?php
/**
 * Account Change Ticket
 *
 * Holds information about an account change ticket, so we can re-use the same
 * ticket if needed. This is needed in the case of account changes to and from
 * Essential
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 Plusnet
 * @since     File available since 2009-01-19
 */
/**
 * Account Change Ticket class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 Plusnet
 */
class AccountChange_Ticket
{
    /**
     * The name of the pool for the ticket to be raised in
     *
     * @var string
     */
    protected $_pool = '';

    /**
     * The actual comment we want to raise the ticket with
     *
     * @var string
     */
    protected $_comment = '';

    /**
     * Whether or not the ticket/contact should be raised as closed
     *
     * @var boolean
     */
    protected $_isClosedContact = false;

    /**
     * Constructor
     *
     * @param string  $strPool         Which pool the ticket should be raised to
     * @param string  $strComment      Content of the ticket/contact
     * @param boolean $isClosedContact Are we wanting the contact raised to be a closed one
     *
     * @return AccountChange_Ticket
     */
    public function __construct($strComment = '', $strPool = 'CSC - Account change', $isClosedContact = false)
    {
        $this->_pool = $strPool;
        $this->_comment = $strComment;
        $this->_isClosedContact = $isClosedContact;
    }

    /**
     * Getter for the pool string
     *
     * @return string
     */
    public function getPool()
    {
        return $this->_pool;
    }

    /**
     * Getter for the comment
     *
     * @return string
     */
    public function getComment()
    {
        return $this->_comment;
    }

    /**
     * Getter for isClosedContact
     *
     * @return boolean
     */
    public function isClosedContact()
    {
        return $this->_isClosedContact;
    }

    /**
     * Setter for the pool
     *
     * @param $strPool
     *
     * @return void
     */
    public function setPool($strPool)
    {
        $this->_pool = $strPool;
    }

    /**
     * Setter for the comment
     *
     * @param $strComment
     *
     * @return void
     */
    public function setComment($strComment)
    {
        $this->_comment = $strComment;
    }

    /**
     * Setter for isClosedContact
     *
     * @param boolean $isClosedContact Whether or nother the ticket/contact raised should be closed
     *
     * @return void
     */
    public function setClosedContact($isClosedContact)
    {
        $this->_isClosedContact = $isClosedContact;
    }
}