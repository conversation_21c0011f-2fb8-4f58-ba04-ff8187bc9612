<?php
/**
 * Product Rules
 *
 * Collection of functions to check status of products
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-22
 */
/**
 * AccountChange_ProductRules
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_ProductRules
{

    /**
     * Factory Instance, represents rules object
     *
     * @var AccountChange_ProductRules
     */
    private static $_instance;

    /**
     * Internal cache array
     *
     * @var array
     */
    private $_productProvisionForService = array();

    /**
     * Factory instance getter
     *
     * Gets an account object that provides overview information
     *
     * @return AccountChange_ProductRules
     */
    public static function instance()
    {
        if (!isset(self::$_instance)) {

            self::$_instance = new AccountChange_ProductRules();
        }

        return self::$_instance;
    }

    /**
     * Setter for the factory
     *
     * Set a bespoke rules instance
     *
     * @param AccountChange_ProductRules $instance The rules instance
     *
     * @return void
     */
    public static function setInstance(AccountChange_ProductRules $instance)
    {
        self::$_instance = $instance;
    }

     /**
     * Get the product provision details for the service
     *
     * @param int              $intSdi          Service Definition Id
     * @param LineCheck_Result $lineCheckResult Line Check result object
     *
     * @return array intSupplierProductId
     *               intSupplierPlatformID
     *               bolWbcProduct (true/false)
     *               strProvisionOn (Adsl/Adsl2)
     */
    public function getProductProvisionForService($intSdi, LineCheck_Result $lineCheckResult)
    {
        if (!isset($this->_productProvisionForService[$intSdi])) {

            $arrProductProvDetails = array();

            $supplierProductRules = $this->getSupplierProductRules(
                $lineCheckResult,
                $intSdi
            );

            $supplierProduct = $supplierProductRules->getSupplierProduct();
            $supplierPlatform = $supplierProductRules->getSupplierPlatform();

            $arrProductProvDetails['intSupplierProductId'] = $supplierProduct->getSupplierProductId()->getValue();
            $arrProductProvDetails['intSupplierPlatformID'] = $supplierPlatform->getSupplierPlatformId()->getValue();
            $arrProductProvDetails['bolWbcProduct'] = $supplierProductRules->isWbc()->getValue();
            $arrProductProvDetails['strProvisionOn'] = $supplierProductRules->getProvisionOn()->getValue();

            $this->_productProvisionForService[$intSdi] = $arrProductProvDetails;
        }

        Dbg_Dbg::write(
            'AccountChange_ProductRules::getProductProvisionForService '
            . print_r($this->_productProvisionForService[$intSdi], true)
            . ' for product (' . $intSdi. ')',
            'AccountChange'
        );

        return $this->_productProvisionForService[$intSdi];
    }

    /**
     * Get the instance of Product_SupplierProductRules
     *
     * @param LineCheck_Result $lineCheckResult Line check result
     * @param integer          $intSdi          Service definition id
     *
     * @return Product_SupplierProductRules
     */
    protected function getSupplierProductRules(LineCheck_Result $lineCheckResult, $intSdi)
    {
        return new Product_SupplierProductRules(
            $lineCheckResult,
            new Int($intSdi)
        );
    }

    /**
     * Gets the maximum upstream and downsteam speeds for given product.
     * If these values are not set then default cap will be set in line check
     * (40/10 for now) but if these values are set and are higher than default
     * cap then cap in line check will be increased to these values.
     *
     * @param int $serviceDefinitionId The selected product
     *
     * @return array
     */
    public function getLinecheckSpeedCaps($serviceDefinitionId)
    {
        $capDefinitions = array(
            'downCap' => null,
            'upCap'   => null
        );

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $arrAdslProduct = $objDatabase->getAdslProductDetails($serviceDefinitionId);

        if (!empty($arrAdslProduct)) {

            if (array_key_exists('intMaximumSpeed', $arrAdslProduct)
                && isset($arrAdslProduct['intMaximumSpeed'])
            ) {
                $capDefinitions['downCap'] = $arrAdslProduct['intMaximumSpeed'];
            }

            if (array_key_exists('intMaxUploadSpeed', $arrAdslProduct)
                && isset($arrAdslProduct['intMaxUploadSpeed'])
            ) {
                $capDefinitions['upCap'] = $arrAdslProduct['intMaxUploadSpeed'];
            }
        }

        return $capDefinitions;
    }
}
