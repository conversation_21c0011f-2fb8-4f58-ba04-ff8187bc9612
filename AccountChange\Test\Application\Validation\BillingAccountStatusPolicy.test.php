<?php

use Plusnet\BillingApiClient\Service\ServiceManager;
use Plusnet\BillingApiClient\Entity\Constant\BillingAccountStatus;

class AccountChange_BillingAccountStatusPolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;

    /**
     * Test validate method for BillingAccountStatusPolicy returns correctly
     *
     * @dataProvider dataProviderForTestValidatorReturnsExpectedValue
     */
    public function testValidatorReturnsExpectedValueForCustomer($status, $expected)
    {
        $client = $this->getMock(
            'BillingApiFacade',
            array('getBillingAccountStatus')
        );

        $client->expects($this->once())
            ->method('getBillingAccountStatus')
            ->will($this->returnValue($status));

        ServiceManager::setService('BillingApiFacade', $client);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('plus.net');
        $service->shouldReceive('isJohnLewisUser')->andReturnFalse();

        $validator = Mockery::mock('AccountChange_BillingAccountStatusPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);

        $this->assertEquals($expected, $validator->validate(self::SERVICE_ID));
        $this->assertEquals(AccountChange_BillingAccountStatusPolicy::MC_ERROR_MESSAGE, $validator->getFailure());
    }

    /**
     * Test validate method for BillingAccountStatusPolicy returns correctly
     *
     * @dataProvider dataProviderForTestValidatorReturnsExpectedValue
     */
    public function testValidatorReturnsExpectedValueForColleague($status, $expected)
    {
        $client = $this->getMock(
            'BillingApiFacade',
            array('getBillingAccountStatus')
        );

        $client->expects($this->once())
            ->method('getBillingAccountStatus')
            ->will($this->returnValue($status));

        ServiceManager::setService('BillingApiFacade', $client);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('PLUSNET_STAFF');

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('plus.net');
        $service->shouldReceive('isJohnLewisUser')->andReturnFalse();

        $validator = Mockery::mock('AccountChange_BillingAccountStatusPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);

        $this->assertEquals($expected, $validator->validate(self::SERVICE_ID));
        $this->assertEquals(AccountChange_BillingAccountStatusPolicy::WP_ERROR_MESSAGE, $validator->getFailure());
    }

    /**
     * Test validate method for BillingAccountStatusPolicy returns correctly
     *
     * @dataProvider dataProviderForTestValidatorReturnsExpectedValue
     */
    public function testValidatorReturnsExpectedValueForJLCustomer($status, $expected)
    {
        $client = $this->getMock(
            'BillingApiFacade',
            array('getBillingAccountStatus')
        );

        $client->expects($this->once())
            ->method('getBillingAccountStatus')
            ->will($this->returnValue($status));

        ServiceManager::setService('BillingApiFacade', $client);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('plus.net');
        $service->shouldReceive('isJohnLewisUser')->andReturnTrue();

        $validator = Mockery::mock('AccountChange_BillingAccountStatusPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);

        $this->assertEquals($expected, $validator->validate(self::SERVICE_ID));
        $this->assertEquals(AccountChange_BillingAccountStatusPolicy::JL_ERROR_MESSAGE, $validator->getFailure());
    }

    /**
     * Data for testValidatorReturnsExpectedValue
     */
    public function dataProviderForTestValidatorReturnsExpectedValue()
    {
        return array(
            array(
                BillingAccountStatus::ACCOUNT_STATUS_TERMINATED_AWAITING_BILL,
                false
            ),
            array(
                BillingAccountStatus::ACCOUNT_STATUS_TERMINATED,
                false
            ),
            array(
                BillingAccountStatus::ACCOUNT_STATUS_ACTIVE,
                true
            )
        );
    }
}
