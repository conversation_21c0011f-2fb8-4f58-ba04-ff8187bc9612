<?php

use \Plusnet\C2mApiClient\Entity\Promotion;
use \Plusnet\C2mApiClient\Entity\Discount;
use \Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation;
use \Plusnet\C2mApiClient\Entity\DiscountValue;
use \Plusnet\C2mApiClient\Entity\PaymentFrequency;
use \Plusnet\C2mApiClient\C2MClient;

require_once(__DIR__.'/../../Libraries/C2mPromotionsHelper.php');

class C2mPromotionsHelperTest extends PHPUnit_Framework_TestCase
{
    /**
     * @test
     */
    public function shouldReturnPromotionUnmodified()
    {
        $promotion = $this->givenPromotionWithoutPlusnetProtect();

        $mockC2MClient = $this->getMockBuilder('Plusnet\C2mApiClient\V5\Client')
            ->disableOriginalConstructor()
            ->setMethods(array('getPromotion'))
            ->getMock();

        BusTier_BusTier::setClient('c2mapi.v5', $mockC2MClient);

        $mockC2MClient
            ->method('getPromotion')
            ->will($this->returnValue($promotion));

        $c2mPromotionHelper = new AccountChange_C2mPromotionsHelper(
            $mockC2MClient,
            $this->getMockBuilder(PromotionRules::class)->getMock(),
            'PlusnetResidential-AccountChange-NoAffiliate-EndOfContract',
            'code1'
        );
        
        $c2mPromotionHelper::getActivePromotionWithPromocode('code');

        $this->assertEquals($promotion, $this->givenPromotionWithoutPlusnetProtect());
    }

    /**
     * @test
     */
    public function shouldReturnPromotionWithPlusnetProtectDiscountRemoved()
    {
        $promotion = $this->givenPromotionWithPlusnetProtect();
        $mockC2MClient = $this->getMockBuilder(C2MClient::class)->getMock();
        $mockC2MClient->expects($this->any())
            ->method('getPromotion')
            ->will($this->returnValue($promotion));

        $c2mPromotionHelper = new AccountChange_C2mPromotionsHelper(
            $mockC2MClient,
            $this->getMockBuilder(PromotionRules::class)->getMock(),
            'PlusnetResidential-AccountChange-NoAffiliate-EndOfContract',
            'code1'
        );
        $c2mPromotionHelper::getActivePromotionWithPromocode('code');

        $this->assertEquals($promotion, $this->givenPromotionWithoutPlusnetProtect());
    }

    /**
     * @test
     */
    public function shouldReturnPromotionWithPlusnetProtectDiscountRemovedWhenUsingConstructorWithCode()
    {
        $promotion = $this->givenPromotionWithPlusnetProtect();
        $mockC2MClient = $this->getMockBuilder(C2MClient::class)->getMock();
        $mockC2MClient->expects($this->any())
            ->method('getPromotion')
            ->will($this->returnValue($promotion));

        $c2mPromotionHelper = new AccountChange_C2mPromotionsHelper(
            $mockC2MClient,
            $this->getMockBuilder(PromotionRules::class)->getMock(),
            'PlusnetResidential-AccountChange-NoAffiliate-EndOfContract',
            'code1'
        );

        $this->assertEquals($c2mPromotionHelper->getPromotions(), [$this->givenPromotionWithoutPlusnetProtect()]);
    }

    /**
     * @test
     */
    public function shouldReturnUnmodifiedWhenUsingConstructorWithCode()
    {
        $promotion = $this->givenPromotionWithoutPlusnetProtect();
        $mockC2MClient = $this->getMockBuilder(C2MClient::class)->getMock();
        $mockC2MClient->expects($this->any())
            ->method('getPromotion')
            ->will($this->returnValue($promotion));

        $c2mPromotionHelper = new AccountChange_C2mPromotionsHelper(
            $mockC2MClient,
            $this->getMockBuilder(PromotionRules::class)->getMock(),
            'PlusnetResidential-AccountChange-NoAffiliate-EndOfContract',
            'code1'
        );

        $this->assertEquals($c2mPromotionHelper->getPromotions(), [$this->givenPromotionWithoutPlusnetProtect()]);
    }

    /**
     * @test
     */
    public function shouldReturnPromotionWithPlusnetProtectDiscountRemovedWhenUsingConstructorForChannel()
    {
        $promotion = $this->givenPromotionWithPlusnetProtect();
        $mockC2MClient = $this->getMockBuilder(C2MClient::class)->getMock();
        $mockC2MClient->expects($this->any())
            ->method('getPromotionsForSalesChannelAndCharacteristics')
            ->will($this->returnValue([$promotion]));

        $c2mPromotionHelper = new AccountChange_C2mPromotionsHelper(
            $mockC2MClient,
            $this->getMockBuilder(PromotionRules::class)->getMock(),
            'PlusnetResidential-AccountChange-NoAffiliate-EndOfContract',
            null
        );

        $this->assertEquals($c2mPromotionHelper->getPromotions(), [$this->givenPromotionWithoutPlusnetProtect()]);
    }

    /**
     * @test
     */
    public function shouldReturnUnmodifiedPromotionWhenUsingConstructorForChannel()
    {
        $promotion = $this->givenPromotionWithoutPlusnetProtect();
        $mockC2MClient = $this->getMockBuilder(C2MClient::class)->getMock();
        $mockC2MClient->expects($this->any())
            ->method('getPromotionsForSalesChannelAndCharacteristics')
            ->will($this->returnValue([$promotion]));

        $c2mPromotionHelper = new AccountChange_C2mPromotionsHelper(
            $mockC2MClient,
            $this->getMockBuilder(PromotionRules::class)->getMock(),
            'PlusnetResidential-AccountChange-NoAffiliate-EndOfContract',
            null
        );

        $this->assertEquals($c2mPromotionHelper->getPromotions(), [$this->givenPromotionWithoutPlusnetProtect()]);
    }

    private function givenPromotionWithPlusnetProtect()
    {
        $productOffering1 = new ProductOfferingPaymentInformation('Broadband', '', '');
        $productOffering2 = new ProductOfferingPaymentInformation('PlusnetProtectChargeable', '', '');

        $discountValue1 = new DiscountValue();
        $discountValue1->setValue(10);
        $discountValue1->setDiscountValueType('FIXED_AMOUNT');
        $discountValue1->setDuration(12);
        $discountValue1->setDurationType(PaymentFrequency::MONTHLY);

        $discount1 = new Discount();
        $discount1->setProductOfferingPaymentInformations(
            [$productOffering1]
        );
        $discount1->setDiscountValues([$discountValue1]);

        $discountValue2 = new DiscountValue();
        $discountValue2->setValue(50);

        $discount2 = new Discount();
        $discount2->setDiscountValues([$discountValue2]);
        $discount2->setType('CASH_BACK');
        $discount2->setSubType('REWARD_CARD');

        $discountValue3 = new DiscountValue();
        $discountValue3->setValue(2);
        $discountValue3->setDiscountValueType('FIXED_AMOUNT');
        $discountValue3->setDuration(3);
        $discountValue3->setDurationType(PaymentFrequency::MONTHLY);

        $discount3 = new Discount();
        $discount3->setDiscountValues([$discountValue3]);
        $discount3->setProductOfferingPaymentInformations(
            [$productOffering2]
        );
        $discount3->setDiscountValues([$discountValue3]);

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount1, $discount2, $discount3]);
        $promotion->setCode('testPromoCode');
        $promotion->setActiveTo('********');
        $promotion->setActiveFrom('********');
        $promotion->setSalesChannels(['PlusnetResidential-AccountChange-NoAffiliate-EndOfContract']);
        return $promotion;
    }

    private function givenPromotionWithoutPlusnetProtect()
    {
        $productOffering1 = new ProductOfferingPaymentInformation('Broadband', '', '');

        $discountValue1 = new DiscountValue();
        $discountValue1->setValue(10);
        $discountValue1->setDiscountValueType('FIXED_AMOUNT');
        $discountValue1->setDuration(12);
        $discountValue1->setDurationType(PaymentFrequency::MONTHLY);

        $discount1 = new Discount();
        $discount1->setProductOfferingPaymentInformations(
            [$productOffering1]
        );
        $discount1->setDiscountValues([$discountValue1]);


        $discountValue2 = new DiscountValue();
        $discountValue2->setValue(50);

        $discount2 = new Discount();
        $discount2->setDiscountValues([$discountValue2]);
        $discount2->setType('CASH_BACK');
        $discount2->setSubType('REWARD_CARD');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount1, $discount2]);
        $promotion->setCode('testPromoCode');
        $promotion->setActiveTo('********');
        $promotion->setActiveFrom('********');
        $promotion->setSalesChannels(['PlusnetResidential-AccountChange-NoAffiliate-EndOfContract']);
        return $promotion;
    }
}
