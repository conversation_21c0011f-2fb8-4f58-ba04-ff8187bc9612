<?php
/**
 * <AUTHOR> <<EMAIL>>
 */
require_once '/local/data/mis/database/standard_include.inc';
require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
require_once '/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php';

/**
 * Account change script that takes in a list of service ids and cancels any pending product changes
 * against them.
 *
 *  Example: php /local/codebase2005/modules/Framework/Scripts/RunScript.php
 *            -c AccountChange_MassCancelScheduledChanges -p PLUSNET --sids=5009497,5009498
 *             --ticket-reason="Scheduled change cancelled by script'
 *
 */
class AccountChange_MassCancelScheduledChanges implements Mvc_Script
{
    const SCRIPTUSER = 'scriptuser';

    private $suppressOutput = false;
    
    private $serviceIds = array();
    
    private $cancellationReason = "Stalled account change removed by script";

    /**
     * @return bool
     */
    public function getSuppressOut<PERSON>()
    {
        return $this->suppressOutput;
    }

    /**
     * @param bool $suppressOutput
     */
    public function setSuppressOutput($suppressOutput)
    {
        $this->suppressOutput = $suppressOutput;
    }

    /**
     * @return array
     */
    public function getServiceIds()
    {
        return $this->serviceIds;
    }

    /**
     * @param array $serviceIds
     */
    public function setServiceIds($serviceIds)
    {
        $this->serviceIds = $serviceIds;
    }

    /**
     * @return string
     */
    public function getCancellationReason()
    {
        return $this->cancellationReason;
    }

    /**
     * @param string $cancellationReason
     */
    public function setCancellationReason($cancellationReason)
    {
        $this->cancellationReason = $cancellationReason;
    }

    /**
     * Get a string version of what short opts we want to register
     *
     * Mvc_Script method
     *
     * @return string
     */
    public static function getShortOpts()
    {
        return '';
    }

    /**
     * Get an array of long opts we want to register
     *
     * Mvc_Script method
     *
     * @return array
     */
    public static function getLongOpts()
    {
        return array('sids:', 'suppress-output:', 'ticket-reason:');
    }

    /**
     * Determines if parallel runs are allowed
     *
     * Mvc_Script method
     *
     * @return boolean
     */
    public function bolAllowParallelRuns()
    {
        return true;
    }

    private function isTrue($input)
    {
        if (trim($input) == 'true' || filter_var($input, FILTER_VALIDATE_BOOLEAN)) {
            return true;
        }
        return false;
    }

    protected function checkAndSetOptions($options) {
        
        if (isset($options['suppress-output']) && $this->isTrue($options['suppress-output'])) {
            $this->setSuppressOutput(true);
        }
        
        if (isset($options['ticket-reason'])) {
            $this->setCancellationReason($options['ticket-reason']);
        }
        
        if (isset($options['sids']))  {
            $sids = explode(',', $options['sids']);
            foreach ($sids as $sid) {
                if (ctype_digit($sid)) {
                    $this->serviceIds[] = $sid;
                }
            }
        }
        if (empty($this->getServiceIds())) {
            throw new \RuntimeException('No service ids supplied to cancel the changes for, please add a command line option like:  --sids=123,456');
        }
    }

    private function echoOrNot($line)
    {
        if (!$this->getSuppressOutput()) {
            echo $line;
        }
    }

    /**
     * @param  array $args    Arguments
     * @param  array $options Run options
     * @return null
     */
    public function run(array $args, array $options)
    {
        try {
            $this->checkAndSetOptions($options);
        } catch (Exception $exception) {
            $this->echoOrNot($exception->getMessage());
            $this->help();
        }

        $time_start = microtime(true);

        $this->processAccounts();
        $time_end = microtime(true);
        $execution_time = ($time_end - $time_start)/60;
        $this->echoOrNot( "Total Execution Time: '.$execution_time.' Mins\n");
    }

    /**
     * Migrate given accounts onto new products
     *
     * @return null
     */
    private function processAccounts()
    {
        // Main account processing...
        foreach ($this->getServiceIds() as $serviceId) {
            $this->processCancellation($serviceId);
        }
    }

    /**
     * Prints the command help
     *
     * @return null
     */
    private function help()
    {
        echo "\n\n\n\n";
        echo "Script Instructions\n";
        echo "------------------\n";
        echo "This script will cancel any scheduled product changes for a provided list of service ids and leave a service notice on the account.\n";
        echo "\n";
        echo "Run options:\n";
        echo "--sids=<comma separated list of service ids>\n";
        echo '--ticket-reason="reason for the cancellation to appear on the serivce notice"'."\n";
        echo "\n\n";
        echo "Example: php /local/codebase2005/modules/Framework/Scripts/RunScript.php ";
        echo '-c AccountChange_MassCancelScheduledChanges -p PLUSNET --sids=5009497,5009498 --ticket-reason="Scheduled change cancelled by script'."\n";
        echo "------------------\n";
    }


    /**
     * Look up the current ADSL component Id
     *
     * @param int $serviceId Service id
     *
     * @return int
     */
    protected function getComponentIdForInternetConnection($serviceId)
    {
        $db = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);

        $activeAdslComponentId = $db->getComponentIdForTypeAndStatus('active', 'INTERNET_CONNECTION', $serviceId);

        if (is_array($activeAdslComponentId) && count($activeAdslComponentId) == 1) {
            $adslComponentId = $activeAdslComponentId[0];
        } else {
            $adslComponentId = null;
        }

        return $adslComponentId;
    }

    /**
     * Get the new ADSL service component Id for the account
     *
     * @param int $serviceId              Service Id
     * @param int $newServiceDefinitionId New service definition id
     *
     * @return int
     */
    protected function getNewAdslServiceComponentId($serviceId, $newServiceDefinitionId)
    {
        $lineCheckResult = LineCheck_Result::getLatestResultByServiceId($serviceId);
        // Default to market 3 if there's a problem with the line check result
        $marketId = 3;
        if ($lineCheckResult instanceof LineCheck_Result) {
            $market = LineCheck_Market::getMarketFromExchange($lineCheckResult->getExchangeCode());

            $marketId = $market->getMarketId();
        }
        return ProductFamily_InternetConnectionHelper::getId($newServiceDefinitionId, $marketId);
    }

    /**
     * get business actor by workplace user name
     *
     * @param string $name workplace user name
     *
     * @return Auth_BusinessActor
     *
     * @codeCoverageIgnore
     */
    public function getBusinessActorByName($name)
    {
        return Auth_BusinessActor::getActorByWpUsername($name);
    }

    protected function processCancellation($serviceId)
    {
        $errors = array();
        $this->importLegacy();
        $serviceDetails = userdata_service_get($serviceId);
        $scheduledChange = new AccountChange_ScheduledChange($serviceId);
        $data = $this->buildData($serviceId, $scheduledChange);

        //Default old and new products to current product
        $oldServiceDefinitionId = !empty($data['oldServiceDefinitionId'])
            ? $data['oldServiceDefinitionId']
            : $serviceDetails['type'];

        $newServiceDefinitionId = !empty($data['newServiceDefinitionId'])
            ? $data['newServiceDefinitionId']
            : $serviceDetails['type'];

        if (!empty($data['changes']['Broadband']) && !empty($productFamily) && ($productFamily->hasAutoContracts() || $productFamily->isJohnLewis())) {
            try {
                $contractClient = BusTier_BusTier::getClient('contracts');
                $contractClient->cancelPendingContract($serviceId, "Cancelling Scheduled Account Change");
            } catch (Exception $exception) {
                $errors[] = 'Failed to execute product change plan. Please check the error log for more details.';
                error_log("Exception while executing product change plan. Message : " . $exception->getMessage());
            }
        }

        //If the ProductChangePlan call hasn't failed, the customer's contracts will now have been re-arraged
        //based on the selection from previous page. Now we cancel the scheduled changes in core db and raise tickets.
        $actioner = $this->getBusinessActorByName(self::SCRIPTUSER);

        if (sizeof($data['changes']) > 0) {
            foreach ($data['changes'] as $type => $changeDetails) {
                try {
                    $scheduledChange->cancelChange($actioner, array($changeDetails['change']));
                    Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
                } catch (Exception $exception) {
                    Db_Manager::rollback(Db_Manager::DEFAULT_TRANSACTION);

                    $errors[] = 'Error in cancelling scheduled ' . $type . ' change.'
                        . ' Please check the error log for more details.';
                    error_log('Exception while cancelling scheduled ' . $type . ' change. Message : ' . $exception->getMessage());

                    return;
                }

                $body = "Service was scheduled to be changed to {$changeDetails['newProductName']} on ".
                    "{$changeDetails['changeDate']} but was cancelled. <br><br> Reason : ".$this->getCancellationReason();

                $this->echoOrNot("[$serviceId] Added ticket: {$body}\n");

                userdata_log_removed_scheduled_type_change(
                    $serviceId,
                    $oldServiceDefinitionId,
                    $newServiceDefinitionId
                );
                tickets_ticket_add('Internal', $serviceId, 0, 0, 'Closed', $my_id, $body);

                $adslCease = CAdslCeaseOrder::getOrderForServiceId($serviceId);
                if (is_object($adslCease)) {
                    if ($adslCease->getSupplierHandle() == "TISCALI"
                        || $adslCease->getSupplierHandle() == "BT"
                    ) {
                        $adslCease->setCancelled(time());
                    }
                }

            }
        } else {
            $this->echoOrNot("[$serviceId] no scheduled changes found.\n");
        }

        if (sizeof($errors) > 0) {
            foreach ($errors as $error) {
                $this->echoOrNot("ERROR: {$error}\n");
            }
        }
    }

    /**
     * Fetches scheduled product changes
     *
     * @param int                           $serviceId       Service Id
     * @param AccountChange_ScheduledChange $scheduledChange Scheduled account change
     *
     * @return array
     */
    protected function buildData($serviceId, AccountChange_ScheduledChange $scheduledChange = null)
    {
        $this->importLegacy();
        global $my_id;

        if (empty($scheduledChange)) {
            $scheduledChange = new AccountChange_ScheduledChange($serviceId);
        }

        $data = array();
        $data['changes'] = array();
        $oldServiceDefinitionId = null;
        $newServiceDefinitionId = null;
        $componentIdsToRemove = array();
        $servicesAdded = array();

        //The following logic is to build arguments to pass to product change plan client and
        //template variables
        foreach ($scheduledChange->getScheduledChanges() as $change) {
            $changeData = $change->getData();

            if (!empty($changeData['intOldServiceDefinitionId'])) {
                $oldServiceDefinitionId = $changeData['intOldServiceDefinitionId'];
            }

            if (!empty($changeData['intNewServiceDefinitionId'])) {
                $newServiceDefinitionId = $changeData['intNewServiceDefinitionId'];
            }

            if ($change->getProductType() == 'Broadband') {
                $changeDetails = array();
                $changeDetails['change'] = $change;

                //Old/current adsl service component id
                $oldAdslComponentId = $this->getComponentIdForInternetConnection($serviceId);

                if (!empty($oldAdslComponentId)) {
                    $componentIdsToRemove[] = $oldAdslComponentId;
                }

                //Get the new adsl service component id that would have been added to the account.
                $newAdslServiceComponentId = $this->getNewAdslServiceComponentId($serviceId, $newServiceDefinitionId);
                $serviceEntry['serviceTypeId'] = (int) $newAdslServiceComponentId;
                $servicesAdded[] = $serviceEntry;

                $oldProductDetails = product_get_service((int) $changeData['intOldServiceDefinitionId']);
                $newProductName = $changeData['strNewProductName'];
                $oldProductName = $oldProductDetails['name'];

                $displayMessage = 'Scheduled to change from "'.$oldProductName . '" to "'. $newProductName. '"';

                if (!empty($changeData['strPromoCode'])) {
                    $displayMessage .= ' (With promo code "'.$changeData['strPromoCode'].'")';
                }

                $changeDetails['newProductName'] = $newProductName;
                $changeDetails['oldProductName'] = $oldProductName;
                $changeDetails['displayMessage'] = $displayMessage;
                $changeDetails['changeDate'] = $changeData['dteChangeDate'];

                $this->echoOrNot("[$serviceId] $displayMessage\n");

                $data['changes'][$change->getProductType()] = $changeDetails;
            }
        }
        $data['newServiceDefinitionId'] = $newServiceDefinitionId;
        $data['oldServiceDefinitionId'] = $oldServiceDefinitionId;
        $data['servicesAdded'] = $servicesAdded;
        $data['componentIdsToRemove'] = $componentIdsToRemove;

        return $data;
    }
    
    /**
     * Import Legacy libraries
     *
     * @return null
     */
    public function importLegacy()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
        require_once '/local/data/mis/database/database_libraries/secure-transaction-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/HomePhone.class.php';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
    }
}
