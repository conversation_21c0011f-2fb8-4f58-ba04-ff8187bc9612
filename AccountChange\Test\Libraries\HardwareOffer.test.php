<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_HardwareOffer_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @test
     */
    public function shouldReturnCorrectDataIfOfferingHardwareWithDefaultSelection()
    {
        $bundle = new HardwareClient_Bundle(
            new String('Router'),
            new String('Description'),
            new Int(1234),
            new Int(12343),
            new String('ROUTER_HANDLE')
        );

        $expectedRouterDetailsArray = [
            'handle' => 'ROUTER_HANDLE',
            'name' => 'Router',
        ];

        $test = new AccountChange_HardwareOffer($bundle);
        $test->setShouldOfferHardware(true);
        $test->setHardwareSelectedByDefault(true);
        $this->assertEquals(true, $test->shouldOfferHardware());
        $this->assertEquals('ROUTER_HANDLE', $test->getHardwareHandle());
        $this->assertEquals('ROUTER_HANDLE', $test->getHardwareDefaultSelection());
        $this->assertEquals($expectedRouterDetailsArray, $test->getHardwareBundleDetails());
        $test->setHardwareSelectedByDefault(false);
        $this->assertEquals(null, $test->getHardwareDefaultSelection());
    }
}
