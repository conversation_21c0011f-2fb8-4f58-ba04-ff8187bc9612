<?php

/**
 * <AUTHOR>
 */

use \Plusnet\Feature\FeatureToggleManager;

class AccountChange_Product_WlrProductFilterPlusnetBusiness implements AccountChange_Product_ProductFilter
{
    private $wlrProductFilter;

    /**
     * @param AccountChange_Product_WlrProductFilter $wlrProductFilter the WLR product filter
     */
    public function __construct(AccountChange_Product_WlrProductFilter $wlrProductFilter)
    {
        $this->wlrProductFilter = $wlrProductFilter;
    }

    /**
     * @param array $products products
     * @return array
     */
    public function filter($products)
    {
        if (FeatureToggleManager::isOn(FeatureToggleManager::BGL_CALL_PLANS_GO_LIVE)) {
            $availableProducts = [];

            foreach ($products as $product) {
                $productFamily = $this->wlrProductFilter->getProductFamily($product['intNewWlrId']);
                if ($productFamily == AccountChange_Product_WlrProductBase::FAMILY_BGL) {
                    $availableProducts[] = $product;
                }
            }

            return $availableProducts;
        }

        return $products;
    }
}
