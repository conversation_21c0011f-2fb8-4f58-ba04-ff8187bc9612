server: coredb
role: slave
rows: single
statement:

SELECT
    se.intScheduledEventID AS intScheduleId,
    se.dteDue AS dteChangeDate,
    pc.intComponentID AS intOldComponentId,
    pc.intNewComponentID AS intNewComponentId,
    sc.name AS strNewProductName,
    pc.intProductChangeID AS intProductChangeId,
    t.intCostIncVatPence / 100 AS decCostPound
FROM
    dbSystemEvents.tblScheduledEvent AS se
INNER JOIN dbSystemEvents.tblProductChange AS pc
    ON pc.intScheduledEventID = se.intScheduledEventID
INNER JOIN userdata.components AS c
    ON c.component_id = pc.intNewComponentID
INNER JOIN products.service_components AS sc
    ON sc.service_component_id = component_type_id
INNER JOIN userdata.tblProductComponentInstance pci
    ON pci.intComponentID = c.component_id
INNER JOIN dbProductComponents.tblTariff t
    ON t.intTariffId = pci.intTariffId
WHERE
    se.dtmCompleted IS NULL
AND se.dtmCancelled IS NULL
AND se.intEventTypeID = 3
AND c.service_id = :serviceId
