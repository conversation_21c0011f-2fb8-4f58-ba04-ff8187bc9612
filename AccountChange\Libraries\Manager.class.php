<?php
/**
 * Account Change Manager
 *
 * Processes the rules for downgrades/upgrades and creates necessary strategy for account change
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://www.plus.net/
 * @since     File available since 2008-08-19
 */
/**
 * Account Change Manager class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://www.plus.net/
 */
class AccountChange_Manager
{
    /**
     * Upgrade Account Change
     *
     */
    const ACCOUNT_TYPE_CHANGE_UPGRADE   = 1;

    /**
     * Downgrade Account Change
     *
     */
    const ACCOUNT_TYPE_CHANGE_DOWNGRADE = 2;

    /**
     * It's not possible to figure out if its upgrade or downgrade
     *
     */
    const ACCOUNT_TYPE_CHANGE_UNDECIDED = 3;

    /**
     * Currency Unit
     *
     */
    const CURRENCY_UNIT = 'gbp';

    /**
     * Subscription product type for line rental (from RBM)
     *
     */
    const LINE_RENTAL_PRODUCT_TYPE = 'Line Rental';


    /**
     * Username of the script user
     *
     */
    const SCRIPT_USERNAME = 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz';

    /**
     * Account Change Type
     *
     * @access private
     * @var int
     */
    private $_intAccountChangeType = null;

    /**
     * Account Configuration for current setup
     *
     * @var AccountChange_AccountConfiguration
     */
    private $_objOldAccountConfiguration = null;

    /**
     * Account Configuration for new setup
     *
     * @var AccountChange_AccountConfiguration
     */
    private $_objNewAccountConfiguration = null;

    /**
     * Service ID
     *
     * @var int
     */
    private $_intServiceId = null;

    /**
     * Total one off charge that needs to be paid
     *
     * @var I18n_Currency
     */
    private $_totalOneOffCharge = null;

    /**
     *
     *
     * @var
     */
    private $_isPartnerUser = false;

    /**
     * Service change schedule ID
     *
     * @var int
     */
    private $serviceChangeScheduleId = null;

    /**
     * Constructor for Account Change Manager
     *
     * @param int                                $intServiceId        service id
     * @param AccountChange_AccountConfiguration $objOldConfiguration old account configuration
     * @param AccountChange_AccountConfiguration $objNewConfiguration new account configuration
     *
     * <AUTHOR> Selby
     *
     * @throws AccountChange_ManagerException
     *
     * @return void
     */
    public function __construct(
        $intServiceId,
        AccountChange_AccountConfiguration $objOldConfiguration,
        AccountChange_AccountConfiguration $objNewConfiguration
    ) {
        if (!preg_match('/^[0-9]*$/', $intServiceId)) {
            throw new AccountChange_ManagerException(
                'Non numeric service id',
                AccountChange_ManagerException::ERR_INVALID_SERVICE_ID_TYPE
            );
        }

        Dbg_Dbg::write('AccountChange_Manager instantiated for ' . $intServiceId, 'AccountChange');

        $this->_intServiceId = (int)$intServiceId;
        $objOldConfiguration->setServiceId($intServiceId);
        $objNewConfiguration->setServiceId($intServiceId);

        $this->_objOldAccountConfiguration = $objOldConfiguration;
        $this->_objNewAccountConfiguration = $objNewConfiguration;
    }

    /**
     * Setter for the total one off charge
     *
     * @param I18n_Currency $totalOneOffCharge one off charge
     *
     * @return void
     */
    public function setTotalOneOffCharge(I18n_Currency $totalOneOffCharge)
    {
        $this->_totalOneOffCharge = $totalOneOffCharge;
    }

    /**
     * Setter for the is partner user variable
     *
     * @param bool $isPartnerUser is a partner user
     *
     * @return void
     */
    public function setPartnerUser($isPartnerUser)
    {
        $this->_isPartnerUser= $isPartnerUser;
    }

    /**
     * Get Service Id
     *
     * @return int
     */
    public function getServiceId()
    {
        return $this->_intServiceId;
    }

    /**
     * Getter for the total one off charge
     *
     * @return I18n_Currency
     */
    public function getTotalOneOffCharge()
    {
        return $this->_totalOneOffCharge;
    }

    /**
     * Returns account change type based on provided values
     *
     * Works both for number of "key products" and product value
     *
     * @param int $intOldValue old value
     * @param int $intNewValue new vlue
     *
     * @access public
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return int Account change type (Upgrade, Downgrade)
     * @throws AccountChange_ManagerException
     */
    public static function getAccountChangeType($intOldValue, $intNewValue)
    {
        if ($intOldValue == $intNewValue) {
            return self::ACCOUNT_TYPE_CHANGE_UNDECIDED;
        }

        if ($intNewValue > $intOldValue) {
            return self::ACCOUNT_TYPE_CHANGE_UPGRADE;
        }

        return  self::ACCOUNT_TYPE_CHANGE_DOWNGRADE;
    }

    /**
     * Getter for the old account configuration
     *
     * @return AccountChange_AccountConfiguration
     */
    public function getOldAccountConfiguration()
    {
        return $this->_objOldAccountConfiguration;
    }

    /**
     * Getter for the new account configuration
     *
     * @return AccountChange_AccountConfiguration
     */
    public function getNewAccountConfiguration()
    {
        return $this->_objNewAccountConfiguration;
    }

    /**
     * Selects account change type
     *
     * @access public
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses AccountChange_Manager::getAccountChangeType()
     *
     * @return int
     * @throws AccountChange_ManagerException
     */
    public function selectAccountChangeType()
    {
        // Try and work out the account change type (up/downgrade) by the total number
        // of products in the account configuration (BB, Wlr etc)
        // If we cannot work it out via that method,
        // then revert back to total cost of the products in the configuration

        //first - number of key products
        $intAccountChangeType = AccountChange_Manager::getAccountChangeType(
            $this->_objOldAccountConfiguration->getTotalNumberOfKeyProducts(),
            $this->_objNewAccountConfiguration->getTotalNumberOfKeyProducts()
        );

        if ($intAccountChangeType != self::ACCOUNT_TYPE_CHANGE_UNDECIDED) {
            $this->_intAccountChangeType = $intAccountChangeType;
            return $this->_intAccountChangeType;
        }

        //second - cost of key products
        $intAccountChangeType = AccountChange_Manager::getAccountChangeType(
            $this->_objOldAccountConfiguration->getTotalCostOfKeyProducts(),
            $this->_objNewAccountConfiguration->getTotalCostOfKeyProducts()
        );

        if ($intAccountChangeType != self::ACCOUNT_TYPE_CHANGE_UNDECIDED) {
            $this->_intAccountChangeType = $intAccountChangeType;
            return $this->_intAccountChangeType;
        }

        //third - both - number and and cost of key products are equal - consider it as an upgrade
        $this->_intAccountChangeType = self::ACCOUNT_TYPE_CHANGE_UPGRADE;
        return $this->_intAccountChangeType;
    }

    /**
     * Top level account change
     *
     * @access public
     * <AUTHOR> Marek <<EMAIL>>
     * <AUTHOR> Selby <<EMAIL>>
     *
     * @return bool
     * @throws Exception
     */
    public function changeAccount()
    {
        // Perform the account changes
        return $this->execute();
    }

    /**
     * Execute all the product configurations execute methods
     * Actual account type changing
     *
     * <AUTHOR> Marek <<EMAIL>>
     * <AUTHOR> Selby <<EMAIL>>
     *
     * @return bool
     */
    protected function execute()
    {
        $tickets = array();
        $serviceNotices = array();

        $this->output('Matching product configurations');
        $arrProductConfigurations = $this->matchProductConfigurations(
            $this->_objOldAccountConfiguration,
            $this->_objNewAccountConfiguration
        );

        foreach ($arrProductConfigurations as $objProductConfiguration) {

            $this->output(
                'Executing configuration for product/operation: ' .
                $objProductConfiguration->getProductId() . '/' . $objProductConfiguration->getAccountChangeOperation()
            );

            $objProductConfiguration->execute();

            $this->output('Getting tickets for configuration change');
            $tickets = array_merge($tickets, $objProductConfiguration->getTickets());

            $this->output('Getting service notice(s) for configuration change');
            $serviceNotices = array_merge($serviceNotices, $objProductConfiguration->getServiceNotices());
        }

        $this->output('Raising tickets');
        $this->raiseTickets($tickets);

        $this->output('Raising service notice(s)');
        $this->raiseServiceNotices($serviceNotices);

        $this->output('Selecting account type');
        $this->selectAccountChangeType();

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $this->serviceChangeScheduleId = $objDatabase->getScheduleIdForBroadband($this->_intServiceId);

        return true;
    }

    /**
     * Raise the ticket for the account change.
     *
     * @param array $tickets Array of AccountChange_Ticket objects
     *
     * @return void
     */
    protected function raiseTickets(array $tickets = array())
    {
        if (!empty($tickets)) {
            $client  = BusTier_BusTier::getClient('tickets');

            foreach ($tickets as $ticket) {

                $pool = TicketClient_DbTeam::getTeamByHandle($ticket->getPool());

                $dbTicket = $client->createTicket($this->getBusinessActor(), Db_Manager::DEFAULT_TRANSACTION);
                $dbTicket->comment(
                    $this->getBusinessActor(),
                    $ticket->getComment(),
                    $pool->getTeamId(),
                    null,
                    $ticket->isClosedContact()
                );

                if ($ticket->isClosedContact()) {
                    $dbTicket->close($this->getBusinessActor());
                }
                // In order to deal with the legacy and framework code locking each other
                // it was decided to commit the default transaction everytime we called the database
                Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
            }
        }
    }

    /**
     * Raise all of the service notices for account change process
     *
     * @param array $serviceNotices Array of AccountChange_ServiceNotice objects
     *
     * @return void
     */
    protected function raiseServiceNotices(array $serviceNotices = array())
    {
        if (!empty($serviceNotices)) {
            $client = BusTier_BusTier::getClient('serviceNotices');

            foreach ($serviceNotices as $notice) {

                $objServiceNotice = $client->createServiceNotice(
                    $this->getBusinessActor(),
                    Db_Manager::DEFAULT_TRANSACTION
                );
                $objServiceNotice->setBody($notice->getComment());
                $objServiceNotice->setServiceNoticeTypeId($notice->getServiceNoticeTypeId());
                $objServiceNotice->setActionerId($this->getBusinessActor()->getActorId());
                $objServiceNotice->write();
                // In order to deal with the legacy and framework code locking each other
                // it was decided to commit the default transaction everytime we called the database
                Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
            }
        }
    }

    /**
     * Returns id of authenticated user,
     * If executed from CLI or BBCR - returns script user id
     *
     * @return string
     */
    public static function getMyId()
    {
        if ('cli' == PHP_SAPI || AccountChange_Registry::instance()->getEntry('runFromBBCRScript')) {
            return self::SCRIPT_USERNAME;
        } else {
            return Auth_Auth::getCurrentLogin()->getBusinessActor()->getExternalUserId();
        }
    }

    /**
     * Match up the product configurations
     * We want to end up with an array of product configurations (with no duplicates) that can have execute()
     * called on them
     *
     * @param AccountChange_AccountConfiguration $objOldAccountConfiguration old account configuration
     * @param AccountChange_AccountConfiguration $objNewAccountConfiguration new account configuration
     *
     * <AUTHOR> Selby <<EMAIL>>
     *
     * @return array
     */
    protected function matchProductConfigurations(
        AccountChange_AccountConfiguration $objOldAccountConfiguration,
        AccountChange_AccountConfiguration $objNewAccountConfiguration
    ) {
        $arrProductConfigurations = array();

        foreach ($objOldAccountConfiguration->getProductConfigurations() as $objOldProductConfiguration) {

            if ($objOldProductConfiguration instanceof AccountChange_Product_Configuration) {

                $objOldProductConfiguration->setMatchingProductConfiguration($this->_objNewAccountConfiguration);

                if (in_array(
                    $objOldProductConfiguration->getAction(),
                    array(AccountChange_Product_Manager::ACTION_REMOVE,
                        AccountChange_Product_Manager::ACTION_CHANGE,
                        AccountChange_Product_Manager::ACTION_REFRESH
                    )
                )) {

                    $arrProductConfigurations[] = $objOldProductConfiguration;
                }
            }

            // Let the product make the decision about how it will change itself. Upgrade | Downgrade
            if ($objOldProductConfiguration->getAction() == AccountChange_Product_Manager::ACTION_CHANGE) {

                $objOldProductConfiguration->setAccountChangeOperation($this->_objNewAccountConfiguration);
            }
        }

        foreach ($objNewAccountConfiguration->getProductConfigurations() as $objNewProductConfiguration) {

            if ($objNewProductConfiguration instanceof AccountChange_Product_Configuration) {

                if ($objNewProductConfiguration->getAction() === AccountChange_Product_Manager::ACTION_ADD) {

                    $arrProductConfigurations[] = $objNewProductConfiguration;
                }
            }
        }

        return $arrProductConfigurations;
    }

    /**
     * Getter for the total upgrade cost
     *
     * @param array $arrInvoiceItems invoice items
     *
     * @return float
     */
    public function getTotalUpgradeCost(array $arrInvoiceItems = array())
    {
        return self::getTotalCostIncVat($arrInvoiceItems);
    }

    /**
     * Getter for the total cost including vat
     *
     * @param array $arrInvoiceItems invoice items
     *
     * @return float
     */
    private static function getTotalCostIncVat($arrInvoiceItems)
    {
        $floTotalCostIncVat = 0.0;
        foreach ($arrInvoiceItems as $arrItem) {
            $floTotalCostIncVat += $arrItem['amount'];
        }
        return $floTotalCostIncVat;
    }

    /**
     * Are we going to be taking payment as part of the account change
     *
     * @return boolean
     */
    public function isTakingPayment()
    {
        $arrProductConfigurations = $this->_objOldAccountConfiguration->getProductConfigurations();

        // Iterate through all of the key products and check to see if they are taking payments
        foreach ($arrProductConfigurations as $objProductConfiguration) {

            if ($objProductConfiguration->isTakingPayment() == true) {
                Dbg_Dbg::write("AccountChange_Manager::isTakingPayment yes", 'AccountChange');
                return true;
            }
        }

        Dbg_Dbg::write("AccountChange_Manager::isTakingPayment no", 'AccountChange');
        return false;
    }

    /**
     * Send confirmation emails
     *
     * This is just a wrapper, so that each product configuration can send its own email
     * That way if that email needs more information it can get it itself
     *
     * @param array $arrData account change data
     *
     * @return void
     */
    public function sendConfirmationEmails(array $arrData = array())
    {

        $arrProductConfigurations = $this->matchProductConfigurations(
            $this->_objOldAccountConfiguration,
            $this->_objNewAccountConfiguration
        );

        foreach ($arrProductConfigurations as $objProductConfiguration) {

            if ($objProductConfiguration->isKeyProduct() && !$objProductConfiguration->isScheduled()) {

                $objProductConfiguration->sendConfirmationEmail($arrData);

            }
        }
    }

    /**
     * Send an email confirming the engineer appointment date for account changes that switch to Fibre
     *
     * @param array $arrData account change data
     *
     * @return void
     */
    public function sendAppointmentEmail($arrData)
    {
        if (isset($arrData['appointment']) || isset($arrData['appointmentdate1'])) {
            $arrProductConfigurations = $this->matchProductConfigurations(
                $this->_objOldAccountConfiguration,
                $this->_objNewAccountConfiguration
            );
            foreach ($arrProductConfigurations as $objProductConfiguration) {
                if ($objProductConfiguration->isKeyProduct()) {
                    $objProductConfiguration->sendAppointmentEmail($arrData);
                }
            }
        }
    }

    /**
     * Get the business actor
     *
     * @return Auth_BusinessActor
     */
    private function getBusinessActor()
    {
        // The solution below is because of the way how PDO is handling string
        // columns in db but you passing an int var to where cond
        // 1) in db you have '********', you passing 1232323 - it will not match (you have to pad it with leading 0s)
        // 2) in db you have  '1232323' - if you will padd it with 0 it will not work - so you have to pass 1232323
        try {
            $objBusinessActor = Auth_BusinessActor::getActorByExternalUserId(
                str_pad($this->_intServiceId, 8, '0', STR_PAD_LEFT)
            );
        } catch (Exception $e) {
            $objBusinessActor = Auth_BusinessActor::getActorByExternalUserId($this->_intServiceId);
        }

        return $objBusinessActor;
    }

    /**
     * Grabs the product names for the exclusion of account change email
     * When account change is performed and selected one of these product
     * we should block the emails sent to customer.
     * Fix for the problem
     * https://workplace.plus.net/programme_tool/problem.html?problem_id=59074
     *
     * @return array - $arrProducts array of product names
     */
    public static function getBlockedAccountChangeEmailProductNames()
    {
        $adaptor = Db_Manager::getAdaptor('AccountChange');
        $arrProductNames = $adaptor->getBlockedAccountChangeEmailProductNames();

        return $arrProductNames;
    }

    /**
     * Checks if there is an active Line Rental Saver component
     * and contract on the account
     *
     * @param int $serviceId service id to check
     *
     * @return bool
     */
    public static function hasActiveLineRentalSaver($serviceId)
    {
        $subscriptionsHelper = new AccountChange_BillingApi_SubscriptionsHelper();

        return $subscriptionsHelper->hasActiveLineRentalSaver($serviceId);
    }

    /**
     * Need to include the legacy files, but we need to be able to mock it for the unit tests
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/config-access.inc';
    }

    /**
     * Function to output a formatted message to standard output - needs to be mockable for unit tests
     *
     * @param string  $message    the message to output
     * @param boolean $printDebug used to decide if the output should be written to either STDOUT or Dbg_Dbg
     *
     * @return void
     */
    protected function output($message, $printDebug = false)
    {
        if ($printDebug) {
            $line = date(DATE_COOKIE) . ": $message\n";
            print $line;
        } else {
            Dbg_Dbg::write($message, 'AccountChange');
        }
    }

    /**
     * Get Product Component MGALS OBJ
     *
     *
     * @return object
     */
    protected function getObjProductComponentMGALS()
    {
        return new ProductComponent_MGALS();
    }

    /**
     * Marks scheduled account change as complete
     *
     * @param integer    $serviceChangeScheduleId Service change schedule id
     * @param Db_Adaptor $dbAccountChangeAdaptor  account change database adapator
     *
     * @return boolean
     */
    public function completeServiceChangeSchedule($serviceChangeScheduleId, $dbAccountChangeAdaptor)
    {
        $result = $dbAccountChangeAdaptor->completeServiceChangeSchedule($serviceChangeScheduleId);
        $this->output("completeServiceChangeSchedule result: " . print_r($result, true));

        return $result;
    }


    /**
     * @return int
     */
    public function getServiceChangeScheduleId()
    {
        return $this->serviceChangeScheduleId;
    }
}
