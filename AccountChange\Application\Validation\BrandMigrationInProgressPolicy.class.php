<?php

use \Plusnet\BrandMigration\Services\ServiceManager;

class AccountChange_BrandMigrationInProgressPolicy extends AccountChange_AbstractValidationPolicy
{
    /**
     * Error code used to map this policy's failure with a template
     */
    const BM_ERROR_CODE = 'ERROR_ACCOUNT_CHANGE_BRAND_MIGRATION_IN_PROGRESS';
    const AM_ERROR_CODE = 'ERROR_ACCOUNT_CHANGE_ACCOUNT_MIGRATION_IN_PROGRESS';

    private $errorCodeLookup = [
        1 => self::AM_ERROR_CODE,
        2 => self::BM_ERROR_CODE
    ];

    const ERROR_BM_MESSAGE = 'I\'m sorry we are unable to action your request at this time as your account is moving away from us. We hope you come back in future!';
    const ERROR_AM_MESSAGE = 'Looks like you are getting a new account from us, it is in progress, we\'ll let you know when this completes.';
    const ERROR_MESSAGE_JL = '%s||%s';
    const ERROR_MESSAGE_WP = 'There is a brand migration in progress on this account. Please be aware any change to the broadband technology (ADSL to Fibre or Fibre 40 to Fibre 80) may jeopardize this.';

    /** @var Core_Service */
    private $service;
    private $errorCode;

    /**
     * @return bool
     */
    public function validate()
    {
        $serviceId = $this->actor->getExternalUserId();
        $this->service = $this->getCoreService($serviceId);

        if ($this->isMigrationInProgress($serviceId)) {
            return false;
        }

        return true;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        $message = self::ERROR_MESSAGE_WP;

        if ($this->isScript) {
            $message = $this->getErrorCode();
        }

        if (!$this->isWorkplace) {
            if ($this->service->isJohnLewisVispCustomer()) {
                $message = sprintf(self::ERROR_MESSAGE_JL, $this->errorCode, $this->getMessageText());
            } else {
                $message = $this->getMessageText();
            }
        }

        return $message;
    }

    private function getMessageText()
    {
        return $this->errorCode === self::BM_ERROR_CODE ? self::ERROR_BM_MESSAGE : self::ERROR_AM_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return $this->errorCode;
    }

    protected function getService()
    {
        return ServiceManager::getService("BrandMigrationService");
    }

    public function isMigrationInProgress($serviceId)
    {
        $migration = $this->getService()->getBrandMigrationByServiceId($serviceId);
        $inProgress = false;
        if (!is_null($migration)
            && is_null($migration->getCompletedAt())
            && is_null($migration->getCancelledAt())
        ) {
            $this->errorCode = $this->errorCodeLookup[$migration->getTypeId()];
            $inProgress = true;
        }
        return $inProgress;
    }

    public function getCoreService($serviceId)
    {
        return new Core_Service($serviceId);
    }

}
