<?php

/**
 * <AUTHOR>
 */

class AccountChange_ExistingScheduledProductChangePolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;

    /**
     * @return void
     */
    public function testValidationPassesWithNoScheduledChanged()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('plus.net');

        $accountChange = Mockery::mock();
        $accountChange->shouldReceive('getScheduledChanges')->andReturnFalse();

        $validator = Mockery::mock('AccountChange_ExistingScheduledProductChangePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);
        $validator->shouldReceive('getScheduledChange')->once()->with(self::SERVICE_ID)->andReturn($accountChange);

        $this->assertTrue($validator->validate());
    }

    /**
     * @return void
     */
    public function testValidationPassesWithNonUserAccount()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('INVALID');

        $accountChange = Mockery::mock();
        $accountChange->shouldReceive('getScheduledChanges')->andReturnTrue();

        $validator = Mockery::mock('AccountChange_ExistingScheduledProductChangePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);
        $validator->shouldReceive('getScheduledChange')->once()->with(self::SERVICE_ID)->andReturn($accountChange);

        $this->assertTrue($validator->validate());
    }

    /**
     * @return void
     */
    public function testValidationFailsWhenThereIsAScheduledChangePlusnet()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('plus.net');
        $service->shouldReceive('isJohnLewisUser')->andReturnFalse();

        $accountChange = Mockery::mock();
        $accountChange->shouldReceive('getScheduledChanges')->andReturnTrue();

        $validator = Mockery::mock('AccountChange_ExistingScheduledProductChangePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);
        $validator->shouldReceive('getScheduledChange')->once()->with(self::SERVICE_ID)->andReturn($accountChange);

        $this->assertFalse($validator->validate());
        $this->assertEquals($validator::ERROR_MESSAGE_PLUSNET, $validator->getFailure());
    }
    
    /**
     * @return void
     */
    public function testValidationFailsWhenThereIsAScheduledChangeJohnLewis()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('plus.net');
        $service->shouldReceive('isJohnLewisUser')->andReturnTrue();

        $accountChange = Mockery::mock();
        $accountChange->shouldReceive('getScheduledChanges')->andReturnTrue();

        $validator = Mockery::mock('AccountChange_ExistingScheduledProductChangePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);
        $validator->shouldReceive('getScheduledChange')->once()->with(self::SERVICE_ID)->andReturn($accountChange);

        $this->assertFalse($validator->validate());
        $this->assertEquals($validator::ERROR_MESSAGE_JOHN_LEWIS, $validator->getFailure());
    }

    /**
     * @return void
     */
    public function testErrorMessageWhenValidationIsCalledFromAScript() {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getIsp')->andReturn('plus.net');
        $service->shouldReceive('isJohnLewisUser')->andReturnTrue();

        $accountChange = Mockery::mock();
        $accountChange->shouldReceive('getScheduledChanges')->andReturnTrue();

        $validator = Mockery::mock('AccountChange_ExistingScheduledProductChangePolicy', [$actor, false, true]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->with(self::SERVICE_ID)->andReturn($service);
        $validator->shouldReceive('getScheduledChange')->once()->with(self::SERVICE_ID)->andReturn($accountChange);

        $this->assertFalse($validator->validate());
        $this->assertEquals($validator::ERROR_MESSAGE_SCRIPT, $validator->getFailure());
    }
}
