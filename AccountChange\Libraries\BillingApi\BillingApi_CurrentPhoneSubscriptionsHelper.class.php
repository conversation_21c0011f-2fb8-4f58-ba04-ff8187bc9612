<?php


class AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper
{
    const LINE_RENTAL_TARIFF_NAME = 'Line Rental';
    const CALL_PLAN_PRODUCT_TYPE = 'Call Plan';

    /**
     * Gets the current subscription data for the line rental and call plan elements.
     * Assumptions:
     * * The tariffName distinguishes line rental and line rental saver and will be consistent regardless of
     * whether the customer is on a legacy product, current product and regardless of their channel, eg Plusnet/JLP.
     * * There could be a current and a future subscription for one/both/neither elements
     * * The only way to get the current one is to filter based on the current date
     *
     * @param int $serviceId service id to check
     *
     * @return AccountChange_BillingApi_CurrentPhoneSubscriptions
     */
    public function getCurrentPhoneSubscriptionData($serviceId)
    {
        $subscriptions = $this->getCurrentSubscriptions($serviceId);

        $currentLineRentalSubscription = null;
        $currentCallPlanSubscription = null;

        if (!empty($subscriptions)) {
            foreach ($subscriptions as $subscription) {
                if ($this->isCurrentLineRentalSubscription($subscription)) {
                    $currentLineRentalSubscription = $subscription;
                } else if ($this->isCurrentCallPlanSubscription($subscription)) {
                    $currentCallPlanSubscription = $subscription;
                }
            }
        }

        return new AccountChange_BillingApi_CurrentPhoneSubscriptions(
            $currentLineRentalSubscription,
            $currentCallPlanSubscription);
    }

    /**
     * @param $serviceId
     * @return array
     */
    protected function getCurrentSubscriptions($serviceId)
    {
        $subscriptionsHelper = new AccountChange_BillingApi_SubscriptionsHelper();
        return $subscriptionsHelper->getCustomerCurrentSubscriptions($serviceId);
    }

    /**
     * Checks if the subscription element passed in is current line rental
     *
     * @param object $subscription Subscription element
     *
     * @return bool
     **/
    private function isCurrentLineRentalSubscription($subscription)
    {
        return  $subscription->getTariffName() === self::LINE_RENTAL_TARIFF_NAME &&
            $this->isCurrentSubscription($subscription);
    }

    /**
     * Checks if the subscription element passed in is current call plan
     *
     * @param object $subscription Subscription element
     *
     * @return bool
     **/
    private function isCurrentCallPlanSubscription($subscription)
    {
        return $subscription->getProductType() === self::CALL_PLAN_PRODUCT_TYPE &&
            $this->isCurrentSubscription($subscription);
    }

    /**
     * Checks if the subscription element passed in is current
     *
     * @param object $subscription Subscription element
     *
     * @return bool
     **/
    private function isCurrentSubscription($subscription)
    {
        $today = date('Y-m-d');

        return $subscription instanceof Plusnet\BillingApiClient\Entity\CustomerProductSubscription &&
            $subscription->getStartDate() <= $today &&
            ($subscription->getEndDate() === null || $subscription->getEndDate() >= $today);
    }
}
