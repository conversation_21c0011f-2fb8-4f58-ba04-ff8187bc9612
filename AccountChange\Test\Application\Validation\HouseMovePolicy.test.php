<?php

/**
 * <AUTHOR>
 */

use \Plusnet\HouseMoves\Services\ServiceManager;

class AccountChange_HouseMovePolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;

    /**
     * testValidationFailureWhenHouseMoveInProgress
     *
     * @param string $userType      user type to be returned from actor mock
     * @param string $expectedError expected error message
     *
     * @dataProvider providerForValidationFailures
     * @return void
     * @throws PHPUnit_Framework_AssertionFailedError
     */
    public function testValidationFailureWhenHouseMoveInProgress($userType, $expectedError)
    {
        $houseMoveService = Mockery::mock();
        $houseMoveService->shouldReceive('houseMoveIsOpenForUser')->with(self::SERVICE_ID)->once()->andReturnTrue();

        ServiceManager::setService('HouseMoveService', $houseMoveService);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn($userType);
        $validator = new AccountChange_HouseMovePolicy($actor);
        $this->assertFalse($validator->validate());

        $this->assertEquals($expectedError, $validator->getFailure());
    }

    /**
     * Data provider for testValidationFailureWhenHouseMoveInProgress
     *
     * @return array
     */
    public function providerForValidationFailures()
    {
        return [
          'external' => ['PLUSNET_ENDUSER',AccountChange_HouseMovePolicy::MC_ERROR_MESSAGE],
          'internal' => ['PLUSNET_STAFF',AccountChange_HouseMovePolicy::WP_ERROR_MESSAGE]
        ];
    }

    /**
     * @return void
     */
    public function testValidationSuccessWhenNoActiveHouseMove()
    {
        $houseMoveService = Mockery::mock();
        $houseMoveService->shouldReceive('houseMoveIsOpenForUser')->with(self::SERVICE_ID)->once()->andReturnFalse();

        ServiceManager::setService('HouseMoveService', $houseMoveService);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $validator = new AccountChange_HouseMovePolicy($actor);

        $this->assertTrue($validator->validate());
    }
}
