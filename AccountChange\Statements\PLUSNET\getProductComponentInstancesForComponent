server: coredb
role: slave
rows: multiple
statement:

SELECT
    pci.intProductComponentInstanceID,
    pci.intProductComponentID,
    pci.intStatusID,
    pci.intTariffID,
    pci.dteNextInvoice
FROM
    userdata.tblProductComponentInstance AS pci
INNER JOIN dbProductComponents.tblStatus AS s
    ON s.intStatusID = pci.intStatusID
WHERE
    intComponentID = :componentId
AND vchHandle IN (:status)
AND pci.dtmEnd IS NULL;