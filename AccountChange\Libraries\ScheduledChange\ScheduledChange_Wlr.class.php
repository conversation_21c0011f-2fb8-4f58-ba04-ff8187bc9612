<?php
/**
 * Scheduled Change Wlr File
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      link
 */
/**
 * Concrete class for wlr product change
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      link
 */
class AccountChange_ScheduledChange_Wlr extends AccountChange_ScheduledChange_Abstract
{
    /**
     * Customer friendly string representation of product
     *
     * @var string
     */
    const TYPE = 'Phone';

    /**
     * Constructor for the scheduled change
     *
     * @param int $serviceId The customers service id
     *
     * @return AccountChange_IScheduledChange
     */
    public function __construct($serviceId)
    {
        $db = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
        $this->data = $db->getWlrScheduledChange($serviceId);

        if (!empty($this->data)) {
            $this->data['serviceId'] = $serviceId;
        }
    }

    /**
     * Cancel the change
     *
     * @param Auth_BusinessActor $actioner Business Actor performing the operation
     *
     * @throws Exception
     *
     * @return void
     */
    public function cancelChange(Auth_BusinessActor $actioner)
    {
        if ($this->hasScheduledChange()) {
            $db = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
            $db->cancelWlrProductChange($this->data['intScheduleId']);

            $this->cancelRetentionOffer($actioner);

            $this->data = array();
        }
    }

    /**
     * Cancels any pending retention offers associated with this scheduled change.
     *
     * CONTEXT: A Retention Offer can have multiple Retention Offer Items
     *
     * Retention Offer Items that are associated with this scheduled change are automatically
     * cancelled when the scheduled change is cancelled. The purpose of this method is to cancel
     * the Retention Offer if there are no offer items left in the offer.
     *
     * @param Auth_BusinessActor $actioner Business Actor performing the operation
     *
     * @return void
     */
    public function cancelRetentionOffer(Auth_BusinessActor $actioner)
    {
        if ($this->hasScheduledChange()) {

            $serviceId = $this->data['serviceId'];
            $scheduleId = $this->data['intProductChangeId'];

            $targetActor = Auth_BusinessActor::getActorByExternalUserId((int) $serviceId);

            $retentionManager = $this->getRetentionManager(
                $targetActor
            );

            if (!empty($retentionManager)) {

                $hasRemainingOfferItems = false;

                foreach ($retentionManager->getOfferManager()->getOffers() as $offerItem) {

                    if ($offerItem->getWlrScheduleId() != $scheduleId) {

                        $hasRemainingOfferItems = true;
                        break;
                    }
                }

                //Cancel the retention offer if no offer items remain in the offer.
                if ($hasRemainingOfferItems == false) {

                    $retentionManager->cancel($actioner);
                }
            }
        }
    }

    /**
     * Getter for the product type. Customer friendly version
     *
     * (non-PHPdoc)
     *
     * @see AccountChange_IScheduledChange::getProductType()
     *
     * @return string
     */
    public function getProductType()
    {
        return self::TYPE;
    }
}
