<?php

/**
 * <AUTHOR>
 */

abstract class AccountChange_AbstractValidationPolicy implements AccountChange_ValidationPolicy
{

    const C2M_PROMOTION_KEY = 'C2MPromotion';
    const C2M_PROMOTION_CODE_KEY = 'C2MPromotionCode';

    /** @var Auth_BusinessActor  */
    protected $actor;
    protected $isWorkplace;
    protected $isScript;
    protected $additionalInformation;

    /**
     * @param Auth_BusinessActor $actor                 The business actor
     * @param bool               $isWorkplace           Flag to determine if WP
     * @param bool               $isScript              Flag to determine if script
     * @param array              $additionalInformation Any additional information required to validate
     */
    public function __construct(Auth_BusinessActor $actor, $isWorkplace = false, $isScript = false, $additionalInformation = array())
    {
        $this->actor = $actor;
        $this->isWorkplace = $isWorkplace;
        $this->isScript = $isScript;
        $this->additionalInformation = $additionalInformation;
    }

    public function getAdditionalInformation()
    {
        return $this->additionalInformation;
    }

    /**
     * Gets a C2mPromotion object from the additional data (either from a C2mPromotion object or by building one from a promotion code)
     *
     * @return Plusnet\C2mApiClient\Entity\Promotion or false
     **/
    protected function getC2mPromotionFromAdditionalData()
    {
        $additionalInfo = $this->getAdditionalInformation();
        if (isset($additionalInfo[self::C2M_PROMOTION_KEY]) && $additionalInfo[self::C2M_PROMOTION_KEY] instanceof Plusnet\C2mApiClient\Entity\Promotion) {
            return $additionalInfo[self::C2M_PROMOTION_KEY];
        } elseif (isset($additionalInfo[self::C2M_PROMOTION_CODE_KEY])) {
            return $this->getC2mPromotionFromPromotionalCode($additionalInfo[self::C2M_PROMOTION_CODE_KEY]);
        }
        return false;
    }

    /**
     * Builds a Plusnet\C2mApiClient\Entity\Promotion object from a promo code, or returns false if no c2m promo
     * exists with that code.
     *
     * @return Plusnet\C2mApiClient\Entity\Promotion or false
     **/
    public function getC2mPromotionFromPromotionalCode($promoCode)
    {
        require_once(__DIR__ . '/../../Libraries/C2mPromotionsHelper.php');
        $promotion = AccountChange_C2mPromotionsHelper::getActivePromotionWithPromocode($promoCode);
        if ($promotion !== null) {
            return $promotion;
        }
        return false;
    }

}
