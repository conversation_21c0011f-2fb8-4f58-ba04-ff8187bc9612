server: coredb
role: slave
rows: single
statement:

SELECT SUM(t.intCostIncVatPence)
FROM dbProductComponents.tblTariff t
INNER JOIN dbProductComponents.tblContractLength cl
	ON cl.intContractLengthID = t.intContractLengthID
	AND cl.vchHandle = :strContractHandle
INNER JOIN dbProductComponents.tblPaymentFrequency pf
	ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID
	AND pf.vchHandle = :strPaymentFrequencyHandle
INNER JOIN dbProductComponents.tblProductComponentConfig pcc
	ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
INNER JOIN products.tblServiceComponentProduct scp
	ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
	AND scp.intServiceComponentID = :intServiceComponentID
INNER JOIN dbProductComponents.tblProductComponent pc
	ON pc.intProductComponentID = pcc.intProductComponentID
	AND pc.vchHandle in ('SUBSCRIPTION', 'WlrLineRent')
	AND t.dtmEnd IS NULL
