server: coredb
role: slave
rows: multiple
statement:

SELECT
        sd.name,
        sd.service_definition_id,
        sd.minimum_charge,
        ap.intMaximumSpeed,
        ap.bolCapped,
        ap.vchContract,
        sd.blurb
FROM products.service_definitions AS sd
LEFT JOIN products.adsl_product AS ap
        ON sd.service_definition_id = ap.service_definition_id
WHERE
        sd.type = :strType
        AND IF (:strIsp = 'force9', sd.isp = :strIsp AND sd.name NOT LIKE 'plus.net.uk%', sd.isp = :strIsp)
        AND IF (:strContract = '', true, ap.vchContract = :strContract)
        AND sd.end_date IS NULL
        AND IF (:bolSignupViaPortalOnly = 1, sd.signup_via_portal = 'Y', true)
ORDER BY sd.name
