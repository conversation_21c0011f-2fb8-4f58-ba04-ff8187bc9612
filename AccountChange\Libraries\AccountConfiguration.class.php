<?php
/**
 * Account Configuration
 *
 * Configuration for an entire account. Holds an array of product configurations
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 Plusnet
 * @since     File available since 2008-08-19
 */
/**
 * Account Configuration Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 Plusnet
 */
class AccountChange_AccountConfiguration
{
    /**
     * Array of product configurations that make up the account configuration
     *
     * @var array
     */
    private $_arrProductConfigurations = array();

    /**
     * Service ID
     *
     * @var int
     */
    private $_intServiceId = null;

    /**
     * Constructor for the AccountConfiguration
     *
     * @param array $arrProductConfigurations of AccountChange_Product_Configuration
     *
     * @return AccountChange_AccountConfiguration
     */
    public function __construct(array $arrProductConfigurations)
    {
        $this->_arrProductConfigurations = $arrProductConfigurations;
        $this->validateAccountConfiguration();
    }

    /**
     * Sets Service ID
     *
     * @param int $intServiceId Service Id
     *
     * @return void
     */
    public function setServiceId($intServiceId)
    {
        if (!preg_match('/^[0-9]*$/', $intServiceId)) {

            throw new AccountChange_AccountConfigurationException(
                'Non numeric service id',
                AccountChange_AccountConfigurationException::ERR_INVALID_SERVICE_ID
            );
        }

        $this->_intServiceId = (int)$intServiceId;
        $this->setServiceIdForProductConfigurations($intServiceId);
    }

    /**
     * Returns product configurations
     *
     * @access public
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return array
     */
    public function getProductConfigurations()
    {
        return $this->_arrProductConfigurations;
    }

    /**
     * Setter for the product configurations
     *
     * @param array $arrProductConfigurations Product Configurations for the account
     *
     * @return void
     */
    public function setProductConfigurations(array $arrProductConfigurations)
    {
        $this->_arrProductConfigurations = $arrProductConfigurations;
        $this->validateAccountConfiguration();
    }

    /**
     * Returns Product Configuration based on the provided type
     *
     * @param string $strProductType (ServiceDefinition, ServiceComponent, Wlr)
     *
     * @throws AccountChange_AccountConfigurationException
     *
     * @return AccountChange_Product_Configuration
     */
    public function getProductConfigurationByType($strProductType)
    {
        $strClassName = "AccountChange_Product_{$strProductType}";

        foreach ($this->_arrProductConfigurations as $objProductConfiguration) {
            if ($objProductConfiguration instanceof $strClassName) {
                return $objProductConfiguration;
            }
        }

        throw new AccountChange_AccountConfigurationException(
            "No valid product configuration found for type '$strProductType'",
            AccountChange_AccountConfigurationException::ERR_NO_VALID_PRODUCT_CONFIGURATION_FOUND
        );
    }

    /**
     * Validate if the configuration is OK
     *
     * If it isn't then throw exceptions as we cannot carry on
     *
     * @throws AccountChange_AccountConfigurationException::ERR_INVALID_PRODUCT_CONFIGURATION_OBJECT
     * @throws AccountChange_AccountConfigurationException::ERR_INVALID_PRODUCT_CONFIGURATION_COUNT
     * @throws AccountChange_AccountConfigurationException::ERR_INVALID_COMPONENT_FOR_SERVICE_DEFINITION
     *
     * @return boolean
     */
    public function validateAccountConfiguration()
    {
        $objServiceDefinitionConfiguration = null;

        foreach ($this->_arrProductConfigurations as $objProductConfiguration) {
            if (!$objProductConfiguration instanceof AccountChange_Product_Configuration) {
                throw new AccountChange_AccountConfigurationException(
                    'Invalid Product Configuration object',
                    AccountChange_AccountConfigurationException::ERR_INVALID_PRODUCT_CONFIGURATION_OBJECT
                );
            }

            if ($objProductConfiguration instanceof AccountChange_Product_ServiceDefinition) {
                $objServiceDefinitionConfiguration = $objProductConfiguration;
            }
        }

        /**
         * Commenting out the code below rather than removing it due to:
         * This is technically correct. There should always be a ServiceDefinition.class.php
         * product in the AccountConfiguration, but this has now been confused with/linked to
         * the customers adsl product. What needs to happen in the account change code
         * is to remove the assumption that the ServiceDefinition is the adsl product
         * and we should start to use the InternetConnection.class.php object.
         * Leaving this here to remember this aspect, so hopefully we can revist some day.
         * We would severly need to refactor the ServiceDefinition class, and it may
         * even be redundant, as althought each customer needs a service definition, it
         * has no real impact on an account change (other than the code has exlicitly been used
         * that way)
         * if (null == $objServiceDefinitionConfiguration) {
         *     throw new AccountChange_AccountConfigurationException(
         *         'An Account Configuration Must Have A Service Definition Product',
         *         AccountChange_AccountConfigurationException::ERR_MUST_HAVE_SERVICE_DEFINITION_PRODUCT
         *     );
         * }
         */

        return true;
    }

    /**
     * Get the total number of key products that an account configuration has
     *
     * @return int
     */
    public function getTotalNumberOfKeyProducts()
    {
        $intNumber = 0;

        foreach ($this->_arrProductConfigurations as $objProductConfiguration) {
            if ($objProductConfiguration->isKeyProduct()) {
                $intNumber++;
            }
        }

        return $intNumber;
    }

    /**
     * Get the total cost of an account configuration
     *
     * @return int
     */
    public function getTotalCostOfKeyProducts()
    {
        $intCost = 0;

        foreach ($this->_arrProductConfigurations as $objProductConfiguration) {
            if ($objProductConfiguration->isKeyProduct()) {
                $intCost += $objProductConfiguration->getProductCost();
            }
        }

        return $intCost;
    }

    /**
     * Setter for the service id of all the product configurations
     *
     * @param int $intServiceId Service Id
     *
     * @throws AccountChange_AccountConfigurationException
     *
     * @return void
     */
    public function setServiceIdForProductConfigurations($intServiceId)
    {
        if (!preg_match('/^[0-9]*$/', $intServiceId)) {

            throw new AccountChange_AccountConfigurationException(
                'Non numeric service id',
                AccountChange_AccountConfigurationException::ERR_INVALID_SERVICE_ID
            );
        }

        foreach ($this->_arrProductConfigurations as $objProductConfiguration) {
            $objProductConfiguration->setServiceId($intServiceId);
        }
    }
}
