<?php
/**
 * AccountChange PaymentRedirect Workplace
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-07-19
 */
/**
 * AccountChange PaymentRedirect Portal
 *
 * This class handles the redirection to external payment application.
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class AccountChange_PaymentRedirectWorkplace extends AccountChange_PaymentRedirect
{
    /**
     * Get the decorator class
     *
     * @return string
     */
    protected function getDecorator()
    {
        return 'CustomerDetails_Decorator';
    }

    /**
     * Get the view to return to from GImP
     *
     * @see AccountChange_PaymentRedirect::getReturnView
     *
     * @return string
     */
    protected function getReturnView()
    {
        return 'PaymentRedirectWorkplace';
    }
}
