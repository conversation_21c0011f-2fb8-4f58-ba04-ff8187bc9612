<?php

use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\BillingApiClient\Exceptions\BillingApiClientPartnerResellerAccessException;

/**
 * <AUTHOR>
 */

class AccountChange_InvoiceDateCorrectPolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;

    /**
     * @dataProvider testValidatorFailsValidationForInvalidInvoiceDateDataProvider
     *
     * @param mixed  $invoiceDate next invoice date
     * @param string $failure     failure message
     *
     * @return void
     */
    public function testValidatorFailsValidationForInvalidInvoiceDate($invoiceDate, $failure)
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getNextInvoiceDate')->once()->andReturn($invoiceDate);

        $validator = Mockery::mock('AccountChange_InvoiceDateCorrectPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->andReturn($service);

        $this->assertFalse($validator->validate());
        $this->assertEquals($failure, $validator->getFailure());
    }

    /**
     * @return array
     */
    public function testValidatorFailsValidationForInvalidInvoiceDateDataProvider()
    {
        $invoiceInThePast = I18n_Date::fromString('2019-07-22 00:00:00');

        return [
            [null, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE],
            [new DateTime(), AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE],
            [$invoiceInThePast, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE_IN_PAST],
        ];
    }

    /**
     * @return void
     */
    public function testValidatorFailsValidationWhenInvalidArgumentExceptionIsThrown()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andThrow(InvalidArgumentException::class);

        $validator = new AccountChange_InvoiceDateCorrectPolicy($actor);

        $this->assertFalse($validator->validate());
        $this->assertEquals($validator::ERROR_MESSAGE_INVOICE_DATE, $validator->getFailure());
    }

    /**
     * @return void
     */
    public function testValidatorFailsValidationWhenBillingApiClientBillingServiceExceptionIsThrown()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andThrow(BillingApiClientBillingServiceException::class);

        $validator = new AccountChange_InvoiceDateCorrectPolicy($actor);

        $this->assertFalse($validator->validate());
        $this->assertEquals($validator::ERROR_BILLING_API_EXCEPTION, $validator->getFailure());
    }

    /**
     * @return void
     */
    public function testValidatorFailsValidationWhenBillingApiClientPartnerResellerAccessExceptionIsThrown()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andThrow(BillingApiClientPartnerResellerAccessException::class);

        $validator = new AccountChange_InvoiceDateCorrectPolicy($actor);

        $this->assertFalse($validator->validate());
        $this->assertEquals($validator::ERROR_BILLING_API_RESELLER_EXCEPTION, $validator->getFailure());
    }

    /**
     * @return void
     */
    public function testValidatorPassesValidationForValidInvoiceDate()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $invoiceDate = I18n_Date::fromString(
            (new DateTime('tomorrow'))->format('Y-m-d H:i:s')
        );

        $service = Mockery::mock();
        $service->shouldReceive('getNextInvoiceDate')->once()->andReturn($invoiceDate);

        $validator = Mockery::mock('AccountChange_InvoiceDateCorrectPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->once()->andReturn($service);

        $this->assertTrue($validator->validate());
    }
}
