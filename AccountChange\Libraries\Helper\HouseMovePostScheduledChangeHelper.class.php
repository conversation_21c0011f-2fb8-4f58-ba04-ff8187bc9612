<?php
/**
 * Post scheduled change actions for house move
 * Moved from Controller::complete to allow use via PerformChangeApi
 *
 * <AUTHOR> <<EMAIL>>
 */

use Plusnet\HouseMoves\Services\HouseMoveService;
use Plusnet\HouseMoves\Services\ServiceManager;

class AccountChange_HouseMovePostScheduledChangeHelper
{
    /**
     * @var int|null
     */
    private $contractDuration;

    /**
     * @var Plusnet\HouseMoves\Models\HouseMove|null
     */
    private $houseMove;

    /**
     * @var Plusnet\HouseMoves\Services\HouseMoveScheduledAccountChangeService
     */
    private $houseMoveScheduledAccountChange;

    /**
     * @var Plusnet\HouseMoves\Services\HouseMoveScheduledAccountChangeService
     */
    private $houseMoveServiceNoteService;

    /**
     * @var HouseMoveService
     */
    private $houseMoveService;

    /**
     * @var int
     */
    private $serviceId;

    /**
     * @param int      $serviceId        service id
     * @param int|null $contractDuration contract duration
     * @return void
     */
    public function __construct($serviceId, $contractDuration)
    {
        $this->serviceId = $serviceId;
        $this->contractDuration = $contractDuration;
        $this->houseMoveService = ServiceManager::getService('HouseMoveService');
        $this->houseMove = $this->houseMoveService->getHouseMoveByServiceId($serviceId);
        $this->houseMoveScheduledAccountChange = ServiceManager::getService('HouseMoveScheduledAccountChangeService');
        $this->houseMoveServiceNoteService = ServiceManager::getService('ServiceNoteService');
    }

    /**
     * Execute the action
     * @return void
     */
    public function execute()
    {
        $this->createDefaultWlrComponents();
        $this->updateHouseMove();
        Db_Manager::commit();
    }

    /**
     * Create Wlr components in case of house move.
     * @return void
     */
    protected function createDefaultWlrComponents()
    {
        $this->includeLegacyFiles();
        $componentsArray = $this->getChargeableComponents();

        $wlrComponentCreated = false;
        foreach ($componentsArray as $componentMeta) {
            if ($componentMeta['vchComponentTypeHandle'] == 'WLR') {
                if ($componentMeta['status'] == 'unconfigured' || $componentMeta['status'] == 'queued-activate') {
                    $wlrComponentCreated = true;
                } elseif ($componentMeta['status'] == 'active' && $componentMeta['vchHandle'] == 'SUBSCRIPTION') {
                    $intServiceComponentId = $componentMeta['component_type_id'];
                    $intTariffID = $componentMeta['intTariffID'];
                }
            }
        }

        if (!$wlrComponentCreated &&
            isset($intTariffID) &&
            $this->expectingPhoneForHouseMove() &&
            !empty($intServiceComponentId)
        ) {
            $houseMoveComponentsService = ServiceManager::getService('ComponentsService');
            $productComponentOptions = $houseMoveComponentsService->getProductComponentOptionsByServiceId(
                $intServiceComponentId,
                $this->serviceId
            );

            // Not removing phone, and this is the phone component. Add it.
            $this->createWlr3Component(
                $intServiceComponentId,
                $intTariffID,
                $productComponentOptions
            );
        }
    }

    /**
     * Get Chargeable Components By Service Id.
     *
     * @return array
     */
    protected function getChargeableComponents()
    {
        return CProductComponent::getChargeableComponentsFromServiceId($this->serviceId);
    }

    /**
     * Will create the phone component
     *
     * @param int   $intServiceComponentId   Service Component Id
     * @param int   $intTariffID             Tariff Id
     * @param array $productComponentOptions Product component options
     *
     * @return void
     */
    protected function createWlr3Component(
        $intServiceComponentId,
        $intTariffID,
        $productComponentOptions = array()
    ) {
        CWlrProduct::create(
            $this->serviceId,
            $intServiceComponentId,
            $intTariffID,
            $productComponentOptions,
            ''
        );
    }

    /**
     * If this is an account change as part of a house move, checks whether a phone is expected at the new address
     *
     * @return boolean
     */
    protected function expectingPhoneForHouseMove()
    {
        return $this->houseMove->getSiOrderReference() !== '0';
    }

    /**
     * @return void
     */
    private function updateHouseMove()
    {
        $this->setHouseMoveContractDuration();

        $this->houseMoveScheduledAccountChange->insertHouseMoveScheduledAccountChange($this->serviceId);

        $this->houseMoveServiceNoteService->create($this->serviceId);
    }

    /**
     * Will calculate the house move contract duration to write to tblHouseMove
     *
     * @return void
     */
    private function setHouseMoveContractDuration()
    {
        if ($this->contractDuration !== null) {
            $this->houseMove->setContractDuration((int)$this->contractDuration);

            $this->houseMove->setHasRecontract($this->isRecontract());

            $this->houseMoveService->save($this->houseMove);
        }
    }

    /**
     * @return bool
     */
    private function isRecontract()
    {
        return $this->contractDuration != '0';
    }

    /**
     * Inclusion of legacy files so we can mock them.
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
        require_once '/local/data/mis/database/database_libraries/secure-transaction-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/HomePhone.class.php';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';

        // Used to apply promotion code
        require_once '/local/data/mis/portal_modules/signup/v4/signup_regrade_shared_functions.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/Retention/BroadbandDiscount.class.php';

        // ADSL details
        require_once '/local/data/mis/database/database_libraries/adsl-access.inc';
        require_once '/local/data/mis/common_library_functions/common_application_apis/common-adsl-api.inc';
    }
}