<?php
/**
 * Radius action
 *
 * Action that performs all radius related changes
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_Radius.class.php,v 1.2 2009-01-27 07:07:46 bselby Exp $
 * @since     File available since 2008-09-02
 */

/**
 * Billing action class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Action_Radius extends AccountChange_Action
{
    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $this->setRadiusData();
        $this->resetToDefaultSpeed();
    }

    /**
     * Set the radius data
     *
     * @return void
     */
    public function setRadiusData()
    {
        $this->includeLegacyFiles();
        config_dialup_set_all_radius_data($this->intServiceId);
    }

    /**
     * Reset to the default speed
     *
     * @return void
     */
    protected function resetToDefaultSpeed()
    {
        $this->includeLegacyFiles();
        RadiusResetToDefaultSpeed($this->intServiceId);
    }

    /**
     * Include all the legacy files we need, so we can also mock this for unit tests
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/components/config-dialup-access.inc';
        require_once '/local/data/mis/database/database_libraries/radius-access.inc';
    }
}
