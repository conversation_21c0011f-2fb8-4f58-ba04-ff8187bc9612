server: coredb
role: slave
rows: single
statement:

SELECT
	sd.service_definition_id,
	sd.name,
	sd.isp,
	t.intCostIncVatPence/100 AS minimum_charge,
	t.intTariffID,
	sd.initial_charge,
	sd.type,
	sd.password_visible_to_support,
	sd.requires,
	sd.date_created,
	sd.end_date,
	sd.signup_via_portal,
	sd.blurb,
	cl.vchHandle AS vchContract,
	sc.name AS strComponentName
FROM  userdata.services AS s
INNER JOIN userdata.components AS c
	ON c.service_id = s.service_id
INNER JOIN products.service_definitions AS sd
	ON sd.service_definition_id = s.type
INNER JOIN products.service_components sc
	ON c.component_type_id = sc.service_component_id
INNER JOIN products.tblServiceComponentProduct scp
	ON sc.service_component_id = scp.intServiceComponentId
INNER JOIN products.tblServiceComponentProductType scpt
	ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
INNER JOIN userdata.tblProductComponentInstance pci
	ON c.component_id = pci.intComponentID
INNER JOIN dbProductComponents.tblTariff t
	ON t.intTariffID = pci.intTariffID
INNER JOIN dbProductComponents.tblContractLength cl
	ON cl.intContractLengthID = t.intContractLengthID
INNER JOIN dbProductComponents.tblPaymentFrequency pf
	ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID
WHERE
	s.service_id = :intServiceId
	AND (pci.dtmEnd is NULL OR pci.dtmEnd > NOW())
	AND scpt.vchHandle = 'INTERNET_CONNECTION'
