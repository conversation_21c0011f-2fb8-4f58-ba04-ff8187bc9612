server: coredb
role: master
rows: single
statement:
    SELECT si.sales_invoice_id  
      FROM userdata.services AS s 
INNER JOIN userdata.users AS u 
        ON u.user_id = s.user_id 
INNER JOIN userdata.accounts AS a 
        ON a.customer_id = u.customer_id 
INNER JOIN financial.sales_invoices AS si
        ON si.account_id = a.account_id
INNER JOIN financial.sales_invoice_items AS sii
        ON sii.sales_invoice_id = si.sales_invoice_id
     WHERE sii.item_description LIKE '%Line Rental Saver%'
       AND s.service_id = :serviceId;
