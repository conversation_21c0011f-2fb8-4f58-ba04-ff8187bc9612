<?php
/**
 * Base action
 *
 * Action that performs all the basic functionality for actions
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action.class.php,v 1.2 2009-01-27 07:07:15 bselby Exp $
 * @link      link
 * @since     File available since 2008-09-01
 */

/**
 * Base action class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2008 PlusNet
 * @link      link
 */

abstract class AccountChange_Action
{
    /**
    * Handle for auto problem template handle.
    *
    * @var string
    */
    const AUTO_PROBLEM_TEMPLATE_HANDLE = 'accountChangeActionFailed';

    /**
     * Service Id
     *
     * @var int
     */
    protected $intServiceId;

    /**
     * Options
     *
     * @var array
     */
    protected $arrOptions = array();

    /**
     * Supplier product rules
     *
     * @var Product_SupplierProductRules
     */
    protected $supplierProductRules = null;

    /**
     * Constructor
     *
     * @param int   $intServiceId Service id
     * @param array $arrOptions   Options
     */
    public function __construct($intServiceId, array $arrOptions = array())
    {
        $this->initialise($intServiceId, $arrOptions);
    }

    /**
     * Initialise
     *
     * @param int   $intServiceId Service id
     * @param array $arrOptions   Options
     * @throws AccountChange_Action_ManagerException
     *
     * @return void
     */
    public function initialise($intServiceId, array $arrOptions = array())
    {
        if (!preg_match('/^[0-9]*$/', $intServiceId)) {
            throw new AccountChange_Action_ManagerException(
                'Non numeric service id',
                AccountChange_Action_ManagerException::ERR_INVALID_SERVICE_ID_TYPE
            );
        }

        $this->intServiceId = (int)$intServiceId;
        $this->arrOptions   = $arrOptions;
    }

    /**
     * Getter for the service id
     *
     * @return int
     */
    public function getServiceId()
    {
        return (int)$this->intServiceId;
    }

    /**
     * Raise a ticket on the account
     *
     * @param string $strTicketComment Ticket comment
     * @param string $strPoolHandle    Pool handle
     * @param bool $closedTicket       Raise ticket in closed state?
     * @param string $causeId          The ticket cause handle
     *
     * @return int
     * @throws Db_TransactionException
     */
    public function raiseTicket($strTicketComment = '', $strPoolHandle = '', $closedTicket = false, $causeId = '')
    {
        if (!empty($strTicketComment) && !empty($strPoolHandle)) {
            $objTicketClient  = BusTier_BusTier::getClient('tickets');
            //the solution below is because of the way how PDO is hadling string columns in db but you passing an int
            //var to where cond
            //1) in db you have '********', you passing 1232323 - it will not match (you have to pad it with leading 0s)
            //2) in db you have  '1232323' - if you will padd it with 0 it will not work - so you have to pass 1232323
            try {
                $objBusinessActor = Auth_BusinessActor::getActorByExternalUserId(
                    str_pad($this->intServiceId, 8, '0', STR_PAD_LEFT)
                );
            } catch (Exception $e) {
                $objBusinessActor = Auth_BusinessActor::getActorByExternalUserId($this->intServiceId);
            }

            $objPool = TicketClient_DbTeam::getTeamByHandle($strPoolHandle);

            $objTicket = $objTicketClient->createTicket($objBusinessActor, Db_Manager::DEFAULT_TRANSACTION);
            $objTicket->comment($objBusinessActor, $strTicketComment, $objPool->getTeamId(), null, $closedTicket);
            $objTicket->attachTicketCause($objTicket->getTicketId(), $causeId);
            if ($closedTicket) {
                $objTicket->close($objBusinessActor);
            }

            // In order to deal with the legacy and framework code locking each other
            // it was decided to commit the default transaction everytime we called the database
            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
            return $objTicket->getId();
        }

        return 0;
    }

    /**
     * Raise a service event on the account
     *
     * @param string $strBody                Service notice body
     * @param int    $intServiceNoticeTypeId Service notice type id
     *
     * @return void
     */
    public function raiseServiceEvent($strBody = '', $intServiceNoticeTypeId = 1)
    {
        if (!empty($strBody)) {
            $objClient = BusTier_BusTier::getClient('serviceNotices');
            //the solution below is because of the way how PDO is hadling string columns in db but you passing an int
            //var to where cond
            //1) in db you have '********', you passing 1232323 - it will not match (you have to pad it with leading 0s)
            //2) in db you have  '1232323' - if you will padd it with 0 it will not work - so you have to pass 1232323
            try {
                $objBusinessActor = Auth_BusinessActor::getActorByExternalUserId(
                    str_pad($this->intServiceId, 8, '0', STR_PAD_LEFT)
                );
            } catch (Exception $e) {
                $objBusinessActor = Auth_BusinessActor::getActorByExternalUserId($this->intServiceId);
            }

            $objServiceNotice = $objClient->createServiceNotice($objBusinessActor, Db_Manager::DEFAULT_TRANSACTION);
            $objServiceNotice->setBody($strBody);
            $objServiceNotice->setServiceNoticeTypeId($intServiceNoticeTypeId);
            $objServiceNotice->setActionerId(0);
            $objServiceNotice->write();
            // In order to deal with the legacy and framework code locking each other
            // it was decided to commit the default transaction everytime we called the database
            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        }
    }

    /**
     * Raises an auto problem using the template stored in
     * /local/codebase2005/content/templatedautoproblems/PLUSNET/WORKPLACE_ISP_ADMIN
     *
     * @param Exception $e Exception thrown
     *
     * @return void
     */
    public function raiseAutoProblem($e)
    {
        $client = $this->getAutoProblem();
        $actor = $this->getLoggedInOrScriptActor();

        $autoProblem = $client->prepareAutoProblem(
            self::AUTO_PROBLEM_TEMPLATE_HANDLE,
            array(
                'className' => get_class($this),
                'actorId' => $actor->getActorId(),
                'serviceId' => $this->intServiceId,
                'exception' => $e
            ),
            $actor
        );

        $autoProblem->raiseProblem();
    }

    /**
     * Return an autoproblem object
     *
     * @return AutoProblem_AutoProblemClient
     */
    protected function getAutoProblem()
    {
        return BusTier_BusTier::getClient('autoproblem');
    }

    /**
     * Return the logged in or script actor object
     *
     * @return Auth_BusinessActor
     */
    protected function getLoggedInOrScriptActor()
    {
        return BusTier_BusTier::getLoggedInOrScriptActor();
    }

    /**
     * Send an email to the customer using the email template specified.
     * Templates are stored in:
     * /local/codebase2005/content/templatedemails/PLUSNET/WORKPLACE_ISP_ADMIN/WORKPLACE_ROOT/PLUSNET_PARTNER_ROOT/?_ISP
     *
     * @param string $emailTemplateHandle Handle of the email template
     * @param array  $emailData           Additional data for the template (optional)
     *
     * @return void
     */
    protected function sendEmail($emailTemplateHandle, $emailData = array())
    {
        try {
            $objIspAutomatedEmail = $this->getIspAutomatedEmail($this->intServiceId);
            $objIspAutomatedEmail->prepareIspAutomatedEmail($emailTemplateHandle, $emailData);
            $objIspAutomatedEmail->send();
        } catch (Exception $e) {
            $this->raiseAutoProblem($e);
        }
    }

    /**
     * Returns a new IspAutomatedEmail_IspAutomatedEmail object for the service ID given.
     *
     * @param integer $serviceId Service id
     *
     * @return IspAutomatedEmail_IspAutomatedEmail
     */
    protected function getIspAutomatedEmail($serviceId)
    {
        return new IspAutomatedEmail_IspAutomatedEmail($serviceId);
    }

    /**
     * Execution of the action that needs to be performed
     *
     * @return void
     */
    abstract public function execute();

    /**
     * function to get supplier Product Rules
     *
     * @return Product_SupplierProductRules
     */
    protected function getSupplierProductRules()
    {
        if (empty($this->supplierProductRules)) {
            $registry = AccountChange_Registry::instance();
            $lineCheckResult = $this->getLineCheckResult($registry);
            $newSdi = $registry->getEntry('intNewServiceDefinitionId');
            $productRules = AccountChange_ProductRules::instance();

            $capping = $productRules->getLinecheckSpeedCaps($newSdi);

            // The line check result we have in the registry is for the current product
            // so we need to amend it with the capping for the new product..
            $lineCheckResult->amendResultWithNewCapping($capping['downCap'], $capping['upCap']);

            $this->supplierProductRules = $this->newSupplierProductRules(
                $lineCheckResult,
                new Int($newSdi),
                new Int($this->intServiceId)
            );
        }

        return $this->supplierProductRules;
    }

    /**
     * function to get line check result
     *
     * @param AccountChange_Registry $registry Registry
     * @throws Exception
     * @return LineCheck_Result
     */
    public function getLineCheckResult($registry)
    {
        $lineCheckResult = $registry->getEntry('objLineCheckResult');

        if (!($lineCheckResult instanceof LineCheck_Result)) {
            throw new Exception('Invalid line check result');
        }

        return $lineCheckResult;
    }

    /**
     * Returns a new Product_SupplierProductRules object
     *
     * @param LineCheck_Result $lineCheckResult        Line Check result
     * @param Int              $newServiceDefinitionId New Service Definition Id
     * @param Int              $serviceId              Service id
     *
     * @return Product_SupplierProductRules
     */
    protected function newSupplierProductRules(
        LineCheck_Result $lineCheckResult,
        Int $newServiceDefinitionId,
        Int $serviceId = null
    ) {
        return new Product_SupplierProductRules(
            $lineCheckResult,
            $newServiceDefinitionId,
            $serviceId
        );
    }

    /**
     * Accessor for the new supplier product
     *
     * @return Product_SupplierProduct
     */
    protected function getSupplierProduct()
    {
        return $this->getSupplierProductRules()->getSupplierProduct();
    }

    /**
     * Inclusion of legacy files so we can mock the include if needed
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/adsl-access.inc';
    }

    /**
     * Wrapper to obtain ProductFamily
     *
     * @param int $serviceDefinitionId Service Definition Id
     *
     * @return ProductFamily_ProductFamily
     */
    protected function getProductFamily($serviceDefinitionId)
    {
        return ProductFamily_Factory::getFamily($serviceDefinitionId);
    }
}
