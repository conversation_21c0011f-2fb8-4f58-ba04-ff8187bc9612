<?php

/**
 * File Product_BroadbandProductPriceSort.test.php
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       http://confluence.internal.plus.net/display/PRI/Solus+and+Dual+Play+Product+Combinations
 */

/**
 * Class AccountChange_Product_BroadbandProductPriceSortTest
 *
 * Unit tests for AccountChange_Product_BroadbandProductPriceSortTest
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Product_BroadbandProductPriceSortTest extends PHPUnit_Framework_TestCase
{
    private function buildProductArray()
    {
        $products = array();
        $products[] = $this->buildProduct(12, 30.23);
        $products[] = $this->buildProduct(13, 40.23);
        $products[] = $this->buildProduct(14, 12.23);
        $products[] = $this->buildProduct(15, 10.23);
        $products[] = $this->buildProduct(16, 60.23);
        return $products;
    }

    /**
     * @covers AccountChange_Product_BroadbandProductPriceSort::sort
     */
    public function testSortProductsByPrice()
    {
        $products = $this->buildProductArray();
        $products = AccountChange_Product_BroadbandProductPriceSort::sort($products);

        $this->assertEquals(
            $products[0]['intSdi'],
            15,
            'expected product with sid 15 but got sid '.$products[0]['intSdi']
        );
        $this->assertEquals(
            $products[1]['intSdi'],
            14,
            'expected product with sid 14 but got sid '.$products[1]['intSdi']
        );
        $this->assertEquals(
            $products[2]['intSdi'],
            12,
            'expected product with sid 12 but got sid '.$products[2]['intSdi']
        );
        $this->assertEquals(
            $products[3]['intSdi'],
            13,
            'expected product with sid 13 but got sid '.$products[3]['intSdi']
        );
        $this->assertEquals(
            $products[4]['intSdi'],
            16,
            'expected product with sid 16 but got sid '.$products[4]['intSdi']
        );
    }

    /**
     * @covers AccountChange_Product_BroadbandProductPriceSort::moveCurrentProductVariantsToEndOfList
     */
    public function testMoveCurrentProductVariantsToEndOfList()
    {
        $products = $this->buildProductArray();
        $products = AccountChange_Product_BroadbandProductPriceSort::sort($products);

        $broadbandProductVariants = $this->getBroadbandVariants(13, 20);
        $products = AccountChange_Product_BroadbandProductPriceSort::moveCurrentProductVariantsToEndOfList(
            $products,
            $broadbandProductVariants
        );

        $this->assertEquals(
            $products[0]['intSdi'],
            15,
            'expected product with sid 15 but got sid '.$products[0]['intSdi']
        );
        $this->assertEquals(
            $products[1]['intSdi'],
            14,
            'expected product with sid 14 but got sid '.$products[1]['intSdi']
        );
        $this->assertEquals(
            $products[2]['intSdi'],
            12,
            'expected product with sid 12 but got sid '.$products[2]['intSdi']
        );
        $this->assertEquals(
            $products[3]['intSdi'],
            16,
            'expected product with sid 16 but got sid '.$products[3]['intSdi']
        );
        $this->assertEquals(
            $products[4]['intSdi'],
            13,
            'expected product with sid 13 to be last'.$products[4]['intSdi']
        );
    }

    public function testMoveCurrentProductToEndOfList()
    {
        $products = $this->buildProductArray();
        $products = AccountChange_Product_BroadbandProductPriceSort::sort($products);
        $products = AccountChange_Product_BroadbandProductPriceSort::moveCurrentProductToEndOfList(
            $products,
            13
        );

        $this->assertEquals(
            $products[0]['intSdi'],
            15,
            'expected product with sid 15 but got sid '.$products[0]['intSdi']
        );
        $this->assertEquals(
            $products[1]['intSdi'],
            14,
            'expected product with sid 14 but got sid '.$products[1]['intSdi']
        );
        $this->assertEquals(
            $products[2]['intSdi'],
            12,
            'expected product with sid 12 but got sid '.$products[2]['intSdi']
        );
        $this->assertEquals(
            $products[3]['intSdi'],
            16,
            'expected product with sid 16 but got sid '.$products[3]['intSdi']
        );
        $this->assertEquals(
            $products[4]['intSdi'],
            13,
            'expected product with sid 13 to be last'.$products[4]['intSdi']
        );
    }

    private function buildProduct($id, $price)
    {
        $product = array();
        $product['intSdi'] = $id;
        $product['intProductCost'] = new I18n_Currency('gbp', $price);
        return $product;
    }

    private function getBroadbandVariants($solus, $dualplay)
    {
        return array (
            'solus' => $solus,
            'dualplay' => $dualplay
        );
    }
}
