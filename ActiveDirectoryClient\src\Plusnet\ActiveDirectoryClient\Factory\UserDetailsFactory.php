<?php

namespace Plusnet\ActiveDirectoryClient\Factory;

use Plusnet\ActiveDirectoryClient\Exceptions\ActiveDirectoryClientException;
use Plusnet\ActiveDirectoryClient\Models\UserDetails;

/**
 * Class UserDetailsFactory
 * @package Plusnet\ActiveDirectoryClient\Factory
 */
class UserDetailsFactory
{
    /**
     * Create new UserDetails instance.
     *
     * @param string      $ein
     * @param bool|string $isPartner
     *
     * @return UserDetails
     */
    public function create($ein, $isPartner)
    {
        return new UserDetails($ein, $isPartner);
    }

    /**
     * Create new UserDetails instance from array.
     *
     * @param array $userDetailsArray array containing ein and is_partner
     *
     * @return UserDetails
     */
    public function createFromArray($userDetailsArray)
    {
        $this->assertKeyIsSet(UserDetails::KEY_EIN, $userDetailsArray);
        $this->assertKeyIsSet(UserDetails::KEY_IS_PARTNER, $userDetailsArray);

        return new UserDetails(
            $userDetailsArray[UserDetails::KEY_EIN],
            $userDetailsArray[UserDetails::KEY_IS_PARTNER]
        );
    }

    /**
     * Checks that keys are set if not throw an exception
     *
     * @param string $key   Array Key
     * @param string $array Array Key data
     */
    private function assertKeyIsSet($key, $array)
    {
        if (isset($array[$key])) {
            return;
        }

        throw new ActiveDirectoryClientException("Can't create UserDetails object as missing key: " . $key);
    }

    /**
     * If data is Invalid set ein and isPartner
     * @return UserDetails
     */
    public function createInvalidUserDetails()
    {
        return new UserDetails(
            UserDetails::INVALID_EIN,
            UserDetails::INVALID_IS_PARTNER
        );
    }
}
