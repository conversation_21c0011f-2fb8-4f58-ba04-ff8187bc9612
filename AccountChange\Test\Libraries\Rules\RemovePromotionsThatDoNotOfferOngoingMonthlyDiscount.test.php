<?php
use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\C2mApiClient\Entity\Discount;
use Plusnet\C2mApiClient\Entity\DiscountValue;
use Plusnet\C2mApiClient\Entity\DiscountType;
use Plusnet\C2mApiClient\Entity\PaymentFrequency;

class RemovePromotionsThatDoNotOfferOngoingMonthlyDiscountTest extends PHPUnit_Framework_TestCase
{

    public function testThatPromotionsContainingDiscountsThatOfferNonMonthlyDurationTypeAreRemoved()
    {

        $discountValue1 = new DiscountValue();
        $discountValue1->setDurationType("MONTHS");
        $discount1 = new Discount();
        $discount1->setDiscountValues([$discountValue1]);
        $discount1->setType(DiscountType::ON_GOING);
        $promotion1 = new Promotion();
        $promotion1->setCode('Promo1');
        $promotion1->setDiscounts([$discount1]);

        $discountValue2 = new DiscountValue();
        $discountValue2->setDurationType(PaymentFrequency::ANNUALLY);
        $discount2 = new Discount();
        $discount2->setDiscountValues([$discountValue2]);
        $discount2->setType(DiscountType::ON_GOING);
        $promotion2 = new Promotion();
        $promotion2->setCode('Promo2');
        $promotion2->setDiscounts([$discount2]);

        $discountValue3 = new DiscountValue();
        $discountValue3->setDurationType("MONTHS");
        $discount3 = new Discount();
        $discount3->setDiscountValues([$discountValue3]);
        $discount3->setType(DiscountType::ON_GOING);
        $promotion3 = new Promotion();
        $promotion3->setCode('Promo3');
        $promotion3->setDiscounts([$discount3]);

        $discountValue4 = new DiscountValue();
        $discountValue4->setDurationType(PaymentFrequency::ONE_OFF);
        $discount4 = new Discount();
        $discount4->setDiscountValues([$discountValue4]);
        $discount4->setType(DiscountType::ON_GOING);
        $promotion4 = new Promotion();
        $promotion4->setCode('Promo4');
        $promotion4->setDiscounts([$discount4]);

        $promotions = [$promotion1, $promotion2, $promotion3, $promotion4];

        $RemovePromotionsThatDoNotOfferOngoingMonthlyDiscount = new RemovePromotionsThatDoNotOfferOngoingMonthlyDiscount();
        $promotions = $RemovePromotionsThatDoNotOfferOngoingMonthlyDiscount->handle($promotions);

        $this->assertEquals(2, count($promotions));
        $this->assertEquals('Promo1', $promotions[0]->getCode());
        $this->assertEquals('Promo3', $promotions[2]->getCode());
    }


    public function testRemovePromotionsThatDontHaveMonthlyDiscountIgnoresDiscountsWithTypeThatIsNotOnGoing()
    {
        $discountValue = new DiscountValue();
        $discountValue->setDurationType("MONTHS");
        $discount = new Discount();
        $discount->setDiscountValues([$discountValue]);
        $discount->setType(DiscountType::UP_FRONT);
        $promotion1 = new Promotion();
        $promotion1->setCode('Promo1');
        $promotion1->setDiscounts([$discount]);


        $discountValue = new DiscountValue();
        $discountValue->setDurationType("MONTHS");
        $discount = new Discount();
        $discount->setDiscountValues([$discountValue]);
        $discount->setType(DiscountType::CASH_BACK);

        $discountValue2 = new DiscountValue();
        $discountValue2->setDurationType("MONTHS");

        $discount2 = new Discount();
        $discount2->setDiscountValues([$discountValue]);
        $discount2->setType(DiscountType::ON_GOING);

        $promotion2 = new Promotion();
        $promotion2->setCode('Promo2');
        $promotion2->setDiscounts([$discount, $discount2]);

        $promotions = [$promotion1, $promotion2];

        $ruleUnderTest = new RemovePromotionsThatDoNotOfferOngoingMonthlyDiscount();
        $promotions = $ruleUnderTest->handle($promotions);

        $this->assertEquals(1, count($promotions));
        $this->assertEquals('Promo2', $promotions[1]->getCode());

    }


}
