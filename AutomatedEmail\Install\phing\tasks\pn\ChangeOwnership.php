<?php
include_once 'phing/Task.php';
class ChangeOwnership extends Task {

    private $_strFile = null;
    private $_strOwner = null;
    private $_strGroup = null;
    private $_strMode = null;

    public function setFile($str) {
        $this->_strFile = $str;
    }
    
    public function setOwner($strUser) {
        $this->_strOwner = $strUser;
    }

    public function setGroup($str) {
        $this->_strGroup = $str;
    }    

    public function setMode($str) {
        $this->_strMode = $str;
    }    

    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strFile) {
		    throw new BuildException("You must specify the file attribute", $this->getLocation());
		}  
		
		$msg = '';
		if ($this->_strOwner) {
			$msg.= "chown -R $this->_strOwner ";
			system("chown -R $this->_strOwner $this->_strFile");
		}  

		if ($this->_strGroup) {
			$msg.= "| chgrp -R $this->_strGroup ";
			system("chgrp -R $this->_strGroup $this->_strFile");
		}  

		if ($this->_strMode) {
			$msg.= "| chmod -R $this->_strMode ";
			system("chmod -R $this->_strMode $this->_strFile");
		}  
		$this->log("$msg $this->_strFile");

    }
}