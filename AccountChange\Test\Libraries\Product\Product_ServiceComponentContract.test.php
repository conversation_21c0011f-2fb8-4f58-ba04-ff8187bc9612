<?php
/**
 * Service Component Contract
 *
 * Testing class for the AccountChange_Product_ServiceComponentContract class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_ServiceComponentContract.test.php,v 1.2 2009-01-27 09:07:53 bselby Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Service Definition Contract Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_ServiceComponentContract_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @covers AccountChange_Product_ServiceComponentContract::__construct
     */
    public function testConstructorPopulatesProperties()
    {
        $strContractLengthHandle   = 'MY_MONTHLY';
        $strPaymentFrequencyHandle = 'MY_MONTHLY';
        $strProductComponentHandle = 'MY_SUBSCRIPTION';
        $uxtContractEndDate        = time();
        $uxtContractStartDate      = time();

        $objContract = new AccountChange_Product_ServiceComponentContract(
            $strContractLengthHandle,
            $strPaymentFrequencyHandle,
            $strProductComponentHandle,
            $uxtContractEndDate,
            $uxtContractStartDate
        );

        $this->assertAttributeEquals($strContractLengthHandle, 'strContractLengthHandle', $objContract);
        $this->assertAttributeEquals($strPaymentFrequencyHandle, 'strPaymentFrequencyHandle', $objContract);
        $this->assertAttributeEquals($strProductComponentHandle, 'strProductComponentHandle', $objContract);
        $this->assertAttributeEquals($uxtContractEndDate, 'uxtContractEndDate', $objContract);
        $this->assertAttributeEquals($uxtContractStartDate, 'uxtContractStartDate', $objContract);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::fetchTariffId
     */
    public function testFetchTariffIdWhenItsEmpty()
    {
        $intServiceComponentId     = 1234;
        $strContractLengthHandle   = 'MONTHLY';
        $strPaymentFrequencyHandle = 'MONTHLY';
        $strProductComponentHandle = 'SUBSCRIPTION';
        $intTariffId               = 567;

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getProductPaymentFrequencyOptions'),
            array($strContractLengthHandle, $strPaymentFrequencyHandle, $strProductComponentHandle)
        );
        $objContract->expects($this->once())
                    ->method('getProductPaymentFrequencyOptions')
                    ->will($this->returnValue(array(array('intTariffID' => $intTariffId))));

        $intResult = $objContract->fetchTariffId($intServiceComponentId);

        $this->assertEquals($intResult, $intTariffId);
        $this->assertAttributeEquals($intTariffId, 'intTariffId', $objContract);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::fetchTariffId
     */
    public function testFetchTariffIdWhenItsPopulated()
    {
        $intServiceComponentId     = 1234;
        $strContractLengthHandle   = 'MONTHLY';
        $strPaymentFrequencyHandle = 'MONTHLY';
        $strProductComponentHandle = 'SUBSCRIPTION';
        $intTariffId               = 567;

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getProductPaymentFrequencyOptions'),
            array($strContractLengthHandle, $strPaymentFrequencyHandle, $strProductComponentHandle)
        );
        $objContract->expects($this->once())
                    ->method('getProductPaymentFrequencyOptions')
                    ->will($this->returnValue(array(array('intTariffID' => $intTariffId))));

        $intResult1 = $objContract->fetchTariffId($intServiceComponentId);
        $intResult2 = $objContract->fetchTariffId($intServiceComponentId);

        $this->assertEquals($intResult1, $intTariffId);
        $this->assertEquals($intResult1, $intResult2);
        $this->assertAttributeEquals($intTariffId, 'intTariffId', $objContract);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::fetchTariffId
     */
    public function testForceFetchTariffId()
    {
        $intServiceComponentId     = 1234;
        $strContractLengthHandle   = 'MONTHLY';
        $strPaymentFrequencyHandle = 'MONTHLY';
        $strProductComponentHandle = 'SUBSCRIPTION';
        $intTariffId               = 567;

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getProductPaymentFrequencyOptions'),
            array($strContractLengthHandle, $strPaymentFrequencyHandle, $strProductComponentHandle)
        );
        $objContract->expects($this->exactly(2))
                    ->method('getProductPaymentFrequencyOptions')
                    ->will($this->returnValue(array(array('intTariffID' => $intTariffId))));

        $intResult1 = $objContract->fetchTariffId($intServiceComponentId);
        $intResult2 = $objContract->fetchTariffId($intServiceComponentId, true);

        $this->assertEquals($intResult1, $intTariffId);
        $this->assertEquals($intResult1, $intResult2);
        $this->assertAttributeEquals($intTariffId, 'intTariffId', $objContract);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::fetchTariffId
     */
    public function testFetchTariffIdFallsBackToDefault()
    {
        $intServiceComponentId     = 1234;
        $strContractLengthHandle   = 'ANNUAL';
        $strPaymentFrequencyHandle = 'MONTHLY';
        $strProductComponentHandle = 'SUBSCRIPTION';
        $intTariffId               = 567;

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getProductPaymentFrequencyOptions'),
            array($strContractLengthHandle, $strPaymentFrequencyHandle, $strProductComponentHandle)
        );
        $objContract->expects($this->at(0))
                    ->method('getProductPaymentFrequencyOptions')
                    ->with($intServiceComponentId, $strProductComponentHandle, $strContractLengthHandle)
                    ->will($this->returnValue(array()));

        $objContract->expects($this->at(1))
                    ->method('getProductPaymentFrequencyOptions')
                    ->with($intServiceComponentId, $strProductComponentHandle, '')
                    ->will($this->returnValue(array(array('intTariffID' => $intTariffId))));

        $intResult = $objContract->fetchTariffId($intServiceComponentId);

        $this->assertEquals($intResult, $intTariffId);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::fetchTariffId
     * @expectedException AccountChange_AccountConfigurationException
     */
    public function testFetchTariffIdThrowsExceptionOnFailure()
    {
        $intServiceComponentId     = 1234;
        $strContractLengthHandle   = 'ANNUAL';
        $strPaymentFrequencyHandle = 'MONTHLY';
        $strProductComponentHandle = 'SUBSCRIPTION';
        $intTariffId               = 567;

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getProductPaymentFrequencyOptions'),
            array(
                $strContractLengthHandle,
                $strPaymentFrequencyHandle,
                $strProductComponentHandle,
            )
        );
        $objContract->expects($this->at(0))
                    ->method('getProductPaymentFrequencyOptions')
                    ->with($intServiceComponentId, $strProductComponentHandle, $strContractLengthHandle)
                    ->will($this->returnValue(array()));

        $objContract->expects($this->at(1))
                    ->method('getProductPaymentFrequencyOptions')
                    ->with($intServiceComponentId, $strProductComponentHandle, '')
                    ->will($this->returnValue(array()));

        $intResult = $objContract->fetchTariffId($intServiceComponentId);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::getContractLengthHandle
     * @covers AccountChange_Product_ServiceComponentContract::getPaymentFrequencyHandle
     * @covers AccountChange_Product_ServiceComponentContract::getProductComponentHandle
     * @covers AccountChange_Product_ServiceComponentContract::getContractEndDate
     * @covers AccountChange_Product_ServiceComponentContract::getContractStartDate
     */
    public function testGetters()
    {
        $strContractLengthHandle   = 'MY_MONTHLY';
        $strPaymentFrequencyHandle = 'MY_YEARLY';
        $strProductComponentHandle = 'MY_SUBSCRIPTION';
        $uxtContractEndDate        = time();
        $uxtContractStartDate      = time();

        $objContract = new AccountChange_Product_ServiceComponentContract(
            $strContractLengthHandle,
            $strPaymentFrequencyHandle,
            $strProductComponentHandle,
            $uxtContractEndDate,
            $uxtContractStartDate
        );

        $this->assertAttributeEquals($objContract->getContractLengthHandle(), 'strContractLengthHandle', $objContract);
        $this->assertAttributeEquals($objContract->getPaymentFrequencyHandle(), 'strPaymentFrequencyHandle', $objContract);
        $this->assertAttributeEquals($objContract->getProductComponentHandle(), 'strProductComponentHandle', $objContract);
        $this->assertAttributeEquals($objContract->getContractEndDate(), 'uxtContractEndDate', $objContract);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::getProductCost
     *
     */
    public function testGetProductCostReturnsTheCorrectPriceAndFormatted()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getProductComponentPrice'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
                         ->method('getProductComponentPrice')
                         ->will($this->returnValue(1500));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $strContractLengthHandle   = 'MY_MONTHLY';
        $strPaymentFrequencyHandle = 'MY_YEARLY';
        $strProductComponentHandle = 'MY_SUBSCRIPTION';
        $uxtContractEndDate        = time();

        $objContract = new AccountChange_Product_ServiceComponentContract(
            $strContractLengthHandle,
            $strPaymentFrequencyHandle,
            $strProductComponentHandle,
            $uxtContractEndDate
        );

        $floActualCost = $objContract->getProductCost(1111);

        $this->assertEquals(15.00, $floActualCost);
    }

    /**
     * @covers AccountChange_Product_ServiceComponentContract::getContractLengthDisplay
     *
     */
    public function testGetContractLengthDisplayCorrectlyFormatsContractHandle()
    {
        $strContractLengthHandle   = 'MONTHLY';
        $strPaymentFrequencyHandle = 'MY_YEARLY';
        $strProductComponentHandle = 'MY_SUBSCRIPTION';
        $uxtContractEndDate        = time();
        $strFormattedContract      = 'Monthly';

        $objContract = new AccountChange_Product_ServiceComponentContract(
            $strContractLengthHandle,
            $strPaymentFrequencyHandle,
            $strProductComponentHandle,
            $uxtContractEndDate
        );

        $strActualContractDisplay = $objContract->getContractLengthDisplay();
        $this->assertEquals($strFormattedContract, $strActualContractDisplay);
    }
}
