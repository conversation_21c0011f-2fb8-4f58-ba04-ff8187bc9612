<?php

use Plusnet\C2mApiClient\Entity\DiscountType;

class AccountChange_EmailHandler_PromotionHelper
{
    private $serviceId;
    private $promotionCode;
    private $broadbandPrice;
    private $serviceDefinitionId;

    /**
     * AccountChange_EmailHandlerPromotionHelper constructor.
     *
     * @param $serviceId
     * @param $promotionCode
     * @param $broadbandPrice
     * @param $serviceDefinitionId
     */
    public function __construct($serviceId, $promotionCode, $broadbandPrice, $serviceDefinitionId)
    {
        $this->serviceId = $serviceId;
        $this->promotionCode = $promotionCode;
        $this->broadbandPrice = $broadbandPrice;
        $this->serviceDefinitionId = $serviceDefinitionId;
    }

    /**
     * Attempt to retrieve discount information from c2m. If the promotion is not present in c2m, check in the database
     * for legacy style promotions. If it is not present there either, return null.
     *
     * @return array|null
     */
    public function getDiscountData()
    {
        $discountData = $this->getDiscountDataFromC2M();

        if ($discountData === null) {
            $discountData = $this->getDiscountDataFromLegacyTables();
        }

        return $discountData;
    }

    /**
     * Attempt to get discount data from a c2m promotion
     *
     * @return array|null
     */
    private function getDiscountDataFromC2M()
    {
        $discountData = null;

        $promotion = $this->getC2MPromotionFromPromotionCode();
        if ($promotion !== null) {

            $discountData = $this->getDiscountDataFromC2MPromotion($promotion);
        }

        return $discountData;
    }

    /**
     * @return \Plusnet\C2mApiClient\Entity\Promotion
     */
    protected function getC2MPromotionFromPromotionCode()
    {
        return AccountChange_C2mPromotionsHelper::getActivePromotionWithPromocode($this->promotionCode);
    }

    /**
     * We have found a c2m promotion, lets convert it into the data we need for the email
     *
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     *
     * @return array
     */
    private function getDiscountDataFromC2MPromotion($promotion)
    {
        $discounts = $this->convertC2MPromotionDiscountsToLegacyFormat($promotion);
        return $this->generateDiscountDataFromDiscounts($discounts);
    }

    /**
     * Convert the c2m promotion into the same format as legacy promotions retrieved from the database.
     * We can then handle both of them in the same way.
     *
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     *
     * @return array
     */
    private function convertC2MPromotionDiscountsToLegacyFormat($promotion)
    {
        $discounts = array();

        foreach ($promotion->getDiscounts() as $discount) {

            $promotionAdapter = new C2MPromotionAdapter();
            $promotionAdapter->setPromotion($promotion);
            $promotionAdapter->setDiscount($discount);

            $discounts[] = $promotionAdapter->convertPromotion();
        }

        return $discounts;
    }

    /**
     * Attempt to get discount data from legacy promotion tables
     *
     * @return array|null
     */
    private function getDiscountDataFromLegacyTables()
    {
        $discountData = null;

        $presetDiscount = $this->getLegacyPresetDiscountFromPromotionCode();
        if ($presetDiscount !== null) {

            $discountData = $this->generateDiscountDataFromDiscounts(array($presetDiscount));
        }

        return $discountData;
    }

    /**
     * Get preset discount data from the database
     *
     * @return null|array
     */
    protected function getLegacyPresetDiscountFromPromotionCode()
    {
        $presetDiscount = null;

        $adaptor = Db_Manager::getAdaptor('AccountChange');
        $presetDiscountId = $adaptor->getInitialPresetDiscountId($this->promotionCode, $this->serviceDefinitionId);

        if (!empty($presetDiscountId)) {
            $presetDiscount = $adaptor->getPresetDiscountTemplate($presetDiscountId);
        }

        return $presetDiscount;
    }

    /**
     * We have a discount in the legacy format, lets convert this into the format we need for the email
     *
     * @param array $discounts
     *
     * @return array
     */
    private function generateDiscountDataFromDiscounts($discounts)
    {
        $discountData = array();
        $discountData['discountAmount']   = $this->getTotalDiscountAmount($discounts);
        $discountData['discountDuration'] = $this->getDiscountDuration($discounts);

        return $discountData;
    }

    /**
     * Iterate through all discounts and add together their values.
     *
     * @param $discounts array The discounts to iterate through
     *
     * @return string The total of all discount values
     */
    private function getTotalDiscountAmount($discounts)
    {
        $totalDiscountAmount = 0;
        foreach ($discounts as $discount) {

            // discountSubType will be empty for legacy promotions but not for C2m promotions
            if (empty($discount['discountSubType']) || $discount['discountSubType'] === DiscountType::ON_GOING ) {
                $discountAmount = AccountChange_DiscountHelper::calculateDiscountAmount(
                    static::createCurrencyObject($this->broadbandPrice),
                    $discount['promoDiscountType'],
                    $discount['decValue']
                );

                $totalDiscountAmount += $discountAmount->toDecimal();
            }
        }
        return $totalDiscountAmount;
    }

    /**
     * Iterate through all discounts to get the duration. If any durations do not match, throw an exception.
     *
     * @param $discounts array The discounts to iterate through
     *
     * @return int The duration of the discounts
     */
    private function getDiscountDuration($discounts)
    {
        $discountDuration = 0;

        foreach ($discounts as $discount) {

            // discountSubType will be empty for legacy promotions but not for C2m promotions
            if (empty($discount['discountSubType']) || $discount['discountSubType'] === DiscountType::ON_GOING ) {

                $currentDiscountDuration = $discount['intDiscountLength'];

                if (empty($discountDuration)) {
                    $discountDuration = $currentDiscountDuration;

                } elseif ($discountDuration !== $currentDiscountDuration) {

                    throw new AccountChange_EmailHandler_Exception(
                        "Promotion has ongoing discounts of different durations. Cannot arbitrarily choose a duration to use in email.");
                }
            }
        }

        return $discountDuration;
    }

    /**
     * Helper function to create a currency object
     *
     * @param string $amount
     *
     * @return I18n_Currency
     */
    private static function createCurrencyObject($amount)
    {
        return new I18n_Currency(
            AccountChange_Manager::CURRENCY_UNIT,
            $amount
        );
    }
}
