<?php

use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\CampaignNotificationClient\Exception\PersonalisedPromotionNotFoundException;

class AccountChange_PromotionCodeAccountCompatibilityPolicy extends \AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE = "Ah, that's not supposed to happen. Give us a call on 0800 432 0200 to make the most of your personalised offer.";
    const SALES_CHANNEL = 'SalesChannel';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_PROMO_RISK_PROFILE_CODE';

    private $campaignNotificationClient;
    private $c2mClient;
    protected $actor;

    /**
     * AccountChange_PromotionCodeAccountCompatibilityPolicy constructor.
     * @param Auth_BusinessActor $actor                 the business actor
     * @param bool               $isWorkplace           whether the request is from workplace
     * @param bool               $isScript              whether the call is from a script
     * @param array              $additionalInformation any additional information
     */
    public function __construct(
        Auth_BusinessActor $actor,
        $isWorkplace = false,
        $isScript = false,
        $additionalInformation = array()
    ) {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);
        $this->actor = $actor;
        $this->campaignNotificationClient = \BusTier_BusTier::getClient('campaignNotificationClient');
        $this->c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
    }

    /**
     * For non-personalised promotions then compare the applied promotion characteristics for risk profile,
     * propensity to upgrade and current product to the config for that promotion and only allow the
     * promo to be used if these match the user profile.
     *
     * @return bool
     */
    public function validate()
    {
        $c2mPromotion = $this->getC2mPromotionFromAdditionalData();

        // No promotion, allow the account change to continue.
        if (!$c2mPromotion) {
            return true;
        }

        $isPersonalised = false;

        if ($c2mPromotion instanceof Promotion) {
            $isPersonalised = $c2mPromotion->getIsPersonalisedOffer();
        }

        if (!$isPersonalised) {
            $externalUserId = $this->actor->getExternalUserId();
            $serviceId = isset($externalUserId) ? $externalUserId : '';
            $additionalInfo = $this->getAdditionalInformation();

            $channel = isset($additionalInfo[self::SALES_CHANNEL]) ? $additionalInfo[self::SALES_CHANNEL] : '';
            $promotionsFromCharacteristics = $this->getPromotionsForSalesChannelAndCharacteristics(
                $channel,
                $this->buildPromotionCharacteristics($serviceId)
            );

            return $c2mPromotion instanceof Promotion &&
                $this->promotionIsInListOfAllowedPromotions($c2mPromotion, $promotionsFromCharacteristics);

        } else {
            // Promotion isn't one we need to validate, so allow it through.. personalised promotions are
            // validated separately by AccountChange_PromotionCodePersonalisedPolicy
            return true;
        }
    }

    /**
     * @param string $serviceId customer service ID
     * @return array
     */
    private function buildPromotionCharacteristics($serviceId)
    {
        // SALES-5219
        // If we can't get current product or insight data, then we should continue
        // with the checks as the promo can be linked to only 1 characteristic.
        try {
            $currentProduct = $this->getProductFromRbm($serviceId);
        } catch (Exception $e) {
            $currentProduct = null;
        }

        $riskProfile = null;
        $upgradePropensity = null;

        try {
            $insightData = $this->campaignNotificationClient->getCustomerInsightData($serviceId);
            if (!empty($insightData)) {
                $riskProfile = $insightData->getRiskProfile();
                $upgradePropensity = $insightData->getPropensityToUpgrade();
            }
        } catch (Exception $e) {
            error_log(
                __FILE__." ".__LINE__.
                " Failed to get customer insight data with message: ".$e->getMessage().".  Continuing with defaults."
            );
        }

        return array(
            'riskProfile' => $riskProfile,
            'upgradePropensity' => $upgradePropensity,
            'currentProduct' => $currentProduct,
            'lineRentalPayment' => $this->hasLineRentalSaver($serviceId) ? 'ONE_OFF' : 'MONTHLY'
        );
    }

    /**
     * Returns true if the given promotion object is contained in the array of promotion objects passed in
     *
     * @param Promotion $promotion                 A c2m promotion object
     * @param array     $listOfAllowablePromotions An array containing Promotion objects
     *
     * @return bool
     **/
    protected function promotionIsInListOfAllowedPromotions($promotion, $listOfAllowablePromotions)
    {
        if (is_array($listOfAllowablePromotions) && count($listOfAllowablePromotions) > 0) {
            foreach ($listOfAllowablePromotions as $allowablePromotion) {
                if ($allowablePromotion->getCode() === $promotion->getCode()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Wrapper to the call to getPromotionsForSalesChannelAndCharacteristics on C2mApiClient
     *
     * @param string $channel         Sales channel
     * @param array  $characteristics Array of characteristics to restrict to
     *
     * @return array
     **/
    protected function getPromotionsForSalesChannelAndCharacteristics($channel, $characteristics)
    {
        return $this->c2mClient->getPromotionsForSalesChannelAndCharacteristics($channel, null, $characteristics);
    }

    /**
     * Gets the current product from the subscriptions in RBM
     * @param string $serviceId service ID
     * @return boolean
     */
    protected function getProductFromRbm($serviceId)
    {
        $subscriptionsHelper = new AccountChange_BillingApi_SubscriptionsHelper();
        $scid = $subscriptionsHelper->getServiceComponentIdFromBroadbandSubscription($serviceId);

        if ($scid) {
            $productOfferings = $this->c2mClient->getProductOfferings($this->getCorrelationId(), null, null, $scid);
            if (!empty($productOfferings[0])) {
                return $productOfferings[0]->getName();
            }
        }
        return false;
    }

    /**
     * Generates a correlation (unique) id to pass into the call to c2m api when getting promotions
     *
     * @return string
     **/
    protected function getCorrelationId()
    {
        $mt = explode(' ', microtime());
        $mtime = ((int)$mt[1]) * 10000 + ((int)round($mt[0] * 10000));
        return $_SERVER['UNIQUE_ID'] . $mtime; // web server
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }

    /**
     * @param string $serviceId the customer service ID
     * @return string
     */
    protected function hasLineRentalSaver($serviceId)
    {
        return (new AccountChange_BillingApi_SubscriptionsHelper())->hasActiveOrPendingLineRentalSaver($serviceId);
    }
}
