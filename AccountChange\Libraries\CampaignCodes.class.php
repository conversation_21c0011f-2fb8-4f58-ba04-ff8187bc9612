<?php
/**
 * File CampaignCodes.class.php
 *
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Class AccountChange_CampaignCodes
 *
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_CampaignCodes
{
    const ADSL_TO_FIBRE = 'fibreUpgrade';
    const FIBRE_TO_FIBRE = 'fibreExtraUpgrade';

    /* DIGITAL-1126 we need a code to modify the journey like
     * the fibreUpgrade one, but we don't want to reuse the fibreUpgrade one. */
    const MOD_LIKE_ADSL_TO_FIBRE = 'modifyJourneyLikeFibreUpgrade';

    /**
     * @return array
     */
    public static function getAllFibreUpgradeCampaignCodes()
    {
        return array(self::ADSL_TO_FIBRE, self::FIBRE_TO_FIBRE, self::MOD_LIKE_ADSL_TO_FIBRE);
    }

    /**
     * @return bool
     */
    public static function isC2fToggleSet()
    {
        $_SESSION['usingC2F'] = true;
        return true;
//        return \Plusnet\Feature\Toggle::isOn('copperToFibre');
    }
}
