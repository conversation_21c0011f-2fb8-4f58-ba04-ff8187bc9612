<?php

/**
 * <AUTHOR>
 */

class AccountChange_ExistingScheduledProductChangePolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE_PLUSNET =
        'You can\'t change your products for the moment, you already have a product change in progress.';

    const ERROR_MESSAGE_SCRIPT = 'ERROR_SCHEDULED_CHANGE_IN_PLACE';

    const ERROR_MESSAGE_JOHN_LEWIS =
        'ERROR_SCHEDULED_CHANGE_IN_PLACE||You can\'t change your products for the moment, there is already a scheduled product change, please review <a href="https://portal.johnlewisbroadband.com/account/scheduledchange/">here</a>';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_ACCOUNT_CHANGE_IN_PROGRESS';

    /** @var Core_Service */
    private $service;

    /**
     * @return bool
     */
    public function validate()
    {
        $serviceId = $this->actor->getExternalUserId();
        $this->service = $this->getCoreService($serviceId);
        $changes = $this->getScheduledChange($serviceId);

        $scheduledChanges = $changes->getScheduledChanges();

        if (in_array($this->service->getIsp(), array('greenbee', 'waitrose', 'johnlewis', 'plus.net', 'partner'))
            && !empty($scheduledChanges)
        ) {
            return false;
        }

        return true;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        if ($this->isScript) {
            return self::ERROR_MESSAGE_SCRIPT;
        }

        if ($this->service->isJohnLewisUser() && !$this->isWorkplace) {
            return self::ERROR_MESSAGE_JOHN_LEWIS;
        }
        return self::ERROR_MESSAGE_PLUSNET;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }

    /**
     * Wrapper to get Core Service
     *
     * @param int $serviceId Service Id
     *
     * @return Core_Service
     * @throws Core_Exception
     */
    protected function getCoreService($serviceId)
    {
        return new Core_Service($serviceId);
    }

    /**
     * @param int $serviceId service id
     *
     * @return AccountChange_ScheduledChange
     */
    protected function getScheduledChange($serviceId)
    {
        return new AccountChange_ScheduledChange($serviceId);
    }
}
