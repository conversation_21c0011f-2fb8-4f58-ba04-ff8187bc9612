<?php
/**
 * AccountChange Mode Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 Plusnet
 * @since     File available since 2010-05-26
 */
/**
 * AccountChange Mode Test
 *
 * Test class for AccountChange_Mode
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 Plusnet
 */
class AccountChange_Mode_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for Auth_BusinessActor
     *
     * @var Auth_BusinessActor
     */
    protected $_actor;

    /**
     * PHPUnit setup functionality
     *
     * @return void
     */
    public function setup()
    {
        $this->_actor = new Auth_BusinessActor(null);
        $this->_actor->setExternalUserId(1);
    }

    /**
     * Tear down functionality
     *
     * (non-PHPdoc)
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::restoreAdaptor('Auth');
        AccountChange_Account::setInstance(null);
    }

    /**
     * Testing to make sure the constructor sets up other variables
     * needed in the object
     *
     * @covers AccountChange_Mode::__construct
     *
     * @return void
     */
    public function testConstructorCorrectlySetsUpAttributes()
    {
        $mode = new AccountChange_Mode($this->_actor);

        $this->assertAttributeEquals($this->_actor, '_actor', $mode);
        $this->assertAttributeEquals(1, '_serviceId', $mode);
    }

    /**
     * Test that the instance methods returns the object originally set
     *
     * @covers AccountChange_Mode::instance
     * @covers AccountChange_Mode::setInstance
     *
     * @return void
     */
    public function testInstanceReturnsObjectAlreadySetIfThereIsOne()
    {
        $fakeActor = new Auth_BusinessActor(null);
        $fakeActor->setExternalUserId(990909);

        $mode = new AccountChange_Mode($this->_actor);

        AccountChange_Mode::setInstance($mode);

        $mode = AccountChange_Mode::instance($fakeActor);

        $this->assertAttributeEquals($this->_actor, '_actor', $mode);
    }

    /**
     * Test that getChange mode can correctly report to the app which mode
     * it should run in
     *
     * @param boolean $adslOk       Is adsl active
     * @param boolean $wlrOk        Is wlr active
     * @param boolean $johnLewis    Is customer on adsl product for John Lewis
     * @param string  $expectedMode What mode should account change run in
     *
     * @covers AccountChange_Mode::getChangeMode
     * @dataProvider provideDataForGetChangeMode
     *
     * @return void
     */
    public function testGetChangeModeCanCorrectlyReportTheModeToRunIn($adslOk, $wlrOk, $johnLewisNonAdsl, $expectedMode)
    {
        $mode = $this->getMock(
            'AccountChange_Mode',
            array('isAdslActive', 'isWlrChangeAllowed', 'isOnNonAdslJohnLewisProduct'),
            array($this->_actor)
        );

        $mode->expects($this->once())
             ->method('isAdslActive')
             ->will($this->returnValue($adslOk));

        $mode->expects($this->once())
             ->method('isWlrChangeAllowed')
             ->will($this->returnValue($wlrOk));

        $mode->expects($this->once())
             ->method('isOnNonAdslJohnLewisProduct')
             ->will($this->returnValue($johnLewisNonAdsl));

        $runningIn = $mode->getChangeMode();

        $this->assertEquals($runningIn, $expectedMode);
    }

    /**
     * Data provder for testGetChangeModeCanCorrectlyReportTheModeToRunIn
     *
     * @return array
     */
    public static function provideDataForGetChangeMode()
    {
        // adsl OK, wlr OK, expected Mode
        return array(
            array(true, true, false, AccountChange_Mode::BOTH),
            array(true, false, false, AccountChange_Mode::ADSL_ONLY),
            array(false, true, false, AccountChange_Mode::WLR_ONLY),
            array(false, false, false, AccountChange_Mode::NONE),
            array(true, true, true, AccountChange_Mode::WLR_ONLY),
        );
    }

    /**
     * Test that isAdslActive can correctly work out wether or not
     * it is really active
     *
     * Needs to check install diary and if not then the product component
     * instance for subscription. If it cannot find that, then it has to
     * return true
     *
     * @param string  $diaryStatus The status of the Install Diary
     * @param boolean $componentOk Is there a subscription component and is the state active
     * @param boolean $expected    What are we thinking should be the result
     *
     * @covers AccountChange_Mode::isAdslActive
     * @dataProvider provideDataForIsAdslActive
     *
     * @return void
     */
    public function testIsAdslActiveReturnsCorrectValueFromInstallDiary($diaryStatus, $componentOk, $expected)
    {
        $mode = $this->getMock(
            'AccountChange_Mode',
            array('getInstallDiaryStatus', 'isAdslSubscriptionComponentActive'),
            array($this->_actor)
        );

        $mode->expects($this->once())
             ->method('getInstallDiaryStatus')
             ->will($this->returnValue($diaryStatus));

        $mode->expects($this->any())
             ->method('isAdslSubscriptionComponentActive')
             ->will($this->returnValue($componentOk));

        $result = $mode->isAdslActive();

        $this->assertEquals($expected, $result);
    }

    /**
     * Data provider for testIsAdslActiveReturnsCorrectValueFromInstallDiary
     *
     * @return array
     */
    public static function provideDataForIsAdslActive()
    {
        // diary status, component OK, expected result
        return array(
            array('active', null, true),
            array('foobar', false, false),
            array('foobar', true, true),
        );
    }

    /**
     * Test that isWlrChangeAllowed gets an AccountChange_Account instance
     * and then gets the wlr information.
     *
     * @param boolean $change   Is change allowed
     * @param boolean $add      Is add allowed
     * @param boolean $expected Is wlr allowed to change/add
     *
     * @covers AccountChange_Mode::isWlrChangeAllowed
     * @dataProvider provideDataForIsWlrChangeAllowed
     *
     * @return void
     */
    public function testIsWlrChangeAllowedReturnsCorrectInformationFromAccountChangeAccount($change, $add, $expected)
    {
        $wlrInfo = array(
            'bolWlrChangeAllowed' => $change,
            'bolWlrAddAllowed'    => $add
        );

        $account = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation'),
            array(new Int($this->_actor->getExternalUserId()))
        );

        $account->expects($this->once())
                ->method('getWlrInformation')
                ->will($this->returnValue($wlrInfo));

        AccountChange_Account::setInstance($account);

        $mode = new AccountChange_Mode($this->_actor);
        $result = $mode->isWlrChangeAllowed();

        $this->assertEquals($expected, $result);
    }

    /**
     * Data provider for testIsWlrChangeAllowedReturnsCorrectInformationFromAccountChangeAccount
     *
     * @return array
     */
    public static function provideDataForIsWlrChangeAllowed()
    {
        // change allowed, add allowed, expected result
        return array(
            array(true, true, true),
            array(true, false, true),
            array(false, true, true),
            array(false, false, false)
        );
    }

    /**
     *
     * @param array   $product  Product from the database
     * @param boolean $expected Expected result (Boolean)
     *
     * @covers AccountChange_Mode::isOnNonAdslJohnLewisProduct
     * @dataProvider provideDataForIsOnNonAdslJohnLewisProduct
     *
     * @return void
     */
    public function testIsJohnLewisAccountOnNonAdslProductCanWorkItOutBasedOnNameVisp($product, $expected)
    {
        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
                ->method('getServiceDefinitionDetailsForService')
                ->will($this->returnValue($product));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $mode = new AccountChange_Mode($this->_actor);

        $actual = $mode->isOnNonAdslJohnLewisProduct();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Data provider for testIsJohnLewisAccountOnNonAdslProductCanWorkItOutBasedOnNameVisp
     *
     * @return array
     */
    public function provideDataForIsOnNonAdslJohnLewisProduct()
    {
        return array(
            // Data Set 0
            // John Lewis Basic Product therefore true
            array(
                array(
                    'name' => 'Basic',
                    'isp'  => 'johnlewis'
                ),
                true
            ),
            // Data Set 1
            // John Lewis non Basic product therefore false
            array(
                array(
                    'name' => 'Standard',
                    'isp'  => 'johnlewis'
                ),
                false
            ),
            // Data Set 2
            // Plusnet Basic product therefore false
            array(
                array(
                    'name' => 'Basic',
                    'isp'  => 'plus.net'
                ),
                false
            ),
        );
    }
}
