<?php
/**
 * Wlr Service Component Contract
 *
 * Completely utilises its parent's functionality, apart from 2 functions (see below)
 *
 * @package   AccountChange
 * <AUTHOR> <kso<PERSON><PERSON>@plus.net>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_WlrServiceComponentContract.class.php,v 1.2 2009-05-11 04:19:00 fzaki Exp $
 * @link      http://confluence.internal.plus.net/display/LRS
 * @since     File available since 2008-08-19
 */
/**
 * Wlr Service Component Contract class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://confluence.internal.plus.net/display/LRS
 */
class AccountChange_Product_WlrServiceComponentContract extends AccountChange_Product_ServiceComponentContract
{
    /**
     * Overriding the constructor
     * This is actually a fix for P 59429. We should consider the contract of W<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> instead of SUBSCRIPTION,
     * as SUBSCRIPTION can be annual/monthly, but  WlrLineRent is always monthly
     *
     * @param string $strContractLengthHandle   contract length handle
     * @param string $strPaymentFrequencyHandle payment frequency handle
     * @param string $strProductComponentHandle product component handle
     * @param string $uxtContractEndDate        contract end date
     * @param string $uxtContractStartDate      contract start date
     * @param string $strTariffTypeHandle       tariff type handle
     */
    public function __construct(
        $strContractLengthHandle,
        $strPaymentFrequencyHandle,
        $strProductComponentHandle = 'WlrLineRent',
        $uxtContractEndDate = null,
        $uxtContractStartDate = null,
        $strTariffTypeHandle = 'DEFAULT'
    ) {
        parent::__construct(
            $strContractLengthHandle,
            $strPaymentFrequencyHandle,
            $strProductComponentHandle,
            $uxtContractEndDate,
            $uxtContractStartDate,
            $strTariffTypeHandle
        );
    }

    /**
     * Get the cost of the Wlr product
     *
     * @param int $intServiceComponentId service component id
     *
     * @return int
     */
    public function getProductCost($intServiceComponentId)
    {
        $objDatabase = Db_Manager::getAdaptor('AccountChange');

        $intProductComponentPrice = $objDatabase->getWlrProductComponentPrice(
            $this->strContractLengthHandle,
            $this->strPaymentFrequencyHandle,
            $intServiceComponentId
        );
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        return sprintf("%.2f", $intProductComponentPrice / 100);
    }
}
