<?php
/**
 * Broadband product filter for house move process
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
/**
 * Broadband product filter for house move process (through workplace)
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Product_BroadbandProductFilter_Housemove extends
AccountChange_Product_BroadbandProductFilter_WorkplaceHousemove
{

    /**
     * Categorise the current product and variants
     *
     * @param array[] &$filteredProducts      Filtered products so far, updated in place
     * @param array   $currentProduct         Current product
     * @param array[] $currentProductVariants Variants of the current product
     *
     * @return void
     */
    protected function categoriseCurrentProduct(&$filteredProducts, $currentProduct, $currentProductVariants)
    {
        /** @var ProductFamily_ProductFamily $currentProductFamily */
        $currentProductFamily = $currentProduct['productFamily'];
        $isCurrentProductDualPlay = $currentProductFamily->isDualPlay();

        if (array_key_exists('solus', $currentProductVariants) ||
            array_key_exists('dualplay', $currentProductVariants)
        ) {
            if (array_key_exists('solus', $currentProductVariants) &&
                $this->shouldAllowSolusProducts($currentProduct)
            ) {
                $filteredProducts['Current Solus Product'][] = $currentProductVariants['solus'];
            }
            if (array_key_exists('dualplay', $currentProductVariants) &&
                $this->shouldAllowDualPlayProducts($currentProduct)
            ) {
                $filteredProducts['Current DualPlay Product'][] = $currentProductVariants['dualplay'];
            }
        } else {
            $filteredProducts['Current Product'][] = $currentProductVariants['legacy'];
        }
    }
}
