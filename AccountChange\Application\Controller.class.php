<?php
/**
 * Account Change Application Controller (Wizard)
 *
 * The Wizard Controller for the Account Change App
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://www.plus.net/
 * @since     File available since 2008-09-29
 */
require_once(__DIR__ . '/../Libraries/C2mPromotionsHelper.php');
require_once(__DIR__ . '/../Libraries/C2mPriceHelper.php');
require_once(__DIR__ . '/../Libraries/C2mSalesChannels.class.php');
require_once(__DIR__ . '/../Libraries/C2MCopperToFibreJourney.php');
require_once(__DIR__ . '/../Libraries/BizProductsHelper.class.php');


use AccountChange_BillingApi_BasePriceHelper as BasePriceHelper;
use AccountChange_BillingApi_ProductOfferingPricePointIdHelper as ProductOfferingPricePointIdHelper;
use Plusnet\BillingApiClient\Entity\AvailableProduct;
use Plusnet\BillingApiClient\Entity\DirectDebit;
use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;
use Plusnet\BillingApiClient\Entity\Request\ProductsQueryRequest;
use Plusnet\C2mApiClient\C2MClient;
use Plusnet\C2mApiClient\Entity\Discount;
use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\ContractsClient\Client as ContractsClient;
use Plusnet\Feature\FeatureToggleManager;
use Plusnet\HouseMoves\Models\HouseMoveAddress;
use Plusnet\HouseMoves\Services\ServiceManager;
use Plusnet\PriceProtected\Services\Factory\StatusServiceFactory;
use Plusnet\C2mApiClient\Exception\PromotionNotFoundException;

/**
 * AccountChange_Controller Wizard Controller
 *
 * @package   AccountChange
 * <AUTHOR> Marek <<EMAIL>>
 * <AUTHOR> Selby <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://www.plus.net/
 */
class AccountChange_Controller extends Mvc_WizardController
{
    /**
     * Keys for base price and base price in contract values returned from Billing API getCustomerAvailableProducts()
     *
     * @var string
     */
    const CURRENT_BASE_PRICE_KEY = 'currentBasePrice';
    const CURRENT_BASE_PRICE_IN_CONTRACT_KEY = 'currentBasePriceInContract';

    /**
     * Key for C2MPromotion
     *
     * @var string
     **/
    const C2M_PROMOTION_KEY = 'C2MPromotion';


    /**
     * Array key for getting in contract wlr price from array returned from Account.class.php->getWlrInformation()
     *
     * @var string
     */
    const PRODUCT_COST_IN_CONTRACT_KEY = 'intProductCostInContract';

    /**
     * Array key for getting selected product cost in workplace account change journey
     *
     * @var string
     */
    const WORKPLACE_NEW_COST_KEY = 'intNewCost';

    /**
     * Array key for getting base price of product in workplace account change journey
     *
     * @var string
     */
    const WORKPLACE_MONTHLY_PRICE_KEY = 'intMonthlyPrice';

    /**
     * Array key for getting base price in contract of product in workplace account change journey
     *
     * @var string
     */
    const WORKPLACE_IN_CONTRACT_MONTHLY_PRICE_IN_CONTRACT_KEY = 'intInContractMonthlyPrice';

    /**
     * Array key for getting broadband product cost when performing account change through API
     *
     * @var string
     */
    const PRODUCT_COST_KEY = 'intProductCost';

    /**
     * Array key for getting a broadband product's discount amount
     *
     * @var string
     */
    const DISCOUNT_AMOUNT_KEY = 'discountAmount';

    /**
     * Array key for getting decimal value of preset discount
     *
     * @var string
     */
    const DECIMAL_VALUE_KEY = 'decValue';

    /**
     * Array key for getting service id from service definition details array
     *
     * @var string
     */
    const SERVICE_ID_KEY = 'service_id';

    /**
     * Request is from member centre
     *
     * @var string
     */
    const SOURCE_MEMBER_CENTRE_KEY = 'MEMBER_CENTRE';

    /**
     * Request is from workplace
     *
     * @var string
     */
    const SOURCE_WORKPLACE_KEY = 'WORKPLACE';

    /**
     * Location of the pricing dates json config file
     *
     * @var string
     */
    const MILESTONE_DATE_JSON_LOCATION = '/local/codebase2005/modules/Framework/milestoneDates.json';

    /**
     * The maximum download speed currently supported by Plusnet for each product types in Kb.
     *
     * @var integer
     */
    const MAX_DOWN_SPEED = 8000;
    const MAX_FIBRE_DOWN_SPEED = 80000;
    const MAX_FIBRE_CAPPED_DOWN_SPEED = 40000;
    const MAX_ADSL2_DOWN_SPEED = 20000;
    const MAX_ADSL_DOWN_SPEED = 8000;

    /**
     * The maximum upload speed currently supported by Plusnet for each product types in Kb.
     *
     * @var integer
     */
    const MAX_UP_SPEED = 448;
    const MAX_FIBRE_UP_SPEED = 20000;
    const MAX_FIBRE_CAPPED_UP_SPEED = 2000;
    const MAX_ADSL2_UP_SPEED = 2500;
    const MAX_ADSL_UP_SPEED = 448;

    /**
     * Alphanumeric regular expression used for field validation
     *
     * @var string
     */
    const ALPHANUMERIC_REGEX = '/^[a-zA-Z0-9]+$/';

    /**
     * Access Technology - Fibre
     *
     * @var string
     */
    const ACCESS_TECH_FIBRE = 'FTTC';

    /**
     * Access Technology - ADSL
     *
     * @var string
     */
    const ACCESS_TECH_ADSL = 'ADSL';

    /**
     * Billing API Toggle RBM_MIGRATION_COMPLETE
     *
     * @var string
     */
    const RBM_MIGRATION_COMPLETE = 'RBM_MIGRATION_COMPLETE';

    /**
     * Displays intMGALSInMB this is used multiple times in an array
     *
     * @var string
     */
    const MGALS_IN_MB_KEY = 'intMGALSInMB';

    /**
     * Displays intMGSInMB this is used multiple times in an array
     *
     * @var string
     */
    const INT_MGS_IN_MB = 'intMGSInMB';

    /**
     * Displays intImpactedMGALS this is used multiple times in an array
     *
     * @var string
     */
    const INT_IMPACTED_MGALS = 'intImpactedMGALS';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_PROMO_PERSONALISED_CODE';

    /**
     * DSL Provisioning proffile
     */
    const DSL_PROFILE = '512k+ 50:1 SI';

    /**
     * ADSL Provisioning proffile
     */
    const ADSL_PROFILE = '2Mb+ 50:1 SI';

    /**
     * Secure?
     *
     * @var boolean
     */
    protected $bolSecure = true;

    /**
     * Auto login allowed?
     *
     * @var boolean
     */
    protected $bolAutoLoginAllowed = true;

    /**
     * Seconds to maintain app state.
     *
     * @var integer
     */
    protected $intSecsToMaintainAppState = 7200;

    /**
     * Seconds duration expected.
     *
     * @var integer
     */
    protected $intExpectedSecsDuration = 50;

    /**
     * Target actor types.
     *
     * @var array
     */
    protected $arrTargetActorTypes = array('PLUSNET_ENDUSER');

    /**
     * Inputs into the application.
     *
     * @var array
     */
    protected $arrInputs = array(
        'bolSchedule'            => 'external:Bool',
        'bolWlrOnly'             => 'external:Bool',
        'bolOverrideChecks'      => 'external:Bool',
        'bolChangeBroadbandOnly' => 'external:Bool',
        'bolHousemove'           => 'external:Bool:optional',
        'promoCode'              => 'external:Val_PromoCodeOrC2mPromoCode->promoCode:optional',
        'campaignCode'           => 'external:custom:optional',
        'promotion'              => 'external:custom:optional',
        'campaign'               => 'external:string:optional',
        'impressionOfferId'      => 'external:string:optional'
    );

    /**
     * The minimum requirements that the wizard needs to fulfil.
     *
     * @var array
     */
    protected $arrMinimumReqs = array(
        'TermsAndConditions'
    );

    /**
     * The views and requirements the wizard needs to fulfil.
     *
     * @var array
     */
    protected $arrViews = array(

        'CurrentAccount' => array(
            'ShowCurrentAccount',
            'ShowLinechecker',
        ),

        'Broadband' => array(
            'SelectBroadband',
        ),

        'BroadbandWorkplace' => array(
            'SelectBroadbandWorkplace'
        ),

        'Hardware' => array(
            'Hardware'
        ),

        'Homephone' => array(
            'SelectHomephone'
        ),

        'HomephoneWorkplace' => array(
            'SelectHomephoneWorkplace'
        ),

        'AddressDetails' => array(
            'Address'
        ),

        'Engineer' => array(
            'EngineerDetails'
        ),

        'Summary' => array(
            'TermsAndConditions'
        ),

        'PaymentRedirectWorkplace' => array(
            'PaymentWorkplace'
        ),

        'PaymentRedirectPortal' => array(
            'PaymentPortal'
        ),

        'PaymentDetails' => array(
            'PaymentMethodDetails',
            'DirectDebitDetails'
        ),

        'ConfirmDetails' => array(
            'ConfirmDetails'
        )
    );

    private static $FIBRE_UPGRADE_BROADBAND_VIEW = array(
        'FibreUpgradeBroadband' => array(
            'AccountChange_SelectBroadband'
        )
    );

    private static $C2F_HOME_PHONE_VIEW = array(
        'C2FHomephone' => array(
            'AccountChange_SelectHomephone'
        ),
    );

    private static $C2F_UPGRADE_BROADBAND_VIEW = array(
        'C2FBroadband' => array(
            'AccountChange_SelectBroadband'
        )
    );

    private static $C2F_SUMMARY_VIEW = array(
        'C2FSummary' => array(
            'AccountChange_TermsAndConditions'
        )
    );


    private static $C2F_HARDWARE_VIEW = array(
        'C2FHardware' => array(
            'AccountChange_Hardware'
        )
    );

    /**
     * Mappings for the redirect on completion.
     *
     * @var array
     */
    protected $arrRedirectUrls = array(
        'PaymentRedirectWorkplace'
        => 'https://workplace.plus.net/apps/{targetActor}/paymentdetails/takepayment?bolWizReset=1&paymentHandoverId={paymentHandoverId}&validationHash={validationHash}',
        'PaymentRedirectPortal'
        => 'https://portal.plus.net/apps/paymentdetails/takepayment?bolWizReset=1&paymentHandoverId={paymentHandoverId}&validationHash={validationHash}'
    );

    /**
     * Actions which do not progress to the next view.
     *
     * @var array
     */
    protected $arrRestrictedValidationActions = array(
        'switchpaymentmethod' => array('PaymentMethodDetails'),
        'linecheck'           => array('ShowLinechecker'),
        'broadbandswitcher'   => array('SelectBroadbandWorkplace')
    );

    /**
     * Bundles.
     *
     * @var array
     */
    static public $arrEssentialBundles = array(
        '6704' => array(
            'intServiceComponentId' => 596,
            'strProductName'        => 'Plusnet Essential' // Annual
        ),
        '6705' => array(
            'intServiceComponentId' => 596,
            'strProductName'        => 'Plusnet Essential' // Monthly
        ),
        '6708' => array(
            'intServiceComponentId' => 596,
            'strProductName'        => 'Force9 Essential' // Annual
        ),
        '6709' => array(
            'intServiceComponentId' => 596,
            'strProductName'        => 'Force9 Essential' // Monthly
        ),
        '6710' => array(
            'intServiceComponentId' => 596,
            'strProductName'        => 'Free-Online Essential' // Annual
        ),
        '6711' => array(
            'intServiceComponentId' => 596,
            'strProductName'        => 'Free-Online Essential' // Monthly
        )
    );

    /**
     * Visp related bundles.
     *
     * @var array
     */
    static public $arrNonEssentialBundles = array(
        '6687' => array(
            'intServiceComponentId' => 583,
            'strProductName'        => 'Greenbee Phone and Broadband'
        ),
        '6688' => array(
            'intServiceComponentId' => 583,
            'strProductName'        => 'Greenbee Phone and Broadband Plus'
        )
    );

    private static $additionalChargeHelper = null;
    private static $crd = null;

    /**
     * @param array $arrData Wizard data
     *
     * @return bool
     */
    private static function isHardwareFree(array $arrData)
    {
        return $arrData['waivePostage']
            || (!empty($arrData['campaignCode']) && self::isFibreUpgradeCampaign($arrData['campaignCode']));
    }

    /**
     * Validator for promotion.
     *
     * @param string $promotion Promo code
     *
     * @return array
     */
    public function valPromotion($promotion)
    {
        if (!preg_match(self::ALPHANUMERIC_REGEX, trim($promotion))) {
            $promotion = '';
        }

        return array('promotion' => $promotion);
    }

    /**
     * Validator for campaignCode.
     *
     * @param string $campaignCode Campaign code
     *
     * @return array
     */
    public function valCampaignCode($campaignCode)
    {
        if (!preg_match(self::ALPHANUMERIC_REGEX, trim($campaignCode))) {
            $campaignCode = '';
        }

        return array('campaignCode' => $campaignCode);
    }

    /**
     * Check if the given service definition is a Bundle.
     *
     * TODO: Hard coded as there are some issues as to the use of default_quantity in the service_component_config table
     *
     * @param int $intSdi The Service Definition Id
     *
     * @return boolean
     */
    public static function isBundle($intSdi)
    {
        return in_array((int)$intSdi, array_keys(self::getAllBundles()));
    }

    /**
     * Get all of the bundles that we have.
     *
     * @return array
     */
    public static function getAllBundles()
    {
        return self::$arrNonEssentialBundles + self::$arrEssentialBundles;
    }

    /**
     * Check to see if the service definition id is an essential id.
     *
     * @param integer $intSdi The Service Definition Id
     *
     * @return boolean
     */
    public static function isEssential($intSdi)
    {
        return in_array((int)$intSdi, array_keys(self::$arrEssentialBundles));
    }

    /**
     * Returns true if its wlr essential id.
     *
     * @param integer $intServiceComponentId Service component Id
     *
     * @return boolean
     */
    public static function isWlrEssential($intServiceComponentId)
    {
        foreach (self::$arrEssentialBundles as $arrBundle) {
            if ((int)$intServiceComponentId == $arrBundle['intServiceComponentId']) {
                return true;
            }
        }

        return false;
    }

    /**
     * Getter for the Wlr Id given a service definition id.
     *
     * @param integer $intSdi The Service Definition Id
     *
     * @return integer
     */
    public static function getWlrIdBySdi($intSdi)
    {
        $arrBundles = self::getAllBundles();

        return $arrBundles[(int)$intSdi]['intServiceComponentId'];
    }

    /**
     * Getter for the product name given a service definition id.
     *
     * @param integer $intSdi The Definition Id
     *
     * @return string
     */
    public static function getProductNameBySdi($intSdi)
    {
        $arrBundles = self::getAllBundles();

        return $arrBundles[(int)$intSdi]['strProductName'];
    }

    /**
     * Is the user a member of a phplib group?
     *
     * @param string $userId Phplib ID of the user to check
     * @param string $groupName Name of the group to check
     *
     * @return boolean
     */
    protected function isUserMemberOfPhplibGroup($userId, $groupName)
    {
        $customerDetailsAdaptor = Db_Manager::getAdaptor("CustomerDetails");
        $userInGroup = $customerDetailsAdaptor->wpUserInGroup($groupName, $userId);

        if ($userInGroup) {
            return true;
        }

        return false;
    }

    /**
     * Can the user perform an instant account change?
     *
     * @return boolean
     */
    public function canUserPerformInstantAccountChange()
    {
        $userActor = $this->getCurrentBusinessActor();

        if ($userActor instanceof Auth_BusinessActor && $userActor->getUserType() == 'PLUSNET_STAFF') {
            $userId = $userActor->getExternalUserId();

            return $this->isUserMemberOfPhplibGroup($userId, 'Instant Account Change');
        }

        return false;
    }

    /**
     * Initialise controller. Sets up additional requirements based on current logged in user.
     *
     * @param boolean               $bolSchedule       Are we scheduling the change
     * @param boolean               $bolWlrOnly        Are we only wanting to deal with wlr
     * @param boolean               $bolChangeBroadbandOnly  Are we only wanting to deal with broadband
     * @param boolean               $bolOverrideChecks Are we wanting to override the validation checks
     * @param Val_ReferralPromoCode $promoCode         Promo code provided
     * @param boolean               $bolHousemove      Are we doing Housemove productchange
     * @param string                $campaignCode      Campaign code indicating which landing page this journey
     *                                                 originated from, if any
     * @param int                   $impressionOfferId PEGA impression offer id
     *
     * @return array
     */
    public function init(
        $bolSchedule,
        $bolWlrOnly,
        $bolOverrideChecks,
        $bolChangeBroadbandOnly,
        $promoCode = null,
        $bolHousemove = false,
        $campaignCode = null,
        $promotion = null,
        $campaign = null,
        $impressionOfferId = null
    )
    {
        $arrData = array();
        $arrAdditionalReqs = array();

        //added to allow the new decorationPortal.tpl to work  [DLIFE-105]
        $_SESSION['usingC2F'] = false;
        $_SESSION['removeSolus'] = FeatureToggleManager::isOn(FeatureToggleManager::REMOVE_PLUSNET_RES_SOLUS, null, true);
        $arrData['campaign'] = $campaign;
        $arrData['impressionOfferId'] = $impressionOfferId;

        /*
         * DIGITAL-1126
         * We want to propagate fibre upgrade journey modification initially implemented
         * by SALES-2073 across all Residential journeys. We're using the same
         * mechanism but I've created a new campaign code just incase we want to retain
         * the fibreUpgrade one specifically for another purpose. These can be found in
         * the AccountChange_CampaignCodes class library.
         *
         */
        $objUserActor = $this->getUserBusinessActor();
        if ($objUserActor && $objUserActor->getUserType() == 'PLUSNET_ENDUSER') {
            $intServiceId = $objUserActor->getExternalUserId();
            $objCoreService = $this->getCoreService($intServiceId);
            $objServiceDefinition = $this->getCoreServiceDefinition($objCoreService->getType());

            // Logging for statistics tracking on splunk
            error_log("$intServiceId: AccountChange journey started ("
                . "campaign=" . $arrData['campaign']
                . ", impressionOfferId=$impressionOfferId"
                . ")");
        }

        $objBusinessActor = $this->getCurrentBusinessActor();
        $isResPortalUser = $this->isResidentialPortalUserWithResidentialServiceDefinition($objServiceDefinition, $objCoreService, $objBusinessActor);

        if ($this->isEligibleForPEGAInteraction($impressionOfferId, $isResPortalUser, $promotion, $campaign)) {
            $isCustomer = $this->isCustomerUser($objBusinessActor);
            $this->registerPegaInteraction($impressionOfferId, AccountChange_PegaInteractionHelper::CLICKED, $isCustomer);
        }

        if ($campaignCode == AccountChange_CampaignCodes::ADSL_TO_FIBRE && !$this->isC2fToggleSet()) {
            /* Only modify journey if its res journey or if special campaignCode is supplied.
           This way campaignCode use is sales channel agnostic */
            $this->modifyJourneyForFibreUpgradeCampaign();
            $arrData['campaignCode'] = $campaignCode;
        }
        elseif ($isResPortalUser) {
            if ($this->isC2fToggleSet()) {
                $_SESSION['usingC2F'] = true;
                if($campaignCode) {
                    $arrData['campaignCode'] = $campaignCode;
                }
                $this->modifyJourneyForC2fUpgradeCampaign();
            } else {
                if (!$campaignCode) {
                    $campaignCode = AccountChange_CampaignCodes::MOD_LIKE_ADSL_TO_FIBRE;
                }
                $this->modifyJourneyForFibreUpgradeCampaign();
                $arrData['campaignCode'] = $campaignCode;
            }
        } else {
            $arrAdditionalReqs[] = 'AccountChange_ShowCurrentAccount';
            $arrAdditionalReqs[] = 'AccountChange_ShowLinechecker';
        }

        $this->setRestrictedValidationActions($this->arrRestrictedValidationActions);
        $floLeadPrice = null;
        $intMonthlyBroadbandLeadPrice = null;
        $intDiscountLength = 0;

        $additionalValidatorInformation = array();

        if ($objUserActor instanceof Auth_BusinessActor && $objUserActor->getUserType() == 'PLUSNET_ENDUSER') {
            $intServiceId = $objUserActor->getExternalUserId();

            $arrData = $this->handlePromoCode(
              $promoCode,
              $promotion,
              $arrData
            );

            if (isset($arrData[self::C2M_PROMOTION_KEY])) {
                $additionalValidatorInformation[self::C2M_PROMOTION_KEY] = $arrData[self::C2M_PROMOTION_KEY];
            }
            $check = $this->getValidationCheck($objUserActor, $bolHousemove, $additionalValidatorInformation, ($objBusinessActor) ? $objBusinessActor->getUserType() == 'PLUSNET_STAFF' : $objBusinessActor);

            $arrData['bolWlrOnly'] = $bolWlrOnly;
            $arrData['bolChangeBroadbandOnly'] = $bolChangeBroadbandOnly;
            $arrData['bolWlrOnlyRestriction'] = false;
            $arrData['bolWlrChangeRestriction'] = false;
            $arrData['bolSchedule'] = $bolSchedule;
            $arrData['bolHousemove'] = $bolHousemove;


            $mode = AccountChange_Mode::instance($objUserActor);

            if ($bolChangeBroadbandOnly) {
                $mode->setChangeMode(AccountChange_Mode::CHANGE_BROADBAND_ONLY);
            }

            $changeMode = $mode->getChangeMode();

            // Restricting the home phone change when phone is not active. When phone is active, the function
            // AccountChange_Mode::getChangeMode should returns either AccountChange_Mode::WLR_ONLY or
            // AccountChange_Mode::BOTH
            if ($bolWlrOnly && !in_array($changeMode, array(AccountChange_Mode::WLR_ONLY, AccountChange_Mode::BOTH))
            ) {
                $arrData['bolWlrChangeRestriction'] = true;
            }

            if (AccountChange_Mode::WLR_ONLY == $changeMode) {
                $arrData['bolWlrOnlyRestriction'] = true;
                $bolWlrOnly = true;
            }

            Dbg_Dbg::write(
              'AccountChange_Controller::init is running in ' . $mode->getChangeMode() . ' mode',
              'AccountChange'
            );

            if ($bolSchedule) {
                Dbg_Dbg::write('AccountChange_Controller::init is running in schedule mode', 'AccountChange');
            }
            if ($bolHousemove) {
                Dbg_Dbg::write('AccountChange_Controller::init is running in HouseMove mode', 'AccountChange');
            }

            $account = AccountChange_Account::instance(new Int($intServiceId));
            $objCoreService = $this->getCoreService($intServiceId);
            $objCoreUser    = new Core_User($objCoreService->getUserId());
            $objCoreAddress = new Core_Address($objCoreUser->getAddressId());
            $arrData['currentAddress'] = array (
                    'house'    => $objCoreAddress->getHouse(),
                    'street'   => $objCoreAddress->getStreet(),
                    'town'     => $objCoreAddress->getTown(),
                    'county'   => $objCoreAddress->getCounty(),
                    'postcode' => $objCoreAddress->getPostcode()
                );
            $objServiceDefinition = new Core_ServiceDefinition($objCoreService->getType());
            $arrData['intContractLengthMonths'] = null;

            if ($objServiceDefinition->isBusiness()) {
                $currentProductContractLength = self::getProductDefaultContractLength($objCoreService->getType());
                $arrData['intContractLengthMonths'] = (empty($currentProductContractLength)) ? 1 : $currentProductContractLength;
            }

            $bolIsOldProductValueFamilyProduct = $objServiceDefinition->isValueFamilyProduct();
            $directDebitDetails = $this->getDirectDebitDetails($intServiceId);

            $arrAdslProductDetails = $objCoreService->getAdslDetails();

            // For all value family products we can calculate lead price and lead price length using tariff deviations
            // BPR09 - For business we don't currently have lead prices.  I'll keep this check in place, so in future if
            // we decide to do an offer; we only need to populate the tariff deviation table.
            // Currently this code will return a discount length of 0, and lead price of null for all business accounts.
            if (true === $bolIsOldProductValueFamilyProduct || $objServiceDefinition->isBusiness()) {
                $intTariffId
                  = (isset($arrAdslProductDetails['intTariffID'])) ? $arrAdslProductDetails['intTariffID'] : 0;
                $objTariffDeviation = new ProductComponent_TariffDeviation($intTariffId);
                $uxtContractStart = $account->getExistingContractStartDate('INTERNET_CONNECTION');
                $floLeadPrice = $objTariffDeviation->getPrice($uxtContractStart, time());

                $intMonthlyBroadbandLeadPrice = null;
                if(!is_null($floLeadPrice)){
                    $leadPrice = $floLeadPrice / 100;
                    $intMonthlyBroadbandLeadPrice = self::createCurrencyObject($leadPrice);
                }

                if (!is_null($intMonthlyBroadbandLeadPrice)) {
                    $intDiscountLength = $objTariffDeviation->getRemainingMonths($uxtContractStart);
                }
            } else {
                //If not family products
                $intMonthlyBroadbandLeadPrice = null;
                //If customer changes from one of lagacy products to family product
                //we start new contract with 3 months lead price period
                $intDiscountLength = 3;
            }

            $arrWlrDetails = $account->getWlrInformation();
            $objWlrPrice = self::createCurrencyObject(0);

            if (isset($arrWlrDetails[self::PRODUCT_COST_IN_CONTRACT_KEY])
                   && $arrWlrDetails[self::PRODUCT_COST_IN_CONTRACT_KEY] instanceof I18n_Currency) {

                $objWlrPrice = $arrWlrDetails[self::PRODUCT_COST_IN_CONTRACT_KEY];
            }

            if ($bolHousemove) {
                // Checks if the agent selected remove phone during house move and sets
                // bolWlrAddAllowed to false to ignore phone components
                $arrWlrDetails['bolWlrAddAllowed'] = $this->expectingPhoneForHouseMove($intServiceId);
                // bolWlrChangeAllowed to false if remove phone has been selected during house move
                if (!$arrWlrDetails['bolWlrAddAllowed']) {
                    $arrWlrDetails['bolWlrChangeAllowed'] = false;
                }
            }

            if (!$check->isAccountChangeAllowed()) {
                $arrWlrDetails['bolWlrChangeAllowed'] = false;
                $arrData['reasonForBlockage'] = $check->getReasonForBlockage();
                $arrData['brandMigrationInProgress'] =
                    $check->getErrorCode() === 'ERROR_ACCOUNT_CHANGE_BRAND_MIGRATION_IN_PROGRESS' ?: false;
                $arrData['accountMigrationInProgress'] =
                    $check->getErrorCode() === 'ERROR_ACCOUNT_CHANGE_ACCOUNT_MIGRATION_IN_PROGRESS' ?: false;
            }

            $arrVoipDetails = $account->getVoipInformation();

            if (empty($arrData['reasonForBlockage']) && $objServiceDefinition->isAdsl()) {
                $supplierProduct = $this->getProvisionedProduct($intServiceId);
                if ($supplierProduct != false) {
                    $arrData['currentSupplierProduct'] = $supplierProduct;
                }
            }

            try {
                if ($bolHousemove) {
                    $houseMoveAddress = $this->getHouseMoveAddress($intServiceId);

                    $arrData['strNewPostCode'] = $houseMoveAddress->getPostcode();
                    $arrData['addressRef'] = $houseMoveAddress->getAddressReference();
                    $arrData['cssDatabaseCode'] = $houseMoveAddress->getCssDatabaseCode();
                    $arrData['houseName'] = $houseMoveAddress->getHouseName();
                } else {
                    $arrData['addresses'] = $this->matchAddress(
                      $objCoreAddress->getHouse(),
                      $objCoreAddress->getPostCode()
                    );

                    if ($arrData['addresses']->count() == 1) {
                        $arrData['addressRef'] = $arrData['addresses']->current()->getIdentifyingString();
                    }
                }
            } catch (Exception $e) {
                Log_AuditLog::write('Live appointing Failed: ' . $e->getMessage(), __CLASS__);
            }

            $serviceDefinitionId = $objCoreService->getType();
            $serviceComponentId  = $objCoreService->getServiceComponentId();
            $tariffId            = $arrAdslProductDetails['intTariffID'];

            $productOfferingPricePointPair = BasePriceHelper::createProductOfferingPricePointPairFromIds(
                $serviceDefinitionId,
                $serviceComponentId,
                $tariffId);

            $basePrices =
                BasePriceHelper::getBasePrices($intServiceId, array($productOfferingPricePointPair));
            $productOfferingPricePointId =
                ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

            $currentBroadbandPrice = $basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY];

            if (!$bolWlrOnly) {
                $broadbandPrice = $currentBroadbandPrice;
            } else {
                $broadbandPrice = 0;
            }

            $totalMonthlyCost = $broadbandPrice + $objWlrPrice->toDecimal();
            $objCurrentBroadbandPrice = static::createCurrencyObject($currentBroadbandPrice);
            $objTotalMonthlyCost = static::createCurrencyObject($totalMonthlyCost);
            $objBroadbandPrice = static::createCurrencyObject($broadbandPrice);

            // We need to know if customer has paperbilling or not
            $arrData['customerHasPaperBilling'] = $this->customerHasPaperBilling($objCoreService);

            //call RBM API client to check today as invoice date
            $arrData['isTodayBillDate'] = $this->isBillingToday($objCoreService);

            $arrData['intOldSdi'] = $objCoreService->getType();
            $arrData['productIsp'] = $objServiceDefinition->getIsp();
            $arrData['bolIsBusiness'] = $objServiceDefinition->isBusiness();
            $arrData['bolCurrentProductIsBundle'] = self::isBundle($objCoreService->getType());
            $arrData['strBroadbandProduct'] = $objServiceDefinition->getName();
            $arrData['strBroadbandContract'] = strtoupper($objCoreService->getBroadbandContract());
            $arrData['intBroadbandTariff'] = $account->getCurrentBroadbandTariff();
            $arrData['bolIsOldProductValueFamilyProduct'] = $bolIsOldProductValueFamilyProduct;
            $arrData['bolBroadbandGracePeriodPassed'] = $account->hasPassedAdslChangeGracePeriod();
            $arrData['uxtBroadbandChangeAllowed'] = $account->getAdslGracePeriod();
            $arrData['bolWlrChangeAllowed'] = $arrWlrDetails['bolWlrChangeAllowed'];
            $arrData['arrWlrProduct'] = $arrWlrDetails;
            $arrData['intMonthlyBroadbandPrice'] = $objBroadbandPrice;
            $arrData['intDiscountLength'] = $intDiscountLength;
            $arrData['intTotalMonthlyCost'] = $objTotalMonthlyCost;
            $arrData['objCoreService'] = $objCoreService;
            $arrData['intPhoneNumber'] = $arrData['objCoreService']->getCliNumber();
            $arrData['strPostCode'] = $objCoreAddress->getPostCode();
            $arrData['strThoroughFareNumber'] = $objCoreAddress->getHouse();
            $arrData['bolHasVoip'] = $arrVoipDetails['bolHasVoip'];
            $arrData['strVoipProduct'] = $arrVoipDetails['strVoipProduct'];
            $arrData['intVoipProductCost'] = $arrVoipDetails['intVoipProductCost'];
            $arrData['objBusinessActor'] = $objBusinessActor;
            $arrData['objUserActor'] = $objUserActor;
            $arrData['arrAdslProductDetails'] = $arrAdslProductDetails;
            $arrData['strProvisionOn'] = false;
            $arrData['arrCurrentBroadband'] = $this->getProvisionedService($intServiceId);
            $arrData['strProvisionedOn'] = $this->getProvisionedOn($intServiceId);
            $arrData['costLevel'] = $account->getCostLevel();
            $arrData['arrDirectDebitDetails'] = $directDebitDetails;
            $arrData['currentBroadbandPrice'] = $objCurrentBroadbandPrice;
            $arrData['jlpPricingDate'] = $this->getPricingDate('john.lewis.pricing.drop2');
            $arrData['residentialDropTwoPricingDate'] = $this->getPricingDate('residential.pricing.drop2');
            $arrData['bolShowJlPriceIncrease'] = FeatureToggleManager::isOn(FeatureToggleManager::JOHN_LEWIS_PRICE_INCREASE);
            $arrData['bolShowPnPriceIncrease'] = FeatureToggleManager::isOn(FeatureToggleManager::PLUSNET_RES_PRICE_INCREASE);
            $arrData['bolShowJlPriceIncreaseDrop2'] = FeatureToggleManager::isOn(FeatureToggleManager::JOHN_LEWIS_PRICE_INCREASE_DROP2);
            $arrData['bolShowPnPriceIncreaseDrop2'] = FeatureToggleManager::isOn(FeatureToggleManager::PLUSNET_RES_PRICE_INCREASE_DROP2);
            $arrData['bolBtSport2020'] = FeatureToggleManager::isOn(FeatureToggleManager::BTSPORT_2020);
            $arrData['bglCallPlansGoLive'] = FeatureToggleManager::isOn(FeatureToggleManager::BGL_CALL_PLANS_GO_LIVE);
            $arrData['bolrpr21PricinIncrease'] = FeatureToggleManager::isOn(FeatureToggleManager::RPR21_PRICE_INCREASE);
            $arrData['businessPricingDate'] = $this->getPricingDate('business.pricing.drop2');
            $arrData['bolShowBusinessPricingNotification'] = $this->getShowBusinessPricingNotification();
            $arrData['bolShowRpr21Notification'] = FeatureToggleManager::isOn(FeatureToggleManager::RPR21_NOTIFICATIONS);
            $rpr21PricinIncreaseToDate = DateTime::createFromFormat('Y-m-d', FeatureToggleManager::getGoLiveDateByToggleName(FeatureToggleManager::RPR21_PRICE_INCREASE));
            $arrData['rpr21PricinIncreaseDate'] = $rpr21PricinIncreaseToDate->format('jS F Y');
            $arrData['priceIncreasePercent'] =  $this->getPriceIncreasePercent('Johnlewis','2021');
            $arrData['priceIncreasePercentPR'] =  $this->getPriceIncreasePercent('PlusnetResidential','2021');
            $arrData['bolShowBbp22Notification'] = FeatureToggleManager::isOn(FeatureToggleManager::BBP22_NOTIFICATIONS);
            $bbp22PricinIncreaseToDate = DateTime::createFromFormat('Y-m-d', FeatureToggleManager::getGoLiveDateByToggleName(FeatureToggleManager::BBP22_PRICE_INCREASE));
            $arrData['bbp22PricinIncreaseDate'] = $bbp22PricinIncreaseToDate->format('jS F Y');
            $arrData['bolBullguardRemoval'] = FeatureToggleManager::isOn(FeatureToggleManager::BULLGUARDSTOPSELL);
            $arrData['bolJohnLewisUpdates'] = FeatureToggleManager::isOn(FeatureToggleManager::JOHN_LEWIS_UPDATES);

            if (!empty($directDebitDetails)) {
                $arrData['bolDdInstructionActive'] = true;
            }
            $arrData = $this->performLineCheckIfFibreUpgradeCampaign($arrData, $objCoreService, $campaignCode);


            // 70182: the final stages of AccountChange were blowing up because the CInternetConnectionProduct
            // class could not be found when the data was being serialised - as it's a legacy class, it's not
            // picked up by the Framework autoloader.  Given that the AccountChange system is using the
            // 'adslComponent' value purely as a boolean to feed into the CurrentAccount.tpl template, the
            // function getAdslComponent() has been modified/renamed so that it simply returns true if the
            // component has been found in the database
            $arrData['adslComponent'] = $this->getAdslComponentExists($intServiceId);
            $arrData['wlrStatus'] = $arrWlrDetails['strComponentStatus'];
            $arrData['existingBroadband'] = $this->getExistingBroadbandContractData(
              $intServiceId,
              $objCoreService->getType()
            );

            $contractStatus = $this->getStatusService();
            $arrData['isCurrentlyOnFixedPriceContract'] = $contractStatus->isServicePriceProtected($intServiceId);

            /**
             * DBIZ-209
             * Business still has a few capped products. We need to identify these
             * in the template so we can clearly state to the customer that they're capped.
             */
            if ($objServiceDefinition->isBusiness()) {
                $arrData['isBizADSLCapped'] = AccountChange_BizProductsHelper::isCappedADSLProduct($arrData['intOldSdi']);
                $arrData['isBizFibreCapped'] = AccountChange_BizProductsHelper::isCappedFibreProduct($arrData['intOldSdi']);
            }
        }

        if ($objBusinessActor instanceof Auth_BusinessActor) {
            if ('PLUSNET_STAFF' == $objBusinessActor->getUserType()) {
                if (!$bolSchedule && !$this->canUserPerformInstantAccountChange()) {
                    $arrData['reasonForBlockage'] = 'instantChangePermissionDenied';
                }

                // This is incorrect - we should be using permissions
                if ($arrData['bolWlrOnly']) {
                    $arrAdditionalReqs[] = 'AccountChange_SelectHomephoneWorkplace';
                } else {
                    $arrAdditionalReqs[] = 'AccountChange_SelectBroadbandWorkplace';
                }

                $arrData['bolScheduleDowngrade'] = false;
                $arrData['bolShowTakeCharge'] = true;
                $arrData['bolTakeCharge'] = false;
            } else {
                // This is incorrect - we should be using permissions
                if ($arrData['bolWlrOnly']) {
                    $arrAdditionalReqs[] = 'AccountChange_SelectHomephone';
                } else {
                    $arrAdditionalReqs[] = 'AccountChange_SelectBroadband';
                }

                if ($arrData['bolChangeBroadbandOnly']) {
                    $this->isApplicationStateVariable('bolChangeBroadbandOnly');
                }

                if ($arrData['bolWlrOnly']) {
                    $this->isApplicationStateVariable('bolWlrOnly');
                }

                $arrData['bolPortal'] = true;
                $arrData['bolScheduleDowngrade'] = true;
                $arrData['bolShowTakeCharge'] = false;
                $arrData['bolTakeCharge'] = true;
            }
        }

        return array(
          'arrData'           => $arrData,
          'arrAdditionalReqs' => $arrAdditionalReqs
        );
    }

    /**
     * Return a feature toggle
     *
     * @return bool
     */
    protected function getShowBusinessPricingNotification()
    {
        return FeatureToggleManager::isOn(
            FeatureToggleManager::BUSINESS_PRICE_INCREASE_NOTIFICATIONS
        );
    }


    /**
     * @param $amount
     *
     * @return \I18n_Currency
     */
    public static function createCurrencyObject($amount)
    {
        return new I18n_Currency(
          AccountChange_Manager::CURRENCY_UNIT,
          $amount
        );
    }

    /**
     * @param Core_Service
     *
     * @return bool
     */
    protected function isBillingToday(Core_Service $objCoreService)
    {
        return $objCoreService->isBillingToday();
    }

    /**
     * @param String $campaignCode Campaign code
     *
     * @return bool
     */
    private static function isFibreUpgradeCampaign($campaignCode)
    {
        return in_array(
            $campaignCode,
            AccountChange_CampaignCodes::getAllFibreUpgradeCampaignCodes()
        );
    }

    /**
     * Modify journey for fibre upgrade campaign
     *
     * @return void
     */
    private function modifyJourneyForFibreUpgradeCampaign()
    {
        $this->removeCurrentAccountView();
        $this->removeBroadbandView();
        $this->addFibreUpgradeBroadbandView();
    }

    /**
     * Modify journey for fibre upgrade campaign
     *
     * Also worth noting here that we also switch the templates for the payment details and confirm details views
     * However the switching for these two templates is done at template level rather than here in the wizard array.
     * This might seem confusing but there are issues with the persistence of the view modifications,
     * specifically when the user is sent to the payment portal then sent back into account change.
     * To get around this for the two aforementioned templates we're doing the switching depending on whether
     * the usingC2F session variable is set instead.
     *
     * @return void
     */
    private function modifyJourneyForC2fUpgradeCampaign()
    {
        $this->removeView('CurrentAccount');
        $this->replaceViewWith('Broadband', self::$C2F_UPGRADE_BROADBAND_VIEW);
        $this->replaceViewWith('Hardware', self::$C2F_HARDWARE_VIEW);
        $this->replaceViewWith('Homephone', self::$C2F_HOME_PHONE_VIEW);
        $this->replaceViewWith('Summary', self::$C2F_SUMMARY_VIEW);
    }

    /**
     * @param string $replace
     * @param array $with
     */
    private function replaceViewWith($replace, array $with){
        $this->addView($replace, $with);
        $this->removeView($replace);
    }


    /**
     * Insert the new view ($with) just before the view we plan to replace
     *
     * @param string $replace
     * @param array $with
     */
    private function addView($replace, array $with)
    {
        if(!isset($this->arrViews[$replace])){
            return;
        }

        $offset = array_search($replace, array_keys($this->arrViews));

        $this->arrViews = array_merge
        (
            array_slice($this->arrViews, 0, $offset),
            $with,
            array_slice($this->arrViews, $offset, null)
        );
    }

    /**
     * Add fibre upgrade broadband view
     *
     * @return void
     */
    private function addFibreUpgradeBroadbandView()
    {
        $this->arrViews = self::$FIBRE_UPGRADE_BROADBAND_VIEW + $this->arrViews;
    }

    /**
     * Remove the view from arrViews
     *
     * @param string $viewName
     */
    private function removeView($viewName)
    {
        if(!isset($this->arrViews[$viewName])){
            return;
        }

        unset($this->arrViews[$viewName]);
    }

    /**
     * Remove broadband view
     *
     * @return void
     */
    private function removeBroadbandView()
    {
        unset($this->arrViews['Broadband']);
    }

    /**
     * Remove current account view
     *
     * @return void
     */
    private function removeCurrentAccountView()
    {
        unset($this->arrViews['CurrentAccount']);
    }

    /**
     * Perform LineCheck if fibre upgrade campaign
     *
     * @param array        $arrData        Data
     * @param Core_Service $objCoreService Core_Service
     * @param string       $campaignCode   Campaign code
     *
     * @return array
     */
    private function performLineCheckIfFibreUpgradeCampaign(
        $arrData,
        $objCoreService,
        $campaignCode
    ) {
        $arrData['objLineCheckResult'] = new LineCheck_Result();

        if (self::isFibreUpgradeCampaign($campaignCode) || $this->isC2fToggleSet()) {
            try {
                $arrData['objLineCheckResult'] = $this->lineChecker()->performLineCheck(
                    $arrData['intPhoneNumber'],
                    (is_null($arrData['strNewPostCode'])) ? $arrData['strPostCode'] : $arrData['strNewPostCode'],
                    $arrData['addressRef'],
                    $arrData['cssDatabaseCode'],
                    ($arrData['bolHousemove']) ? $arrData['houseName'] : $arrData['strThoroughFareNumber'],
                    $arrData['bolHousemove'],
                    $objCoreService,
                    $arrData['objLineCheckResult']
                );
            } catch (Exception $exception) {
                $arrData['bolCampaignLineCheckFailed'] = true;
            }
        }

        return $arrData;
    }

    /**
     * lineChecker
     *
     * @return \AccountChange_LineChecker
     */
    protected function lineChecker()
    {
        return new AccountChange_LineChecker();
    }

    /**
     * Get views
     *
     * @return array
     */
    protected function getViews()
    {
        return $this->arrViews;
    }

    /**
     * Get the customers direct debit details
     *
     * @param integer $serviceId Service Id
     *
     * @return Financial_DirectDebitDetails|array
     */
    protected function getDirectDebitDetails($serviceId)
    {
        if ($this->isNewBillingEngineOn(self::RBM_MIGRATION_COMPLETE, $serviceId)) {
            $ddDetails = $this->getDirectDebitDetailsRbm($serviceId);
        } else {
            $ddDetails = $this->getDirectDebitDetailsLegacy($serviceId);
        }

        return $ddDetails;
    }

    /**
     * Retrieve Direct Debit details from RBM
     * Note that the <directDebit> element of the returned api call to get the billing account details
     * contains masked values for the sort code and account number properties
     *
     * @param integer $serviceId Service Id
     *
     * @return array
     */
    protected function getDirectDebitDetailsRbm($serviceId)
    {
        AccountChange_AuditLogger::functionEntry(__METHOD__);

        $ddDetails = array();

        $billingAccount = $this->getBillingAccount($serviceId);

        if (isset($billingAccount['paymentDetails']['directDebit'])) {
            $ddDetailsArray = $billingAccount['paymentDetails']['directDebit'];
            /** @var $directDebitDetails \Plusnet\BillingApiClient\Entity\DirectDebit */
            $directDebitDetails = DirectDebit::fromArray($ddDetailsArray);

            if (!in_array($ddDetailsArray['status'], array('Unknown', 'Cancelled'))) {
                $ddDetails['name'] = $directDebitDetails->getName();
                $ddDetails['accountNumber'] = $directDebitDetails->getAccountNumber();
                $ddDetails['sortCode'] = $directDebitDetails->getSortCode();
            }
        }

        AccountChange_AuditLogger::functionExit(__METHOD__);

        return $ddDetails;
    }

    /**
     * Retrieve Direct Debit details from Legacy Coredb
     *
     * @param integer $serviceId Service Id
     *
     * @return array
     */
    protected function getDirectDebitDetailsLegacy($serviceId)
    {
        $ddDetails = array();

        $directDebitDetails = $this->getFinancialDirectDebitDetails($serviceId);

        if (empty($directDebitDetails)) {
            return null;
        }

        $ddDetails['name'] = $directDebitDetails->getName();

        $decryptedAccountNo = $directDebitDetails->getDecryptedBankAccountNumber();
        $intAccountLength = strlen($decryptedAccountNo);
        $ddDetails['accountNumber'] = str_repeat('*', $intAccountLength - 3) . substr($decryptedAccountNo, -3);

        $sortCode = $directDebitDetails->getBankSortCode();
        $intSortCodeLength = strlen($sortCode);
        $ddDetails['sortCode'] = str_repeat('*', $intSortCodeLength - 2) . substr($sortCode, -2);

        return $ddDetails;
    }

    /**
     * Retrieves the billing account using the billing api
     *
     * @param integer $serviceId Service Id
     *
     * @return array
     */
    protected function getBillingAccount($serviceId)
    {
        $billingApiClient = $this->getBillingApiClient();

        /** @var $billingApi \Plusnet\BillingApiClient\Facade\BillingApiFacade */
        return $billingApiClient->getBillingAccount($serviceId);
    }

    /**
     * Retrieves the DirectDebit details using the legacy Financial library
     *
     * @param int $serviceId Service Id
     *
     * @return Financial_DirectDebitDetails
     */
    protected function getFinancialDirectDebitDetails($serviceId)
    {
        $correctDdiStatus = array(
            Financial_DirectDebitDetails::DD_INSTRUCTION_SUBMITTED,
            Financial_DirectDebitDetails::DD_AWAITING_SUBMISSION,
            Financial_DirectDebitDetails::DD_ACTIVE
        );

        return Financial_DirectDebitDetails::getInstructionByServiceAndStatuses(
            $serviceId,
            $correctDdiStatus
        );
    }

    /**
     * Get the customers service id.
     *
     * @param integer $intServiceId Customers Service Id
     *
     * @return boolean
     */
    protected function getAdslComponentExists($intServiceId)
    {
        $tmpComponent = CInternetConnectionProduct::getInternetConnectionProductFromServiceId($intServiceId);

        $bolRetval = false;

        // NOTE: as per the comment above, to fix the issue for 70182, this function has been renamed to
        // getAdslComponentExists and now returns a boolean, rather than the object

        if ($tmpComponent !== false) {
            $bolRetval = true;
        }

        return $bolRetval;
    }

    /**
     * Get the customers new House Move Address
     *
     * @param integer $intServiceId Customers Service Id
     *
     * @return Plusnet\HouseMoves\Models\HouseMoveAddress
     */
    protected function getHouseMoveAddress($intServiceId)
    {
        $houseMoveService = ServiceManager::getService('HouseMoveService');
        $houseMove = $houseMoveService->getHouseMoveByServiceId($intServiceId);

        if (!empty($houseMove)) {
            $houseMoveAddressService = ServiceManager::getService('HouseMoveAddressService');
            $houseMoveAddress = $houseMoveAddressService->getByHouseMoveId($houseMove->getHouseMoveId());
        } else {
            $houseMoveAddress = new HouseMoveAddress();
        }

        return $houseMoveAddress;
    }

    /**
     * Does the customer have paper billing.
     *
     * @param Core_Service $core The customers service object
     *
     * @return boolean
     */
    public function customerHasPaperBilling($core)
    {
        if (in_array($core->getIsp(), array('greenbee', 'waitrose'))) {
            $db = Db_Manager::getAdaptor('Pdf');
            $components = $db->getHasPaperBilling(
                $core->getServiceId(),
                COMPONENT_PAPERBILLING,
                COMPONENT_PAPERBILLING_FREE
            );

            if (isset($components['hasPaperBilling']) && $components['hasPaperBilling'] > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the currently provisioned product given the service id.
     *
     * @param integer $serviceId The service Id of the customer
     *
     * @return Product_SupplierProduct
     */
    protected function getProvisionedProduct($serviceId)
    {
        try {
            $service = Userdata_Factory::getService(new Int($serviceId));

            if (null === $service) {
                throw new AccountChange_NoActiveSupplierProductException($serviceId);
            }

            $supplierProduct = Product_Factory::getSupplierProduct($service->getProvisionedProductId());
        } catch (Exception $exception) {
            error_log($exception);
            $supplierProduct = false;
        }

        return $supplierProduct;
    }

    /**
     * Get the name of the view which should be used for the completion of the wizard.
     *
     * @param string $strResultHandle The handle of the result
     * @param array  $arrVars         Any additional information
     *
     * @return string
     */
    public function getCompleteView($strResultHandle, array $arrVars)
    {
        switch ($strResultHandle) {
            case Mvc_ActionResult::SUCCESS:
                if (!empty($arrVars['bolHousemove'])) {
                    // Store new products in session for house moves to pick up
                    $sessionVarBase = sprintf('HouseMove_%d_', $arrVars['objCoreService']->getServiceId());
                    $sessionVarProducts = $sessionVarBase . 'arrSelectedProducts';

                    $_SESSION[$sessionVarProducts] = array(
                        "Internet Connection" => $this->getApplicationStateVar($this, "arrSelectedBroadband"),
                        "Home Phone"          => $this->getApplicationStateVar($this, "arrSelectedWlr")
                    );

                    $intServiceId = $arrVars['objCoreService']->getServiceId();
                    $this->arrRedirectUrls['HouseMoveConfirmation'] =
                        "/apps/housemove/confirm/{$intServiceId}/create";

                    return 'redirect:HouseMoveConfirmation';
                }

                return 'Complete';
            default:
                return 'Failed';
        }
    }

    /**
     * Will create the phone component
     *
     * @param int   $intServiceId            Service id
     * @param int   $intServiceComponentId   Service Component Id
     * @param int   $intTariffID             Tariff Id
     * @param array $productComponentOptions Product component options
     *
     * @return void
     */
    public function createWlr3Component(
        $intServiceId,
        $intServiceComponentId,
        $intTariffID,
        $productComponentOptions = array()
    ) {
        CWlrProduct::create(
            $intServiceId,
            $intServiceComponentId,
            $intTariffID,
            $productComponentOptions,
            ''
        );
    }
    /**
     * Get the entry method into the wizard.
     *
     * @return Mvc_HttpRequest
     */
    public function getEntryPointMethod()
    {
        return Mvc_HttpRequest::GET;
    }

    /**
     * Complete. Perform the account change process on the data collected throughout the wizard.
     *
     * @param array $arrCommands Commands
     * @param array $arrData     Data collected throughout the journey
     *
     * @return Mvc_WizardCompleteResult
     */
    public function complete(array $arrCommands, array $arrData)
    {
        AccountChange_AuditLogger::functionEntry(__METHOD__);

        $objWizardResult = new Mvc_WizardCompleteResult(Mvc_WizardCompleteResult::ERROR);

        // As the change is about to complete, we need a copy of the old line checker results here so we can show the
        // customer what we're moving from (for Fibre only as the WBC results don't get capped)
        //Collect the current product speed ranges
        $intServiceId = $this->getUserBusinessActor()->getExternalUserId();
        $objCoreService = new Core_Service($intServiceId);
        $objLineCheckResult = $arrData['objLineCheckResult'];
        $objDatabase = Db_Manager::getAdaptor('Core');
        $currentProductSpeedRanges = self::getMinAndMaxSpeedRanges($objCoreService, $objLineCheckResult);

        $preChangeLineCheckResult = array(
            'fttcDownSpeed'                       => $arrData['objLineCheckResult']->getFttcDownSpeed(),
            'fttcUpSpeed'                         => $arrData['objLineCheckResult']->getFttcUpSpeed(),
            'currentProductDownloadSpeedRangeMax' => $currentProductSpeedRanges['downloadSpeedRangeMax'],
            'currentProductDownloadSpeedRangeMin' => $currentProductSpeedRanges['downloadSpeedRangeMin'],
        );

        // We need to have an account change manager object in the data passed, otherwise fail.
        if (isset($arrData['objAccountChangeManager']) && isset($arrData['arrProductConfigurations'])) {
            /** @var AccountChange_Manager $objAccountChangeManager */
            $objAccountChangeManager = $arrData['objAccountChangeManager'];
            $arrInvoiceItems = array();
            $intServiceId = $this->getUserBusinessActor()->getExternalUserId();
            $objDatabase = Db_Manager::getAdaptor('AccountChange');
            $this->getLegacyFiles();
            $newSdi = $this->getApplicationStateVar(__CLASS__, 'intNewSdi');
            $oldSdi = $this->getApplicationStateVar(__CLASS__, 'intOldSdi');
            $newServiceDefinition = Core_ServiceDefinition::instance($newSdi);

            $storeDirectDebit = new AccountChange_StoreDirectDebit(Db_Manager::getAdaptor('AccountChange'));
            $storeDirectDebit->execute($intServiceId, $newServiceDefinition->getIsp(), $arrData);

            // Send of the data we collected throughout the journey to look for invoice items
            $arrInvoiceItems = self::generateInvoiceItems($arrData);
            $bolIsPaymentNeeded = false;

            if (!empty($arrInvoiceItems)) {
                $bolIsPaymentNeeded = true;
            }

            $paymentSuccess = isset($arrData['paymentSuccess']) ? $arrData['paymentSuccess'] : false;

            $impressionOfferId = null;
            if (!empty($arrData['impressionOfferId']))  {
                $impressionOfferId = $arrData['impressionOfferId'];
            }

            // The actual account change process begins. Check to see if the payment is needed or not.
            if (!$bolIsPaymentNeeded || ($paymentSuccess && $bolIsPaymentNeeded)) {
                $registry = AccountChange_Registry::instance();
                $registry->setEntry(
                    'intSelectedTariffID',
                    isset($arrData['arrSelectedBroadband']['intSelectedTariffID']) ?
                        $arrData['arrSelectedBroadband']['intSelectedTariffID'] : null
                );

                if ($arrData['arrSelectedBroadband']['presetDiscount']['promoCode']) {
                    $arrData['promoCode'] = $arrData['arrSelectedBroadband']['presetDiscount']['promoCode'];
                }

                $registry->setEntry(
                    'promoCode',
                    !empty($arrData['promoCode']) ? $arrData['promoCode'] : null
                );

                $registry->setEntry(
                    'selectedContractDuration',
                    isset($arrData['selectedContractDuration']) ? $arrData['selectedContractDuration'] : null
                );

                $registry->setEntry(
                    'phoneContractChange',
                    isset($arrData['phoneContractChange']) ? $arrData['phoneContractChange'] : null
                );

                $registry->setEntry(
                    'impressionOfferId',
                    $impressionOfferId
                );

                $lineCheckResult = $this->getApplicationStateVar(__CLASS__, 'objLineCheckResult');

                if (!empty($lineCheckResult)) {
                    $registry->setEntry('lineCheckResult', $lineCheckResult);
                }

                $registry->setEntry(
                    'bolAdslTakeover',
                    isset($arrData['bolAdslTakeover']) ? $arrData['bolAdslTakeover'] : null
                );

                $registry->setEntry(
                    'bolWlrTakeover',
                    isset($arrData['bolWlrTakeover']) ? $arrData['bolWlrTakeover'] : null
                );

                $registry->setEntry(
                    'vchConsentVersionHandle',
                    isset($arrData['vchConsentVersionHandle']) ? $arrData['vchConsentVersionHandle'] : null
                );

                //backdated Date
                $registry->setEntry(
                    'backDatedDate',
                    isset($arrData['backDatedDate']) ? $arrData['backDatedDate'] : null
                );

                $registry->setEntry(
                    'callerDisplay',
                    !empty($arrData['callerDisplay'])
                );

                $registry->setEntry(
                    'bolHousemove',
                    !empty($arrData['bolHousemove'])
                );

                $agreementDate = date('Y-m-d');
                $registry->setEntry(
                    'agreementDate',
                    $agreementDate
                );

                $contractTypeDto = $this->determineContractType($agreementDate, $intServiceId);
                $registry->setEntry(
                    'contractType',
                    $contractTypeDto->getContractType()
                );

                $registry->setEntry(
                    'contractSubType',
                    $contractTypeDto->getContractSubType()
                );

                $businessActor = $this->getApplicationStateVar(__CLASS__, 'objBusinessActor');
                $registry->setEntry('intServiceId', $intServiceId);

                $isCustomerUser = $this->isCustomerUser($businessActor);
                if (!$isCustomerUser) {
                    $registry->setEntry('intAgentActorId', $businessActor->getActorId());
                    $registry->setEntry('bolPortal', false);
                } else {
                    $registry->setEntry('bolPortal', true);
                }

                if (isset($arrData['promoCodeInvalidated']) && $arrData['promoCodeInvalidated']) {
                    $arrData['promoCode'] = null;
                    $arrData['arrSelectedBroadband']['presetDiscount'] = null;
                    $this->registerPegaInteraction($impressionOfferId, AccountChange_PegaInteractionHelper::REJECTED, $isCustomerUser, 'Negative');
                }

                if (isset($arrData['bolSchedule']) && !$arrData['bolSchedule']) {
                    /** @var \Plusnet\InventoryEventClient\Service\EventService $objInventoryEventService */
                    $objInventoryEventService = \BusTier_BusTier::getClient('inventoryEventService');

                    if (!empty($arrData['backDatedDate'])) {
                        $arrData['backDatedDate'] = str_replace('/', '-', $arrData['backDatedDate']);
                        $date = new DateTime($arrData['backDatedDate']);
                        $context = new \Plusnet\InventoryEventClient\Context\AccountChangeContext($date);
                        $context->setIsPredate(true);
                    } else {
                        $context = new \Plusnet\InventoryEventClient\Context\AccountChangeContext();
                    }
                    // we should not change broadband start date if not been changed
                    if (empty($newSdi) || ($oldSdi == $newSdi)) {
                        $curBBStartDate = $this->getCurrentBroadbandStartDate($intServiceId);
                        if (!empty($curBBStartDate)) {
                            $curBroadbandStartDateObj = new \DateTime();
                            $curBroadbandStartDateObj->setTimestamp(strtotime($curBBStartDate));
                            $context->setBroadbandStartDate($curBroadbandStartDateObj);
                        }
                    } else {
                        // to let Account Change context know that old BB is ceased
                        $context->setIsBBCRCease(true);
                    }
                    // Take pre snapshot to send it to Billing API (MBA)
                    $objInventoryEventService->takePreChangeSnapshot($intServiceId, $context);
                }

                // One off charge group data - will be registered in Action_RegisterCharges
                $registry->setEntry('invoiceItems', $arrInvoiceItems);
                $registry->setEntry('paymentHandoverId', $arrData['paymentHandoverId']);

                // Tone down the error handling whilst we play in the legacy codebase.
                $intPreviousErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_STRICT);
                $bolSuccess = $objAccountChangeManager->changeAccount();
                /** @var AccountChange_MGALSHelper $mgalsHelper */
                $mgalsHelper = AccountChange_ServiceManager::getService('MgalsHelper');
                if (!$objCoreService->isPartnerUser() && !empty($arrData['arrSelectedBroadband'])) {
                    $intScheduleId = $objAccountChangeManager->getServiceChangeScheduleId();

                    if (!empty($intScheduleId)) {
                        $selectedBroadbandType = $arrData['arrSelectedBroadband']['strBroadbandType'];
                        if (in_array($selectedBroadbandType, array(self::ACCESS_TECH_ADSL, self::ACCESS_TECH_FIBRE))) {
                            $mgalsHelper->saveMGALSFromLineCheckData(
                                $objLineCheckResult->getLineCheckId(),
                                $newSdi,
                                $intScheduleId
                            );
                        }
                    }
                }

                if (empty($arrData['bolSchedule']) &&
                    empty($arrCompleteData['bolBroadbandScheduled']) &&
                    !$objCoreService->isPartnerUser() &&
                    empty(AccountChange_Registry::instance()->getEntry('oldBBMgalsValue'))
                ) {
                    $mgalsHelper->makeScheduledMGALSLiveForServiceId($intServiceId);
                }

                if ($registry->getEntry('completeServiceChangeSchedule')) {
                    $objAccountChangeManager->completeServiceChangeSchedule(
                        $objAccountChangeManager->getServiceChangeScheduleId(),
                        $objDatabase
                    );
                }

                error_reporting($intPreviousErrorReporting);

                // In order to deal with the legacy and framework code locking each other if was decided to commit
                // the default transaction every time we called the database.
                Db_Manager::commit();

                if (!empty($objInventoryEventService)) {
                    $objInventoryEventService->takePostChangeSnapshot($intServiceId);
                }

                if ($bolSuccess) {
                    // Logging for tracking statistics on splunk.
                    error_log("$intServiceId: AccountChange journey successfully scheduled ("
                        . "campaign=" . $arrData['campaign']
                        . ", impressionOfferId=" . $arrData['impressionOfferId']
                        . ")");

                    $objLineCheckResult = $arrData['objLineCheckResult'];
                    $this->writeLinecheckResult($objLineCheckResult);

                    $objCoreServiceDef = Core_ServiceDefinition::instance($objCoreService->getType());

                    $objOngoingProductCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
                    $objDiscountedProductCost = null;
                    $arrNewBroadbandProduct = null;
                    $arrCompleteData = array();
                    $arrCompleteData['bolFibreToFibre'] = false;

                    if ($this->isFibreProduct($oldSdi) && $this->isFibreProduct($newSdi)) {
                        $arrCompleteData['bolFibreToFibre'] = true;
                    }

                    $arrCompleteData['completePage'] = true;
                    $arrCompleteData['preChangeLineCheckResult'] = $preChangeLineCheckResult;
                    if (isset($arrData['arrSelectedBroadband'])) {
                        $arrNewBroadbandProduct = $arrData['arrSelectedBroadband'];
                        if ($arrNewBroadbandProduct[self::WORKPLACE_NEW_COST_KEY] instanceof I18n_Currency && !$arrData['promoCodeInvalidated']) {
                            $objOngoingProductCost = $arrNewBroadbandProduct[self::WORKPLACE_NEW_COST_KEY];
                            $arrData['floOngoingProductCost'] = sprintf('%01.2f', $objOngoingProductCost->toDecimal());
                        } elseif($arrNewBroadbandProduct['intInContractMonthlyPrice'] instanceof I18n_Currency) {
                            $objOngoingProductCost = $arrNewBroadbandProduct['intInContractMonthlyPrice'];
                            $arrData['floOngoingProductCost'] = sprintf('%01.2f', $objOngoingProductCost->toDecimal());
                        }

                        if ($arrNewBroadbandProduct['intNewLeadingCost'] instanceof I18n_Currency) {
                            $objDiscountedProductCost = $arrNewBroadbandProduct['intNewLeadingCost'];
                            $arrData['floDiscountedProductCost'] = sprintf('%01.2f', $objDiscountedProductCost->toDecimal());
                        } else {
                            $arrData['floDiscountedProductCost'] = 0;
                        }

                        $arrData['objOngoingProductCost'] = $objOngoingProductCost;
                        $arrData['objDiscountedProductCost'] = $objDiscountedProductCost;

                        $arrCompleteData['selectedBroadband'] = $arrNewBroadbandProduct;
                    }

                    //Fix for problem https://workplace.plus.net/programme_tool/problem.html?problem_id=59074
                    $arrEmailExcludedProductNames = AccountChange_Manager::getBlockedAccountChangeEmailProductNames();
                    if (!in_array($arrNewBroadbandProduct['strNewProduct'], $arrEmailExcludedProductNames)) {
                        $objAccountChangeManager->sendConfirmationEmails($arrData);
                    }

                    $objAccountChangeManager->sendAppointmentEmail($arrData);
                    $arrCompleteData['bolPaymentTaken'] = $paymentSuccess;
                    $arrCompleteData['intNewMonthlyCost']
                        = AccountChange_PaymentMethodDetails::getNewMonthlyCost($arrData);
                    $arrCompleteData['intAmountTaken'] = new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $objAccountChangeManager->getTotalUpgradeCost($arrInvoiceItems)
                    );

                    $uxtNextInvoiceDate = I18n_Date::fromString($arrData['objCoreService']->getNextInvoiceDate());

                    if ($uxtNextInvoiceDate->fShort() == I18n_Date::now()->fShort()) {
                        $uxtNextInvoiceDate = I18n_Date::fromString(
                            UserdataServiceGetNextNextInvoiceDate($intServiceId)
                        );
                    }

                    $arrCompleteData['uxtNextInvoiceDate'] = date("d/m/Y", $uxtNextInvoiceDate->getTimestamp());
                    $arrCompleteData['uxtScheduledChangeDate'] = $this->getScheduledChangeDate($intServiceId);

                    if (isset($arrData['arrProductConfigurations']['objOldBroadband'])) {
                        $arrCompleteData['bolBroadbandScheduled']
                            = $arrData['arrProductConfigurations']['objOldBroadband']->isScheduled();
                    }

                    if (isset($arrData['arrProductConfigurations']['objOldWlr'])) {
                        $arrCompleteData['bolWlrScheduled']
                            = $arrData['arrProductConfigurations']['objOldWlr']->isScheduled();
                    }

                    if (isset($arrData['intNewWlrId'])) {
                        $arrCompleteData['bolWlrEssential'] = self::isWlrEssential($arrData['intNewWlrId']);
                    }

                    if (isset($arrData['intNewSdi'])) {
                        $arrCompleteData['bolIsNewProductValueFamilyProduct']
                            = $newServiceDefinition->isValueFamilyProduct();
                        $arrCompleteData['bolIsNewProductBpr09FamilyProduct']
                            = $newServiceDefinition->isBpr09FamilyProduct();
                        $arrCompleteData['bolIsRes2012FamilyProduct']
                            = $newServiceDefinition->isRes2012FamilyProduct();
                    }

                    $arrCompleteData['objOngoingProductCost'] = $objOngoingProductCost;
                    $arrCompleteData['objDiscountedProductCost'] = $objDiscountedProductCost;
                    $arrCompleteData['intDiscountLength'] = $arrData['intDiscountLength'];
                    $arrCompleteData['bolIsFibre'] = $this->isFibreProduct($newSdi);
                    $arrCompleteData['bolAppointmentBooked'] = isset($arrData['appointment']) ||
                        isset($arrData['appointmentdate1']);
                    $arrCompleteData['fibreAppointments'] = $this->getFttcAppointmentData($arrData);
                    $arrCompleteData['bolAppointmentRequired']
                        = $arrCompleteData['fibreAppointments']['hasInstallationAppointment'];


                    $newHouseMoveContractDuration = $arrData['selectedContractDuration'];

                    if ($newServiceDefinition->isJohnLewis()) {
                        $newHouseMoveContractDuration = self::getProductDefaultContractLength($newSdi);
                    }

                    if ($arrData['bolHousemove']) {
                        $postChangeHouseMoveActions = $this->getHouseMovePostScheduledChangeHelper(
                            $intServiceId,
                            $newHouseMoveContractDuration
                        );
                        $postChangeHouseMoveActions->execute();
                    }

                    if (!$objCoreService->isPartnerUser()
                        && ((isset($arrData['bolSchedule']) && $arrData['bolSchedule'])
                            || (isset($arrCompleteData['bolBroadbandScheduled'])
                                && $arrCompleteData['bolBroadbandScheduled']))
                    ) {
                        // SALES-2855 do not send broadband product change email if no broadband selected - eg via Workplace call plan change
                        if (!empty($arrData['arrSelectedBroadband'])
                                && isset($arrCompleteData['bolBroadbandScheduled'])
                                && $arrCompleteData['bolBroadbandScheduled']
                        ) {
                            $this->sendEmailMSN(
                                $intServiceId,
                                $arrData,
                                $arrCompleteData,
                                $objCoreServiceDef,
                                $objCoreService
                            );
                        }
                    }

                    $objWizardResult = new Mvc_WizardCompleteResult(
                        Mvc_WizardCompleteResult::SUCCESS,
                        $arrCompleteData
                    );
                } else {
                    // Logging for tracking statistics on splunk.
                    error_log("$intServiceId: AccountChange journey failed to schedule correctly ("
                        . "campaign=" . $arrData['campaign']
                        . ", impressionOfferId=" . $arrData['impressionOfferId']
                        . ")");
                }
            }
        }

        AccountChange_AuditLogger::functionExit(__METHOD__);

        return $objWizardResult;
    }

    /**
     * Registers a successful offer with Pega
     *
     * @param string $impressionOfferId  Impression offer ID
     * @param string $interaction        Interaction status
     * @param bool   $isExternalCustomer Check to use customer or agent channels
     * @param string $behaviour          Behaviour of PEGA interaction
     * @param string $direction          Direction of PEGA interaction
     */
    protected function registerPegaInteraction($impressionOfferId, $interaction, $isExternalCustomer, $behaviour = 'Positive', $direction = 'Inbound')
    {
        $actor = $this->getUserBusinessActor();
        $pegaHelper = $this->getPegaHelper();
        $pegaHelper->registerInteraction($impressionOfferId, $interaction, $actor->getActorId(), $isExternalCustomer, $behaviour, $direction);
    }

    /**
     * @return AccountChange_PegaHelper
     */
    protected function getPegaHelper()
    {
        return new AccountChange_PegaHelper();
    }

    /**
     * Determine contract type from agreement date and service id
     *
     * @param string $agreementDate Agreement date
     * @param int    $serviceId     Service Id
     *
     * @return \Plusnet\ContractsClient\Entity\ContractTypeDto
     */
    protected function determineContractType($agreementDate, $serviceId)
    {
        $contractClient = new ContractsClient();

        return $contractClient->getContractTypeByAgreementDate($agreementDate, $serviceId);
    }

    /**
     * Get a call feature API
     *
     * @return AccountChange_CallFeature_Api
     * @codeCoverageIgnore
     */
    protected function getCallFeatureApi()
    {
        return new AccountChange_CallFeature_Api();
    }

    /**
     * Is the c2f toggle set
     *
     * @return bool
     */
    public function isC2fToggleSet()
    {
        return AccountChange_C2mSalesChannels::isC2fToggleSet();
    }

    /**
     * Send MSN Email.
     *
     * @param int                    $intServiceId         Service ID
     * @param array                  $arrData              Data collected throughout the journey
     * @param array                  $arrCompleteData      Complete Data collected throughout the journey
     * @param Core_ServiceDefinition $objServiceDefinition Core Service Definition instance
     * @param Core_Service           $objCoreService       Core Service instance
     *
     * @return void
     */
    public function sendEmailMSN($intServiceId, $arrData, $arrCompleteData, $objServiceDefinition, $objCoreService)
    {
        $strHardware = "None";
        $intContractDuration = 0;
        $strScheduledChangeDate = 0;

        if (!empty($arrData['hardwareOption'])) {
            if ($objCoreService->isJohnLewisVispCustomer()) {
                $strHardware = "Wireless router";
            } elseif ($objServiceDefinition->isBusiness() || $objServiceDefinition->isResidential()) {
                $strHardware = "Plusnet wireless router";
            }
        } else {
            $strHardware = "None";
        }

        if ($objCoreService->isJohnLewisVispCustomer()) {
            $intContractDuration = 12;
        } elseif ($objServiceDefinition->isResidential()) {
            if (empty($arrData['selectedContractDuration'])) {
                if( $arrData['bolPortal'] == 1) {
                    $intContractDuration = static::getProductDefaultContractLength($arrData['arrSelectedBroadband']['intSdi']);
                }
                else{
                    $intContractDuration = $arrData['existingBroadband']['remainingTime'];
                }
            } else {
                $intContractDuration = $arrData['selectedContractDuration'];
            }
        } elseif ($objServiceDefinition->isBusiness()) {
            if (empty($arrData['selectedContractDuration'])) {
                $intContractDuration = static::getProductDefaultContractLength($arrData['arrSelectedBroadband']['intSdi']);
            } else {
                $intContractDuration = $arrData['selectedContractDuration'];
            }
        }

        if (!empty($arrCompleteData['uxtScheduledChangeDate'])) {
            $strScheduledChangeDate = str_replace('/', '-', $arrCompleteData['uxtScheduledChangeDate']);
            $strScheduledChangeDate = date('Y-m-d', strtotime($strScheduledChangeDate));
        }

        $arrEmailTemplateData = array(
            'broadbandProduct'                  => $arrData['arrSelectedBroadband']['strNewProduct'],
            'phoneProduct'                      =>
                empty($arrData['arrSelectedWlr']['strNewProduct']) ? '' : $arrData['arrSelectedWlr']['strNewProduct'],
            'contractLength'                    => $intContractDuration,
            'hardware'                          => $strHardware,
            'nextBillingDate'                   => $strScheduledChangeDate,
            'newMonthlyCost'                    => number_format($arrCompleteData['intNewMonthlyCost']->toDecimal(), 2),
            'houseMove'                         => empty($arrData['bolHousemove']) ? 0 : 1,
        );

        $speeds = $this->getSpeedsForEmail($arrData);

        $arrEmailTemplateData = array_merge($arrEmailTemplateData, [
            'minimumGuaranteedSpeedAvailable'   => $speeds['guaranteedSpeedAvailable'] ? 1 : 0,
            'minimumGuaranteedSpeed'            => $speeds['guaranteedSpeedValue'],
            'estimatedDownloadSpeedAvailable'   => $speeds['minimumEstimatedDownloadSpeedMbs'] > 0 ? 1 : 0,
            'estimatedDownloadSpeedRangeLower'  => $speeds['minimumEstimatedDownloadSpeedMbs'],
            'estimatedDownloadSpeedRangeHigher' => $speeds['maximumEstimatedDownloadSpeedMbs'],
            'estimatedUploadSpeedAvailable'     => $speeds['minimumDownloadSpeedMbs'] > 0 ? 1 : 0,
            'estimatedUploadSpeedRangeLower'    => $speeds['minimumEstimatedUploadSpeedMbs'],
            'estimatedUploadSpeedRangeHigher'   => $speeds['maximumEstimatedUploadSpeedMbs'],
            'maximumDownloadSpeed'              => $speeds['maximumDownloadSpeedMbs'],
            'minimumUploadSpeed'                => $speeds['minimumUploadSpeedMbs'],
            'maximumUploadSpeed'                => $speeds['maximumUploadSpeedMbs'],
            'advertisedDownloadSpeed'           => $speeds['advertisedDownloadSpeedMbs'],
            'advertisedUploadSpeed'             => $speeds['advertisedUploadSpeedMbs'],
        ]);

        $objAccountChangeEmailHandler = new AccountChange_ConfirmationEmailHandler();
        $objAccountChangeEmailHandler->sendEmailMSN(
            $intServiceId,
            'CUSTOMER_PRODUCT_CHANGE',
            $arrEmailTemplateData,
            'CONFIRMATION'
        );
    }

    /**
     * Get the speeds information required for the email
     *
     * @param array $arrData The account change data
     *
     * @return array
     * @throws Db_TransactionException
     * @throws NumberFormatException
     * @throws WrongTypeException
     */
    protected function getSpeedsForEmail($arrData)
    {
        $lineCheckId = null;
        if (isset($arrData['objLineCheckResult']) && $arrData['objLineCheckResult'] instanceof LineCheck_Result) {
            /** @var LineCheck_Result $lineCheckResult */
            $lineCheckResult = $arrData['objLineCheckResult'];

            $lineCheckId = $lineCheckResult->getLineCheckId();
        }

        $minimumGuaranteedSpeedKb = round($arrData['arrSelectedBroadband'][self::INT_MGS_IN_MB] * 1000,0);
        return (new SignupApplication_WelcomeEmailSpeeds())
            ->setCleanMGALS($minimumGuaranteedSpeedKb)
            ->setMinimumGuaranteedSpeed($minimumGuaranteedSpeedKb)
            ->setLineCheckId($lineCheckId)
            ->setServiceDefinitionId($arrData['arrSelectedBroadband']['intSdi'])
            ->getWelcomeEmailSpeedData();

    }

    /**
     * returns true if supplied service definition id is a fibre product.
     *
     * @param integer $intSdi service definition id
     *
     * @return boolean
     */
    public function isFibreProduct($intSdi = null)
    {
        $helper = new AccountChange_FibreHelper();

        return $helper->isFibreProduct($intSdi);
    }

    /**
     * Returns FTTC appointment data.
     *
     * @param array $arrData Validated application data
     *
     * @return array
     */
    private function getFttcAppointmentData($arrData)
    {
        $arrReturn = array('hasInstallationAppointment' => false);

        if ((isset($arrData['appointment']) || isset($arrData['appointmentdate1'])) &&
            $arrData['appointingType']['serviceHandle'] == 'FTTC'
        ) {
            if (isset($arrData['appointment'])) {
                if (isset($arrData['appointment']) &&
                    preg_match('/^\d\d\/\d\d\/\d\d\d\d(AM|PM)$/', $arrData['appointment'])
                ) {
                    $arrReturn['hasInstallationAppointment'] = true;
                    $arrReturn['alternativeAppointmentDates'] = false;
                    $arrReturn['appointmentDate'] = substr($arrData['appointment'], 0, 10);
                    $arrReturn['appointmentTime'] = substr($arrData['appointment'], 10, 2);
                }
            } else {
                $datesAreSet = false;

                for ($count = 1; $count <= 3; $count++) {
                    if (isset($arrData['appointmentdate' . $count]) &&
                        isset($arrData['appointmenttime' . $count]) &&
                        preg_match('/^\d{10}$/', $arrData['appointmentdate' . $count]) &&
                        preg_match('/^(AM|PM)$/', $arrData['appointmenttime' . $count])
                    ) {
                        $arrReturn['appointmentDate' . $count] = date('d/m/Y', $arrData['appointmentdate' . $count]);
                        $arrReturn['appointmentTime' . $count] = $arrData['appointmenttime' . $count];
                        $datesAreSet = true;
                    }
                }

                if ($datesAreSet) {
                    $arrReturn['hasInstallationAppointment'] = true;
                    $arrReturn['alternativeAppointmentDates'] = true;
                }
            }
        }

        return $arrReturn;
    }

    /**
     * Returns existing broadband contract details.
     *
     * @param integer                         $serviceId           Service Id of the account to check
     * @param integer                         $serviceDefinitionId Service Definition Id of the existing product
     * @param \Plusnet\ContractsClient\Client $contractsClient     Contract client object
     * @param ProductFamily                   $productFamily       ProductFamily object
     *
     * @return array
     */
    public function getExistingBroadbandContractData(
        $serviceId,
        $serviceDefinitionId,
        $contractsClient = null,
        $productFamily = null
    ) {
        if (is_null($productFamily)) {
            $productFamily = ProductFamily_Factory::getFamily($serviceDefinitionId);
        }

        $hasAutoContracts = $productFamily->hasAutoContracts();
        $isContracted = false;
        $contractDuration = 0;
        $remainingTime = 0;
        $hasExpiredContracts = false;

        if (true === $hasAutoContracts) {
            if (is_null($contractsClient)) {
                $contractsClient = BusTier_BusTier::getClient('contracts');
                $contractsClient->setServiceId($serviceId);
            }

            $contractsCriteria = array(
                'status'     => 'ACTIVE',
                'typeHandle' => \Plusnet\ContractsClient\Entity\ContractTypeHandle::PROFIT_FOREGONE_SERVICES,
                'serviceId'  => $serviceId,
            );

            try {
                $contracts = $contractsClient->getContracts($contractsCriteria);
            } catch (Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException $apiEx) {
                error_log(
                    'Billing API returned error: ' . $apiEx->getMessage()
                    . PHP_EOL . 'Backtrace: ' . $apiEx->getTraceAsString()
                );
                throw $apiEx;
            }

            if (!empty($contracts) && is_array($contracts)) {
                foreach ($contracts as $contract) {
                    if ($contract instanceof \Plusnet\ContractsClient\Entity\Contract) {
                        $isContracted = true;
                        $unit = \Plusnet\ContractsClient\Entity\DurationUnit::MONTH;
                        $contractRemainingTime = $contract->getRemainingTime($unit);

                        if (!empty($contractRemainingTime) && (is_array($contractRemainingTime)) &&
                            ($unit == $contractRemainingTime['unit']) &&
                            ($contractRemainingTime['value'] > $remainingTime)
                        ) {
                            $remainingTime = $contractRemainingTime['value'];
                            $contractDurationDetails = $contract->getDuration();

                            if (!empty($contractDurationDetails) && (is_array($contractDurationDetails)) &&
                                ($unit == $contractDurationDetails['unit'])
                            ) {
                                $contractDuration = $contractDurationDetails['value'];
                            }
                        }
                    }
                }
            } else {
                $contractsCriteria = array(
                    'status'     => 'OUT_OF_CONTRACT',
                    'typeHandle' => \Plusnet\ContractsClient\Entity\ContractTypeHandle::PROFIT_FOREGONE_SERVICES,
                    'serviceId'  => $serviceId,
                );

                // get all contracts based on criteria
                try {
                    $contracts = $contractsClient->getContracts($contractsCriteria);
                } catch (Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException $apiEx) {
                    error_log(
                        'Billing API returned error: ' . $apiEx->getMessage()
                        . PHP_EOL . 'Backtrace: ' . $apiEx->getTraceAsString()
                    );
                    throw $apiEx;
                }

                if (is_array($contracts) && sizeof($contracts) > 0) {
                    $hasExpiredContracts = true;
                }
            }
        }

        $contractData = array(
            'hasAutoContracts'    => $hasAutoContracts,
            'isContracted'        => $isContracted,
            'contractDuration'    => $contractDuration,
            'remainingTime'       => $remainingTime,
            'hasExpiredContracts' => $hasExpiredContracts,
        );

        return $contractData;
    }

    /**
     * Method to store collected linecheck.
     *
     * @param LineCheck_Result $objLineCheckResult Line check object
     *
     * @return boolean
     */
    protected function writeLinecheckResult(LineCheck_Result $objLineCheckResult)
    {
        try {
            $objAdaptor = Db_Manager::getAdaptor('LineCheck');
            $objAdaptor->activateServiceLineData($objLineCheckResult->getServiceId());
        } catch (Exception $objException) {
            error_log(
                "Activating ServiceLineData failed during account change process. \nDetails: "
                . $objException->getMessage() . "\n Backtrace: " . $objException->getTraceAsString()
            );
        }

        return true;
    }

    /**
     * Function to raise an autoproblem
     *
     * @param exception $ex DB_Exception
     *
     * @return int - autoproblem id
     */
    protected function raiseLineCheckProblem(DB_Exception $ex)
    {
        $strAdditionalComment = $ex->getFile() . "@" . $ex->getLine() . ". Type: " . get_class($ex) . " Message " .
            $ex->getMessage() . "\n";

        $objAutoProblem = new PomsClient_AutoProblem(
            'AUTO-PROBLEM: LineChecker Error',
            'Failed to assoicate line check with customer during account change',
            $strAdditionalComment,
            $strAdditionalComment
        );

        $intAutoProblemId = $objAutoProblem->raiseProblem();

        if (empty($intAutoProblemId)) {
            error_log('Failed to raise an autoproblem: ' . $strAdditionalComment);
        }

        return $intAutoProblemId;
    }

    /**
     * Based on the information we have gathered throughout the journey. Return an array of invoice items. This can be
     * empty. It should be called after all requirements were collected.
     *
     * TODO: Split this function down to getting adsl charges and wlr charges.
     *
     * @param array $arrData Data array
     *
     * @return array
     */
    public static function generateInvoiceItems(array $arrData)
    {
        $arrReturn = array();
        $arrProductConfigurations = $arrData['arrProductConfigurations'];

        if (isset($arrProductConfigurations['objOldBroadband'])) {
            if (self::isBbProRataChargeRequired($arrProductConfigurations)) {
                $arrProRataCharge = $arrProductConfigurations['objOldBroadband']->calculateProRataCharge();

                if (!empty($arrProRataCharge)) {
                    $arrReturn[] = array(
                        'description' => $arrProRataCharge['strOutstandingCharges'],
                        'amount'      => $arrProRataCharge['intOutstandingFees']->toDecimal(),
                        'gross'       => true,
                        'handle'      => 'SUBSCRIPTION'
                    );
                }
            }

            // The setup fee applies of there is a charge for the upgrade - but if the cutomer has WLR,
            // then the fee is deferred, so it is not taken during the change.
            $broadbandSetupFee = $arrProductConfigurations['objNewBroadband']->getSetupFee();

            if ($broadbandSetupFee && !$arrData['wlrCustomer']) {
                $arrReturn[] = array(
                    'description' => $broadbandSetupFee['description'],
                    'amount'      => $broadbandSetupFee['charge']->toDecimal(),
                    'gross'       => true,
                    'handle'      => 'SETUP'
                );
            } elseif ($broadbandSetupFee) {
                AccountChange_Registry::instance()->setEntry('bolActivationContract', true);
            }
        }

        // BPR09 -
        // We should calculate the upgrade on the new wlr object, as we may not have had one before. Also, the pro-rata
        // charge calculation is eventually handled by getUpgradeCost() in CWlrProduct.inc, which expects the new
        // service component id, and works everything out using the new rules from there  (the new rules being not to
        // pro-rata the cost, but just go on the difference in call plans).
        if (isset($arrProductConfigurations['objNewWlr'])) {
            if (self::isWlrProRataChargeRequired($arrProductConfigurations)) {
                $arrProRataCharge = $arrProductConfigurations['objNewWlr']->calculateProRataCharge();

                if (!empty($arrProRataCharge)) {
                    $arrReturn[] = array(
                        'description' => $arrProRataCharge['strOutstandingCharges'],
                        'amount'      => $arrProRataCharge['intOutstandingFees']->toDecimal(),
                        'gross'       => true,
                        'handle'      => 'SUBSCRIPTION'
                    );
                }
            }
        }

        $arrHardwareCharges = self::getHardwareCharges($arrData);

        if ($arrHardwareCharges) {
            $arrReturn[] = $arrHardwareCharges;
        }

        return $arrReturn;
    }

    /**
     * Get the hardware charges if applicable.
     *
     * @param array $arrData The data collected throughout the journey
     *
     * @return array
     */
    public static function getHardwareCharges(array $arrData)
    {
        $charges = null;

        if (self::isHardwareFree($arrData)) {
            return $charges;
        }

        $database = Db_Manager::getAdaptor('AccountChange');
        $details = $database->getServiceDefinitionDetails($arrData['intNewSdi']);

        // [SALES-893] Plusnet residential postage is now free in signup but charged here.
        // Refer to c2m to get the price rather than the (now 0.00) db config.
        if ($details['isp'] == 'plus.net'
            && ($details['type'] == 'residential' || $details['type'] == 'business')
            && isset($arrData['hardwareOption'])
            && '' != $arrData['hardwareOption']
        ) {
            $c2mPriceArray = null;
            if ($details['type'] == 'residential') {
                $c2mPriceArray = self::getAdditionalChargeHelper()->getResidentialPostageAndPackagingFromC2m();
            } elseif ($details['type'] == 'business') {
                $c2mPriceArray = self::getAdditionalChargeHelper()->getBusinessPostageAndPackagingFromC2m();
            }
            $c2mCharge = $c2mPriceArray['amount'];
            $c2mChargeExVat = $c2mPriceArray['exVatAmount'];
            if (is_numeric($c2mCharge) && is_numeric($c2mChargeExVat) && $c2mCharge > 0) {
                return array(
                    'description' => 'Postage and Packaging',
                    'amount' => $c2mCharge,
                    'exVatAmount' => $c2mChargeExVat,
                    'gross' => true
                );
            }
        }

        if (isset($arrData['hardwareOption']) &&
            '' != $arrData['hardwareOption'] &&
            (!self::freePartnerHardwarePostage($arrData))
        ) {
            $additionalCharge = self::getAdditionalChargeHelper()->getAdditionalCharge(
                $arrData['intNewSdi'],
                Financial_AdditionalChargeType::POSTAGE_AND_PACKAGING
            );

            if ((null != $additionalCharge) && ($additionalCharge->getAmount() > 0)) {
                $charges = array(
                    'description' => 'Postage and Packaging',
                    'amount'      => $additionalCharge->getAmount() / 100,
                    'gross'       => true
                );
            }
        }

        return $charges;
    }

    /**
     * Getter to determine if an ADSL pro-rata charge is required.
     *
     * @param array $arrProductConfigurations Product configurations
     *
     * @return boolean
     */
    protected static function isBbProRataChargeRequired($arrProductConfigurations)
    {
        return (
            $arrProductConfigurations['objOldBroadband']->getAccountChangeOperation()
            == AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE &&
            !AccountChange_Controller::isEssential($arrProductConfigurations['objOldBroadband']->getProductId()) &&
            !AccountChange_Controller::isEssential($arrProductConfigurations['objNewBroadband']->getProductId()) &&
            !$arrProductConfigurations['objOldBroadband']->isScheduled()
        );
    }

    /**
     * Getter to determine if a WLR pro-rata charge is required.
     *
     * @param array $arrProductConfigurations Product configurations
     *
     * @return boolean
     */
    protected static function isWlrProRataChargeRequired($arrProductConfigurations)
    {
        if (isset($arrProductConfigurations['objOldWlr']) && isset($arrProductConfigurations['objNewWlr'])) {
            return (
                $arrProductConfigurations['objOldWlr']->getAccountChangeOperation()
                == AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE &&
                !AccountChange_Controller::isWlrEssential($arrProductConfigurations['objOldWlr']->getProductId()) &&
                !AccountChange_Controller::isWlrEssential($arrProductConfigurations['objNewWlr']->getProductId()) &&
                !$arrProductConfigurations['objOldWlr']->isScheduled()
            );
        }

        return false;
    }

    /**
     * Returns a Auth_BusinessActor object for current logged in user.
     *
     * @return Auth_BusinessActor
     */
    protected function getCurrentBusinessActor()
    {
        $objLogin = Auth_Auth::getCurrentLogin();

        if ($objLogin instanceof Auth_Login) {
            return $objLogin->getBusinessActor();
        }

        return false;
    }

    /**
     * Returns a Auth_BusinessActor object for the account we are sending into the wizard.
     *
     * @return Auth_BusinessActor
     */
    protected function getUserBusinessActor()
    {
        $objTargetActor = $this->getWizardTargetActor();

        if ($objTargetActor instanceof Auth_BusinessActor) {
            return Auth_BusinessActor::getActorByAuthDetails(
                $objTargetActor->getUsername(),
                $objTargetActor->getRealm()
            );
        }

        return $this->getCurrentBusinessActor();
    }

    /**
     * Get the available broadband products depending on the current product.
     *
     * @param integer          $intServiceId The Service Id
     * @param integer          $intMarketId  The Market Id for the customer
     * @param boolean          $includeAll   Do we need to include all products
     * @param LineCheck_Result $lineCheck    Line check result
     * @param string           $promoCode    Promo code
     * @param boolean          $bolHousemove Does the account in house move
     * @param boolean          $hasPhone     Does the account currently have wlr
     * @param string           $source       The source of the request (WORKPLACE, MEMBER_CENTRE). The default is MEMBER_CENTRE.
     *
     * @return array
     */
    public static function getBroadbandProducts(
        $intServiceId,
        $intMarketId,
        $includeAll,
        $lineCheck = null,
        $promoCode = null,
        $bolHousemove = false,
        $hasPhone = false,
        $source = AccountChange_Controller::SOURCE_MEMBER_CENTRE_KEY,
        $campaign = 'NoCampaign',
        $agentSubmittedPromoCode = false
    ) {
        $arrExcludedServiceDefinitionIds = array();
        $arrProducts = array();
        $objDatabase = Db_Manager::getAdaptor('Core');
        $arrServiceDefinitionDetails = $objDatabase->getServiceDefinitionDetailsForService($intServiceId);
        $bolSignupViaPortal = ($includeAll ? 0 : 1);
        $isBusiness = (Core_ServiceDefinition::BUSINESS_ACCOUNT == $arrServiceDefinitionDetails['type']);
        $bolMps = false;
        $objCoreService = new Core_Service($intServiceId);

        if (!empty($arrServiceDefinitionDetails)) {
            // Business Accounts can change between options 1,2,3 and BoS products
            // They also have 12, 24, 36 and 48 month contracts (36 and 48 being those of BoS),
            // whereas res have 18 month contracts.
            if ($isBusiness) {
                $arrContractLengths = array('ANNUAL', '12MONTH', '24MONTH', '36MONTH', '48MONTH', 'MONTHLY');
                $family = self::getProductFamily($arrServiceDefinitionDetails['service_definition_id']);

                if ($family instanceof ProductFamily_Mps) {
                    $arrContractLengths = array('12MONTH', '36MONTH', '24MONTH', '48MONTH');
                    $bolMps = true;
                }

                $familyHandle = $family->getProductFamilyHandle();
            } else {
                //Residential
                $arrContractLengths = array('MONTHLY');
                $acAdaptor = Db_Manager::getAdaptor('AccountChange');
                $arrCurrentProductFamily = $acAdaptor->getProductFamilyHandle(
                    $arrServiceDefinitionDetails['service_definition_id']
                );
                $currentFamilyHandle = $arrCurrentProductFamily['productFamilyHandle'];
                $familyHandle = $currentFamilyHandle;
            }

            $isBauer = self::checkIfBauer($familyHandle);

            // BPR09 - we need to get the contract length of the selected product
            $objDatabase = Db_Manager::getAdaptor('AccountChange');
            // If performing account change from portal, exclude BoS products from the list.
            $arrServiceComponentProducts = $objDatabase->getAvailableServiceComponentProducts(
                $intMarketId,
                $arrServiceDefinitionDetails['type'],
                $arrContractLengths,
                $arrServiceDefinitionDetails['isp'],
                $arrServiceDefinitionDetails['isp'],
                $arrServiceDefinitionDetails['isp'],
                $arrServiceDefinitionDetails['isp'],
                $bolSignupViaPortal,
                $bolSignupViaPortal
            );

            if ($includeAll) {
                foreach ($arrServiceComponentProducts as $arrServiceComponentProduct) {
                    $arrExcludedServiceDefinitionIds[] = $arrServiceComponentProduct['service_definition_id'];
                }

                $intNumberOfExcludedServiceDefinitions = count($arrExcludedServiceDefinitionIds);
                $arrExcludedServiceDefinitionIds
                    = ($intNumberOfExcludedServiceDefinitions > 0) ? $arrExcludedServiceDefinitionIds : '';
                $arrServiceDefinitionProducts = $objDatabase->getAvailableServiceDefinitionProducts(
                    $arrServiceDefinitionDetails['type'],
                    $arrServiceDefinitionDetails['isp'],
                    $arrServiceDefinitionDetails['isp'],
                    $arrServiceDefinitionDetails['isp'],
                    $arrServiceDefinitionDetails['isp'],
                    $arrServiceDefinitionDetails['isp'],
                    $bolSignupViaPortal,
                    $intNumberOfExcludedServiceDefinitions,
                    $arrExcludedServiceDefinitionIds
                );

                $arrProducts = array_merge($arrServiceComponentProducts, $arrServiceDefinitionProducts);
            } else {
                $arrProducts = $arrServiceComponentProducts;
            }

            // If its a Bauer account the only Plusnet products available are the fibre ones
            if ($isBauer) {
                foreach ($arrProducts as $key => $product) {
                    if ($product['provisioningProfile'] != 'FTTC' ||
                        ($product['provisioningProfile'] == 'FTTC' &&
                            ($product['vchProductFamily'] != 'RES_APR14_DUAL' &&
                                $product['vchProductFamily'] != 'RES_APR14_SOLUS'))
                    ) {
                        unset($arrProducts[$key]);
                    }
                }
            }

            if (!$bolHousemove) {
                $hasPhone = self::hasWlr($intServiceId);
            }

            $arrProducts = self::getProducts(
                $arrServiceDefinitionDetails,
                $arrProducts,
                $includeAll,
                $lineCheck,
                $hasPhone,
                $bolHousemove
            );

            // If the customer is on a product which has solus/dual-play variants make sure the variant product has
            // been included (so that legacy solus customers have the option of changing to the dual-play version).
            $client = \BusTier_BusTier::getClient('productChangePlan');

            $broadbandProductVariants = $client->getServiceDefinitionVariants(
                $arrServiceDefinitionDetails['service_definition_id']
            );

            $availableProducts = $objDatabase->getAvailableServiceComponentProducts(
                $intMarketId,
                $arrServiceDefinitionDetails['type'],
                $arrContractLengths,
                $arrServiceDefinitionDetails['isp'],
                $arrServiceDefinitionDetails['isp'],
                $arrServiceDefinitionDetails['isp'],
                $arrServiceDefinitionDetails['isp'],
                0,
                0
            );

            $variantDetails = self::getBroadbandProductVariantDetails(
                $broadbandProductVariants,
                $arrServiceDefinitionDetails['service_definition_id'],
                $intMarketId
            );

            if (!empty($variantDetails)) {
                $availableProducts[] = $variantDetails;
            }

            foreach ($broadbandProductVariants as $variant) {
                foreach ($availableProducts as $product) {
                    if ($product['service_definition_id'] == $variant) {
                        $sdi = $product['service_definition_id'];

                        $familyObj = static::getProductFamily($sdi);
                        $isDual = $familyObj->isDualPlay();
                        $intProductCost = new I18n_Currency(
                            AccountChange_Manager::CURRENCY_UNIT,
                            $product['minimum_charge']
                        );

                        $downloadSpeedCap = (array_key_exists('intMaximumSpeed', $product) &&
                            !empty($product['intMaximumSpeed'])
                        ) ? $product['intMaximumSpeed'] : self::MAX_DOWN_SPEED;

                        $uploadSpeedCap = (array_key_exists('intMaxUploadSpeed', $product) &&
                            !empty($product['intMaxUploadSpeed'])
                        ) ? $product['intMaxUploadSpeed'] : self::MAX_UP_SPEED;

                        $productData = self::getMinAndMaxSpeedRanges($objCoreService, $lineCheck, $sdi);

                        $activationFee = $productData['activationFee'];
                        $isFibre = $productData['isFibre'];
                        $maxDownloadSpeed = $productData['maxDownloadSpeed'];
                        $maxUploadSpeed = $productData['maxUploadSpeed'];

                        if($bolHousemove && $isFibre && !$lineCheck->isFttcEnabled()) {
                            break;
                        }

                        if($bolHousemove && !$isFibre && !$lineCheck->isAdslEnabled()) {
                            break;
                        }

                        if ($maxDownloadSpeed > $downloadSpeedCap / 1000) {
                            $maxDownloadSpeed = $downloadSpeedCap / 1000;
                        }

                        if ($maxUploadSpeed != null && $maxUploadSpeed > $uploadSpeedCap / 1000) {
                            $maxUploadSpeed = $uploadSpeedCap / 1000;
                        }

                        $contractLength = static::getProductDefaultContractLength($sdi);
                        $contractLengthMonths = 1;

                        if (!empty($contractLength)) {
                            $contractLengthMonths = $contractLength;
                        }

                        $product['intSdi'] = $sdi;
                        $product['isDual'] = $isDual;
                        $product['strContract'] = strtoupper($product['vchContract']);
                        $product['strContractLength'] = $product['vchDisplayName'];
                        $product['intContractLengthMonths'] = $contractLengthMonths;
                        $product[self::PRODUCT_COST_KEY] = $intProductCost;
                        $product['strProductName'] = $product['name'];
                        $product['productFamily'] = $familyObj;
                        $product['objProductDiscountedCost'] = null;
                        $product['maxUploadSpeed'] = $maxUploadSpeed;
                        $product['maxDownloadSpeed'] = $maxDownloadSpeed;
                        $product['downloadSpeedRangeMin'] = $productData['downloadSpeedRangeMin'];
                        $product['downloadSpeedRangeMax'] = $productData['downloadSpeedRangeMax'];
                        $product['uploadSpeedRangeMin'] = $productData['uploadSpeedRangeMin'];
                        $product['uploadSpeedRangeMax'] = $productData['uploadSpeedRangeMax'];
                        $product['downloadSpeedRangeMgsFormatted'] = $productData['downloadSpeedRangeMgsFormatted'];
                        $product['uploadSpeedRangeMgsFormatted'] = $productData['uploadSpeedRangeMgsFormatted'];
                        $product[self::INT_MGS_IN_MB] = isset($productData[self::INT_MGS_IN_MB]) ? $productData[self::INT_MGS_IN_MB] : null;
                        $product['activationFee'] = $activationFee;
                        $product['isFibre'] = $isFibre;

                        if ($arrServiceDefinitionDetails['service_definition_id'] != $sdi) {
                            $product[self::MGALS_IN_MB_KEY] = isset($productData[self::MGALS_IN_MB_KEY]) ? $productData[self::MGALS_IN_MB_KEY] : null;
                            $product[self::INT_IMPACTED_MGALS] = isset($productData[self::INT_IMPACTED_MGALS]) ? $productData[self::INT_IMPACTED_MGALS] : null;
                            $product['strBroadbandType'] = $productData['strBroadbandType'];
                            $product['isPartner'] = $productData['isPartner'];
                        }

                        foreach ($arrProducts as $key => $prod) {
                            if ($prod['intSdi'] == $sdi) {

                                if (isset($prod[self::CURRENT_BASE_PRICE_KEY]) &&
                                    isset($prod[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY])) {

                                    $product[self::CURRENT_BASE_PRICE_KEY]             = $prod[self::CURRENT_BASE_PRICE_KEY];
                                    $product[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] = $prod[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY];
                                }

                                unset($arrProducts[$key]);
                                break;
                            }
                        }
                        $arrProducts[] = $product;
                    }
                }
            }
        }

        foreach ($arrProducts as $key => $product) {

            if (empty($product[self::CURRENT_BASE_PRICE_KEY]) ||
                empty($product[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY])) {

                // Solus/Dualplay variant we haven't retrieved prices for - get prices from Billing API
                $serviceId = $arrServiceDefinitionDetails[self::SERVICE_ID_KEY];
                $serviceDefinitionId = $product['intSdi'];
                $serviceComponentId = $product['intServiceComponentId'];
                $tariffId = $product['intTariffID'];

                $productOfferingPricePointPair = BasePriceHelper::createProductOfferingPricePointPairFromIds(
                    $serviceDefinitionId,
                    $serviceComponentId,
                    $tariffId);

                if ($productOfferingPricePointPair !== null) {

                    $basePrices =
                        BasePriceHelper::getBasePrices($serviceId, array($productOfferingPricePointPair));
                    $productOfferingPricePointId =
                        ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

                    $product[self::CURRENT_BASE_PRICE_KEY] =
                        static::createCurrencyObject($basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE_KEY]);

                    $product[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] =
                        static::createCurrencyObject($basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY]);

                    $arrProducts[$key] = $product;
                } else {

                    /**
                     * Product is not a legacy broadband account type without subscription components and does not have
                     * a single service component/tariff we can use to get it's price from Billing API.
                     *
                     * Ignore it.
                     */
                    unset($arrProducts[$key]);
                }
            }
        }

        if ($bolMps) {
            $account = AccountChange_Account::instance(new Int($intServiceId));
            $arrServiceDefinitionDetails['strContractLength'] = strtoupper($objCoreService->getBroadbandContract());
            $arrServiceDefinitionDetails['costLevel'] = $account->getCostLevel();
            $arrProducts = AccountChange_Controller::arrangeBoSProducts($arrProducts, $arrServiceDefinitionDetails);
        }

        if (!empty($promoCode)) {
            // Get details of discount when promoCode set up but only if all products are not included
            $arrProducts = AccountChange_Controller::getProductsWithDiscount(
                $arrProducts,
                $promoCode,
                $arrServiceDefinitionDetails['service_definition_id']
            );
        }
        if (!$isBusiness
            && $arrServiceDefinitionDetails['isp'] === 'plus.net'
        ) {

            $accountChangeRegistry = AccountChange_Registry::instance();
            $accountChangeRegistry->setEntry('BTLPromotion', '');
            if(isset($_SESSION['BTLPromotion']) && $_SESSION['BTLPromotion'] instanceof Promotion){

                $accountChangeRegistry->setEntry('BTLPromotion', $_SESSION['BTLPromotion']);
            }

            $C2MCopperToFibreJourney = new C2MCopperToFibreJourney(
                $arrProducts,
                $arrServiceDefinitionDetails['service_definition_id'],
                $accountChangeRegistry
            );

            $error_code = self::ERROR_CODE;

            try {
                $arrProducts = $C2MCopperToFibreJourney->getBroadbandProducts(
                    $intServiceId,
                    $campaign,
                    $promoCode,
                    $source,
                    $agentSubmittedPromoCode
                );
                $error_code = $C2MCopperToFibreJourney->getErrorCode();
            } catch (PromotionNotFoundException $pnfe) {
                Log_AuditLog::write(sprintf("Couldn't find promotion for the promo code ", $promoCode));
            }

            return [
                'arrProducts' => $arrProducts,
                'errorCode'   => $error_code
            ];
        }

        // In order to deal with the legacy and framework code locking each other if was decided to commit the default
        // transaction every time we called the database.
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        return $arrProducts;
    }

    /**
     * Filter the products we have received from the database.
     *
     * @param array            $arrServiceDefinitionDetails Current Service Definition Details
     * @param array            $arrProducts                 All the products we currently have
     * @param boolean          $includeAll                  Do we need to include all products
     * @param LineCheck_Result $lineCheck                   Line check result
     * @param boolean          $alreadyHasWlr               Does the account currently have wlr
     * @param boolean          $bolHousemove                Does the account in house move
     *
     * @return array
     */
    public static function getProducts(
        array $arrServiceDefinitionDetails,
        array $arrProducts,
        $includeAll,
        $lineCheck = null,
        $alreadyHasWlr = false,
        $bolHousemove = false
    ) {

        $arrNewProducts = array();
        $bolCurrentProductHasBeenIncluded = false;
        $arrSpecificProducts = array();

        if (Core_ServiceDefinition::BUSINESS_ACCOUNT == $arrServiceDefinitionDetails['type']) {
            $currentProductDefaultContract = self::getProductDefaultContractLength(
                $arrServiceDefinitionDetails['service_definition_id']
            );
        }

        $currentProductFamily = static::getProductFamily($arrServiceDefinitionDetails['service_definition_id']);
        $currentFamilyHandle = $currentProductFamily->getProductFamilyHandle();
        $currentProductVariant = $currentProductFamily->getVariantHandle();
        $currentProductHasAutoContracts = $currentProductFamily->hasAutoContracts();

        // Check if the current product is contracted
        $currentProductIsContracted = $currentProductFamily->isAutoContractedProduct();

        // Build up some details we can check against
        foreach ($arrProducts as $arrProduct) {
            $arrSpecificProducts[$arrProduct['service_definition_id']][] = strtoupper($arrProduct['vchContract']);
            $arrSpecificProducts[$arrProduct['service_definition_id']][$arrProduct['vchContract']]
                = $arrProduct['minimum_charge'];
            if ($arrServiceDefinitionDetails['service_definition_id'] === $arrProduct['service_definition_id']) {
                $currentMaxSpeed = $arrProduct['intMaximumSpeed'];
                $currentUploadSpeed = $arrProduct['intMaxUploadSpeed'];
            }
        }


        foreach ($arrProducts as $arrProduct) {
            $bolAddProduct = true;
            $newProductFamily = static::getProductFamily($arrProduct['service_definition_id']);
            $newProductVariant = $newProductFamily->getVariantHandle();
            $newFamilyHandle = $newProductFamily->getProductFamilyHandle();

            if ($newFamilyHandle === 'FTTP' || $newFamilyHandle === 'SoGEA') {
                continue;
            }

            if ($currentFamilyHandle != 'PDSLBUS' && $newFamilyHandle == 'PDSLBUS') {
                continue;
            } elseif ($currentFamilyHandle == 'PDSLBUS' && $newFamilyHandle != 'PDSLBUS') {
                continue;
            }

            if ($arrServiceDefinitionDetails['isp'] == 'plus.net') {
                // For current product with autoContracts implement rules based on LTC-980 and LTC-976 Also, for BRP-570
                if (true === $currentProductHasAutoContracts) {
                    // check if the new product is one of autoContracts
                    if ($newProductFamily->hasAutoContracts()) {
                        // new product with autoContracts check if the new product is a contracted one
                        $newProductIsContracted = $newProductFamily->isAutoContractedProduct();

                        if (true === $currentProductIsContracted) {
                            // current product is contracted

                            if (false === $newProductIsContracted) {
                                // the new product is non contracted - skip
                                continue;
                            }
                        }
                    } else {
                        // new product without autoContracts = legacy product - skip
                        continue;
                    }
                } else {
                    if (!$newProductFamily->hasAutoContracts() && (!$includeAll)) {
                        // new product without autoContracts in portal - skip
                        continue;
                    }
                }
            }

            $objProductDiscountedCost = null;
            $intProductCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $arrProduct['minimum_charge']);

            if ($arrProduct['service_definition_id'] == $arrServiceDefinitionDetails['service_definition_id']) {
                $bolCurrentProductHasBeenIncluded = true;
            }

            // If we are going from legacy plusnet residential products, customers are entitled to lead price.
            if (in_array($currentFamilyHandle, array('BBYW', '')) &&
                Core_ServiceDefinition::BUSINESS_ACCOUNT != $arrServiceDefinitionDetails['type']
            ) {
                if ($includeAll ||
                    (in_array('MONTHLY', $arrSpecificProducts[$arrProduct['service_definition_id']]) &&
                        !in_array('QUARTERLY', $arrSpecificProducts[$arrProduct['service_definition_id']])
                    ) ||
                    'QUARTERLY' == strtoupper($arrProduct['vchContract'])
                ) {
                    $bolAddProduct = true;

                    // If it is the quarterly product. This is kind of the lead product, so pad it out with monthly
                    // details such as the ongoing price (which is the price of the monthly product)
                    if ('QUARTERLY' == strtoupper($arrProduct['vchContract'])) {
                        $objProductDiscountedCost = new I18n_Currency(
                            AccountChange_Manager::CURRENCY_UNIT,
                            $arrProduct['minimum_charge']
                        );

                        if (isset($arrSpecificProducts[$arrProduct['service_definition_id']]['MONTHLY'])) {
                            $intProductCost = new I18n_Currency(
                                AccountChange_Manager::CURRENCY_UNIT,
                                $arrSpecificProducts[$arrProduct['service_definition_id']]['MONTHLY']
                            );
                        }
                    }
                } else {
                    $bolAddProduct = false;
                }
            }

            // FTTC related product selection here. You cannot have an FTTC product if your line does not support FTTC.
            // Doing this decision here, as it would break me to add yet another conditional if in the SQL.
            if (null != $lineCheck && !$lineCheck->isFttcEnabled() && 'FTTC' == $arrProduct['provisioningProfile']) {
                $bolAddProduct = false;
            }

            //remove adsl products if no adsl is available
            if (null !== $lineCheck && !$lineCheck->isAdslEnabled() &&
                (self::DSL_PROFILE === $arrProduct['provisioningProfile'] ||
                    self::ADSL_PROFILE === $arrProduct['provisioningProfile'])) {
                $bolAddProduct = false;
            }

            // If the current service definition is FTTC, do not allow regrade to non-FTTC products.
            if (!$includeAll &&
                'FTTC' == $arrServiceDefinitionDetails['provisioningProfile'] &&
                'FTTC' != $arrProduct['provisioningProfile']
            ) {
                $bolAddProduct = false;
            }

            $isDual = $newProductFamily->isDualPlay();

            $contractLength = static::getProductDefaultContractLength($arrProduct['service_definition_id']);
            $contractLengthMonths = 1;

            if (!empty($contractLength)) {
                $contractLengthMonths = $contractLength;
            }

            //allow current product to be displayed
            if($arrServiceDefinitionDetails['service_definition_id'] === $arrProduct['service_definition_id'] && !$bolHousemove) {
                $bolAddProduct = true;
            }

            //allow products to be shown that share the same technology(incase they have been removed due to no availability)
            if(!$bolHousemove &&
                static::isProductTheSameProfileAsExisting($arrProduct,
                    $currentMaxSpeed,
                    $currentUploadSpeed,
                    $arrServiceDefinitionDetails['provisioningProfile'])) {
                $bolAddProduct = true;
            }

            // If this product is un-contracted and the users current product is contracted then don't add it.
            if (Core_ServiceDefinition::BUSINESS_ACCOUNT == $arrServiceDefinitionDetails['type']) {
                if ($currentProductFamily->hasAutoContracts() &&
                    !empty($currentProductDefaultContract) &&
                    (empty($contractLength) && ProductFamily_BizGoLarge::PRODUCT_FAMILY_HANDLE != $newFamilyHandle)
                ) {
                    $bolAddProduct = false;
                }
                if ($bolHousemove) {
                    if (!$alreadyHasWlr && $isDual) {
                        $bolAddProduct = false;
                    } elseif ($alreadyHasWlr && !$isDual) {
                        $bolAddProduct = false;
                    }
                } elseif (($currentProductFamily->isDualPlay() || $alreadyHasWlr === true) && !$isDual) {
                    $bolAddProduct = false;
                }
            }

            if (!empty($contractLength)) {
                $contractLength = "{$contractLength}MONTH";
            } else {
                $contractLength = 'MONTHLY';
            }

            // By default we are adding products, for the portal we hide some of them
            if ($bolAddProduct) {
                if ($arrProduct['signup_via_portal']) {
                    $objCoreService = new Core_Service($arrServiceDefinitionDetails[self::SERVICE_ID_KEY]);
                    $productData = self::getMinAndMaxSpeedRanges(
                        $objCoreService,
                        $lineCheck,
                        $arrProduct['service_definition_id']
                    );
                }

                $activationFee = $productData['activationFee'];
                $isFibre = $productData['isFibre'];
                $maxDownloadSpeed = $productData['maxDownloadSpeed'];
                $maxUploadSpeed = $productData['maxUploadSpeed'];
                $bglCallPlansGoLive = FeatureToggleManager::isOn(FeatureToggleManager::BGL_CALL_PLANS_GO_LIVE);

                $arrNewProducts[] = array(
                    'intSdi'                         => $arrProduct['service_definition_id'],
                    'strProductName'                 => $arrProduct['name'],
                    'intTariffID'                    => $arrProduct['intTariffID'],
                    'intServiceComponentId'          => $arrProduct['intServiceComponentId'],
                    'strContract'                    => strtoupper($arrProduct['vchContract']),
                    'strContractLength'              => $arrProduct['vchDisplayName'],
                    self::PRODUCT_COST_KEY           => $intProductCost,
                    'objProductDiscountedCost'       => $objProductDiscountedCost,
                    'costLevel'                      => isset($arrProduct['vchDescription'])
                        ? $arrProduct['vchDescription'] : '',
                    'provisioningProfile'            => $arrProduct['provisioningProfile'],
                    'isFibre'                        => $isFibre,
                    'isDual'                         => $isDual,
                    'defaultContractLength'          => $contractLength,
                    'intContractLengthMonths'        => $contractLengthMonths,
                    'maxDownloadSpeed'               => $maxDownloadSpeed,
                    'maxUploadSpeed'                 => $maxUploadSpeed,
                    'downloadSpeedRangeMin'          => $productData['downloadSpeedRangeMin'],
                    'downloadSpeedRangeMax'          => $productData['downloadSpeedRangeMax'],
                    'uploadSpeedRangeMin'            => $productData['uploadSpeedRangeMin'],
                    'uploadSpeedRangeMax'            => $productData['uploadSpeedRangeMax'],
                    'activationFee'                  => $activationFee,
                    'productFamily'                  => $newProductFamily,
                    self::MGALS_IN_MB_KEY            => isset($productData[self::MGALS_IN_MB_KEY])
                        ? $productData[self::MGALS_IN_MB_KEY] : null,
                    self::INT_MGS_IN_MB => isset($productData[self::INT_MGS_IN_MB])
                        ? $productData[self::INT_MGS_IN_MB] : null,
                    self::INT_IMPACTED_MGALS => isset($productData[self::INT_IMPACTED_MGALS]) ?
                        $productData[self::INT_IMPACTED_MGALS] : null,
                    'uploadSpeedRangeMgsFormatted'   => $productData['uploadSpeedRangeMgsFormatted'],
                    'downloadSpeedRangeMgsFormatted' => $productData['downloadSpeedRangeMgsFormatted'],
                    'strBroadbandType'               => isset($productData['strBroadbandType']) ?
                        $productData['strBroadbandType'] : null,
                    'isPartner'                      => isset($productData['isPartner']) ?
                        $productData['isPartner'] : 0,
                    'bglCallPlansGoLive'             => $bglCallPlansGoLive
                );

                if (isset($arrProduct['vchProductFamily'])) {
                    $arrNewProducts[count($arrNewProducts) - 1]['vchProductFamily'] = $arrProduct['vchProductFamily'];
                }
            }
        }

        // Get prices from Billing API
        $serviceId = $arrServiceDefinitionDetails[self::SERVICE_ID_KEY];
        $productOfferingPricePointPairs = array();

        foreach ($arrNewProducts as $newProductKey => $arrNewProduct) {

            $productOfferingPricePointPair = BasePriceHelper::createProductOfferingPricePointPairFromIds(
                $arrNewProduct['intSdi'],
                $arrNewProduct['intServiceComponentId'],
                $arrNewProduct['intTariffID']);

            if ($productOfferingPricePointPair !== null) {

                $arrNewProducts[$newProductKey]['productOfferingPricePointId'] =
                    ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

                $productOfferingPricePointPairs[] = $productOfferingPricePointPair;
            } else {

                /**
                 * Product is not a legacy broadband account type without subscription components and does not have a
                 * single service component/tariff we can use to get it's price from Billing API.
                 *
                 * Ignore it.
                 */
                unset($arrNewProducts[$newProductKey]);
            }
        }

        $basePrices = BasePriceHelper::getBasePrices($serviceId, $productOfferingPricePointPairs);

        foreach ($arrNewProducts as $key => $arrNewProduct) {

            $productOfferingPricePointId = $arrNewProduct['productOfferingPricePointId'];

            $arrNewProducts[$key][self::CURRENT_BASE_PRICE_KEY] =
                static::createCurrencyObject($basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE_KEY]);
            $arrNewProducts[$key][self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] =
                static::createCurrencyObject($basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY]);
        }

        if (!$bolCurrentProductHasBeenIncluded) {
            $arrNewProducts[] = self::getCurrentBroadbandProduct($arrServiceDefinitionDetails, $lineCheck);
        }
        return $arrNewProducts;
    }


    /**
     * Get accurate estimated speeds.
     *
     * @param LineCheck_Result $lineCheck           Line check results
     * @param integer          $serviceDefinitionId Product service definition id
     *
     * @return array
     */
    protected static function getProductDataFromSupplierProductRules(LineCheck_Result $lineCheck, $serviceDefinitionId)
    {
        $supplierProduct = static::getSupplierProductFromSupplierRules($lineCheck, $serviceDefinitionId);
        $accessTechnology = $supplierProduct->getAccessTechnology()->getValue();

        if ($accessTechnology == 'FTTC') {

            $activationFee = self::getProductActivationFee($serviceDefinitionId);
            $isFibre = true;
            $maxDownloadSpeed = $lineCheck->getFttcUncappedDownSpeed() / 1000;
            $maxUploadSpeed = $lineCheck->getFttcUncappedUpSpeed() / 1000;
            $downloadSpeedRangeMin = $lineCheck->getRangeABottomDownstreamDataBandwidth() / 1000;
            $downloadSpeedRangeMax = $lineCheck->getRangeATopDownstreamDataBandwidth() / 1000;
        } elseif ($accessTechnology == 'ADSL2+' || $accessTechnology == 'ADSL2') {

            $activationFee = null;
            $isFibre = false;
            $maxDownloadSpeed = $lineCheck->getWbcSpeed() / 1000;
            $maxUploadSpeed = null;
            $downloadSpeedRangeMin = $lineCheck->getWbcSpeedRangeMin() / 1000;
            $downloadSpeedRangeMax = $lineCheck->getWbcSpeedRangeMax() / 1000;
        } else {

            $activationFee = null;
            $isFibre = false;
            $maxDownloadSpeed = $lineCheck->getMaxSpeed() / 1000;
            $maxUploadSpeed = null;
            $downloadSpeedRangeMin = $lineCheck->getMaxSpeedRangeMin() / 1000;
            $downloadSpeedRangeMax = $lineCheck->getMaxSpeedRangeMax() / 1000;
        }
        $uploadSpeedRangeMin = null;
        $uploadSpeedRangeMax = null;

        return array(
            'activationFee'         => $activationFee,
            'isFibre'               => $isFibre,
            'maxDownloadSpeed'      => $maxDownloadSpeed,
            'maxUploadSpeed'        => $maxUploadSpeed,
            'downloadSpeedRangeMin' => $downloadSpeedRangeMin,
            'downloadSpeedRangeMax' => $downloadSpeedRangeMax,
            'uploadSpeedRangeMin'   => $uploadSpeedRangeMin,
            'uploadSpeedRangeMax'   => $uploadSpeedRangeMax,
        );
    }

    /**
     * Get estimated speeds only using data available from the line check results.
     *
     * @param LineCheck_Result $lineCheck        Line check results
     * @param array            $product          Product
     * @param integer          $downloadSpeedCap Download speed cap
     * @param integer          $uploadSpeedCap   Upload speed cap
     *
     * @return array
     */
    protected static function getProductDataFromLineCheckResults(
        $lineCheck,
        $product,
        $downloadSpeedCap,
        $uploadSpeedCap
    ) {
        if ($product['provisioningProfile'] == 'FTTC') {
            $activationFee = self::getProductActivationFee($product['service_definition_id']);
            $isFibre = true;
            $downloadSpeedRangeMin = null;
            $downloadSpeedRangeMax = null;
            $uploadSpeedRangeMin = null;
            $uploadSpeedRangeMax = null;

            if ($lineCheck != null) {
                $maxUploadSpeed = $lineCheck->getFttcUncappedUpSpeed() / 1000;
                $maxDownloadSpeed = $lineCheck->getFttcUncappedDownSpeed() / 1000;
                $downloadSpeedRangeMin = $lineCheck->getRangeABottomDownstreamDataBandwidth() / 1000;
                $downloadSpeedRangeMax = $lineCheck->getRangeATopDownstreamDataBandwidth() / 1000;
                $uploadSpeedRangeMin = $lineCheck->getRangeABottomUpstreamDataBandwidth() / 1000;
                $uploadSpeedRangeMax = $lineCheck->getRangeATopUpstreamDataBandwidth() / 1000;
            } else {
                $maxUploadSpeed = $uploadSpeedCap / 1000;
                $maxDownloadSpeed = $downloadSpeedCap / 1000;
            }
        } else {
            $activationFee = null;
            $isFibre = false;
            $maxUploadSpeed = null;
            $downloadSpeedRangeMin = null;
            $downloadSpeedRangeMax = null;
            $uploadSpeedRangeMin = null;
            $uploadSpeedRangeMax = null;

            if ($lineCheck != null) {
                if ($lineCheck->isWbcEnabled()) {
                    $maxDownloadSpeed = $lineCheck->getWbcSpeed() / 1000;
                    $downloadSpeedRangeMin = $lineCheck->getWbcSpeedRangeMin() / 1000;
                    $downloadSpeedRangeMax = $lineCheck->getWbcSpeedRangeMax() / 1000;
                } else {
                    $maxDownloadSpeed = $lineCheck->getMaxSpeed() / 1000;
                    $downloadSpeedRangeMin = $lineCheck->getMaxSpeedRangeMin() / 1000;
                    $downloadSpeedRangeMax = $lineCheck->getMaxSpeedRangeMax() / 1000;
                }
            } else {
                $maxDownloadSpeed = $downloadSpeedCap / 1000;
            }
        }

        return array(
            'activationFee'         => $activationFee,
            'isFibre'               => $isFibre,
            'maxDownloadSpeed'      => $maxDownloadSpeed,
            'maxUploadSpeed'        => $maxUploadSpeed,
            'downloadSpeedRangeMin' => $downloadSpeedRangeMin,
            'downloadSpeedRangeMax' => $downloadSpeedRangeMax,
            'uploadSpeedRangeMin'   => $uploadSpeedRangeMin,
            'uploadSpeedRangeMax'   => $uploadSpeedRangeMax,
        );
    }

    /**
     * Get the supplier product selection from line check result and service definition.
     *
     * @param LineCheck_Result $lineCheck           Line check results
     * @param integer          $serviceDefinitionId Service definition id
     *
     * @return Product_SupplierProduct
     */
    protected static function getSupplierProductFromSupplierRules(LineCheck_Result $lineCheck, $serviceDefinitionId)
    {
        $supplierRules = new Product_SupplierProductRules($lineCheck, new Int($serviceDefinitionId));

        return $supplierRules->getSupplierProduct();
    }

    /**
     * Get the current adsl product of the customer.
     *
     * @param array            $arrServiceDefinitionDetails Current Service Definition Details
     * @param LineCheck_Result $lineCheck                   Line check result
     *
     * @return array
     */
    public static function getCurrentBroadbandProduct(array $arrServiceDefinitionDetails, $lineCheck = null)
    {
        $intProductCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
        $objProductDiscountedCost = null;
        $db = Db_Manager::getAdaptor('AccountChange');
        $arrTariffData = $db->getServiceDefinitionTariffData($arrServiceDefinitionDetails[self::SERVICE_ID_KEY]);

        if (isset($arrTariffData['intCostIncVatPence'])) {
            $intProductCost = new I18n_Currency(
                AccountChange_Manager::CURRENCY_UNIT,
                $arrTariffData['intCostIncVatPence'] / 100
            );
        } elseif (isset($arrServiceDefinitionDetails['minimum_charge'])) {
            $intProductCost = new I18n_Currency(
                AccountChange_Manager::CURRENCY_UNIT,
                $arrServiceDefinitionDetails['minimum_charge']
            );
        }

        $downloadSpeedCap = (array_key_exists('intMaximumSpeed', $arrServiceDefinitionDetails) &&
            !empty($arrServiceDefinitionDetails['intMaximumSpeed'])
        ) ? $arrServiceDefinitionDetails['intMaximumSpeed'] : self::MAX_DOWN_SPEED;

        $uploadSpeedCap = (array_key_exists('intMaxUploadSpeed', $arrServiceDefinitionDetails) &&
            !empty($arrServiceDefinitionDetails['intMaxUploadSpeed'])
        ) ? $arrServiceDefinitionDetails['intMaxUploadSpeed'] : self::MAX_UP_SPEED;

        if ($arrServiceDefinitionDetails['provisioningProfile'] == 'FTTC') {
            $activationFee = self::getProductActivationFee($arrServiceDefinitionDetails['service_definition_id']);
            $isFibre = true;
            $maxDownloadSpeed = (null != $lineCheck) ? $lineCheck->getFttcDownSpeed() / 1000 : $downloadSpeedCap / 1000;
            $maxUploadSpeed = (null != $lineCheck) ? $lineCheck->getFttcUpSpeed() / 1000 : $uploadSpeedCap / 1000;
        } else {
            $activationFee = null;
            $isFibre = false;
            $maxDownloadSpeed = (null != $lineCheck) ? $lineCheck->getMaxSpeed() / 1000 : $downloadSpeedCap / 1000;
            $maxUploadSpeed = null;
        }

        $familyObj = static::getProductFamily($arrServiceDefinitionDetails['service_definition_id']);
        $isDual = $familyObj->isDualPlay();

        $objCoreService = new Core_Service($arrServiceDefinitionDetails[self::SERVICE_ID_KEY]);
        $productData = self::getMinAndMaxSpeedRanges($objCoreService, $lineCheck);

        $arrProduct = array(
            'intSdi'                         => $arrServiceDefinitionDetails['service_definition_id'],
            'strProductName'                 => $arrServiceDefinitionDetails['name'],
            self::PRODUCT_COST_KEY                 => $intProductCost,
            'objProductDiscountedCost'       => $objProductDiscountedCost,
            'provisioningProfile'            => $arrServiceDefinitionDetails['provisioningProfile'],
            'isFibre'                        => $isFibre,
            'maxDownloadSpeed'               => $maxDownloadSpeed,
            'maxUploadSpeed'                 => $maxUploadSpeed,
            'downloadSpeedRangeMin'          => $productData['downloadSpeedRangeMin'],
            'downloadSpeedRangeMax'          => $productData['downloadSpeedRangeMax'],
            'uploadSpeedRangeMin'            => $productData['uploadSpeedRangeMin'],
            'uploadSpeedRangeMax'            => $productData['uploadSpeedRangeMax'],
            'activationFee'                  => $activationFee,
            'productFamily'                  => $familyObj,
            'isDual'                         => $isDual,
            'uploadSpeedRangeMgsFormatted'   => $productData['uploadSpeedRangeMgsFormatted'],
            'downloadSpeedRangeMgsFormatted' => $productData['downloadSpeedRangeMgsFormatted'],
            self::MGALS_IN_MB_KEY            => isset($productData[self::MGALS_IN_MB_KEY]) ? $productData[self::MGALS_IN_MB_KEY] : null,
            self::INT_MGS_IN_MB => isset($productData[self::INT_MGS_IN_MB]) ? $productData[self::INT_MGS_IN_MB] : null,
            self::INT_IMPACTED_MGALS => isset($productData[self::INT_IMPACTED_MGALS]) ? $productData[self::INT_IMPACTED_MGALS] : null,
            'strBroadbandType'               => isset($productData['strBroadbandType']) ? $productData['strBroadbandType'] : null,
            'isPartner'                      => isset($productData['isPartner']) ? $productData['isPartner'] : 0
        );

        // Set tariff details to defaults, if no data present
        if (empty($arrTariffData)) {
            $arrTariffData = array(
                'intTariffID'           => null,
                'intCostIncVatPence'    => null,
                'strContract'           => null,
                'strContractLength'     => null,
                'intServiceComponentId' => null
            );
        }

        $arrProduct = array_merge($arrProduct, $arrTariffData);

        // Get prices from Billing API
        $productOfferingPricePointPair = BasePriceHelper::createProductOfferingPricePointPairFromIds(
            $arrProduct['intSdi'],
            $arrProduct['intServiceComponentId'],
            $arrProduct['intTariffID']);

        if ($productOfferingPricePointPair !== null) {

            $serviceId = $arrServiceDefinitionDetails[self::SERVICE_ID_KEY];
            $basePrices = BasePriceHelper::getBasePrices($serviceId, array($productOfferingPricePointPair));

            $productOfferingPricePointId =
                ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

            $arrProduct[self::CURRENT_BASE_PRICE_KEY] =
                static::createCurrencyObject($basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE_KEY]);

            $arrProduct[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] =
                static::createCurrencyObject($basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY]);

        } else {

            /**
             * Product is not a legacy broadband account type without subscription components and does not have a
             * single service component/tariff we can use to get it's price from Billing API.
             *
             * Set the price of current broadband to 0 as we cannot currently charge for it in RBM.
             */
            $arrProduct[self::CURRENT_BASE_PRICE_KEY] = static::createCurrencyObject(0);
            $arrProduct[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] = static::createCurrencyObject(0);
        }

        return $arrProduct;
    }

    /**
     * Override getApplicationStateVar so we can capture the new service definition id.
     *
     * @param string $strCallingClass The calling class
     * @param string $strVariableName The name of the variable we are after
     *
     * @return string | bool
     */
    public function getApplicationStateVar($strCallingClass, $strVariableName)
    {
        if ('intNewSdi' == $strVariableName) {
            if (!$this->isApplicationStateVar($strCallingClass, $strVariableName)) {
                $strVariableName = 'intOldSdi';
            }
        }

        if (!$this->isApplicationStateVar($strCallingClass, $strVariableName)) {
            return false;
        }

        return parent::getApplicationStateVar($strCallingClass, $strVariableName);
    }

    /**
     * Collect the Min and Max speed ranges after cap.
     *
     * @param Core_Service     $objCoreService         Core service instance
     * @param LineCheck_Result $objLineCheckResult     Line check result instance
     * @param integer          $intServiceDefinitionId Service definition id
     *
     * @return array
     */
    public static function getMinAndMaxSpeedRanges(
        Core_Service $objCoreService,
        $objLineCheckResult,
        $intServiceDefinitionId = null
    ) {
        // If we're not using MGALS or MGS, work out the ranges..
        $objAccountChangeDatabase = Db_Manager::getAdaptor('AccountChange');
        $intServiceId = $objCoreService->getServiceId();

        if ($intServiceDefinitionId === null) {
            $objCoreDatabase = Db_Manager::getAdaptor('Core');
            $product = $objCoreDatabase->getServiceDefinitionDetailsForService($intServiceId);
            $intServiceDefinitionId = $product['service_definition_id'];
        } else {
            $product = $objAccountChangeDatabase->getServiceDefinitionDetails($intServiceDefinitionId);
            $product['service_definition_id'] = $intServiceDefinitionId;
        }

        $supplierProduct = static::getSupplierProductFromSupplierRules(
            $objLineCheckResult,
            $product['service_definition_id']
        );
        $accessTechnology = $supplierProduct->getAccessTechnology()->getValue();

        $downloadSpeedCap = self::MAX_DOWN_SPEED;
        $uploadSpeedCap = self::MAX_UP_SPEED;

        if ($accessTechnology == 'FTTC') {
            $provisionedService = $objAccountChangeDatabase->getProvisionedServiceDetailsForService($intServiceId);

            $downloadSpeedCap = self::MAX_FIBRE_CAPPED_DOWN_SPEED;
            $uploadSpeedCap = self::MAX_FIBRE_CAPPED_UP_SPEED;

            if (null !== $provisionedService['intInternalMaxUpstream']) {
                $uploadSpeedCap = self::MAX_FIBRE_UP_SPEED;
            }

            if (($objCoreService->isPlusnetUser() &&
                    (strpos($product['name'], 'Extra') !== false || strpos($product['name'], 'Business') !== false)) ||
                $objCoreService->isPartnerUser() ||
                (($objCoreService->isJlpUser() || $objCoreService->isJohnLewisUser())
                    && (strpos($product['name'], 'Extra') !== false))
            ) {
                $downloadSpeedCap = self::MAX_FIBRE_DOWN_SPEED;
                $uploadSpeedCap = self::MAX_FIBRE_UP_SPEED;
            }
        } elseif ($accessTechnology == 'ADSL2+') {
            $downloadSpeedCap = self::MAX_ADSL2_DOWN_SPEED;
            $uploadSpeedCap = self::MAX_ADSL2_UP_SPEED;
        } elseif ($accessTechnology == 'ADSL') {
            $downloadSpeedCap = self::MAX_ADSL_DOWN_SPEED;
            $uploadSpeedCap = self::MAX_ADSL_UP_SPEED;
        }

        $productData = static::getProductDataFromSupplierProductRules(
            $objLineCheckResult,
            $product['service_definition_id']
        );

        if ($productData['downloadSpeedRangeMax'] > $downloadSpeedCap / 1000) {
            $productData['downloadSpeedRangeMax'] = $downloadSpeedCap / 1000;
        }

        if ($productData['downloadSpeedRangeMin'] > $downloadSpeedCap / 1000) {
            $productData['downloadSpeedRangeMin'] = $downloadSpeedCap / 1000;
        }

        if ($productData['downloadSpeedRangeMin'] == $productData['downloadSpeedRangeMax']) {
            if (!empty($productData['downloadSpeedRangeMax']) && $productData['downloadSpeedRangeMax'] >= 2) {
                $productData['downloadSpeedRangeMin'] = $productData['downloadSpeedRangeMax'] - 2;
            } else {
                $productData['downloadSpeedRangeMin'] = null;
            }
        }

        if (is_numeric($productData['downloadSpeedRangeMax'])) {
            $productData['downloadSpeedRangeMax'] = number_format(floor($productData['downloadSpeedRangeMax']), 0);
        }

        if (is_numeric($productData['downloadSpeedRangeMin'])) {
            $productData['downloadSpeedRangeMin'] = number_format(floor($productData['downloadSpeedRangeMin']), 0);
        }

        $objMGALS = new ProductComponent_MGALS();

        $ofcomLineCheckHelper = new AccountChange_LineCheckOfcomHelper();

        $productData = $objMGALS->getAdjustedSpeedsWithMGS(
            $objLineCheckResult,
            $intServiceDefinitionId,
            $accessTechnology,
            $productData
        );

        $productData['uploadSpeedRangeMgsFormatted'] = $ofcomLineCheckHelper->presentSpeedsInRangeOrPart(
            $productData['uploadSpeedRangeMin'],
            $productData['uploadSpeedRangeMax']
        );

        $productData['downloadSpeedRangeMgsFormatted'] = $ofcomLineCheckHelper->presentSpeedsInRangeOrPart(
            $productData['downloadSpeedRangeMin'],
            $productData['downloadSpeedRangeMax']
        );

        $productData['isPartner'] = $objCoreService->isPartnerUser();

        return $productData;
    }

    /**
     * @param bool $houseMove   Is the change being validated a house move.
     * @return array
     */
    public function getValidationPolicies($houseMove)
    {
        return array_filter(AccountChange_ValidationCheck::getDefaultPolicies(), function($policyClassName) use ($houseMove) {
            // If we're doing a house move, exclude the house move validation class..
            if ($houseMove && $policyClassName === AccountChange_HouseMovePolicy::class) {

                return false;
            }

            return true;
        });
    }

    /**
     * Wrapper so to get the ValidationChecks object.
     *
     * @param Auth_BusinessActor $actor                 The business actor we want to work with
     * @param bool               $houseMove             Is this change due to a house move
     * @param array              $additionalInformation Additional information to make available to the validators
     *
     * @return AccountChange_ValidationCheck
     */
    protected function getValidationCheck($actor, $houseMove, $additionalInformation, $isWorkplace)
    {
        return new AccountChange_ValidationCheck(
            $actor,
            $this->getValidationPolicies($houseMove),
            $isWorkplace,
            false,
            $additionalInformation
        );
    }

    /**
     * Method to find account id from service id.
     *
     * @param integer $intServiceId       Service id
     * @param string  $strTransactionName Name of the transaction to use
     *
     * @return integer
     */
    protected function getAccountIdFromServiceId(
        $intServiceId,
        $strTransactionName = Db_Manager::DEFAULT_TRANSACTION
    ) {
        $objService = new Core_Service($intServiceId, $strTransactionName);
        $intAccountId = Core_Account::getAccountIdByUserId(
            $objService->getUserId(),
            $strTransactionName
        );

        return $intAccountId;
    }

    /**
     * Method to get the broadband product variant details,
     *
     * @param array   $broadbandProductVariants Array of broadband product variants
     * @param integer $currentProductSdi        Current product service definition id
     * @param integer $marketId                 Market id
     *
     * @return array
     */
    protected static function getBroadbandProductVariantDetails(
        $broadbandProductVariants,
        $currentProductSdi,
        $marketId
    ) {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
        $variantDetails = array();

        if (is_array($broadbandProductVariants)) {
            foreach ($broadbandProductVariants as $sdi) {
                if ($sdi != $currentProductSdi) {
                    $variantDetails = $dbAdaptor->getProductDetailsByServiceDefinitionIdAndMarket($sdi, $marketId);
                }
            }
        }

        return $variantDetails;
    }

    /**
     * Arranges available products based on current product, to make things easier for BSC.
     *
     * @param array $arrProducts                 Array of available products
     * @param array $arrServiceDefinitionDetails Current product details
     *
     * @return array $arrProducts
     */
    public static function arrangeBoSProducts($arrProducts, $arrServiceDefinitionDetails)
    {
        foreach ($arrProducts as $key => $product) {
            if ($arrServiceDefinitionDetails['service_definition_id'] == $product['intSdi']) {
                // Ignore current sdi
                continue;
            }

            if ($arrServiceDefinitionDetails['strContractLength'] == $product['strContract'] &&
                $arrServiceDefinitionDetails['costLevel'] == $product['costLevel']
            ) {
                // Move to the top of the array
                unset($arrProducts[$key]);
                array_unshift($arrProducts, $product);
            }
        }

        return $arrProducts;
    }

    /**
     * Check if promotion code is valid for any products in array.
     *
     * @param array   $products  Array of available products
     * @param string  $promoCode Promotion code
     * @param integer $oldSdi    Current product service definition id
     *
     * @return array
     */
    public static function getProductsWithDiscount($products, $promoCode, $oldSdi = null)
    {
        $request = new Mvc_HttpRequest();
        $ip = $request->getSourceIpAddress();
        $adaptor = Db_Manager::getAdaptor('AccountChange');

        foreach ($products as $key => $product) {
            try {
                if ($product['intSdi'] != $oldSdi &&
                    true === Val_ReferralPromoCode::isPromoCodeValidForProductAndIp(
                        new Val_ReferralPromoCode((string)$promoCode),
                        new Val_ConnectivityProduct((int)$product['intSdi']),
                        $ip
                    )
                ) {
                    $presetDiscountId = $adaptor->getInitialPresetDiscountId($promoCode, $product['intSdi']);

                    if (!empty($presetDiscountId)) {
                        $presetDiscount = $adaptor->getPresetDiscountTemplate($presetDiscountId);

                        if (!empty($presetDiscount)) {
                            if (!array_key_exists(self::DISCOUNT_AMOUNT_KEY, $products[$key]) ||
                                empty($products[$key][self::DISCOUNT_AMOUNT_KEY])
                            ) {
                                $presetDiscount['intDiscountTypeId'] = (integer)$presetDiscount['intDiscountTypeId'];

                                /**
                                 * The new cost will be:
                                 * - 'intProductCost'           if coming to this from api
                                 * - 'currentBasePrice'         if coming from member centre account change
                                 * - 'intNewCost'               if coming from workplace account change
                                 */
                                if (isset($products[$key][self::CURRENT_BASE_PRICE_KEY])) {
                                    $newCostKey = self::CURRENT_BASE_PRICE_KEY;

                                } else if (isset($products[$key][self::PRODUCT_COST_KEY])) {
                                    $newCostKey = self::PRODUCT_COST_KEY;

                                } else {
                                    $newCostKey = self::WORKPLACE_NEW_COST_KEY;
                                    // Also add discount information to in contract and new contract prices for workplace
                                    $monthlyPrice           = $products[$key][self::WORKPLACE_MONTHLY_PRICE_KEY];
                                    $inContractMonthlyPrice = $products[$key][self::WORKPLACE_IN_CONTRACT_MONTHLY_PRICE_IN_CONTRACT_KEY];

                                    $products[$key][self::WORKPLACE_MONTHLY_PRICE_KEY] =
                                        AccountChange_DiscountHelper::calculateLeadingPrice(
                                            $monthlyPrice,
                                            $presetDiscount['intDiscountTypeId'],
                                            $presetDiscount[self::DECIMAL_VALUE_KEY]
                                        );

                                    $products[$key][self::WORKPLACE_IN_CONTRACT_MONTHLY_PRICE_IN_CONTRACT_KEY] =
                                        AccountChange_DiscountHelper::calculateLeadingPrice(
                                            $inContractMonthlyPrice,
                                            $presetDiscount['intDiscountTypeId'],
                                            $presetDiscount[self::DECIMAL_VALUE_KEY]
                                        );
                                }

                                $productCost = $products[$key][$newCostKey];

                                $products[$key][$newCostKey] =
                                    AccountChange_DiscountHelper::calculateLeadingPrice(
                                        $productCost,
                                        $presetDiscount['intDiscountTypeId'],
                                        $presetDiscount[self::DECIMAL_VALUE_KEY]
                                    );

                                $products[$key][self::DISCOUNT_AMOUNT_KEY] =
                                    AccountChange_DiscountHelper::calculateDiscountAmount(
                                        $productCost,
                                        $presetDiscount['intDiscountTypeId'],
                                        $presetDiscount[self::DECIMAL_VALUE_KEY]
                                    );
                            }

                            $products[$key]['presetDiscount'] = $presetDiscount;
                        }
                    }
                }
            } catch (Exception $e) {
                continue;
            }
        }

        return $products;
    }

    /**
     * Returns the product family,
     *
     * @param integer $intServiceDefinitionId Service definition id
     *
     * @return ProductFamily_ProductFamily
     */
    public static function getProductFamily($intServiceDefinitionId)
    {
        self::includeLegacyFiles();

        return ProductFamily_Factory::getFamily($intServiceDefinitionId);
    }

    /**
     * Does a given service Id have an active or pending Wlr component?
     *
     * @param integer $serviceId Service id
     *
     * @return boolean
     */
    public static function hasWlr($serviceId)
    {
        self::includeLegacyFiles();

        return (CWlrProduct::getWlrProductFromServiceId($serviceId) !== false);
    }

    /**
     * Perform an address match and return an array of results.
     *
     * @param integer $homeNumber Home number
     * @param string  $postcode   Post code
     *
     * @return array
     */
    public function matchAddress($homeNumber, $postcode)
    {
        $wlr3Manager = $this->getWlr3Manager();

        $params = $wlr3Manager->createParams(Wlr3_Manager::PARAMS_ADDRESS_SEARCH)
            ->setPostcode($postcode);

        if (is_numeric($homeNumber)) {
            $params->setHomeNumber($homeNumber);
        }

        return $wlr3Manager->addressSearch($params)->getAddresses();
    }

    /**
     * Get wlr3 manager object.
     *
     * @return Wlr3_Manager
     */
    protected function getWlr3Manager()
    {
        return Wlr3_Manager::getInstance();
    }

    /**
     * Inclusion of legacy files so we can mock them.
     *
     * @return void
     */
    protected static function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
        require_once '/local/data/mis/database/database_libraries/secure-transaction-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/HomePhone.class.php';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';

        // Used to apply promotion code
        require_once '/local/data/mis/portal_modules/signup/v4/signup_regrade_shared_functions.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/Retention/BroadbandDiscount.class.php';

        // ADSL details
        require_once '/local/data/mis/database/database_libraries/adsl-access.inc';
        require_once '/local/data/mis/common_library_functions/common_application_apis/common-adsl-api.inc';
    }

    /**
     * Method that allow us to properly mock out static call to includeLegacyFiles
     *
     * @return void
     */
    public function getLegacyFiles()
    {
        self::includeLegacyFiles();
    }

    /**
     * Get the activation fee for fibre products based on service definition id,
     *
     * @param integer $serviceDefinitionId Service definition id
     *
     * @return float
     */
    public static function getProductActivationFee($serviceDefinitionId)
    {
        $activationFee = 0.0;

        if (!empty($serviceDefinitionId)) {
            $activationFee = Db_Manager::getAdaptor('AccountChange')->getSetupFee($serviceDefinitionId);
        }

        return $activationFee;
    }

    /**
     * Gets the length of the broadband contract based on service definition id.
     *
     * @param integer $serviceDefinitionId Service definition id
     *
     * @return int
     */
    public static function getProductDefaultContractLength($serviceDefinitionId)
    {
        return Db_Manager::getAdaptor('AccountChange')->getContractDefinitionLengthFromSdi($serviceDefinitionId);
    }

    /**
     * Gets the length of the broadband contract based on service definition id.
     *
     * @param array $arrData Data array
     *
     * @return bool
     */
    public static function freePartnerHardwarePostage($arrData)
    {
        return Db_Manager::getAdaptor('AccountChange')->freePartnerHardwarePostage(
            isset($arrData['objAccountChangeManager']) ?
                $arrData['objAccountChangeManager']->getServiceId() : $arrData['intServiceId'],
            $arrData['intNewSdi']
        );
    }

    /**
     * Checks if the current account is a Bauer account or not based on the family handle.
     *
     * @param string $familyHandle Account family handle
     *
     * @return boolean
     */
    public static function checkIfBauer($familyHandle)
    {
        switch ($familyHandle) {
            case 'TALK24_DUAL':
                $isBauer = true;
                break;
            case 'TALK24_SOLUS':
                $isBauer = true;
                break;
            case 'YOURCALLS_DUAL':
                $isBauer = true;
                break;
            case 'YOURCALLS_SOLUS':
                $isBauer = true;
                break;
            default:
                $isBauer = false;
                break;
        }

        return $isBauer;
    }

    /**
     * Creates an object of financial additional carge helper.
     *
     * @return Financial_AdditionalChargeHelper
     */
    public static function getAdditionalChargeHelper()
    {
        if (is_null(self::$additionalChargeHelper)) {
            self::$additionalChargeHelper = new Financial_AdditionalChargeHelper();
        }

        return self::$additionalChargeHelper;
    }

    /**
     * Setter for additional charge helper.
     *
     * @param Financial_AdditionalChargeHelper $additionalChargeHelper Additional charge helper
     *
     * @return void
     */
    public static function setAdditionalChargeHelper(Financial_AdditionalChargeHelper $additionalChargeHelper)
    {
        self::$additionalChargeHelper = $additionalChargeHelper;
    }

    /**
     * Wrapper function for calling the legacy code.
     *
     * @param integer $serviceId Service id
     *
     * @return array
     */
    protected function getProvisionedService($serviceId)
    {
        $this->getLegacyFiles();

        return adslGetProvisionedService($serviceId);
    }

    /**
     * Gets the type provisioned on for the service.
     *
     * @param integer $intServiceId Service Id
     *
     * @return string
     */
    protected function getProvisionedOn($intServiceId)
    {
        $strResult = 'Adsl';

        if ($this->isProvisionedOnAdsl2($intServiceId)) {
            $provisionedService = $this->getProvisionedService($intServiceId);

            if (isset($provisionedService['vchProductCode']) && $provisionedService['vchProductCode'] == 'FTTC') {
                $strResult = $provisionedService['vchProductCode'];
            } else {
                $strResult = 'Adsl2';
            }
        }

        return $strResult;
    }

    /**
     * Determine whether or not the currently provisioned platform is ADSL or ADSL2.
     *
     * @param integer $serviceId Service id
     *
     * @return boolean
     */
    protected function isProvisionedOnAdsl2($serviceId)
    {
        $currentPlatform = $this->getProvisionedService($serviceId);

        if (isset($currentPlatform['vchSupplierPlatform']) && 'BT21CN' == $currentPlatform['vchSupplierPlatform']) {
            return true;
        }

        return false;
    }

    /**
     * Getter method for Customer required date
     *
     * @return string
     */
    public static function getCrd()
    {
        return self::$crd;
    }

    /**
     * Setter method for Customer required date
     *
     * @param string $crd Customer required date
     *
     * @return null
     */
    public static function setCrd($crd)
    {
        self::$crd = $crd;
    }

    /**
     * Get scheduled change date.
     *
     * @param integer $intServiceId Service id
     *
     * @return string
     */
    public function getScheduledChangeDate($intServiceId)
    {
        return UserdataServiceGetScheduledChangeDate($intServiceId);
    }

    /**
     * Helper to check if new billing is switched on
     *
     * @param string $toggleName   Name of the toggle state required
     * @param int    $intServiceId Service ID
     *
     * @return bool
     */
    protected function isNewBillingEngineOn($toggleName, $intServiceId)
    {
        return FeatureToggleManager::isOnFiltered($toggleName, null, null, null, $intServiceId);
    }

    /**
     * @return \Plusnet\BillingApiClient\Facade\BillingApiFacade
     */
    protected function getBillingApiClient()
    {
        return \Plusnet\BillingApiClient\Service\ServiceManager::getService('BillingApiFacade');
    }

    /**
     * If this is an account change as part of a house move, checks whether a phone is expected at the new address
     *
     * @param integer $intServiceId Service id
     *
     * @return boolean
     */
    protected function expectingPhoneForHouseMove($intServiceId)
    {
        /** @var \Plusnet\HouseMoves\Services\HouseMoveService $houseMoveService */
        $houseMoveService = ServiceManager::getService('HouseMoveService');
        $houseMove = $houseMoveService->getHouseMoveByServiceId($intServiceId);

        $siOrderReference = $houseMove->getSiOrderReference();

        if ($siOrderReference === '0') {
            return false;
        }

        return true;
    }

    /**
     * Get current broadband start date
     *
     * @param int $serviceId Service id
     *
     * @return String
     */
    public function getCurrentBroadbandStartDate($serviceId)
    {
        $this->getLegacyFiles();
        $activationDate = null;
        $adslApplicationDetails = get_adsl_application_details(ADSLGetInstallDiaryServiceId($serviceId), true);
        if (is_array($adslApplicationDetails) && isset($adslApplicationDetails['date_active'])) {
            $activationDate = $adslApplicationDetails['date_active'];
        }

        return $activationDate;
    }

    /**
     * Stubbable call to get the StatusServiceFactory.
     *
     * @return \Plusnet\PriceProtected\Services\StatusService
     */
    public function getStatusService()
    {
        return StatusServiceFactory::createService();
    }

    /**
     * Is the user a residential portal user with a residential service definition?
     * Makes it easier for mocking the user types in unit tests
     *
     * Auth_BusinessActor could be either the object or false because of where
     * it is set in $this->>getCurrentBusinessActor()
     *
     * @param null $objServiceDefinition    Core_ServiceDefinition
     * @param null $objCoreService          Core_Service
     * @param null $objBusinessActor        Auth_BusinessActor or boolean false
     * @return bool
     */
    protected function isResidentialPortalUserWithResidentialServiceDefinition(Core_ServiceDefinition $objServiceDefinition = null,
                                                                               Core_Service $objCoreService = null,
                                                                               $objBusinessActor = null)
    {
        return ($objServiceDefinition && $objServiceDefinition->isResidential()
              && (!$objCoreService->isJohnLewisUser())
              && ($objCoreService->getIsp() === 'plus.net')
              && ($objBusinessActor && $objBusinessActor->getUserType() != 'PLUSNET_STAFF'));

    }

    /**
     * Is the user a workplace user with residential service definition?
     * Makes it easier for mocking the user types in unit tests
     *
     * Auth_BusinessActor could be either the object or false because of where
     * it is set in $this->>getCurrentBusinessActor()
     *
     * @param null $objServiceDefinition    Core_ServiceDefinition
     * @param null $objCoreService          Core_Service
     * @param null $objBusinessActor        Auth_BusinessActor or boolean false
     * @return bool
     */
    protected function isResidentialWorkplaceUserWithResidentialServiceDefinition(Core_ServiceDefinition $objServiceDefinition = null,
                                                                                  Core_Service $objCoreService = null,
                                                                                  $objBusinessActor = null)
    {
        if (($objServiceDefinition->isResidential() &&
            (!$objCoreService->isJohnLewisUser()) &&
            ($objBusinessActor->getUserType() == 'PLUSNET_STAFF')
        )) {

            return true;

        } else {

            return false;

        }
    }

    /**
     * @param $promoCode
     * @param $promotion
     * @param $arrData
     *
     * @return mixed
     */
    private function handlePromoCode(
      $promoCode,
      $promotion,
      $arrData
    ) {
        /* For C2F/C2M promotions */
        unset($_SESSION['BTLPromotion']);
        if (!empty($promotion)) {
            // Promotion validation depends on whether the promo code is through the line or submitted via the url.
            $promotion = AccountChange_C2mPromotionsHelper::getActivePromotionWithPromocode(
              $promotion
            );

            AccountChange_Registry::instance()->setEntry(
                'userSubmittedPromoCode',
                $promotion
            );

            if (!empty($promotion)) {
                $_SESSION['BTLPromotion'] = $promotion;
                $arrData['promoCode'] = $promoCode;
                $arrData[self::C2M_PROMOTION_KEY] = $promotion;
            } else {
                $arrData['promoCodeInvalid'] = true;
            }
        }

        /* For Legacy Workplace Component Tool promotions */
        if (!empty($promoCode)) {

            if (is_object($promoCode) && $promoCode instanceof Val_ReferralPromoCode) {
                $arrData['promoCode'] = $promoCode;
            } else {
                $arrData['promoCodeInvalid'] = true;
            }

        }

        return $arrData;
    }

    /**
     * Wrapper around Core_Service (For unit tests)
     *
     * @param int $intServiceId Service Id
     *
     * @return Core_Service
     * @throws Core_Exception
     * @codeCoverageIgnore
     */
    protected function getCoreService($intServiceId)
    {
        return new Core_Service($intServiceId);
    }

    /**
     * Wrapper around Core_ServiceDefinition (for unit tests)
     *
     * @param int $intServiceDefinitionId Service Definition Id
     *
     * @return Core_ServiceDefinition
     * @codeCoverageIgnore
     */
    protected function getCoreServiceDefinition($intServiceDefinitionId)
    {
        return new Core_ServiceDefinition($intServiceDefinitionId);
    }

    /**
     * Get a pricing date identified by a name, in a specified format - found in Framework/milestoneDates.json
     *
     * @param string $name   Name that identifies which date in the json file to return
     * @param string $format Format to return the date in - defaults to 'jS F Y'
     *
     * @return string|null
     */
    protected function getPricingDate($name, $format = 'jS F Y')
    {
        $pricingDate = null;

        if (file_exists(self::MILESTONE_DATE_JSON_LOCATION))
        {
            $pricingDates = json_decode(file_get_contents(self::MILESTONE_DATE_JSON_LOCATION), true);
            if (isset($pricingDates[$name]))
            {
                $pricingDateByName = new DateTime($pricingDates[$name]);
                $pricingDate = $pricingDateByName->format($format);
            }
        }

        return $pricingDate;
    }

    /**
     * Wrapper around AccountChange_C2mPriceHelper (For unit tests)
     *
     * @param string $sectorname
     * @param string $sectorname
     *
     * @codeCoverageIgnore
     */
    protected function getPriceIncreasePercent($sectorname, $year)
    {
        return AccountChange_C2mPriceHelper::getPriceIncreasePercent($sectorname, $year);
    }

    /**
     * Checks to see if the customer's journey is eligible for PEGA interaction
     *
     * @param int    $impressionOfferId
     * @param bool   $isResUser
     * @param string $promotionCode
     * @param string $campaignCode
     * @return bool
     */
    protected function isEligibleForPEGAInteraction($impressionOfferId, $isResUser, $promotionCode, $campaignCode)
    {
        return $impressionOfferId !== null && $isResUser && !empty($promotionCode) && $campaignCode === 'Retention';
    }

    /**
     * Checks the business actor to check if the user is a customer.
     *
     * @param Auth_BusinessActor $actor
     * @return bool
     */
    private function isCustomerUser(Auth_BusinessActor $actor)
    {
        return 'PLUSNET_STAFF' !== $actor->getUserType();
    }

    /**
     * Compares the provisioned profile's and speeds on the existing product to see if they are th same technology type
     *
     * @param array $arrProduct array of product data
     * @param string $currentMaxSpeed max speed for existing product
     * @param string $currentUploadSpeed max upload speed for existing product
     * @param string $currentProfile current provisioning profile
     * @return bool
     */
    private static function isProductTheSameProfileAsExisting($arrProduct,$currentMaxSpeed,$currentUploadSpeed,$currentProfile)
    {
        return $arrProduct['provisioningProfile'] === $currentProfile &&
            $arrProduct['intMaximumSpeed'] === $currentMaxSpeed &&
            ($arrProduct['provisioningProfile'] !== 'FTTC' ||
                $arrProduct['intMaxUploadSpeed'] === $currentUploadSpeed);
    }

    /**
     * @param int      $intServiceId     customer service id
     * @param int|null $contractDuration contract duration
     * @return AccountChange_HouseMovePostScheduledChangeHelper
     */
    protected function getHouseMovePostScheduledChangeHelper($intServiceId, $contractDuration)
    {
        return new AccountChange_HouseMovePostScheduledChangeHelper(
            $intServiceId,
            $contractDuration
        );
    }
}
