Account Change
==============

This application is deployed to the following locations:

* Workplace (All visps, excluding metronet)
* Plusnet Portal

All account changes should be scheduled for the customer's next invoice, although there is an option
to allow end users to perform instance account changes (workplace only), but this is discouraged.

Areas for improvement
=====================

Documenting this here, so developers hopefully read this everytime they work with Account Change.

* Controller::getBroadbandProducts and Controller::getProducts could do with some refactoring
** We should have an interface for ProductFilter each class knowing about one specific filter
** The calling method should then pass in which filters they want
** Filters I can think of: Contract, SignUpable, Fttc

* Code duplication for getOneOffCharges in TermsAndConditions and generateInvoiceItems in Controller

* Controller is too heavy, it really should not have that much code in it

* The Product/* classes should be able to take "behaviours" on what should actually happen in certain
  scenarios in account change. For example, raising a ticket if FTTC is selected in the portal. This
  should be a new behaviour, not "yet another function" in Product_ServiceDefinition

* Product/* classes are too heavy

* We call the database more times than we really need too
