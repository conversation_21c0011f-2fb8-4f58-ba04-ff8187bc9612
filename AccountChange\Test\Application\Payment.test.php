<?php
/**
 * Payment Requirement Test
 *
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-07-19
 */
/**
 * AccountChange_Payment_Test class
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class AccountChange_Payment_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Checks for the arrInput variables for the requirement
     *
     */
    public function testArrInputIsSetupCorrectly()
    {
        $arrInputs = array(
            'validationHash' => 'external:string',
            'paymentHandoverId' => 'external:string'
        );

        $req = new AccountChange_Payment();

        $this->assertAttributeEquals($arrInputs, 'arrInputs', $req);
    }

    /**
     * Test if describe returns the necessary information
     *
     * @covers AccountChange_Payment::describe
     */
    public function testDescribeReturnsCorrectInformation()
    {
        $validationHash = 'facfb055eda7796e05e00be89bcc29ac';
        $paymentHandoverId = 123;

        $validatedData = array(
            'paymentSuccess'    => true,
            'paymentHandoverId' => $paymentHandoverId,
            'validationHash'    => $validationHash
        );

        $expectedReturn = array(
            'paymentHandoverId' => $paymentHandoverId,
            'validationHash'    => $validationHash,
            'redirected'        => '0',
            'paymentSuccess'    => true
        );

        $req = new AccountChange_Payment();

        $result = $req->describe($validatedData);
        $this->assertEquals($expectedReturn, $result);
    }

    /**
     * Unit test for the validation of GImP payment handover data
     *
     * @covers AccountChange_Payment::valPaymentHandoverIdAndHash
     */
    public function testValPaymentHandoverIdAndHash()
    {
        $validationHash = 'facfb055eda7796e05e00be89bcc29ac';
        $paymentHandoverId = 123;
        $paymentSuccess = true;
        $expected = array(
            'paymentHandoverId' => $paymentHandoverId,
            'validationHash'    => $validationHash,
            'paymentSuccess'    => $paymentSuccess
        );

        $paymentResponseData = $this->getMock(
            'GenericImmediatePaymentApplication_PaymentResponseData',
            array('isSuccessful'),
            array($paymentHandoverId, $validationHash)
        );

        $paymentResponseData->expects($this->once())
                            ->method('isSuccessful')
                            ->will($this->returnValue($paymentSuccess));

        $req = $this->getMock(
            'AccountChange_Payment',
            array('getPaymentResponseData'),
            array(), '', false
        );

        $req->expects($this->once())
            ->method("getPaymentResponseData")
            ->with($this->equalTo($paymentHandoverId), $this->equalTo($validationHash))
            ->will($this->returnValue($paymentResponseData));

        $result = $req->valPaymentHandoverIdAndHash($paymentHandoverId, $validationHash);

        $this->assertEquals($result, $expected);
    }
}