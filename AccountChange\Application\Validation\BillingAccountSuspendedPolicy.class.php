<?php

/**
 * <AUTHOR>
 */

use Plusnet\BillingApiClient\Service\ServiceManager as BillingServiceManager;
use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\BillingApiClient\Exceptions\BillingApiClientQueueException;

class AccountChange_BillingAccountSuspendedPolicy extends AccountChange_AbstractValidationPolicy
{
    const MC_ERROR_MESSAGE =
        'There\'s currently an issue with your account, meaning you can\'t make any changes to your services. Give us a call on 0800 432 0200 and we\'ll see if we can help.';
    const WP_ERROR_MESSAGE = 'Account is in bill suspend state, product change is not allowed.';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_BILLING_ACCOUNT_SUSPENDED';

    /**
     * @return bool
     * @throws BillingApiClientBillingServiceException
     * @throws BillingApiClientQueueException
     */
    public function validate()
    {
        $billingApi = BillingServiceManager::getService("BillingApiFacade");

        return !$billingApi->isBillingAccountSuspended($this->actor->getExternalUserId());
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return ($this->isWorkplace || $this->actor->getUserType() === static::STAFF_USER_TYPE)
            ? self::WP_ERROR_MESSAGE
            : self::MC_ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
