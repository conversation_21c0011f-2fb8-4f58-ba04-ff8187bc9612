<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_BroadbandOnlyHelperTest extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::restoreAdaptor('AccountChange');
        $registry = AccountChange_Registry::instance();
        $registry->reset();
    }

    /**
     * @test
     * @return void
     **/
    public function shouldCallDbAndPopulateCacheForBBOProduct()
    {
        $registry = AccountChange_Registry::instance();
        $registry->reset();

        $sdi = 6666;

        $dbMock = Mockery::mock(Db_Adaptor::class);

        $dbMock
            ->shouldReceive('isProductType')
            ->once()
            ->with([AccountChange_BroadbandOnlyHelper::TECHNOLOGY_TYPE_FTTP,
                AccountChange_BroadbandOnlyHelper::TECHNOLOGY_TYPE_SOGEA], $sdi)
            ->andR<PERSON>urn(true);

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $helper = new AccountChange_BroadbandOnlyHelper();
        $result = $helper->isBroadbandOnlyProduct($sdi);
        $this->assertTrue($result);

        $cache = $registry->getEntry('isBroadbandOnlyCache');
        $this->assertEquals([$sdi => true], $cache);
    }

    /**
     * @test
     * @return void
     **/
    public function shouldCallDbAndPopulateCacheForMultipleBBOProducts()
    {
        $sdi1 = 6666;
        $sdi2 = 6667;

        $dbMock = Mockery::mock(Db_Adaptor::class);

        $dbMock
            ->shouldReceive('isProductType')
            ->twice()
            ->andReturn(true);

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $helper = new AccountChange_BroadbandOnlyHelper();
        $helper->isBroadbandOnlyProduct($sdi1);
        $helper->isBroadbandOnlyProduct($sdi2);

        $registry = AccountChange_Registry::instance();
        $cache = $registry->getEntry('isBroadbandOnlyCache');
        $this->assertEquals([$sdi1 => true, $sdi2 => true], $cache);
    }

    /**
     * @test
     * @return void
     **/
    public function shouldCallDbAndPopulateCacheWhenSdiIsNotBBO()
    {
        $sdi = 6667;

        $dbMock = Mockery::mock(Db_Adaptor::class);

        $dbMock
            ->shouldReceive('isProductType')
            ->once()
            ->with([AccountChange_BroadbandOnlyHelper::TECHNOLOGY_TYPE_FTTP,
                AccountChange_BroadbandOnlyHelper::TECHNOLOGY_TYPE_SOGEA], $sdi)
            ->andReturn(false);

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $helper = new AccountChange_BroadbandOnlyHelper();
        $result = $helper->isBroadbandOnlyProduct($sdi);
        $this->assertFalse($result);

        $registry = AccountChange_Registry::instance();
        $cache = $registry->getEntry('isBroadbandOnlyCache');
        $this->assertEquals([$sdi => false], $cache);
    }

    /**
     * @test
     * @return void
     **/
    public function shouldReturnValuesFromCacheWithoutCallingDb()
    {
        $cache = array();
        $cache[6891] = true;
        $cache[6892] = false;

        $helper = new AccountChange_BroadbandOnlyHelper();
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isBroadbandOnlyCache', $cache);

        $result1 = $helper->isBroadbandOnlyProduct(6891);
        $result2 = $helper->isBroadbandOnlyProduct(6892);

        $this->assertTrue($result1);
        $this->assertFalse($result2);
    }

    /**
     * @test
     * @return void
     **/
    public function shouldReturnValuesFromCacheWithoutCallingDbForFttp()
    {
        $cache = array();
        $cache[6891] = true;
        $cache[6892] = false;

        $helper = new AccountChange_BroadbandOnlyHelper();
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isFttpCache', $cache);

        $result1 = $helper->isFttpProduct(6891);
        $result2 = $helper->isFttpProduct(6892);

        $this->assertTrue($result1);
        $this->assertFalse($result2);
    }

    /**
     * @test
     * @return void
     **/
    public function shouldReturnValuesFromCacheWithoutCallingDbForSogea()
    {
        $cache = array();
        $cache[6891] = true;
        $cache[6892] = false;

        $helper = new AccountChange_BroadbandOnlyHelper();
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isSogeaCache', $cache);

        $result1 = $helper->isSogeaProduct(6891);
        $result2 = $helper->isSogeaProduct(6892);

        $this->assertTrue($result1);
        $this->assertFalse($result2);
    }

    /**
     * @test
     * @dataProvider scheduledChangeProvider
     * @param $queryReturnData
     * @return void
     * @throws Db_TransactionException
     */
    public function shouldDetermineIfServiceIdHasPendingChangeToFttp($queryReturnData, $expectedResult)
    {
        $serviceID = 1234;
        $dbMock = Mockery::mock(Db_Adaptor::class);
        $cache = array();
        $cache[9999] = true;
        $cache[6666] = false;

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isFttpCache', $cache);

        $dbMock
            ->shouldReceive('getAdslScheduledChange')
            ->once()
            ->with($serviceID)
            ->andReturn($queryReturnData);

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $test = new AccountChange_BroadbandOnlyHelper();
        $result = $test->serviceHasPendingChangeToFttp($serviceID);

        $this->assertEquals($expectedResult, $result);

    }

    /**
     * @test
     * @dataProvider scheduledChangeProviderBBO
     * @param $queryReturnData
     * @return void
     * @throws Db_TransactionException
     */
    public function shouldDetermineIfServiceHasPendingChangeToBBO($queryReturnData, $expectedResult)
    {
        $serviceID = 1234;
        $dbMock = Mockery::mock(Db_Adaptor::class);
        $cache = array();
        $cache[9999] = true;
        $cache[8888] = true;
        $cache[6666] = false;

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isBroadbandOnlyCache', $cache);

        $dbMock
            ->shouldReceive('getAdslScheduledChange')
            ->once()
            ->with($serviceID)
            ->andReturn($queryReturnData);

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $test = new AccountChange_BroadbandOnlyHelper();
        $result = $test->serviceHasPendingChangeToBBO($serviceID);

        $this->assertEquals($expectedResult, $result);

    }

    /**
     * @return array[]
     */
    public function scheduledChangeProvider()
    {
        return [
            'FTTP change' => [['intNewServiceDefinitionId' => 9999],true],
            'Not FTTP change' => [['intNewServiceDefinitionId' => 6666],false],
            'No change' => [[], false]
        ];
    }

    /**
     * @return array[]
     */
    public function scheduledChangeProviderBBO()
    {
        return [
            'FTTP change' => [['intNewServiceDefinitionId' => 9999],true],
            'SoGEA change' => [['intNewServiceDefinitionId' => 8888],true],
            'Not BBO change' => [['intNewServiceDefinitionId' => 6666],false],
            'No change' => [[], false]
        ];
    }

    /**
     * @test
     * @dataProvider fttpServiceTestProvider
     * @return void
     */
    public function shouldDetermineIfServiceHasFttp($serviceDefinition, $expectedResult)
    {
        $serviceID = 1234;
        $dbMock = Mockery::mock(Db_Adaptor::class);
        Db_Manager::setAdaptor('AccountChange', $dbMock);
        $cache = array();
        $cache[9999] = true;
        $cache[6666] = false;

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isFttpCache', $cache);

        /** @var Core_Service|Core_ServiceDao|\Mockery\LegacyMockInterface|\Mockery\MockInterface $test */
        $coreServiceMock = Mockery::mock(Core_Service::class);
        $coreServiceMock->shouldReceive('getServiceDefinitionId')
            ->andReturn($serviceDefinition);

        /** @var AccountChange_BroadbandOnlyHelper|\Mockery\LegacyMockInterface|\Mockery\MockInterface $test */
        $test = Mockery::mock(AccountChange_BroadbandOnlyHelper::class)
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();
        $test->__construct();

        $test
            ->shouldReceive('getCoreService')
            ->once()
            ->with($serviceID)
            ->andReturn($coreServiceMock);

        $result = $test->serviceHasFttp($serviceID);

        $this->assertEquals($expectedResult, $result);
    }

    /**
     * @return array[]
     */
    public function fttpServiceTestProvider()
    {
        return [
            'has FTTP' => [9999,true],
            'does not have FTTP' => [9999,true]
        ];
    }

    /**
     * @test
     * @return void
     */
    public function shouldReturnFalseForServiceHasFttpIfCoreServiceThrowsException()
    {
        $serviceID = 1234;
        /** @var AccountChange_BroadbandOnlyHelper|\Mockery\LegacyMockInterface|\Mockery\MockInterface $test */
        $test = Mockery::mock(AccountChange_BroadbandOnlyHelper::class)
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();
        $test->__construct();

        $test
            ->shouldReceive('getCoreService')
            ->once()
            ->with($serviceID)
            ->andThrows(new Core_Exception('CORE GO BOOM'));

        $result = $test->serviceHasFttp($serviceID);

        $this->assertEquals(false, $result);
    }
}
