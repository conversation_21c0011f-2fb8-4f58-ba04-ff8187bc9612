<?php
/**
 * Payment Method Details Requirement
 *
 * Decide which method of payment is needed
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: PaymentMethodDetails.req.php,v 1.3 2009-02-17 04:41:06 rmerewood Exp $
 * @since     File available since 2008-10-14
 */
/**
 * AccountChange_PaymentMethodDetails class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_PaymentMethodDetails extends Mvc_WizardRequirement
{
    const PAY_BY_DIRECT_DEBIT = 'dd';
    const PAY_BY_CREDIT_CARD  = 'cc';
    const PAY_BY_COMBO        = 'combo';

    /**
     * Key for getting prices from product array
     *
     * @var string
     */
    const NEW_COST_KEY = 'intNewCost';

    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'strPaymentDetailsValid' => 'external:In(no,yes)'
    );

    /**
     * Describe
     *
     * Gather's information needed for the requirement
     *
     * @param array &$arrValidatedApplicationData the validated data
     *
     * @return array
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $arrReturn = array();

        /**
         * See comment in AccountChange_TermsAndConditions why this is here.
         * Without getting the lightweight basket price, we don't have enough information to
         * calculate the new monthly cost for a lightweight journey.
         * The legacy journey only shows the cost of the CHANGES, not the full package the user has.
         */
        if ($_SESSION['totalLightweightBasketPrice']) {
            $arrReturn['intNewMonthlyPayment'] = new I18n_Currency(
                                                    AccountChange_Manager::CURRENCY_UNIT,
                                                    $_SESSION['totalLightweightBasketPrice']
                                                );
        } else {
            $arrReturn['intNewMonthlyPayment'] = $this->UTgetNewMonthlyCost($arrValidatedApplicationData);
        }

        $arrReturn['uxtNewMonthlyPaymentDate'] = $this->getNewMonthlyPaymentDate($arrValidatedApplicationData);
        if (isset($arrValidatedApplicationData['strPaymentDetailsValid'])) {
            $arrReturn['strPaymentDetailsValid'] = $arrValidatedApplicationData['strPaymentDetailsValid'];
        }
        return $arrReturn;
    }

    /**
     * Getter for the new monthly cost
     * A wrapper to make it easily unittestable
     *
     * @param array $arrData the array of data
     *
     * @return I18n_Currency
     */
    protected function UTgetNewMonthlyCost(array $arrData)
    {
        return self::getNewMonthlyCost($arrData);
    }

    /**
     * Getter for the new monthly cost
     *
     * @param array $arrData the array of data
     *
     * @return I18n_Currency
     */
    public static function getNewMonthlyCost(array $arrData)
    {
        $arrSelectedProducts = array();

        if(!empty($arrData['promoCodeInvalidated']) && $arrData['promoCodeInvalidated']) {

            if (is_object($arrData['arrSelectedBroadband']['intNewCost']) && is_object($arrData['arrSelectedBroadband']['discountAmount'])) {
                $arrData['arrSelectedBroadband']['intNewCost'] = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $arrData['arrSelectedBroadband']['intNewCost']->toDecimal() + $arrData['arrSelectedBroadband']['discountAmount']->toDecimal()
                );
            }
            if (isset($arrData['arrSelectedWlr']) && isset($arrData['arrSelectedWlr']['lineRentalDiscountAmount']) && is_object($arrData['arrSelectedWlr']['intNewCost'])) {
                $arrData['arrSelectedWlr']['intNewCost'] = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $arrData['arrSelectedWlr']['intNewCost']->toDecimal() + $arrData['arrSelectedWlr']['lineRentalDiscountAmount']->toDecimal()
                );
            }
        }

        $intNewMonthlyPayment = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);

        if (isset($arrData['arrSelectedBroadband'])) {
            $arrSelectedProducts[] = $arrData['arrSelectedBroadband'];
        }

        if (isset($arrData['arrSelectedWlr'])) {
            $arrSelectedProducts[] = $arrData['arrSelectedWlr'];
        } elseif (!empty($arrData['arrWlrProduct']['intProductCost'])) {
            // If we've not selected a new wlr product, then the total price will still include
            // the line rental and old wlr product (if they have them).
            $intNewMonthlyPayment = $arrData['arrWlrProduct']['intProductCost'];
        }

        foreach ($arrSelectedProducts as $arrSelectedProduct) {

            if ($arrSelectedProduct[self::NEW_COST_KEY] instanceof I18n_Currency) {
                $objCost = $arrSelectedProduct[self::NEW_COST_KEY];
            } else {
                $objCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
            }

            if ($objCost instanceof I18n_Currency) {
                $intNewMonthlyPayment = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $objCost->toDecimal() +
                    $intNewMonthlyPayment->toDecimal()
                );
            }
        }
        return $intNewMonthlyPayment;
    }

    /**
     * Getter for the new monthly payment date
     *
     * @param array $arrData the array of data
     *
     * @return I18n_Date
     */
    protected function getNewMonthlyPaymentDate(array $arrData)
    {
        // Ideally perhaps this should use AccountChange SchedulingHelper to get the date, but that requires
        // a service ID and whether an engineering appointment is required, and I don't know if we know that here.
        $objCoreService = $arrData['objCoreService'];
        // Note - the getNextInvoiceDate/getNextNextInvoiceDate functions claim to return I18n_Date
        // so it's not clear why this function attempts to convert to I18n_Date::fromString. Keeping existing behaviour.
        $nextInvoiceDate = I18n_Date::fromString($objCoreService->getNextInvoiceDate());

        if ($nextInvoiceDate->fShort() === I18n_Date::now()->fShort()) {
            $nextInvoiceDate = I18n_Date::fromString($objCoreService->getNextNextInvoiceDate());
        }

        return $nextInvoiceDate;
    }
}
