<?php
use \Plusnet\C2mApiClient\Entity\Promotion;
use \Plusnet\C2mApiClient\Entity\Discount;
use \Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation;
use \Plusnet\C2mApiClient\Entity\DiscountValue;
use \Plusnet\C2mApiClient\Entity\PaymentFrequency;
use \Plusnet\C2mApiClient\C2MClient;

require_once(__DIR__.'/../../Libraries/PromotionConverter.php');
require_once(__DIR__.'/../../Libraries/C2MPromotionAdapter.php');
require_once(__DIR__.'/../../Libraries/C2MPromotionManager.php');

class C2MPromotionManagerTest extends PHPUnit_Framework_TestCase
{
    /**
     * @var array
     */
    protected $products = [];

    public function setUp()
    {
        $this->products = [
          [
            'productOfferingName' => 'Product 1',
            'intSdi'              => '123',
            'intNewLeadingCost'   => '1',
            'isDual'              => 1
          ],
          [
            'productOfferingName' => 'Product 2',
            'intSdi'              => '1234',
            'intNewLeadingCost'   => '1',
          ],
          [
            'productOfferingName' => 'Product 3',
            'intSdi'              => '12345',
            'intNewLeadingCost',
          ],
          ['intSdi' => '123456'],
        ];
    }

    /**
     * @test
     *
     * @dataProvider product_list_data
     * @param $returnedPromotions
     * @param $expectedProducts
     */
    public function itReturnsOriginalProductListUnalteredIfNoPromotionsExistOnSalesChannelAreProvided(
      $returnedPromotions,
      $expectedProducts
    ) {
        $c2mClientMock = $this->getMockBuilder(C2MClient::class)
          ->getMock();

        $c2mClientMock->expects($this->any())
          ->method('getPromotionsForSalesChannel')
          ->will($this->returnValue([]));

        $mockPromotionRules = $this->getMockBuilder(PromotionRules::class)
          ->getMock();

        $C2mSalesChannelPromotions = $this->getMockBuilder(
          'AccountChange_C2mPromotionsHelper'
        )
          ->disableOriginalConstructor()
          ->setConstructorArgs([$c2mClientMock, $mockPromotionRules, ['mockSalesChannel']])
          ->setMethods(array('getPromotions'))
          ->getMock();


        $C2mSalesChannelPromotions->expects($this->any())
          ->method('getPromotions')
          ->will($this->returnValue($returnedPromotions));


        $AccountChange_Registry = $this->getMock('AccountChange_Registry');


        $C2MPromotionManager = new C2MPromotionManager(
          $C2mSalesChannelPromotions,
          $AccountChange_Registry,
          new C2MPromotionAdapter()
        );


        $products = $C2MPromotionManager->getProductsWithC2mDiscount(
          $this->products,
          'service_id',
          1234,
          'testPromoCode'
        );

        $this->assertEquals(4, $expectedProducts);

    }


    /**
     * @return array
     */
    public function product_list_data()
    {
        return [
          [[], 4],
          [null, 4],
          [false, 4],
          ['', 4],
        ];
    }

    /**
     * @return array
     */
    public function testing_discounts_apply_to_product_data()
    {
        $productOffering1 = new ProductOfferingPaymentInformation('Product 1', '', '');
        $productOffering2 = new ProductOfferingPaymentInformation('Product 101', '', '');

        $discountValue1 = new DiscountValue();
        $discountValue1->setValue(1);
        $discountValue1->setDiscountValueType('FIXED_AMOUNT');
        $discountValue1->setDuration(12);
        $discountValue1->setDurationType(PaymentFrequency::MONTHLY);

        $discount1 = new Discount();
        $discount1->setProductOfferingPaymentInformations(
            [$productOffering1, $productOffering2]
        );
        $discount1->setDiscountValues([$discountValue1]);


        $discountValue2 = new DiscountValue();
        $discountValue2->setValue(50);

        $discount2 = new Discount();
        $discount2->setDiscountValues([$discountValue2]);
        $discount2->setType('CASH_BACK');
        $discount2->setSubType('REWARD_CARD');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount1, $discount2]);
        $promotion->setCode('testPromoCode');
        $promotion->setActiveTo('********');
        $promotion->setActiveFrom('********');

        return [
          [
            [
              'promotions' => [$promotion],
              'products'   => [
                [
                  'productOfferingName' => 'Product 1',
                  'intSdi'              => '123',
                  'currentBasePrice'    => new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT, 19.99
                  ),
                  'isDual'              => 1
                ],
                [
                  'productOfferingName' => 'Broadband',
                  'intSdi'              => '1234',
                  'presetDiscount'      => '1',
                  'currentBasePrice'    => new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT, 19.99
                  ),
                  'currentBasePriceNoDiscount'    => new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT, 19.99
                   ),
                ],
                [
                  'productOfferingName' => 'Product 3',
                  'intSdi'              => '12345',
                  'presetDiscount',
                ],
                [
                  'productOfferingName' => 'Fibre 2 Good',
                  'intSdi'              => '123456',
                ],
              ],
              'intOldSdi'  => '1234',
            ],
            ['results' => []],
          ],
        ];

    }

    /**
     * @test
     *
     * @dataProvider testing_discounts_apply_to_product_data
     * @param $testData
     *
     * @throws ReflectionException
     */
    public function itCorrectlyIdentifiesIfDiscountIsApplicableToProduct($testData)
    {
        $c2mClientMock = $this->getMockBuilder(C2MClient::class)
          ->getMock();

        $c2mClientMock->expects($this->any())
          ->method('getPromotionsForSalesChannel')
          ->will($this->returnValue($testData['promotions']));

        $mockPromotionRules = $this->getMockBuilder(PromotionRules::class)
          ->getMock();

        $C2mSalesChannelPromotions = $this->getMockBuilder(
          'AccountChange_C2mPromotionsHelper'
        )
        ->disableOriginalConstructor()
        ->setConstructorArgs([$c2mClientMock, $mockPromotionRules, ['mockSalesChannel']])
        ->setMethods(array('getPromotions'))
        ->getMock();

        $AccountChange_Registry = $this->getMockBuilder(
          'AccountChange_Registry'
        )->getMock();


        $C2MPromotionManager = new C2MPromotionManager(
          $C2mSalesChannelPromotions,
          $AccountChange_Registry,
          new C2MPromotionAdapter()
        );

        $discount = $testData['promotions'][0]->getDiscounts()[0];
        $shouldBeDiscountable = $this->invokeMethod(
          $C2MPromotionManager,
          'isDiscountApplicableToBroadbandProduct',
          [$discount, $this->products[0]]
        );

        $shouldNotDiscountFirst = $this->invokeMethod(
          $C2MPromotionManager,
          'isDiscountApplicableToBroadbandProduct',
          [$discount, $this->products[1]]
        );

        $shouldNotDiscountSecond = $this->invokeMethod(
          $C2MPromotionManager,
          'isDiscountApplicableToBroadbandProduct',
          [$discount, $this->products[3]]
        );

        $this->assertTrue($shouldBeDiscountable);
        $this->assertFalse($shouldNotDiscountFirst);
        $this->assertFalse($shouldNotDiscountSecond);

    }

    /**
     * @test
     *
     * @dataProvider testing_discounts_apply_to_product_data
     * @param $testData
     */
    public function itAppliesDiscountsToProductsAndDoesntApplyItToInvalidProducts($testData)
    {
        $testSalesChannel = 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign';

        $c2mClientMock = $this->getMockBuilder(C2MClient::class)
          ->getMock();

        $c2mClientMock->expects($this->any())
          ->method('getPromotionsForSalesChannel')
          ->will($this->returnValue($testData['promotions']));

        $mockPromotionRules = $this->getMockBuilder(PromotionRules::class)
          ->getMock();

        $C2mSalesChannelPromotions = $this->getMockBuilder(
          'AccountChange_C2mPromotionsHelper'
        )
            ->disableOriginalConstructor()
            ->setConstructorArgs([$c2mClientMock, $mockPromotionRules, ['mockSalesChannel']])
            ->setMethods(array('getPromotions', 'getSalesChannel'))
            ->getMock();

        $C2mSalesChannelPromotions->expects($this->once())
            ->method('getPromotions')
            ->will($this->returnValue($testData['promotions']));

        $C2mSalesChannelPromotions->expects($this->any())
            ->method('getSalesChannel')
            ->will($this->returnValue($testSalesChannel));

        $AccountChange_Registry = $this->getMock('AccountChange_Registry');

        $C2MPromotionManager = Mockery::mock('C2MPromotionManager', [$C2mSalesChannelPromotions, $AccountChange_Registry, new C2MPromotionAdapter()]);
        $C2MPromotionManager->makePartial();
        $C2MPromotionManager->shouldAllowMockingProtectedMethods();
        $C2MPromotionManager->shouldReceive('canAddDiscount')->andReturn(true);
        $C2MPromotionManager->shouldReceive('userCanHaveDiscount')->andReturn(true);

        $discountedProducts = $C2MPromotionManager->getProductsWithC2mDiscount(
            $testData['products'],
            $testData['oldSdi'],
            1234,
            false // promoCode ends up being passed as 'false' for through the line C2M promotions
        );

        $this->assertArrayHasKey('presetDiscount', $discountedProducts[0]);

        $this->assertArrayHasKey('currentBasePriceNoDiscount', $discountedProducts[0]);

        $this->assertArrayHasKey(
          'discountAmount',
          $discountedProducts[0]
        );
        $this->assertArrayHasKey(
          'presetDiscount',
          $discountedProducts[0]
        );
        $this->assertEquals(
          'testPromoCode',
          $discountedProducts[0]['presetDiscount']['promoCode']
        );
        $this->assertEquals(
            '/legals/PlusnetResidential/AccountChange/NoCampaign/testPromoCode/A',
            $discountedProducts[0]['promoLegals']
        );
        $this->assertEquals(
          12,
          $discountedProducts[0]['presetDiscount']['intDiscountLength']
        );
        $this->assertEquals(
          1,
          $discountedProducts[0]['presetDiscount']['decValue']
        );
        $this->assertEquals(
          AccountChange_DiscountHelper::FIXED_DISCOUNT,
          $discountedProducts[0]['presetDiscount']['promoDiscountType']
        );
        $this->assertEquals(
          1,
          $discountedProducts[0]['presetDiscount']['decValue']
        );
        $this->assertEquals(
            true,
            $discountedProducts[0]['presetDiscount']['includesCashback']
        );
        $this->assertEquals(
            "REWARD_CARD",
            $discountedProducts[0]['presetDiscount']['cashbackType']
        );
        $this->assertEquals(
            50,
            $discountedProducts[0]['presetDiscount']['cashbackValue']
        );

        $this->assertArrayNotHasKey('presetDiscount', $discountedProducts[2]);
        $this->assertArrayNotHasKey(
          'discountedDisplayPrice',
          $discountedProducts[2]
        );

        $this->assertArrayNotHasKey('presetDiscount', $discountedProducts[3]);
        $this->assertArrayNotHasKey(
          'discountedDisplayPrice',
          $discountedProducts[3]
        );

        $this->assertArrayNotHasKey('promoLegals', $discountedProducts[2]);
        $this->assertArrayNotHasKey('promoLegals', $discountedProducts[3]);
    }


    /**
     * Call protected/private method of a class.
     *
     * @param object &$object Instantiated object that we will run method on.
     * @param string $methodName Method name to call
     * @param array $parameters Array of parameters to pass into method.
     *
     * @return mixed Method return.
     * @throws ReflectionException
     */
    public function invokeMethod(&$object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}
