<?php

use Plusnet\InventoryEventClient\Context\AccountChangeContext;

/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_InstantChangeHelperTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 123456;

    /**
     * @var Db_Adaptor|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockDb;

    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockIec;

    /**
     * setUp
     */
    public function setUp()
    {
        $this->mockDb = Mockery::mock('Db_Adaptor');
        Db_Manager::setAdaptor('AccountChange', $this->mockDb);

        $this->mockIec = Mockery::mock('inventoryEventService');
        \BusTier_BusTier::setClient('inventoryEventService', $this->mockIec);
    }

    /**
     * tearDown
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @test
     */
    public function shouldBuildContextWithAndSetPreDateAndMakeSnapshotCalls()
    {
        $broadbandChanging = true;
        $backDatedDate = DateTime::createFromFormat('Y-m-d', '2021-09-27');

        $this->mockIec
            ->shouldReceive('takePreChangeSnapshot')
            ->once()
            ->with(static::SERVICE_ID, \Mockery::on(function ($argument) use ($backDatedDate) {
                $this->assertTrue($argument instanceof AccountChangeContext);
                $this->assertEquals($backDatedDate, $argument->getEffectiveDate());
                $this->assertTrue($argument->getIsPredate());
                $this->assertTrue($argument->getIsBBCRCease());
                return true;
            }));

        $this->mockIec
            ->shouldReceive('takePostChangeSnapshot')
            ->once()
            ->with(static::SERVICE_ID);

        $test = new AccountChange_InstantChangeHelper(
            static::SERVICE_ID,
            $broadbandChanging,
            $backDatedDate
        );

        $test->takePreChangeSnapshot();
        $test->takePostChangeSnapshot();
    }

    /**
     * @test
     */
    public function shouldBuildContextWithBroadbandStartDateAndMakeSnapshotCalls()
    {
        $broadbandChanging = false;
        $backDatedDate = null;

        $this->mockDb
            ->shouldReceive('getCurrentBroadbandStartDate')
            ->once()
            ->with(static::SERVICE_ID)
            ->andReturn('2021-09-27');

        $expectedDate = '2021-09-27';

        $this->mockIec
            ->shouldReceive('takePreChangeSnapshot')
            ->once()
            ->with(static::SERVICE_ID, \Mockery::on(function ($argument) use ($expectedDate) {
                $this->assertTrue($argument instanceof AccountChangeContext);
                $this->assertEquals($expectedDate, $argument->getBroadbandStartDate()->format('Y-m-d'));
                $this->assertFalse($argument->getIsBBCRCease());
                $this->assertFalse($argument->getIsPredate());
                $this->assertEmpty($argument->getEffectiveDate());
                return true;
            }));

        $this->mockIec
            ->shouldReceive('takePostChangeSnapshot')
            ->once()
            ->with(static::SERVICE_ID);

        $test = new AccountChange_InstantChangeHelper(
            static::SERVICE_ID,
            $broadbandChanging,
            $backDatedDate
        );

        $test->takePreChangeSnapshot();
        $test->takePostChangeSnapshot();
    }

    /**
     * @test
     */
    public function shouldBuildContextWithBBCRCeaseTrueAndMakeSnapShotCalls()
    {
        $broadbandChanging = true;

        $this->mockIec
            ->shouldReceive('takePreChangeSnapshot')
            ->once()
            ->with(static::SERVICE_ID, \Mockery::on(function ($argument) {
                $this->assertTrue($argument instanceof AccountChangeContext);
                $this->assertTrue($argument->getIsBBCRCease());
                $this->assertEmpty($argument->getBroadbandStartDate());
                $this->assertEmpty($argument->getEffectiveDate());
                $this->assertFalse($argument->getIsPredate());
                return true;
            }));

        $this->mockIec
            ->shouldReceive('takePostChangeSnapshot')
            ->once()
            ->with(static::SERVICE_ID);

        $test = new AccountChange_InstantChangeHelper(
            static::SERVICE_ID,
            $broadbandChanging
        );

        $test->takePreChangeSnapshot();
        $test->takePostChangeSnapshot();
    }
}
