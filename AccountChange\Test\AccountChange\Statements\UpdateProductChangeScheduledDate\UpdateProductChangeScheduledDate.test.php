<?php
/**
 * AccountChange_UpdateProductChangeScheduledDateTest
 *
 * @category   Test
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://documentation.plus.net/index.php/AccountChange_Module
 */

namespace Plusnet\AccountChange\Test\Statements;

use Db_TransactionException;

require_once '/local/codebase2005/modules/Framework/Test/Plusnet_Database_TestCase.class.php';

/**
 * AccountChange_UpdateProductChangeScheduledDateTest
 *
 * @category   Test
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_UpdateProductChangeScheduledDateTest extends \Plusnet_Database_TestCase
{
    const MODULE_ROOT = __DIR__ . '/../../../..';

    const EXPECTED_DATASET_PATH = __DIR__ . '/expected';

    const EXPECTED_ORIGINAL_CHANGE_DATE = 'originalChangeDate.xml';
    const EXPECTED_UPDATED_CHANGE_DATE = 'updatedChangeDate.xml';

    const TABLE_SERVICE_CHANGE_SCHEDULE = 'userdata.service_change_schedule';

    protected $arrServers = array(
        'Coredb_master' => array()
    );

    protected $arrDataStructureDirectories = array(
        self::MODULE_ROOT . '/Test/datastructure/'
    );

    protected $dataSet = self::MODULE_ROOT . '/Test/dataset/ProductChangeScheduledDateTestData.xml';

    /**
     * Set Up the Tests
     *
     * @throws \Exception
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
    }

    /**
     * Tear Down the Tests
     *
     * @return void
     */
    public function tearDown()
    {
        parent::tearDown();
    }

    /**
     * Test that only one record is updated when more than one record exists for a service id
     *
     * @param string $newChangeDate                      the new change date
     * @param int    $serviceId                          the service id
     * @param int    $scheduleOneId                      the first record's stored id
     * @param string $scheduleOneExpectedChangeDateTable the first record's stored date
     * @param int    $scheduleTwoId                      the second record's stored id
     * @param string $scheduleTwoExpectedChangeDateTable the second record's stored date
     *
     * @dataProvider dataProviderCorrectlyUpdatesChangeDateWhenMoreThanOneRecordExistsForAServiceId
     *
     * @return void
     * @throws Db_TransactionException
     */
    public function testICorrectlyUpdateChangeDateWhenMoreThanOneRecordExistsForAServiceId(
        $newChangeDate,
        $serviceId,
        $scheduleOneId,
        $scheduleOneExpectedChangeDateTable,
        $scheduleTwoId,
        $scheduleTwoExpectedChangeDateTable
    ) {
        \AccountChange_AccountChangeApi::updateProductScheduledChangeDate($newChangeDate, $serviceId);

        $updatedSchedule = $this->createActualQueryTable(self::TABLE_SERVICE_CHANGE_SCHEDULE, $scheduleOneId);
        $unchangedSchedule = $this->createActualQueryTable(self::TABLE_SERVICE_CHANGE_SCHEDULE, $scheduleTwoId);

        self::assertTablesEqual($scheduleOneExpectedChangeDateTable, $updatedSchedule, 'Updated Schedule');
        self::assertTablesEqual($scheduleTwoExpectedChangeDateTable, $unchangedSchedule, 'Unchanged Schedule');
    }

    /**
     * Test that an invalid date isn't sent to the DB
     *
     * @return void
     * @throws Db_TransactionException
     */
    public function testAnInvalidDateIsRejected()
    {
        self::assertFalse(\AccountChange_AccountChangeApi::updateProductScheduledChangeDate('InvalidDate', 1));
    }

    /**
     * Test that an invalid ID isn't sent to the DB
     *
     * @return void
     * @throws Db_TransactionException
     */
    public function testAnInvalidIdIsRejected()
    {
        self::assertFalse(\AccountChange_AccountChangeApi::updateProductScheduledChangeDate('2001-01-01', 'InvalidId'));
    }

    /**
     * Create the dummy db data for testing
     *
     * @return array
     */
    public function dataProviderCorrectlyUpdatesChangeDateWhenMoreThanOneRecordExistsForAServiceId()
    {
        $newChangeDate = '2001-01-02';

        $originalChangeDateTable = $this->createExpectedQueryTable(
            self::TABLE_SERVICE_CHANGE_SCHEDULE,
            self::EXPECTED_ORIGINAL_CHANGE_DATE
        );

        $updatedChangeDateTable = $this->createExpectedQueryTable(
            self::TABLE_SERVICE_CHANGE_SCHEDULE,
            self::EXPECTED_UPDATED_CHANGE_DATE
        );

        return array(
            'Target record has active "yes", change_complete "no"' => array(
                'newChangeDate' => $newChangeDate,
                'serviceId' => 1,
                'scheduleOneId' => 1,
                'scheduleOneExpectedChangeDateTable' => $updatedChangeDateTable,
                'scheduleTwoId' => 2,
                'scheduleTwoExpectedChangeDateTable' => $originalChangeDateTable
            ),
            'Target record has active "yes", change_complete "yes"' => array(
                'newChangeDate' => $newChangeDate,
                'serviceId' => 2,
                'scheduleOneId' => 3,
                'scheduleOneExpectedChangeDateTable' => $originalChangeDateTable,
                'scheduleTwoId' => 4,
                'scheduleTwoExpectedChangeDateTable' => $originalChangeDateTable
            ),
            'Target record has active "no", change_complete "yes"' => array(
                'newChangeDate' => $newChangeDate,
                'serviceId' => 3,
                'scheduleOneId' => 5,
                'scheduleOneExpectedChangeDateTable' => $originalChangeDateTable,
                'scheduleTwoId' => 6,
                'scheduleTwoExpectedChangeDateTable' => $originalChangeDateTable
            ),
            'Target record has active "no", change_complete "no"' => array(
                'newChangeDate' => $newChangeDate,
                'serviceId' => 4,
                'scheduleOneId' => 7,
                'scheduleOneExpectedChangeDateTable' => $originalChangeDateTable,
                'scheduleTwoId' => 8,
                'scheduleTwoExpectedChangeDateTable' => $originalChangeDateTable
            )
        );
    }

    /**
     * Create a dataset for the expected query results
     *
     * @param string $tableName       the name of the table
     * @param string $dataSetFileName the name of the data set
     *
     * @return \PHPUnit_Extensions_Database_DataSet_ITable
     */
    private function createExpectedQueryTable($tableName, $dataSetFileName)
    {
        $dataSet = $this->createXMLDataSet(self::EXPECTED_DATASET_PATH . '/' . $dataSetFileName);

        return $dataSet->getTable($tableName);
    }

    /**
     * Create a dataset with actual query results
     *
     * @param string $tableName  the table name
     * @param int    $scheduleId the schedule id
     *
     * @return \PHPUnit_Extensions_Database_DB_Table
     */
    private function createActualQueryTable($tableName, $scheduleId)
    {
        return $this->getConnection()->createQueryTable(
            $tableName,
            "SELECT change_date FROM userdata.service_change_schedule WHERE schedule_id = $scheduleId"
        );
    }
}
