<?php

require_once '/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php';

class UpdateDiscountsForPriceProtectedCustomers
{
    const LIVE_MODE = "live";
    const TEST_MODE = "test";
    const PROMOTION_CODE_CSV_FILE_NAME = "promotion_codes.csv";

    private static $LONG_OPTION_MODE = array("mode:");

    private $dbAdaptor;
    private $isLiveMode;

    /**
     * Parse command line arguments to check which mode has been requested - if unspecified or invalid then print help
     *
     * @return void
     */
    public function parseArguments()
    {
        $options = getopt(null, self::$LONG_OPTION_MODE);
        if (isset($options['mode']))
        {
            switch ($options['mode'])
            {
                case self::LIVE_MODE:
                    $this->isLiveMode = true;
                    break;
                case self::TEST_MODE:
                    $this->isLiveMode = false;
                    break;
                default:
                    $this->printHelp();
                    exit;
            }
        }
        else
        {
            $this->printHelp();
            exit;
        }
    }

    /**
     * Run the script
     *
     * @return void
     */
    public function run()
    {
        // DEFINE LOG LOCATIONS
        define('GENERAL_LOG', "/tmp/UpdateDiscountsForPriceProtectedCustomers_log_".date("j.n.Y").".txt");
        define('ELIGIBLE_LOG', "/tmp/ELIGIBLE_UpdateDiscountsForPriceProtectedCustomers_log_".date("j.n.Y").".txt");
        define('SUCCESSFUL_LOG', "/tmp/SUCCESS_UpdateDiscountsForPriceProtectedCustomers_log_".date("j.n.Y").".txt");
        echo "Logging to file: ". GENERAL_LOG . "\n";

        // START THE SCRIPT
        $this->log("SCRIPT STARTED AT: ".date("j.n.Y H:i:s"));
        $this->logLineBreak();

        try
        {
            $this->dbAdaptor = Db_Manager::getAdaptor('AccountChange');

            // READ LIST OF PRICE PROTECTED PROMOTION CODES FROM CSV FILE
            $this->log("ATTEMPTING TO: Read list of price protected promotion codes from '" . self::PROMOTION_CODE_CSV_FILE_NAME . "' file.");
            $promotionCodes = $this->readPromotionCodesFromCsv();
            $this->log("RECEIVED: " . json_encode($promotionCodes));
            $this->log("SUCCESS: Successfully read list of price protected promotion codes.");
            $this->logLineBreak();

            // FIND DISCOUNT IDS AND SERVICE IDS LINKED TO PROMOTION CODES
            $this->log("ATTEMPTING TO: Get discount ids and linked service ids for promotion codes.");
            $discountIdsAndServiceIds = $this->getDiscountIdsAndServiceIdsForPriceProtectedPromotionCodes($promotionCodes);
            $this->log("SUCCESS: List of eligible service ids recorded in: '" . ELIGIBLE_LOG . "'.");
            $this->logLineBreak();

            if($this->isLiveMode)
            {
                // INCREMENT DISCOUNT VALUES BY ONE FOR ALL PRICE PROTECTED CUSTOMERS
                $this->log("ATTEMPTING TO: Increment all price protected discount values by 1GBP.");
                $this->log("Successfully price protected customers' service ids recorded in: '" . SUCCESSFUL_LOG ."'.\n");
                $this->incrementDiscountValues($discountIdsAndServiceIds);
                $this->log("\nSUCCESS: All discounts incremented by 1GBP.");
                $this->logLineBreak();
            }
        }
        catch (\Exception $exception)
        {
            $this->log("FAILURE: An unexpected exception occurred - aborting the script. The exception was:");
            $this->log($exception);
        }
        finally
        {
            $this->log("SCRIPT ENDED AT: ".date("j.n.Y H:i:s"));
            exit;
        }
    }

    /**
     * Retrieve the list of price protected promotion codes from the csv and add them to an array
     *
     * @return array
     */
    private function readPromotionCodesFromCsv()
    {
        return array_column(array_map('str_getcsv', file(self::PROMOTION_CODE_CSV_FILE_NAME)), 0);
    }

    /**
     * Find the discount ids and service ids linked to a list of promotion codes
     *
     * @param array $promotionCodes List of price protected promotion codes
     *
     * @return array
     */
    private function getDiscountIdsAndServiceIdsForPriceProtectedPromotionCodes($promotionCodes)
    {
        $discountIdsAndServiceIds = $this->dbAdaptor->getDiscountIdAndServiceIdForPromotionCodes($promotionCodes);
        foreach ($discountIdsAndServiceIds as $discountIdAndServiceId)
        {
            $this->log($discountIdAndServiceId['service_id'], ELIGIBLE_LOG);
        }

        return $discountIdsAndServiceIds;
    }

    /**
     * Increment the discount value of all discounts passed to method by 1GBP.
     * Log the service ids of successfully price protected customers.
     *
     * @param $discountIdsAndServiceIds
     *
     * @return void
     */
    private function incrementDiscountValues($discountIdsAndServiceIds)
    {
        foreach ($discountIdsAndServiceIds as $discountIdAndServiceId)
        {
            $this->log("Incrementing discount with discount id: '" . $discountIdAndServiceId['intDiscountId'] . "'");
            $this->log("                        and service id: '" . $discountIdAndServiceId['service_id'] . "'");
            $this->dbAdaptor->incrementDiscountValueByOneGBP($discountIdAndServiceId['intDiscountId']);

            Db_Manager::commit();
            $this->log($discountIdAndServiceId['service_id'], SUCCESSFUL_LOG);
        }
    }

    /**
     * Adds a message to the log
     *
     * @param string $message Message to add to the log file
     * @param null $logLocation Location of the log - set to the general log by default
     *
     * @return void
     */
    private function log($message, $logLocation = null)
    {
        if(is_null($logLocation))
        {
            $logLocation = GENERAL_LOG;
        }

        file_put_contents($logLocation, $message . "\n", FILE_APPEND);
    }

    /**
     * Prints an asterisk line break to the log file - useful for separating information in the log
     *
     * @param null $logLocation Location of the log - set to the general log by default
     *
     * @return void
     */
    private function logLineBreak($logLocation = null)
    {
        $this->log(
            "\n*******************************************************************************************************\n",
            $logLocation);
    }

    private function printHelp()
    {
        echo "\n\n**************************************************************************************\n";
        echo "-----------------------------------------HELP-----------------------------------------\n";
        echo "**************************************************************************************\n";
        echo "To use this script you must specify a mode using the long option: --mode\n\n";
        echo "To run in test mode use: --mode=test\n";
        echo "To run in live mode use: --mode=live\n";
        echo "**************************************************************************************\n\n";
    }
}

$script = new UpdateDiscountsForPriceProtectedCustomers();
$script->parseArguments();
$script->run();