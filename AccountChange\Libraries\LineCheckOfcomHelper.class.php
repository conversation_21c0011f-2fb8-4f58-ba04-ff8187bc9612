<?php

/**
 * Helper functions that allow us format speed estimates in a consistent way.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 * @link      http://jira.internal.plus.net/browse/PROF-126
 */

/**
 * Helper functions that allow us format speed estimates in a consistent way.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 PlusNet
 * @link      http://jira.internal.plus.net/browse/SALES-3218
 */
class AccountChange_LineCheckOfcomHelper
{

    /**
     * Return a string that can be presented to the customer for the given speed range
     *
     * If the range is not valid (either top or bottom does not exist or is zero), the other valid speed is presented.
     *
     * @param float  $rangeBottom    The bottom end of the speed range
     * @param float  $rangeTop       The top end of the speed range
     * @param string $speedSuffix    The suffix for the speed. Will default to 'Mb'
     * @param string $rangeSeparator The separator for the speed range. Will default to ' - '
     *
     * @return string
     */
    public function presentSpeedsInRangeOrPart($rangeBottom, $rangeTop, $speedSuffix = 'Mb', $rangeSeparator = '-')
    {
        $return = "";

        if ($rangeBottom != null && $rangeBottom >= 0 && $rangeTop > 0 && $rangeBottom != $rangeTop) {
            $return = "{$rangeBottom} {$rangeSeparator} {$rangeTop}{$speedSuffix}";
        } elseif ($rangeTop > 0) {
            $return = "{$rangeTop}{$speedSuffix}";
        } elseif ($rangeBottom > 0) {
            $return = "{$rangeBottom}{$speedSuffix}";
        }

        return $return;
    }


}
