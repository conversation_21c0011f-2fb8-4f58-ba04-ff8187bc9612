<?php
/**
 * Testing class for the AccountChange_Product_WlrProductSorter
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://jira.internal.plus.net/browse/PRI-281
 *
 */
/**
 * Testing class for the AccountChange_Product_WlrProductSorter
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://jira.internal.plus.net/browse/PRI-281
 *
 */
class AccountChange_Product_WlrProductSorterTest extends PHPUnit_Framework_TestCase
{
    /**
     * Test sorting logic when showing all products
     *
     * @covers AccountChange_Product_WlrProductSorter::sort
     * @covers AccountChange_Product_WlrProductSorter::boltOnMobiles
     * @covers AccountChange_Product_WlrProductSorter::sortCallback
     *
     *
     * @return void
     */
    public function testSortingFullList()
    {
        $sorterMock = $this->getSorterMock();

        $sorted = $sorterMock->sort($this->getAllProducts(), '919');

        $this->assertOrder(
            $sorted,
            array(
                'Plusnet Evening and Weekends Free',
                'Plusnet Evening and Weekends Free with Mobile',
                'Plusnet Weekends',
                'Plusnet Weekends with Mobile',
                'Plusnet Line Only',
                'Plusnet Line Only with Mobile',
                'Plusnet Evening and Weekends',
                'Plusnet Evening and Weekends with Mobile',
                'Plusnet Anytime',
                'Plusnet Anytime with Mobile',
                'Plusnet Anytime International 300',
                'Plusnet Anytime International 300 with Mobile',
                'Plusnet Mobile',
                'Talk Evenings & Weekends',
                'Talk Anytime International 300',
                'Talk Mobile 1',
                'Talk Mobile',
                'Talk Anytime' // Current product last
            )
        );
    }

    /**
     * Test sorting logic when showing only old products
     *
     * This happens when the customer is on old LRS
     *
     * @return void
     */
    public function testSortingOldList()
    {
        $sorterMock = $this->getSorterMock();
        $sorted = $sorterMock->sort($this->getOldProducts(), '919');
        $this->assertOrder(
            $sorted,
            array(
                'Talk Evenings & Weekends',
                'Talk Anytime International 300',
                'Talk Mobile 1',
                'Talk Mobile',
                'Talk Anytime' // Current product last
            )
        );
    }

    /**
     * Test sorting logic when showing only new products
     *
     * This happens when the customer is on a new product
     *
     * @return void
     */
    public function testSortingNewList()
    {
        $sorterMock = $this->getSorterMock();
        $sorted = $sorterMock->sort($this->getNewProducts(), '1638');
        $this->assertOrder(
            $sorted,
            array(
                'Plusnet Evening and Weekends Free',
                'Plusnet Evening and Weekends Free with Mobile',
                'Plusnet Weekends',
                'Plusnet Weekends with Mobile',
                'Plusnet Line Only',
                'Plusnet Line Only with Mobile',
                'Plusnet Evening and Weekends',
                'Plusnet Evening and Weekends with Mobile',
                'Plusnet Anytime',
                'Plusnet Anytime with Mobile',
                'Plusnet Anytime International 300 with Mobile',
                'Plusnet Mobile',
                'Plusnet Anytime International 300' // Current product last
            )
        );
    }

    /**
     * Test sorting logic when we are on a bolton
     * product and the non-bolton version has been
     * filtered out (here we are on Anytime with Mobile,
     * and Anytime has been filtered out).
     *
     * @return void
     */
    public function testSortingNewListWithBoltOnProduct()
    {
        $sorterMock = $this->getSorterMock();

        $products = $this->removeProductWithKeyAndValue(
            'intNewWlrId',
            '1640',
            $this->getNewProducts()
        );

        $sorted = $sorterMock->sort($products, '1641');

        $this->assertOrder(
            $sorted,
            array(
                'Plusnet Evening and Weekends Free',
                'Plusnet Evening and Weekends Free with Mobile',
                'Plusnet Weekends',
                'Plusnet Weekends with Mobile',
                'Plusnet Line Only',
                'Plusnet Line Only with Mobile',
                'Plusnet Evening and Weekends',
                'Plusnet Evening and Weekends with Mobile',
                'Plusnet Anytime International 300',
                'Plusnet Anytime International 300 with Mobile',
                'Plusnet Mobile',
                'Plusnet Anytime with Mobile'// Current product last
            )
        );
    }

    /**
     * Assertion helper for order
     *
     * @param array $sorted   sorted list of products
     * @param array $expected list of product names
     *
     * @return void
     */
    protected function assertOrder(array $sorted, array $expected)
    {
        // array_values to ignore key differences
        $sorted = array_values(
            array_map(
                function ($item) {
                    return $item['strProductName'];
                },
                $sorted
            )
        );

        $this->assertEquals($expected, $sorted);
    }

    /**
     * Produce list of all products
     *
     * @return list of all products
     */
    protected function getAllProducts()
    {
        return array_merge($this->getOldProducts(), $this->getNewProducts());
    }

    /**
     * Produce list of only old products
     *
     * Generated by using var_export on staging
     *
     * @return list of only old products
     */
    protected function getOldProducts()
    {
        return array(
            array (
                'intServiceComponentId' => 669,
                'intNewWlrId' => '669', 'strContract' => 'MONTHLY',
                'strProductName' => 'Talk Anytime International 300',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 19.99),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 13.99),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 6),
            ),
            array (
                'intServiceComponentId' => 670,
                'intNewWlrId' => '670', 'strContract' => 'MONTHLY',
                'strProductName' => 'Talk Evenings & Weekends',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 13.99),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 13.99),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
            ),
            array (
                'intServiceComponentId' => 919,
                'intNewWlrId' => '919', 'strContract' => 'MONTHLY',
                'strProductName' => 'Talk Anytime',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.99),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 13.99),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 4),
            ),
            array (
                'intServiceComponentId' => 920,
                'intNewWlrId' => '920', 'strContract' => 'MONTHLY',
                'strProductName' => 'Talk Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 26.98),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 13.99),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 12.99),
            ),
            array (
                'intServiceComponentId' => 921,
                'intNewWlrId' => '921', 'strContract' => 'MONTHLY',
                'strProductName' => 'Talk Mobile 1',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 23.98),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 13.99),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 9.99),
            ),
        );
    }

    /**
     * Produce list of only new products
     *
     * Generated by using var_export on staging
     *
     * @return list of only new products
     */
    protected function getNewProducts()
    {
        return array (

            array (
                'intServiceComponentId' => 1638,
                'intNewWlrId' => '1638', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Anytime International 300',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 22.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 7),
            ),
            array (
                'intServiceComponentId' => 1639,
                'intNewWlrId' => '1639', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Anytime International 300 with Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 25.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
            ),
            array (
                'intServiceComponentId' => 1642,
                'intNewWlrId' => '1642', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Evening and Weekends Free',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
            ),
            array (
                'intServiceComponentId' => 1643,
                'intNewWlrId' => '1643', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Evening and Weekends Free with Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
            ),
            array (
                'intServiceComponentId' => 1640,
                'intNewWlrId' => '1640', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Anytime',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 5),
            ),
            array (
                'intServiceComponentId' => 1641,
                'intNewWlrId' => '1641',
                'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Anytime with Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 23.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 8),
            ),
            array (
                'intServiceComponentId' => 1644,
                'intNewWlrId' => '1644', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Evening and Weekends',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 2),
            ),
            array (
                'intServiceComponentId' => 1645,
                'intNewWlrId' => '1645', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Evening and Weekends with Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 5),
            ),
            array (
                'intServiceComponentId' => 1646,
                'intNewWlrId' => '1646', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 25.94),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 9.99),
            ),
            array (
                'intServiceComponentId' => 1651,
                'intNewWlrId' => '1651', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Line Only',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
            ),
            array (
                'intServiceComponentId' => 1652,
                'intNewWlrId' => '1652', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Line Only with Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 18.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 3),
            ),
            array (
                'intServiceComponentId' => 1647,
                'intNewWlrId' => '1647', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Weekends',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
            ),
            array (
                'intServiceComponentId' => 1648,
                'intNewWlrId' => '1648', 'strContract' => 'MONTHLY',
                'strProductName' => 'Plusnet Weekends with Mobile',
                'bolSplitPrice' => '1',
                'intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 18.95),
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'intCallPlanCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 3),
            ),
        );
    }

    private function getSorterMock()
    {
        $sorterMock = $this->getMock(
            'AccountChange_Product_WlrProductSorter',
            array('getPhoneHelper')
        );

        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );

        $phoneHelperMock->expects($this->any())
            ->method('getServiceComponentBoltOnMappings')
            ->will(
                $this->returnValue(
                    array(
                        array(
                            'intServiceComponentID' => 1651,
                            'intBoltOnServiceComponentID' => 1652
                        ),
                        array(
                            'intServiceComponentID' => 1647,
                            'intBoltOnServiceComponentID' => 1648
                        ),
                        array(
                            'intServiceComponentID' => 1642,
                            'intBoltOnServiceComponentID' => 1643
                        ),
                        array(
                            'intServiceComponentID' => 1644,
                            'intBoltOnServiceComponentID' => 1645
                        ),
                        array(
                            'intServiceComponentID' => 1640,
                            'intBoltOnServiceComponentID' => 1641
                        ),
                        array(
                            'intServiceComponentID' => 1638,
                            'intBoltOnServiceComponentID' => 1639
                        )
                    )
                )
            );

        $sorterMock
            ->expects($this->once())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));
        return $sorterMock;
    }

    /**
     * Remove a product that has a key with a value
     * The function ignores keys that are not found
     *
     * @param $products
     */
    private function removeProductWithKeyAndValue($key, $value, $products)
    {
        for ($i = 0; $i < count($products); $i++) {
            if (array_key_exists($key, $products[$i])) {
                if ($products[$i][$key] == $value) {
                    unset($products[$i]);
                }
            }
        }
        return $products;
    }
}
