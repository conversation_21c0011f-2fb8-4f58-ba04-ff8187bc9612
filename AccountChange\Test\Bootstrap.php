<?php
ini_set('memory_limit', '-1');

// Definitions taken from /local/data/mis/database/database_libraries/components/component-defines.inc
define('WLR_ANYTIME_COMPONENT_ID', 509);
define('WLR_EVENING_WEEKEND_COMPONENT_ID', 515);
define('WLR_ANYTIME_PLUS_COMPONENT_ID', 510);
define('WLR_TALK_COMPONENT_ID', 554);
define('WLR_TALK_ANYTIME_COMPONENT_ID', 555);
define('COMPONENT_WLR_PN_BUSINESS_PHONE_PAYG', 735);
define('COMPONENT_WLR_PN_BUSINESS_PHONE_DAYTIME', 736);

// Require the Plusnet Bootstrap file
require_once '/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php';

// Enable dependency tracking for unit tests
Auto_ClassLoader::enableDependencyTracking('AccountChange');

// Require any other files you need for the tests
require_once '/local/codebase2005/modules/Framework/Test/Plusnet_Database_TestCase.class.php';
require_once '/local/codebase2005/modules/Framework/Test/TestCaseWithProxy.class.php';
