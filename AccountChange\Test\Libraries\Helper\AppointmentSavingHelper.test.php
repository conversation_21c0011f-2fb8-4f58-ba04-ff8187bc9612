<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_AppointmentSavingHelperTest extends PHPUnit_Framework_TestCase
{
    /**
     * @Covers AccountChange_AppointmentSavingHelper::saveLiveAppointmentToDb()
     */
    public function testSaveLiveAppointmentToDb()
    {
        $arrAppointment = array(
            'live' => array(
                'date'   => '01/01/2025',
                'timeSlot'   => 'AM',
                'ref' => 'AR0000'
            ),
            'notes' => 'Mock appointment notes'
        );
        $appointment = new AccountChange_AccountChangeAppointment($arrAppointment);
        $serviceId = 11111;

        $mockSavingHelper = $this->getMockBuilder('AccountChange_AppointmentSavingHelper')
            ->setMethods(array(
                "createClient"
            ))
            ->setConstructorArgs(array($appointment, new Int($serviceId)))
            ->getMock();

        $mockEngineerClient = $this->getMockBuilder(EngineerAppointmentClient_Service_Fttp::class)
            ->setMethods(array(
                "storeAppointment"
            ))
            ->getMock();

        $mockSavingHelper->expects($this->once())
            ->method("createClient")
            ->willReturn($mockEngineerClient);

        $mockEngineerClient->expects($this->once())
            ->method("storeAppointment");

        $mockSavingHelper->saveLiveAppointmentToDb();
    }
}
