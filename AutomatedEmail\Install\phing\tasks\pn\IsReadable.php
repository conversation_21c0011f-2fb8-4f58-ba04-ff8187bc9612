<?php
include_once 'phing/Task.php';
class IsReadable extends Task {

    private $_strFile = null;
    private $_strUser = null;
    private $_strReturnName;

    public function setFile($str) {
        $this->_strFile = $str;
    }
    
    public function setUser($str) {
        $this->_strUser = $str;
    }

    public function setReturnName($str) {
        $this->_strReturnName = $str;
    }    

    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}  
		
		if(!file_exists($this->_strFile)) {
			if(preg_match('|^/local/codebase2005/modules/([A-Za-z0-9]+)/|', $this->_strFile, $arrMatches))
			{
				throw new BuildException($this->_strFile . ' is not a valid file/directory. You need to run "cd /local/codebase2005/modules/; cvs co ' . $arrMatches[1]. '"', $this->getLocation());
			}
			else
			{
				throw new BuildException("$this->_strFile is not a valid file/directory", $this->getLocation());		
			}
		}
		
		if (!$this->_strUser) {

			$arrUser['gid'] = posix_getgid();
			if($arrUser['uid']===0) {
				$this->project->setProperty($this->_strReturnName, "logged as root");
				return;
			} 
		} else {
			
			$arrUser = posix_getpwnam($this->_strUser);
		}  

		//get user user/group id
		$intPerms = fileperms($this->_strFile);
		$intGroup = filegroup($this->_strFile);
		$intOwner = fileowner($this->_strFile);
	
		//check permissions
		if($intPerms & 0x0004) {
			//world readable
        	$this->project->setProperty($this->_strReturnName, "world readable");
			return;
		}
    
		if(($intPerms & 0x0020) && ($intGroup ==  $arrUser['gid'])) {
			//group readable
       		$this->project->setProperty($this->_strReturnName, "group readable");
			return;
		}
    
		if(($intPerms & 0x0004) && ($intOwner ==  $arrUser['uid'])) {
			//owner readable
       		$this->project->setProperty($this->_strReturnName, "owner readable");
			return;
		}
    }
}
