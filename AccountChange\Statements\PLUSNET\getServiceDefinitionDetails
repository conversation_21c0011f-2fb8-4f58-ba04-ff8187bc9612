server: coredb
role: slave
rows: single
statement:

SELECT
		sd.requires,
		sd.name,
		sd.isp,
		sd.date_created,
		sd.type,
		sd.minimum_charge,
		sd.initial_charge,
		ap.deferred_install_fee,
		ap.nat,
		ap.bolCapped,
		ap.vchContract as strContract,
		ap.hardware_type,
		ap.bolSDSL,
		ap.intMaximumDailyBandwidth,
		ap.product_name,
		ap.usiFairUsageBytes,
		ap.intProvisioningProfileID,
		ap.intProductGroupID,
		ap.intDefaultConnectionProfileGroupId,
		ap.bt_product_code,
		ap.intDefaultPlustalkComponentID AS intDefaultPlustalkComponentID
FROM
		products.service_definitions sd
		LEFT JOIN products.adsl_product ap
			USING(service_definition_id)
WHERE
		sd.service_definition_id = :intSdi