<?php
use Plusnet\C2mApiClient\Entity\Promotion;

class SortPromotionsByHighToLowPriorityTest extends PHPUnit_Framework_TestCase
{

    /**
     * @test
     */
    public function testItSortsPrioritiesFromHighToLow()
    {
        $promotion1 = new Promotion();
        $promotion1->setCode('Promo1');
        $promotion1->setPriority(100);
        $promotion2 = new Promotion();
        $promotion2->setCode('Promo2');
        $promotion2->setPriority(3);
        $promotion3 = new Promotion();
        $promotion3->setCode('Promo3');
        $promotion3->setPriority(5);
        $promotion4 = new Promotion();
        $promotion4->setCode('Promo4');
        $promotion4->setPriority(1);
        $promotion5 = new Promotion();
        $promotion5->setCode('Promo5');
        $promotion5->setPriority(50);

        $promotions = [$promotion1, $promotion2, $promotion3, $promotion4, $promotion5];

        $promotions = (new SortPromotionsByHighToLowPriority())->handle($promotions);

        $this->assertEquals(5, count($promotions));
        $this->assertEquals('Promo4', $promotions[0]->getCode());
        $this->assertEquals('Promo2', $promotions[1]->getCode());
        $this->assertEquals('Promo3', $promotions[2]->getCode());
        $this->assertEquals('Promo5', $promotions[3]->getCode());
        $this->assertEquals('Promo1', $promotions[4]->getCode());
    }

    /**
     * @test
     */
    public function testDoesntSortArraysWithOnlyOnePromotion()
    {
        $promotion1 = new Promotion();
        $promotion1->setCode('Promo1');
        $promotion1->setPriority(100);

        $promotions = [$promotion1];

        $promotions = (new SortPromotionsByHighToLowPriority())->handle($promotions);

        $this->assertEquals(1, count($promotions));
        $this->assertEquals('Promo1', $promotions[0]->getCode());

    }
}
