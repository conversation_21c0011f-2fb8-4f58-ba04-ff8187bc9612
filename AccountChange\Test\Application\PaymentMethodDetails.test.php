<?php
/**
 * Payment Method Details Test
 *
 * Testing class for AccountChange_PaymentMethodDetails
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: PaymentMethodDetails.test.php,v 1.3 2009-02-17 04:41:24 rmerewood Exp $
 * @since      File available since 2008-12-18
 */
/**
 * AccountChange_PaymentMethodDetails Test class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_PaymentMethodDetails_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Checks for the arrInput variables for the requirement
     *
     */
    public function testArrInputIsSetupCorrectly()
    {
        $arrInputs = array(
                'strPaymentDetailsValid' => 'external:In(no,yes)'
            );

        $objReq = new AccountChange_PaymentMethodDetails();

        $this->assertAttributeEquals($arrInputs, 'arrInputs', $objReq);
    }

    /**
     * @covers AccountChange_PaymentMethodDetails::describe
     *
     */
    public function testDescribeReturnsCorrectInformation()
    {
        $intMonthlyCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10);
        $uxtPaymentDate = I18n_Date::fromTimeStamp(time());

        $objReq = $this->getMock('AccountChange_PaymentMethodDetails', array('UTgetNewMonthlyCost', 'getNewMonthlyPaymentDate'));

        $objReq->expects($this->once())
               ->method('UTgetNewMonthlyCost')
               ->will($this->returnValue($intMonthlyCost));

        $objReq->expects($this->once())
               ->method('getNewMonthlyPaymentDate')
               ->will($this->returnValue($uxtPaymentDate));

        $arrValidatedData = array('strPaymentDetailsValid' => 'on');
        $arrExpectedReturn = array(
                'intNewMonthlyPayment'     => $intMonthlyCost,
                'uxtNewMonthlyPaymentDate' => $uxtPaymentDate,
                'strPaymentDetailsValid'   => $arrValidatedData['strPaymentDetailsValid']
            );

        $arrReturn = $objReq->describe($arrValidatedData);
        $this->assertEquals($arrExpectedReturn, $arrReturn);
    }

    /**
     * @covers AccountChange_PaymentMethodDetails::UTgetNewMonthlyCost
     *
     */
    public function testGetNewMonthlyCostReturnsI18nCurrentObjectWithValueOfZeroIfNoProductsSet()
    {
        $arrData = array();
        $objExpectedReturn = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);

        $objReq = TestCaseWithProxy::getPHPUnitProxy('AccountChange_PaymentMethodDetails', array());
        $unkResult = $objReq->protected_UTgetNewMonthlyCost($arrData);

        $this->assertInstanceOf('I18n_Currency', $unkResult);
        $this->assertEquals($objExpectedReturn, $unkResult);
    }

    /**
     * @covers AccountChange_PaymentMethodDetails::UTgetNewMonthlyCost
     *
     */
    public function testGetNewMonthlyCostIncludesCurrentPhonePricesIfWeAreNotChangingPhoneProduct()
    {
        $arrData = array(
            'arrSelectedBroadband' => array('intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20)),
            'arrWlrProduct' => array('intProductCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15))
        );
        $objExpectedReturn = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 35);

        $objReq = TestCaseWithProxy::getPHPUnitProxy('AccountChange_PaymentMethodDetails', array());
        $unkResult = $objReq->protected_UTgetNewMonthlyCost($arrData);

        $this->assertInstanceOf('I18n_Currency', $unkResult);
        $this->assertEquals($objExpectedReturn, $unkResult);
    }

    /**
     * @covers AccountChange_PaymentMethodDetails::UTgetNewMonthlyCost
     *
     */
    public function testGetNewMonthlyCostReturnsI18nCurrentObjectWithOngoingPriceIfProductsAreSet()
    {
        $arrData = array(
                'arrSelectedBroadband' => array('intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20)),
                'arrSelectedWlr'       => array('intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15)),
            );

        $objExpectedReturn = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 35);

        $objCost = AccountChange_PaymentMethodDetails::getNewMonthlyCost($arrData);
        $this->assertEquals($objExpectedReturn, $objCost);
    }

    /**
     * @covers AccountChange_PaymentMethodDetails::UTgetNewMonthlyCost
     *
     */
    public function testGetNewMonthlyCostReturnsI18nCurrencyObjectWithNonDiscountedPriceIfPromoCodeInvalid()
    {

        $arrSelectedBroadband = array(
            'intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
            'discountAmount' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 5)
        );
        $arrSelectedWlr = array(
            'intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15),
            'lineRentalDiscountAmount' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
        );

        $arrData = array(
            'promoCodeInvalidated' => true,
            'arrSelectedBroadband' => $arrSelectedBroadband,
            'arrSelectedWlr'       => $arrSelectedWlr
        );

        $objExpectedReturn = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 50);

        $objCost = AccountChange_PaymentMethodDetails::getNewMonthlyCost($arrData);
        $this->assertEquals($objExpectedReturn, $objCost);
    }

    /**
     * @covers AccountChange_PaymentMethodDetails::UTgetNewMonthlyCost
     *
     */
    public function testGetNewMonthlyCostReturnsI18nCurrencyObjectWithDiscountedPriceIfPromoCodeNotInvalid()
    {

        $arrSelectedBroadband = array(
            'intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
            'discountAmount' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 5)
        );
        $arrSelectedWlr = array(
            'intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15),
            'lineRentalDiscountAmount' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
        );

        $arrData = array(
            'promoCodeInvalidated' => false,
            'arrSelectedBroadband' => $arrSelectedBroadband,
            'arrSelectedWlr'       => $arrSelectedWlr
        );

        $objExpectedReturn = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 35);

        $objCost = AccountChange_PaymentMethodDetails::getNewMonthlyCost($arrData);
        $this->assertEquals($objExpectedReturn, $objCost);
    }





}
