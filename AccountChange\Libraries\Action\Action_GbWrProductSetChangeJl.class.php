<?php
/**
 * Greenbee/Waitrose Product Set Change to <PERSON> action
 *
 * Action that sends different types of emails to Greenbee/Waitrose customers moving to a John Lewis product.
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @since      File available since 2011-09-06
 */
/**
 * Greenbee/Waitrose Product Set Change to <PERSON> action class
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_Action_GbWrProductSetChangeJl extends AccountChange_Action_AbstractGbWrChangeToJl
{
    /**
     * Constants for email template handles
     *
     * @var string
     */
    const CBC_EMAIL_TEMPLATE_HANDLE                                   = 'cbc_before_billing_date';
    const GB_WR_TO_JLP_SCHEDULED_ACCOUNT_CHANGE_EMAIL_TEMPLATE_HANDLE = 'gb_wr_to_jlp_scheduled_account_change';

    /**
     * Execute the action
     *
     * Sends two types of email if the requirements are met.
     *
     * @return void
     */
    public function execute()
    {
        parent::execute();

        // Is the customer changing from a Greenbee/Waitrose product to a John Lewis product?
        if ($this->isCustomerSwitchingFromGbWrToJlp()) {

            // Is the account change actioned before the billing date for the current month?
            if ($this->beforeBillingDateForCurrentMonth()) {

                // Send email to inform them of their full CBC usage until the product change
                $this->sendEmail(self::CBC_EMAIL_TEMPLATE_HANDLE);
            }

            // Is account change scheduled?
            if ($this->_isAccountChangeScheduled) {

                $emailData = array();
                $emailData['strProduct'] = AccountChange_Registry::instance()->getEntry('newProductName');
                $emailData['floOngoingProductCost'] = AccountChange_Registry::instance()->getEntry('newProductCost');

                $lineCheck = AccountChange_Registry::instance()->getEntry('objLineCheckResult');
                $newServiceDefinitionId = AccountChange_Registry::instance()->getEntry('intNewServiceDefinitionId');

                if ($lineCheck instanceof LineCheck_Result) {

                    $rules = $this->getProductRules();
                    $productProvDetails = $rules->getProductProvisionForService(
                        $newServiceDefinitionId,
                        $lineCheck
                    );

                    $estimatedSpeed = $lineCheck->getHighestAvailableSpeed(
                        $productProvDetails['intSupplierPlatformID'],
                        $productProvDetails['intSupplierProductId'],
                        $productProvDetails['strProvisionOn']
                    );

                    $emailData['strEstimatedSpeed'] = $estimatedSpeed/1000;
                }

                $arrSelectedWlrDetails = AccountChange_Registry::instance()->getEntry('arrSelectedWlr');

                if (!empty($arrSelectedWlrDetails) &&
                    array_key_exists('strNewProduct', $arrSelectedWlrDetails) &&
                    !empty($arrSelectedWlrDetails['strNewProduct'])
                ) {

                    $emailData['bolHomePhone']                   = true;
                    $emailData['strHomePhoneProduct']            = $arrSelectedWlrDetails['strNewProduct'];
                    $emailData['floOngoingHomePhoneProductCost'] = $arrSelectedWlrDetails['intNewCost'];

                } else {

                    $emailData['bolHomePhone']                   = false;
                }


                // Send email to inform them that they are scheduled to switch from greenbee/waitrose to john lewis.
                $this->sendEmail(self::GB_WR_TO_JLP_SCHEDULED_ACCOUNT_CHANGE_EMAIL_TEMPLATE_HANDLE, $emailData);

            }
            // No need to send an email for instant account changes as this will be done via the adsl2adsl template
            // in AccountChange_Product_ServiceDefinition::sendConfirmationEmail()
        }
    }

    /**
     * Creates and returns an instance of AccountChange_ProductRules
     *
     * @return AccountChange_ProductRules
     */
    protected function getProductRules()
    {
        return AccountChange_ProductRules::instance();
    }

    /**
     * Returns whether account change has been actioned before the next billing day
     * and the next billing date is in the current month.
     *
     * @return boolean
     */
    protected function beforeBillingDateForCurrentMonth()
    {
        $currentDay = $this->getCurrentDay();
        $currentMonth = $this->getCurrentMonth();

        $nextBillingDate = $this->_accountDetails->getBillingDate();
        $nextBillingDay = $nextBillingDate->getBillingDate()->getDay();
        $nextBillingMonth = $nextBillingDate->getBillingDate()->getMonth();

        return ($currentDay < $nextBillingDay && $currentMonth == $nextBillingMonth);
    }

    /**
     * Return the current day of the month
     *
     * @return integer
     */
    protected function getCurrentDay()
    {
        return date('j', time());
    }

    /**
     * Return the current month
     *
     * @return integer
     */
    protected function getCurrentMonth()
    {
        return date('n', time());
    }
}
