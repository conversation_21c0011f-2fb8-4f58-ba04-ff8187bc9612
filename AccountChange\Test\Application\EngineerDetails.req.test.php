<?php
/**
 * AccountChange_EngineerDetails_Test
 *
 * Tests for AccountChange ConfirmationDetails requirement
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @link       http://documentation.plus.net/index.php/AccountChange_Module
 */
/**
 * AccountChange_EngineerDetails_Test
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @link       http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_EngineerDetails_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test the notes validator
     *
     * @param string $engineeringNotes The input
     * @param array  $expected         The expected result
     * @param array  $expectedError    The expected validation error array
     *
     * @covers       AccountChange_EngineerDetails::valEngineeringNotes
     * @dataProvider notesProvider
     *
     * @return void
     */
    public function testValEngineeringNotes($engineeringNotes, $expected, $expectedError)
    {
        $page = $this->getMock('AccountChange_EngineerDetails', array('describe'));
        $result = $page->valEngineeringNotes($engineeringNotes);
        $errors = $page->getValidationErrors();

        $this->assertEquals($expected, $result);
        $this->assertEquals($expectedError, $errors);
    }

    /**
     * Provide data for testValEngineeringNotes
     *
     * @return array
     **/
    public function notesProvider()
    {
        return array(
            array(
                'Normal, valid notes',
                array('engineeringNotes' => 'Normal, valid notes'),
                array(),
            ),
            array(
                'Nasty, invalid notes*&^%$�@',
                array('engineeringNotes' => 'Nasty, invalid notes*&^%$�@'),
                array('engineeringNotes' =>
                    array('INVALID' => true)
                ),
            ),
            array(
                'Notes which exceed the 40 character limit',
                array('engineeringNotes' => 'Notes which exceed the 40 character limit'),
                array('engineeringNotes' => array('INVALID' => true)),
            ),
        );
    }

    /**
     * Test the appointment times validator
     *
     * @param string $appointmenttime1 Appointment time input
     * @param string $appointmenttime2 Appointment time input
     * @param string $appointmenttime3 Appointment time input
     * @param bool   $liveAppointing   Flag to indicate live appointing was used
     * @param array  $expected         Output
     * @param array  $expectedError    Expected validation errors
     *
     * @covers       AccountChange_EngineerDetails::valTimes
     * @dataProvider timesProvider
     *
     * @return void
     */
    public function testValTimes(
        $appointmenttime1,
        $appointmenttime2,
        $appointmenttime3,
        $liveAppointing,
        $expected,
        $expectedError
    ) {
        $page = $this->getMock('AccountChange_EngineerDetails', array('describe'));
        $result = $page->valTimes($appointmenttime1, $appointmenttime2, $appointmenttime3, $liveAppointing);
        $errors = $page->getValidationErrors();

        $this->assertEquals($expected, $result);
        $this->assertEquals($expectedError, $errors);
    }

    /**
     * Provider for testValTimes
     *
     *  @return array
     */
    public function timesProvider()
    {
        return array(
            array(
                null, null, null, true,
                array('appointmenttime1' => null, 'appointmenttime2' => null, 'appointmenttime3' => null),
                array(),
            ),
            array(
                'AM', 'PM', 'Rhubarb', true,
                array('appointmenttime1' => null, 'appointmenttime2' => null, 'appointmenttime3' => null),
                array(),
            ),
            array(
                'AM', 'PM', '1400', false,
                array('appointmenttime1' => 'AM', 'appointmenttime2' => 'PM', 'appointmenttime3' => '1400'),
                array(),
            ),
            array(
                'Rhubarb', '1000', '1200', false,
                array('appointmenttime1' => 'Rhubarb', 'appointmenttime2' => '1000', 'appointmenttime3' => '1200'),
                array( 'appointmenttime1' => array ('INVALID' => true)),
            ),
            array(
                '1400', 'custard', '1600', false,
                array('appointmenttime1' => '1400', 'appointmenttime2' => 'custard', 'appointmenttime3' => '1600'),
                array( 'appointmenttime2' => array ('INVALID' => true)),
            ),
            array(
                '1800', '2000', 'treacle', false,
                array('appointmenttime1' => '1800', 'appointmenttime2' => '2000', 'appointmenttime3' => 'treacle'),
                array( 'appointmenttime3' => array ('INVALID' => true)),
            )
        );
    }

    /**
     * Test that the validater for the 3 dates works
     *
     * @param string $appointmentdate1 Date input
     * @param string $appointmentdate2 Date input
     * @param string $appointmentdate3 Date input
     * @param string $appointmenttime1 Appointment time 1
     * @param string $appointmenttime2 Appointment time 2
     * @param string $appointmenttime3 Appointment time 3
     * @param bool   $liveAppointing   Flag indicating whether live appointing was used
     * @param int    $leadTime         Lead time
     * @param string $leadDate         Lead date
     * @param bool   $holiday          Holiday
     * @param array  $expected         Expected output
     * @param array  $expectedError    Expected validation errors
     *
     * @covers       AccountChange_EngineerDetails::valDates
     * @dataProvider datesProvider
     *
     * @return void
     */
    public function testValDates(
        $appointmentdate1,
        $appointmentdate2,
        $appointmentdate3,
        $appointmenttime1,
        $appointmenttime2,
        $appointmenttime3,
        $liveAppointing,
        $leadTime,
        $leadDate,
        $holiday,
        $expected,
        $expectedError
    ) {
        $page = $this->getMock('AccountChange_EngineerDetails', array('getLeadTime', 'getLeadDate', 'isHoliday'));
        $page
            ->expects($this->any())
            ->method('getLeadTime')
            ->will($this->returnValue($leadTime));
        $page
            ->expects($this->any())
            ->method('getLeadDate')
            ->will($this->returnValue($leadDate));
        $page
            ->expects($this->any())
            ->method('isHoliday')
            ->will($this->returnValue($holiday));


        $result = $page->valDates(
            $appointmentdate1, $appointmentdate2, $appointmentdate3,
            $appointmenttime1, $appointmenttime2, $appointmenttime3,
            $liveAppointing
        );
        $errors = $page->getValidationErrors();

        $this->assertEquals($expected, $result);
        $this->assertEquals($expectedError, $errors);
    }

    /**
     * Provider for testValDates
     *
     * @return array
     */
    public function datesProvider()
    {
        return array(
            array(
                null, null, null, null, null, null, true,
                10, I18n_Date::fromString('2011-05-31'), false,
                array('appointmentdate1' => null, 'appointmentdate2' => null, 'appointmentdate3' => null,
                ),

                array(),
            ),
            array(
                '10/10/2011', 'stuff', null, 'AM', 'PM', 'AM', true,
                10, I18n_Date::fromString('2011-05-31'), false,
                array('appointmentdate1' => null, 'appointmentdate2' => null, 'appointmentdate3' => null,
                ),
                array(),
            ),
            array(
                '31/05/2011', '01/06/2011', '02/06/2011', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-31'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 31, 2011),
                    'appointmentdate2' => mktime(12, 0, 0, 06, 01, 2011),
                    'appointmentdate3' => mktime(12, 0, 0, 06, 02, 2011),
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(),
            ),
            array(
                '', '01/06/2011', '02/06/2011', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-31'), false,
                array(
                    'appointmentdate1' => null,
                    'appointmentdate2' => mktime(12, 0, 0, 06, 01, 2011),
                    'appointmentdate3' => mktime(12, 0, 0, 06, 02, 2011),
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array('appointmentdate1' => array('INVALID' => true)),
            ),
            array(
                '31/05/2011', 'bananas', '02/06/2011', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-31'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 31, 2011),
                    'appointmentdate2' => null,
                    'appointmentdate3' => mktime(12, 0, 0, 06, 02, 2011),
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array('appointmentdate2' => array('INVALID' => true)),
            ),
            array(
                '31/05/2011', '2010-05-28', '548/06/2011', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-31'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 31, 2011),
                    'appointmentdate2' => null,
                    'appointmentdate3' => null,
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(
                    'appointmentdate2' => array('INVALID' => true),
                    'appointmentdate3' => array('INVALID' => true),
                ),
            ),
            array(
                '29/05/2011', '28/05/2011', '', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-29'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 29, 2011),
                    'appointmentdate2' => mktime(12, 0, 0, 05, 28, 2011),
                    'appointmentdate3' => null,
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(
                    'appointmentdate1' => array('WEEKEND' => true),
                    'appointmentdate2' => array('LEADTIME' => true),
                    'appointmentdate3' => array('INVALID' => true),
                ),
            ),
            array(
                '30/05/2011', '', '', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-29'), true,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate2' => null,
                    'appointmentdate3' => null,
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(
                    'appointmentdate1' => array('BANKHOLIDAY' => true),
                    'appointmentdate2' => array('INVALID' => true),
                    'appointmentdate3' => array('INVALID' => true),
                ),
            ),
            array(
                '30/05/2011', '30/05/2011', '30/05/2011', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-29'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate2' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate3' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(
                    'appointmentdate1' => array('NOTUNIQUE' => true),
                    'appointmentdate3' => array('NOTUNIQUE' => true),
                ),
            ),
            array(
                '30/05/2011', '30/05/2011', '30/05/2011', 'AM', 'AM', 'PM', false,
                10, I18n_Date::fromString('2011-05-29'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate2' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate3' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(
                    'appointmentdate1' => array('NOTUNIQUE' => true),
                    'appointmentdate2' => array('NOTUNIQUE' => true),
                ),
            ),
            array(
                '30/05/2011', '30/05/2011', '30/05/2011', 'AM', 'PM', 'PM', false,
                10, I18n_Date::fromString('2011-05-29'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate2' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate3' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(
                    'appointmentdate2' => array('NOTUNIQUE' => true),
                    'appointmentdate3' => array('NOTUNIQUE' => true),
                ),
            ),
            array(
                '30/05/2011', '30/05/2011', '31/05/2011', 'AM', 'PM', 'AM', false,
                10, I18n_Date::fromString('2011-05-29'), false,
                array(
                    'appointmentdate1' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate2' => mktime(12, 0, 0, 05, 30, 2011),
                    'appointmentdate3' => mktime(12, 0, 0, 05, 31, 2011),
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(),
            ),
        );
    }

    /**
     * Test that the live appointing validator works
     *
     * @param bool   $liveAppointing Flag indicating live appointing was used
     * @param string $appointment    Input string, looks like '31/04/2011AM'
     * @param array  $expected       Expected output
     * @param array  $expectedError  Expected validation errors
     *
     * @covers       AccountChange_EngineerDetails::valAppointment
     * @dataProvider liveAppointingProvider
     *
     * @return void
     */
    public function testValAppointment($liveAppointing, $appointment, $expected, $expectedError)
    {
        $page = $this->getMock('AccountChange_EngineerDetails', array('describe'));
        $result = $page->valAppointment($liveAppointing, $appointment);
        $errors = $page->getValidationErrors();

        $this->assertEquals($expected, $result);
        $this->assertEquals($expectedError, $errors);
    }

    /**
     * Dataprovider for testValLiveAppointing
     *
     * @return array
     */
    public function liveAppointingProvider()
    {
        return array(
            array(
                false,
                '31/0sdfsdf011AM',
                array('appointment' => null),
                array(),
            ),
            array(
                true,
                '31/05/2011AM',
                array(
                    'appointment' => '31/05/2011AM',
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(),
            ),
           array(
                true,
                '31/05/20111400',
                array(
                    'appointment' => '31/05/20111400',
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array(),
            ),
           array(
                true,
                '31/05/20112100',
                array(
                    'appointment' => '31/05/20112100',
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array('appointment' => array('INVALID' => true)),
            ),
           array(
                true,
                '31/a/20111400',
                array(
                    'appointment' => '31/a/20111400',
                    'appointingType'   => array(
                        'serviceHandle' => 'FTTC',
                        'service' => 'EngineerAppointmentClient_Service_Fttc'
                    ),
                ),
                array('appointment' => array('INVALID' => true)),
            ),
        );
    }

    /**
     * Check that the live appointing failed flag is successfully set from the live appointing requirement
     * and presence.
     *
     * @param boolean        $liveAppointing     Live appointing was used
     * @param string|boolean $appointingRequired Was live appointing attempted
     * @param array          $expected           Expected output
     *
     * @covers       AccountChange_EngineerDetails::valLiveAppointingFailed
     * @dataProvider liveAppointingFailedProvider
     *
     * @return void
     */
    public function testValLiveAppointingFailed($liveAppointing, $appointingRequired, $expected)
    {
        $page = $this->getMock('AccountChange_EngineerDetails', array('isLiveAppointingRequired'));
        $page
            ->expects($this->any())
            ->method('isLiveAppointingRequired')
            ->will($this->returnValue($appointingRequired));

        $result = $page->valLiveAppointingFailed($liveAppointing);
        $errors = $page->getValidationErrors();

        $this->assertEquals($expected, $result);
        $this->assertEquals(array(), $errors);
    }

    /**
     * Provider for testValLiveAppointingFailed
     *
     * @return array
     */
    public function liveAppointingFailedProvider()
    {
        return array(
            array(
                true,
                true,
                array('liveAppointing' => true, 'liveAppointingFailed' => false),
            ),
            array(
                true,
                false,
                array('liveAppointing' => true, 'liveAppointingFailed' => false),
            ),
            array(
                false,
                false,
                array('liveAppointing' => false, 'liveAppointingFailed' => false),
            ),
            array(
                false,
                true,
                array('liveAppointing' => false, 'liveAppointingFailed' => true),
            ),
        );
    }

    /**
     * Test calculating the lead date from the start and lead time in days
     *
     * @param int       $days     Number of days to ofset
     * @param I18n_Date $start    Date to start counting from
     * @param I18n_Date $expected Expected result
     *
     * @covers       AccountChange_EngineerDetails::getLeadDate
     * @dataProvider leadDateProvider
     *
     * @return void
     */
    public function testGetLeadDate($days, $start, $expected)
    {
        $page = $this->getMock('AccountChange_EngineerDetails', array('getLiveAppointingType'));
        $result = $page->getLeadDate($days, $start);
        $this->assertEquals($expected->getTimestamp(), $result->getTimestamp());
    }

    /**
     * Data provider for testGetLeadDate
     *
     * @return array
     */
    public function leadDateProvider()
    {
        return array(
            array(2, I18n_Date::fromString('2010-05-31'), I18n_Date::fromString('2010-06-02')),
            array(5, I18n_Date::fromString('2010-05-31'), I18n_Date::fromString('2010-06-05')),
            array(10, I18n_Date::fromString('2010-05-31'), I18n_Date::fromString('2010-06-12')),
            array(15, I18n_Date::fromString('2010-05-31'), I18n_Date::fromString('2010-06-19')),
            array(20, I18n_Date::fromString('2010-05-31'), I18n_Date::fromString('2010-06-26')),
        );
    }

    /**
     * Test getApppointingType
     *
     * @covers AccountChange_EngineerDetails::getAppointingType
     *
     * @return void
     */
    public function testAppointingType()
    {
        $expected = array(
            'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
            'service'        => EngineerAppointmentClient_Service::FTTC,
        );
        $page = new AccountChange_EngineerDetails();
        $result = $page->getAppointingType();
        $this->assertEquals($expected, $result);
    }

    /**
     * Test describe works correctly when there are no live appointments
     *
     * @param int   $leadTime     Lead time
     * @param array $bankHolidays Bank holidays
     * @param array $expected     Expected result
     *
     * @covers AccountChange_EngineerDetails::describe
     * @covers AccountChange_EngineerDetails::formatBankHolidaysList
     * @dataProvider noLiveAppointingProvider
     *
     * @return void
     */
    public function testDescribeWithNoLiveAppointing(
        $leadTime,
        $bankHolidays,
        $expected
    ) {
        $page = $this->getMock(
            'AccountChange_EngineerDetails',
            array(
                'getLeadTime',
                'getBankHolidays',
                'isLiveAppointingRequired',
                'getSelectedBroadband'
            )
        );

        $page
            ->expects($this->once())
            ->method('getLeadTime')
            ->will($this->returnValue($leadTime));

        $page
            ->expects($this->once())
            ->method('getBankHolidays')
            ->will($this->returnValue($bankHolidays));

        $page
            ->expects($this->once())
            ->method('isLiveAppointingRequired')
            ->will($this->returnValue(false));

        $page
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(null));


        $data = array();
        $result = $page->describe($data);
        $this->assertEquals($expected, $result);
    }

    /**
     * Provide data for testDescribeWithNoLiveAppointing
     *
     * @return array
     **/
    public function noLiveAppointingProvider()
    {
        return array(
            // Data set 0
            array(
                5,
                array(**********, *********, *********),
                array(
                    'leadTime'       => 5,
                    'bankHolidays'   => "['**********000','*********000','*********000']",
                    'liveAppointing' => false,
                    'appointingType' => array(
                        'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
                        'service'        => EngineerAppointmentClient_Service::FTTC,
                    ),
                    'selectedBroadband' => null
                )
            )
        );
    }

    /**
     * Tests describe when fetching live appointments
     *
     * @param int   $leadTime           Lead time
     * @param array $bankHolidays       Bank holidays
     * @param array $appData            Application data
     * @param array $varsPassedToHelper Variables passed to helper
     * @param array $appointmentData    Live appointing data
     * @param array $decorated          Decorated vars
     * @param array $expected           Expected result
     *
     * @covers AccountChange_EngineerDetails::describe
     * @covers AccountChange_EngineerDetails::formatBankHolidaysList
     * @covers AccountChange_EngineerDetails::fetchAvailableAppointments
     * @covers AccountChange_EngineerDetails::getEngineeringAppointingHelper
     * @dataProvider liveAppointingSuccessfulProvider
     *
     * @return void
     */
    public function testDescribeAndFetchLiveAppointments(
        $leadTime,
        $bankHolidays,
        $appData,
        $varsPassedToHelper,
        $appointmentData,
        $decorated,
        $expected
    ) {
        $page = $this->getMock(
            'AccountChange_EngineerDetails',
            array(
                'getLeadTime',
                'getBankHolidays',
                'isLiveAppointingRequired',
                'getEngineeringAppointingHelper',
                'getSelectedBroadband'
            )
        );

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );


        $controller
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('addressRef'))
            ->will($this->returnValue($appData['addressRef']));

        $controller
            ->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('intPhoneNumber'))
            ->will($this->returnValue(true));

        $controller
            ->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('intPhoneNumber'))
            ->will($this->returnValue($appData['intPhoneNumber']));

        $controller
            ->expects($this->at(3))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('extensionKitId'))
            ->will($this->returnValue(true));

        $controller
            ->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('extensionKitId'))
            ->will($this->returnValue($appData['extensionKitId']));

        $controller
            ->expects($this->at(5))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('bolIsBusiness'))
            ->will($this->returnValue(true));

        $controller
            ->expects($this->at(6))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('bolIsBusiness'))
            ->will($this->returnValue($appData['bolIsBusiness']));


        $page->setAppStateCallback($controller);

        $page
            ->expects($this->once())
            ->method('getLeadTime')
            ->will($this->returnValue($leadTime));

        $page
            ->expects($this->once())
            ->method('getBankHolidays')
            ->will($this->returnValue($bankHolidays));

        $page
            ->expects($this->once())
            ->method('isLiveAppointingRequired')
            ->will($this->returnValue(true));

        $page
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue($appData['selectedBroadband']));

        $helper = $this->getMock('AccountChange_EngineerAppointing');

        $helper
            ->expects($this->once())
            ->method('getLiveAppointments')
            ->with($varsPassedToHelper)
            ->will($this->returnValue($appointmentData));

        $helper
            ->expects($this->any())
            ->method('decorateAppointments')
            ->with($appointmentData['appointments'])
            ->will($this->returnValue($decorated));

        $page
            ->expects($this->once())
            ->method('getEngineeringAppointingHelper')
            ->will($this->returnValue($helper));


        $data = array();
        $result = $page->describe($data);

        $this->assertEquals($expected, $result);
    }

    /**
     * Provider for  testDescribeAndFetchLiveAppointments
     *
     * @return array
     */
    public function liveAppointingSuccessfulProvider()
    {
        return array(
            // Data set 0
            array(
                5,
                array(**********, *********, *********),
                array(
                    'addressRef'     => 'A12345678901:PS:GOLD',
                    'intPhoneNumber' => '***********',
                    'extensionKitId' => 2,
                    'bolIsBusiness'  => true,
                    'selectedBroadband' => null
                ),
                array(
                    'serviceHandle'   => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    'service'         => EngineerAppointmentClient_Service::FTTC,
                    'sparePairs'      => null,
                    'journeyType'     => 'A',
                    'addressRef'      => 'A12345678901',
                    'databaseCode'    => 'PS',
                    'addressCategory' => 'GOLD',
                    'cli'             => '***********',
                    'extensionKitId'  => 2,
                    'locationType'
                        => EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_BUSINESS,
                ),
                array(
                    'appointments' => array(
                        '31/05/2011' => array('AM', 'PM'),
                        '01/06/2011' => array('AM'),
                        '02/06/2011' => array('1400', 'PM'),
                        '04/06/2011' => array('AM', '1800'),
                        '05/06/2011' => array('AM', 'PM', '1000', '2000'),
                    ),
                    'offsetDate'   => '2011/06/21',
                ),
                array(
                    '01/06/2011' => array(
                        'AM' => 'AM',
                        'PM' => false,
                    ),
                    '02/06/2011' => array(
                            'AM' => false,
                            'PM' => 1400,
                    ),
                    '04/06/2011' => array(
                            'AM' => 'AM',
                            'PM' => 1800,
                    ),
                    '05/06/2011' => array(
                            'AM' => 1000,
                            'PM' => 2000,
                    ),
                    '31/05/2011' => array(
                            'AM' => 'AM',
                            'PM' => 'PM',
                    ),
                ),
                array(
                    'appointingType' => array(
                        'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
                        'service'       => EngineerAppointmentClient_Service::FTTC,
                    ),
                    'addressCategory'       => 'GOLD',
                    'addressRef'            => 'A12345678901',
                    'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    'service'       => EngineerAppointmentClient_Service::FTTC,
                    'appointmentsDecorated' => array(
                        '01/06/2011' => array(
                            'AM' => 'AM',
                            'PM' => false,
                        ),
                        '02/06/2011' => array(
                                'AM' => false,
                                'PM' => 1400,
                        ),
                        '04/06/2011' => array(
                                'AM' => 'AM',
                                'PM' => 1800,
                        ),
                        '05/06/2011' => array(
                                'AM' => 1000,
                                'PM' => 2000,
                        ),
                        '31/05/2011' => array(
                                'AM' => 'AM',
                                'PM' => 'PM',
                        ),
                    ),
                    'bankHolidays'      => "['**********000','*********000','*********000']",
                    'cli'               => '***********',
                    'databaseCode'      => 'PS',
                    'extensionKitId'    => 2,
                    'journeyType'       => 'A',
                    'leadTime'          => 5,
                    'liveAppointing'    => true,
                    'offsetDate'        => '2011/06/21',
                    'sparePairs'        => null,
                    'locationType'
                        => EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_BUSINESS,
                    'selectedBroadband' => null
                ),
            ),


            // Data set 1
            array(
                5,
                array(**********, *********, *********),
                array(
                    'addressRef'     => 'A12345678901:PS:GOLD',
                    'intPhoneNumber' => '***********',
                    'extensionKitId' => 1,
                    'bolIsBusiness'  => false,
                    'selectedBroadband' => null
                ),
                array(
                    'serviceHandle'   => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    'service'         => EngineerAppointmentClient_Service::FTTC,
                    'sparePairs'      => null,
                    'journeyType'     => 'A',
                    'addressRef'      => 'A12345678901',
                    'databaseCode'    => 'PS',
                    'addressCategory' => 'GOLD',
                    'cli'             => '***********',
                    'extensionKitId'  => 1,
                    'locationType'
                        => EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_RESIDENTIAL,
                ),
                array(
                    'appointments' => array(
                        '31/05/2011' => array('AM', 'PM'),
                        '01/06/2011' => array('AM'),
                        '02/06/2011' => array('1400', 'PM'),
                        '04/06/2011' => array('AM', '1800'),
                        '05/06/2011' => array('AM', 'PM', '1000', '2000'),
                    ),
                    'offsetDate'   => '2011/06/21',
                ),
                array(
                    '01/06/2011' => array(
                        'AM' => 'AM',
                        'PM' => false,
                    ),
                    '02/06/2011' => array(
                            'AM' => false,
                            'PM' => 1400,
                    ),
                    '04/06/2011' => array(
                            'AM' => 'AM',
                            'PM' => 1800,
                    ),
                    '05/06/2011' => array(
                            'AM' => 1000,
                            'PM' => 2000,
                    ),
                    '31/05/2011' => array(
                            'AM' => 'AM',
                            'PM' => 'PM',
                    ),
                ),
                array(
                    'appointingType' => array(
                        'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
                        'service'       => EngineerAppointmentClient_Service::FTTC,
                    ),
                    'addressCategory' => 'GOLD',
                    'addressRef'      => 'A12345678901',
                    'serviceHandle'   => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    'service'         => EngineerAppointmentClient_Service::FTTC,
                    'appointmentsDecorated' => array(
                        '01/06/2011' => array(
                            'AM' => 'AM',
                            'PM' => false,
                        ),
                        '02/06/2011' => array(
                                'AM' => false,
                                'PM' => 1400,
                        ),
                        '04/06/2011' => array(
                                'AM' => 'AM',
                                'PM' => 1800,
                        ),
                        '05/06/2011' => array(
                                'AM' => 1000,
                                'PM' => 2000,
                        ),
                        '31/05/2011' => array(
                                'AM' => 'AM',
                                'PM' => 'PM',
                        ),
                    ),
                    'bankHolidays'      => "['**********000','*********000','*********000']",
                    'cli'               => '***********',
                    'databaseCode'      => 'PS',
                    'extensionKitId'    => 1,
                    'journeyType'       => 'A',
                    'leadTime'          => 5,
                    'liveAppointing'    => true,
                    'offsetDate'        => '2011/06/21',
                    'sparePairs'        => null,
                    'locationType'
                        => EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_RESIDENTIAL,
                    'selectedBroadband' => null
                ),
            ),
        );
    }

    /**
     * Tests describe when an exception is thrown
     *
     * @param int   $leadTime           Lead time
     * @param array $bankHolidays       Bank holidays
     * @param array $appData            Application data
     * @param array $varsPassedToHelper Variables passed to helper
     * @param array $expected           Expected result
     *
     * @covers AccountChange_EngineerDetails::describe
     * @covers AccountChange_EngineerDetails::formatBankHolidaysList
     * @covers AccountChange_EngineerDetails::fetchAvailableAppointments
     * @covers AccountChange_EngineerDetails::auditLog
     * @dataProvider liveAppointingExceptionProvider
     *
     * @return void
     */
    public function testDescribeAndFetchLiveAppointmentsWithExceptionThrown(
        $leadTime,
        $bankHolidays,
        $appData,
        $varsPassedToHelper,
        $expected
    ) {
        $page = $this->getMock(
            'AccountChange_EngineerDetails',
            array(
                'getLeadTime',
                'getBankHolidays',
                'isLiveAppointingRequired',
                'getEngineeringAppointingHelper',
                'auditLog'
            )
        );

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        $controller
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('addressRef'))
            ->will($this->returnValue($appData['addressRef']));

        $controller
            ->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('intPhoneNumber'))
            ->will($this->returnValue(true));

        $controller
            ->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('intPhoneNumber'))
            ->will($this->returnValue($appData['intPhoneNumber']));

        $controller
            ->expects($this->at(3))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('extensionKitId'))
            ->will($this->returnValue(true));

        $controller
            ->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('extensionKitId'))
            ->will($this->returnValue($appData['extensionKitId']));

        $controller
            ->expects($this->at(5))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('bolIsBusiness'))
            ->will($this->returnValue(true));

        $controller
            ->expects($this->at(6))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($page)), $this->equalTo('bolIsBusiness'))
            ->will($this->returnValue($appData['bolIsBusiness']));

        $page->setAppStateCallback($controller);

        $page
            ->expects($this->once())
            ->method('getLeadTime')
            ->will($this->returnValue($leadTime));

        $page
            ->expects($this->once())
            ->method('getBankHolidays')
            ->will($this->returnValue($bankHolidays));

        $page
            ->expects($this->once())
            ->method('isLiveAppointingRequired')
            ->will($this->returnValue(true));

        $helper = $this->getMock('AccountChange_EngineerAppointing');

        $helper
            ->expects($this->once())
            ->method('getLiveAppointments')
            ->will($this->throwException(new Exception('Live appointing failed')));

        $page
            ->expects($this->once())
            ->method('getEngineeringAppointingHelper')
            ->will($this->returnValue($helper));

        $data = array();
        $result = $page->describe($data);
        $this->assertEquals($expected, $result);
    }

    /**
     * Provider for  testDescribeAndFetchLiveAppointmentsWithExceptionThrown
     *
     * @return array
     */
    public function liveAppointingExceptionProvider()
    {
        return array(
            // Data set 0
            array(
                5,
                array(**********, *********, *********),
                array(
                    'addressRef'     => 'A12345678901:PS:GOLD',
                    'intPhoneNumber' => '***********',
                    'extensionKitId' => 2,
                    'bolIsBusiness'  => true,
                ),
                array(
                    'service'         => EngineerAppointmentClient_Service::FTTC,
                    'serviceHandle'   => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    'sparePairs'      => null,
                    'journeyType'     => 'A',
                    'addressRef'      => 'A12345678901',
                    'databaseCode'    => 'PS',
                    'addressCategory' => 'GOLD',
                    'cli'             => '***********',
                    'extensionKitId'  => 2,
                    'locationType'
                        => EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_BUSINESS,
                    'selectedBroadband' => null
                ),
                array(
                    'addressCategory'       => 'GOLD',
                    'addressRef'            => 'A12345678901',
                    'appointingType' => array(
                        'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
                        'service'       => EngineerAppointmentClient_Service::FTTC,
                    ),
                    'appointmentsDecorated' => array(),
                    'bankHolidays'          => "['**********000','*********000','*********000']",
                    'cli'                   => '***********',
                    'databaseCode'          => 'PS',
                    'extensionKitId'        =>  2,
                    'journeyType'           => 'A',
                    'leadTime'              => 5,
                    'liveAppointing'        => false,
                    'sparePairs'            => null,
                    'locationType'
                        => EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_BUSINESS,
                    'service'               => EngineerAppointmentClient_Service::FTTC,
                    'serviceHandle'         => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    'selectedBroadband'     => null
                ),
            ),
        );
    }

    /**
     * Check that getSelectedBroadband returns the application state
     * variable that we're expecting
     *
     * @covers AccountChange_EngineerDetails::getSelectedBroadband
     *
     * @return void
     **/
    public function testGetSelectedBroadbandReturnsExpected()
    {
        $selectedBroadband = array(
            'serviceDefinitionId' => 6768
        );

        $appointment = new AccountChange_EngineerDetails();

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController
            = $this->getMock('AccountChange_Controller', array('getApplicationStateVar'));

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with('AccountChange_EngineerDetails', 'arrSelectedBroadband')
            ->will($this->returnValue($selectedBroadband));

        // Set callback object within the requirement itself to application controller
        $appointment->setAppStateCallback($mockController);
        $actual = $appointment->getSelectedBroadband();
        $this->assertEquals($actual, $selectedBroadband);
    }

    /**
     * Check that isLiveAppointingRequired returns the application state
     * variable that we're expecting
     *
     * @covers AccountChange_EngineerDetails::isLiveAppointingRequired
     *
     * @return void
     **/
    public function testIsLiveAppointingWhenRequired()
    {
        $appointment = new AccountChange_EngineerDetails();

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar'));

        $mockController
            ->expects($this->once())
            ->method('isApplicationStateVar')
            ->with('AccountChange_EngineerDetails', 'addressRef')
            ->will($this->returnValue(true));

        // Set callback object within the requirement itself to application controller
        $appointment->setAppStateCallback($mockController);
        $this->assertTrue($appointment->isLiveAppointingRequired());
    }
}

