<?php
require_once(__DIR__ . '/../Libraries/Rules/RemoveExpiredPromotions.php');
require_once(__DIR__ . '/../Libraries/Rules/BumpPromotionToTop.php');
require_once(__DIR__ . '/../Libraries/Rules/RemoveNonThroughTheLinePromotions.php');
require_once(__DIR__ . '/../Libraries/Rules/PromotionRules.php');
require_once(__DIR__ . '/../Libraries/Rules/SortPromotionsByHighToLowPriority.php');
require_once(__DIR__ . '/../Libraries/Rules/RemovePromotionsThatDoNotOfferOngoingMonthlyDiscount.php');
require_once(__DIR__ . '/../Libraries/ServiceDefinitionProductOfferingNameMapper.php');
require_once(__DIR__ . '/../Libraries/C2MPromotionManager.php');
require_once(__DIR__ . '/../Libraries/C2MPromotionAdapter.php');
require_once(__DIR__ . '/../Libraries/C2mSalesChannels.class.php');
require_once(__DIR__ . '/../Libraries/C2mPromotionsHelper.php');


class C2MCopperToFibreJourney
{

    /**
     * @var array
     */
    private $products;

    /**
     * The SDI of the product the user is currently contracted to and paying
     * for services on.
     *
     * @var string
     */
    private $activeProductSdi;

    /**
     * @var \AccountChange_Registry
     */
    private $registry;

    /**
     * Error Code captured during validationcheck
     * @var String
     */
    private $errorCode;

    /**
     * CopperToFibreJourney constructor.
     *
     * @param $products
     * @param $activeProductSdi
     * @param \AccountChange_Registry $registry
     */
    public function __construct($products, $activeProductSdi, AccountChange_Registry $registry)
    {
        $this->products = $products;
        $this->activeProductSdi = $activeProductSdi;
        $this->registry = $registry;
    }

    /**
     * Wrapper method to carry out all the work to fetch, filter and apply
     * the C2M offers to the list of Broadband products.
     *
     * @param int    $serviceId                Service id
     * @param string $campaign                 Campaign code
     * @param string $promoCode                Promo code
     * @param string $source                   Did this request originate at workplace or member centre?
     * @param bool   $agentSubmittedPromoCode  Has this promo code been entered in workplace account change
     *
     * @return array Products with promotions applied.
     *
     * @throws PromotionNotFoundException
     */
    public function getBroadbandProducts($serviceId, $campaign, $promoCode, $source, $agentSubmittedPromoCode = false)
    {
        $promotionRules = new PromotionRules();
        $promotionRules->setServiceId($serviceId);

        $promotionRules
          ->addRule(new RemoveExpiredPromotions())
          ->addRule(new RemovePromotionsThatDoNotOfferOngoingMonthlyDiscount())
          ->addRule(new SortPromotionsByHighToLowPriority());

        // If an agent has submitted a promo code, then don't remove it..
        if (!$agentSubmittedPromoCode) {
            $promotionRules->addRule(new RemoveNonThroughTheLinePromotions());
        }

        if ($this->registry->getEntry('BTLPromotion')) {
            $promotionRules->addRule(
              new BumpPromotionToTop(
                $this->registry->getEntry('BTLPromotion')
              )
            );
        }

        $c2mSalesChannelPromotions = AccountChange_C2mPromotionsHelper::getFromGivenSalesChannels(
            \BusTier_BusTier::getClient('c2mapi.v5'),
            $promotionRules,
            AccountChange_C2mSalesChannels::getC2MSalesChannel($campaign,
                ($agentSubmittedPromoCode || $source === AccountChange_Controller::SOURCE_WORKPLACE_KEY)),
            $promoCode
        );

        $c2mSalesChannelPromotions->applyRules();

        $c2mPromotionsManager = new C2MPromotionManager(
          $c2mSalesChannelPromotions,
          $this->registry,
          new C2MPromotionAdapter()
        );

        $this->products = (new ServiceDefinitionProductOfferingNameMapper())->mapTo($this->products);
        $productsWithDiscounts = $c2mPromotionsManager->getProductsWithC2mDiscount(
            $this->products,
            $this->activeProductSdi,
            $serviceId,
            $promoCode,
            $agentSubmittedPromoCode
        );

        $this->errorCode = $c2mPromotionsManager->getPromotionValidationErrorCode();

        return $productsWithDiscounts;
    }

    /**
     * Getter method to get the error received from validationcheck
     *
     * @return String
     */
    public function getErrorCode()
    {
        return $this->errorCode;
    }
}
