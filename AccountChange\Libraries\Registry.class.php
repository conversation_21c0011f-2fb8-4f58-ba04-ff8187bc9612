<?php
/**
 * AccountChange Registry
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-15
 */
/**
 * AccountChange Registry
 *
 * This class a registry to keep global common information
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Registry
{
    /**
     * Factory Instsance, represents registry
     *
     * @var AccountChange_Account
     */
    private static $instance;

    /**
     * Registry data
     *
     * @var array
     */
    private $data = null;

    /**
     * Constructor
     *
     * Should only be used by the internal factory methods, left public for unit tests
     *
     * @return AccountChange_Registry
     */
    public function __construct()
    {
        $this->data = array();

        Dbg_Dbg::write('AccountChange_Registry created', 'AccountChange');
    }

    /**
     * Factory instance getter
     *
     * Gets an account object that provides overview information
     *
     * @return AccountChange_Registry
     */
    public static function instance()
    {
        if (!isset(self::$instance)) {

            self::$instance = new AccountChange_Registry();
        }

        return self::$instance;
    }

    /**
     * Setter for the factory
     *
     * Set a bespoke registry instance
     *
     * @param AccountChange_Registry $instance The registry instance
     *
     * @return void
     */
    public static function setInstance(AccountChange_Registry $instance)
    {
        self::$instance = $instance;
    }

    /**
     * Get a specific entry
     *
     * @param string $key The key to the entry
     *
     * @return mixed
     */
    public function getEntry($key)
    {
        $return = null;

        Dbg_Dbg::write('AccountChange_Registry::getEntry asking for ' . $key, 'AccountChange');

        if (isset($this->data[$key])) {

            Dbg_Dbg::write('AccountChange_Registry::getEntry found data for ' . $key, 'AccountChange');

            $return = $this->data[$key];
        }

        return $return;
    }

    /**
     * Set an entry for a specific key
     *
     * @param string $key   The key to the entry
     * @param mixed  $entry The entry itself
     *
     * @return AccountChange_Registry
     */
    public function setEntry($key, $entry)
    {
        Dbg_Dbg::write('AccountChange_Registry::setEntry setting data for ' . $key . ' : ' . var_export($entry, 1), 'AccountChange');
        $this->data[$key] = $entry;
        return $this;
    }

    /**
     * Reset the internal registry
     *
     * @return void
     */
    public function reset()
    {
        $this->data = array();
    }
}
