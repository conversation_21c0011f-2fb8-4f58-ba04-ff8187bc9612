<?php

/**
 * Action Cbc
 *
 * Testing class for the AccountChange_Action_Cbc class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Action_Cbc.test.php,v 1.4 2009-02-17 04:41:26 rmerewood Exp $
 * @since      File available since 2008-09-02
 */
/**
 * Action Cbc Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Action_Cbc_Test extends PHPUnit_Framework_TestCase
{
    /**
    * Service Id
    *
    * @var int
    */
    protected $intServiceId;

    /**
    * PHPUnit setup function
    *
    */
    public function setup()
    {
        $this->intServiceId = 999;
    }

    /**
    * @covers AccountChange_Action_Cbc::execute
    *
    */
    public function testExecuteCallsCorrectSubActions()
    {
        $objMock = $this->getMock('AccountChange_Action_Cbc',
                                array('cancelDataTransferWatch'),
                                array($this->intServiceId));

        $objMock->expects($this->once())
                ->method('cancelDataTransferWatch');

        $objMock->execute();
    }
}
