server: coredb
role: slave
rows: single
statement:

SELECT
    DISTINCT sd.service_definition_id
FROM
    products.service_definitions sd
    INNER JOIN products.adsl_product ap
        ON sd.service_definition_id = ap.service_definition_id
    INNER JOIN products.tblProvisioningProfile pp
        ON pp.intProvisioningProfileID = ap.intProvisioningProfileID
WHERE
    pp.vchName = 'FTTC' AND sd.service_definition_id = :strSdi
