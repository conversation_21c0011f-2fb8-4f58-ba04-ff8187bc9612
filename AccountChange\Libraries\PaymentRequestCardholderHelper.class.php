<?php
/**
 * @package    FrameworkWebApi
 * @subpackage Libraries
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Helper class to manage the obtaining and formatting of additional cardholder information
 * to enrich GenericImmediatePaymentApplication_PaymentRequestData
 *
 */
class AccountChange_PaymentRequestCardholderHelper
{
    /**
     * Template for the array pattern this class will return
     *
     * @var array Multidimensional array
     */
    const DEFAULT_DATA_RESPONSE = array(
        'cardholderContactDetails' => array(),
        'cardholderAddress' => array(),
        'cardholderName' => '',
    );

    /**
     * Config for each outputtable key value
     *
     * @var array Multidimensional array of arrays
     */
    const PARAMETER_KEY_CONFIG = array(
        'name'  => array('characterLimit'  => 30),
        'email' => array(
            'valPattern' => '/@/', // Must contain an @ sign
            'characterLimit'  => 254,
        ),
        'mobilePhone' => array(
            'valPattern'   => '/^(((00)?44)|08|07)/', // Permitted number prefixes: 0044, 44, 07 and 08
            'transPattern' => '/^((00)?44)?0?/',      // Strip allowed country codes and leading zero
            'transReplace' => '',
            'characterLimit'    => 15,
        ),
        'thoroughfareNumber' => array('characterLimit' => 10),
        'premisesName'       => array('characterLimit' => 50),
        'thoroughfare'       => array('characterLimit' => 80),
        'postTown'           => array('characterLimit' => 30),
        'postCode'           => array('characterLimit' => 10),
        'county'             => array('characterLimit' => 30),
        'country'            => array('characterLimit' => 50),
    );

    /**
     * Stores coredb Account Identifier
     *
     * @var integer
     */
    private $accountId;

    /**
     * Stores customer details obtained from database
     *
     * @var array
     */
    private $customerDetails;

    /**
     * Constructor
     *
     * @param integer $accountId The userdata.accounts.account_id of account to provide data for
     */
    public function __construct($accountId)
    {
        $this->accountId = $accountId;
    }

    /**
     * Main method: returns customer details required for enrichment of payment requests
     *
     * @return array Multidimensional array of arrays with the keys: cardholderContactDetails, cardholderAddress
     */
    public function getData()
    {
        $data = self::DEFAULT_DATA_RESPONSE;

        if (!empty($this->accountId) && is_numeric($this->accountId)) {
            $db = $this->getDbAdaptor();
            $this->customerDetails = $db->getCustomerDetailsFromAccountId($this->accountId);
        }

        if (!empty($this->customerDetails)) {
            $data['cardholderContactDetails'] = $this->populateCardholderContactDetails();
            $data['cardholderAddress'] = $this->populateCardholderAddress();
            $data['cardholderName'] = $this->populateCardholderName();
        }

        return $data;
    }

    /**
     * Get a database adaptor
     *
     * @return \Db_Adaptor Db_Adaptor Object
     */
    private function getDbAdaptor()
    {
        return \Db_Manager::getAdaptor('AccountChange');
    }

    /**
     * Helper function to populate the cardholderContactDetails array with valid values
     *
     * @return array Array to store in GenericImmediatePaymentApplication_PaymentRequestData::cardholderContactDetails,
     *               or instead just an empty array
     */
    private function populateCardholderContactDetails()
    {
        $this->sanitiseMobilePhone();

        $keys = array('email', 'mobilePhone');
        $contactDetails = $this->getPopulatedArrayForKeys($keys);

        return $this->enrichArrayWithMobilePhone($contactDetails);
    }

    /**
     * Removes non-digit characters such as +, -, (), and spaces from stored mobilePhone value
     *
     * @return void
     */
    private function sanitiseMobilePhone()
    {
        $this->customerDetails['mobilePhone'] = preg_replace('/\D/', '', $this->customerDetails['mobilePhone']);
    }

    /**
     * Gets array populated from requested parameter keys that have valid values in $this->customerDetails
     *
     * @param  array $parameters Array of keys to return validated key/value pairs for
     *
     * @return array             Array of keys that have valid values, or an empty array
     */
    private function getPopulatedArrayForKeys($parameters)
    {
        $details = array();
        foreach ($parameters as $key => $parameterKey) {
            $this->setArrayKeyIfValueValid($details, $parameterKey, $this->customerDetails[$parameterKey]);
        }
        return $details;
    }

    /**
     * Enriches and returns an array with its mobilePhone value converted into an array hydratable into
     * a \Plusnet\BillingApiClient\Entity\CardholderPhoneDetails object
     *
     * @param  array $array The array to enrich
     *
     * @return array Enriched array
     */
    private function enrichArrayWithMobilePhone($array)
    {
        if (!empty($array['mobilePhone'])) {
            // Due to permitted mobile suffixes, mobile country code will always be 44: i.e. UK, for valid numbers
            $array['mobilePhone'] = array(
                'cc' => 44,
                'subscriber' => (int) $array['mobilePhone'],
            );
        }

        return $array;
    }

    /**
     * Helper function to set key/value in array passed in by reference, if value is valid for that type of key
     *
     * @param array  $targetArray Array to set key/value pair in, if validation passes, passed in by reference
     * @param string $key         Key to set, and find validation rules for
     * @param string $value       Value to validate - empty() values are considered invalid
     *
     * @return void
     */
    private function setArrayKeyIfValueValid(&$targetArray, $key, $value)
    {
        if (empty($value)) {
            return;
        }

        $config = $this->getConfigForKey($key);

        if (!empty($config)) {
            $validatedValue = $this->validateAndTransform(
                $value,
                $config['valPattern'],
                $config['transPattern'],
                $config['transReplace'],
                $config['charLimit']
            );
        }

        if (!empty($validatedValue)) {
            $targetArray[$key] = $validatedValue;
        }
    }

    /**
     * Returns configuration values for the requested attribute key
     *
     * @param  string $key Named key to retrieve validation configuration for
     *
     * @return array       Array populated with config values
     */
    private function getConfigForKey($key)
    {
        $config = self::PARAMETER_KEY_CONFIG;

        $keyConfig = array();
        if (array_key_exists($key, $config)) {
            $keyConfig['valPattern'] = (empty($config[$key]['valPattern'])) ? '/./' : $config[$key]['valPattern'];
            $keyConfig['transPattern'] = (empty($config[$key]['transPattern'])) ? '' : $config[$key]['transPattern'];
            $keyConfig['transReplace'] = (empty($config[$key]['transReplace'])) ? '' : $config[$key]['transReplace'];
            $keyConfig['charLimit'] = (empty($config[$key]['characterLimit'])) ? null : $config[$key]['characterLimit'];
        }

        return $keyConfig;
    }

    /**
     * Validates subject against a pattern. Performs transformations and applies character limits as required
     *
     *
     * @param  string       $subject                   The string to validate (and possibily find and replace on)
     * @param  string       $validationPattern         The regex pattern that a valid subject string will match
     * @param  string|array $transformationPattern     Regex pattern string or array of pattern strings to find
     *                                                 in order to replace on validated strings
     * @param  string|array $transformationReplacement String or array of strings to replace your found pattern
     *                                                 matches with
     *                                                 # See PHP preg_replace() documentation for more details on
     *                                                 supported $transformation* argument values
     * @param  integer      $characterLimit            Maximum length of string to return
     *
     * @return string       Either validated string with rules applied or an empty string
     */
    private function validateAndTransform(
        $subject,
        $validationPattern,
        $transformationPattern = '',
        $transformationReplacement = '',
        $characterLimit = null
    ) {
        $subjectIsValid = preg_match($validationPattern, $subject);
        if ($subjectIsValid !== 1) {
            $subject = '';
        }

        if (!empty($subject)) {
            if (!empty($transformationPattern)) {
                $subject = preg_replace($transformationPattern, $transformationReplacement, $subject);
            }

            if (!is_null($characterLimit) && strlen($subject) > $characterLimit) {
                $subject = '';
            }
        }

        return $subject;
    }

    /**
     * Populates the cardholderAddress array with valid values
     *
     * @return array An array to store in GenericImmediatePaymentApplication_PaymentRequestData::cardholderAddress,
     *               or an empty array.
     *
     *               Parameter rule: If the value of thoroughfareNumber key is invalid, we will
     *               not return thoroughfareNumber. We will attempt to return as the value for
     *               premisesName providing the value is valid for that key
     */
    private function populateCardholderAddress()
    {
        $this->customerDetails['premisesName'] = $this->customerDetails['thoroughfareNumber'];

        $keys = array(
            'thoroughfareNumber',
            'premisesName',
            'thoroughfare',
            'postTown',
            'postCode',
            'county',
            'country',
        );
        $addressDetails = $this->getPopulatedArrayForKeys($keys);

        if (!empty($addressDetails['thoroughfareNumber']) && !empty($addressDetails['premisesName'])) {
            unset($addressDetails['premisesName']);
        }

        return $addressDetails;
    }

    /**
     * Helper function to populate the cardholderName value
     *
     * @return string String containing the cardholderName value to store in
     *                GenericImmediatePaymentApplication_PaymentRequestData::cardholderContactDetails
     */
    private function populateCardholderName()
    {
        $keys = array('name');
        $cardholderName = '';

        $dbName = $this->getPopulatedArrayForKeys($keys);
        if (!empty($dbName)) {
            $cardholderName = $dbName['name'];
        }

        return $cardholderName;
    }
}
