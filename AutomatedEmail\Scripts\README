The scripts directory stores all the scripts this application uses.

Scripts are organised primarily by how frequently they are 
designed to run. This is to ensure that scripts no longer in use 
are easy to identify. 

If scripts covering serveral different function area's exist within an application 
the top level Scripts directory may contain directories splitting the scripts
into these functional area. If scripts are split functionally ALL scripts must
be classified in this way and the generic (all scripts) directories removed and
physically deleted fro the CVS server.

The scripts position within this directory (whether functionally divided or not) 
does not effect the script's application namespace.   

The directory structure within the scripts directory is 

./[<functional area>]/Cron        -  Those scripts (optionally split by 
                                     functional area) which should be 
                                     scheduled to run on a regular basis.
					
                                     The actual frequency of these scripts 
                                     (and on which servers they run) is
                                     configured via the .cron file entries
                                     within the <ModuleRoot>/ServerRoles 
                                     directory structure.

./[<functional area>]/Maintenance -  Those scripts which are used manually on an
                                     irregular basis by developers.

./[<functional area>]/OneUse      -  Those scripts which are written for a one
                                     time task such as software upgrade or a
                                     custom cleaning up after a bugfix. These
                                     scripts are never used again and are 
                                     retained within the module for forensic 
                                     purposes only.

No scripts should be stored in this top-level directory. 
