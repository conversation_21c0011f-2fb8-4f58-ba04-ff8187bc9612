<?php

class AccountChange_LineCheckResultException extends Exception
{
    private $lineCheckErrorId;

    public function __construct($lineCheckErrorId, $message = "", $code = 0, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->lineCheckErrorId = $lineCheckErrorId;
    }

    public function getLineCheckErrorId()
    {
        return $this->lineCheckErrorId;
    }
}
