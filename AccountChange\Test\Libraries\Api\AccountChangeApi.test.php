<?php

require_once(__DIR__.'/../../../Libraries/Api/Model/AccountChangeOrder.class.php');
require_once(__DIR__.'/../../../Libraries/Api/Model/AccountChangeOrderProducts.class.php');
require_once(__DIR__.'/../../../Libraries/Api/Model/AccountChangePcics.class.php');
require_once(__DIR__.'/../../../Libraries/Api/AccountChangeApi.class.php');

class AccountChange_AccountChangeApi_Test extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 123456;
    const STREAMING_URL = 'http://test.com';
    const STREAMING_ID = '40cd98bb85fc8772c4427b2767dbf7b4';
    const ORDER_ID = '9d79f938-d7c3-4dd8-9c5a-3ed1fef5c9b8';

    public function testShouldCreatePerformChangeApiAndValidateInput()
    {
        $this->validateInput(1705);
    }

    public function testShouldCreatePerformChangeApiWithoutWlrComponentIdAndValidateInput()
    {
        $this->validateInput(null);
    }

    public function testShouldCreatePerformChangeApiWithContractAndValidateInput()
    {
        $this->validateInput(1705, true);
    }

    public function testShouldCreatePerformChangeApiWithContractWithoutWlrComponentIdAndValidateInput()
    {
        $this->validateInput(null, true);
    }

    public function testShouldCreatePerformChangeApiAndSetRecontractFlag()
    {
        $this->validateInput(null, false, true);
    }

    public function testShouldCreatePerformChangeApiAndSetInstantRecontractFlag()
    {
        $this->validateInput(null, false, true, true);
    }

    public function testShouldCreatePerformChangeApiAndAddHardware()
    {
        $this->validateInput(null, false, false, false, true);
    }

    public function testShouldCreatePerformChangeApiAndSetOneOffCharges()
    {
        $this->validateInput(null, false, false, false, false, true);
    }


    public function testShouldCreatePerformChangeApiAndSetAppointment()
    {
        $this->validateInput(null, false, false, false, true, false, "live");
    }

    public function testShouldCreatePerformChangeApiAndSetManualAppointment()
    {
        $this->validateInput(null, false, false, false, true, false, "manual");
    }

    public function testShouldCreatePerformChangeApiAndSetBackDatedDate()
    {
        $this->validateInput(null, false, false, false, false, false, null, true);
    }

    public function testShouldCreatePerformChangeApiAndSetContractOptionsForRetain()
    {
        $this->validateInput(null, false, false, false, false, false, null, true, true);
    }

    public function testShouldNotSetUpPhoneWhenBothAreTheSame()
    {
        $this->validateInput(1705, false, false, false, false, false, null, false, null, 1705);
    }

    public function testShouldSetUpPhoneWhenBothAreTheSame()
    {
        $this->validateInput(1705, false, false, false, false, false, null, false, null, 1706);
    }

    public function testShouldCreatePerformChangeApiAndSetCurrentSdi()
    {
        $this->validateInput(null, false, false, false, false, false, null, true, true, null, true, true, static::STREAMING_URL, static::STREAMING_ID, static::ORDER_ID);
    }

    private function validateInput(
        $expectedPhoneComponentId,
        $hasContract = false,
        $isRecontract = false,
        $isInstantRecontract = false,
        $hasHardware = false,
        $hasOneOffCharges = false,
        $appointment = null,
        $hasBackDatedDate = false,
        $retainContract = null,
        $currentPhoneComponentId = null,
        $currentSdi = false,
        $sentPci = null,
        $pcicsStreamingUrl = null,
        $pcicsStreamingId = null,
        $pcicsOrderId = null
    ) {
        $mockPerformChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'isAllowed',
                'doScheduledChange',
                'setNewWlrServiceComponentId',
                'setTariffIdByServiceDefinitionAndMarket',
                'setIsRecontract',
                'setIsInstantRecontract',
                'setOneOffCharges',
                'setToSdi',
                'setHardwareOption',
                'setAddress',
                'setAppointment',
                'setBackDatedDate',
                'getRetainContract',
                'setNewContractOrder',
                'getCurrentSdi',
                'setShouldSaveScheduledMGALS',
                'isRecontract'
            ),
            array()
        );

        if ($currentPhoneComponentId !== $expectedPhoneComponentId) {
            $mockPerformChangeApi
                ->expects($this->once())
                ->method('setNewWlrServiceComponentId')
                ->with($expectedPhoneComponentId);
        } else {
            $mockPerformChangeApi
                ->expects($this->never())
                ->method('setNewWlrServiceComponentId')
                ->with($expectedPhoneComponentId);
        }

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('setTariffIdByServiceDefinitionAndMarket')
            ->will($this->returnValue('2879'));

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('findServiceDefinitionIdFromServiceDefinitionComponentId',
                'getAppointmentRebookerHelper'),
            array()
        );

        $accountChangeApi
            ->expects($this->any())
            ->method('findServiceDefinitionIdFromServiceDefinitionComponentId')
            ->will($this->returnValue('********'));

        $mockPerformChangeApi
                    ->expects($this->any())
                    ->method('setToSdi')
                    ->with(6865);

        $mockPerformChangeApi->expects($this->once())
            ->method('isAllowed')
            ->will($this->returnValue(true));

        $mockPerformChangeApi->expects($this->once())
            ->method('doScheduledChange');

        if ($isRecontract) {
            $mockPerformChangeApi->expects($this->once())
                ->method('setIsRecontract');
        } else {
            $mockPerformChangeApi->expects($this->once())
                ->method('setShouldSaveScheduledMGALS')
                ->with(true);
        }

        $mockPerformChangeApi->expects($this->any())
            ->method('isRecontract')
            ->willReturn($isRecontract);

        if ($isInstantRecontract) {
            $mockPerformChangeApi->expects($this->once())
                ->method('setIsInstantRecontract');
        }

        if ($hasHardware) {
            $mockPerformChangeApi->expects($this->once())
                ->method('setHardwareOption')
                ->with('handle');
        }

        if ($hasOneOffCharges) {
            $mockPerformChangeApi->expects($this->once())
                ->method('setOneOffCharges');
        }

        if (!is_null($appointment)) {
            $mockPerformChangeApi->expects($this->once())
                ->method('setAppointment');
        }

        if ($hasBackDatedDate) {
            $mockPerformChangeApi->expects($this->once())
                ->method('setBackDatedDate');
        }

        $mockPerformChangeApiFactory = $this->getMock(
            'AccountChange_PerformChangeApiFactory',
            array('createPerformChangeApi'),
            array()
        );

        $mockPerformChangeApiFactory->expects($this->once())
            ->method('createPerformChangeApi')
            ->will($this->returnValue($mockPerformChangeApi));

        $mockEmailHandlerFactory = $this->getValidEmailHandlerMock();

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(6865);

        $accountChangeOrder = $this->getValidAccountChangeOrderMock(
            $expectedPhoneComponentId,
            $hasBackDatedDate,
            $currentPhoneComponentId,
            $sentPci,
            $pcicsStreamingUrl,
            $pcicsStreamingId,
            $pcicsOrderId
        );

        $dbMock = $this->getMock(
            'Db_Adaptor',
            array('getAccountIdByServiceId'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $dbMock->expects($this->once())
            ->method('getAccountIdByServiceId')
            ->will($this->returnValue(STREAMING_ID));

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('getAccountId'),
            array(STREAMING_ID)
        );

        $accountChangeApi
            ->expects($this->any())
            ->method('getAccountId')
            ->will($this->returnValue(STREAMING_ID));

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('getEmailHandlerFactory',
                'getSchedulingHelper',
                'getExistingProductDetails',
                'getAppointmentRebookerHelper',
                'getAppointmentSavingHelper',
                'getBusinessActor'),
            array($mockPerformChangeApiFactory)
        );

        $mockBusinessActor = $this->getMock(
            'Auth_BusinessActor',
            array(),
            array(),
            '',
            false
        );

        if($sentPci) {
            $this->getPcicsCreateServiceNoticeMock(true, $mockBusinessActor);

            $this->getPcicsUpddateJourneyCompletionStatusMock(true);

            $accountChangeApi->expects($this->once())
                ->method('getBusinessActor')
                ->will($this->returnValue($mockBusinessActor));
        } else {
            $accountChangeApi->expects($this->never())
                ->method('getBusinessActor');
        }

        $accountChangeApi
            ->expects($this->atMost(1))
            ->method('getSchedulingHelper')
            ->will($this->returnValue($this->getSchedulingHelper()));

        if ($hasContract) {
            $contract = $this->getMock(
                'AccountChange_AccountChangeOrderContract',
                array('getType', 'getSubType', 'getAgreementDate', 'getLength'),
                array()
            );

            $accountChangeOrder->setContract($contract);

            $contract->expects($this->once())->method('getType');
            $contract->expects($this->once())->method('getSubType');
            $contract->expects($this->once())->method('getAgreementDate');
            $contract->expects($this->once())->method('getLength');

            $mockPerformChangeApi->expects($this->once())
                ->method('setNewContractOrder')
                ->with(true);
        } else {
            $mockPerformChangeApi->expects($this->never())
                ->method('setNewContractOrder');
        }

        $accountChangeOrder->setRetainCurrentContracts($retainContract);

        $accountChangeOrder->setIsRecontract($isRecontract);

        if (!is_null($appointment)) {
            $mockAppointment = $this->getMock(
                'AccountChange_AccountChangeAppointment',
                array('getManualAppointment',
                    'getLiveAppointment'),
                array(),
                '',
                false
            );

            $mockAppointment->expects($this->once())
                ->method('getManualAppointment')
                ->will($this->returnValue($appointment === 'manual'));

            if ($appointment === 'manual') {
                $mockRebooker = $this->getMock(
                    'AccountChange_AppointmentRebookerHelper',
                    array('attemptToRebookFromManualAppointment'),
                    array(),
                    '',
                    false
                );

                $mockRebooker->expects($this->once())
                    ->method("attemptToRebookFromManualAppointment");

                $accountChangeApi
                    ->expects($this->once())
                    ->method('getAppointmentRebookerHelper')
                    ->will($this->returnValue($mockRebooker));
            } elseif ($appointment === 'live') {
                $mockAppointment->expects($this->once())
                    ->method('getLiveAppointment')
                    ->will($this->returnValue($appointment === 'live'));

                $mockSavingHelper = $this->getMock(
                    'AccountChange_AppointmentSavingHelper',
                    array('saveLiveAppointmentToDb'),
                    array(),
                    '',
                    false
                );

                $mockSavingHelper->expects($this->once())
                    ->method('saveLiveAppointmentToDb');

                $accountChangeApi
                    ->expects($this->once())
                    ->method('getAppointmentSavingHelper')
                    ->will($this->returnValue($mockSavingHelper));
            }

            $accountChangeOrder->setAppointment($mockAppointment);
        }
        $accountChangeOrder->setIsInstantRecontract($isInstantRecontract);

        $accountChangeApi
            ->expects($this->once())
            ->method('getEmailHandlerFactory')
            ->will($this->returnValue($mockEmailHandlerFactory));

        if ($currentSdi) {
            $mockPerformChangeApi
                ->expects($this->exactly(2))
                ->method('getCurrentSdi')
                ->will($this->returnValue(null));

            $accountChangeApi
                ->expects($this->once())
                ->method('getExistingProductDetails')
                ->will($this->returnValue(['broadbandDetails' => ['service_definition_id' => '3']]));
        } else {
            $matcher = $isRecontract ? $this->once() : $this->exactly(2);
            $mockPerformChangeApi
                ->expects($matcher)
                ->method('getCurrentSdi')
                ->will($this->returnValue(1));
        }

        $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
    }

    public function testShouldValidateInputWithNoChangeInPhone()
    {
        $mockPerformChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('isAllowed', 'doScheduledChange', 'setTariffIdByServiceDefinitionAndMarket', 'setToSdi','getCurrentSdi'),
            array()
        );

        $mockPerformChangeApi
            ->expects($this->exactly(2))
            ->method('getCurrentSdi')
            ->will($this->returnValue(1));

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('findServiceDefinitionIdFromServiceDefinitionComponentId'),
            array()
        );

        $accountChangeApi
            ->expects($this->any())
            ->method('findServiceDefinitionIdFromServiceDefinitionComponentId')
            ->will($this->returnValue('********'));

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('setTariffIdByServiceDefinitionAndMarket')
            ->will($this->returnValue('2879'));

        $mockPerformChangeApi
            ->expects($this->any())
            ->method('setToSdi')
            ->with(6865);

        $mockPerformChangeApi->expects($this->once())
            ->method('isAllowed')
            ->will($this->returnValue(true));

        $mockPerformChangeApi->expects($this->once())
            ->method('doScheduledChange');

        $mockPerformChangeApiFactory = $this->getMock(
            'AccountChange_PerformChangeApiFactory',
            array('createPerformChangeApi'),
            array()
        );

        $mockEmailHandlerFactory = $this->getValidEmailHandlerMock();

        $mockPerformChangeApiFactory->expects($this->once())
            ->method('createPerformChangeApi')
            ->will($this->returnValue($mockPerformChangeApi));

        $accountChangeOrder = $this->getValidAccountChangeOrderMock();

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('getEmailHandlerFactory',
                'getSchedulingHelper'),
            array($mockPerformChangeApiFactory)
        );

        $accountChangeApi
            ->expects($this->once())
            ->method('getSchedulingHelper')
            ->will($this->returnValue($this->getSchedulingHelper()));

        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array('isValid'),
            array()
        );

        $mockAddress->expects($this->atMost(1))
            ->method('isValid')
            ->will($this->returnValue(true));

        $accountChangeOrder->setAddress($mockAddress);

        $accountChangeApi
            ->expects($this->once())
            ->method('getEmailHandlerFactory')
            ->will($this->returnValue($mockEmailHandlerFactory));

        $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
    }

    /**
     * Test that doInstantChange is called if the appropriate flag is set
     *
     * @return void
     */
    public function testShouldValidateInputAndRunInstantChange()
    {
        $mockPerformChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('isAllowed',
                'doInstantChange',
                'setTariffIdByServiceDefinitionAndMarket',
                'setToSdi',
                'getCurrentSdi'),
            array()
        );

        $mockPerformChangeApi
            ->expects($this->exactly(2))
            ->method('getCurrentSdi')
            ->will($this->returnValue(1));

        $mockPerformChangeApi
            ->expects($this->never())
            ->method('doScheduledChange');

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('isAllowed')
            ->will($this->returnValue(true));

        $mockPerformChangeApiFactory = $this->getMock(
            'AccountChange_PerformChangeApiFactory',
            array('createPerformChangeApi'),
            array()
        );

        $mockPerformChangeApiFactory->expects($this->once())
            ->method('createPerformChangeApi')
            ->will($this->returnValue($mockPerformChangeApi));

        /** @var AccountChange_AccountChangeOrder|PHPUnit_Framework_MockObject_MockObject $accountChangeOrder */
        $accountChangeOrder = $this->getValidAccountChangeOrderMock();
        $accountChangeOrder->setIsScheduledChange(false);
        $mockEmailHandlerFactory = $this->getValidEmailHandlerMock();

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('getEmailHandlerFactory',
                'getSchedulingHelper'),
            array($mockPerformChangeApiFactory)
        );

        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array('isValid'),
            array()
        );

        $mockAddress->expects($this->atMost(1))
            ->method('isValid')
            ->will($this->returnValue(true));

        $accountChangeApi
            ->expects($this->once())
            ->method('getSchedulingHelper')
            ->will($this->returnValue($this->getSchedulingHelper()));

        $accountChangeOrder->setAddress($mockAddress);

        $accountChangeApi
            ->expects($this->once())
            ->method('getEmailHandlerFactory')
            ->will($this->returnValue($mockEmailHandlerFactory));
        $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
    }

    /**
     * Test that doInstantChange is called if the appropriate flag is set
     *
     * @return void
     */
    public function testShouldValidateInputAndRunInstantChangeWithSnapshot()
    {
        $mockPerformChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'isAllowed',
                'doInstantChange',
                'setTariffIdByServiceDefinitionAndMarket',
                'setToSdi',
                'getCurrentSdi'
            ),
            array()
        );

        $mockPerformChangeApi
            ->expects($this->exactly(2))
            ->method('getCurrentSdi')
            ->will($this->returnValue(1));

        $mockPerformChangeApi
            ->expects($this->never())
            ->method('doScheduledChange');

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('isAllowed')
            ->will($this->returnValue(true));

        $mockPerformChangeApiFactory = $this->getMock(
            'AccountChange_PerformChangeApiFactory',
            array('createPerformChangeApi'),
            array()
        );

        $mockPerformChangeApiFactory->expects($this->once())
            ->method('createPerformChangeApi')
            ->will($this->returnValue($mockPerformChangeApi));

        /** @var AccountChange_AccountChangeOrder|PHPUnit_Framework_MockObject_MockObject $accountChangeOrder */
        $accountChangeOrder = $this->getValidAccountChangeOrderMock();
        $accountChangeOrder->setIsScheduledChange(false);
        $mockEmailHandlerFactory = $this->getValidEmailHandlerMock();
        $accountChangeOrder->setRequiresSnapshot(true);

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('getEmailHandlerFactory',
                'getInstantChangeHelper',
                'getSchedulingHelper'),
            array($mockPerformChangeApiFactory)
        );

        $accountChangeApi
            ->expects($this->once())
            ->method('getSchedulingHelper')
            ->will($this->returnValue($this->getSchedulingHelper()));

        $instantChangeHelper = $this->getMock(
            'AccountChange_InstantChangeHelper',
            array('takePreChangeSnapshot','takePostChangeSnapshot'),
            array(),
            '',
            false
        );

        $accountChangeApi
            ->expects($this->once())
            ->method('getEmailHandlerFactory')
            ->will($this->returnValue($mockEmailHandlerFactory));

        $instantChangeHelper
            ->expects($this->once())
            ->method('takePreChangeSnapshot');

        $instantChangeHelper
            ->expects($this->once())
            ->method('takePostChangeSnapshot');

        $accountChangeApi
            ->expects($this->once())
            ->method('getInstantChangeHelper')
            ->with($accountChangeOrder, $mockPerformChangeApi)
            ->willReturn($instantChangeHelper);

        $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
    }

    /**
     * @throws AccountChange_AccountChangeApiFailureException
     * @throws AccountChange_AccountChangeNotAllowedException
     * @throws AccountChange_InvalidAccountChangeOrderException
     *
     * @expectedException AccountChange_AccountChangeNotAllowedException
     */
    public function testExceptionIsThrownWhenAccountChangeIsNotAllowed()
    {
        $mockPerformChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('isAllowed',
                'setTariffIdByServiceDefinitionAndMarket',
                'getCurrentSdi'),
            array()
        );

        $mockPerformChangeApi
            ->expects($this->exactly(2))
            ->method('getCurrentSdi')
            ->will($this->returnValue(1));

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('isAllowed')
            ->will($this->returnValue(false));

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('setTariffIdByServiceDefinitionAndMarket')
            ->will($this->returnValue('2879'));

        $mockPerformChangeApi
            ->expects($this->any())
            ->method('setToSdi')
            ->with(6865);

        $mockPerformChangeApiFactory = $this->getMock(
            'AccountChange_PerformChangeApiFactory',
            array('createPerformChangeApi'),
            array()
        );

        $mockPerformChangeApiFactory
            ->expects($this->once())
            ->method('createPerformChangeApi')
            ->will($this->returnValue($mockPerformChangeApi));

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(6865);
        $accountChangeOrderProducts->addServiceComponentId(AccountChange_AccountChangeOrderProducts::PHONE_COMPONENT_ID, 1705);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('validate', 'getConsentValue'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('validate')
            ->will($this->returnValue(null));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getConsentValue')
            ->will($this->returnValue(null));

        $accountChangeOrder->setType(AccountChange_AccountChangeOrder::TYPE_MY_OFFERS_RE_CONTRACT);
        $accountChangeOrder->setServiceId(123456);
        $accountChangeOrder->setProducts($accountChangeOrderProducts);

        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array('isValid'),
            array()
        );

        $mockAddress->expects($this->atMost(1))
            ->method('isValid')
            ->will($this->returnValue(true));

        $accountChangeOrder->setAddress($mockAddress);

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('findServiceDefinitionIdFromServiceDefinitionComponentId',
                'getSchedulingHelper'),
            array($mockPerformChangeApiFactory)
        );

        $accountChangeApi
            ->expects($this->any())
            ->method('findServiceDefinitionIdFromServiceDefinitionComponentId')
            ->will($this->returnValue('********'));

        $accountChangeApi
            ->expects($this->once())
            ->method('getSchedulingHelper')
            ->will($this->returnValue($this->getSchedulingHelper()));

        $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
    }

    /**
     * @throws AccountChange_AccountChangeApiFailureException
     * @throws AccountChange_AccountChangeNotAllowedException
     * @throws AccountChange_InvalidAccountChangeOrderException
     *
     * @expectedException AccountChange_AccountChangeApiFailureException
     */
    public function testExceptionIsThrownWhenAccountChangeFails()
    {
        $mockPerformChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('isAllowed',
                'doScheduledChange',
                'setTariffIdByServiceDefinitionAndMarket',
                'setToSdi',
                'getCurrentSdi'),
            array()
        );

        $mockPerformChangeApi
            ->expects($this->exactly(2))
            ->method('getCurrentSdi')
            ->will($this->returnValue(1));

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('setTariffIdByServiceDefinitionAndMarket')
            ->will($this->returnValue('2879'));

        $mockPerformChangeApi
            ->expects($this->any())
            ->method('setToSdi')
            ->with(6865);

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('isAllowed')
            ->will($this->returnValue(true));

        $mockPerformChangeApi
            ->expects($this->once())
            ->method('doScheduledChange')
            ->will($this->throwException(new Exception()));

        $mockPerformChangeApiFactory = $this->getMock(
            'AccountChange_PerformChangeApiFactory',
            array('createPerformChangeApi'),
            array()
        );

        $mockPerformChangeApiFactory
            ->expects($this->once())
            ->method('createPerformChangeApi')
            ->will($this->returnValue($mockPerformChangeApi));

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(6865);
        $accountChangeOrderProducts->addServiceComponentId(AccountChange_AccountChangeOrderProducts::PHONE_COMPONENT_ID, 1705);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('validate', 'getConsentValue'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('validate')
            ->will($this->returnValue(null));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getConsentValue')
            ->will($this->returnValue(null));

        $accountChangeOrder->setType(AccountChange_AccountChangeOrder::TYPE_MY_OFFERS_RE_CONTRACT);
        $accountChangeOrder->setServiceId(123456);
        $accountChangeOrder->setProducts($accountChangeOrderProducts);

        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array('isValid'),
            array()
        );

        $mockAddress->expects($this->atMost(1))
            ->method('isValid')
            ->will($this->returnValue(true));

        $accountChangeOrder->setAddress($mockAddress);

        $accountChangeApi = $this->getMock(
            'AccountChange_AccountChangeApi',
            array('findServiceDefinitionIdFromServiceDefinitionComponentId',
                'getSchedulingHelper'),
            array($mockPerformChangeApiFactory)
        );

        $accountChangeApi
            ->expects($this->once())
            ->method('getSchedulingHelper')
            ->will($this->returnValue($this->getSchedulingHelper()));

        $accountChangeApi
            ->expects($this->any())
            ->method('findServiceDefinitionIdFromServiceDefinitionComponentId')
            ->will($this->returnValue('********'));

        $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
    }

    /**
     * Return a reusable AccountChangeOrder Mock object
     *
     * @param int    $expectedPhoneComponentId Phone component ID
     * @param bool   $hasBackDatedDate         is there a backdated date
     * @param int    $currentPhoneComponentId  Old Phone component ID
     * @param bool   $sentPcics                PCI-CS notification sent status
     * @param string $pcicsStreamingUrl        PCI-CS StreamingUrl
     * @param string $pcicsStreamingId         PCI-CS StreamingId
     * @param string $pcicsOrderId             PCI-CS OrderId
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getValidAccountChangeOrderMock(
        $expectedPhoneComponentId = null,
        $hasBackDatedDate = false,
        $currentPhoneComponentId = null,
        $sentPcics = null,
        $pcicsStreamingUrl = null,
        $pcicsStreamingId = null,
        $pcicsOrderId = null
    )
    {
        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount','getValidationCheck'),
            array()
        );

        $validationCheckMock = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed'),
            array(),
            '',
            false
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->will($this->returnValue(false));

        if ($hasBackDatedDate) {
            $expectedValidators = [
                'AccountChange_BackDatedDatePolicy' => AccountChange_BackDatedDatePolicy::class,
                'AccountChange_BillingPendingReratingPolicy' => AccountChange_BillingPendingReratingPolicy::class
            ];

            $accountChangeOrder
                ->expects($this->once())
                ->method('getValidationCheck')
                ->with($expectedValidators)
                ->willReturn($validationCheckMock);

            $validationCheckMock
                ->expects($this->once())
                ->method('isAccountChangeAllowed')
                ->willReturn(true);

            $accountChangeOrder->setBackDatedDate('2021-09-21');
        }

        $accountChangeOrder->setOldPhoneId($currentPhoneComponentId);

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(6865);
        if ($expectedPhoneComponentId) {
            $accountChangeOrderProducts->addServiceComponentId(
                AccountChange_AccountChangeOrderProducts::PHONE_COMPONENT_ID,
                $expectedPhoneComponentId
            );
        }
        $accountChangeOrderProducts->addServiceComponentId(
            AccountChange_AccountChangeOrderProducts::HARDWARE_COMPONENT_ID,
            123
        );

        $accountChangeOrder->setType(AccountChange_AccountChangeOrder::TYPE_MY_OFFERS_RE_CONTRACT);
        $accountChangeOrder->setServiceId(123456);
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setOneOffCharges(
            array(
                array(
                    'name' => 'test',
                    'amount' => 12,
                    'amountExVat' => 10
                )
            )
        );

        if (isset($sentPcics)) {
            $pcics = new AccountChange_AccountChangePcics();
            $pcics->setNotificationSent($sentPcics);
            $pcics->setStreamingUrl($pcicsStreamingUrl);
            $pcics->setStreamingId($pcicsStreamingId);
            $pcics->setOrderId($pcicsOrderId);

            $accountChangeOrder->setPcics($pcics);
        }

        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array('isValid'),
            array()
        );

        $mockAddress->expects($this->atMost(1))
            ->method('isValid')
            ->will($this->returnValue(true));

        $accountChangeOrder->setAddress($mockAddress);

        $mockHardwareClient = $this->getMockBuilder(HardwareClient_Client::class)
            ->disableOriginalConstructor()
            ->setMethods(['getHandleByServiceComponentId'])
            ->getMock();

        $mockHardwareClient->expects($this->once())
            ->method('getHandleByServiceComponentId')
            ->with(123)
            ->willReturn('handle');

        BusTier_BusTier::setClient('hardware', $mockHardwareClient);

        $accountChangeOrder->addConsent(AccountChange_AccountChangeOrder::CONSENT_L2C, "codeFromL2C");
        return $accountChangeOrder;
    }

    /**
     * Return a reusable EmailHandler mock object
     *
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getValidEmailHandlerMock()
    {
        $mockEmailHandler = $this->getMock(
            'AccountChange_ConfirmationEmailHandler',
            array('sendEmail'),
            array()
        );

        $mockEmailHandler->expects($this->once())
            ->method('sendEmail')
            ->with(123456);

        $mockEmailHandlerFactory = $this->getMock(
            'AccountChange_EmailHandlerFactory',
            array('buildConfirmationEmailForPerformChangeApi'),
            array()
        );

        $mockEmailHandlerFactory->expects($this->once())
            ->method('buildConfirmationEmailForPerformChangeApi')
            ->will($this->returnValue([$mockEmailHandler]));
        return $mockEmailHandlerFactory;
    }

    /**
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getCoreMock()
    {
        return $this->getMockBuilder('Core_Service')
            ->disableOriginalConstructor()
            ->getMock();
    }

    /**
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getSchedulingHelper()
    {
        return $this->getMockBuilder('AccountChange_SchedulingHelper')
            ->setMethods(['calculateChangeDateByOrder'])
            ->getMock();
    }

    /**
     * Get the mock for PcicsCreateServiceNotice
     *
     * @param bool               $isSuccess         Is it for success
     * @param Auth_BusinessActor $mockBusinessActor Mock object for business actor
     *
     * @return void
     */
    private function getPcicsCreateServiceNoticeMock($isSuccess, $mockBusinessActor)
    {
        if($isSuccess) {
            $ServiceNoticeMessage = "PCI/CS was generated for this customer on the above date.\n\n"
                . "PDF link: http://test.com\n" . "Email sent: Yes\n";
        } else {
            $ServiceNoticeMessage = "PCI/CS failed to generate for this customer on the above date.\n\n"
                . "PDF link: Not generated\n" . "Email sent: No\n";
        }

        $mockSnType = $this->getMock(
            'ServiceNoticeClient_DbServiceNoticeType',
            array('getServiceNoticeTypeId'),
            array(),
            '',
            false
        );

        $mockSnType->expects($this->once())
            ->method('getServiceNoticeTypeId')
            ->will($this->returnValue(1));

        $mockSn = $this->getMock(
            'ServiceNoticeClient_ServiceNotice',
            array('comment'),
            array(),
            '',
            false
        );

        $mockSn->expects($this->once())
            ->method('comment')
            ->with(
                $this->equalTo($mockBusinessActor),
                $this->equalTo($ServiceNoticeMessage),
                $this->equalTo(1)
            );

        $mockSnClient = $this->getMock(
            'ServiceNoticeClient_ServiceNoticeClient',
            array(
                'getNoticeTypeByHandle',
                'createServiceNotice'
            ),
            array(),
            '',
            false
        );

        $mockSnClient->expects($this->once())
            ->method('getNoticeTypeByHandle')
            ->will($this->returnValue($mockSnType));

        $mockSnClient->expects($this->once())
            ->method('createServiceNotice')
            ->will($this->returnValue($mockSn));

        BusTier_BusTier::setClient('serviceNotices', $mockSnClient);
    }
    /**
     * Get the mock for UpdateJourneyCompletionStatus
     *
     * @param bool               $shouldCallClient         Is it for clientcall
     *
     * @return void
     */
    private function getPcicsUpddateJourneyCompletionStatusMock($shouldCallClient){
        $mockPdfClient = $this->getMock(
            'PCICSPDFGenerationClient',
            array('updateJourneyCompletionStatus'),
            array(),
            '',
            false
        );
        if ($shouldCallClient) {
            $mockPdfClient->expects($this->once())
                ->method('updateJourneyCompletionStatus');
        } else {
            $mockPdfClient->expects($this->never())
                ->method('updateJourneyCompletionStatus');
        }

        \BusTier_BusTier::setClient('pdfgenClient', $mockPdfClient);
    }
}
