<?php

/**
 * Action Discounts
 *
 * Testing class for the AccountChange_Action_Discounts class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 PlusNet
 */

use Plusnet\PromotionTools\Model\DiscountSummary;
use \Plusnet\C2mApiClient\Entity\Promotion;
use \Plusnet\C2mApiClient\Entity\Discount;
use \Plusnet\C2mApiClient\Entity\DiscountValue;

/**
 * Action Discounts Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 PlusNet
 */
class AccountChange_Action_Discounts_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Utility function to create a mocked Core_ServiceDefinition
     *
     * @param boolean $isValueFamily
     * @param boolean $isBpr09Family
     * @param boolean $isJlpFamily
     * @param boolean $isRes2012Family
     *
     * @return Core_ServiceDefintion mock
     */
    protected function getmockServiceDefinition(
        $isValueFamily      = false,
        $isBpr09Family      = false,
        $isJlpFamily        = false,
        $isRes2012Family    = false
    ) {
        $mockServiceDefinition = $this->getMock(
            'Core_ServiceDefinition',
            array(
                'isBpr09FamilyProduct',
                'isValueFamilyProduct',
                'isJohnLewisFamilyProduct',
                'isRes2012FamilyProduct'
            ),
            array(),
            '',
            false
        );

        $mockServiceDefinition
            ->expects($this->any())
            ->method('isBpr09FamilyProduct')
            ->will($this->returnValue($isBpr09Family));

        $mockServiceDefinition
            ->expects($this->any())
            ->method('isValueFamilyProduct')
            ->will($this->returnValue($isValueFamily));

        $mockServiceDefinition
            ->expects($this->any())
            ->method('isJohnLewisFamilyProduct')
            ->will($this->returnValue($isJlpFamily));

        $mockServiceDefinition
            ->expects($this->any())
            ->method('isRes2012FamilyProduct')
            ->will($this->returnValue($isRes2012Family));

        return $mockServiceDefinition;
    }

    /**
     * Reset the account change registry before we start.
     */
    public function setUp()
    {
        AccountChange_Registry::instance()->reset();
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intOldServiceDefinitionId', 1);
        $registry->setEntry('intNewServiceDefinitionId', 2);
        $registry->setEntry('intAccountChangeOperation', AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE);
    }

    /**
     * Ensure discounts are not touched for WLR only changes
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testDiscountsNotRemovedOnWlrOnlyChange()
    {
        // Same SDI indicates a WLR only change
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intNewServiceDefinitionId', 1);
        $registry->setEntry('newWlrServiceComponentId', 1);
        $registry->setEntry('oldWlrServiceComponentId', 2);

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('cancelDiscounts'),
            array(123456)
        );

        $mock->expects($this->never())->method('cancelDiscounts');

        $mock->execute();
    }

    /**
     * Ensure only pending ADSL discounts are removed
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testOnlyPendingADSLDiscountRemoved()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('oldAdslComponentId', 1);
        $registry->setEntry('newAdslComponentId', 2);

        $mockServiceDefinition = $this->getmockServiceDefinition();

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(['hasAutoContracts'])
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily->expects($this->once())->method('hasAutoContracts')->willReturn(false);

        $mock = $this->getMock('AccountChange_Action_Discounts',
            ['cancelDiscounts', 'getServiceDefinition', 'getProductFamily'],
            [123456]
        );

        $mock->expects($this->any())->method('getServiceDefinition')->willReturn($mockServiceDefinition);

        $mock->expects($this->once())->method('cancelDiscounts')->with('downgrade', [1]);

        $mock->expects($this->once())->method('getProductFamily')->willReturn($mockFamily);

        $mock->execute();
    }

    /**
     * Ensure discounts are removed if it's a value family product.
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testDiscountsRemovedOnValueFamilyProduct()
    {
        $mockServiceDefinition = $this->getmockServiceDefinition(true, false);

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(true));

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('cancelDiscounts', 'getServiceDefinition', 'getProductFamily'),
            array(123456)
        );

        $mock
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock
            ->expects($this->once())
            ->method('cancelDiscounts')
            ->with($this->equalTo('accountchange'));

        $mock
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $mock->execute();
    }

    /**
     * Ensure discounts are removed if it's a BPR09 value family prdouct.
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testDiscountsRemovedOnBpr09ValueFamilyProduct()
    {
        $mockServiceDefinition = $this->getmockServiceDefinition(false, true);

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(true));

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('cancelDiscounts', 'getServiceDefinition', 'getProductFamily'),
            array(123456)
        );

        $mock
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock
            ->expects($this->once())
            ->method('cancelDiscounts')
            ->with($this->equalTo('accountchange'));

        $mock
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $mock->execute();
    }

    /**
     * Ensure discounts are removed if it's a JLP family prdouct.
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testDiscountsRemovedOnJLPFamilyProduct()
    {
        $mockServiceDefinition = $this->getmockServiceDefinition(false, false, true);

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(true));

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('cancelDiscounts', 'getServiceDefinition', 'getProductFamily'),
            array(123456)
        );

        $mock
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock
            ->expects($this->once())
            ->method('cancelDiscounts')
            ->with($this->equalTo('accountchange'));

        $mock
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $mock->execute();
    }

    /**
     * Ensure discounts are removed if it's a Falcon/Residential Refresh 2012 product
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testDiscountsRemovedOnResRefresh2012Product()
    {
        $mockServiceDefinition = $this->getmockServiceDefinition(false, false, false, true);

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(true));

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('cancelDiscounts', 'getServiceDefinition', 'getProductFamily'),
            array(123456)
        );

        $mock
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock
            ->expects($this->once())
            ->method('cancelDiscounts')
            ->with($this->equalTo('accountchange'));

        $mock
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $mock->execute();
    }

    /**
     * Ensure discounts are removed if it's a downgrade.
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testDiscountsRemovedOnDowngrade()
    {
        $mockServiceDefinition = $this->getmockServiceDefinition();

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->once())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('cancelDiscounts', 'getServiceDefinition', 'getProductFamily'),
            array(123456)
        );

        $mock
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock
            ->expects($this->once())
            ->method('cancelDiscounts')
            ->with($this->equalTo('downgrade'));

        $mock
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $mock->execute();
    }

    /**
     * Ensure discounts do not get removed if it is neither a value family product nor a downgrade
     *
     * @covers AccountChange_Action_Discounts::execute
     */
    public function testDiscountsNotRemovedIfNotValueOrDowngrade()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intAccountChangeOperation', AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE);

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->once())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));

        $mockServiceDefinition = $this->getmockServiceDefinition();

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('cancelDiscounts', 'getServiceDefinition', 'getProductFamily'),
            array(123456)
        );

        $mock
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock
            ->expects($this->never())
            ->method('cancelDiscounts');

        $mock
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $mock->execute();
    }

    /**
     * Ensure discount remove will actually remove discounts.
     *
     * @covers AccountChange_Action_Discounts::execute
     * @covers AccountChange_Action_Discounts::cancelDiscounts
     *
     * @medium
     *
     * @return null
     */
    public function testCancelDiscountsCancelsDiscounts()
    {
        $mockServiceDefinition = $this->getmockServiceDefinition();

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->once())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));

        $mock = $this->getMock('AccountChange_Action_Discounts',
            array('getServiceDefinition', 'getDiscountManager', 'getProductFamily'),
            array(123456)
        );

        $mock
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $discountManagerMock = $this->getMock(
            'Retention_DiscountManager',
            array('getServiceDiscounts', 'saveDiscountArray'),
            array(),
            '',
            false
        );

        $mock
            ->expects($this->any())
            ->method('getDiscountManager')
            ->will($this->returnValue($discountManagerMock));

        $discountsMock = $this->getMock(
            'Retention_DiscountArray',
            array('cancelAll', 'count'),
            array(),
            '',
            false
        );

        // Result of cancel all should be passed to saveDiscountArray to ensure cancelling
        $discountsMock
            ->expects($this->once())
            ->method('cancelAll')
            ->will($this->returnValue(123));

        $discountsMock
            ->expects($this->any())
            ->method('count')
            ->will($this->returnValue(1));

        // Make sure we get pending discounts for given service ID
        $discountManagerMock
            ->expects($this->once())
            ->method('getServiceDiscounts')
            ->with($this->equalTo(123456), $this->equalTo('pending'))
            ->will($this->returnValue($discountsMock));

        $discountManagerMock
            ->expects($this->once())
            ->method('saveDiscountArray')
            ->with($this->equalTo(123));

        $mock->execute();
    }

    public function testC2MDiscountsAreCreatedIfC2MCodeSupplied()
    {
        $actionDiscounts = $this->getMock('AccountChange_Action_Discounts',
            array(
                'getServiceDefinition',
                'getDiscountManager',
                'getProductFamily',
                'getPromotionToolService',
                'addCustomerNoteToAccountForPromotion',
                'getActivePromotionWithPromocode',
            ),
            array(123456)
        );

        $this->setupMocksForC2MPromotionTest($actionDiscounts);

        $actionDiscounts->expects($this->once())
            ->method('getActivePromotionWithPromocode')
            ->will($this->returnValue(false));

        $actionDiscounts->expects($this->once())
            ->method('addCustomerNoteToAccountForPromotion')
            ->with($this->anything(), 'c2mPromotionCode', $this->anything());

        $actionDiscounts->execute();
    }

    public function testTicketIsRaisedWhenC2MPromotionHasABasicCashbackDiscount()
    {
        $actionDiscounts = $this->getMock('AccountChange_Action_Discounts',
            array(
                'getServiceDefinition',
                'getDiscountManager',
                'getProductFamily',
                'getPromotionToolService',
                'addCustomerNoteToAccountForPromotion',
                'getActivePromotionWithPromocode',
                'getTicketCommentForDisplayName',
                'raiseTicket'
            ),
            array(123456)
        );

        $this->setupMocksForC2MPromotionTest($actionDiscounts);

        $discountValue = new DiscountValue();
        $discountValue->setValue(50);

        $discount = new Discount();
        $discount->setDiscountValues([$discountValue]);
        $discount->setType('CASH_BACK');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount]);

        $actionDiscounts->expects($this->once())
            ->method('getActivePromotionWithPromocode')
            ->will($this->returnValue($promotion));

        $actionDiscounts->expects($this->once())
            ->method('getTicketCommentForDisplayName')
            ->with('Cashback-50')
            ->will($this->returnValue(['txtTicketComment' => 'CUSTOMER IS ELIGIBLE FOR CASHBACK', 'intCauseID' => 'ABC']));

        $actionDiscounts->expects($this->once())
            ->method('raiseTicket')
            ->with('CUSTOMER IS ELIGIBLE FOR CASHBACK', 'CSC', true, 'ABC');

        $actionDiscounts->execute();
    }

    public function testTicketIsRaisedWhenC2MPromotionHasARewardCardCashbackDiscount()
    {
        $actionDiscounts = $this->getMock('AccountChange_Action_Discounts',
            array(
                'getServiceDefinition',
                'getDiscountManager',
                'getProductFamily',
                'getPromotionToolService',
                'addCustomerNoteToAccountForPromotion',
                'getActivePromotionWithPromocode',
                'getTicketCommentForDisplayName',
                'raiseTicket'
            ),
            array(123456)
        );

        $this->setupMocksForC2MPromotionTest($actionDiscounts);

        $discountValue = new DiscountValue();
        $discountValue->setValue(50);

        $discount = new Discount();
        $discount->setDiscountValues([$discountValue]);
        $discount->setType('CASH_BACK');
        $discount->setSubType('REWARD_CARD');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount]);

        $actionDiscounts->expects($this->once())
            ->method('getActivePromotionWithPromocode')
            ->will($this->returnValue($promotion));

        $actionDiscounts->expects($this->once())
            ->method('getTicketCommentForDisplayName')
            ->with('Reward-Plusnet-50')
            ->will($this->returnValue(['txtTicketComment' => 'CUSTOMER IS ELIGIBLE FOR CASHBACK', 'intCauseID' => 'ABC']));

        $actionDiscounts->expects($this->once())
            ->method('raiseTicket')
            ->with('CUSTOMER IS ELIGIBLE FOR CASHBACK', 'CSC', true, 'ABC');

        $actionDiscounts->execute();
    }

    public function testTicketIsRaisedWhenC2MPromotionHasAnAmazonVoucherCashbackDiscount()
    {
        $actionDiscounts = $this->getMock('AccountChange_Action_Discounts',
            array(
                'getServiceDefinition',
                'getDiscountManager',
                'getProductFamily',
                'getPromotionToolService',
                'addCustomerNoteToAccountForPromotion',
                'getActivePromotionWithPromocode',
                'getTicketCommentForDisplayName',
                'raiseTicket'
            ),
            array(123456)
        );

        $this->setupMocksForC2MPromotionTest($actionDiscounts);

        $discountValue = new DiscountValue();
        $discountValue->setValue(50);

        $discount = new Discount();
        $discount->setDiscountValues([$discountValue]);
        $discount->setType('CASH_BACK');
        $discount->setSubType('VOUCHER');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount]);

        $actionDiscounts->expects($this->once())
            ->method('getActivePromotionWithPromocode')
            ->will($this->returnValue($promotion));

        $actionDiscounts->expects($this->once())
            ->method('getTicketCommentForDisplayName')
            ->with('Voucher-Amazon-50')
            ->will($this->returnValue(['txtTicketComment' => 'CUSTOMER IS ELIGIBLE FOR CASHBACK', 'intCauseID' => 'ABC']));

        $actionDiscounts->expects($this->once())
            ->method('raiseTicket')
            ->with('CUSTOMER IS ELIGIBLE FOR CASHBACK', 'CSC', true, 'ABC');

        $actionDiscounts->execute();
    }

    public function testTicketIsNotRaisedWhenC2MPromotionDoesNotHaveCashbackDiscount()
    {
        $actionDiscounts = $this->getMock('AccountChange_Action_Discounts',
            array(
                'getServiceDefinition',
                'getDiscountManager',
                'getProductFamily',
                'getPromotionToolService',
                'addCustomerNoteToAccountForPromotion',
                'getActivePromotionWithPromocode',
                'getTicketCommentForDisplayName',
                'raiseTicket'
            ),
            array(123456)
        );

        $this->setupMocksForC2MPromotionTest($actionDiscounts);

        $discount = new Discount();
        $discount->setType('UP_FRONT');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount]);

        $actionDiscounts->expects($this->once())
            ->method('getActivePromotionWithPromocode')
            ->will($this->returnValue($promotion));

        $actionDiscounts->expects($this->never())
            ->method('getTicketCommentForDisplayName');

        $actionDiscounts->expects($this->never())
            ->method('raiseTicket');

        $actionDiscounts->execute();
    }

    public function testTicketIsNotRaisedWhenC2MPromotionDoesNotHaveACashbackDiscountThatMapsToATicketCause()
    {
        $actionDiscounts = $this->getMock('AccountChange_Action_Discounts',
            array(
                'getServiceDefinition',
                'getDiscountManager',
                'getProductFamily',
                'getPromotionToolService',
                'addCustomerNoteToAccountForPromotion',
                'getActivePromotionWithPromocode',
                'getTicketCommentForDisplayName',
                'raiseTicket'
            ),
            array(123456)
        );

        $this->setupMocksForC2MPromotionTest($actionDiscounts);

        $discountValue = new DiscountValue();
        $discountValue->setValue(50);

        $discount = new Discount();
        $discount->setDiscountValues([$discountValue]);
        $discount->setType('CASH_BACK');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount]);

        $actionDiscounts->expects($this->once())
            ->method('getActivePromotionWithPromocode')
            ->will($this->returnValue($promotion));

        $actionDiscounts->expects($this->once())
            ->method('getTicketCommentForDisplayName')
            ->with('Cashback-50')
            ->will($this->returnValue(''));

        $actionDiscounts->expects($this->never())
            ->method('raiseTicket');

        $actionDiscounts->execute();
    }

    public function testTicketIsNotRaisedWhenC2MPromotionHasAnUnsupportedCashbackSubType()
    {
        $actionDiscounts = $this->getMock('AccountChange_Action_Discounts',
            array(
                'getServiceDefinition',
                'getDiscountManager',
                'getProductFamily',
                'getPromotionToolService',
                'addCustomerNoteToAccountForPromotion',
                'getActivePromotionWithPromocode',
                'getTicketCommentForDisplayName',
                'raiseTicket'
            ),
            array(123456)
        );

        $this->setupMocksForC2MPromotionTest($actionDiscounts);

        $discountValue = new DiscountValue();
        $discountValue->setValue(50);

        $discount = new Discount();
        $discount->setDiscountValues([$discountValue]);
        $discount->setType('CASH_BACK');
        $discount->setSubType('E_VOUCHER');

        $promotion = new Promotion();
        $promotion->setDiscounts([$discount]);

        $actionDiscounts->expects($this->once())
            ->method('getActivePromotionWithPromocode')
            ->will($this->returnValue($promotion));

        $actionDiscounts->expects($this->never())
            ->method('getTicketCommentForDisplayName');

        $actionDiscounts->expects($this->never())
            ->method('raiseTicket');

        $actionDiscounts->execute();
    }

    private function setupMocksForC2MPromotionTest(&$mock)
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('promoCode', 'c2mPromotionCode');
        $registry->setEntry('c2mPromotion', true);
        $registry->setEntry('agreementDate', date('Y-m-d'));

        $mockPromotionToolsService = $this->getMock(
            'PromotionToolsService',
            array('addPromotionToAccountSpecifyingPermittedStatusCodes'),
            array()
        );

        $discountSummary = new DiscountSummary(array());

        $mockPromotionToolsService->expects($this->once())
            ->method('addPromotionToAccountSpecifyingPermittedStatusCodes')
            ->with($this->anything(), $this->anything(), $this->anything(), $this->anything(), $this->callback(
                function ($agreementDate) {
                    $this->assertEquals(date('Ymd'), $agreementDate);
                    return true;
                }
            ))
            ->will($this->returnValue($discountSummary));


        $mockServiceDefinition = $this->getmockServiceDefinition();

        $mockFamily = $this->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('hasAutoContracts'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily->expects($this->once())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));

        $discountManagerMock = $this->getMock(
            'Retention_DiscountManager',
            array('getServiceDiscounts'),
            array(),
            '',
            false
        );

        $discountManagerMock
            ->expects($this->once())
            ->method('getServiceDiscounts')
            ->with($this->equalTo(123456), $this->equalTo('pending'))
            ->will($this->returnValue(array()));

        $mock->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($mockServiceDefinition));

        $mock->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($mockFamily));

        $mock->expects($this->any())
            ->method('getDiscountManager')
            ->will($this->returnValue($discountManagerMock));

        $mock->expects($this->any())
            ->method('getPromotionToolService')
            ->will($this->returnValue($mockPromotionToolsService));
    }
}
