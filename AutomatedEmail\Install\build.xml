<?xml version="1.0"?>
<project name="Framework Install" basedir="." default="main">

	<target name="init">
		<echo>========================= Framework install script =========================</echo>

		<!-- ================================= 
		     Used variables
		     ================================= -->	
		<property name="apacheConfFile"     value="/etc/apache/httpd.conf" />
		<property name="php5bin"            value="/usr/bin/php" />
		<property name="phingTasksDir"      value="/usr/share/php/phing/tasks" />
		<property name="databaseLocalFile"  value="/local/data/mis/database/database_local.inc" />
		<property name="codebaseRootDir"    value="/local/codebase2005" />
		<property name="sitesContentDir"    value="${codebaseRootDir}/content" />
		<property name="smartyClassFile"    value="${codebaseRootDir}/modules/CommonPortalFramework/Libraries/Smarty/Smarty.class.php" />
		<property name="modulesConfigDir"   value="${codebaseRootDir}/modules/Config" />
		<property name="frameworkDir"       value="${codebaseRootDir}/modules/Framework" />
		<property name="statementsDir"      value="${codebaseRootDir}/modules/Framework/Statements" />
		<property name="sitesDir"           value="${codebaseRootDir}/sites" />
		<property name="codebaseSharedDir"  value="${codebaseRootDir}/shared" />
		<property name="smartyCompileDir"   value="${codebaseRootDir}/shared/Framework/Mvc/CompiledSmartyTemplates" />
		<property name="sessionManager"     value="http://session.businesstier.plus.net:8080/session-wsdl-fix/SessionManagerService.wsdl" />
		<property name="dbInstallHost"      value="doom" />

		<!-- ================================= 
		     Custom task definitions
		     ================================= -->
		<taskdef name="apacheUser"          classname="phing.tasks.pn.ApacheUser" />
		<taskdef name="isReadable"          classname="phing.tasks.pn.IsReadable" />
		<taskdef name="regExpSearch"        classname="phing.tasks.pn.RegExpSearch" />
		<taskdef name="dirsReadable"        classname="phing.tasks.pn.DirsReadable" />
		<taskdef name="scanStatements"      classname="phing.tasks.pn.ScanStatements" />
		<taskdef name="urlCheck"            classname="phing.tasks.pn.UrlCheck" />
		<taskdef name="sqlInstallDbs"       classname="phing.tasks.pn.SqlInstallDbs" />
		<taskdef name="classesCheck"        classname="phing.tasks.pn.ClassesCheck" />
		<taskdef name="handlesCheck"        classname="phing.tasks.pn.HandlesCheck" />
		<taskdef name="symlink"             classname="phing.tasks.pn.Symlink" />
		<taskdef name="serverRoles"         classname="phing.tasks.pn.ServerRoles" />
		<taskdef name="isWritable"          classname="phing.tasks.pn.IsWritable" />
		<taskdef name="checkModulePath"     classname="phing.tasks.pn.CheckModulePath" />
		<taskdef name="checkUsers"          classname="phing.tasks.pn.CheckUsers" />
		<taskdef name="changeOwnership"     classname="phing.tasks.pn.ChangeOwnership" />
		<taskdef name="isEmptyDirectory"    classname="phing.tasks.pn.IsEmptyDirectory" />
		<taskdef name="checkPdo"            classname="phing.tasks.pn.CheckPdo" />
	</target>

	<!-- ================================= 
	     The script must be run as root or as deployer user
	     ================================= -->	
	<target name="checkUsers" depends="init">
		<checkUsers users="deployer,root" groups="deployer,debug" returnName="usersCheck" />
		<fail unless="usersCheck" message="correct above erorrs" />			
	</target>

	<!-- =================================
	     Check to see if Framework module is install 
	     ================================= -->
	<target name="checkPhpInApache" depends="checkUsers">
		<available file="/etc/apache/conf.d/php5.conf" property="apachePhp5Config" />
		<fail unless="apachePhp5Config">

Apache doesn't seem to have PHP 5 enabled (see /etc/apache/conf.d/php5.conf). Please correct this before attempting to run installer again.
		</fail>
	</target>

	<!-- ================================= 
	     Ask for confirmation
	     ================================= -->	
	<target name="installConfirm" depends="checkPDO">
		<echo>Defined variables</echo>
		<echo>Apache config file  : ${apacheConfFile}</echo>
		<echo>PHP5 executable     : ${php5bin}</echo>
		<echo>Phing tasks dir     : ${phingTasksDir}</echo>
		<echo>database_local.inc  : ${databaseLocalFile}</echo>
		<echo>Sites content dir   : ${sitesContentDir}</echo>
		<echo>Framework directory : ${frameworkDir}</echo>
		<echo>Smarty class file   : ${smartyClassFile}</echo>
		<echo>Modules config dir  : ${modulesConfigDir}</echo>
		<echo>Statements dir      : ${statementsDir}</echo>
		<echo>Sites directory     : ${sitesDir}</echo>
		<echo>Smarty compile dir  : ${smartyCompileDir}</echo>
		<echo>Session Manager     : ${sessionManager}</echo>
		<echo>DB install host     : ${dbInstallHost}</echo>
		<input message="Do you want to proceed with framework installation with these settings" defaultValue="n" validargs="y,n" promptChar="?" propertyname="performInstall"/>
		<condition property="installCheck">
			<equals arg1="y" arg2="${performInstall}"/>
		</condition>
		<fail unless="installCheck" message="Aborted by user"/>
	</target>


	<!-- ================================= 
	     Scan all directories under ${statementsDir} for statement files and check each 'server: <database> ' listed is accessible.
	     ================================= -->
	<target name="scanStatements" depends="frameworkDir">
		<scanStatements baseDir="${statementsDir}" returnName="scanCheckFailed"/>
		<fail if="scanCheckFailed" message="${scanCheckFailed}"/>
	</target>

	<!-- ================================= 
	     Run the Install/generateConfigFiles.php file
	     ================================= -->
	<target name="generateConfigFiles" depends="sessionManagerCheck">
		<exec command="${php5bin} generateConfigFiles.php" dir="${project.basedir}" passthru="true" />
		<changeOwnership file="${modulesConfigDir}" owner="deployer" group="deployer" mode="777"/>
	</target>

	<!-- ================================= 
	     Check class definitions with autoloader
	     ================================= -->
	<target name="classesCheck" depends="createWurflDir">
		<echo>Checking class declarations with autoloader...</echo>
		<classesCheck returnName="classCheckOk"/>
		<fail unless="classCheckOk" message="Class check failed."/>
	</target>

	<!-- ================================= 
	     Select all files matching Install/<databaseName>.(structure|populate[1-9]+).sql	
	     Verifiy that the database host is accessible
	     Verify that a database with that name exists, offer to create it if not.
	     Verify that the database contains any table, perform the queries given in 
	     Install/<databaseName>.structure.sql and Install/<databaseName>.populate[1-9]+.sql
	     Verify that the structure of all table listed in Install/<databaseName>.structure.sql
	     exactly matches the database versions if the database was created.
	     ================================= -->
	<target name="dbInstall" depends="classesCheck">
		<echo>Checking if host ${dbInstallHost} is accessible...</echo> 
		<sqlInstallDbs action="checkHost" host="${dbInstallHost}" returnName="hostCheck"/>
		<fail unless="hostCheck" message="Host ${dbInstallHost} is not accessible"/>
		<sqlInstallDbs action="getDbNames" returnName="sqlDbs"/>
		<foreach list="${sqlDbs}" param="dbName" target="populateDb" />
	</target>

	<target name="checkDb">
		<echo>Checking if database ${dbName} exists...</echo> 
		<sqlInstallDbs action="checkDb" db="${dbName}" returnName="dbExists"/>
	</target>

	<target name="createDb" depends="checkDb" unless="dbExists">
		<input message="Database ${dbName} doesn't exists, do you want to create it" defaultValue="y" validargs="y,n" promptChar="?" propertyname="dbCheck"/>
		<condition property="performDbCreate">
			<equals arg1="y" arg2="${dbCheck}"/>
		</condition>
		<fail unless="performDbCreate" message="Aborted by user"/>

		<echo>Creating database ${dbName}...</echo>
		<sqlInstallDbs action="createDb" db="${dbName}" returnName="dbCreated"/>

		<echo>Creating tables...</echo>
		<sqlInstallDbs action="createTables" db="${dbName}" returnName="tablesCreated"/>
	</target>

	<target name="verifyDb" depends="checkDb" if="dbExists">
		<echo>Verifying tables structure in ${dbName} database...</echo>
		<sqlInstallDbs action="verifyTables" db="${dbName}" returnName="verifyError"/>
		<fail if="verifyError" message="${verifyError}"/>
	</target>

	<target name="populateDb" depends="createDb,verifyDb" unless="dbExists">
		<echo>Populating ${dbName} database...</echo> 
		<sqlInstallDbs action="populateTables" db="${dbName}" returnName="dbNameCheck"/>
	</target>

	<!-- ================================= 
	     Check handles in modules dir
	     ================================= -->
	<target name="checkHandles" depends="dbInstall">
		<echo>Checking handles...</echo>
		<handlesCheck returnName="handlesCheck" dir="${frameworkDir}"/>
		<fail unless="handlesCheck" message="Handles check failed"/>
	</target>

	<!-- ================================= 
	     Cron script installation
	     ================================= -->
	<target name="checkServerRolesDir" depends="checkHandles">
		<echo>Checking if ${codebaseRootDir}/modules/Config/ServerRoles exists...</echo>
		<available property="serverRolesDirExists" type="dir" file="${codebaseRootDir}/modules/Config/ServerRoles"/>
	</target>

	<target name="createServerRolesDir" depends="checkServerRolesDir" unless="serverRolesDirExists">
		<echo>Directory ${codebaseRootDir}/modules/Config/ServerRoles doesn't exist, creating now...</echo>
		<mkdir dir="${codebaseRootDir}/modules/Config/ServerRoles" />
	</target>

	<target name="serverRolesDirRights" depends="checkServerRolesDir" if="serverRolesDirExists">
		<echo>Checking if ${codebaseRootDir}/modules/Config/ServerRoles is writable by current user...</echo>
		<isWritable returnName="serverRolesDirRights" file="${codebaseRootDir}/modules/Config/ServerRoles"/>
		<fail unless="serverRolesDirRights" message="${codebaseRootDir}/modules/Config/ServerRoles is not writable by current user"/>
	</target>

	<target name="checkShareAdminPortalDir" depends="createServerRolesDir,serverRolesDirRights">
		<echo>Checking if /share/admin/portal exists...</echo>
		<available property="shareAdminPortalDirExists" type="dir" file="/share/admin/portal"/>
	</target>

	<target name="createShareAdminPortalDir" depends="checkShareAdminPortalDir" unless="shareAdminPortalDirExists">
		<echo>Directory /share/admin/portal doesn't exist, creating now...</echo>
		<mkdir dir="/share/admin/portal" />
		<changeOwnership file="/share/admin/portal" owner="deployer" group="deployer" mode="755"/> 
	</target>

	<!-- ================================= 
	     Server roles
	     ================================= -->	
	<target name="serverRoles" depends="createShareAdminPortalDir">
		<serverRoles 
			availableRolesDir="${frameworkDir}/ServerRoles"
			serverRolesDir="${codebaseRootDir}/modules/Config/ServerRoles"
			cvsModule="Framework"
		/>
		<changeOwnership file="${codebaseRootDir}/modules/Config/ServerRoles" owner="deployer" group="deployer" mode="755"/> 		
	</target>

	<!-- ================================= 
	     Run any tests in the site's Test/PreInstall directory or its children, abort on failure.	
	     ================================= -->
	<target name="preInstallTests" depends="serverRoles">
	</target>

	<!-- ================================= 
	     Link custom task to phing directory
	     ================================= -->
	<target name="phingDir" depends="preInstallTests">
		<available file="${phingTasksDir}" type="dir" property="phingDirExists" />
		<fail unless="phingDirExists" message="${phingTasksDir} is not available"/>
	</target>

	<target name="phingTasks" depends="phingDir">
		<echo>Linking task to phing directory...</echo>		
		<symlink action="create" link="${phingTasksDir}/pn" target="${frameworkDir}/Install/phing/tasks/pn"  returnName="symlinkCreateCheck"/>
	</target>	

	<target name="main" depends="phingTasks">
		<echo>All targets executed.</echo>
	</target>

</project>
