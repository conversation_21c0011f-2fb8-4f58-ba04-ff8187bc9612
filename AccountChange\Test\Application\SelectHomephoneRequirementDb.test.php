<?php
/**
 * AccountChange Homephone Requirement Test
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @since      File available since 2011-09-05
 */
/**
 * AccountChange Homephone Requirement Test
 *
 * Database test class for AccountChange_SelectHomephoneRequirement Requirement
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_SelectHomephoneRequirementDb_Test extends Plusnet_Database_TestCase
{
    protected $arrServers = array(
        'Coredb_master' => array(),
        'Coredb_slave' => array()
    );

    protected $arrDataStructureDirectories = array('/local/codebase2005/modules/AccountChange/Test/datastructure/');

    protected $dbName = 'products';

    protected $dataSet = '/local/codebase2005/modules/AccountChange/Test/dataset/ProductsTestData.xml';

    /**
     * @return void
     */
    public function setUp()
    {
        Db_Manager::reset();
        parent::setUp();
    }

    /**
     * To make sure every test starts with a brand new connection
     * we need to restore Adaptor and connection and signal a transaction to end
     * on default transaction
     *
     * @return void
     */
    public function tearDown()
    {
        \Db_Manager::reset();
        parent::tearDown();

        Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('AccountChange');
        Db_Manager::restoreAdaptor('Auth');
        Db_Manager::restoreConnection('coredb_master');
    }

    /**
     * Test the GetWlrProducts on with different data
     *
     * @covers       AccountChange_SelectHomephoneRequirement::getWlrProducts
     * @covers       AccountChange_SelectHomephoneRequirement::getAllUpgradeProducts
     *
     * @dataProvider dataProviderForGetWlrProducts
     *
     * @param $serviceDefinitionId
     * @param $strContractHandle
     * @param $bolIncludeNoWlr
     * @param $newServiceDefinition
     * @param $oldServiceDefinition
     * @param $strReportSector
     * @param $upgradeProducts
     * @param $excludedProducts
     * @param $oldSdi
     * @param $oldWlrId
     * @param $callCountApplyProductImCurrentlyOn
     * @param $callCountGetProductContractOptions
     * @param $expectedProducts
     * @param $businessActor
     * @param $contractDetails
     * @return void
     */
    public function testGetWlrProducts(
        $serviceDefinitionId,
        $strContractHandle,
        $bolIncludeNoWlr,
        $newServiceDefinition,
        $oldServiceDefinition,
        $strReportSector,
        $upgradeProducts,
        $excludedProducts,
        $oldSdi,
        $oldWlrId,
        $callCountApplyProductImCurrentlyOn,
        $callCountGetProductContractOptions,
        $expectedProducts,
        $businessActor,
        $contractDetails = null
    ) {
        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->at(0))
                ->method('getServiceDefinitionDao')
                ->will($this->returnValue($newServiceDefinition));

        $adaptor->expects($this->at(1))
                ->method('getServiceDefinitionDao')
                ->will($this->returnValue($oldServiceDefinition));

        Db_Manager::setAdaptor('Core', $adaptor);

        $callPlanDetails = array(
            'intCallPlanId' => 1,
            'strDisplayName' => 'test1',
            'intChargingIntervalSeconds' => 22,
            'intCostIncVatPence' => 12,
            'intServiceComponentID' => 1,
            'bolDisplaySplitCharge' => 1,
            'intTariffID' => '123'
        );

        if (is_null($contractDetails)) {
            $contractDetails = array(
                array(
                    'strContractHandle' => 'test1',
                    'strDisplayName' => 'test1',
                    'intCostIncVatInPence' => 2000,
                    'strCustomerSectorHandle' => 'test1',
                    'intCostToShow' => 2000,
                    'floCostToShow' => 20,
                    'intTariffID' => '123'
                )
            );
        }

        $product = $this->getMock(
            'CProduct',
            array('getProductContractOptions'),
            array()
        );

        $product->expects($this->exactly($callCountGetProductContractOptions))
            ->method('getProductContractOptions')
            ->will($this->returnValue($contractDetails));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'),
            array()
        );

        $availableProduct = new Plusnet\BillingApiClient\Entity\AvailableProduct();
        $availableProduct->setProductOfferingId(123);
        $availableProduct->setPricePointId(456);
        $availableProduct->setCurrentBasePriceInPounds(10);
        $availableProducts = array($availableProduct);

        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('getCustomerAvailableProducts'),
            array(),
            '',
            false
        );

        $billingApiFacade
            ->expects($this->any())
            ->method('getCustomerAvailableProducts')
            ->willReturn($availableProducts);

        \Plusnet\BillingApiClient\Service\ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );

        $coreService->expects($this->once())
                    ->method('getServiceId')
                    ->will($this->returnValue(0));

        $controller->expects($this->at(0))
                   ->method('getApplicationStateVar')
                   ->will($this->returnValue(array('intOldWlrId' => $oldWlrId)));

        $controller->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->will($this->returnValue($oldSdi));

        $controller->expects($this->at(2))
                   ->method('getApplicationStateVar')
                   ->will($this->returnValue($businessActor));

        $controller->expects($this->at(3))
            ->method('getApplicationStateVar')
            ->will($this->returnValue($coreService));

        $requirement = TestCaseWithProxy::getPHPUnitProxy('AccountChange_SelectHomephoneRequirement', array());

        $requirement = $this->getMock(
            get_class($requirement),
            array(
                'getProductComponent',
                'includeLegacyFiles',
                'getApplicationStateVariable',
                'applyProductImCurrentlyOn',
                'getCallPlanDetailsByWlrServiceComponentId',
                'getExcludedWlrProducts',
                'getReportSector',
                'hasActiveLineRentalSaver',
                'filterWlrProductsBasedOnContractLength',
                'getBasePrices',
                'generateIdFromProductOfferingPricePointPair',
                'getLineRentalDiscounts',
            ),
            array()
        );

        $requirement->expects($this->any())
            ->method('getBasePrices')
            ->will($this->returnValue(array(
                '123:456' => array(
                    'currentBasePrice'           => 10,
                    'currentBasePriceInContract' => 20
                )
            )));

        $requirement->expects($this->any())
            ->method('generateIdFromProductOfferingPricePointPair')
            ->will($this->returnValue('123:456'));

        $requirement->expects($this->once())
                    ->method('includeLegacyFiles');

        $requirement->expects($this->once())
                    ->method('getExcludedWlrProducts')
                    ->will($this->returnValue($excludedProducts));

        $requirement
            ->expects($this->any())
            ->method('filterWlrProductsBasedOnContractLength')
            ->will($this->returnValue($upgradeProducts));

        $requirement->expects($this->exactly($callCountApplyProductImCurrentlyOn))
                    ->method('applyProductImCurrentlyOn')
                    ->will($this->returnValue($upgradeProducts));

        $requirement->expects($this->once())
                    ->method('getProductComponent')
                    ->will($this->returnValue($product));

        $requirement->expects($this->once())
                    ->method('getReportSector')
                    ->will($this->returnValue($strReportSector));
        $requirement->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $requirement->setAppStateCallback($controller);
        $requirement->expects($this->any())
                    ->method('getCallPlanDetailsByWlrServiceComponentId')
                    ->will($this->returnValue($callPlanDetails));

        $requirement
            ->expects($this->any())
            ->method('getLineRentalDiscounts')
            ->will($this->returnValue(array('lineRentalDiscountedPrice' => null, 'discountAmountInPounds' => null)));

        $products = $requirement->protected_getWlrProducts($serviceDefinitionId, $strContractHandle, $bolIncludeNoWlr);

        $this->assertEquals($expectedProducts, $products);
    }

    /**
     * Test that getWlrProducts throws an exception when getProductContractOptions returns multiple results
     *
     * @covers            AccountChange_SelectHomephoneRequirement::getWlrProducts
     * @expectedException AccountChange_MultipleLineRentalTariffsException
     */
    public function testGetWlrProductsThrowsExceptionWhenMultipleLineRentalTariffsAreReturned()
    {
        $contractDetails = array(
            array(
                'intTariffID' => '123'
            ),
            array(
                'intTariffID' => '456'
            )
        );

        $serviceDefinition = array(
            'service_definition_id'       => 2,
            'intProductVariantId'         => 2,
            'name'                        => 'test2',
            'isp'                         => 'new',
            'minimum_charge'              => 4.33,
            'date_created'                => time(),
            'requires'                    => 'Yes',
            'initial_charge'              => 0.00,
            'type'                        => 'business',
            'password_visible_to_support' => 'No',
            'end_date'                    => null,
            'signup_via_portal'           => 'Y',
            'bt_product_id'               => '',
            'strProductVariant'           => 'test',
            'strProductFamily'            => 'test',
            'bolAdsl'                     => 0);

        $upgradeProducts = array(
            array(
                'intServiceComponentID' => 2,
                'ProductName' => 'test2',
                'strDescription' => 'test2'
            )
        );

        $this->testGetWlrProducts(
            2,                                  // Service Definition ID
            null,                               // Contract Handle
            true,                               // Include No WLR
            $serviceDefinition,
            $serviceDefinition,
            'business',
            $upgradeProducts,
            $excludedProducts = array(7, 8, 9), // Excluded Products
            6705,                               // Old Strategic Defense Initiative
            3,                                  // Old WLR ID
            1,                                  // Call count for ApplyProductImCurrentlyOn function
            1,                                  // Call count for GetProductContractOptions function
            null,                               // We don't expect any products as this will throw an exception
            $this->getBusinessActor('PLUSNET_STAFF'),
            $contractDetails
        );
    }

    /**
     * Data provider for the function getWlrProducts
     *
     * @return array
     */
    public function dataProviderForGetWlrProducts()
    {
        $serviceDefinitions = array(
            2 => array('service_definition_id' => 2,
                'intProductVariantId' => 2,
                'name' => 'test2',
                'isp' => 'new',
                'minimum_charge' => 4.33,
                'date_created' => time(),
                'requires' => 'Yes',
                'initial_charge' => 0.00,
                'type' => 'business',
                'password_visible_to_support' => 'No',
                'end_date' => null,
                'signup_via_portal' => 'Y',
                'bt_product_id' => '',
                'strProductVariant' => 'test',
                'strProductFamily' => 'test',
                'bolAdsl' => 0
            ),
            3 => array('service_definition_id' => 3,
                'intProductVariantId' => 2,
                'name' => 'test3',
                'isp' => 'new',
                'minimum_charge' => 4.33,
                'date_created' => time(),
                'requires' => 'Yes',
                'initial_charge' => 0.00,
                'type' => 'residential',
                'password_visible_to_support' => 'No',
                'end_date' => null,
                'signup_via_portal' => 'Y',
                'bt_product_id' => '',
                'strProductVariant' => 'test',
                'strProductFamily' => 'test',
                'bolAdsl' => 0
            ),
            4 => array('service_definition_id' => 4,
                'intProductVariantId' => 2,
                'name' => 'test4',
                'isp' => 'new',
                'minimum_charge' => 4.33,
                'date_created' => time(),
                'requires' => 'Yes',
                'initial_charge' => 0.00,
                'type' => 'business',
                'password_visible_to_support' => 'No',
                'end_date' => null,
                'signup_via_portal' => 'Y',
                'bt_product_id' => '',
                'strProductVariant' => 'test',
                'strProductFamily' => 'test',
                'bolAdsl' => 0
            ),
            5 => array('service_definition_id' => 6770,
                'intProductVariantId' => 2,
                'name' => 'test4',
                'isp' => 'johnlewis',
                'minimum_charge' => 4.33,
                'date_created' => time(),
                'requires' => 'Yes',
                'initial_charge' => 0.00,
                'type' => 'residential',
                'password_visible_to_support' => 'No',
                'end_date' => null,
                'signup_via_portal' => 'Y',
                'bt_product_id' => '',
                'strProductVariant' => 'test',
                'strProductFamily' => 'test',
                'bolAdsl' => 0
            ),
            6 => array('service_definition_id' => 6687,
                'intProductVariantId' => 2,
                'name' => 'Product 6',
                'isp' => 'greenbee',
                'minimum_charge' => 4.33,
                'date_created' => time(),
                'requires' => 'Yes',
                'initial_charge' => 0.00,
                'type' => 'residential',
                'password_visible_to_support' => 'No',
                'end_date' => null,
                'signup_via_portal' => 'Y',
                'bt_product_id' => '',
                'strProductVariant' => 'test',
                'strProductFamily' => 'test',
                'bolAdsl' => 0
            ),
            7 => array('service_definition_id' => 6685,
                'intProductVariantId' => 2,
                'name' => 'Product 7',
                'isp' => 'waitrose',
                'minimum_charge' => 4.33,
                'date_created' => time(),
                'requires' => 'Yes',
                'initial_charge' => 0.00,
                'type' => 'residential',
                'password_visible_to_support' => 'No',
                'end_date' => null,
                'signup_via_portal' => 'Y',
                'bt_product_id' => '',
                'strProductVariant' => 'test',
                'strProductFamily' => 'test',
                'bolAdsl' => 0
            )
        );

        $upgradeProducts = array (
            2 => array(
                array(
                'intServiceComponentID' => 2,
                'ProductName' => 'test2',
                'strDescription' => 'test2'
                )
            ),
            4 => array(
                array(
                'intServiceComponentID' => 4,
                'ProductName' => 'test4',
                'strDescription' => 'test4'
                )
            )
        );

        $expectedProducts = array(
            2 => array (
                array(
                    'intNewWlrId'                 => 2,
                    'intServiceComponentId'       => 2,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'test2',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                ),
                array(
                    'intNewWlrId'                 => 0,
                    'strContract'                 => 'MONTHLY',
                    'strProductName'              => 'No Home Phone',
                    'bolSplitPrice'               => false,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                )
            ),
            3 => array (
                array(
                    'intNewWlrId'                 => 3,
                    'intServiceComponentId'       => 3,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'test3',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                ),
                array(
                    'intNewWlrId'       => 0,
                    'strContract'       => 'MONTHLY',
                    'strProductName'    => 'No Home Phone',
                    'bolSplitPrice'     => false,
                    'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intLineRentCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intCallPlanCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
                )
            ),
            4 => array (
                array(
                    'intNewWlrId'                 => 4,
                    'intServiceComponentId'       => 4,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'test4',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                )
            ),
            5 => array (
                array(
                    'intNewWlrId'                 => 995,
                    'intServiceComponentId'       => 995,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'JohnLewisEWCalls',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                ),
                array(
                    'intNewWlrId'                 => 996,
                    'intServiceComponentId'       => 996,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'JohnLewisAnyTimeInternationalCalls',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                ),
                array(
                    'intNewWlrId'                 => 997,
                    'intServiceComponentId'       => 997,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'JohnLewisAnyTimeCalls',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                ),
            ),
            6 => array (
                array(
                    'intNewWlrId'                 => 3,
                    'intServiceComponentId'       => 3,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'test3',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null

                ),
                array(
                    'intNewWlrId'                 => 7,
                    'intServiceComponentId'       => 7,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'test7',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                ),
                array(
                    'intNewWlrId'                 => 8,
                    'intServiceComponentId'       => 8,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'test8',
                    'bolSplitPrice'               => 1,
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'lineRentalDiscountAmount'    => null,
                    'lineRentalDiscountedPrice'   => null
                ),
                array(
                    'intNewWlrId'       => 0,
                    'strContract'       => 'MONTHLY',
                    'strProductName'    => 'No Home Phone',
                    'bolSplitPrice'     => false,
                    'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intLineRentCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intCallPlanCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
                )
            ),
            7 => array (
                array(
                    'intNewWlrId'                 => 995,
                    'intServiceComponentId'       => 995,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'JohnLewisEWCalls',
                    'bolSplitPrice'               => '',
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
                ),
                array(
                    'intNewWlrId'                 => 996,
                    'intServiceComponentId'       => 996,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'JohnLewisAnyTimeInternationalCalls',
                    'bolSplitPrice'               => '',
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
                ),
                array(
                    'intNewWlrId'                 => 997,
                    'intServiceComponentId'       => 997,
                    'strContract'                 => 'test1',
                    'strProductName'              => 'JohnLewisAnyTimeCalls',
                    'bolSplitPrice'               => '',
                    'intProductCost'              => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                    'intLineRentCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                    'intCallPlanCost'             => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
                ),
                array(
                    'intNewWlrId'       => 0,
                    'strContract'       => 'MONTHLY',
                    'strProductName'    => 'No Home Phone',
                    'bolSplitPrice'     => false,
                    'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intLineRentCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    'intCallPlanCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
                )
            ),
        );

        return array(
            // Data Set 0
            // Service Definition ID 2
            array(
                2, // Service Definition ID
                null, // Contract Handle
                true, // Include No WLR
                $serviceDefinitions[2], // New Service Definition
                $serviceDefinitions[2], // Old Service Definition
                'business',
                $upgradeProducts[2], // Upgrade Products
                $excludedProducts = array(7, 8, 9), // Excluded Products
                6705, // Old Strategic Defense Initiative
                3, // Old WLR ID
                1, // Call count for ApplyProductImCurrentlyOn function
                1, // Call count for GetProductContractOptions function
                $expectedProducts[2], // Expected Products array
                $this->getBusinessActor('PLUSNET_STAFF')
            ),

            // Data Set 1
            // Service Definition ID 3
            array(
                3, // Service Definition ID
                'test1', // Contract Handle
                true, // Include No WLR
                $serviceDefinitions[3], // New Service Definition
                $serviceDefinitions[3], // Old Service Definition
                'residential',
                null, // Upgrade Products
                $excludedProducts = array(7, 8, 9), // Excluded Products
                6704, // Old Strategic Defense Initiative
                596, // Old WLR ID
                0, // Call count for ApplyProductImCurrentlyOn function
                1, // Call count for GetProductContractOptions function
                $expectedProducts[3], // Expected Products array
                $this->getBusinessActor('PLUSNET_STAFF')
            ),

            // Data Set 2
            // Service Definition ID 3 (no excluded WLR products)
            array(
                3, // Service Definition ID
                'test1', // Contract Handle
                true, // Include No WLR
                $serviceDefinitions[3], // New Service Definition
                $serviceDefinitions[3], // Old Service Definition
                'residential',
                null, // Upgrade Products
                $excludedProducts = array(), // Excluded Products
                6704, // Old Strategic Defense Initiative
                596, // Old WLR ID
                0, // Call count for ApplyProductImCurrentlyOn function
                3, // Call count for GetProductContractOptions function
                $expectedProducts[6], // Expected Products array
                $this->getBusinessActor('PLUSNET_STAFF')
            ),

            // Data Set 3
            // Service Definition ID 4
            array(
                4, // Service Definition ID
                'test2', // Contract Handle
                false, // Include No WLR
                $serviceDefinitions[4], // New Service Definition
                $serviceDefinitions[4], // Old Service Definition
                'business',
                $upgradeProducts[4], // Upgrade Products
                $excludedProducts = array(7, 8, 9), // Excluded Products
                3, // Old Strategic Defense Initiative
                596, // Old WLR ID
                1, // Call count for ApplyProductImCurrentlyOn function
                1, // Call count for GetProductContractOptions function
                $expectedProducts[4], // Expected Products array
                $this->getBusinessActor('PLUSNET_STAFF')
            ),

            // Data Set 4
            // Switching to a John Lewis Product from a Greenbee product on the portal
            array(
                6770, // Service Definition ID
                null, // Contract Handle
                true, // Include No WLR
                $serviceDefinitions[5], // New Service Definition
                $serviceDefinitions[6], // Old Service Definition
                'residential',
                null, // Upgrade Products
                $excludedProducts = array(7, 8, 9), // Excluded Products
                6687, // Old Strategic Defense Initiative
                596, // Old WLR ID
                0, // Call count for ApplyProductImCurrentlyOn function
                3, // Call count for GetProductContractOptions function
                $expectedProducts[5], // Expected Products array
                $this->getBusinessActor('PLUSNET_ENDUSER')
            ),

            // Data Set 5
            // Switching to a John Lewis Product from a Waitrose product on the portal
            array(
                6770, // Service Definition ID
                null, // Contract Handle
                true, // Include No WLR
                $serviceDefinitions[5], // New Service Definition
                $serviceDefinitions[7], // Old Service Definition
                'residential',
                null, // Upgrade Products
                $excludedProducts = array(7, 8, 9), // Excluded Products
                6687, // Old Strategic Defense Initiative
                596, // Old WLR ID
                0, // Call count for ApplyProductImCurrentlyOn function
                3, // Call count for GetProductContractOptions function
                $expectedProducts[5], // Expected Products array
                $this->getBusinessActor('PLUSNET_ENDUSER')
            ),

            // Data Set 6
            // Switching to a John Lewis Product from a GreenBee product on workplace
            array(
                6770, // Service Definition ID
                null, // Contract Handle
                true, // Include No WLR
                $serviceDefinitions[5], // New Service Definition
                $serviceDefinitions[6], // Old Service Definition
                'residential',
                $upgradeProducts[2], // Upgrade Products
                $excludedProducts = array(7, 8, 9), // Excluded Products
                6687, // Old Strategic Defense Initiative
                596, // Old WLR ID
                1, // Call count for ApplyProductImCurrentlyOn function
                1, // Call count for GetProductContractOptions function
                $expectedProducts[2], // Expected Products array
                $this->getBusinessActor('PLUSNET_STAFF')
            ),

            // Data Set 7
            // Switching to a John Lewis Product from a Waitrose product on workplace
            array(
                6770, // Service Definition ID
                null, // Contract Handle
                true, // Include No WLR
                $serviceDefinitions[5], // New Service Definition
                $serviceDefinitions[7], // Old Service Definition
                'residential',
                $upgradeProducts[2], // Upgrade Products
                $excludedProducts = array(7, 8, 9), // Excluded Products
                6687, // Old Strategic Defense Initiative
                596, // Old WLR ID
                1, // Call count for ApplyProductImCurrentlyOn function
                1, // Call count for GetProductContractOptions function
                $expectedProducts[2], // Expected Products array
                $this->getBusinessActor('PLUSNET_STAFF')
            ),
        );
    }

    /**
     * Getter for generating a business actor for the tests
     *
     * @param string $userType The user type of the business actor
     *
     * @return Auth_BusinessActor
     */
    protected function getBusinessActor($userType)
    {
        $businessActor = new Auth_BusinessActor(null);
        $businessActor->setRealm('workplace.plus.net');
        $businessActor->setUserType($userType);
        return $businessActor;
    }
}
