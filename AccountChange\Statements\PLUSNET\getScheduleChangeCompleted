server: coredb
role: slave
rows: single
statement:

select count(*)
from userdata.service_change_schedule ucs
INNER JOIN userdata.services s on s.service_id = ucs.service_id
INNER JOIN userdata.tblProvisionedService ps on s.service_id = ps.intServiceID
INNER JOIN products.tblSupplierProduct sp ON ps.intSupplierProductID = sp.intSupplierProductID
INNER JOIN products.service_definitions psd ON s.type = psd.service_definition_id
INNER JOIN products.tblSupplierProductType spt ON sp.intSupplierProductTypeID = spt.intSupplierProductTypeID
INNER JOIN products.tblSupplierPlatform plt ON spt.intSupplierPlatformID = plt.intSupplierPlatformID
WHERE s.status='active'
AND s.isp in ('plus.net','john<PERSON><PERSON>','partner')
AND plt.vchHandle = 'BT21CN'
AND ps.dtmEnd IS NULL
AND ucs.service_id = :service_id
AND ucs.change_complete ='yes'
AND DATEDIFF(NOW(),ucs.change_complete_date) <= 1
