<?php
/**
 * Created by IntelliJ IDEA.
 * User: kbuthpur
 * Date: 2020-01-30
 * Time: 17:03
 */

use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\CampaignNotificationClient\Exception\PersonalisedPromotionNotFoundException;

class AccountChange_PromotionCodePersonalisedPolicy extends \AccountChange_AbstractValidationPolicy
{

    const C2M_PROMOTION_CODE = 'C2MPromotionCode';

    const C2M_PROMOTION_PERSONALISED = 'PersonalisedOffer';

    const C2M_CLIENT = 'C2mClient';

    const CAMPAIGN_NOTIFICATION_CLIENT = 'CampaignNotificationClient';

    const CURRENT_DATE = 'CurrentDate';

    const ERROR_MESSAGE = 'Oops, that offer isn\'t available. Please call us on 0800 432 0080 to find out the latest deal we can give you.';

    const SALES_CHANNEL = 'SalesChannel';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_PROMO_PERSONALISED_CODE';

    private $campaignNotificationClient;
    private $c2mClient;
    private $currentDate;
    protected $actor;

    public function __construct(Auth_BusinessActor $actor, $isWorkplace = false, $isScript = false, $additionalInformation = array())
    {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);

        $this->actor = $actor;

        $this->campaignNotificationClient = \BusTier_BusTier::getClient('campaignNotificationClient');

        $this->c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');

        $this->currentDate = (new \DateTime())->format('Y-m-d');
    }

    /**
     * @return bool
     */
    public function validate()
    {
        /*
         *  checks if promotion has personalisedoffer set to true in C2M,
         *  promotion is applicable to the channel(campaign) of the customer &
         *  the claim date is in between start and end date of promotion set by customer domain
         *  @return bool
         */
        $externalUserId     =   $this->actor->getExternalUserId();
        $c2mPromotion       =   $this->getC2mPromotionFromAdditionalData();
        $additionalInfo     =   $this->getAdditionalInformation();
        $promotionCode      =   null;
        $isPersonalised     =   false;

        if ($c2mPromotion instanceof Promotion) {
            $promotionCode  = $c2mPromotion->getCode();
            $isPersonalised = $c2mPromotion->getIsPersonalisedOffer();
        }
        // Service id can come from ServiceId (from workplace) or external user id (logged in user)
        if (isset($additionalInfo['ServiceId'])) {
            $serviceId = $additionalInfo['ServiceId'];
        } elseif(isset($externalUserId)) {
            $serviceId = $externalUserId;
        } else {
            $serviceId = '';
        }

        $channel        = isset($additionalInfo[self::SALES_CHANNEL]) ? $additionalInfo[self::SALES_CHANNEL] : '';
        $promoOfferCode = isset($additionalInfo[self::C2M_PROMOTION_CODE])
                            && !is_null($additionalInfo[self::C2M_PROMOTION_CODE])
                            && !empty(trim($additionalInfo[self::C2M_PROMOTION_CODE]));

        if ($promoOfferCode) {
            $salesChannelData = $this->c2mClient->getSalesChannelData($channel);
            if ($isPersonalised && ($salesChannelData->isCampaignFlagEcn() || $salesChannelData->isCampaignFlagAbtn())) {
                try {
                    $personalisedPromoData = $this->campaignNotificationClient->getPersonalisedPromotion($serviceId, $promotionCode);
                } catch (PersonalisedPromotionNotFoundException $e) {
                    Log_AuditLog::write('Validation failed as personalised promotion not found', __CLASS__);
                }
                if (is_null($personalisedPromoData) ||
                    is_null($personalisedPromoData->getStartDate()) ||
                    is_null($personalisedPromoData->getEndDate()) ||
                    $this->currentDate < $personalisedPromoData->getStartDate() ||
                    $this->currentDate > $personalisedPromoData->getEndDate()) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
