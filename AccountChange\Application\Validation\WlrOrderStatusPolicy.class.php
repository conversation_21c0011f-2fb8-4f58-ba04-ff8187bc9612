<?php

require_once "/local/data/mis/database/database_libraries/components/component-defines.inc";

class AccountChange_WlrOrderStatusPolicy extends AccountChange_AbstractValidationPolicy
{
    /**
     * @var int The component ID of the
     */
    private $phoneComponentID;

    const ERROR_MESSAGE = 'You cannot make any changes to your account while there is a phone order in progress.';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_PHONE_ORDER_IN_PROGRESS';

    /**
     * @return bool
     */
    public function validate()
    {
        $serviceId = $this->actor->getExternalUserId();

        $phoneComponentTypeID = $this->getPhoneComponentTypeID($serviceId);

        return $phoneComponentTypeID === null || !$this->hasPendingOrder($phoneComponentTypeID);
    }

    /**
     * @param int $serviceId    Service ID to get type ID for.
     * @return int
     */
    protected function getPhoneComponentTypeID($serviceId)
    {
        if (!class_exists(CProduct::class)) {
            include_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        }

        $this->phoneComponentID = CProduct::getComponentIDByServiceID(
            $serviceId,
            'WLR',
            array('queued-activate', 'queued-deactivate', 'queued-destroy', 'queued-reactivate', 'active')
        );

        if (!$this->phoneComponentID) {
            return null;
        }

        $phoneComponent = CComponent::createInstance($this->phoneComponentID);

        if (!$phoneComponent) {
            return null;
        }

        return $phoneComponent->getComponentTypeID();
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }

    /**
     * @param integer $phoneComponentTypeID Component ID of the phone component.
     * @return array
     */
    protected function getCallFeatureTypes($phoneComponentTypeID)
    {
        return CWlrCallFeature::getAllCallFeatures($phoneComponentTypeID);
    }

    /**
     * @param  array $featureTypes    A list of components
     * @param  array $componentStates The component types to return.
     * @return array
     */
    protected function getComponentInstances($featureTypes, $componentStates)
    {
        return CWlrProduct::getSelectedProductComponentInstanceIDs(
            $featureTypes,
            $componentStates,
            $this->phoneComponentID
        );
    }

    /**
     * @param  integer  $phoneComponentTypeID Component Type ID
     * @return bool
     */
    protected function hasPendingOrder($phoneComponentTypeID)
    {
        try {
            $features = $this->getCallFeatureTypes($phoneComponentTypeID);

            $unconfiguredFeatures = $this->getComponentInstances(
                $features,
                array(PRODUCT_COMPONENT_UNCONFIGURED)
            );

            $queuedDeactivateFeatures = $this->getComponentInstances(
                $features,
                array(PRODUCT_COMPONENT_QUEUED_DEACTIVATE)
            );

            return $unconfiguredFeatures || $queuedDeactivateFeatures;
        } catch (\Exception $e) {
            return false;
        }
    }
}
