<?php
/**
 * Account Change Line rental saver common actions
 *
 * @category  AccountChange_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-10-28
 */
/**
 * Account Change Line rental saver common actions
 *
 * This class contains elements common to the LineRentalSaver
 * actions (only LineRentalSaverAdd at the moment)
 *
 * @category  AccountChange_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class AccountChange_Action_LineRentalSaver extends AccountChange_Action
{

    /**
     * Line rental saver instance
     *
     * @var PrepaidContract_Instance
     **/
    protected $lrsInstance = null;

    /**
     * Gets a prepaid contract lrs instance for the current service
     *
     * @param string $componentStatus What status should the lrs pci be in?
     *
     * @return void
     **/
    public function createLrsInstance($componentStatus = 'ACTIVE')
    {
        $serviceId = $this->getServiceId();
        $this->setLrsInstance(
            $this->getLineRentalSaver($serviceId, $componentStatus)
        );
    }

    /**
     * Gets a prepaid contract instance on service id and component status
     *
     * @param int    $serviceId       The service id
     * @param string $componentStatus Product component instance status for lrs
     *
     * @return PrepaidContract_Instance
     **/
    protected function getLineRentalSaver($serviceId, $componentStatus)
    {
        return PrepaidContract_Factory::getLineRentalSaver(
            $serviceId,
            $componentStatus
        );

    }


    /**
     * Getter for the current line rental saver instance
     *
     * @return PrepaidContract_Instance
     **/
    public function getLrsInstance()
    {
        return $this->lrsInstance;
    }

    /**
     * Setter for the line rate saver instance
     *
     * @param PrepaidContract_Instance $instance LRS instance
     *
     * @return void
     **/
    public function setLrsInstance($instance)
    {
        $this->lrsInstance = $instance;
    }


    /**
     * Get details of any already existing line rental saver product
     *
     * @return I18n_Date|bool End date of current contract or false if there isn't one
     **/
    public function getEndDateOfActiveLrs()
    {
        $instance = $this->getLrsInstance();

        if (!($instance instanceof PrepaidContract_Instance) || !$instance->hasActiveContract()) {
            return false;
        }

        return $instance->getContractExpiryDate();
    }

    /**
     * This should be overriden in the child classes
     *
     * @return void
     **/
    public function execute()
    {
        // Override this in the child classes, or the
        // action will not do anything.
    }

    /**
     * Get existing tariff id for an lrs instance
     *
     * @param int $pcid Line rental saver product component instance id
     *
     * @return int
     **/
    protected function getTariffIdFromInstanceId($pcid)
    {
        $database = Db_Manager::getAdaptor('AccountChange');
        $tariffId = $database->getTariffIdFromCurrentPcid($pcid);
        return $tariffId;
    }
}
