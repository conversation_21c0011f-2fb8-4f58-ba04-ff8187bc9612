server: coredb
role: slave
rows: single
statement:

SELECT
    s.vch<PERSON>andle AS vchSupplierHandle,
    s.vchDisplayName AS vchSupplierName,
    s.intSupplierID,
    spt.vchContention,
    spt.vchInstallType,
    spt.intSupplierPlatformID,
    id.vchRealmProvisioned,
    sp.*,
    ps.*,
    pset.vchDisplayName AS vchEventName,
    plt.vchHandle AS vchSupplierPlatform
FROM products.tblSupplier s
INNER JOIN products.tblSupplierProductType spt ON s.intSupplierID = spt.intSupplierID
INNER JOIN products.tblSupplierProduct sp ON sp.intSupplierProductTypeID = spt.intSupplierProductTypeID
INNER JOIN userdata.tblProvisionedService ps ON ps.intSupplierProductID = sp.intSupplierProductID
INNER JOIN userdata.tblProvisionedServiceEventType pset ON pset.intProvisionedServiceEventTypeID = ps.intProvisionedServiceEventTypeID
INNER JOIN adsl.install_diary id ON id.service_id = ps.intServiceID
INNER JOIN products.tblSupplierPlatform plt ON spt.intSupplierPlatformID = plt.intSupplierPlatformID
WHERE ps.intServiceID = :intServiceId
AND ps.dtmStart IS NOT NULL
AND ps.dtmEnd IS NULL
