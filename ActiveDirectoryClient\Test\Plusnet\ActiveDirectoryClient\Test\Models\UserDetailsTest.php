<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\ActiveDirectoryClient\Models\Test;

use Plusnet\ActiveDirectoryClient\Models\UserDetails;

/**
 * Class UserDetailsTest
 *
 * @package Test\Plusnet\ActiveDirectoryClient
 */
class UserDetailsTest extends \PHPUnit_Framework_TestCase
{
    /**
     * @test
     *
     * Test that with a different combination of valid and invalid EIN's and
     * Partner flags return the correct data.
     *
     * @param mixed $ein                  Agents EIN
     * @param mixed $isPartner            Is partner agent
     * @param mixed $expectedEIN          Whats the expected EIN
     * @param mixed $expectedIsPartner    Whats the expected is_partner
     * @param bool  $isDataValidEIN       Should the EIN be valid
     * @param bool  $isDataValidIsPartner Should is partner be valid
     *
     * @dataProvider dataProvided
     */
    public function isProvidedUserDetailsCorrectTest(
        $ein,
        $isPartner,
        $expectedEIN,
        $expectedIsPartner,
        $isDataValidEIN,
        $isDataValidIsPartner
    ) {
        $userDetails = new UserDetails($ein, $isPartner);

        $actualEIN = $userDetails->getEIN();
        $actualIsPartner = $userDetails->getIsPartner();

        $this->assertSame($expectedEIN, $actualEIN);
        $this->assertSame($expectedIsPartner, $actualIsPartner);
        $this->assertSame($isDataValidEIN, $userDetails->hasValidEin());
        $this->assertSame($isDataValidIsPartner, $userDetails->hasValidIsPartner());
    }

    /**
     * Provide data for test.
     *
     * @return array
     */
    public function dataProvided()
    {
        return array(
            'EIN is 10 digit number and is_partner is valid' => array(
                'ein'                  => '**********',
                'is_partner'           => true,
                'expectedEIN'          => 'INVALID_EIN',
                'expectedIsPartner'    => true,
                'isDataValidEIN'       => false,
                'isDataValidIsPartner' => true
            ),
            'EIN is 9 digit number and is_partner is valid'  => array(
                'ein'                  => '123456789',
                'is_partner'           => true,
                'expectedEIN'          => '123456789',
                'expectedIsPartner'    => true,
                'isDataValidEIN'       => true,
                'isDataValidIsPartner' => true
            ),
            'EIN is not just digits but is_partner is valid' => array(
                'ein'                  => 'NOTaNumber123',
                'is_partner'           => true,
                'expectedEIN'          => 'INVALID_EIN',
                'expectedIsPartner'    => true,
                'isDataValidEIN'       => false,
                'isDataValidIsPartner' => true
            ),
            'Invalid EIN and isPartner not a boolean'        => array(
                'ein'                  => '123',
                'is_partner'           => 'notbool',
                'expectedEIN'          => 'INVALID_EIN',
                'expectedIsPartner'    => 'INVALID_PARTNER',
                'isDataValidEIN'       => false,
                'isDataValidIsPartner' => false
            ),
            'Valid EIN and isPartner is invalid'             => array(
                'ein'                  => '123456789',
                'is_partner'           => 'notbool',
                'expectedEIN'          => '123456789',
                'expectedIsPartner'    => 'INVALID_PARTNER',
                'isDataValidEIN'       => true,
                'isDataValidIsPartner' => false
            )
        );
    }

    /**
     * @test
     *
     * Test that the UserDetails object can be successfully serialised into JSON.
     *
     */
    public function iCanJsonSerializeTheUserDetails()
    {
        $ein = '123456789';
        $partner = true;

        $expectedJson = json_encode(array(
                                        UserDetails::KEY_EIN        => $ein,
                                        UserDetails::KEY_IS_PARTNER => $partner
                                    ));

        $actualJson = json_encode(new UserDetails($ein, $partner));

        $this->assertSame($expectedJson, $actualJson);
    }
}
