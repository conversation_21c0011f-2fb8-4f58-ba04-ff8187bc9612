<?php
require_once __DIR__.'/../../Libraries/BizProductsHelper.class.php';

/**
 * Class AccountChange_BizProductsHelper
 *
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_BizProductsHelperTest extends PHPUnit_Framework_TestCase
{

    /**
     * Array of product sdi numbers for test data.
     *
     * @var array
     */
    protected $products = [];

    /**
     * Set up test data
     *
     * @return void
     */
    public function setUp()
    {
        $this->products = [
            'validADSLCapped' => 6801,
            'invalidADSLCapped' => 1234,
            'validFibreCapped' => 6807,
            'invalidFibreCapped' => 5678
        ];
    }

    /**
     * Test isCappedADSLProduct returns true when appropriate.
     *
     * @covers AccountChange_BizProductsHelper::isCappedADSLProduct
     */
    public function testItReturnsTrueWhenNotADSLCappedProduct()
    {
        $this->assertTrue(
            AccountChange_BizProductsHelper::isCappedADSLProduct(
                $this->products['validADSLCapped']
            )
        );
    }

    /**
     * Test isCappedADSLProduct returns false when appropriate.
     *
     * @covers AccountChange_BizProductsHelper::isCappedADSLProduct
     */
    public function testItReturnsFalseWhenNotADSLCappedProduct()
    {
        $this->assertFalse(
            AccountChange_BizProductsHelper::isCappedADSLProduct(
                $this->products['invalidADSLCapped']
            )
        );
    }

    /**
     * Test isCappedFibreProduct returns true when appropriate.
     *
     * @covers AccountChange_BizProductsHelper::isCappedFibreProduct
     */
    public function testItReturnsTrueWhenNotFibreCappedProduct()
    {
        $this->assertTrue(
            AccountChange_BizProductsHelper::isCappedFibreProduct(
                $this->products['validFibreCapped']
            )
        );
    }

    /**
     * Test isCappedFibreProduct returns false when appropriate.
     *
     * @covers AccountChange_BizProductsHelper::isCappedFibreProduct
     */
    public function testItReturnsFalseWhenNotFibreCappedProduct()
    {
        $this->assertFalse(
            AccountChange_BizProductsHelper::isCappedFibreProduct(
                $this->products['invalidFibreCapped']
            )
        );
    }
}
