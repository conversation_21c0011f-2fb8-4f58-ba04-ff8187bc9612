<?php

class AccountChange_Action_CallerDisplay extends AccountChange_Action
{
    /**
     * Overrides default constructor to add obtaining the relevent data from the registry.
     *
     * @param int   $serviceId The service id
     * @param array $options   Array of options
     *
     * @see AccountChange_Action::_construct
     *
     * @return void
     */
    public function __construct($serviceId, $options)
    {
        parent::__construct($serviceId, $options);
    }

    /**
     * Execute the action
     *  safe to run more than once.
     *
     * @return void
     */
    public function execute()
    {
        /*
          Caller display is normally added at the point of wlr component creation,
          that's either:
               - Product_Wlr::doSignup for an account coming from no previous call plan or
               - Product_Wlr::create for an account with changing call plan.
           They both place the order for caller display as part of the phone order, so this action
           only places an order if: we've already got a call plan & there's no change to that call plan
           & we want to add / remove caller display and it's not a house move (house moves never place caller
           display orders because they have to submit other orders at the same time).
        */
        if (
            !$this->hasCallPlanAlreadyAndCallPlanIsNotChanging() ||
            !$this->callerDisplayOptionSetInRegistry() ||
            $this->houseMove()
        ) {
            return;
        }

        $registry = AccountChange_Registry::instance();

        $userWantsCallerDisplay = $registry->getEntry('callerDisplay');
        $userHasCallerDisplay = $this->hasCallerDisplay();

        if ($userHasCallerDisplay && !$userWantsCallerDisplay) {
            $this->getCallFeatureApi()->removeCallFeature(
                AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                $this->intServiceId
            );
        } elseif (!$userHasCallerDisplay && $userWantsCallerDisplay) {
            $this->getCallFeatureApi()->addCallFeature(
                AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                $this->intServiceId
            );
        }
    }

    /**
     * Check to see if we should be running this action - i.e. the customer has a call plan
     * and that plan is not going to be changing (other scenarios are provisioned elsewhere)
     *
     * @return bool
     */
    protected function hasCallPlanAlreadyAndCallPlanIsNotChanging()
    {
        $registry = AccountChange_Registry::instance();
        $newWlrServiceComponentId = $registry->getEntry('newWlrServiceComponentId');
        $oldWlrServiceComponentId = $registry->getEntry('oldWlrServiceComponentId');
        return ($this->hasWlr() && ($newWlrServiceComponentId == $oldWlrServiceComponentId));
    }

    /**
     * Check to see if the caller display option is actually set in the registry.
     * If no specific option is set, then we want to leave caller display as-is, i.e. don't
     * run this action.
     *
     * @return bool
     */
    protected function callerDisplayOptionSetInRegistry()
    {
        $registry = AccountChange_Registry::instance();
        return !is_null(($registry->getEntry('callerDisplay')));
    }

    /**
     * Check to see if the house move option is set in the registry
     *
     * @return bool
     */
    protected function houseMove()
    {
        $registry = AccountChange_Registry::instance();
        return $registry->getEntry('bolHousemove');
    }

    /**
     * Does a given service Id have an active or pending Wlr component?
     *
     * @return bool
     */
    protected function hasWlr()
    {
        return (CWlrProduct::getWlrProductFromServiceId($this->getServiceId()) !== false);
    }

    /**
     * Does the customer already have caller display in an active or pending state
     *
     * @return int
     */
    protected function hasCallerDisplay()
    {
        return $this->getCallFeatureApi()->customerHasCallFeature(
            AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
            $this->getServiceId()
        );
    }

    /**
     * Get a call feature API
     *
     * @return AccountChange_CallFeature_Api
     * @codeCoverageIgnore
     */
    protected function getCallFeatureApi()
    {
        return new AccountChange_CallFeature_Api();
    }

}
