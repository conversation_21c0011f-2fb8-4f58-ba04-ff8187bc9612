<?php
/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_BackDatedDatePolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_CODE = 'ERROR_INVALID_BACKDATED_DATE';
    const ERROR_MESSAGE_NOT_IN_PAST = 'Back dated date is not in the past';
    const ERROR_MESSAGE_NOT_INSTANT = 'Back dated product change cannot be scheduled';
    const ERROR_MESSAGE_INVALID_VALUE = 'Invalid value: %s';
    const ERROR_MESSAGE_BEFORE_MOST_RECENT_SERVICE_EVENT = 'Back dated date cannot be before most recent service event';

    /**
     * @var string
     */
    private $failureReason;

    /**
     * @var DateTime
     */
    private $backDatedDate;

    /**
     * @var bool
     */
    private $isScheduledChange;

    /**
     * @return bool
     */
    public function validate()
    {
        try {
            $this->setAdditionalInformation();
            if ($this->isScheduledChange) {
                throw new AccountChange_AccountChangeNotAllowedException(
                    static::ERROR_MESSAGE_NOT_INSTANT
                );
            }
            $this->isBackDatedDateInThePast();
            $this->isBackDatedDateWithinValidRange();
        } catch (AccountChange_AccountChangeNotAllowedException $e) {
            $this->failureReason = $e->getMessage();
            return false;
        } catch (InvalidArgumentException $e) {
            $this->failureReason = $e->getMessage();
            return false;
        }

        return true;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return $this->failureReason;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return static::ERROR_CODE;
    }

    /**
     * @return void
     * @throws AccountChange_AccountChangeNotAllowedException
     */
    private function isBackDatedDateInThePast()
    {
        if ($this->backDatedDate >= new DateTime('today midnight')) {
            throw new AccountChange_AccountChangeNotAllowedException(static::ERROR_MESSAGE_NOT_IN_PAST);
        }
    }

    /**
     * As per BILL-16437 - date must be no earlier than most recent service event
     *
     * @return void
     * @throws AccountChange_AccountChangeNotAllowedException
     */
    private function isBackDatedDateWithinValidRange()
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $maxDaysInThePast = (int)$db->getDaysDiffOfPreviousProductChange($this->actor->getExternalUserId());
        $maxPastDate = (new DateTime('today midnight'))->modify("-$maxDaysInThePast days");

        if ($this->backDatedDate < $maxPastDate) {
            throw new AccountChange_AccountChangeNotAllowedException(
                static::ERROR_MESSAGE_BEFORE_MOST_RECENT_SERVICE_EVENT
            );
        }
    }

    /**
     * Sets class properties for backDatedDate and Scheduled Change
     * @return void
     */
    private function setAdditionalInformation()
    {
        $additionalInfo = $this->getAdditionalInformation();
        if (array_key_exists('backDatedDate', $additionalInfo) &&
            $additionalInfo['backDatedDate'] instanceof DateTime
        ) {
            $this->backDatedDate = $additionalInfo['backDatedDate'];
        } else {
            throw new InvalidArgumentException(
                sprintf(static::ERROR_MESSAGE_INVALID_VALUE, 'backDatedDate')
            );
        }

        if (array_key_exists('isScheduledChange', $additionalInfo) && is_bool($additionalInfo['isScheduledChange'])) {
            $this->isScheduledChange = $additionalInfo['isScheduledChange'];
        } else {
            throw new InvalidArgumentException(
                sprintf(static::ERROR_MESSAGE_INVALID_VALUE, 'isScheduledChange')
            );
        }
    }
}
