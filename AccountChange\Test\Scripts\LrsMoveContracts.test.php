<?php
/**
 * LRS Move scrip tests
 *
 * Testing class for the AccountChange_LrsMoveContracts
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 * @since      File available since 2011-12-08
 */
/**
 * Testing class for the AccountChange_LrsMoveContracts
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
class Test_AccountChange_LrsMoveContracts extends PHPUnit_Framework_TestCase
{
    const SCRIPT_NAME = 'AccountChange_LrsMoveContracts';

    /**
     * Tests command
     *
     * @covers AccountChange_LrsMoveContracts::processCommandLineArgs
     *
     * @return void
     */
    public function testProcessCommandLineArgsEmpty()
    {
        $script = new AccountChange_LrsMoveContracts();

        $script->processCommandLineArgs(array());
        $actual   = $script->getRestrictTo();
        $expected = array();
        $this->assertEquals($expected, $actual);
    }

    /**
     * @covers AccountChange_LrsMoveContracts::processCommandLineArgs
     *
     * @return void
     */
    public function testProcessCommandLineArgsSinlgeVal()
    {
        $script = new AccountChange_LrsMoveContracts();

        $script->processCommandLineArgs(array('s'=>'1'));
        $actual   = $script->getRestrictTo();
        $expected = array('1'=>'1');
        $this->assertEquals($expected, $actual);
    }

    /**
     * @covers AccountChange_LrsMoveContracts::processCommandLineArgs
     *
     * @return void
     */
    public function testProcessCommandLineArgsMultiVals()
    {
        $script = new AccountChange_LrsMoveContracts();

        $script->processCommandLineArgs(array('s'=>array('1','123')));
        $actual   = $script->getRestrictTo();
        $expected = array('1'=>'1', '123'=>'123');
        $this->assertEquals($expected, $actual);
    }

    /**
     * @covers AccountChange_LrsMoveContracts::checkAndAddSid
     *
     * @return void
     */
    public function testCheckAndAddSidValid()
    {
        $script = TestCaseWithProxy::getPHPUnitProxy('AccountChange_LrsMoveContracts', array());
        $expected = true;
        $actual = $script->protected_checkAndAddSid(199);

        $this->assertEquals($expected, $actual);
        $expected = array(199=>199);
        $actual = $script->getRestrictTo();
        $this->assertEquals($expected, $actual);
    }

    /**
     * @covers AccountChange_LrsMoveContracts::checkAndAddSid
     *
     * @return void
     */
    public function testCheckAndAddSidInValid()
    {
        $script = TestCaseWithProxy::getPHPUnitProxy('AccountChange_LrsMoveContracts', array());
        $expected = false;
        $actual = $script->protected_checkAndAddSid('ss');

        $this->assertEquals($expected, $actual);

        //empty array
        $expected = array();
        $actual = $script->getRestrictTo();
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test the flow of run()
     *
     * @covers AccountChange_LrsMoveContracts::run
     *
     * @return void
     **/
    public function testFlowOfRunNoCommandLineArgs()
    {

        $allAccounts = array(12345, 34567, 2211221, 77846);

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getAccountsToExcludeInBilling',
            ),
            array('Financial', Db_Manager::DEFAULT_TRANSACTION, false)
        );


        $mockDbAdaptor->expects($this->any())
            ->method('getAccountsToExcludeInBilling')
            ->will($this->returnValue(array(1, 2, 77846)));

        Db_Manager::setAdaptor('Financial', $mockDbAdaptor);

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'processCommandLineArgs',
                'setupOutputBuffer',
                'output',
                'processAccount',
                'getAccounts'
            ),
            array()
        );

        $script
            ->expects($this->at(2))
            ->method('output')
            ->with('Command line params processed.', true);

        $script
            ->expects($this->at(3))
            ->method('output')
            ->with('No restictions applied.', true);

        $script
            ->expects($this->once())
            ->method('setupOutputBuffer');

        $script
            ->expects($this->once())
            ->method('processCommandLineArgs');

        $script
            ->expects($this->once())
            ->method('getAccounts')
            ->will($this->returnValue($allAccounts));


        $script
            ->expects($this->at(6))
            ->method('processAccount')
            ->with($allAccounts[0]);

        $script
            ->expects($this->at(8))
            ->method('processAccount')
            ->with($allAccounts[1]);

        $script
            ->expects($this->at(10))
            ->method('processAccount')
            ->with($allAccounts[2]);

        $script->run(array(), array());

    }

    /**
     * Test the flow of run()
     *
     * @covers AccountChange_LrsMoveContracts::run
     *
     * @return void
     **/
    public function testFlowOfRunWithCommandLineArgs()
    {

        $allAccounts = array(12345, 34567, 2211221);


        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'processCommandLineArgs',
                'setupOutputBuffer',
                'output',
                'processAccount',
                'getRestrictTo'
            ),
            array()
        );

        $script
            ->expects($this->at(2))
            ->method('output')
            ->with('Command line params processed.', true);

        $script
            ->expects($this->at(4))
            ->method('output')
            ->with('Restricted to accounts specified on command line.', true);

        $script
            ->expects($this->once())
            ->method('setupOutputBuffer');

        $script
            ->expects($this->once())
            ->method('processCommandLineArgs');

        $script
            ->expects($this->once())
            ->method('getRestrictTo')
            ->will($this->returnValue($allAccounts));

        $script
            ->expects($this->at(9))
            ->method('processAccount')
            ->with($allAccounts[2]);

        $script->run(array(), array());
    }

    /**
     * Test the flow of run() when account throws exception
     *
     * @covers AccountChange_LrsMoveContracts::run
     *
     * @return void
     **/
    public function testFlowOfRunWithError()
    {
        $allAccounts = array(2211221);

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'processCommandLineArgs',
                'setupOutputBuffer',
                'output',
                'processAccount',
                'getRestrictTo'
            ),
            array()
        );

        $script
            ->expects($this->at(2))
            ->method('output')
            ->with('Command line params processed.', true);

        $script
            ->expects($this->at(4))
            ->method('output')
            ->with('Restricted to accounts specified on command line.', true);

        $script
            ->expects($this->once())
            ->method('setupOutputBuffer');

        $script
            ->expects($this->once())
            ->method('processCommandLineArgs');

        $script
            ->expects($this->once())
            ->method('getRestrictTo')
            ->will($this->returnValue($allAccounts));

        $exception = new Exception('test exception');

        $script
            ->expects($this->once())
            ->method('processAccount')
            ->will($this->throwException($exception));

        $script->run(array(), array());
    }

    /**
     * Tests that if testFailToAddPendingEntryThrowsExceptionAndCancelsPayment
     * returns true we do a search and destroy on line rental payments
     *
     * @covers AccountChange_LrsMoveContracts::processAccount
     *
     * @return void
     **/
    public function testProcessAccountWithTransfer()
    {
        $sid = ********;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'output',
                'getPrepaidContractInstance',
                'findLrsDestroyedByAccountChangeAndTransfer',
                'checkForAndCancelAnyLineRentalScheduledPayments',
                'getCurrentLrsPayments'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(),
            array(123456)
        );

        $script
            ->expects($this->once())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->will($this->returnValue($prepaidContract));

        $script
            ->expects($this->once())
            ->method('getCurrentLrsPayments')
            ->will($this->returnValue(array(1234)));

        $script
            ->expects($this->once())
            ->method('findLrsDestroyedByAccountChangeAndTransfer')
            ->will($this->returnValue(true));

        $script
            ->expects($this->once())
            ->method('checkForAndCancelAnyLineRentalScheduledPayments')
            ->with($sid);

        $script->processAccount($sid);
    }

    /**
     * Tests the successful flow fro processAccount
     *
     * @covers AccountChange_LrsMoveContracts::processAccount
     *
     * @return void
     **/
    public function testSuccessfulFlowOfProcessAccount()
    {

        $pcid = 123456;
        $tariffId = 1088;
        $billingDate = null;
        $scheduledPaymentId = 234567;
        $sid = 234567;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'findLrsDestroyedByAccountChangeAndTransfer',
                'createScheduledPaymentForLrs',
                'createPendingLrs',
                'output',
                'getCoreService',
                'getAnyPaidOrPendingLrsScheduledPayments',
                'getLrsSalesInvoiceItem',
                'checkForAndCancelAnyLineRentalScheduledPayments',
                'getCurrentLrsPayments'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'getTariffByDuration',
                'add',
                'hasActiveContract',
                'getPendingPrepaidContracts',
                'redeem'
            ),
            array($pcid)
        );

        $pendingEntry = $this->getMock(
            'PrepaidContract_PendingEntry',
            array(),
            array()
        );

        $coreService = $this->getMock(
            'Core_Service',
            array('getBillingDate'),
            array()
        );

        $coreService
            ->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue('2016-01-01'));


        $prepaidContract
            ->expects($this->once())
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $script
            ->expects($this->once())
            ->method('findLrsDestroyedByAccountChangeAndTransfer')
            ->with($sid)
            ->will($this->returnValue(false));

        $script
            ->expects($this->once())
            ->method('getCurrentLrsPayments')
            ->will($this->returnValue(array(1234)));

        $prepaidContract
            ->expects($this->once())
            ->method('hasActiveContract')
            ->will($this->returnValue(false));

        $prepaidContract
            ->expects($this->once())
            ->method('getTariffByDuration')
            ->with('ANNUAL')
            ->will($this->returnValue(array('intTariffID' =>$tariffId)));

        $prepaidContract
            ->expects($this->once())
            ->method('add')
            ->with($pendingEntry);

        $prepaidContract
            ->expects($this->once())
            ->method('getPendingPrepaidContracts')
            ->will($this->returnValue(array($pendingEntry)));

        $prepaidContract
            ->expects($this->once())
            ->method('redeem')
            ->with($pendingEntry);


        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getCoreService')
            ->with($sid)
            ->will($this->returnValue($coreService));

        $script
            ->expects($this->once())
            ->method('getAnyPaidOrPendingLrsScheduledPayments')
            ->with($sid)
            ->will($this->returnValue(null));

        $script
            ->expects($this->once())
            ->method('getLrsSalesInvoiceItem')
            ->with($sid)
            ->will($this->returnValue(null));

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->with($sid, 'ACTIVE')
            ->will($this->returnValue($prepaidContract));
        $script
            ->expects($this->once())
            ->method('createScheduledPaymentForLrs')
            ->will($this->returnValue($scheduledPaymentId));

        $script
            ->expects($this->once())
            ->method('createPendingLrs')
            ->with($scheduledPaymentId, $tariffId)
            ->will($this->returnValue($pendingEntry));


        $script
            ->expects($this->once())
            ->method('checkForAndCancelAnyLineRentalScheduledPayments')
            ->with($sid);

        $script->processAccount($sid);

    }

    /**
     * Tests the active lrs instance exception
     *
     * @covers AccountChange_LrsMoveContracts::processAccount
     *
     * @return void
     **/
    public function testFailedToCreatePaymentException()
    {
        $this->setExpectedException(
            'RunTimeException',
            'Failed to create a scheduled payment, skipping'
        );

        $pcid = 123456;
        $tariffId = 1088;
        $billingDate = null;
        $scheduledPaymentId = 234567;
        $sid = 234567;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'findLrsDestroyedByAccountChangeAndTransfer',
                'createScheduledPaymentForLrs',
                'createPendingLrs',
                'output',
                'getCoreService',
                'getAnyPaidOrPendingLrsScheduledPayments',
                'getLrsSalesInvoiceItem',
                'checkForAndCancelAnyLineRentalScheduledPayments',
                'getCurrentLrsPayments'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'getTariffByDuration',
                'add',
                'hasActiveContract',
                'getPendingPrepaidContracts',
                'redeem'
            ),
            array($pcid)
        );

        $pendingEntry = $this->getMock(
            'PrepaidContract_PendingEntry',
            array(),
            array()
        );

        $coreService = $this->getMock(
            'Core_Service',
            array('getBillingDate'),
            array()
        );

        $coreService
            ->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue('2016-01-01'));

        $prepaidContract
            ->expects($this->never())
            ->method('findLrsDestroyedByAccountChangeAndTransfer');

        $prepaidContract
            ->expects($this->once())
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $prepaidContract
            ->expects($this->once())
            ->method('hasActiveContract')
            ->will($this->returnValue(false));

        $prepaidContract
            ->expects($this->never())
            ->method('getTariffByDuration');

        $prepaidContract
            ->expects($this->never())
            ->method('add');

        $prepaidContract
            ->expects($this->never())
            ->method('getPendingPrepaidContracts');

        $prepaidContract
            ->expects($this->never())
            ->method('redeem');


        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getCurrentLrsPayments')
            ->will($this->returnValue(array(1234)));

        $script
            ->expects($this->once())
            ->method('getCoreService')
            ->with($sid)
            ->will($this->returnValue($coreService));

        $script
            ->expects($this->once())
            ->method('getAnyPaidOrPendingLrsScheduledPayments')
            ->with($sid)
            ->will($this->returnValue(-1));

        $script
            ->expects($this->once())
            ->method('getLrsSalesInvoiceItem')
            ->with($sid)
            ->will($this->returnValue(-1));

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->will($this->returnValue($prepaidContract));

        $script
            ->expects($this->never())
            ->method('createScheduledPaymentForLrs');

        $script
            ->expects($this->never())
            ->method('createPendingLrs');


        $script
            ->expects($this->never())
            ->method('checkForAndCancelAnyLineRentalScheduledPayments');

        $script->processAccount($sid);
    }

    /**
     * Tests the active lrs instance exception
     *
     * @covers AccountChange_LrsMoveContracts::processAccount
     *
     * @return void
     **/
    public function testProcessAccountLrsActiveException()
    {
        $pcid = 123456;
        $tariffId = 1088;
        $billingDate = null;
        $scheduledPaymentId = 234567;
        $sid = 234567;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'output'
            ),
            array()
        );

        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->with($sid, 'ACTIVE')
            ->will($this->returnValue(null));

        $this->setExpectedException(
            'UnexpectedValueException',
            'Unable to locate an active lrs instance to copy from'
        );

        $script->processAccount($sid);
    }

    /**
     * Tests the active lrs instance exception
     *
     * @covers AccountChange_LrsMoveContracts::processAccount
     *
     * @return void
     **/
    public function testProcessAccountInContractException()
    {
        $this->setExpectedException(
            'LogicException',
            'Nothing to do as this instance already has an active contract.'
        );

        $pcid = 123456;
        $tariffId = 1088;
        $billingDate = null;
        $scheduledPaymentId = 234567;
        $sid = 234567;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'output',
                'findLrsDestroyedByAccountChangeAndTransfer',
                'getCurrentLrsPayments'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'hasActiveContract',
            ),
            array($pcid)
        );

        $prepaidContract
            ->expects($this->once())
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $prepaidContract
            ->expects($this->once())
            ->method('hasActiveContract')
            ->will($this->returnValue(true));

        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->will($this->returnValue($prepaidContract));

        $script
            ->expects($this->once())
            ->method('getCurrentLrsPayments')
            ->will($this->returnValue(array(1234)));

        $script->processAccount($sid);
    }

    /**
     * Tests the active lrs instance exception
     *
     * @covers AccountChange_LrsMoveContracts::processAccount
     *
     * @return void
     **/
    public function testProcessBillingDateException()
    {
        $this->setExpectedException(
            'UnexpectedValueException',
            'Account has no billing date yet (probably in presignup) -- skipping.'
        );
        $pcid = 123456;
        $tariffId = 1088;
        $billingDate = null;
        $scheduledPaymentId = 234567;
        $sid = 234567;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'createScheduledPaymentForLrs',
                'findLrsDestroyedByAccountChangeAndTransfer',
                'output',
                'getCoreService',
                'getCurrentLrsPayments'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'hasActiveContract',
            ),
            array($pcid)
        );


        $coreService = $this->getMock(
            'Core_Service',
            array('getBillingDate'),
            array()
        );

        $coreService
            ->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue('9999-99-99'));


        $prepaidContract
            ->expects($this->once())
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $prepaidContract
            ->expects($this->once())
            ->method('hasActiveContract')
            ->will($this->returnValue(false));

        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('findLrsDestroyedByAccountChangeAndTransfer')
            ->will($this->returnValue(false));

        $script
            ->expects($this->once())
            ->method('getCoreService')
            ->with($sid)
            ->will($this->returnValue($coreService));

        $script
            ->expects($this->once())
            ->method('getCurrentLrsPayments')
            ->will($this->returnValue(array(1234)));

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->with($sid, 'ACTIVE')
            ->will($this->returnValue($prepaidContract));

        $script->processAccount($sid);
    }

    /**
     * Tests flow of processAccount where creating pending entry has failed
     *
     * @covers AccountChange_LrsMoveContracts::processAccount
     *
     * @return void
     **/
    public function testFailToAddPendingEntryThrowsExceptionAndCancelsPayment()
    {
        $this->setExpectedException(
            'RuntimeException',
            'Failed to create a new pending entry for lrs'
        );

        $pcid = 123456;
        $tariffId = 1088;
        $billingDate = null;
        $scheduledPaymentId = 234567;
        $sid = 234567;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'createScheduledPaymentForLrs',
                'findLrsDestroyedByAccountChangeAndTransfer',
                'createPendingLrs',
                'output',
                'getCoreService',
                'getAnyPaidOrPendingLrsScheduledPayments',
                'getLrsSalesInvoiceItem',
                'checkForAndCancelAnyLineRentalScheduledPayments',
                'cancelScheduledPayment',
                'getCurrentLrsPayments'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'getTariffByDuration',
                'add',
                'hasActiveContract',
                'getPendingPrepaidContracts',
                'redeem'
            ),
            array($pcid)
        );

        $pendingEntry = $this->getMock(
            'PrepaidContract_PendingEntry',
            array(),
            array()
        );

        $coreService = $this->getMock(
            'Core_Service',
            array('getBillingDate'),
            array()
        );

        $coreService
            ->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue('2016-01-01'));


        $prepaidContract
            ->expects($this->once())
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $script
            ->expects($this->once())
            ->method('findLrsDestroyedByAccountChangeAndTransfer')
            ->will($this->returnValue(false));

        $prepaidContract
            ->expects($this->once())
            ->method('hasActiveContract')
            ->will($this->returnValue(false));

        $prepaidContract
            ->expects($this->once())
            ->method('getTariffByDuration')
            ->with('ANNUAL')
            ->will($this->returnValue(array('intTariffID' =>$tariffId)));

        $prepaidContract
            ->expects($this->never())
            ->method('add');

        $prepaidContract
            ->expects($this->never())
            ->method('getPendingPrepaidContracts');

        $prepaidContract
            ->expects($this->never())
            ->method('redeem');


        $script
            ->expects($this->any())
            ->method('cancelScheduledPayment');

        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getCurrentLrsPayments')
            ->will($this->returnValue(array(1234)));

        $script
            ->expects($this->once())
            ->method('getCoreService')
            ->with($sid)
            ->will($this->returnValue($coreService));

        $script
            ->expects($this->once())
            ->method('getAnyPaidOrPendingLrsScheduledPayments')
            ->with($sid)
            ->will($this->returnValue(null));

        $script
            ->expects($this->once())
            ->method('getLrsSalesInvoiceItem')
            ->with($sid)
            ->will($this->returnValue(null));

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->with($sid, 'ACTIVE')
            ->will($this->returnValue($prepaidContract));
        $script
            ->expects($this->once())
            ->method('createScheduledPaymentForLrs')
            ->will($this->returnValue($scheduledPaymentId));

        $script
            ->expects($this->once())
            ->method('createPendingLrs')
            ->with($scheduledPaymentId, $tariffId)
            ->will($this->returnValue(null));


        $script
            ->expects($this->never())
            ->method('checkForAndCancelAnyLineRentalScheduledPayments');

        $script->processAccount($sid);
    }

    /**
     * Tests flow of createScheduledPaymentForLrs
     *
     * @covers AccountChange_LrsMoveContracts::createScheduledPaymentForLrs
     *
     * @return void
     **/
    public function testFlowOfCreateScheduledPaymentForLrs()
    {

        $pcid = 123456;
        $billingDate = *********;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPaymentScheduler'
            ),
            array()
        );

        $paymentScheduler = $this->getMock(
            'CProductComponentPaymentScheduler',
            array('addIncVatAmount'),
            array()
        );

        $paymentScheduler
            ->expects($this->once())
            ->method('addIncVatAmount')
            ->with(11388, $billingDate, 'Line Rental Saver charge');

        $script
            ->expects($this->once())
            ->method('getPaymentScheduler')
            ->with($pcid)
            ->will($this->returnValue($paymentScheduler));

        $script->createScheduledPaymentForLrs($pcid, $billingDate);
    }

    /**
     * Tests flow of findLrsDestroyedByAccountChangeAndTransfer
     *
     * @covers AccountChange_LrsMoveContracts::findLrsDestroyedByAccountChangeAndTransfer
     *
     * @return void
     **/
    public function testFlowOfFindLrsDestroyedByAccountChangeAndTransfer()
    {

        $date = I18n_Date::fromString('2011-12-15');
        $pcid = 123455;
        $sid = ********;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'output',
                'getEndDateOfDestroyedLrsInstance',
                'getAnyPaidOrPendingLrsScheduledPayments',
                'getLrsSalesInvoiceItem',
                'createScheduledPaymentForLrs',
                'getCoreService'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'getTariffByDuration',
                'transfer'
            ),
            array($pcid)
        );

        $coreService = $this->getMock(
            'Core_Service',
            array('getBillingDate'),
            array()
        );

        $coreService
            ->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue('2016-01-01'));

        $script
            ->expects($this->exactly(2))
            ->method('getPrepaidContractInstance')
            ->will($this->returnValue($prepaidContract));

        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getEndDateOfDestroyedLrsInstance')
            ->will($this->returnValue($date));

        $script
            ->expects($this->once())
            ->method('getAnyPaidOrPendingLrsScheduledPayments')
            ->will($this->returnValue(null));

        $script
            ->expects($this->once())
            ->method('getLrsSalesInvoiceItem')
            ->will($this->returnValue(null));

        $script
            ->expects($this->once())
            ->method('createScheduledPaymentForLrs');

        $script
            ->expects($this->once())
            ->method('getCoreService')
            ->with($sid)
            ->will($this->returnValue($coreService));

        $prepaidContract
            ->expects($this->exactly(2))
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $prepaidContract
            ->expects($this->once())
            ->method('getTariffByDuration')
            ->will($this->returnValue(array('intTariffID' => 1900)));

        $prepaidContract
            ->expects($this->once())
            ->method('transfer')
            ->with(1900, $date);

        $this->assertTrue(
            $script->findLrsDestroyedByAccountChangeAndTransfer($sid)
        );
    }

    /**
     * Tests flow of findLrsDestroyedByAccountChangeAndTransfer  with no found
     * pcid
     *
     * @covers AccountChange_LrsMoveContracts::findLrsDestroyedByAccountChangeAndTransfer
     *
     * @return void
     **/
    public function testFlowOfFindLrsDestroyedByAccountChangeAndTransferWithNoPcid()
    {

        $date = I18n_Date::fromString('2011-12-15');
        $pcid = 123455;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'output',
                'getEndDateOfDestroyedLrsInstance'
            ),
            array()
        );

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->will($this->returnValue(null));

        $script
            ->expects($this->any())
            ->method('output');

        $this->assertFalse(
            $script->findLrsDestroyedByAccountChangeAndTransfer(12345)
        );
    }

    /**
     * Tests flow of findLrsDestroyedByAccountChangeAndTransfer when there's no
     * end date
     *
     * @covers AccountChange_LrsMoveContracts::findLrsDestroyedByAccountChangeAndTransfer
     *
     * @return void
     **/
    public function testFlowOfFindLrsDestroyedByAccountChangeAndTransferNoEndDate()
    {
        $pcid = 123455;

        $script = $this->getMock(
            'AccountChange_LrsMoveContracts',
            array(
                'getPrepaidContractInstance',
                'output',
                'getEndDateOfDestroyedLrsInstance'
            ),
            array()
        );

        $prepaidContract = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'getTariffByDuration',
                'transfer'
            ),
            array($pcid)
        );

        $script
            ->expects($this->once())
            ->method('getPrepaidContractInstance')
            ->will($this->returnValue($prepaidContract));

        $script
            ->expects($this->any())
            ->method('output');

        $script
            ->expects($this->once())
            ->method('getEndDateOfDestroyedLrsInstance')
            ->will($this->returnValue(null));


        $prepaidContract
            ->expects($this->once())
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $prepaidContract
            ->expects($this->never())
            ->method('getTariffByDuration');

        $prepaidContract
            ->expects($this->never())
            ->method('transfer');

        $this->assertFalse(
            $script->findLrsDestroyedByAccountChangeAndTransfer(12345)
        );
    }

    /**
     * Check that the correct value is returned by getShortOpts
     *
     * @covers AccountChange_LrsMoveContracts::getShortOpts
     *
     * @return void
     */
    public function testGetShortOpts()
    {
        $expected = 's:ql:';
        $actual = AccountChange_LrsMoveContracts::getShortOpts();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Check that the correct value is returned by getLongOpts
     *
     * @covers AccountChange_LrsMoveContracts::getLongOpts
     *
     * @return void
     */
    public function testGetLongOpts()
    {
        $expected = array();
        $actual = AccountChange_LrsMoveContracts::getLongOpts();

        $this->assertEquals($expected, $actual);
    }
}
