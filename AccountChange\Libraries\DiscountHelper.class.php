<?php

/**
 * Discount related calculations
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Plusnet
 */

/**
 * Discount related calculations
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Plusnet
 */
class AccountChange_DiscountHelper
{
    const FIXED_DISCOUNT = 1;
    const PERCENTAGE_DISCOUNT = 2;

    /**
     * Calculates the amount of discount
     *
     * @param I18n_Currency $cost Cost of product
     * @param int $discountType AccountChange_DiscountHelper::FIXED_DISCOUNT, ::PERCENTAGE_DISCOUNT
     * @param float $discountValue Amount to discount by. Either a fixed amount or a percentage of $cost
     *
     * @return I18n_Currency discount
     */
    public static function calculateDiscountAmount(I18n_Currency $cost, $discountType, $discountValue)
    {

        switch($discountType)
        {
            case self::FIXED_DISCOUNT:
                $discountAmount = $discountValue;
                break;
            case self::PERCENTAGE_DISCOUNT:
                $discountAmount = round($cost->toDecimal() * ($discountValue / 100), 2, PHP_ROUND_HALF_UP);
                break;
            default:

                throw new AccountChange_DiscountHelperException('Invalid discountType');

        }

        return new I18n_Currency(
            AccountChange_Manager::CURRENCY_UNIT,
            $discountAmount
        );

    }

    /**
     * Calculates the leading price (discounted price) of a product
     *
     * @param I18n_Currency $cost          Cost of product
     * @param int           $discountType  Type of discount, 1=Fixed amount, 2=Percentage
     * @param float         $discountValue Amount to discount by. Either a fixed amount or a percentage of $cost
     *
     * @return I18n_Currency Leading price of product
     */
    public static function calculateLeadingPrice($cost, $discountType, $discountValue)
    {
        $leadingPrice = $cost->toDecimal() - static::calculateDiscountAmount($cost, $discountType, $discountValue)->toDecimal();

        return new I18n_Currency(
            AccountChange_Manager::CURRENCY_UNIT,
            $leadingPrice
        );
    }
}
