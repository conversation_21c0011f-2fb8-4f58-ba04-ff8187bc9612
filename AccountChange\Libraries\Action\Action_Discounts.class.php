<?php
/**
 * Discounts Action
 *
 * Action that handles discounts. Logic ported from AccountChange_Manager::processDiscounts.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */

use Plusnet\PromotionTools\PromotionToolsService;
use Plusnet\PromotionTools\Service\ComponentService;
use AccountChange_BillingApi_BasePriceHelper as BasePriceHelper;
use AccountChange_BillingApi_ProductOfferingPricePointIdHelper as ProductOfferingPricePointIdHelper;

require_once(__DIR__.'/../../Libraries/C2mPromotionsHelper.php');

/**
 * AccountChange_Action_Discounts class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Action_Discounts extends AccountChange_Action
{
    const CASHBACK_DISCOUNT_TYPE = 'CASH_BACK';

    const REWARD_CARD_DISCOUNT_SUB_TYPE = 'REWARD_CARD';
    const VOUCHER_DISCOUNT_SUB_TYPE = 'VOUCHER';
    const NO_DISCOUNT_SUB_TYPE = '';

    const REWARD_CARD_TICKET_CAUSE_PREFIX = 'Reward-Plusnet-';
    const VOUCHER_TICKET_CAUSE_PREFIX = 'Voucher-Amazon-';
    const DEFAULT_CASHBACK_TICKET_CAUSE_PREFIX = 'Cashback-';

    const CUSTOMER_SUPPORT_CENTRE_TICKET_POOL = 'CSC';
    const PROMOTIONAL_DISCOUNT_FAILURES = 'PROMOTIONAL_DISCOUNT_FAILURES';

    const LONG_DATE_FORMAT = 'Y-m-d H:i:s';
    const FLAT_RATE_STRATEGY = '1';

    /**
     * Handles discounts during account change.
     *
     * If ValueFamilyProducts are involved in the change always remove discounts,
     * otherwise remove discounts only if downgrading
     * For cancellation handles see <i>financial.tblCancellationReason</i> table.
     *
     * @return null
     */
    public function execute()
    {
        /**
         * Not copied over from AccountChange_Manager::processDiscounts are two things:
         *
         * 1. isScheduled check. This could easily be done using bolSchedule from the registry.
         *    However, we can simply not invoke the discounts action for a scheduled change.
         *
         * 2. Exact logic to handle an account change when the old configuration did not contain
         *    PRODUCT_TYPE_SERVICE_DEFINITION configuration. The only case I could find that this
         *    would happen is from AccountChange_TermsAndConditions which is where the product
         *    configurations originate in the wizard data and only when oldSdi == newSdi.
         *    Therefore that logic is replicated since the old product config is not available easily.
         */
        $registry = AccountChange_Registry::instance();

        $oldSdi = $registry->getEntry('intOldServiceDefinitionId');
        $newSdi = $registry->getEntry('intNewServiceDefinitionId');

        $bolScheduleChange = $registry->getEntry('bolScheduleChange');

        $newWlrServiceComponentId = $registry->getEntry('newWlrServiceComponentId');
        $oldWlrServiceComponentId = $registry->getEntry('oldWlrServiceComponentId');
        $adslToFttc = $registry->getEntry('$adslToFttc');

        $wlrChanging = (!$adslToFttc
            && !empty($newWlrServiceComponentId)
            && $newWlrServiceComponentId != $oldWlrServiceComponentId
        );

        $componentIds = [];
        if ($registry->getEntry('oldAdslComponentId') !== $registry->getEntry('newAdslComponentId')) {
            $componentIds[] = $registry->getEntry('oldAdslComponentId');
        }

        /**
         * Since SDI isn't changing this is likely a WLR only change.
         * Behaviour of old logic in this case was to simply return.
         * It was however marked as a FIXME
         */
        if ($oldSdi == $newSdi && $wlrChanging) {
            return;
        }

        $oldServiceDefinition = $this->getServiceDefinition($oldSdi);
        $newServiceDefinition = $this->getServiceDefinition($newSdi);

        // Figure out if this is a value family or BPR09 account change since we always remove
        // discounts if it's a value family product.
        $valueFamilyChange
            = $oldServiceDefinition->isValueFamilyProduct() || $newServiceDefinition->isValueFamilyProduct();

        $bpr09FamilyChange = ($valueFamilyChange == false) && (
                $oldServiceDefinition->isBpr09FamilyProduct() || $newServiceDefinition->isBpr09FamilyProduct()
            );

        $isDowngrade = (bool)(
            $registry->getEntry('intAccountChangeOperation') == AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE
        );

        $newFamily = $this->getProductFamily($newSdi);
        $autoContracts = $newFamily->hasAutoContracts();

        if ($valueFamilyChange
            || $bpr09FamilyChange
            || $newServiceDefinition->isJohnLewisFamilyProduct()
            || $autoContracts
        ) {
            $this->cancelDiscounts('accountchange', $componentIds);
        } elseif (!$valueFamilyChange && $isDowngrade) {
            $this->cancelDiscounts('downgrade', $componentIds);
        }

        // process any promo codes which have been registered for this account change
        $promoCode = $registry->getEntry('promoCode');
        $c2mPromotion = $registry->getEntry('c2mPromotion');

        if ($registry->getEntry('migrateBroadbandDiscount')) {
            $databaseAdaptor = Db_Manager::getAdaptor('AccountChange');
            $componentService = $this->getComponentService();
            $newComponents = $componentService->getServiceComponentsWithStatuses($this->intServiceId, array('active'), date('Y-m-d'));
            $currentComponents = $componentService->getServiceComponentsWithStatuses($this->intServiceId, array('destroyed'), date('Y-m-d'));

            foreach ($currentComponents as $currentComponent) {
                $currentProductComponentType = $databaseAdaptor->getProductComponentTypeByComponentId($currentComponent->getComponentId());
                foreach ($newComponents as $newComponent) {
                    if ($currentProductComponentType === $databaseAdaptor->getProductComponentTypeByComponentId($newComponent->getComponentId())) {

                        $discounts = $databaseAdaptor->getDiscountsByComponentIdAndPCI($currentComponent->getComponentId(), $currentComponent->getProductComponentInstanceId());

                        if (!empty($discounts)) {

                            $strategy = $discounts[0]['DiscountTypeId'] === self::FLAT_RATE_STRATEGY ? new \Retention_FlatRateDiscountStrategy() : new \Retention_PercentageDiscountStrategy();

                            $this->addDiscount(
                                $this->intServiceId,
                                $newComponent->getComponentId(),
                                $newComponent->getProductComponentInstanceId(),
                                $this->calculateMigratedDiscountValue($discounts[0]['Value'],
                                    $newSdi,
                                    $newComponent->getComponentTypeId(),
                                    $newServiceDefinition->isBusiness()),
                                $discounts[0]['StrPromotionCode'],
                                $discounts[0]['Description'],
                                $strategy,
                                $discounts);
                        }
                    }
                }
            }
        } else if (!empty($promoCode)) {
            if ($c2mPromotion) {
                try {
                    $promotionToolsService = $this->getPromotionToolService();

                    $agreementDate = DateTime::createFromFormat(
                        'Y-m-d',
                        $registry->getEntry('agreementDate')
                    )->format('Ymd');

                    $discountSummary = $promotionToolsService->addPromotionToAccountSpecifyingPermittedStatusCodes(
                        $this->intServiceId,
                        $promoCode,
                        array('queued-activate', 'active', 'unconfigured'),
                        $registry->getEntry('contractLengthMonths'),
                        $agreementDate
                    );

                    $message = "This customer is on the $promoCode promotion: \n"
                        . $discountSummary->getTotalValuePerMonthIncVat()
                        . " total discount for " . $registry->getEntry('contractLengthMonths') . " months";
                    $this->addCustomerNoteToAccountForPromotion($this->intServiceId, $promoCode, $message);
                    $this->raiseTicketOnAccountIfCashbackDiscountPresent($promoCode);

                } catch (Exception $e) {
                    $error_message = "Unable to add C2M promotion with code [" . $promoCode . "] to service id ["
                        . $this->intServiceId . "]: " . $e->getMessage();
                    error_log($error_message);
                    $this->raiseAutoProblem($e);
                    $this->raiseTicket($error_message, self::PROMOTIONAL_DISCOUNT_FAILURES);
                }
            } else {
                // call to legacy codebase function which handles promo code processing
                applyPromotionCodeLegacyDatabaseImpl(
                    $this->intServiceId,
                    $newSdi,
                    $promoCode,
                    null,
                    $bolScheduleChange
                );
            }
        }

        return;
    }

    public function addCustomerNoteToAccountForPromotion($intServiceId, $promoCode, $message)
    {
        addCustomerNoteToAccountForPromotion($intServiceId, $promoCode, $message);
    }

    /**
     * Cancels all discounts on a service with the given reason
     *
     * @param string $reason Reason for cancellation.
     * @param array $componentIds component Ids
     *
     * @return null
     */
    protected function cancelDiscounts($reason, array $componentIds = [])
    {
        $serviceId = $this->intServiceId;
        $myId = AccountChange_Manager::getMyId();

        $discountManager = $this->getDiscountManager();
        $discounts = $discountManager->getServiceDiscounts($serviceId, 'pending', false, $componentIds);

        if (count($discounts) > 0) {
            $discountManager->saveDiscountArray(
                $discounts->cancelAll(
                    $reason,
                    $myId,
                    'Cancelled from: ' . __FILE__
                )
            );
        }
    }

    /**
     * Gets service definition object for given SDI.
     *
     * @param integer $sdi Service definition ID.
     *
     * @return Core_ServiceDefinition
     */
    protected function getServiceDefinition($sdi)
    {
        return new Core_ServiceDefinition($sdi);
    }

    /**
     * Gets an instance of discount manager.
     *
     * @return Retention_DiscountManager
     */
    protected function getDiscountManager()
    {
        return Retention_DiscountManager::getInstance();
    }

    /**
     * Returns the product family
     *
     * @param int $serviceDefinitionId Service definition id
     *
     * @return ProductFamily_ProductFamily
     */
    protected function getProductFamily($serviceDefinitionId)
    {
        return ProductFamily_Factory::getFamily($serviceDefinitionId);
    }

    /**
     * @return PromotionToolsService
     */
    protected function getPromotionToolService()
    {
        return new PromotionToolsService();
    }

    /**
     * If the customer has redeemed a C2M promotion with a cashback discount included, raise a ticket on their WP account
     * so that it can be processed manually.
     *
     * @param $promotionCode string C2M promotion code of the promotion the customer has redeemed
     *
     * @return void
     */
    private function raiseTicketOnAccountIfCashbackDiscountPresent($promotionCode)
    {
        $promotion = $this->getActivePromotionWithPromocode($promotionCode);
        if ($promotion) {

            foreach ($promotion->getDiscounts() as $discount) {

                if ($discount->getType() === self::CASHBACK_DISCOUNT_TYPE) {

                    $cashbackSubType = $discount->getSubType();
                    $cashbackValue = $discount->getDiscountValues()[0]->getValue();

                    $this->raiseCashbackTicket($cashbackSubType, $cashbackValue);
                    break;
                }
            }
        }
    }

    /**
     * Raise a ticket on the user's account, the message in the ticket depends on the type of cashback and the amount.
     * If we cannot find a matching ticket cause, raise an error in the logs.
     *
     * @param $cashbackSubType string Sub type of the cashback discount, can be empty
     * @param $cashbackValue   string Value of the cashback discount
     *
     * @return void
     */
    private function raiseCashbackTicket($cashbackSubType, $cashbackValue)
    {
        switch ($cashbackSubType) {
            case self::REWARD_CARD_DISCOUNT_SUB_TYPE:
                $causeArray = $this->getTicketCommentForDisplayName(self::REWARD_CARD_TICKET_CAUSE_PREFIX . $cashbackValue);
                break;
            case self::VOUCHER_DISCOUNT_SUB_TYPE:
                $causeArray = $this->getTicketCommentForDisplayName(self::VOUCHER_TICKET_CAUSE_PREFIX . $cashbackValue);
                break;
            case self::NO_DISCOUNT_SUB_TYPE:
            case null:
                $causeArray = $this->getTicketCommentForDisplayName(self::DEFAULT_CASHBACK_TICKET_CAUSE_PREFIX . $cashbackValue);
                break;
            default:
                $causeArray = '';
                break;
        }

        if (empty($causeArray['txtTicketComment'])) {
            error_log(__CLASS__ . '->' . __METHOD__ . ': ' .
                'There is no ticket cause for cashback of type [' . $cashbackSubType . '] and value [' . $cashbackValue . ']. Unable to raise ticket.');
        } else {
            $this->raiseTicket($causeArray['txtTicketComment'], self::CUSTOMER_SUPPORT_CENTRE_TICKET_POOL, true, $causeArray['intCauseID']);
        }
    }

    /**
     * Wrapper function for unit testing
     *
     * @param $promotionCode string The C2M promotion code
     *
     * @return \Plusnet\C2mApiClient\Entity\Promotion
     */
    protected function getActivePromotionWithPromocode($promotionCode)
    {
        return AccountChange_C2mPromotionsHelper::getActivePromotionWithPromocode($promotionCode);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param $displayName string Display name of the ticket comment we want to retrieve
     *
     * @return array The ticket comment to use in the cashback ticket and the ticket cause ID
     * @throws Db_TransactionException
     */
    protected function getTicketCommentForDisplayName($displayName)
    {
        $databaseAdaptor = Db_Manager::getAdaptor('AccountChange');
        return $databaseAdaptor->getTicketCommentByDisplayName($displayName);
    }

    /**
     * Get a discountDAO object
     *
     * @return \Retention_DiscountDao
     */
    protected function getDiscountDao()
    {
        return new \Retention_DiscountDao();
    }

    /**
     * Get a component service object
     *
     * @return ComponentService
     */
    protected function getComponentService()
    {
        return new ComponentService();
    }

    /**
     * @param $currentDiscountValue
     * @param $serviceDefinitionId
     * @param $serviceComponentId
     * @param $isBusiness
     * @return int The lowest value out of the current discount and broadband price
     */
    private function calculateMigratedDiscountValue($currentDiscountValue, $serviceDefinitionId, $serviceComponentId, $isBusiness)
    {
        $databaseAdaptor = Db_Manager::getAdaptor('AccountChange');
        $tariffId = $databaseAdaptor->getTariffIdUsingServiceComponent($serviceComponentId);

        $productOfferingPricePointPair = BasePriceHelper::createProductOfferingPricePointPairFromIds(
            $serviceDefinitionId,
            $serviceComponentId,
            $tariffId);

        $basePrices =
            BasePriceHelper::getBasePrices($this->intServiceId, array($productOfferingPricePointPair));
        $productOfferingPricePointId =
            ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

        $broadbandPrice = $basePrices[$productOfferingPricePointId]['currentBasePriceInContract'];

        if ($isBusiness) {
            // Billing returns exVAT and the discounts are incVAT
            $broadbandPrice *= 1.2;
        }

        return min($currentDiscountValue, $broadbandPrice);
    }

    /**
     * Add new discount
     *
     * @param int $serviceId Service id
     * @param int $componentId Component id
     * @param int $productComponentId Product component id
     * @param float $value Discount value (inc VAT)
     * @param string $promoCode Discount promo code
     * @param string $description Discount description
     * @param Retention_AbstractDiscountStrategy $discountStrategy Discount type
     * @param array $oldDiscounts Old discounts redemption
     *
     * @return void
     * @throws Retention_Exception
     */
    private function addDiscount(
        $serviceId,
        $componentId,
        $productComponentId,
        $value,
        $promoCode,
        $description,
        $discountStrategy,
        $oldDiscounts
    )
    {
        // ADD FIRST REDEMPTION TO BE SCHEDULED TO NEXT APPLICABLE DAY
        $immediateDiscount = new \Retention_Discount($componentId, null, $discountStrategy);
        $immediateDiscount->setProductComponentInstanceID($productComponentId);
        $immediateDiscount->calculateValue($value, $serviceId);
        $immediateDiscount->setDueDate(date('Y-m-d'));
        $immediateDiscount->setCreatedBy(SCRIPT_USER);
        $immediateDiscount->setStrPromotionCode($promoCode);
        $immediateDiscount->setDescription($description);

        $discounts = new \Retention_DiscountArray();
        $discounts[] = $immediateDiscount;

        // ADD REMAINING REDEMPTIONS
        foreach ($oldDiscounts as $discount) {
            if ($discount['DueDate'] > date('Y-m-d')) {

                $discountToAdd = new \Retention_Discount($componentId, null, $discountStrategy);
                $discountToAdd->setProductComponentInstanceID($productComponentId);
                $discountToAdd->calculateValue($value, $serviceId);
                $discountToAdd->setDueDate($discount['DueDate']);
                $discountToAdd->setCreatedBy(SCRIPT_USER);
                $discountToAdd->setStrPromotionCode($promoCode);
                $discountToAdd->setDescription($description);

                $discounts[] = $discountToAdd;
            }
        }

        $discountDao = $this->getDiscountDao();
        $discountDao->insertDiscountArray($discounts);
    }
}