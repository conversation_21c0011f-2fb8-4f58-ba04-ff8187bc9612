<?php

class AccountChange_EmailHandler_Factory
{
    /**
     * Email template names
     *
     * @var string
     */
    const UPGRADE_EMAIL_HANDLE    = 'upgrade_confirmation';
    const RECONTRACT_EMAIL_HANDLE = 'recontract_confirmation';
    const RECONTRACT_EMAIL_HANDLE_BUSINESS = 'recontract_confirmation_business';
    const ACCOUNT_CHANGE_JOURNEYS = [
        AccountChange_AccountChangeOrder::TYPE_WORKPLACE_ACCOUNT_CHANGE
    ];

    /**
     * Key used to get the discount amount from AccountChange_EmailHandler_PriceHelper::getNewPackagePrices()
     *
     * @var string
     */
    const DISCOUNT_AMOUNT_KEY = 'discountAmount';

    /**
     * Keys used multiple times to retrieve and add values from/to arrays
     *
     * @var string
     */
    const OLD_WLR_COMPONENT_ID_KEY = 'intOldWlrId';

    /**
     * Broadband product names as they are stored in the database
     *
     * @var string
     */
    const BROADBAND_PRODUCT_FTTP = 'FTTP';
    const BROADBAND_PRODUCT_FTTC = 'FTTC';
    const BROADBAND_PRODUCT_SOGEA = 'SOGEA';

    /**
     * @var AccountChange_PerformChangeApi
     */
    private $performChangeApi;

    /**
     * @var AccountChange_AccountChangeOrder
     */
    private $order;

    /**
     * @var AccountChange_ConfirmationEmailHandler
     */
    private $confirmationEmail;

    /**
     * @var AccountChange_Account
     */
    private $account;

    /**
     * See AccountChange_Account->getWlrInformation()
     *
     * @var array
     */
    private $currentWlrData;

    /**
     * See AccountChange_Account->getBroadbandDetails()
     *
     * @var array
     */
    private $currentBroadbandDetails;

    /**
     * @var Core_ServiceDefinition
     */
    private $currentCoreServiceDefinition;

    /**
     * AccountChange_EmailHandler_Factory constructor.
     *
     * @param AccountChange_PerformChangeApi   $performChangeApi
     * @param AccountChange_AccountChangeOrder $order
     */
    public function __construct(
        \AccountChange_PerformChangeApi   $performChangeApi,
        \AccountChange_AccountChangeOrder $order
    ) {
        $this->performChangeApi  = $performChangeApi;
        $this->order             = $order;
        $this->confirmationEmail = new AccountChange_ConfirmationEmailHandler();
    }

    /**
     * @return AccountChange_EmailHandler_Confirmation
     */
    protected function getAccountChangeConfirmation()
    {
        return new AccountChange_EmailHandler_Confirmation(
            $this->performChangeApi,
            $this->order,
            $this->getPriceHelper(),
            $this->getAccountInstance(),
            $this->getSpeedHelper(),
            new AccountChange_EmailHandler_DataHelper()
        );
    }

    /**
     * @return AccountChange_EmailHandler_Completion
     */
    protected function getAccountChangeCompletion()
    {
        return new AccountChange_EmailHandler_Completion(
            $this->performChangeApi,
            $this->order,
            $this->getSpeedHelper(),
            $this->getPriceHelper(),
            new AccountChange_CompleteContractHelper(
                $this->performChangeApi->getServiceId(),
                $this->getProductFamily()
            )
        );
    }

    /**
     * @return AccountChange_EmailHandler_PhoneOnlyChange
     */
    protected function getPhoneOnlyChangeCompletion()
    {
        return new AccountChange_EmailHandler_PhoneOnlyChange(
            $this->performChangeApi,
            $this->order,
            $this->getAccountInstance(),
            $this->getPriceHelper(),
            new AccountChange_EmailHandler_DataHelper()
        );
    }

    /**
     * Get product family for the given service definition id
     *
     * @return ProductFamily_Generic
     */
    protected function getProductFamily()
    {
        return ProductFamily_Factory::getFamily($this->performChangeApi->getToSdi());
    }

    /**
     * @return AccountChange_EmailHandler
     */
    public function buildConfirmationEmailForPerformChangeApi()
    {
        $bolInstant = $this->isInstantChange();
        $isPhoneOnlyChange = $this->isPhoneOnlyChange();
        $isBroadbandOnlyChange = $this->isBroadbandOnlyChange();

        if ($this->isAccountChangeJourney()) {
            if ($bolInstant) {
                if ($isPhoneOnlyChange && !$isBroadbandOnlyChange) {

                    return [$this->getPhoneOnlyChangeCompletion()];

                } else if (!$isPhoneOnlyChange && $isBroadbandOnlyChange) {
                    return [$this->getAccountChangeCompletion()];

                } else if($isPhoneOnlyChange && $isBroadbandOnlyChange) {

                    return [$this->getAccountChangeCompletion(), $this->getPhoneOnlyChangeCompletion()];
                }
            } else {

                return [$this->getAccountChangeConfirmation()];
            }
        }

        $technologyType = $this->performChangeApi->getTechnologyType();

        $this->getCurrentAccountInformation($technologyType);
        $this->setEmailName();
        $this->setDirectDebitDetails();
        $this->setChangeDate();
        $this->setCurrentDualPlay();
        $this->setPrices();
        $this->setBroadbandProductNames();

        switch ($technologyType) {
            case self::BROADBAND_PRODUCT_FTTP:
                $this->setFttpDetails();
                break;
            case self::BROADBAND_PRODUCT_SOGEA:
                $this->setSogeaDetails();
                break;
            case self::BROADBAND_PRODUCT_FTTC:
                $this->confirmationEmail->setFttcProduct(true);
                // no break
            default:
                $this->setLineRentalFrequency();
                $this->setNewCallPlan();
                $this->setCallFeatureNames();
                break;
        }

        $this->setSpeedData();
        $this->setContractMessage();
        $this->setCallerClassName();

        return [$this->confirmationEmail];
    }

    /**
     * @return bool
     */
    private function isInstantChange()
    {
        return !$this->order->getIsScheduledChange();
    }

    /**
     * @param string $technologyType get current wlr details
     * @return void
     */
    private function getCurrentAccountInformation($technologyType)
    {
        $this->account = $this->getAccountInstance();
        $this->currentCoreServiceDefinition = $this->getCoreServiceDefinition(
            $this->performChangeApi->getCurrentSdi()
        );

        if (!($technologyType == 'FTTP' || $technologyType == 'SOGEA')) {
            $this->currentWlrData = $this->account->getWlrInformation();
        }
    }

    /**
     * @return bool
     */
    protected function isRecontracting()
    {
        return !empty($this->order->getContract()) && $this->isStayingOnSameSdi();
    }

    /**
     * If the customer is recontracting, use the recontracting email template. Otherwise, use the upgrade template.
     */
    private function setEmailName()
    {
        if ($this->isRecontracting()) {
            $emailName = $this->getRecontractHandle();
        } else {
            $emailName = self::UPGRADE_EMAIL_HANDLE;
        }

        $this->confirmationEmail->setEmailName($emailName);
    }

    /**
     * @return bool
     */
    protected function isPhoneOnlyChange()
    {
        return $this->performChangeApi->getNewWlrServiceComponentId()
            && $this->performChangeApi->getOldWlrServiceComponentId()
            !== $this->performChangeApi->getNewWlrServiceComponentId();
    }

    /**
     * @return bool
     */
    protected function isBroadbandOnlyChange()
    {
        return $this->performChangeApi->getCurrentSdi() !== null
            && $this->performChangeApi->getCurrentSdi() !== $this->performChangeApi->getToSdi();
    }

    /**
     * Are they staying on the same service definition?
     *
     * @return bool
     */
    private function isStayingOnSameSdi()
    {
        return $this->performChangeApi->getOldSdi() === $this->performChangeApi->getToSdi();
    }

    /**
     * Do they need a new direct debit instruction?
     * Will their new direct debit be ready before their next bill?
     */
    private function setDirectDebitDetails()
    {
        $newDirectDebitRequired = $this->doesNotHaveActiveDirectDebitInstruction($this->performChangeApi->getServiceId());
        $this->confirmationEmail->setNewDirectDebit($newDirectDebitRequired);

        $nextInvoiceDate = $this->account->getNextInvoiceDate('Y-m-d');
        $isNewDDReady    = $this->isNewDDReady($nextInvoiceDate);
        $this->confirmationEmail->setNewDirectDebitReady($isNewDDReady);
    }

    /**
     * When will their change be scheduled for?
     * @return void
     */
    private function setChangeDate()
    {
        $this->confirmationEmail->setChangeDate($this->performChangeApi->getScheduledChangeDate());
    }

    /**
     * Are they currently on a dual play product?
     */
    private function setCurrentDualPlay()
    {
        $currentDualPlay = !empty($this->currentWlrData[self::OLD_WLR_COMPONENT_ID_KEY]);
        $this->confirmationEmail->setCurrentDualPlay($currentDualPlay);
    }

    /**
     * Get all new package prices including discounts and add them to the confirmation email data.
     */
    private function setPrices()
    {
        $priceHelper = $this->getPriceHelper();
        $newPackagePrices = $priceHelper->getNewPackagePrices();
        $this->confirmationEmail->setBroadbandSubscriptionCost($this->formatPrice($newPackagePrices['broadband']));
        $this->confirmationEmail->setLineRentalSubscriptionCost($this->formatPrice($newPackagePrices['lineRental']));
        $this->confirmationEmail->setCallPlanSubscriptionCost($this->formatPrice($newPackagePrices['callPlan']));
        $this->confirmationEmail->setCallFeatureSubscriptionCost($this->formatPrice($newPackagePrices['callFeatures']));
        $this->confirmationEmail->setNewPrice($this->formatPrice($newPackagePrices['total']));
        $this->confirmationEmail->setNewLineRental($this->formatPrice($newPackagePrices['lineRental']));

        if (!empty($newPackagePrices['discountDuration']) && !empty($newPackagePrices[self::DISCOUNT_AMOUNT_KEY])) {
            $this->confirmationEmail
                ->setSpecialOffer(true)
                ->setOfferDuration($newPackagePrices['discountDuration'])
                ->setDiscountAmount($this->formatPrice($newPackagePrices[self::DISCOUNT_AMOUNT_KEY]))
                ->setOfferPrice($this->formatPrice($newPackagePrices['total'] - $newPackagePrices[self::DISCOUNT_AMOUNT_KEY]));
        }
    }

    /**
     * What are the display names of their current and new broadband products?
     */
    private function setBroadbandProductNames()
    {
        $this->confirmationEmail->setCurrentBroadbandProduct($this->performChangeApi->getOldProductName());
        $this->confirmationEmail->setNewBroadbandProduct($this->performChangeApi->getNewProductName());
    }

    /**
     * Are they on monthly or annual line rental?
     */
    protected function setLineRentalFrequency()
    {
        if ($this->hasActiveLineRentalSaver()) {
            $lineRentalFrequency = 'Annual';
        } else {
            $lineRentalFrequency = 'Monthly';
        }

        $this->confirmationEmail->setLineRentalFrequency($lineRentalFrequency);
    }

    /**
     * What is the name of their new call plan?
     */
    private function setNewCallPlan()
    {
        $newCallPlanName = null;

        $phoneComponentId = $this->getCurrentPhoneComponentId();
        if (!empty($phoneComponentId)) {
            $callPlan = $this->getCallPlanDetailsByWlrServiceComponentId($phoneComponentId);
            $newCallPlanName = $callPlan['strDisplayName'];
        }

        $this->confirmationEmail->setNewCallPlan($newCallPlanName);
    }

    /**
     * What are the names of their current active call features?
     */
    private function setCallFeatureNames()
    {
        $activeCallFeatures = $this->currentWlrData['activeCallFeatures'];
        if (!empty($activeCallFeatures)) {
            $this->confirmationEmail->setCallFeatures(implode(', ', $activeCallFeatures));
        }
    }

    /**
     * Get speed data from the helper class and add it to the confirmation email data.
     */
    private function setSpeedData()
    {
        $speedHelper = $this->getSpeedHelper();
        $speedData = $speedHelper->getSpeedData();
        $this->confirmationEmail
            ->setMinimumEstimatedDownloadSpeedMbs($speedData['minimumEstimatedDownloadSpeedMbs'])
            ->setMaximumEstimatedDownloadSpeedMbs($speedData['maximumEstimatedDownloadSpeedMbs'])
            ->setMinimumEstimatedUploadSpeedMbs($speedData['minimumEstimatedUploadSpeedMbs'])
            ->setMaximumEstimatedUploadSpeedMbs($speedData['maximumEstimatedUploadSpeedMbs'])
            ->setBroadbandSpeedRange($speedData['broadbandSpeedRange'])
            ->setGuaranteedSpeedValue($speedData['guaranteedSpeedValue'])
            ->setMaximumDownloadSpeedMbs($speedData['maximumDownloadSpeedMbs'])
            ->setAdvertisedDownloadSpeedMbs($speedData['advertisedDownloadSpeedMbs'])
            ->setAdvertisedUploadSpeedMbs($speedData['advertisedUploadSpeedMbs']);
    }

    /**
     * Unfortunately:
     *
     * - The "contractDuration" variable is misleadingly used in the email templates to hold a full message
     * - The "recontracted" variable is used as a flag to decide whether to show the above message or not
     */
    private function setContractMessage()
    {
        $contractMessageHelper = $this->getContractMessageHelper();
        $message = $contractMessageHelper->generateContractMessage();

        $this->confirmationEmail->setRecontracted(!empty($message));
        $this->confirmationEmail->setContractDuration($message);
    }

    /**
     * There is logic inside the email templates that displays certain messaging depending on where the email is being
     * generated from. We need to replicate the same behaviour as the campaign pages here.
     */
    private function setCallerClassName()
    {
        $this->confirmationEmail->setCallerClassName('AccountChangeApi');
    }

    /**
     * Return the new phone component id if present, otherwise return the existing one.
     * If neither are present, customer is on solus product so return null.
     *
     * @return integer|null
     */
    private function getCurrentPhoneComponentId()
    {
        $newPhoneComponentId = $this->order->getProducts()->getPhoneComponentId();

        if (isset($this->currentWlrData[self::OLD_WLR_COMPONENT_ID_KEY])) {
            $oldPhoneComponentId = $this->currentWlrData[self::OLD_WLR_COMPONENT_ID_KEY];
        } else {
            $oldPhoneComponentId = null;
        }

        return !empty($newPhoneComponentId) ? $newPhoneComponentId : $oldPhoneComponentId;
    }

    private function formatPrice($price)
    {
        return number_format($price, 2);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param $serviceId string The customer's service id
     *
     * @return bool
     */
    protected function doesNotHaveActiveDirectDebitInstruction($serviceId)
    {
        $paymentHelper = $this->getPaymentHelper();
        $activeDirectDebitDetails = $paymentHelper->getActiveDirectDebitInstructionDetails($serviceId, true);

        return empty($activeDirectDebitDetails);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param $nextInvoiceDate string The customer's next invoice date
     *
     * @return bool
     */
    protected function isNewDDReady($nextInvoiceDate)
    {
        $sevenWorkingDaysFromNow = \I18n_Date::addWorkingDays(time(), 7);
        $nextInvoiceTimestamp = strtotime($nextInvoiceDate);

        return $nextInvoiceTimestamp > $sevenWorkingDaysFromNow;
    }

    /**
     * Wrapper function for unit testing
     *
     * @return AccountChange_Account
     */
    protected function getAccountInstance()
    {
        return AccountChange_Account::instance(new Int($this->performChangeApi->getServiceId()));
    }

    /**
     * Wrapper function for unit testing
     *
     * @param $serviceComponentId integer Service component id if the call plan
     *
     * @return array
     */
    protected function getCallPlanDetailsByWlrServiceComponentId($serviceComponentId)
    {
        return CWlrProduct::getCallPlanDetailsFromServiceComponentId($serviceComponentId);
    }

    /**
     * Wrapper function for unit testing
     *
     * @return GenericImmediatePaymentApplication_DirectDebitPaymentHelper
     */
    protected function getPaymentHelper()
    {
        return new \GenericImmediatePaymentApplication_DirectDebitPaymentHelper();
    }

    /**
     * Wrapper function for unit testing
     *
     * @return AccountChange_EmailHandler_PriceHelper
     */
    protected function getPriceHelper()
    {
        return new AccountChange_EmailHandler_PriceHelper(
            $this->performChangeApi->getServiceId(),
            $this->performChangeApi->getMarketId(),
            $this->performChangeApi->getToSdi(),
            $this->getCurrentPhoneComponentId(),
            $this->performChangeApi->getPromoCode(),
            $this->currentWlrData['intInstanceId']
        );
    }

    /**
     * Wrapper function for unit testing
     *
     * @return AccountChange_EmailHandler_SpeedHelper
     */
    protected function getSpeedHelper()
    {
        return new AccountChange_EmailHandler_SpeedHelper(
            $this->performChangeApi->getLineCheckResults()->getLineCheckId(),
            $this->performChangeApi->getToSdi()
        );
    }

    /**
     * Wrapper function for unit testing
     *
     * @return bool
     */
    protected function hasActiveLineRentalSaver()
    {
        return AccountChange_Manager::hasActiveLineRentalSaver($this->performChangeApi->getServiceId());
    }

    /**
     * Wrapper function for unit testing
     *
     * @return string
     */
    protected function getContractMessageHelper()
    {
        return new AccountChange_EmailHandler_ContractMessageHelper(
            $this->performChangeApi->getServiceId(),
            $this->order->getContract()
        );
    }

    /**
     * @return string
     */
    private function getRecontractHandle()
    {
        return $this->currentCoreServiceDefinition->isBusiness() ?
            self::RECONTRACT_EMAIL_HANDLE_BUSINESS : self::RECONTRACT_EMAIL_HANDLE;
    }

    /**
     * @param int $serviceDefinitionId service definition id
     * @return Core_ServiceDefinition
     */
    protected function getCoreServiceDefinition($serviceDefinitionId)
    {
        return new Core_ServiceDefinition($serviceDefinitionId);
    }

    /**
     * @return bool
     */
    private function isAccountChangeJourney()
    {
        return in_array($this->order->getType(), static::ACCOUNT_CHANGE_JOURNEYS) && !$this->isRecontracting();
    }

    /**
     * @return void
     * @throws Db_TransactionException
     */
    private function setFttpDetails()
    {
        $this->confirmationEmail->setFttpProduct(true);
        $this->confirmationEmail->setFttpInstallationType(
            $this->performChangeApi->getLineCheckResults()->getFttpInstallProcess()
        );
        $this->setAccessTechnologyChanging($this->performChangeApi);
    }

    /**
     * @return void
     * @throws Db_TransactionException
     */
    private function setSogeaDetails()
    {
        $this->confirmationEmail->setSogeaProduct(true);
        $this->setAccessTechnologyChanging($this->performChangeApi);
    }

    /**
     * @param AccountChange_PerformChangeApi $performChangeApi perform change api
     * @return void
     */
    private function setAccessTechnologyChanging(AccountChange_PerformChangeApi $performChangeApi)
    {
        $accessTechnologyChanging = false;
        $oldAccessTechnology = $performChangeApi->getCurrentAccessTechnology();
        $newAccessTechnology = $performChangeApi->getNewAccessTechnology();
        if (!empty($newAccessTechnology)) {
            $accessTechnologyChanging =
                $oldAccessTechnology !== $newAccessTechnology;
        }

        $this->confirmationEmail->setAccessTechnologyChanging($accessTechnologyChanging);
    }
}
