server: coredb
role: master
rows: multiple
statement:

SELECT
    DISTINCT c.service_id
FROM userdata.components c
INNER JOIN userdata.tblProductComponentInstance pci
    ON c.component_id = pci.intComponentID
INNER JOIN userdata.tblProductComponentContract pcc
    ON pci.intProductComponentInstanceID = pcc.intProductComponentInstanceID
    AND pcc.dtmCancelled IS NULL
    AND pcc.dtmRenewed IS NULL
    AND pcc.intNextProductComponentContractID IS NULL
INNER JOIN userdata.tblPendingPrepaidContract ppc
    ON pci.intProductComponentInstanceID=ppc.intProductComponentInstanceID
INNER JOIN dbProductComponents.tblProductComponent pc
    ON pci.intProductComponentID = pc.intProductComponentId
WHERE
    c.status not in ('destroyed', 'queued-destroy')
AND
    pc.vchHandle = 'LINE_RENTAL_SAVER'
AND
    ppc.dteRedeemed IS NULL
AND
    ppc.dteCancelled IS NULL
AND
    ppc.intScheduledPaymentID != 0
AND
    pcc.dteContractEnd <= now();
