To install an application (once you have phing installed) go into this directory
and type 'install'.

This directory contains all this information needed to setup the application
with the exception of:

- Cron Job configurations, these are stored in the ServerRoles directory

- Which sites and application runs on, this is configured in the sites.

The directory should contain:

./ModuleDependancies.txt     -  A file detailing the other modules this module
                                depends upon. The dependacies are enforced by the
                                autoloader.
./AppDisplayName.txt          - A file containing the human readable name of the
                                application. It is used on alpha by the
                                auto-permissions scripts.
./<Namespace>.permissions.txt - A file containing the permissions used within
                                the <Namespace> namespace. It is generated
                                on alpha by the  by the auto-permissions
                                scripts.
./build.xml       - the phing build file
./phing/pn/tasks  - the custom phing tasks

If the module contains multiple namespaces the file ./Namespaces.txt is placed
here to indicate that fact to the autoloader files generator.

It may contain database setup files for the coredb:
./<database-name>.structure.sql           - MySQL dump to setup the initial structure 
./<database-name>.populate.<number>.sql   - SQL statements to set up the initial data, 
                                            applied in numerical order.
./<database-name>.repopulate.<number>.sql - SQL statements adding data to an existing 
                                            database. 

Other install scripts may be installed.
