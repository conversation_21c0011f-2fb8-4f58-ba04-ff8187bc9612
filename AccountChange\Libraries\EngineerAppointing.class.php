<?php
/**
 * AccountChange_EngineerAppointing class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2011 PlusNet
 */
/**
 * AccountChange_EngineerAppointing helper class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2011 PlusNet
 */
class AccountChange_EngineerAppointing
{
    /**
     * Valid timeslots which BT may give us
     *
     * @var array
     */
    public static $validTimeslots = array(
        'AM', 'PM', '0900', '1000', '1100', '1200', '1300', '1400', '1500', '1600', '1700', '1800', '1900', '2000'
    );

    /**
     * Call to the appointing API to get some live appointments
     *
     * @param array $vars variables to send
     *
     * @return array list of appointments
     */
    public function getLiveAppointments(array $vars)
    {
        $data = array(
            'addressRef'      => $vars['addressRef'],
            'addressCategory' => $vars['addressCategory'],
            'cssDatabaseCode' => $vars['databaseCode'],
            'journeyType'     => $vars['journeyType'],
            'noSparePairs'    => $vars['sparePairs'],
            'cli'             => $vars['cli'],
            'extensionKitId'  => $vars['extensionKitId'],
            'locationType'    => $vars['locationType']
        );

        $client = $this->getEngineerAppointingClient($vars['service'], $data);
        $appointments = $client->getAppointments();

        if (!empty($appointments[$vars['serviceHandle']])) {
            $maxDate = 0;
            foreach (array_keys($appointments[$vars['serviceHandle']]) as $strDate) {
                if (preg_match('/^(\d{2})\/(\d{2})\/(\d{2,4})/', $strDate, $matches)) {
                    $uxtDate = mktime(0, 0, 0, $matches[2], $matches[1], $matches[3]);
                    $maxDate = max($maxDate, $uxtDate);
                }
            }
        }

        return array(
            'appointments' => $appointments[$vars['serviceHandle']],
            'offsetDate'   => $maxDate ? date('d/m/Y', $maxDate) : null
        );
    }

    /**
     * As per http://jira.internal.plus.net/browse/RES-931 we should only be
     * seeing AM/PM rather than time based slots. So to achieve this we need to decorate
     * the dates a little more if they are in the format of 1300,1500 etc
     *
     * @param array $appointments The appointments we need to decorate
     *
     * @return array
     */
    public function decorateAppointments(array $appointments)
    {
        $appointmentHelper = new EngineerAppointmentClient_Helper();
        $decoratedAppointments = $appointmentHelper->decorateAppointments(
            $appointments
        );

        return $decoratedAppointments;
    }

    /**
     * Create an engineer appointing client instance using
     * the data provided
     *
     * @param int   $service The ID of the service type (e.g. PSTN, FTTC, etc)
     * @param array $data    Array of data describing the service
     *
     * @return EngineerAppointmentClient_Client
     */
    protected function getEngineerAppointingClient($service, $data)
    {
        $container = new EngineerAppointmentClient_Service_Container();
        $container->add($service, $data);

        $client = new EngineerAppointmentClient_Client(
            $container
        );

        return $client;
    }

    /**
     * Write to the audit log
     *
     * @param string $message the message
     *
     * @return void
     */
    protected function auditLog($message)
    {
        Log_AuditLog::write($message, __CLASS__);
    }
}
