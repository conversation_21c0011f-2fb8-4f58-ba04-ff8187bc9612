<?php
/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_MGALSHelper
{
    const MAKE_LIVE_LOG_MESSAGE_SUCCESS = 'Made scheduled MGALS live: OK';
    const MAKE_LIVE_LOG_MESSAGE_FAIL = 'Made scheduled MGALS live: Failed (%s)';

    /**
     * @var ProductComponent_MGALS
     */
    private $objMGALS;

    /**
     * Default message which will be overwritten
     *
     * @var string
     */
    private $makeLiveResultMessage = "Not attempted";

    /**
     * @param ProductComponent_MGALS $objMGALS mgals handler
     */
    public function __construct(ProductComponent_MGALS $objMGALS)
    {
        $this->objMGALS = $objMGALS;
    }

    /**
     * @param int $lineCheckId             line check id
     * @param int $newServiceDefinition    new service def
     * @param int $serviceChangeScheduleId change schedule id
     * @return void
     */
    public function saveMGALSFromLineCheckData($lineCheckId, $newServiceDefinition, $serviceChangeScheduleId)
    {
        $speedData = $this->getDataFromLineCheckHelper($lineCheckId, $newServiceDefinition);
        $mgsFigure = $speedData[$newServiceDefinition]['mgs'] / 1000;

        $this->saveScheduledMGS(
            $serviceChangeScheduleId,
            $mgsFigure
        );
    }

    /**
     * @param int $serviceId customer service id
     * @return bool
     */
    public function makeScheduledMGALSLiveForServiceId($serviceId)
    {
        $success = $this->objMGALS->makeScheduledMGALSLive($serviceId);
        if ($success) {
            $this->makeLiveResultMessage = static::MAKE_LIVE_LOG_MESSAGE_SUCCESS;
        } else {
            $this->makeLiveResultMessage = sprintf(
                static::MAKE_LIVE_LOG_MESSAGE_FAIL,
                $serviceId
            );
        }

        return $success;
    }

    /**
     * @return string
     */
    public function getMakeLiveResultMessage()
    {
        return $this->makeLiveResultMessage;
    }

    /**
     * @param int   $serviceChangeScheduleId change schedule id
     * @param float $mgsFigure               mgs figure to record
     * @return void
     */
    private function saveScheduledMGS($serviceChangeScheduleId, $mgsFigure)
    {
        $this->objMGALS->saveScheduledMinimumLineSpeedValue(
            $serviceChangeScheduleId,
            $mgsFigure
        );
    }

    /**
     * @param int $lineCheckId          line check id
     * @param int $newServiceDefinition new service def
     * @return array
     */
    protected function getDataFromLineCheckHelper($lineCheckId, $newServiceDefinition)
    {
        return LineCheck_Helper::calculateMGSAndUpload(
            [$newServiceDefinition],
            $lineCheckId
        );
    }
}