<?php

/**
 * <AUTHOR>
 */

class AccountChange_AccountStatusPolicy extends AccountChange_AbstractValidationPolicy
{
    private $failureReason;

    const ERROR_MESSAGE = 'You cannot make changes to this account as status is %s';
    const EXCEPTION_MESSAGE = 'You cannot make changes to this account as the current status could not be retrieved';
    const ERROR_CODE = 'ACCOUNT_STATUS_INVALID';
    const INVALID_STATUSES = ['queued-destroy', 'destroyed'];

    /**
     * @return bool
     */
    public function validate()
    {
        $hasError = false;
        try {
            $coreService = $this->getCoreService($this->actor->getExternalUserId());
            $accountStatus = $coreService->getStatus();

            if (in_array($accountStatus, self::INVALID_STATUSES)) {
                $this->failureReason = sprintf(self::ERROR_MESSAGE, $accountStatus);
                $hasError = true;
            }
        } catch (Exception $e) {
            $this->failureReason = self::EXCEPTION_MESSAGE;
            error_log($e);
            return false;
        }

        return !$hasError;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return $this->failureReason;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }

    /**
     * Wrapper to get Core Service
     *
     * @param int $serviceId Service Id
     * @return Core_Service
     * @throws Core_Exception
     */
    protected function getCoreService($serviceId)
    {
        return new Core_Service($serviceId);
    }
}
