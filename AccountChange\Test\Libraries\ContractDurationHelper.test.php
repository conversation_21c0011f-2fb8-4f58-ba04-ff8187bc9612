<?php

/**
 * <AUTHOR>
 */

class ContractDurationHelperTest extends PHPUnit_Framework_TestCase
{
    const NEW_SERVICE_DEFINITION_ID = 24233;
    const OLD_SERVICE_DEFINITION_ID = 32523;

    /**
     * @return void
     */
    protected function tearDown()
    {
        Mockery::close();
    }

    /**
     * @test
     * @return void
     */
    public function defaultContractForJohnlewisCustomerIsCorrect()
    {
        $dbAdaptor = $this->getMockBuilder('Db_Adaptor')
            ->disableOriginalConstructor()->getMock();

        $isp = $this->getMockBuilder('Val_ISP')
            ->setMethods(array('getISP'))
            ->disableOriginalConstructor()->getMock();

        $isp->method('getISP')->willReturn('johnlewis');

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor);

        $result = $contractDurationHelper->getContractDuration($isp, null, array());

        $this->assertEquals(12, $result);
    }

    /**
     * @test
     * @return void
     */
    public function defaultContractForPlusnetCustomerIsObtainedFromTheDatabase()
    {
        $dbAdaptor = $this->getMockBuilder('Db_Adaptor')
            ->setMethods(array('getContractDefinitionLengthFromSdi'))
            ->disableOriginalConstructor()->getMock();

        $dbAdaptor->method('getContractDefinitionLengthFromSdi')
            ->with(100)->willReturn(18);

        $isp = $this->getMockBuilder('Val_ISP')
            ->setMethods(array('getISP'))
            ->disableOriginalConstructor()->getMock();

        $isp->method('getISP')->willReturn('plusnet');

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor);

        $result = $contractDurationHelper->getContractDuration($isp, 100, array());

        $this->assertEquals(18, $result);
    }


    /**
     * @test
     * @return void
     */
    public function discountLengthIsReturnedWhenPromotionIsAvailable()
    {
        $dbAdaptor = $this->getMockBuilder('Db_Adaptor')
            ->disableOriginalConstructor()->getMock();

        $isp = $this->getMockBuilder('Val_ISP')
            ->disableOriginalConstructor()->getMock();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor);

        $discountLength = 24;

        $applicationData = [
            'promoCode' => 1,
            'intDiscountLength' => $discountLength,
            'selectedContractDuration' => 18
        ];

        $result = $contractDurationHelper->getContractDuration($isp, null, $applicationData);

        $this->assertEquals($discountLength, $result);
    }

    /**
     * @test
     * @return void
     */
    public function selectedContractDurationIsReturnedWhenAvailable()
    {
        $dbAdaptor = $this->getMockBuilder('Db_Adaptor')
            ->disableOriginalConstructor()->getMock();

        $isp = $this->getMockBuilder('Val_ISP')
            ->disableOriginalConstructor()->getMock();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor);

        $selectedContractDuration = 24;

        $applicationData = [
            'selectedContractDuration' => $selectedContractDuration
        ];

        $result = $contractDurationHelper->getContractDuration($isp, null, $applicationData);

        $this->assertEquals($selectedContractDuration, $result);
    }

    /**
     * @test
     * @return void
     */
    public function retainContractDuration()
    {
        $dbAdaptor = $this->getMockBuilder('Db_Adaptor')
            ->disableOriginalConstructor()->getMock();

        $isp = $this->getMockBuilder('Val_ISP')
            ->disableOriginalConstructor()->getMock();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor);

        $selectedContractDuration = 0;

        $applicationData = [
            'selectedContractDuration' => $selectedContractDuration,
            'existingBroadband' => array(
            'contractDuration' => 12
            )
        ];

        $result = $contractDurationHelper->getContractDuration($isp, null, $applicationData);
        $this->assertEquals($applicationData['existingBroadband']['contractDuration'], $result);
    }

    /**
     * @dataProvider testGetContractDurationOptionsDataProvider
     *
     * @param bool $isFibre         is fibre
     * @param int  $defaultDuration default duration
     * @param bool $hasContracts    has contracts
     *
     * @throws Exception
     * @return void
     */
    public function testGetContractDurationOptions($isFibre, $defaultDuration, $hasContracts, $overriddenDefaultContractDuration)
    {
        AccountChange_Registry::instance()->reset();

        $availableContractDurations = [12, 18];

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getAvailableContractDurationsForProduct')->andReturn($availableContractDurations);
        $dbAdaptor->shouldReceive('isFibreProduct')->andReturn($isFibre);
        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $contractsClient = Mockery::mock();
        $contractsClient->shouldReceive('setServiceId');
        $contractsClient->shouldReceive('getContracts')->andReturn($hasContracts);
        BusTier_BusTier::setClient('contracts', $contractsClient);

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor);
        $result = $contractDurationHelper->getContractDurationOptions([], 1, 1, 1, $overriddenDefaultContractDuration);

        $expected = [
            'availableContractDurations' => $availableContractDurations,
            'intDefaultContractDurationOption' => $defaultDuration,
            'bolShowNoRecontractOption' => $hasContracts
        ];

        $this->assertEquals($expected, $result);
    }

    /**
     * @return array
     */
    public function testGetContractDurationOptionsDataProvider()
    {
        return [
            [true, 18, true, 0],
            [false, 0, false, 0],
            [true, 24, true, 24],
            [false, 24, false, 24],
        ];
    }

    /**
     * @return void
     */
    public function testShouldDisplayContractDurationReturnsFalseWhenNotABroadbandProduct()
    {
        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('isBroadbandProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturn(0);

        $fibreHelper = Mockery::mock('AccountChange_FibreHelper');
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::OLD_SERVICE_DEFINITION_ID)->andReturnTrue();
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturnFalse();

        $coreService = Mockery::mock('Core_Service');
        $coreService->shouldReceive('isJohnLewisVispCustomer')->andReturnTrue();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor, $fibreHelper);

        $this->assertFalse($contractDurationHelper->shouldDisplayContractDuration(
            self::OLD_SERVICE_DEFINITION_ID,
            self::NEW_SERVICE_DEFINITION_ID,
            [],
            $coreService
        ));
    }

    /**
     * @return void
     */
    public function testShouldDisplayContractDurationReturnsFalseWhenNotJohnLewisOrNotMovingToOrFromFibre()
    {
        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('isBroadbandProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturn(1);

        $fibreHelper = Mockery::mock('AccountChange_FibreHelper');
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::OLD_SERVICE_DEFINITION_ID)->andReturnTrue();
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturnTrue();

        $coreService = Mockery::mock('Core_Service');
        $coreService->shouldReceive('isJohnLewisVispCustomer')->andReturnFalse();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor, $fibreHelper);

        $this->assertFalse($contractDurationHelper->shouldDisplayContractDuration(
            self::OLD_SERVICE_DEFINITION_ID,
            self::NEW_SERVICE_DEFINITION_ID,
            [],
            $coreService
        ));
    }

    /**
     * @return void
     */
    public function testShouldDisplayContractDurationReturnsTrueWhenCustomerIsJohnLewis()
    {
        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('isBroadbandProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturn(1);

        $fibreHelper = Mockery::mock('AccountChange_FibreHelper');
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::OLD_SERVICE_DEFINITION_ID)->andReturnFalse();
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturnFalse();

        $coreService = Mockery::mock('Core_Service');
        $coreService->shouldReceive('isJohnLewisVispCustomer')->andReturnTrue();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor, $fibreHelper);

        $this->assertTrue($contractDurationHelper->shouldDisplayContractDuration(
            self::OLD_SERVICE_DEFINITION_ID,
            self::NEW_SERVICE_DEFINITION_ID,
            [],
            $coreService
        ));
    }

    /**
     * @return void
     */
    public function testShouldDisplayContractDurationReturnsTrueWhenCustomerIsDowngradingFromFibre()
    {
        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('isBroadbandProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturn(1);

        $fibreHelper = Mockery::mock('AccountChange_FibreHelper');
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::OLD_SERVICE_DEFINITION_ID)->andReturnTrue();
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturnFalse();

        $coreService = Mockery::mock('Core_Service');
        $coreService->shouldReceive('isJohnLewisVispCustomer')->andReturnFalse();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor, $fibreHelper);

        $this->assertTrue($contractDurationHelper->shouldDisplayContractDuration(
            self::OLD_SERVICE_DEFINITION_ID,
            self::NEW_SERVICE_DEFINITION_ID,
            [],
            $coreService
        ));
    }

    /**
     * @return void
     */
    public function testShouldDisplayContractDurationReturnsTrueWhenCustomerIsUpgradingFromFibre()
    {
        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('isBroadbandProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturn(1);

        $fibreHelper = Mockery::mock('AccountChange_FibreHelper');
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::OLD_SERVICE_DEFINITION_ID)->andReturnFalse();
        $fibreHelper->shouldReceive('isFibreProduct')->with(self::NEW_SERVICE_DEFINITION_ID)->andReturnTrue();

        $coreService = Mockery::mock('Core_Service');
        $coreService->shouldReceive('isJohnLewisVispCustomer')->andReturnFalse();

        $contractDurationHelper = new AccountChange_ContractDurationHelper($dbAdaptor, $fibreHelper);

        $this->assertTrue($contractDurationHelper->shouldDisplayContractDuration(
            self::OLD_SERVICE_DEFINITION_ID,
            self::NEW_SERVICE_DEFINITION_ID,
            [],
            $coreService
        ));
    }
}
