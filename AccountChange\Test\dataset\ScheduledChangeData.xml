<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE dataset SYSTEM "dataset.dtd">
    <dataset>
        <table name="products.service_components">
            <column>service_component_id</column>
            <column>name</column>
            <column>description</column>
            <column>available</column>
            <column>date_created</column>
            <column>dteAvailableFrom</column>
            <column>dteAvailableTo</column>
            <column>bolSignupBoltOn</column>
            <column>bolInLifeBoltOn</column>
            <column>bolScheduledCancellation</column>
            <column>db_src</column>
            <column>time_stamp</column>
            <column>isp</column>
            <row>
                <value>1</value>
                <value>Product One</value>
                <value>Product One</value>
                <value>Yes</value>
                <value>2001-02-03</value>
                <value>2001-02-03</value>
                <null />
                <value>1</value>
                <value>1</value>
                <value>1</value>
                <value>bselby</value>
                <value>2001-02-03 01:02:03</value>
                <value>generic</value>
            </row>
            <row>
                <value>2</value>
                <value>Product Two</value>
                <value>Product Two</value>
                <value>Yes</value>
                <value>2001-02-03</value>
                <value>2001-02-03</value>
                <null />
                <value>1</value>
                <value>1</value>
                <value>1</value>
                <value>bselby</value>
                <value>2001-02-03 01:02:03</value>
                <value>generic</value>
            </row>
        </table>

        <table name="products.service_definitions">
            <column>service_definition_id</column>
            <column>intProductVariantId</column>
            <column>name</column>
            <column>isp</column>
            <column>minimum_charge</column>
            <column>date_created</column>
            <column>db_src</column>
            <column>time_stamp</column>
            <column>requires</column>
            <column>initial_charge</column>
            <column>decUpgradeCharge</column>
            <column>type</column>
            <column>creator_id</column>
            <column>password_visible_to_support</column>
            <column>end_date</column>
            <column>signup_via_portal</column>
            <column>bt_product_id</column>
            <column>blurb</column>
            <row>
                <value>1</value>
                <value>1</value>
                <value>Product One</value>
                <value>plus.net</value>
                <value>10</value>
                <value>2000-01-01</value>
                <value>bselby</value>
                <value>2000-01-01 00:00:00</value>
                <value>adsl</value>
                <value>10</value>
                <value>10</value>
                <value>residential</value>
                <value>bselby</value>
                <value>Y</value>
                <null />
                <value>Y</value>
                <null />
                <value>Blurb</value>
            </row>
            <row>
                <value>2</value>
                <value>1</value>
                <value>Product Two</value>
                <value>plus.net</value>
                <value>20</value>
                <value>2000-01-01</value>
                <value>bselby</value>
                <value>2000-01-01 00:00:00</value>
                <value>adsl</value>
                <value>20</value>
                <value>20</value>
                <value>residential</value>
                <value>bselby</value>
                <value>Y</value>
                <null />
                <value>Y</value>
                <null />
                <value>Blurb</value>
            </row>
        </table>

        <table name="products.tblPromotionCode">
            <column>intPromotionCodeId</column>
            <column>vchPromotionCode</column>
            <column>intPresetDiscountId</column>
            <column>dteValidFrom</column>
            <column>dteValidTo</column>
            <column>vchDescription</column>
            <column>stmTimestamp</column>
            <row>
                <value>1</value>
                <value>ctr12mhp</value>
                <value>1</value>
                <value>2005-09-21 19:53:30</value>
                <value>2009-09-21 19:53:30</value>
                <value>Test Promo Code</value>
                <value>2009-09-21 19:53:30</value>
            </row>
        </table>

        <table name="userdata.service_change_schedule">
            <column>schedule_id</column>
            <column>service_id</column>
            <column>owner_id</column>
            <column>old_type</column>
            <column>new_type</column>
            <column>intTariffID</column>
            <column>change_complete</column>
            <column>change_complete_date</column>
            <column>change_date</column>
            <column>active</column>
            <column>db_src</column>
            <column>intFromCBCFlexID</column>
            <column>intToCBCFlexID</column>
            <column>bolCbcFix</column>
            <column>intContractLengthID</column>
            <column>intPromotionCodeId</column>
            <column>timestamp</column>
            <row>
                <value>1</value>
                <value>919</value>
                <value>eaba325a882a3725dc28e3e5ef272bfa</value>
                <value>1</value>
                <value>2</value>
                <value>1</value>
                <value>no</value>
                <value>0000-00-00</value>
                <value>2011-09-01</value>
                <value>yes</value>
                <value></value>
                <value>1</value>
                <value>1</value>
                <null />
                <value>1</value>
                <value>1</value>
                <value>2011-01-01 00:00:00</value>
            </row>
            <row>
                <value>2</value>
                <value>920</value>
                <value>eaba325a882a3725dc28e3e5ef272bfa</value>
                <value>2</value>
                <value>1</value>
                <value>1</value>
                <value>no</value>
                <value>0000-00-00</value>
                <value>2011-09-01</value>
                <value>yes</value>
                <value></value>
                <value>1</value>
                <value>1</value>
                <null />
                <value>1</value>
                <value>1</value>
                <value>2011-01-01 00:00:00</value>
            </row>
            <row>
                <value>3</value>
                <value>921</value>
                <value>eaba325a882a3725dc28e3e5ef272bfa</value>
                <value>2</value>
                <value>1</value>
                <value>1</value>
                <value>yes</value>
                <value>0000-00-00</value>
                <value>2011-09-01</value>
                <value>no</value>
                <value></value>
                <value>1</value>
                <value>1</value>
                <null />
                <value>1</value>
                <value>1</value>
                <value>2011-01-01 00:00:00</value>
            </row>
        </table>

        <table name="userdata.components">
            <column>component_id</column>
            <column>service_id</column>
            <column>component_type_id</column>
            <column>config_id</column>
            <column>description</column>
            <column>status</column>
            <column>db_src</column>
            <column>creationdate</column>
            <column>timestamp</column>
            <row>
                <value>1</value>
                <value>919</value>
                <value>1</value>
                <value>1</value>
                <value></value>
                <value>active</value>
                <value>bselby</value>
                <value>2011-01-01 00:00:00</value>
                <value>2011-01-01 00:00:00</value>
            </row>
            <row>
                <value>2</value>
                <value>919</value>
                <value>2</value>
                <value>1</value>
                <value></value>
                <value>active</value>
                <value>bselby</value>
                <value>2011-01-01 00:00:00</value>
                <value>2011-01-01 00:00:00</value>
            </row>
            <row>
                <value>3</value>
                <value>920</value>
                <value>2</value>
                <value>1</value>
                <value></value>
                <value>active</value>
                <value>bselby</value>
                <value>2011-01-01 00:00:00</value>
                <value>2011-01-01 00:00:00</value>
            </row>
        </table>

        <table name="dbSystemEvents.tblScheduledEvent">
            <column>intScheduledEventID</column>
            <column>intEventTypeID</column>
            <column>dtmCreated</column>
            <column>dteDue</column>
            <column>bolOnBilling</column>
            <column>bolOnContractEnd</column>
            <column>dtmCompleted</column>
            <column>dtmCancelled</column>
            <column>stmLastUpdate</column>
            <column>bolAdjustDate</column>
            <row>
                <value>1</value>
                <value>3</value>
                <value>2011-09-01 00:00:00</value>
                <value>2030-09-01</value>
            <value>1</value>
            <value>1</value>
            <null />
            <null />
            <value>2011-09-01 00:00:00</value>
            <value>1</value>
        </row>
        <row>
            <value>2</value>
            <value>3</value>
            <value>2011-09-01 00:00:00</value>
            <value>2001-09-01</value>
            <value>1</value>
            <value>1</value>
            <value>2001-09-02</value>
            <null />
            <value>2011-09-01 00:00:00</value>
            <value>1</value>
        </row>
        <row>
            <value>3</value>
            <value>3</value>
            <value>2011-09-01 00:00:00</value>
            <value>2001-09-01</value>
            <value>1</value>
            <value>1</value>
            <null />
            <value>2001-09-02</value>
            <value>2011-09-01 00:00:00</value>
            <value>1</value>
        </row>
        <row>
            <value>4</value>
            <value>3</value>
            <value>2011-09-01 00:00:00</value>
            <value>2001-09-01</value>
            <value>1</value>
            <value>1</value>
            <value>2001-09-02</value>
            <null />
            <value>2011-09-01 00:00:00</value>
            <value>1</value>
        </row>
    </table>

    <table name="dbSystemEvents.tblProductChange">
        <column>intProductChangeID</column>
        <column>intScheduledEventID</column>
        <column>intComponentID</column>
        <column>intNewComponentID</column>
        <column>uxtContractEnd</column>
        <column>stmLastUpdate</column>
        <row>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2</value>
            <value>2321</value>
            <value>2011-09-01 00:00:00</value>
        </row>
        <row>
            <value>2</value>
            <value>2</value>
            <value>1</value>
            <value>2</value>
            <value>2321</value>
            <value>2011-09-01 00:00:00</value>
        </row>
        <row>
            <value>3</value>
            <value>3</value>
            <value>1</value>
            <value>2</value>
            <value>2321</value>
            <value>2011-09-01 00:00:00</value>
        </row>
        <row>
            <value>4</value>
            <value>4</value>
            <value>2</value>
            <value>2</value>
            <value>2321</value>
            <value>2011-09-01 00:00:00</value>
        </row>
    </table>

    <table name="userdata.tblProductComponentInstance">
        <column>intProductComponentInstanceID</column>
        <column>intComponentID</column>
        <column>intProductComponentID</column>
        <column>intStatusID</column>
        <column>intTariffID</column>
        <column>dteNextInvoice</column>
        <column>dtmStart</column>
        <column>dtmEnd</column>
        <column>stmLastUpdate</column>
        <row>
            <value>1</value>
            <value>2</value>
            <value>1</value>
            <value>1</value>
            <value>390</value>
            <value>null</value>
            <value>2011-09-01 00:00:00</value>
            <value>null</value>
            <value>2011-09-01 00:00:00</value>
        </row>
    </table>

    <table name ="dbProductComponents.tblTariffType">
        <column>intTariffTypeID</column>
        <column>vchHandle</column>
        <column>stmModified</column>
            <row>
                <value>1</value>
                <value>DEFAULT</value>
                <value>2011-11-02 03:32:10</value>
            </row>
            <row>
                <value>2</value>
                <value>LINE_RENTAL_SAVER</value>
                <value>2011-11-02 03:32:10</value>
            </row>
            <row>
                <value>3</value>
                <value>PREPAID_CONTRACT</value>
                <value>2011-11-02 03:32:10</value>
            </row>
    </table>

    <table name="dbProductComponents.tblTariff">
        <column>intTariffID</column>
        <column>intProductComponentConfigID</column>
        <column>intTariffTypeID</column>
        <column>intContractLengthID</column>
        <column>intPaymentFrequencyID</column>
        <column>intQuantityFrom</column>
        <column>intQuantityTo</column>
        <column>intCostIncVatPence</column>
        <column>bolAutoRenew</column>
        <column>intNextTariffID</column>
        <column>intNoticePeriodDays</column>
        <column>dtmStart</column>
        <column>dtmEnd</column>
        <column>stmLastUpdate</column>
        <row>
            <value>1</value>
            <value>299</value>
            <value>1</value>
            <value>3</value>
            <value>3</value>
            <value>1</value>
            <value>1</value>
            <value>2000</value>
            <value>1</value>
            <value>390</value>
            <value>0</value>
            <value>2011-09-01 00:00:00</value>
            <value>NULL</value>
            <value>2011-09-01 00:00:00</value>
        </row>
        <row>
            <value>390</value>
            <value>299</value>
            <value>1</value>
            <value>3</value>
            <value>3</value>
            <value>1</value>
            <value>1</value>
            <value>1299</value>
            <value>1</value>
            <value>390</value>
            <value>0</value>
            <value>2011-09-01 00:00:00</value>
            <value>NULL</value>
            <value>2011-09-01 00:00:00</value>
        </row>
    </table>
</dataset>
