<?php

/***
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_AccountChangePcics
{
    /**
     * @var bool
     */
    private $notificationSent;

    /**
     * @var string
     */
    private $streamingUrl;

    /**
     * @var string
     */
    private $streamingId;

    /**
     * @var string
     */
    private $orderId;

    /**
     * @return bool
     */
    public function getNotificationSent()
    {
        return $this->notificationSent;
    }

    /**
     * @param bool $notificationSent Notification sent status
     * @return void
     */
    public function setNotificationSent($notificationSent)
    {
        $this->notificationSent = $notificationSent;
    }

    /**
     * @return string
     */
    public function getStreamingUrl()
    {
        return $this->streamingUrl;
    }

    /**
     * @param string $streamingUrl Streaming URL
     * @return void
     */
    public function setStreamingUrl($streamingUrl)
    {
        $this->streamingUrl = $streamingUrl;
    }

    /**
     * @return string
     */
    public function getStreamingId()
    {
        return $this->streamingId;
    }

    /**
     * @param string $streamingId Streaming Id
     * @return void
     */
    public function setStreamingId($streamingId)
    {
        $this->streamingId = $streamingId;
    }

    /**
     * @return string
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * @param string $orderId Order Id
     * @return void
     */
    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;
    }
}