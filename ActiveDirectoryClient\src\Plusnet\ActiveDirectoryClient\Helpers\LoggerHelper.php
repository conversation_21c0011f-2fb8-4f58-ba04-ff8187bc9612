<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\ActiveDirectoryClient\Helpers;

class LoggerHelper
{
    const LOG_LEVEL_INFO = 'INFO';
    const LOG_LEVEL_ERROR = 'ERROR';
    const BILLING_LOG_FILE_PATH = '/var/log/php5/billingPhpAudit.log';
    const SERVICES_LOG_FILE_PATH = '/var/log/php5/servicesAudit.log';

    const CONTEXT = 'Semafone';

    /**
     * @var  \Log_Logger
     */
    private $logger;

    /**
     * LoggerHelper constructor.
     * Receives a passed in argument to determine which log file should be used
     *
     * @param string            $logFilePath    Path to Log file
     * @param \Log_Logger|null  $logger         Instance of Log_logger
     */
    public function __construct(
        $logFilePath,
        \Log_Logger $logger = null
    ) {
        $this->logger = $logger ?: new \Log_Logger();
        $this->logger->registerLogHandler(
            new \Log_FileLogHandler($logFilePath)
        );

        \Log_AuditLog::registerLoggerForContext($this->logger, static::CONTEXT);
    }

    /**
     * Write to the log with log level info.
     *
     * @param string $message Sets the message to log
     */
    public function info($message)
    {
        $this->log($message, static::LOG_LEVEL_INFO);
    }

    /**
     * Write to the log with log level error.
     *
     * @param string $message Sets the message to log
     */
    public function error($message)
    {
        $this->log($message, static::LOG_LEVEL_ERROR);
    }

    /**
     * Write to the log.
     *
     * @param string $message   Sets the message to log
     * @param string $logLevel  Sets logging level (error/info)
     *
     * @throws \WrongTypeException
     */
    private function log(
        $message,
        $logLevel
    ) {
        $message = str_replace(PHP_EOL, " | ", $message);
        $logData = \Log_LogData::factory($message, static::CONTEXT, $logLevel);

        \Log_AuditLog::writeStandard($logData, static::CONTEXT);
    }
}