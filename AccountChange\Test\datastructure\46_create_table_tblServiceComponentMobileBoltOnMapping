 CREATE TABLE products.tblServiceComponentMobileBoltOnMapping (
  intServiceComponentMobileBoltOnMappingID int(10) unsigned NOT NULL AUTO_INCREMENT,
  intServiceComponentID int(8) unsigned NOT NULL,
  intBoltOnServiceComponentID int(8) unsigned NOT NULL,
  stmLastUpdate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (intServiceComponentMobileBoltOnMappingID),
  UNIQUE KEY intServiceComponentID (intServiceComponentID,intBoltOnServiceComponentID),
  UNIQUE KEY intBoltOnServiceComponentID (intBoltOnServiceComponentID),
  CONSTRAINT tblServiceComponentMobileBoltOnMapping_ibfk_1 FOREIGN KEY (intServiceComponentID) REFERENCES service_components (service_component_id),
  CONSTRAINT tblServiceComponentMobileBoltOnMapping_ibfk_2 FOREIGN KEY (intBoltOnServiceComponentID) REFERENCES service_components (service_component_id)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1