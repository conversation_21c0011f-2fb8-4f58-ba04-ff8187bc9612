<?php
/**
 * Account Change LegacyComponents Action Test
 *
 * @category  AccountChange_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-14
 */
/**
 * Account Change LegacyComponents Action Test
 *
 * Testing class for AccountChange_Action_Products
 *
 * @category  AccountChange_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Action_Products_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Service Id fixture
     *
     * @var void
     */
    private $serviceId = 123;

    /**
     * Test that execute gets called and setup correctly
     *
     * @covers AccountChange_Action_Products::execute
     *
     * @return void
     */
    public function testExecuteCorrectlySetsUpTheObjectAllCallsCorrectSubFunctions()
    {
        $this->registry = AccountChange_Registry::instance();
        $this->registry->setEntry('arrLegacyComponentTypesToIgnore', array());

        $action = $this->getMock('AccountChange_Action_Products',
                                 array('processMailboxProduct', 'processCommunityProduct', 'processFirewallProduct'),
                                 array($this->serviceId));

        $action->expects($this->once())
               ->method('processMailboxProduct');

        $action->expects($this->once())
               ->method('processCommunityProduct');

        $action->expects($this->once())
               ->method('processFirewallProduct');

        $action->execute();
    }
}
