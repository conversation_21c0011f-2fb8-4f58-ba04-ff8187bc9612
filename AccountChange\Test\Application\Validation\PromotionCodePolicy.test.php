<?php

use Plusnet\BillingApiClient\Service\ServiceManager;
use Plusnet\BillingApiClient\Entity\Constant\BillingAccountStatus;

class AccountChange_PromotionCodePolicyTest extends PHPUnit_Framework_TestCase
{

    const SERVICE_ID = 12345;
    const PROMO_CODE = 'testPromotion';

    /**
     * Tests that getErrorCode gives the expected error code
     *
     * @return void
     **/
    public function testGetErrorCodeGivesCorrectCode()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $this->assertEquals(AccountChange_PromotionCodePolicy::ERROR_CODE, $validator->getErrorCode());
    }


    /**
     * Tests that getFailure gives the expected error message
     *
     * @return void
     **/
    public function testGetFailureGivesExpectedErrorMessage()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $this->assertEquals(AccountChange_PromotionCodePolicy::ERROR_MESSAGE, $validator->getFailure());
    }


   /**
    * Tests the below combination:
    *
    *  C2mDiscount code present | C2m Discount Has Line Rental | User has LRS | Should Validate?
    *  -------------------------+------------------------------+--------------+------------------
    *  NO                       | n/a                          | n/a          | YES
    *
    * @return void
    **/
    public function testValidateReturnsTrueWhenNoC2mPromotionCodeOrObjectPresent()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn(false);
        $this->assertTrue($validator->validate());
    }


   /**
    * Tests the below combination:
    *
    *  C2mDiscount code present | C2m Discount Has Line Rental | User has LRS | Should Validate?
    *  -------------------------+------------------------------+--------------+------------------
    *  YES                      | YES                          | YES          | NO
    *
    * @return void
    **/
    public function testValidateReturnsFalseWhenThereIsALineRentalDiscountAndCustomerHasLRS()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $c2mPromotion = $this->getC2mPromotionMock();

        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn($c2mPromotion);
        $validator->shouldReceive('promotionHasLineRentalDiscount')->with($c2mPromotion)->andReturn(true);
        $validator->shouldReceive('hasActiveOrPendingLrs')->andReturn(true);

        $this->assertFalse($validator->validate());
    }

   /**
    * Tests the below combination:
    *
    *  C2mDiscount code present | C2m Discount Has Line Rental | User has LRS | Should Validate?
    *  -------------------------+------------------------------+--------------+------------------
    *  YES                      | NO                           | YES          | YES
    *
    * @return void
    **/
    public function testValidateReturnsTrueWhenThereIsNoLineRentalDiscountAndCustomerHasLRS()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $c2mPromotion = $this->getC2mPromotionMock();

        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn($c2mPromotion);
        $validator->shouldReceive('promotionHasLineRentalDiscount')->with($c2mPromotion)->andReturn(false);
        $validator->shouldReceive('hasActiveOrPendingLrs')->andReturn(true);

        $this->assertTrue($validator->validate());
    }

   /**
    * Tests the below combination:
    *
    *  C2mDiscount code present | C2m Discount Has Line Rental | User has LRS | Should Validate?
    *  -------------------------+------------------------------+--------------+------------------
    *  YES                      | YES                          | NO           | YES
    *
    * @return void
    **/
    public function testValidateReturnsTrueWhenThereIsALineRentalDiscountAndCustomerDoesNotHaveLRS()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $c2mPromotion = $this->getC2mPromotionMock();

        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn($c2mPromotion);
        $validator->shouldReceive('promotionHasLineRentalDiscount')->with($c2mPromotion)->andReturn(true);
        $validator->shouldReceive('hasActiveOrPendingLrs')->andReturn(false);

        $this->assertTrue($validator->validate());
    }

    /**
     * Tests that getC2mPromotionFromAdditionalData returns a c2m promotion if one is passed through the
     * additional data
     *
     * @return void
     **/
    public function testGetC2mPromotionFromAdditionalDataReturnsC2mPromotionWhenPromotionObjectPassedIn()
    {

        $additionalInfo = array('C2MPromotion' => $this->getC2mPromotionMock());

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $c2mPromotion = $this->getC2mPromotionMock();

        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $this->assertEquals($c2mPromotion, $validator->getC2mPromotionFromAdditionalData());
    }


    /**
     * Tests that getC2mPromotionFromAdditionalData returns a c2m promotion if one is passed through the
     * additional data as a promo code, and a c2m promo with that code exists
     *
     * @return void
     **/
    public function testGetC2mPromotionFromAdditionalDataReturnsC2mPromotionWhenValidPromoCodePassedIn()
    {

        $additionalInfo = array('C2MPromotionCode' => self::PROMO_CODE);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $c2mPromotion = $this->getC2mPromotionMock();

        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn($c2mPromotion);
        $this->assertEquals($c2mPromotion, $validator->getC2mPromotionFromAdditionalData());
    }

    /**
     * Tests that getC2mPromotionFromAdditionalData returns false when no promotion or promo code is passed through
     * additional data
     *
     * @return void
     **/
    public function testGetC2mPromotionFromAdditionalDataReturnsFalseWhenNoPromoPassedThrough()
    {

        $additionalInfo = array();

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $this->assertFalse($validator->getC2mPromotionFromAdditionalData());
    }

    /**
     * Tests that getC2mPromotionFromAdditionalData returns false when a promo code is passed through as
     * additional data and that promo code does not map to a valid c2m promotion.
     *
     * @return void
     **/
    public function testGetC2mPromotionFromAdditionalDataReturnsFalseWhenPromoPassedInIsInvalid()
    {

        $additionalInfo = array('C2MPromotionCode' => self::PROMO_CODE);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodePolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn(false);
        $this->assertFalse($validator->getC2mPromotionFromAdditionalData());
    }

    /**
     * Return a mock c2m promotion
     *
     * @return Promotion
     **/
    protected function getC2mPromotionMock()
    {
        $c2mPromotion = Mockery::mock('Plusnet\C2mApiClient\Entity\Promotion');
        $c2mPromotion->makePartial();
        return $c2mPromotion;
    }
}
