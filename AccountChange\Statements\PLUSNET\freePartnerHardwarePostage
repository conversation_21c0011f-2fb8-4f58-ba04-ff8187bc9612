server: coredb
role: slave
rows: single
statement:
SELECT
    ppa.bolIsPostageFree
FROM
	PlusnetSession.tblBusinessActor ba
INNER JOIN Reseller.tblEndUser eu
	ON eu.intEndUserActorId = ba.actorID
INNER JOIN Reseller.tblPartnerProductAvailability ppa
	ON ppa.intPartnerActorId = eu.intPartnerActorId
INNER JOIN Reseller.tblPartnerProductFreeHardware ppfh
	ON ppfh.intPartnerProductAvailabilityId = ppa.intPartnerProductAvailabilityId
WHERE
    ba.externalUserID = :serviceId
    AND ppa.intServiceDefinitionId = :intNewSdi
    AND ppa.dteEnd IS NULL;