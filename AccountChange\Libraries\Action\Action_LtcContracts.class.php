<?php
/**
 * Pending Contract Action
 *
 * Action that uses the ProductChangePlanClient to come up with a change plan
 * relating to contracts, and applies any actions on the scheduling of the
 * account change.  There is a separate action on the actual execution
 * of the account change.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
/**
 * AccountChange_Action_LtcContracts class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 * @link      http://jira.internal.plus.net/browse/LTC-1672
 */
class AccountChange_Action_LtcContracts extends AccountChange_Action
{

    /**
     * Product offering id will eventually tie into the new-world db structure
     * to describe a bundle of ADSL and WLR.  For now, it's the new service
     * definition id.
     *
     * @var int
     **/
    private $productOfferingId;

    /**
     * Old product offering id - which is the old service definition id
     *
     * @var int
     **/
    private $oldProductOfferingId;


    /**
     * Array of associative arrays with
     * 'serviceTypeId' and 'serviceId' keys
     * serviceId is optional depending on whether or not we know it at the time
     *
     * @var array
     **/
    private $servicesToAdd = array();

    /**
     * An array of the actual component ids to remove as part of this change
     * so it's the service_component_id for WLR and INTERNET_CONNECTION
     *
     * @var array
     **/
    private $componentIdsToRemove = array();


    /**
     * Keep track of whether or not the broadband's going to be changing
     *
     * @var bool
     **/
    private $adslChanging = false;

    /**
     * Keep track of whether or not the phone component's going to be changing
     *
     * @var bool
     **/
    private $wlrChanging = false;

    /**
     * Client for the product change plan service
     *
     * @var Plusnet\ProductChangePlanClient\Client
     */
    private $changePlanClient;

    /**
     * If we've come from a completed account change then this
     * will be the INTERNET_CONNECTION component id, if it's a
     * scheduled change then it'll be null
     *
     * @var int
     **/
    private $newAdslComponentId;

    /**
     * If we've come from a completed account change then this
     * will be the WLR component id, if it's changed. If it's
     * unchanged or a scheduled change then it'll be null
     *
     * @var int
     **/
    private $newWlrComponentId;

    /**
     * New contract duration
     *
     * @var int
     **/
    private $newContractDuration;

    /**
     * Boolean to check Account Change is Schedule or Immediate
     *
     * @var boolean
     **/
    private $bolSchedule;

    /**
     * Boolean to check whether it is recontract while activating the contract
     *
     * @var boolean
     **/
    private $activateRecontract;

    /**
     * Overrides default constructor to add obtaining the relevent data from the registry.
     *
     * @param int   $serviceId The service id
     * @param array $options   Array of options
     *
     * @see AccountChange_Action::_construct
     *
     * @return void
     */
    public function __construct($serviceId, $options)
    {
        parent::__construct($serviceId, $options);

        $registry = AccountChange_Registry::instance();
        $newServiceDefinitionId = $registry->getEntry('intNewServiceDefinitionId');
        $oldServiceDefinitionId = $registry->getEntry('intOldServiceDefinitionId');
        $newWlrServiceComponentId = $registry->getEntry('newWlrServiceComponentId');
        $oldWlrServiceComponentId = $registry->getEntry('oldWlrServiceComponentId');

        // Product offering id should be the new service definition id if there is
        // one, else default to the old service definition id..
        if (!empty($newServiceDefinitionId)) {
            if ($oldServiceDefinitionId != $newServiceDefinitionId) {
                $this->adslChanging = true;
            }
            $this->productOfferingId = (int)$newServiceDefinitionId;
            $this->oldProductOfferingId = (int)$oldServiceDefinitionId;
        } else {
            // This will be the case if it's a WLR only change - then old and new will be the same
            $this->productOfferingId = (int)$oldServiceDefinitionId;
            $this->oldProductOfferingId = (int)$oldServiceDefinitionId;
        }
        $this->newAdslComponentId = $registry->getEntry('newAdslComponentId');
        $this->newWlrComponentId = $registry->getEntry('newWlrComponentId');

        if ($this->newAdslComponentId !== null) {
            $this->newAdslComponentId = (int)$this->newAdslComponentId;
        }

        if ($this->newWlrComponentId !== null) {
            $this->newWlrComponentId = (int)$this->newWlrComponentId;
        }

        $adslToFttc = $registry->getEntry('$adslToFttc');

        // Have we changed our phone service?
        $this->wlrChanging = (!$adslToFttc
            && !empty($newWlrServiceComponentId)
            && $newWlrServiceComponentId != $oldWlrServiceComponentId
        );

        // Have we changed our broadband service?
        $broadbandChanging = (!empty($newServiceDefinitionId) && $newServiceDefinitionId != $oldServiceDefinitionId);
        if ($broadbandChanging) {
            $oldAdslComponentId = $registry->getEntry('oldAdslComponentId');
            $newAdslServiceComponentId = $registry->getEntry('newAdslServiceComponentId');

            if (!empty($oldAdslComponentId)) {
                $this->componentIdsToRemove[] = (int)$oldAdslComponentId;
            }

            $defunctAdslComponentIds = $registry->getEntry('defunctAdslComponentIds');

            if (!empty($defunctAdslComponentIds)) {
                foreach ($defunctAdslComponentIds as $componentId) {
                    $this->componentIdsToRemove[] = (int)$componentId;
                }
            }

            // If we've got an old service definition id then we won't have a new scid
            // so don't try and add it to the request.
            if (!empty($newAdslServiceComponentId)) {
                $this->addService(
                    (int)$newAdslServiceComponentId,
                    $this->newAdslComponentId
                );
            }
        }

        if ($this->wlrChanging && !empty($this->newWlrComponentId)) {
            $oldWlrComponentId = $registry->getEntry('oldWlrComponentId');

            if (!empty($oldWlrComponentId)) {
                $this->componentIdsToRemove[] = (int)$oldWlrComponentId;
            }

            $this->addService(
                $newWlrServiceComponentId,
                $this->newWlrComponentId
            );
        }

        $this->newContractDuration = $registry->getEntry('selectedContractDuration');

        $this->activateRecontract = $registry->getEntry('activateRecontract');

        if ($registry->getEntry('isRecontract')) {
            $this->bolSchedule = false;
        } else {
            $this->bolSchedule = $registry->getEntry('bolSchedule');
        }
    }

    /**
     * Adds an entry into the _servicesToAdd array, adding component id if it's available
     *
     * @param int $componentTypeId Component type id (maps to serviceTypeId in the new structure)
     * @param int $componentId     Component id (maps to serviceId in the new db structure)
     *
     * @return void
     **/
    public function addService($componentTypeId, $componentId)
    {
        $entry['serviceTypeId'] = (int)$componentTypeId;
        if (!empty($componentId)) {
            $entry['serviceId'] = (int)$componentId;
        }
        $this->servicesToAdd[] = $entry;
    }

    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $registry = AccountChange_Registry::instance();

        if ($this->isRetainContract($registry)) {
            return;
        }

        if ($registry->getEntry('newContractOrder')) {
            $this->executeChangeForContractOrder($registry);
        } else {
            $this->executeChangeByChangePlan($registry);
        }
    }

    /**
     * Builds and activates a new contract based on the information selected
     * by the customer during the account change journey
     *
     * @param AccountChange_Registry $registry Registry object holding all relevant data for an account change
     *
     * @return void
     */
    public function executeChangeForContractOrder($registry)
    {
        $contractsClient = BusTier_BusTier::getClient('contracts')
            ->setServiceId($this->intServiceId);

        $options = [
            'serviceId' => $this->intServiceId,
            'agreementDate' => $registry->getEntry('agreementDate'),
            'contractType' => $registry->getEntry('contractType'),
            'contractSubType' => $registry->getEntry('contractSubType'),
            'duration' => $registry->getEntry('selectedContractDuration'),
            'durationUnit' => 'MONTH'
        ];

        $contract = $contractsClient->createContractFromOrder(
            $options,
            'ACCOUNT_CHANGE',
            $this->bolSchedule
        );

        if($registry->getEntry('instantRecontract'))
        {
            $contractsClient->updateStatus($contract, 'Immediate re-contract', 'ACTIVE', null, true);
        }
    }

    /**
     * Builds and activates a new contract using the change plan available
     *
     * @param AccountChange_Registry $registry Registry object holding all relevant data for an account change
     * @throws Exception
     *
     * @return void
     */
    private function executeChangeByChangePlan($registry)
    {
        try {
            $changePlan = $this->getChangePlan();
            if ($changePlan != null) {
                $changePlan->bolSchedule = $this->bolSchedule;
                $changePlan->activateRecontract = $this->activateRecontract;
                $options = [
                    'agreementDate' => $registry->getEntry('agreementDate'),
                    'contractType' => $registry->getEntry('contractType'),
                    'contractSubType' => $registry->getEntry('contractSubType')
                ];
                $changePlan->execute($options);
                $registry->setEntry(
                    'willAccountChangeResultInRecontract',
                    $changePlan->isRecontractingOrNewContract()
                );
            }
        } catch (Exception $e) {
            // AccountChange_Api can be configured to re-throw this exception if we need to
            // know about it in the calling code
            $rethrow = $registry->getEntry('disableContractsErrorHandle');

            // Validation failure "cannot add legacy products" is
            // the desired behavior and should never be logged.
            if (method_exists($e, 'getValidationFailures')) {
                $validationFailures = $e->getValidationFailures();
                foreach ($validationFailures as $validationFailure) {
                    if ($validationFailure->getRule() === 'CANNOT_ADD_LEGACY_PRODUCTS') {
                        if ($rethrow) {
                            throw new Exception('Recontracting is not available on legacy products.');
                        }
                        return;
                    }
                }
            }

            $validationFailure = 'n/a';

            if (method_exists($e, 'getValidationMessage')) {
                $validationFailure = $e->getValidationMessage();
            }

            // The client should take care of any error fallout - raising tickets / autoproblems
            // etc, so we should be safe to continue here.
            error_log(
                __FILE__.' '.__LINE__.' caught an exception when trying to request / execute a product change plan '.
                'during Account Change.  The exception was: '.(string)$e."  Validation failures: $validationFailure"
            );

            if ($rethrow) {
                throw new Exception("Recontracting failed with the following errors: {$validationFailure}");
            }
        }
    }

    /**
     * Should the existing contract be retained
     *
     * @param  AccountChange_Registry $registry account change registry
     * @return boolean
     **/
    private function isRetainContract($registry)
    {
        $retainContract = false;
        if ((isset($this->arrOptions['bolHousemove']) && $this->arrOptions['bolHousemove'])
                || $registry->getEntry('retainContract')) {
            $retainContract = true;
        }
        return $retainContract;
    }

    /**
     * Use the ProductChangePlanClient to get a contract change plan based on the
     * specific circumstances of this account change.
     *
     * @return Plusnet\ProductChangePlanClient\Entity\ChangePlan
     **/
    protected function getChangePlan()
    {
        $options = array();

        $changePlan = null;

        $newContractDuration = $this->getNewContractDuration();
        if (!empty($newContractDuration)) {
            $options['ForceRecontractLength'] = array($newContractDuration);
        }

        $changeChannel = $this->getChangeChannel();
        $changePlanClient = $this->getChangePlanClient();

        switch ($changeChannel) {
            case \Plusnet\ProductChangePlanClient\ChangeChannels::EXECUTE_ACCOUNT_CHANGE:
            case \Plusnet\ProductChangePlanClient\ChangeChannels::EXECUTE_ACCOUNT_CHANGE_RECONTRACT:
                // RBM now handles these, so nothing to do
                break;
            case \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST:
            case \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE:
            case \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_CUSTOMER_REQUEST:
            case \Plusnet\ProductChangePlanClient\ChangeChannels::IMMEDIATE_ACCOUNT_CHANGE_RECONTRACT:
            case \Plusnet\ProductChangePlanClient\ChangeChannels::SCHEDULE_ACCOUNT_CHANGE_RECONTRACT:
                $changePlan = $changePlanClient->productChange(
                    $changeChannel,
                    $this->intServiceId,
                    $this->getOldProductOfferingId(),
                    $this->getProductOfferingId(),
                    $this->getServicesToAdd(),
                    $this->getComponentIdsToRemove(),
                    $options
                );
                break;
        }
        return $changePlan;
    }

    /**
     * Lazy load change plan client
     *
     * @return \Plusnet\ProductChangePlanClient\Client
     */
    public function getChangePlanClient()
    {
        if ($this->changePlanClient === null) {
            $this->changePlanClient = \BusTier_BusTier::getClient('productChangePlan');
        }

        $registry = AccountChange_Registry::instance();
        $overridenSessionId = $registry->getEntry('overridenBusinessTierSessonId');
        if (!empty($overridenSessionId)) {
            $this->changePlanClient->setSessionId($overridenSessionId);
        }




        return $this->changePlanClient;
    }

    /**
     * Inject product change plan client
     *
     * @param \Plusnet\ProductChangePlanClient\Client $client Product change plan client
     *
     * @return void
     */
    public function setChangePlanClient(\Plusnet\ProductChangePlanClient\Client $client)
    {
        $this->changePlanClient = $client;
    }

    /**
     * Getter for _productOfferingId
     *
     * @return int
     **/
    public function getProductOfferingId()
    {
        return $this->productOfferingId;
    }

    /**
     * Getter for _oldProductOfferingId
     *
     * @return int
     **/
    public function getOldProductOfferingId()
    {
        return $this->oldProductOfferingId;
    }

    /**
     * Getter for _servicesToAdd
     *
     * @return array
     **/
    public function getServicesToAdd()
    {
        return $this->servicesToAdd;
    }

    /**
     * Getter for _componentIdsToRemove
     *
     * @return array
     **/
    public function getComponentIdsToRemove()
    {
        return $this->componentIdsToRemove;
    }

    /**
     * Getter for _newContractDuration
     *
     * @return int
     **/
    public function getNewContractDuration()
    {
        return $this->newContractDuration;
    }

    /**
     * Get the appropriate change channel based on the context from which
     * this action has  been run (instant account change or run from scheduled
     * account change script.
     *
     * @return string
     **/
    protected function getChangeChannel()
    {
        $registry = AccountChange_Registry::instance();
        $runFromScript = $registry->getEntry('runFromPerformScheduledAccountChange') ||
                         $registry->getEntry('runFromBBCRScript');

        // Default to script execution
        $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::EXECUTE_ACCOUNT_CHANGE;

        if (!$runFromScript) {
            $actor = $this->getCurrentBusinessActor();
            $scheduled = $registry->getEntry('bolSchedule');
            $bolVariantSwitchForWlrAddOrRemove = $registry->getEntry('bolVariantSwitchForWlrAddOrRemove');

            if (!empty($bolVariantSwitchForWlrAddOrRemove)) {
                $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE;

                // Change channel depends on whether we're in portal or Workplace account change
            } elseif (!empty($actor) && $actor->getUserType() == 'PLUSNET_STAFF') {
                if ($scheduled) {
                    $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST;
                } else {
                    $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE;
                }
            } else {
                $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_CUSTOMER_REQUEST;
                if (!$scheduled && (empty($actor) || (!empty($actor) && $actor->getUserType() != 'PLUSNET_ENDUSER'))) {
                    // We're running from a script or the account change api - allow immediate change
                    $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE;
                }
            }
        }

        // Modify the selected change channel if we're only recontracting..
        if (!$this->wlrChanging && !$this->adslChanging) {
            switch ($channel) {
                case \Plusnet\ProductChangePlanClient\ChangeChannels::EXECUTE_ACCOUNT_CHANGE:
                    $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::EXECUTE_ACCOUNT_CHANGE_RECONTRACT;
                    break;
                case \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE:
                    $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::IMMEDIATE_ACCOUNT_CHANGE_RECONTRACT;
                    break;
                case \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_CUSTOMER_REQUEST:
                case \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST:
                    $channel = \Plusnet\ProductChangePlanClient\ChangeChannels::SCHEDULE_ACCOUNT_CHANGE_RECONTRACT;
                    break;
            }
        }
        return $channel;
    }

    /**
     * Returns a Auth_BusinessActor object for current loged in user
     *
     * @return Auth_BusinessActor
     */
    protected function getCurrentBusinessActor()
    {
        $businessActor = false;

        $login = Auth_Auth::getCurrentLogin();
        if ($login instanceof Auth_Login) {
            $businessActor = $login->getBusinessActor();
        }

        return $businessActor;
    }
}
