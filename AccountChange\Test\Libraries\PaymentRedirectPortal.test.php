<?php
/**
 * PaymentRedirect Workplace Test
 *
 * Testing class for the AccountChange_PaymentRedirectPortal
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-07-25
 */
/**
 * Controller Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_PaymentRedirectPortalTest extends PHPUnit_Framework_TestCase
{
    /**
     * Reset database after each test
     *
     * @return  null
     */
    public function tearDown()
    {
        Db_Manager::rollback();
        Db_Manager::restoreAdaptor("GenericImmediatePaymentApplication");
        Db_Manager::restoreAdaptor("AccountChange");
    }

    /**
     * Test for AccountChange_PaymentRedirect::prepareRedirect
     *
     * @covers AccountChange_PaymentRedirect::prepareRedirect
     * @covers AccountChange_PaymentRedirect::getRedirectData
     * @covers AccountChange_PaymentRedirect::getDataForComplete
     * @covers AccountChange_PaymentRedirect::getApplicationStateVariable
     * @covers AccountChange_PaymentRedirect::getArrayDataForComplete
     *
     * @dataProvider prepareRedirectProvider
     */
    public function testPrepareRedirect($dataForComplete, $collectedData)
    {
        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getAccountIdByServiceId', 'getCustomerDetailsFromAccountId'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $accountId = $collectedData['objUserActor']->getExternalUserId();
        $mockDbAdaptor->expects($this->once())
                      ->method('getAccountIdByServiceId')
                      ->will($this->returnValue($accountId));

        $mockDbAdaptor->expects($this->once())
                      ->method('getCustomerDetailsFromAccountId')
                      ->with($this->equalTo($accountId))
                      ->will($this->returnValue(array(
                          'name'               => 'Foo Bar-san',
                          'email'              => 'foo@bar',
                          'mobilePhone'        => '',
                          'thoroughfareNumber' => '',
                          'thoroughfare'       => 'Boo Street',
                          'postTown'           => '',
                          'postCode'           => '',
                          'county'             => '',
                          'country'            => '',
                      )));

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $paymentHandOverId = 12;
        $validationHash = 'facfb055eda7796e05e00be89bcc29ac';

        $mockGimpDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('savePaymentRequestToDatabase', 'getLastInsertId'),
            array('GenericImmediatePaymentApplication', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockGimpDbAdaptor
            ->expects($this->once())
            ->method('savePaymentRequestToDatabase')
            ->with($validationHash, $this->callback(
                function ($encodedSerialisedObj) {
                    $obj = unserialize(base64_decode($encodedSerialisedObj));
                    $contactDetails = $obj->cardholderContactDetails;
                    $address = $obj->cardholderAddress;

                    PHPUnit_Framework_TestCase::assertEquals(
                        'foo@bar',
                        $contactDetails['email'],
                        'Payment request data does not have the expected email value set!'
                    );
                    PHPUnit_Framework_TestCase::assertEquals(
                        'Boo Street',
                        $address['thoroughfare'],
                        'Payment request data does not have the expected thoroughfare value set!'
                    );
                    PHPUnit_Framework_TestCase::assertEquals(
                        'Foo Bar-san',
                        $obj->cardholderName,
                        'Payment request data does not have the expected cardholder name value set!'
                    );
                    return true;
                }
            ));

        $mockGimpDbAdaptor
            ->expects($this->once())
            ->method('getLastInsertId')
            ->will($this->returnValue($paymentHandOverId));

        Db_Manager::setAdaptor('GenericImmediatePaymentApplication', $mockGimpDbAdaptor);

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        $reqList = $this->getMock(
            'Mvc_WizardReqList',
            array('getDataForComplete'),
            array('AccountChange', array('AccountChange_Payment'))
        );

        $reqList->expects($this->once())
                ->method('getDataForComplete')
                ->will($this->returnValue($dataForComplete));

        $controller->expects($this->at(0))
                   ->method('getApplicationStateVar')
                   ->will($this->returnValue($collectedData['objUserActor']));

        $controller->expects($this->at(1))
                   ->method('getApplicationStateVar')
                   ->will($this->returnValue($collectedData['objUserActor']));

        $invoices = array(
            array(
                'description'  => 'Outstanding Charge',
                'amount'       => 90,
                'gross'        => true,
                'handle'       => 'SUBSCRIPTION'
            )
        );

        $paymentRedirect = $this->getMock(
            'AccountChange_PaymentRedirectPortal',
            array('generateInvoiceItems', 'getPaymentRequestData'),
            array($controller, $reqList)
        );

        $paymentRedirect->expects($this->once())
                        ->method('generateInvoiceItems')
                        ->will($this->returnValue($invoices));

        $paymentRequest = $this->getMock(
            'GenericImmediatePaymentApplication_PaymentRequestData',
            array('generateValidationHash')
        );

        $paymentRequest->expects($this->once())
                       ->method('generateValidationHash')
                       ->will($this->returnValue($validationHash));

        $paymentRedirect->expects($this->once())
                       ->method('getPaymentRequestData')
                       ->will($this->returnValue($paymentRequest));

        $paymentRedirect->prepareRedirect();

        $redirectData = $paymentRedirect->getRedirectData();

        $userActor = $collectedData['objUserActor'];
        $targetActor = $userActor->getUsername().'@'.$userActor->getRealm();

        $exectedResult = array(
            'validationHash'    => $validationHash,
            'paymentHandoverId' => $paymentHandOverId,
            'targetActor'       => $targetActor
        );

        $this->assertEquals($exectedResult, $redirectData);
    }

    /**
     * Data provider for testPrepareRedirect
     *
     * @return array
     */
    public function prepareRedirectProvider()
    {
        $businessActor = new Auth_BusinessActor(null);
        $businessActor->setRealm('workplace.plus.net');
        $businessActor->setUserType('PLUSNET_STAFF');
        $loginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $loginMock->expects($this->any())
                  ->method('getBusinessActor')
                  ->will($this->returnValue($businessActor));

        $newServiceDefinition = $this->getMockBuilder(AccountChange_Product_ServiceDefinition::class)
            ->setConstructorArgs([2, true])
            ->getMock();
        $newServiceComponent  = new AccountChange_Product_ServiceComponent(2, true);
        $newConfiguration = new AccountChange_AccountConfiguration(
            array($newServiceDefinition),
            array($newServiceComponent)
        );

        $oldServiceDefinition =
            $this->getMockBuilder(AccountChange_Product_ServiceDefinition::class)
            ->setConstructorArgs([1, true])
            ->getMock();

        $oldServiceComponent  = new AccountChange_Product_ServiceComponent(1, true);

        $oldConfiguration = new AccountChange_AccountConfiguration(
            array($oldServiceDefinition),
            array($oldServiceComponent)
        );

        $actionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array(),
            array(1234)
        );

        $accountChangeManager = new AccountChange_Manager(
            1234,
            $oldConfiguration,
            $newConfiguration,
            $actionManager
        );

        $userActor = new Auth_BusinessActor(null);
        $userActor->setRealm('portal.plus.net');
        $userActor->setUsername('test');
        $userActor->setActorId(12);
        $userActor->setExternalUserId(1234);

        $dataForComplete = array(
            'objAccountChangeManager' => $accountChangeManager,
        );

        $collectedData = array(
            'objBusinessActor' => $businessActor,
            'objUserActor'     => $userActor
        );

        $data = array();
        // For plusnet staff
        $data[] = array($dataForComplete, $collectedData);

        $businessActor = new Auth_BusinessActor(null);
        $businessActor->setRealm('workplace.plus.net');
        $businessActor->setUserType('PLUSNET_END_USER');
        $loginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $loginMock->expects($this->any())
                  ->method('getBusinessActor')
                  ->will($this->returnValue($businessActor));

        $collectedData['objBusinessActor'] = $businessActor;
        $collectedData['objDirectDebitDetails'] = null;

        //For plusnet End User
        $data[] = array($dataForComplete, $collectedData);

        return $data;
    }

    /**
     * Check whether the decorator specified is not changed
     *
     * @covers AccountChange_PaymentRedirectPortal::getDecorator
     */
    public function testGetDecorator()
    {
        $controller = $this->getMock(
            'AccountChange_Controller',
            array()
        );

        $reqList = $this->getMock(
            'Mvc_WizardReqList',
            array(),
            array('AccountChange', array('AccountChange_Payment'))
        );

        $paymentRedirectPortal = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_PaymentRedirectPortal',
            array($controller, $reqList)
        );

        $decorator = $paymentRedirectPortal->protected_getDecorator();

        $expected = 'Mvc_PortalDecorator';

        $this->assertEquals($expected, $decorator);
    }

    /**
     * Check whether the return view specified is not changed
     *
     * @covers AccountChange_PaymentRedirectPortal::getReturnView
     */
    public function testGetReturnView()
    {
        $controller = $this->getMock(
            'AccountChange_Controller',
            array()
        );

        $reqList = $this->getMock(
            'Mvc_WizardReqList',
            array(),
            array('AccountChange', array('AccountChange_Payment'))
        );

        $paymentRedirectPortal = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_PaymentRedirectPortal',
            array($controller, $reqList)
        );

        $decorator = $paymentRedirectPortal->protected_getReturnView();

        $expected = 'PaymentRedirectPortal';

        $this->assertEquals($expected, $decorator);
    }
}
