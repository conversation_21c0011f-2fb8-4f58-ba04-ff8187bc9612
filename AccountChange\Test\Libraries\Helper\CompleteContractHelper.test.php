<?php
/**
 * <AUTHOR> <Reece<PERSON><PERSON><PERSON><EMAIL>>
 */
use Plusnet\ContractsClient\Entity\Contract;

class AccountChange_CompleteContractHelperTest extends PHPUnit_Framework_TestCase
{

    /**
     * @param array  $expectedArray        expected return
     * @param string $contractCreation     contract creation reason
     * @param string $startDate            start date
     * @param string $status               status
     * @param int    $contractCalled       Number of times contracts will be called
     * @param int    $contractReasonCalled Number of times contract reason will be called
     * @dataProvider getDataForAutoContracts
     */
    public function testGetContractDetailsForEmail(
        $expectedArray,
        $contractCreation,
        $startDate,
        $status = 'ACTIVE',
        $contractCalled = 1,
        $contractReasonCalled = 1
    ) {
        $mockProductFamily = $this->getProductFamilyMock(true, true);
        $serviceId = 1234;

        //Contract
        $mockContract = $this->getMock(
            Contract::class,
            array("getStatus",
                "getStartDate",
                "getC<PERSON>",
                "getDuration",
                "getEndDate"),
            array(),
            "",
            false
        );

        $mockContract->expects($this->once())
            ->method("getStatus")
            ->will($this->returnValue($status));

        $mockContract->expects($this->exactly($contractCalled))
            ->method("getStartDate")
            ->will($this->returnValue($startDate));

        $mockContract->expects($this->exactly($contractReasonCalled))
            ->method("getCreationReason")
            ->will($this->returnValue($contractCreation));

        $mockContract->expects($this->exactly($contractCalled))
            ->method("getDuration")
            ->will($this->returnValue(["value" => 12]));

        $mockContract->expects($this->exactly($contractCalled))
            ->method("getEndDate")
            ->will($this->returnValue("05-01-2023"));

        $this->setUpContractClient($mockContract);

        $mockHelper = $this->getMock(
            "AccountChange_CompleteContractHelper",
            array("getTodaysDate"),
            array($serviceId, $mockProductFamily),
            "",
            true
        );

        $mockHelper->expects($this->exactly($contractCalled))
            ->method('getTodaysDate')
            ->will($this->returnValue(I18n_Date::fromString("05-01-2022")));

        $returnedArray = $mockHelper->getContractDetailsForEmail();
        $this->assertEquals($expectedArray, $returnedArray);
    }

    public function getDataForAutoContracts()
    {
        return [
            [
                [
                    "isContractRetained" => false,
                    "intContractLengthInMonths" => 12,
                    "strContractEndDate" => "05-01-2023",
                    "isNewProductDualPlay" => true
                ],
                "ACCOUNT_CHANGE",
                "05-01-2022"
            ],
            [
                [
                    "isContractRetained" => true,
                    "intContractLengthInMonths" => 12,
                    "strContractEndDate" => "05-01-2023",
                    "isNewProductDualPlay" => true
                ],
                "ACCOUNT_CHANGE",
                "04-01-2022",
                "ACTIVE",
                1,
                0
            ],
            [
                [
                    "isContractRetained" => true,
                    "intContractLengthInMonths" => 12,
                    "strContractEndDate" => "05-01-2023",
                    "isNewProductDualPlay" => true
                ],
                "SIGNUP",
                "05-01-2022"
            ],
            [
                [
                    "isNewProductDualPlay" => true
                ],
                "SIGNUP",
                "05-01-2022",
                "PENDING",
                0,
                0
            ]
        ];
    }

    private function getProductFamilyMock($autoContract, $dualPlay)
    {
        $mockProductFamily = $this->getMock(
            "ProductFamily_Generic",
            array("isDualPlay"),
            array(),
            "",
            false
        );

        $mockProductFamily->expects($this->once())
            ->method('isDualPlay')
            ->will($this->returnValue($dualPlay));

        return $mockProductFamily;
    }

    private function setUpContractClient($contract)
    {
        $mockContractClient = $this->getMock(
            "\Plusnet\ContractsClient\Client",
            array("getContract",
                "setServiceId"),
            array(),
            "",
            false
        );

        $mockContractClient->expects($this->once())
            ->method("getContract")
            ->will($this->returnValue($contract));

        BusTier_BusTier::setClient("contracts", $mockContractClient);
    }
}
