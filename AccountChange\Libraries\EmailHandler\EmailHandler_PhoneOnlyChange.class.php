<?php
/**
 * AccountChange_EmailHandler_PhoneOnlyChange
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 */

class AccountChange_EmailHandler_PhoneOnlyChange extends AccountChange_EmailHandler
{
    /**
     * @var string
     */
    private $strProduct;

    /**
     * @var string
     */
    private $strOldProductName;

    /**
     * @var string
     */
    private $strContractLength = 'Monthly';

    /**
     * @var int
     */
    private $newMonthlyCost;

    /**
     * @var string
     */
    private $uxtStartDate = null;

    /**
     * AccountChange_EmailHandler_PhoneOnlyChange constructor.
     * @param AccountChange_PerformChangeApi         $performChangeApi perform account change api
     * @param AccountChange_AccountChangeOrder       $order            order
     * @param AccountChange_Account                  $account          account
     * @param AccountChange_EmailHandler_PriceHelper $priceHelper      price helper
     * @param AccountChange_EmailHandler_DataHelper  $dataHelper       data helper
     */
    public function __construct(
        \AccountChange_PerformChangeApi   $performChangeApi,
        \AccountChange_AccountChangeOrder $order,
        AccountChange_Account $account,
        AccountChange_EmailHandler_PriceHelper $priceHelper,
        AccountChange_EmailHandler_DataHelper $dataHelper
    ) {
        $currentWlrData = $account->getWlrInformation();
        $this->setStrProduct($order, $dataHelper);
        $this->setStrOldProductName($currentWlrData, $dataHelper);
        $this->setNewMonthlyCost($priceHelper->getNewPackagePrices());
    }

    /**
     * @param string $newMonthlyCost monthly cost
     * @return void
     */
    private function setNewMonthlyCost($monthlyCost)
    {
        $newMonthlyCost = $monthlyCost['total'] - $monthlyCost['broadband'];

        $this->newMonthlyCost = number_format($newMonthlyCost, 2);
    }

    /**
     * @param AccountChange_AccountChangeOrder      $order          order
     * @param AccountChange_EmailHandler_DataHelper $dataHelper     data helper
     * @return void
     */
    private function setStrProduct($order, $dataHelper)
    {
        $newPhoneComponentId = $order->getProducts()->getPhoneComponentId();
        $callPlanName = null;
        if (!empty($newPhoneComponentId)) {
            $callPlan = $dataHelper->getCallPlanDetailsByWlrServiceComponentId($newPhoneComponentId);
            $callPlanName = $callPlan['strDisplayName'];
        }
        $this->strProduct = $callPlanName;
    }

    /**
     * @param array                                 $currentWlrData array of wlr data
     * @param AccountChange_EmailHandler_DataHelper $dataHelper     data helper
     * @return void
     */
    private function setStrOldProductName($currentWlrData, $dataHelper)
    {
        $oldCallPlanName = null;
        if (isset($this->currentWlrData['intOldWlrId'])) {
            $oldPhoneComponentId = $currentWlrData['intOldWlrId'];
            $oldCallPlan = $dataHelper->getCallPlanDetailsByWlrServiceComponentId($oldPhoneComponentId);
            $oldCallPlanName = $oldCallPlan['strDisplayName'];
            $this->setStrOldProductName($oldCallPlanName);
        }
        $this->strOldProductName = $oldCallPlanName;
    }


    /**
     * Tranfers local variables into an array ready to be used
     *
     * @return array email variables
     */
    protected function populateEmailVariables()
    {
        return array(
            "strProduct" => $this->getStrProduct(),
            "strOldProductName" => $this->getStrOldProductName(),
            "strContractLength" => $this->formatForMsn($this->getStrContractLength()),
            "monthlyCost" => $this->getNewMonthlyCost(),
            "uxtStartDate" => $this->getUxtStartDate(),
        );
    }

    /**
     * @param mixed $var variable
     * @return string|int
     */
    private function formatForMsn($var)
    {
        if (is_null($var)) {
            return 0;
        }
        if (is_bool($var)) {
            return $var ? 1 : 0;
        }
        return $var;
    }

    /**
     * @return string
     */
    public function getEmailName()
    {
        return 'CUSTOMER_PHONE_CHANGE';
    }

    /**
     * @return string
     */
    public function getStrProduct()
    {
        return $this->strProduct;
    }

    /**
     * @return string
     */
    public function getStrOldProductName()
    {
        return $this->strOldProductName;
    }

    /**
     * @return int
     */
    public function getStrContractLength()
    {
        return $this->strContractLength;
    }

    /**
     * @return int
     */
    public function getNewMonthlyCost()
    {
        return $this->newMonthlyCost;
    }

    /**
     * @return string
     */
    public function getUxtStartDate()
    {
        return $this->uxtStartDate = '';
    }

    /**
     * @param int $serviceId serviceId
     * @return void
     */
    public function sendEmail($serviceId)
    {
        $this->sendEmailMSN($serviceId, $this->getEmailName(), $this->populateEmailVariables(), 'PHONE_CONFIRMATION');
    }

}
