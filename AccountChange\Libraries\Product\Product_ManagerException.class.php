<?php
/**
 * Account Change Product Manager Exception
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_ManagerException.class.php,v 1.3 2009-02-17 04:41:14 rmerewood Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Account Change Product Manager exception class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_ManagerException extends Exception
{
    /**
     * The action passed is not a valid action that can be performed on the product
     *
     */
    const ERR_INVALID_ACTION       = 1;

    /**
     * Invalid product Id passed
     *
     */
    const ERR_INVALID_PRODUCT_ID   = 2;

    /**
     * Invalid product type that was passed to the factory
     *
     */
    const ERR_INVALID_PRODUCT_TYPE = 3;

    /**
     * The product configuration class that was specified does not exist
     *
     */
    const ERR_PRODUCT_CONFIGURATION_CLASS_NOT_EXIST = 4;

    /**
     * The product configuration object is not a valid configuration
     *
     */
    const ERR_INVALID_PRODUCT_CONFIGURATION_CLASS = 5;

    /**
     * The new service definition id is needed in order for the configuration to be actioned upon
     *
     */
    const ERR_NEW_SERVICE_DEFINITION_ID_NOT_PRESENT = 6;

    /**
     * Service Definition|Component Id not in a valid format (basically int)
     *
     */
    const ERR_INVALID_COMPONENT_ID_TYPE = 7;

    /**
     * For a product change to take place for Wlr, you need to have the new and old product
     * component id's set
     *
     */
    const ERR_WLR_PRODUCT_CHANGE_INVALID_DUE_TO_COMPONENT_ID_NOT_SET = 8;

    /**
     * The component type Id is not set in the component details array
     *
     */
    const ERR_COMPONENT_TYPE_ID_NOT_SET = 9;

    /**
     * Something went wrong with the Cli number, either invalid or not present
     *
     */
    const ERR_CLI_ERROR = 10;

    /**
     * In order to progress with certain changes to the product configuration we need
     * a service component contract
     *
     */
    const ERR_MISSING_SERVICE_COMPONENT_CONTRACT = 11;

    /**
     * New Value components are created based on linecheck performed before.
     */
    const ERR_LINECHECK_ID_NOT_PRESENT = 12;
}
