<?xml version="1.0" encoding="UTF-8"?>
<project name="maven-antrun-" default="quality-metrics">
  <target unless="skipExecution" name="quality-metrics">
    <mkdir dir="C:\local\codebase2005\modules\AutomatedEmail\target/logs" />
    <echo>Execution of phpcs</echo>
    <echo></echo>
    <exec dir="C:\local\codebase2005\modules\AutomatedEmail/" executable="php" logerror="on">
      <arg line="-d memory_limit=128M" />
      <arg line="/usr/bin/phpcs" />
      <arg line="-p" />
      <arg line="--extensions=php,html,inc,php3,ihtml,shtml,phtml" />
      <arg line="--standard=Plusnet" />
      <arg line="--report=checkstyle" />
      <arg line="--ignore=vendor/" />
      <arg line="--ignore=target/" />
      <arg line="--ignore=var/cache/" />
      <arg line="--report-file=C:\local\codebase2005\modules\AutomatedEmail\target/logs/codesniffer.xml" />
      <arg line="." />
    </exec>
    <echo>-------------</echo>
    <echo></echo>
    <echo>Execution of pdepend</echo>
    <echo></echo>
    <exec dir="C:\local\codebase2005\modules\AutomatedEmail/" executable="php" logerror="on">
      <arg line="-dmemory_limit=128M" />
      <arg line="/usr/bin/pdepend" />
      <arg line="--summary-xml=C:\local\codebase2005\modules\AutomatedEmail\target/logs/summary.xml" />
      <arg line="--jdepend-xml=C:\local\codebase2005\modules\AutomatedEmail\target/logs/jdepend.xml" />
      <arg line="--jdepend-chart=C:\local\codebase2005\modules\AutomatedEmail\target/logs/dependencies.svg" />
      <arg line="--overview-pyramid=C:\local\codebase2005\modules\AutomatedEmail\target/logs/software-metrics-pyramid.svg" />
      <arg line="--ignore=vendor/,target/,var/cache/" />
      <arg line="." />
    </exec>
    <echo>-------------</echo>
    <echo></echo>
    <echo>Execution of phpmd</echo>
    <echo></echo>
    <exec dir="C:\local\codebase2005\modules\AutomatedEmail/" executable="php" logerror="on">
      <arg line="-dmemory_limit=256M" />
      <arg line="/usr/bin/phpmd" />
      <arg line=". xml codesize,unusedcode" />
      <arg line="--reportfile C:\local\codebase2005\modules\AutomatedEmail\target/logs/pmd.xml" />
      <arg line="--exclude vendor/,target/,var/cache/" />
    </exec>
    <echo>-------------</echo>
    <echo></echo>
    <echo>Execution of phploc</echo>
    <echo></echo>
    <exec dir="C:\local\codebase2005\modules\AutomatedEmail/" executable="php" logerror="on">
      <arg line="-dmemory_limit=128M" />
      <arg line="/usr/bin/phploc" />
      <arg line="--log-csv C:\local\codebase2005\modules\AutomatedEmail\target/logs/phploc.csv" />
      <arg line="--exclude vendor/" />
      <arg line="--exclude target/" />
      <arg line="--exclude var/cache/" />
      <arg line="." />
    </exec>
    <echo>-------------</echo>
    <echo></echo>
    <echo>Execution of phpcpd</echo>
    <echo></echo>
    <exec dir="C:\local\codebase2005\modules\AutomatedEmail/" executable="php" logerror="on">
      <arg line="-dmemory_limit=128M" />
      <arg line="/usr/bin/phpcpd" />
      <arg line="--log-pmd C:\local\codebase2005\modules\AutomatedEmail\target/logs/pmd-cpd.xml" />
      <arg line="--exclude vendor/" />
      <arg line="--exclude target/" />
      <arg line="--exclude var/cache/" />
      <arg line="." />
    </exec>
    <echo>-------------</echo>
    <echo></echo>
  </target>
</project>
