<?php

/***
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_AccountChangeAddress
{
    /**
     * @var string
     */
    private $addressCategory;

    /**
     * @var string
     */
    private $addressReference;

    /**
     * @var string
     */
    private $country;

    /**
     * @var string
     */
    private $county;

    /**
     * @var string
     */
    private $cssDatabaseCode;

    /**
     * @var string
     */
    private $organisationName;

    /**
     * @var string
     */
    private $poBox;

    /**
     * @var string
     */
    private $postTown;

    /**
     * @var string
     */
    private $postCode;

    /**
     * @var string
     */
    private $premisesName;

    /**
     * @var string
     */
    private $subPremises;

    /**
     * @var string
     */
    private $thoroughfareName;

    /**
     * @var string
     */
    private $thoroughfareNumber;

    /**
     * @var string
     */
    private $buildingName;

    /**
     * @return string
     */
    public function getAddressCategory()
    {
        return $this->addressCategory;
    }

    /**
     * @param string $addressCategor<PERSON> Address Category
     * @return void
     */
    public function setAddressCategory($addressCategory)
    {
        $this->addressCategory = $addressCategory;
    }

    /**
     * @return string
     */
    public function getAddressReference()
    {
        return $this->addressReference;
    }

    /**
     * @param string $addressReference Address Reference
     * @return void
     */
    public function setAddressReference($addressReference)
    {
        $this->addressReference = $addressReference;
    }

    /**
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param string $country Country
     * @return void
     */
    public function setCountry($country)
    {
        $this->country = $country;
    }

    /**
     * @return string
     */
    public function getCounty()
    {
        return $this->county;
    }

    /**
     * @param string $county County
     * @return void
     */
    public function setCounty($county)
    {
        $this->county = $county;
    }

    /**
     * @return string
     */
    public function getCssDatabaseCode()
    {
        return $this->cssDatabaseCode;
    }

    /**
     * @param string $cssDatabaseCode DB Code
     * @return void
     */
    public function setCssDatabaseCode($cssDatabaseCode)
    {
        $this->cssDatabaseCode = $cssDatabaseCode;
    }

    /**
     * @return string
     */
    public function getOrganisationName()
    {
        return $this->organisationName;
    }

    /**
     * @param string $organisationName Org Name
     * @return void
     */
    public function setOrganisationName($organisationName)
    {
        $this->organisationName = $organisationName;
    }

    /**
     * @return string
     */
    public function getPoBox()
    {
        return $this->poBox;
    }

    /**
     * @param string $poBox pobox
     * @return void
     */
    public function setPoBox($poBox)
    {
        $this->poBox = $poBox;
    }

    /**
     * @return string
     */
    public function getPostTown()
    {
        return $this->postTown;
    }

    /**
     * @param string $postTown Post Town
     * @return void
     */
    public function setPostTown($postTown)
    {
        $this->postTown = $postTown;
    }

    /**
     * @return string
     */
    public function getPostCode()
    {
        return $this->postCode;
    }

    /**
     * @param string $postCode Pose Code
     * @return void
     */
    public function setPostCode($postCode)
    {
        $this->postCode = $postCode;
    }

    /**
     * @return string
     */
    public function getPremisesName()
    {
        return $this->premisesName;
    }

    /**
     * @param string $premisesName Premises Name
     * @return void
     */
    public function setPremisesName($premisesName)
    {
        $this->premisesName = $premisesName;
    }

    /**
     * @return string
     */
    public function getSubPremises()
    {
        return $this->subPremises;
    }

    /**
     * @param string $subPremises Sub Premises
     * @return void
     */
    public function setSubPremises($subPremises)
    {
        $this->subPremises = $subPremises;
    }

    /**
     * @return string
     */
    public function getThoroughfareName()
    {
        return $this->thoroughfareName;
    }

    /**
     * @param string $thoroughfareName Thoroughfare name
     * @return void
     */
    public function setThoroughfareName($thoroughfareName)
    {
        $this->thoroughfareName = $thoroughfareName;
    }

    /**
     * @return string
     */
    public function getThoroughfareNumber()
    {
        return $this->thoroughfareNumber;
    }

    /**
     * @param string $thoroughfareNumber Thoroughfare number
     * @return void
     */
    public function setThoroughfareNumber($thoroughfareNumber)
    {
        $this->thoroughfareNumber = $thoroughfareNumber;
    }

    /**
     * @return string
     */
    public function getBuildingName()
    {
        return $this->buildingName;
    }

    /**
     * @param string $buildingName Building name
     * @return void
     */
    public function setBuildingName($buildingName)
    {
        $this->buildingName = $buildingName;
    }

    /**
     * @return array
     */
    public function getAddressArray()
    {
        return array(
            "addressCategory" => $this->addressCategory,
            "addressReference" => $this->addressReference,
            "country" => $this->country,
            "county" => $this->county,
            "cssDatabaseCode" => $this->cssDatabaseCode,
            "organisationName" => $this->organisationName,
            "poBox" => $this->poBox,
            "postTown" => $this->postTown,
            "postCode" => $this->postCode,
            "premisesName" => $this->premisesName,
            "subPremises" => $this->subPremises,
            "thoroughfareName" => $this->thoroughfareName,
            "thoroughfareNumber" => $this->thoroughfareNumber,
            "buildingName" => $this->buildingName
        );
    }

    /**
     * @param string $serviceId service id
     * @throws Db_TransactionException
     * @return void
     */
    public function saveAddressRefAndDatabaseCode($serviceId)
    {
        if (!is_null($this->getCssDatabaseCode()) || !is_null($this->getAddressReference())) {
            $this->saveAddress($serviceId);
        }
    }

    protected function saveAddress($serviceId)
    {
        $this->includeLegacyFiles();

        $service = userdata_service_get($serviceId);
        $userData = userdata_user_get_full($service['user_id']);
        userdata_address_alter(
            $userData['address_id'],
            $this->getBuildingName(),
            $this->getThoroughfareName(),
            $this->getPostTown(),
            $this->getCounty(),
            $this->getPostcode(),
            $this->getCountry(),
            $this->getAddressReference(),
            $this->getCssDatabaseCode()
        );
    }

    /**
     * Inclusion of legacy files so we can mock them
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once "/local/data/mis/database/standard_include.inc";
        require_once "/local/www/database-admin/config/config.inc";
        require_once "/local/data/mis/database/database_libraries/userdata-access.inc";
    }

    /**
     * @return boolean
     */
    public function isValid()
    {
        return !is_null($this->buildingName)
            && !is_null($this->postTown)
            && !is_null($this->postCode)
            && !is_null($this->country)
            && !is_null($this->addressReference)
            && !is_null($this->cssDatabaseCode);
    }
}
