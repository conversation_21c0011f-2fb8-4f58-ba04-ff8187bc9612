<?php

/**
 * <AUTHOR>
 */
class StoreDirectDebitTest extends PHPUnit_Framework_TestCase
{
    private $applicationData = [
        'intDirectDebitAccountNumber' => '********',
        'intDirectDebitSortCode1' => '01',
        'intDirectDebitSortCode2' => '01',
        'intDirectDebitSortCode3' => '01',
        'strDirectDebitName' => 'test name'
    ];

    private $gimpDirectDebitDetails = [
        'directDebitFirstName' => 'test name',
        'directDebitSurname' => '',
        'directDebitSortCode' => '010101',
        'directDebitAccountNumber' => '********'
    ];

    /**
     * @return void
     */
    public function testDirectDebitDetailsArePassedToGimpCorrectlyAfterRbmMigrationComplete()
    {
        $serviceId = '12345';
        $isp = 'Plusnet';

        $directDebitHelper = $this->getDirectDebitPaymentHelper();

        $directDebitHelper
            ->expects($this->once())
            ->method('registerDirectDebitInBillingEngine')
            ->with($serviceId, $this->gimpDirectDebitDetails);

        $storeDirectDebit = $this->getStoreDirectDebit();
        $storeDirectDebit->method('getDirectDebitPaymentHelper')->willReturn($directDebitHelper);
        $storeDirectDebit->expects($this->never())->method('storeDirectDebitDetailsLegacy');

        $storeDirectDebit->execute($serviceId, $isp, $this->applicationData);
    }

    /**
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getDbAdaptor()
    {
        $dbAdaptor = $this
            ->getMockBuilder('Db_Adaptor')
            ->disableOriginalConstructor()
            ->setMethods(['getAccountIdByServiceId'])
            ->getMock();
        $dbAdaptor
            ->method('getAccountIdByServiceId')
            ->willReturn('54321');

        return $dbAdaptor;
    }

    /**
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getStoreDirectDebit()
    {
        return $this
            ->getMockBuilder('AccountChange_StoreDirectDebit')
            ->setConstructorArgs([$this->getDbAdaptor()])
            ->setMethods([
                'getDirectDebitPaymentHelper',
                'storeDirectDebitDetailsLegacy'
            ])->getMock();
    }

    /**
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getDirectDebitPaymentHelper()
    {
        return $this
            ->getMockBuilder('GenericImmediatePaymentApplication_DirectDebitPaymentHelper')
            ->disableOriginalConstructor()
            ->setMethods(['registerDirectDebitInBillingEngine'])
            ->getMock();
    }
}
