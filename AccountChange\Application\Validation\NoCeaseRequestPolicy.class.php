<?php

/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_NoCeaseRequestPolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE = 'You cannot make changes to your account as you have an active cease request.';
    const ERROR_CODE = 'ERROR_CUSTOMER_HAS_CEASE_REQUEST';

    /** @var int $serviceId */
    private $serviceId;

    /**
     * @param Auth_BusinessActor $actor                 Business Actor
     * @param bool               $isWorkplace           is workplace flag
     * @param bool               $isScript              is script flag
     * @param array              $additionalInformation extra params
     */
    public function __construct(
        Auth_BusinessActor $actor,
        $isWorkplace = false,
        $isScript = false,
        $additionalInformation = array()
    ) {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);

        $this->serviceId = $this->additionalInformation['serviceId'];
    }

    /**
     * @return bool
     */
    public function validate()
    {
        return empty($this->getCeaseData()->getUnsolicitedCeaseId());
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return static::ERROR_MESSAGE;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return static::ERROR_CODE;
    }

    /**
     * @return CeaseCommunications_UnsolicitedCease
     */
    private function getCeaseData()
    {
        $cease = $this->getCeaseObject();
        $cease->loadUsingServiceId($this->serviceId);

        return $cease;
    }

    /**
     * @return CeaseCommunications_UnsolicitedCease
     */
    protected function getCeaseObject()
    {
        return new CeaseCommunications_UnsolicitedCease();
    }
}
