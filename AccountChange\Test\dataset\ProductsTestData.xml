<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE dataset SYSTEM "dataset.dtd">
<dataset>
    <table name="products.tblCustomerSector">
        <column>intCustomerSectorID</column>
        <column>vchHandle</column>
        <column>vchDisplayName</column>
        <column>stmLastUpdate</column>
        <row>
            <value>1</value>
            <value>CONSUMER</value>
            <value>Consumer</value>
            <value>2005-09-21 19:53:30</value>
        </row>
        <row>
            <value>2</value>
            <value>BUSINESS</value>
            <value>Business</value>
            <value>2005-09-21 19:53:30</value>
        </row>
        <row>
            <value>3</value>
            <value>STAFF</value>
            <value>Staff</value>
            <value>2005-09-21 19:53:30</value>
        </row>
    </table>
    <table name="products.service_components">
        <column>service_component_id</column>
        <column>name</column>
        <column>description</column>
        <column>available</column>
        <column>date_created</column>
        <column>dteAvailableFrom</column>
        <column>dteAvailableTo</column>
        <column>bolSignupBoltOn</column>
        <column>bolInLifeBoltOn</column>
        <column>bolScheduledCancellation</column>
        <column>db_src</column>
        <column>time_stamp</column>
        <column>isp</column>
        <row>
            <value>3</value>
            <value>test3</value>
            <value>test3</value>
            <value>Yes</value>
            <value>2001-02-03</value>
            <value>2001-02-03</value>
            <value>2050-01-01</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>zaz</value>
            <value>2001-02-03 01:02:03</value>
            <value>generic</value>
        </row>
        <row>
            <value>7</value>
            <value>test7</value>
            <value>test7</value>
            <value>Yes</value>
            <value>2001-02-03</value>
            <value>2001-02-03</value>
            <value>2050-01-01</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>zaz</value>
            <value>2001-02-03 01:02:03</value>
            <value>generic</value>
        </row>
        <row>
            <value>8</value>
            <value>test8</value>
            <value>test8</value>
            <value>Yes</value>
            <value>2001-02-03</value>
            <value>2001-02-03</value>
            <value>2050-01-01</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>zaz</value>
            <value>2001-02-03 01:02:03</value>
            <value>generic</value>
        </row>
        <row>
            <value>00000995</value>
            <value>JohnLewisEWCalls</value>
            <value>JohnLewisEWCalls Home Phone product</value>
            <value>Yes</value>
            <value>2011-08-18</value>
            <value>2011-08-18</value>
            <value>2050-01-01</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>sas</value>
            <value>2011-09-02 12:08:55</value>
            <value>generic</value>
        </row>
        <row>
            <value>00000996</value>
            <value>JohnLewisAnyTimeInternationalCalls</value>
            <value>JohnLewisAnyTimeInternationalCalls Home Phone prod</value>
            <value>Yes</value>
            <value>2011-08-18</value>
            <value>2011-08-18</value>
            <value>2050-01-01</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>sas</value>
            <value>2011-09-02 12:08:55</value>
            <value>generic</value>
        </row>
        <row>
            <value>00000997</value>
            <value>JohnLewisAnyTimeCalls</value>
            <value>JohnLewisAnyTimeCalls Home Phone product</value>
            <value>Yes</value>
            <value>2011-08-18</value>
            <value>2011-08-18</value>
            <value>2050-01-01</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>sas</value>
            <value>2011-09-02 12:08:55</value>
            <value>generic</value>
        </row>
    </table>
    <table name="products.tblServiceComponentProductType">
        <column>intServiceComponentProductTypeID</column>
        <column>vchHandle</column>
        <column>vchDescription</column>
        <column>stmLastUpdate</column>
        <row>
            <value>1</value>
            <value>PLUSTALK</value>
            <value>PlusTalk Product Bundles</value>
            <value>2005-09-21 19:53:30</value>
        </row>
        <row>
            <value>2</value>
            <value>METRONET_EMAIL</value>
            <value>Metronet Email Product</value>
            <value>2006-02-15 01:41:37</value>
        </row>
        <row>
            <value>3</value>
            <value>ADD_ON_PRODUCT_BUNDLE</value>
            <value>Handler for multiple billable component bundles</value>
            <value>2006-05-31 00:38:31</value>
        </row>
        <row>
            <value>4</value>
            <value>WLR</value>
            <value>Home Phone Product Bundles</value>
            <value>2006-07-03 23:58:09</value>
        </row>
        <row>
            <value>5</value>
            <value>MAAF_WEBMAIL</value>
            <value>Madasafish Webmail</value>
            <value>2007-12-03 01:42:00</value>
        </row>
        <row>
            <value>6</value>
            <value>INTERNET_CONNECTION</value>
            <value>Internet Connection Product Bundles</value>
            <value>2007-12-03 01:46:47</value>
        </row>
    </table>
    <table name="products.tblServiceComponentProduct">
        <column>intServiceComponentProductID</column>
        <column>intServiceComponentProductTypeID</column>
        <column>intServiceComponentID</column>
        <column>intCustomerSectorID</column>
        <column>stmLastUpdate</column>
        <row>
            <value>1</value>
            <value>4</value>
            <value>3</value>
            <value>1</value>
            <value>2001-02-03 01:02:03</value>
        </row>
        <row>
            <value>2</value>
            <value>4</value>
            <value>7</value>
            <value>1</value>
            <value>2001-02-03 01:02:03</value>
        </row>
        <row>
            <value>3</value>
            <value>4</value>
            <value>8</value>
            <value>1</value>
            <value>2001-02-03 01:02:03</value>
        </row>
        <row>
            <value>357</value>
            <value>4</value>
            <value>995</value>
            <value>1</value>
            <value>2011-08-30 12:39:03</value>
        </row>
        <row>
            <value>358</value>
            <value>4</value>
            <value>996</value>
            <value>1</value>
            <value>2011-08-30 12:39:10</value>
        </row>
        <row>
            <value>359</value>
            <value>4</value>
            <value>997</value>
            <value>1</value>
            <value>2011-08-30 12:39:15</value>
        </row>
    </table>
    <table name="products.service_component_config">
        <column>service_component_config_id</column>
        <column>service_definition_id</column>
        <column>service_component_id</column>
        <column>points_value</column>
        <column>free_quantity</column>
        <column>default_quantity</column>
        <column>max_quantity</column>
        <column>bolAvailableInSignup</column>
        <column>db_src</column>
        <row>
            <value>00000001</value>
            <value>00000003</value>
            <value>00000003</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>zaz</value>
        </row>
        <row>
            <value>00000002</value>
            <value>00000003</value>
            <value>00000007</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>zaz</value>
        </row>
        <row>
            <value>00000003</value>
            <value>00000003</value>
            <value>00000008</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>zaz</value>
        </row>
        <row>
            <value>00365379</value>
            <value>00006770</value>
            <value>00000995</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365380</value>
            <value>00006771</value>
            <value>00000995</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365381</value>
            <value>00006772</value>
            <value>00000995</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365382</value>
            <value>00006770</value>
            <value>00000996</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365383</value>
            <value>00006771</value>
            <value>00000996</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365384</value>
            <value>00006772</value>
            <value>00000996</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365385</value>
            <value>00006770</value>
            <value>00000997</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365386</value>
            <value>00006771</value>
            <value>00000997</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
        <row>
            <value>00365387</value>
            <value>00006772</value>
            <value>00000997</value>
            <value>0</value>
            <value>0</value>
            <value>0</value>
            <value>1</value>
            <value>0</value>
            <value>sas</value>
        </row>
    </table>
</dataset>