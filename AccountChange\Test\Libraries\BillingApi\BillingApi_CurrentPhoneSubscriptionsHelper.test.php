<?php

use Plusnet\BillingApiClient\Entity\CustomerProductSubscription;

class AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper_Test extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 1234;
    const LINE_RENTAL = 'Line Rental';
    const LINE_RENTAL_SAVER = 'Line Rental Saver';
    const CALL_PLAN = 'Call Plan';
    const CALL_PLAN_NAME = 'Anytime';
    const BROADBAND = 'Broadband';

    /**
     * Tests that if we have a current call plan and line rental without line rental saver, they are returned
     *
     * @return void
     */
    public function testGetsLineRentalAndCallPlanWhen2CurrentPhoneSubsAndNoLineRentalSaver()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(20.99)
            ->setCurrentSubscriptionPriceInPounds(18.99);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5);

        $subscriptionBroadband = new CustomerProductSubscription();
        $subscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(15)
            ->setCurrentSubscriptionPriceInPounds(9.99);

        $subs = array(
            $subscriptionBroadband,
            $subscriptionLineRental,
            $subscriptionCallPlan
        );

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentCallPlanSubscription());
        $this->assertEquals(20.99, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(18.99, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentSubscriptionPriceInPounds());
        $this->assertEquals(6, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(5, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentSubscriptionPriceInPounds());
    }

    /**
     * Tests that if we have a current call plan and line rental with line rental saver, the correct
     * (zero) line rental and call plan are returned
     *
     * @return void
     */
    public function testGetsLineRentalAndCallPlanWhenCurrentPhoneSubsAndLineRentalSaver()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRentalSaver = new CustomerProductSubscription();
        $subscriptionLineRentalSaver
            ->setTariffName(self::LINE_RENTAL_SAVER)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(186.99)
            ->setCurrentSubscriptionPriceInPounds(165.99);

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(0)
            ->setCurrentSubscriptionPriceInPounds(0);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5);

        $subscriptionBroadband = new CustomerProductSubscription();
        $subscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(15)
            ->setCurrentSubscriptionPriceInPounds(9.99);

        $subs = array(
            $subscriptionBroadband,
            $subscriptionLineRentalSaver,
            $subscriptionLineRental,
            $subscriptionCallPlan
        );

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentCallPlanSubscription());
        $this->assertEquals(0, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(0, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentSubscriptionPriceInPounds());
        $this->assertEquals(6, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(5, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentSubscriptionPriceInPounds());
    }

    /**
     * Tests that if we have a current call plan and line rental with line rental saver,
     * and future subscriptions as well, then the correct current
     * (zero) line rental and call plan are returned
     *
     * @return void
     */
    public function testGetsLineRentalAndCallPlanWhenCurrentAndFuturePhoneSubsAndLineRentalSaver()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRentalSaver = new CustomerProductSubscription();
        $subscriptionLineRentalSaver
            ->setTariffName(self::LINE_RENTAL_SAVER)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(186.99)
            ->setCurrentSubscriptionPriceInPounds(165.99);

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(0)
            ->setCurrentSubscriptionPriceInPounds(0);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5);

        $subscriptionBroadband = new CustomerProductSubscription();
        $subscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(15)
            ->setCurrentSubscriptionPriceInPounds(9.99);

        $futureSubscriptionLineRentalSaver = new CustomerProductSubscription();
        $futureSubscriptionLineRentalSaver
            ->setTariffName(self::LINE_RENTAL_SAVER)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($futureDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(286.99)
            ->setCurrentSubscriptionPriceInPounds(265.99);

        $futureSubscriptionLineRental = new CustomerProductSubscription();
        $futureSubscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($futureDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(0.50)
            ->setCurrentSubscriptionPriceInPounds(1.50);

        $futureSubscriptionCallPlan = new CustomerProductSubscription();
        $futureSubscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($futureDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(7)
            ->setCurrentSubscriptionPriceInPounds(5.49);

        $futureSubscriptionBroadband = new CustomerProductSubscription();
        $futureSubscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($futureDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(16)
            ->setCurrentSubscriptionPriceInPounds(10.23);

        $subs = array(
            $subscriptionBroadband,
            $subscriptionLineRentalSaver,
            $subscriptionLineRental,
            $subscriptionCallPlan,
            $futureSubscriptionBroadband,
            $futureSubscriptionLineRentalSaver,
            $futureSubscriptionLineRental,
            $futureSubscriptionCallPlan
        );

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentCallPlanSubscription());
        $this->assertEquals(0, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(0, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentSubscriptionPriceInPounds());
        $this->assertEquals(6, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(5, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentSubscriptionPriceInPounds());
    }

    /**
     * Tests that if we have a current call plan and line rental with line rental saver,
     * and future line subscriptions as well, and if the end date is not set
     * then the correct current
     * (zero) line rental and call plan are returned
     *
     * @return void
     */
    public function testGetsLineRentalAndCallPlanWhenCurrentAndFuturePhoneSubsAndLineRentalSaverWhenEndDateNotSet()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRentalSaver = new CustomerProductSubscription();
        $subscriptionLineRentalSaver
            ->setTariffName(self::LINE_RENTAL_SAVER)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setCurrentBasePriceInPounds(186.99)
            ->setCurrentSubscriptionPriceInPounds(165.99);

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setCurrentBasePriceInPounds(0)
            ->setCurrentSubscriptionPriceInPounds(0);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5);

        $subscriptionBroadband = new CustomerProductSubscription();
        $subscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($pastDate)
            ->setCurrentBasePriceInPounds(15)
            ->setCurrentSubscriptionPriceInPounds(9.99);

        $futureSubscriptionLineRentalSaver = new CustomerProductSubscription();
        $futureSubscriptionLineRentalSaver
            ->setTariffName(self::LINE_RENTAL_SAVER)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(286.99)
            ->setCurrentSubscriptionPriceInPounds(265.99);

        $futureSubscriptionLineRental = new CustomerProductSubscription();
        $futureSubscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(0.50)
            ->setCurrentSubscriptionPriceInPounds(1.50);

        $futureSubscriptionCallPlan = new CustomerProductSubscription();
        $futureSubscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(7)
            ->setCurrentSubscriptionPriceInPounds(5.49);

        $futureSubscriptionBroadband = new CustomerProductSubscription();
        $futureSubscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(16)
            ->setCurrentSubscriptionPriceInPounds(10.23);

        $subs = array(
            $subscriptionBroadband,
            $subscriptionLineRentalSaver,
            $subscriptionLineRental,
            $subscriptionCallPlan,
            $futureSubscriptionBroadband,
            $futureSubscriptionLineRentalSaver,
            $futureSubscriptionLineRental,
            $futureSubscriptionCallPlan
        );

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentCallPlanSubscription());
        $this->assertEquals(0, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(0, $phoneSubscriptionData->getCurrentLineRentalSubscription()->getCurrentSubscriptionPriceInPounds());
        $this->assertEquals(6, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(5, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentSubscriptionPriceInPounds());
    }

    /**
     * Tests that if we have a only have a current call plan but no line rental, then
     * just the call plan is returned
     *
     * @return void
     */
    public function testGetsOnlyCallPlanWhenCurrentCallPlanButNoLineRental()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5);

        $subscriptionBroadband = new CustomerProductSubscription();
        $subscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(15)
            ->setCurrentSubscriptionPriceInPounds(9.99);

        $subs = array(
            $subscriptionBroadband,
            $subscriptionCallPlan
        );

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertNull($phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentCallPlanSubscription());
        $this->assertEquals(6, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(5, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentSubscriptionPriceInPounds());
    }

    /**
     * Tests that if we have a current call plan and line rental saver but without line rental,
     * then the only the current call plan is returned
     *
     * @return void
     */
    public function testGetsCallPlanWhenCurrentCallPlanAndLineRentalSaver()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRentalSaver = new CustomerProductSubscription();
        $subscriptionLineRentalSaver
            ->setTariffName(self::LINE_RENTAL_SAVER)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setCurrentBasePriceInPounds(186.99)
            ->setCurrentSubscriptionPriceInPounds(165.99);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5);

        $subscriptionBroadband = new CustomerProductSubscription();
        $subscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(15)
            ->setCurrentSubscriptionPriceInPounds(9.99);

        $subs = array(
            $subscriptionBroadband,
            $subscriptionLineRentalSaver,
            $subscriptionCallPlan
        );

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertNull($phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $phoneSubscriptionData->getCurrentCallPlanSubscription());
        $this->assertEquals(6, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentBasePriceInPounds());
        $this->assertEquals(5, $phoneSubscriptionData->getCurrentCallPlanSubscription()->getCurrentSubscriptionPriceInPounds());
    }

    /**
     * Tests that if we only have future subscriptions for call plan and line rental with line rental saver,
     * then nothing is returned
     *
     * @return void
     */
    public function testGetsNothingWhenOnlyFutureSubscriptions()
    {
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $futureSubscriptionLineRentalSaver = new CustomerProductSubscription();
        $futureSubscriptionLineRentalSaver
            ->setTariffName(self::LINE_RENTAL_SAVER)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(286.99)
            ->setCurrentSubscriptionPriceInPounds(265.99);

        $futureSubscriptionLineRental = new CustomerProductSubscription();
        $futureSubscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(0.50)
            ->setCurrentSubscriptionPriceInPounds(1.50);

        $futureSubscriptionCallPlan = new CustomerProductSubscription();
        $futureSubscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(7)
            ->setCurrentSubscriptionPriceInPounds(5.49);

        $futureSubscriptionBroadband = new CustomerProductSubscription();
        $futureSubscriptionBroadband
            ->setTariffName(self::BROADBAND)
            ->setProductType(self::BROADBAND)
            ->setProductName(self::BROADBAND)
            ->setStartDate($futureDate)
            ->setCurrentBasePriceInPounds(16)
            ->setCurrentSubscriptionPriceInPounds(10.23);

        $subs = array(
            $futureSubscriptionBroadband,
            $futureSubscriptionLineRentalSaver,
            $futureSubscriptionLineRental,
            $futureSubscriptionCallPlan
        );

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertNull($phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertNull($phoneSubscriptionData->getCurrentCallPlanSubscription());
    }

    /**
     * Tests that if we don't have any subscriptions, then nothing is returned
     *
     * @return void
     */
    public function testGetsNothingWhenNoSubscriptionsFound()
    {
        $subs = array();

        $helper = Mockery::mock('AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $phoneSubscriptionData = $helper->getCurrentPhoneSubscriptionData(self::SERVICE_ID);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $phoneSubscriptionData);
        $this->assertNull($phoneSubscriptionData->getCurrentLineRentalSubscription());
        $this->assertNull($phoneSubscriptionData->getCurrentCallPlanSubscription());
    }
}
