<?php

/**
 * Class AccountChange_C2mSalesChannels
 *
 * <AUTHOR> <rob.denis<PERSON><PERSON><EMAIL>>
 */

class AccountChange_C2mSalesChannels
{
    const C2F_C2M_SALES_CHANNEL = 'PlusnetResidential-AccountChange-NoAffiliate-';
    const WORKPLACE_C2M_SALES_CHANNEL = 'PlusnetResidential-StaffAccountChange-NoAffiliate-';

    /**
     * @param string $campaign     Campaign code
     * @param bool   $isWorkplace  Are we in workplace account change?
     *
     * @return string
     */
    public static function getC2MSalesChannel($campaign, $isWorkplace = false)
    {
        if (empty($campaign))
        {
            $campaign = 'NoCampaign';
        }

        $prefix = self::C2F_C2M_SALES_CHANNEL;
        if ($isWorkplace) {
            $prefix = self::WORKPLACE_C2M_SALES_CHANNEL;
        }

        return $prefix . $campaign;
    }

    /**
     * @return Boolean
     */
    public static function isC2fToggleSet()
    {
        if (\Plusnet\Feature\FeatureToggleManager::isOn('copperToFibre')) {
            return true;
        }
    }

    /**
     * @return Boolean
     */
    public static function isC2mPromotionToggleSet()
    {
        if (\Plusnet\Feature\FeatureToggleManager::isOn('accountChangePromotions')) {
            return true;
        }
    }
}
