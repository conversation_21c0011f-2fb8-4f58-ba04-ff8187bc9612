<?php

/**
 * <AUTHOR>
 */

class AccountChange_ServiceManager
{
    private static $factoryMap = array(
        'ContractDurationHelper' => 'AccountChange_ContractDurationHelperFactory',
        'FibreHelper' => 'AccountChange_FibreHelperFactory',
        'MgalsHelper' => 'AccountChange_MgalsHelperFactory'
    );

    private static $services = array();

    /**
     * @param string $serviceName service name
     * @return AccountChange_ContractDurationHelper | AccountChange_FibreHelper
     */
    public static function getService($serviceName)
    {
        if (!empty(static::$services[$serviceName])) {
            return static::$services[$serviceName];
        }

        $factoryClass = static::$factoryMap[$serviceName];

        return $factoryClass::createService($serviceName);
    }

    /**
     * @param string $serviceName service name
     * @param object $service     service
     * @return void
     */
    public static function setService($serviceName, $service)
    {
        static::$services[$serviceName] = $service;
    }
}
