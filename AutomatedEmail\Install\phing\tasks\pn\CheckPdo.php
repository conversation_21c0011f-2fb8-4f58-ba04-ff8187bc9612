<?php

	/**
	 * phing task for checking whether we have PDO and PDO-MySQL installed
	 * 
	 * @uses Task
	 * @package Framework
	 * @subpackage Install
	 * @version $Id: CheckPdo.php,v 1.2 2008-05-19 05:51:34 swestcott Exp $
	 * @copyright 2006 PlusNet plc
	 * <AUTHOR> <<EMAIL>>
	 * @since 10/08/2006
	 * @filesource 
	 */
	require_once('phing/Task.php');

	class checkPdo extends Task
	{
		private $strProperty = null;

		public function setHasPDOProperty($strProperty)
		{
			$this->strProperty = $strProperty;
		}

		public function init()
		{
		}

		/**
		 * Main function that gets called to see if we have PDO installed correctly with MySQL support 
		 * 
		 * @access public
		 */
		public function main()
		{
			if((class_exists('PDO') == true)
			      && (defined('PDO::MYSQL_ATTR_INIT_COMMAND') == true))
			{
				// installed correctly so set property
				$this->project->setProperty($this->strProperty, true);
			}
		}
	}

?>
