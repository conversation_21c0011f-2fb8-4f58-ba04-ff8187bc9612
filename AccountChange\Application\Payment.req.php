<?php
/**
 * Payment Requirement Class
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-07-19
 */
/**
 * AccountChange_Payment class
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class AccountChange_Payment extends Mvc_WizardRequirement
{
    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'validationHash'    => 'external:string',
        'paymentHandoverId' => 'external:string'
    );

    /**
     * Mappage to find out how internal variables are
     * spawned into creation
     *
     * @var array
     */
    protected $arrValidatorProducts = array(
        'paymentSuccess' => 'valPaymentHandoverIdAndHash'
    );


    /**
     * Validate payment handover id & validation hash, then set response data into Signup variables
     *
     * @param string $paymentHandoverId the handover Id
     * @param string $validationHash    the validation hash
     *
     * @return array
     */
    public function valPaymentHandoverIdAndHash($paymentHandoverId, $validationHash)
    {
        $paymentSuccess = false;

        $paymentResponseData = $this->getPaymentResponseData($paymentHandoverId, $validationHash);

        if ($paymentResponseData->isSuccessful()) {
            $paymentSuccess = true;
        }

        return array(
            'paymentHandoverId' => $paymentHandoverId,
            'validationHash'    => $validationHash,
            'paymentSuccess'    => $paymentSuccess
        );
    }

    /**
     * Display extra information required for payment redirect page
     *
     * @param array &$arrData An array of data
     *
     * @return array
     */
    public function describe(array &$arrData)
    {
        return array (
            'paymentHandoverId' => isset($arrData['paymentHandoverId']) ? $arrData['paymentHandoverId'] : false,
            'validationHash'    => isset($arrData['validationHash']) ? $arrData['validationHash'] : false,
            'redirected'        => isset($arrData['redirected']) ? $arrData['redirected'] : '0',
            'paymentSuccess'    => isset($arrData['paymentSuccess']) ? $arrData['paymentSuccess'] : false
        );
    }

    /**
     * Wrapper to mock static call to get PaymentResponseData
     *
     * @param string $paymentHandoverId The payment handover id
     * @param string $validationHash    Validation hash
     *
     * @return GenericImmediatePaymentApplication_PaymentResponseData
     */
    protected function getPaymentResponseData($paymentHandoverId, $validationHash)
    {
        return GenericImmediatePaymentApplication_PaymentResponseData::getResponseByHandoverIdAndHash(
            $paymentHandoverId,
            $validationHash
        );
    }
}
