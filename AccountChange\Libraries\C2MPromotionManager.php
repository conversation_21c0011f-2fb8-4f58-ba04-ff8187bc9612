<?php

use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\C2mApiClient\Entity\Discount;
use Plusnet\C2mApiClient\Entity\PaymentFrequency;

/**
 * Manage the application of promotions on to products.
 *
 * Class C2MPromotionManager
 */
class C2MPromotionManager
{
    /**
     * Keys used multiple times to retrieve and add values from/to arrays
     *
     * @var string
     */
    const DEC_VALUE_KEY                              = 'decValue';
    const PRESET_DISCOUNT_KEY                        = 'presetDiscount';
    const CURRENT_BASE_PRICE_KEY                     = 'currentBasePrice';
    const INT_SDI_KEY                                = 'intSdi';
    const LINE_RENTAL_DISCOUNT_VALUE_KEY             = 'lineRentalDiscountValue';
    const CURRENT_BASE_PRICE_NO_DISCOUNT_KEY         = 'currentBasePriceNoDiscount';
    const LEGALS_URL_KEY                             = 'promoLegals';
    const CHANNEL_SECTOR                             = 0;
    const CHANNEL_SITUATION                          = 1;
    const CHANNEL_CAMPAIGN                           = 3;
    const ERROR_PROMO_CODE_NOT_APPLICABLE_TO_CHANNEL = 'ERROR_PROMO_CODE_NOT_APPLICABLE_TO_CHANNEL';

    private $c2mSalesChannelPromotions;

    private $accountChangeRegistry;

    private $c2MPromotionAdapter;

    private $promoValidationCheckErrorCode;

    /**
     * C2MPromotionManager constructor.
     *
     * @param \AccountChange_C2mPromotionsHelper $c2mSalesChannelPromotions
     * @param \AccountChange_Registry $accountChangeRegistry
     * @param \PromotionConverter $c2MPromotionAdapter
     */
    public function __construct(
      AccountChange_C2mPromotionsHelper $c2mSalesChannelPromotions,
      AccountChange_Registry $accountChangeRegistry,
      PromotionConverter $c2MPromotionAdapter
    ) {
        $this->c2mSalesChannelPromotions = $c2mSalesChannelPromotions;
        $this->accountChangeRegistry = $accountChangeRegistry;
        $this->c2MPromotionAdapter = $c2MPromotionAdapter;
    }

    /**
     * Apply promotional discounts to the array of products.
     *
     * @param array  $products
     * @param string $oldSdi
     * @param string $serviceId
     * @param string $promoCode
     * @param bool   $agentSubmittedPromoCode
     *
     * @return array
     */
    public function getProductsWithC2mDiscount(
        array $products = [],
        $oldSdi,
        $serviceId,
        $promoCode,
        $agentSubmittedPromoCode = false
    ) {
        $promotions = $this->c2mSalesChannelPromotions->getPromotions();

        /**
         * DLIFE-649/SALES-5584
         *
         * Add no discount price to current product array - will be used on Current Package page.
         * Assigning here as CURRENT_BASE_PRICE_KEY value will be mutated if discount is available
         */
        foreach ($products as &$product) {
            $product = $this->addBasePriceNoDiscountToProduct($product);
        }

        if (empty($promotions) || !is_array($promotions)) {
            if (!empty($promoCode)) {
                $this->promoValidationCheckErrorCode = self::ERROR_PROMO_CODE_NOT_APPLICABLE_TO_CHANNEL;
            }
            return $products;
        }

        $products = $this->getDiscountableProducts($products, $oldSdi);
        foreach ($products as &$product) {
            $product = $this->attachPromotionDiscountsToProducts(
                $promotions,
                $product,
                $serviceId,
                $promoCode,
                $agentSubmittedPromoCode
            );
        }

        try {
            $discountedProducts = $this->applyDiscountsToProducts($products);
        } catch (\Exception $e) {
            return $products;
        }

        return $discountedProducts;
    }

    /**
     * Return a list of products that are permitted to have discount applied
     *
     * @see AccountChange_Controller::getProductsWithDiscount()
     * Unfortunately I don't understand the business logic of this method
     * as it is borrowed from  getProductsWithDiscount()
     *
     * @param array $products
     * @param $oldSdi
     *
     * @return array
     */
    private function getDiscountableProducts(array $products, $oldSdi)
    {
        return array_filter(
          $products,
          function ($product) use ($oldSdi) {
              return $product[self::INT_SDI_KEY] != $oldSdi
                && !array_key_exists(self::PRESET_DISCOUNT_KEY, $product)
                || empty($product[self::PRESET_DISCOUNT_KEY]);
          }
        );
    }

    /**
     * If the discount contained in the Promotion applies to the product
     * it is we extract the required C2M discount data into an AccountChange
     * friendly version and attach it to the product.
     *
     * @param array $promotions
     * @param array $product
     *
     * @return array
     */
    private function attachPromotionDiscountsToProducts(
        $promotions,
        array $product,
        $serviceId,
        $promoCode,
        $agentSubmittedPromoCode
    )
    {
        foreach ($promotions as $promotion) {
            $this->c2MPromotionAdapter->reset();
            $promotionAdapter = $this->c2MPromotionAdapter->setPromotion($promotion);
            $promotionAdded = false;

            // First loop to look for applicable product discounts...
            foreach ($promotion->getDiscounts() as $discount) {
                if (!$this->isDiscountApplicableToBroadbandProduct($discount, $product)) {
                    continue;
                }

                $promotionAdapter->setDiscount($discount);
                $promotionAdded = true;
                $this->addPromoCodeToRegistry($product[self::INT_SDI_KEY], $promotion);
                break;
            }

            // Second loop to look for applicable line rental discounts, but only if we've also matched a product above
            // (otherwise we'd pull in any line rental discount into any product). If we want to allow line rental only in the future
            // then we'll need to remove if ($promotionAdded)
            if ($promotionAdded) {
                $promotionAdapter = $this->addLineRentalDiscountIfAppicable($promotion, $promotionAdapter, $product[self::INT_SDI_KEY]);
                if ($this->userCanHaveDiscount($promotion, $serviceId, $promoCode, $agentSubmittedPromoCode)) {
                    $product[self::PRESET_DISCOUNT_KEY] = $promotionAdapter->convertPromotion();

                    if ($promotion->getIsPersonalisedOffer() && $promotion->getPersonalisedEndDate() > $promotion->getActiveTo()) {
                        $product[self::PRESET_DISCOUNT_KEY]['promoEndDate'] = date('Y-m-d', strtotime($promotion->getPersonalisedEndDate()));
                    } else {
                        $product[self::PRESET_DISCOUNT_KEY]['promoEndDate'] = date('Y-m-d', strtotime($promotion->getActiveTo()));
                    }

                    $addedPromotionCode = $promotion->getCode();
                    if ($addedPromotionCode !== null || !empty($promoCode)) {
                        $channelBreakdown = explode('-', $this->c2mSalesChannelPromotions->getSalesChannel());
                        $product[self::LEGALS_URL_KEY] = sprintf(
                            "/legals/%s/%s/%s/%s/A",
                            $channelBreakdown[self::CHANNEL_SECTOR],
                            $channelBreakdown[self::CHANNEL_SITUATION],
                            $channelBreakdown[self::CHANNEL_CAMPAIGN],
                            $addedPromotionCode !== null ? $addedPromotionCode : $promoCode
                        );
                    }
                }
                break;
            }
        }
        return $product;
    }

    private function addLineRentalDiscountIfAppicable($promotion, $promotionAdapter, $sdi)
    {

        foreach ($promotion->getDiscounts() as $discount) {
            if ($this->isDiscountApplicableToLineRental($discount)) {
                $promotionAdapter->setLineRentalDiscount($discount);
                $this->addPromoCodeToRegistry($sdi, $promotion);
            }
        }
        return $promotionAdapter;
    }

    /**
     * A user cannot have a line rental discount if they have LRS.
     * This uses a validator to confirm whether or not the promotion can be added based on the above.
     *
     * @param Promotion\ $promotion                C2m Promotion object
     * @param int        $serviceId                Service id
     * @param string     $promoCode                Promotion code
     * @param bool       $agentSubmittedPromoCode  Has the code been submitted manually in workplace a/c?
     *
     *
     * @return bool
     * @throws AccountChange_InvalidValidatorException
     **/
    protected function userCanHaveDiscount($promotion, $serviceId, $promoCode, $agentSubmittedPromoCode = false)
    {
        $additionalValidatorInformation['C2MPromotion'] = $promotion;
        if ($agentSubmittedPromoCode) {
            $additionalValidatorInformation['C2MPromotionCode'] = $promotion->getCode();
        } else {
            $additionalValidatorInformation['C2MPromotionCode'] = $promoCode;
        }

        $additionalValidatorInformation['ServiceId'] = $serviceId;
        $additionalValidatorInformation['SalesChannel'] = $this->c2mSalesChannelPromotions->getSalesChannel();
        $validationCheck = $this->getPromotionCodeValidationCheck($additionalValidatorInformation);
        $isValid = $validationCheck->isAccountChangeAllowed();
        $this->promoValidationCheckErrorCode = $validationCheck->getErrorCode();
        return $isValid;
    }

    protected function getPromotionCodeValidationCheck($additionalValidatorInformation)
    {
        // Validation policies added here will be run against the promotion and remove it from
        // the change if it fails to validate (allowing the change to continue).
        $actor = $this->getLoggedInOrScriptActor();
        return new AccountChange_ValidationCheck(
            $actor,
            array(
                AccountChange_PromotionCodePolicy::class,
                AccountChange_PromotionCodePersonalisedPolicy::class,
                AccountChange_PromotionCodeAccountCompatibilityPolicy::class,
                AccountChange_PromotionCodeWorkplaceChannelPolicy::class
            ),
            false,
            false,
            $additionalValidatorInformation
        );
    }

    /**
     * Return the logged in or script actor object
     *
     * @return Auth_BusinessActor
     */
    protected function getLoggedInOrScriptActor()
    {
        return BusTier_BusTier::getLoggedInOrScriptActor();
    }

    /**
     * Does the provided discount applied to the $product
     *
     * @param \Plusnet\C2mApiClient\Entity\Discount $discount
     * @param array $product
     *
     * @return bool
     */
    private function isDiscountApplicableToBroadbandProduct(
      Discount $discount,
      array $product
    ) {
        $discountValue = $discount->getDiscountValues();
        $productMappedToC2MProductOffering = isset($product['productOfferingName']);

        if (!$productMappedToC2MProductOffering || !isset($discountValue[0]) || !$this->productIsDualPlay($product)) {
            return false;
        }

        foreach ($discount->getProductOfferingPaymentInformations() as $productOfferingPaymentInformation) {
            if ($product['productOfferingName'] !== $productOfferingPaymentInformation->getName()) {
                continue; // continue to the next productOfferingPaymentInformation
            }

            return true;
        }
        return false;
    }

    /**
     * Does the provided discount applied to the $product
     *
     * @param \Plusnet\C2mApiClient\Entity\Discount $discount
     * @param array $product
     *
     * @return bool
     */
    private function isDiscountApplicableToLineRental(
        Discount $discount
    ) {
        foreach ($discount->getProductOfferingPaymentInformations() as $productOfferingPaymentInformation) {
            if ('LineRental' === $productOfferingPaymentInformation->getName()) {
                return true; // continue to the next productOfferingPaymentInformation
            }
        }
        return false;
    }

    /**
     * Add the valid promocode against the SDI of the product and store the
     * array in the registry.
     *
     * @param $productSdi
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     */
    private function addPromoCodeToRegistry($productSdi, Promotion $promotion)
    {
        $c2mPromoCodes = AccountChange_Registry::instance()->getEntry(
          'c2mPromoCode'
        );
        $c2mPromoCodes[$productSdi] = $promotion->getCode();
        $this->accountChangeRegistry->setEntry(
          'c2mPromoCode',
          $c2mPromoCodes
        );

        $promotions[$productSdi] = $promotion;
        $this->accountChangeRegistry->setEntry(
            'c2mPromotion',
            $promotions
        );
    }


    /**
     * @param array $discountableProducts
     *
     * @return array
     */
    private function applyDiscountsToProducts(array $discountableProducts)
    {
        if (empty($discountableProducts)) {
            return $discountableProducts;
        }

        $productsWithDiscountApplied = array_map(
          [$this, 'applyDiscount'],
          $discountableProducts
        );
        return $productsWithDiscountApplied;

    }


    /**
     * @param $product
     *
     * @return bool
     */
    private function isDiscountGreaterThanProductValue($product)
    {
        $productCost = $product[self::CURRENT_BASE_PRICE_KEY];
        return ($productCost->toDecimal() - $product[self::PRESET_DISCOUNT_KEY][self::DEC_VALUE_KEY]) < 0;
    }

    /**
     * @param $product
     * @return bool
     */
    private function productIsDualPlay($product)
    {
        return $product['isDual'] ? true : false;
    }

    /**
     * @param $product
     *
     * @return array
     */
    protected function applyDiscount($product)
    {
        if (!isset($product[self::PRESET_DISCOUNT_KEY]) || !$this->productIsDualPlay($product)) {
            return $product;
        }

        if ($this->isDiscountGreaterThanProductValue($product)){
            unset($product[self::PRESET_DISCOUNT_KEY]);
            return $product;
        }

        $productCost = $product[self::CURRENT_BASE_PRICE_KEY];

        if (isset($product[self::PRESET_DISCOUNT_KEY][self::DEC_VALUE_KEY])) {
            $leadingPriceBroadband =
            AccountChange_DiscountHelper::calculateLeadingPrice(
                $productCost,
                $product[self::PRESET_DISCOUNT_KEY]['promoDiscountType'],
                $product[self::PRESET_DISCOUNT_KEY][self::DEC_VALUE_KEY]
            );

            $discountAmountBroadband =
            AccountChange_DiscountHelper::calculateDiscountAmount(
                $productCost,
                $product[self::PRESET_DISCOUNT_KEY]['promoDiscountType'],
                $product[self::PRESET_DISCOUNT_KEY][self::DEC_VALUE_KEY]
            );

            $leadingPrice = $leadingPriceBroadband;
            $discountAmount = $discountAmountBroadband;
        }

        // If we've got a line rental discount, further reduce the price..
        if (isset($product[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY])) {
            // The promotion has pulled back the fact we've got a line rental discount of a particular value and
            // type (% or value) - we need to keep a note of this, but we cannot actually calculate the value here as
            // we don't know which WLR product will be selected (and hence the line rental price).
            $product[self::LINE_RENTAL_DISCOUNT_VALUE_KEY] = $product[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY];
            $product['lineRentalPromoDiscountType'] = $product[self::PRESET_DISCOUNT_KEY]['lineRentalPromoDiscountType'];
        }

        $product[self::CURRENT_BASE_PRICE_KEY] = $leadingPrice;
        $product['currentBasePriceBroadband'] = $leadingPriceBroadband;
        $product['discountAmount'] = $discountAmount;
        $product['discountAmountBroadband'] = $discountAmountBroadband;

        return $product;

    }

    /**
     * Add the base price no discount field to product object
     *
     * @param $product
     * @return mixed
     */
    private function addBasePriceNoDiscountToProduct($product)
    {
        $product[self::CURRENT_BASE_PRICE_NO_DISCOUNT_KEY] = $product[self::CURRENT_BASE_PRICE_KEY];
        return $product;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getPromotionValidationErrorCode()
    {
        return $this->promoValidationCheckErrorCode;
    }
}
