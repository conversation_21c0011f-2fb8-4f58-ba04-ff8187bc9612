<?php
/**
 * AccountChange Workplace Decorator
 *
 * Testing class for the ccountChange_WorkplaceDecorator
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: WorkplaceDecorator.test.php,v 1.2 2009-01-27 09:06:55 bselby Exp $
 * @since      File available since 2008-12-17
 */
/**
 * AccountChange Workplace Decorator Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_WorkplaceDecorator_Test extends PHPUnit_Framework_TestCase
{
    /**
     * PHPUnit setUp function
     *
     */
    public function setUp()
    {

    }

    /**
     * PHPUnit tear down function
     *
     */
    public function tearDown()
    {

    }

    /**
     * Checks that the strTemplateName variable is set correctly
     *
     */
    public function testArrInputIsSetupCorrectly()
    {
        $strTemplateName = 'DecorationAccountChange.tpl';

        $objDecorator = new AccountChange_WorkplaceDecorator();

        $this->assertAttributeEquals($strTemplateName, 'strTemplateName', $objDecorator);
    }
}