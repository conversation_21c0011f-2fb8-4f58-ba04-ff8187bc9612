CREATE TEMPORARY TABLE userdata.tmpSeriveMarket
(
    service_id int unsigned zerofill NOT NULL,
    intMarketId tinyint(3) unsigned NOT NULL,
    PRIMARY KEY  (`service_id`)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

insert into userdata.tmpSeriveMarket 
    select s.service_id, min(scm.intMarketId) intMarketId
from userdata.services s  
join userdata.components c on c.service_id  = s.service_id
join products.tblServiceComponentMarket scm on c.component_type_id = scm.intServiceComponentId
where s.status = 'active' 
AND c.status = 'active'
AND s.type in (6718,6721,6724,6754,6755,6768,6769,6782,6784) 
group by s.service_id;



select
CONCAT_WS(
',', 
s.service_id, 
case s.type 
    when 6718 then 6841
    when 6721 then 6841
    when 6724 then 6841
    when 6754 then 6842
    when 6755 then 6841
    when 6768 then 6844
    when 6769 then 6845
    when 6782 then 6843
    when 6784 then 6846
end
, 
scm.intMarketId, 
s.next_invoice
)
from userdata.services s 
join userdata.components c on s.service_id = c.service_id 
join userdata.tmpSeriveMarket scm on s.service_id=scm.service_id
where c.component_type_id IN 
(509,510,515,554,555,582,583, 596, 669, 670, 735, 736, 919, 920, 921, 995, 996, 997, 1331,
1332, 1333, 1334, 1335, 1336, 1337, 1338, 1339, 1340, 1341, 1342, 1345, 1346, 1347, 1348,
1349, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1357)
AND s.type in (6718,6721,6724,6754,6755,6768,6769,6782,6784) 
AND s.status = 'active' 
AND c.status in ('active','unconfigured','queued-activate', 'queued-reactivate')
AND s.next_invoice >= '2013-10-01'
ORDER BY invoice_day limit 1500;

CREATE TEMPORARY TABLE userdata.tmpSeriveWithPhone
(
    service_id int unsigned zerofill NOT NULL,
    PRIMARY KEY  (`service_id`)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

insert into userdata.tmpSeriveWithPhone 
    select distinct s.service_id
from userdata.components c 
join userdata.services s on c.service_id  = s.service_id 
where c.component_type_id IN 
(509,510,515,554,555,582,583, 596, 669, 670, 735, 736, 919, 920, 921, 995, 996, 997, 1331,
1332, 1333, 1334, 1335, 1336, 1337, 1338, 1339, 1340, 1341, 1342, 1345, 1346, 1347, 1348,
1349, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1357) 
AND s.type in (6718,6721,6724,6754,6755,6768,6769,6782,6784) 
AND s.status = 'active' 
And s.next_invoice >= '2013-10-01'
AND c.status in ('active','unconfigured','queued-activate', 'queued-reactivate');

select 
CONCAT_WS(
',', 
s.service_id, 
case s.type 
    when 6718 then 6847
    when 6721 then 6847
    when 6724 then 6847
    when 6754 then 6848
    when 6755 then 6847
    when 6768 then 6850
    when 6769 then 6851
    when 6782 then 6849
    when 6784 then 6852
end
, 
scm.intMarketId, 
s.next_invoice 
)
from  userdata.services s
join userdata.components c on c.service_id  = s.service_id 
join userdata.tmpSeriveMarket scm on s.service_id=scm.service_id
where s.type in (6718,6721,6724,6754,6755,6768,6769,6782,6784) 
AND c.component_type_id IN (648,649,650,651,652,778,779,812,813,814,815,816,817,818,875,876,877,941,942,943,944,945,946,979,980,981,982,1159,1188,1189)
AND s.status = 'active' 
And s.next_invoice >= '2013-10-01'
AND s.service_id not in ( select service_id from userdata.tmpSeriveWithPhone)
AND c.status = 'active'
ORDER BY invoice_day limit 1500;
