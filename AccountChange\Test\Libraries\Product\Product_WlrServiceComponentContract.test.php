<?php
/**
 * Service Component Contract
 *
 * Testing class for the AccountChange_Product_WlrServiceComponentContract class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 Plusnet
 * @since     File available since 2008-08-19
 */
/**
 * Service Definition Contract Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 Plusnet
 */
class AccountChange_Product_WlrServiceComponentContract_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @covers AccountChange_Product_WlrServiceComponentContract::getProductCost
     *
     * @return void
     */
    public function testGetProductCostReturnsTheCorrectPriceAndFormatted()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getWlrProductComponentPrice'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
                         ->method('getWlrProductComponentPrice')
                         ->will($this->returnValue(1500));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $strContractLengthHandle   = 'MY_MONTHLY';
        $strPaymentFrequencyHandle = 'MY_YEARLY';
        $strProductComponentHandle = 'MY_SUBSCRIPTION';
        $uxtContractEndDate        = time();
        $uxtContractStartDate      = time();

        $objContract = new AccountChange_Product_WlrServiceComponentContract(
            $strContractLengthHandle,
            $strPaymentFrequencyHandle,
            $strProductComponentHandle,
            $uxtContractEndDate,
            $uxtContractStartDate
        );

        $floActualCost = $objContract->getProductCost(1111);

        $this->assertEquals(15.00, $floActualCost);
    }

    /**
     * Test that the constructor is using WlrLineRent as the component component handle.
     * Otherwise we end up scheduling account changes way in the future if
     * SUBSCRIPTION has a tariff such as 12 months
     *
     * @covers AccountChange_Product_WlrServiceComponentContract::__construct
     *
     * @return void
     */
    public function testConstructorPopulatesPropertiesAndUsesWlrLineRentForProductComponentHandle()
    {
        $strContractLengthHandle   = 'MONTHLY';
        $strPaymentFrequencyHandle = 'MONTHLY';
        $strProductComponentHandle = 'WlrLineRent';
        $uxtContractEndDate        = null;

        $objContract = new AccountChange_Product_WlrServiceComponentContract(
            $strContractLengthHandle,
            $strPaymentFrequencyHandle
        );

        $this->assertAttributeEquals($strContractLengthHandle, 'strContractLengthHandle', $objContract);
        $this->assertAttributeEquals($strPaymentFrequencyHandle, 'strPaymentFrequencyHandle', $objContract);
        $this->assertAttributeEquals($strProductComponentHandle, 'strProductComponentHandle', $objContract);
        $this->assertAttributeEquals($uxtContractEndDate, 'uxtContractEndDate', $objContract);
    }
}
