<?php
require_once(__DIR__ . '/../Libraries/C2mPromotionsHelper.php');
/**
 * Select hardware requirement
 *
 * Data collection for Hardware
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-13
 */

/**
 * AccountChange_Hardware
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Hardware extends Mvc_WizardRequirement
{
    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'hardwareOption' => 'external:Custom:optional',
        'dataExtensionKit' => 'external:Custom:optional',
        'waivePostage' => 'external:Custom:optional',
    );

    /**
     * Array of variables that can be defined by the validators
     *
     * @var array
     */
    protected $arrValidatorProducts = array(
        'extensionKitId' => 'valExtensionKitId'
    );

    /* @var Core_Service $service */
    private $service;

    /* @var string $routerHandle handle identifying the router, pass to template to determine images */
    private $routerHandle;

    /* @var null|string $defaultSelectedHardware */
    private $defaultSelectedHardware = null;

    /**
     * Describe
     *
     * Pull all the information needed for the view of this requirement
     *
     * @param array &$arrValidatedApplicationData Validated application data
     *
     * @return array
     *
     * @throws NumberFormatException
     * @throws WrongTypeException
     * @throws Db_TransactionException
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $this->service = $this->getApplicationStateVariable('objCoreService');
        $serviceId = $this->getServiceId();
        if ($this->isC2fToggleSet()) {
            $accessedDirectlyFromSummaryPage = strpos($_SERVER['HTTP_REFERER'], 'C2FSummary') !== false;
            $_SESSION['nextPage'] = ($accessedDirectlyFromSummaryPage) ? 'C2FSummary' : '';
        }

        $return = array(
            'hardware' => $this->fetchAvailableHardware(new Int($arrValidatedApplicationData['intNewSdi'])),
            'selectedBroadband' => $this->getSelectedBroadband(),
            'campaignCode' => $this->getApplicationStateVariable('campaignCode')
        );

        // extract hardware information required for the product
        $return['productDetails'] = $this->getFibreProductDetails($serviceId);

        // Collect the current product speed ranges
        $currentProductSpeedRanges = $this->getMinAndMaxSpeedRanges();
        $return['currentProductDownloadSpeedRangeMin'] = $currentProductSpeedRanges['downloadSpeedRangeMin'];
        $return['currentProductDownloadSpeedRangeMax'] = $currentProductSpeedRanges['downloadSpeedRangeMax'];

        $hardwareCharges = $this->getHardwareCharges($serviceId);
        $return['hardwareCost'] = $this->getHardwareCost($hardwareCharges);
        $return['exVatHardwareCost'] = $this->getExVatHardwareCost($hardwareCharges);

        $return['routerHandle'] = $this->getRouterHandle();
        $return['defaultSelectedHardware'] = $this->getDefaultSelectedHardware();
        $return['showWaivePostage'] = $this->shouldShowWaivePostageCheckbox();

        return $return;
    }

    /**
     * @param Core_Service $service Service object
     * @return void
     */
    public function setService(Core_Service $service)
    {
        $this->service = $service;
    }

    /**
     * Gets a fibre helper object
     *
     * @return AccountChange_FibreHelper
     **/
    protected function getFibreHelper()
    {
        return new AccountChange_FibreHelper();
    }

    /**
     * Obtains hardware information for the hardware page.
     *
     * @param int $intServiceId Service ID
     *
     * @return array
     * @throws Db_TransactionException
     */
    private function getFibreProductDetails($intServiceId)
    {
        $arrResult = array();

        if ($this->isApplicationStateVariable('intOldSdi')
            && $this->isApplicationStateVariable('intNewSdi')
            && $this->getApplicationStateVariable('objCoreService')
        ) {
            $intOldSdi = $this->getApplicationStateVariable('intOldSdi');
            $intNewSdi = $this->getApplicationStateVariable('intNewSdi');

            if (isset($intOldSdi) && isset($intNewSdi)) {
                $objDatabase = Db_Manager::getAdaptor('AccountChange');

                $arrOldProduct = array();
                $arrNewProduct = array();

                // determine if we are fibre products
                $fibreHelper = $this->getFibreHelper();

                $arrOldProduct['isFibre'] = $fibreHelper->isFibreProduct($intOldSdi);
                $arrNewProduct['isFibre'] = $fibreHelper->isFibreProduct($intNewSdi);

                // get old and new product information/speeds
                $arrOldProd = $objDatabase->getAdslProductDetails($intOldSdi);
                $arrNewProd = $objDatabase->getAdslProductDetails($intNewSdi);

                $objHardwareClient = $this->getHardwareClient();
                $arrHardware = $objHardwareClient->getLastAddedHardwareForService($intServiceId);

                if (isset($arrHardware) && isset($arrHardware['intComponentTypeId'])) {
                    $intComponentType = $arrHardware['intComponentTypeId'];
                    $arrOldHardware
                        = $objDatabase->getServiceComponentDetailsForComponentType($intComponentType);

                    if (isset($arrOldHardware) && isset($arrOldHardware['strName'])) {
                        $arrOldProduct['currentHardware'] = $arrOldHardware['strName'];
                    }
                }

                if (isset($arrOldProd)) {
                    $arrOldProduct['maximumSpeed'] = $arrOldProd['intMaximumSpeed'];
                    $arrOldProduct['maxUploadSpeed'] = $arrOldProd['intMaxUploadSpeed'];
                }

                if (isset($arrNewProd)) {
                    $arrNewProduct['maximumSpeed'] = $arrNewProd['intMaximumSpeed'];
                    $arrNewProduct['maxUploadSpeed'] = $arrNewProd['intMaxUploadSpeed'];
                }

                $arrResult['oldProduct'] = $arrOldProduct;
                $arrResult['newProduct'] = $arrNewProduct;
            }
        }

        return $arrResult;
    }

    public function getDefaultSelectedHardware()
    {
        return (class_exists("String")) ? new String($this->defaultSelectedHardware) : $this->defaultSelectedHardware;
    }

    public function setDefaultSelectedHardware($selectedHardware)
    {
        $this->defaultSelectedHardware = ($selectedHardware instanceof Primitive) ? $selectedHardware->getValue() : $selectedHardware;
    }

    public function getRouterHandle()
    {
        return (class_exists("String")) ? new String($this->routerHandle) : $this->routerHandle;
    }

    public function setRouterHandle($routerHandle)
    {
        $this->routerHandle = ($routerHandle instanceof Primitive) ? $routerHandle->getValue() : $routerHandle;
    }

    /**
     * @return bool
     */
    public function isC2fToggleSet()
    {
        return AccountChange_C2mSalesChannels::isC2fToggleSet();
    }


    /**
     * Get the selected broadband product from an application state variable
     *
     * @return array
     **/
    public function getSelectedBroadband()
    {
        return $this->getApplicationStateVariable('arrSelectedBroadband');
    }

    /**
     * Validator for $dataExtensionKit
     *
     * @param bool $dataExtensionKit Checkbox for if the customer wants extension kit
     *
     * @return array
     */
    public function valExtensionKitId($dataExtensionKit)
    {
        $data = array();

        if ('on' === $dataExtensionKit) {
            $data['extensionKitId'] = EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_REQUIRED;
            $data['dataExtensionKit'] = true;
        } else {
            $data['extensionKitId'] = EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED;
            $data['dataExtensionKit'] = false;
        }

        return $data;
    }

    /**
     * Validator for the selected hardware option
     *
     * @param string $hardwareOption The Hardware handle
     *
     * @return array
     */
    public function valHardwareOption($hardwareOption)
    {
        $validatedReturn = array();
        $handles = array();
        $newSdi = $this->getApplicationStateVariable('intNewSdi');

        $hardware = $this->fetchAvailableHardware(new Int($newSdi));
        $hardware[] = array('handle' => '');

        foreach ($hardware as $item) {
            $handles[] = $item['handle'];
        }

        if (!in_array($hardwareOption, $handles)) {
            $this->addValidationError('hardwareOption', 'INVALID');
        } else {
            $validatedReturn['hardwareOption'] = $hardwareOption;
        }

        return $validatedReturn;
    }

    /**
     * @param null|string $waivePostage waive postage checkbox - 'on' if checked, null if not.
     *
     * @return array
     */
    public function valWaivePostage($waivePostage)
    {
        if ($waivePostage && !$this->shouldShowWaivePostageCheckbox()) {
            $this->addValidationError('waivePostage', 'INVALID');
        }

        return ['waivePostage' => (bool)$waivePostage];
    }

    /**
     * Fetch the available hardware for a given Service Definition.
     * Currently this method checks for hardware bundles which are available in Signup.
     * This isn't ideal, but generally speaking, the hardware available in Signup is the same
     * as the hardware available in Account Change
     *
     * @param Int $sdi Service definition id
     *
     * @return array
     */
    public function fetchAvailableHardware(Int $sdi)
    {
        $hardwareHelper = $this->getHardwareHelper();

        if ($hardwareHelper->shouldUseHelper()) {
            $hardwareOffer = $hardwareHelper->getHardwareOffer();
            $availableHardware[] = $hardwareOffer->getHardwareBundleDetails();
            $this->setRouterHandle($hardwareOffer->getHardwareHandle());
            $this->setDefaultSelectedHardware($hardwareOffer->getHardwareDefaultSelection());
        } else {
            $hardwareClient = $this->getHardwareClient();
            $hardwareBundles = $hardwareClient->getSignupBundlesForServiceDefinition($sdi);

            if ($hardwareBundles->count() !== 0) {
                foreach ($hardwareBundles as $bundle) {
                    $this->setRouterHandle($bundle->getHandle());
                }
            }
            $this->setDefaultSelectedHardware($this->getRouterHandle());

            $availableHardware = [];

            foreach ($hardwareBundles as $bundle) {
                $availableHardware[] = [
                    'handle' => $bundle->getHandle(),
                    'name' => $bundle->getName(),
                ];
            }
        }

        return $availableHardware;
    }

    /**
     * Wrapper for retrieving a product
     *
     * @param Int $newSdi Service Definition Id
     *
     * @return Product_Product
     */
    protected function getProduct(Int $newSdi)
    {
        return new Product_Product($newSdi);
    }

    /**
     * Get a Hardware Client Object
     *
     * @return HardwareClient_Client
     */
    protected function getHardwareClient()
    {
        return BusTier_BusTier::getClient('hardware');
    }

    /**
     * Unit test wrapper for AccountChange_Controller::getMinAndMaxSpeedRanges.
     *
     * @return array
     */
    public function getMinAndMaxSpeedRanges()
    {
        $objLineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');

        return AccountChange_Controller::getMinAndMaxSpeedRanges($this->service, $objLineCheckResult);
    }

    /**
     * Get Service Id
     *
     * @return integer
     * @throws NumberFormatException
     * @throws WrongTypeException
     */
    private function getServiceId()
    {
        return new Int($this->service->getServiceId());
    }

    /**
     * @param int $serviceId Service ID
     * @return array|null
     */
    protected function getHardwareCharges($serviceId)
    {
        $cost = null;

        if (!$this->isFibreUpgradeCampaign()) {
            $arrData = array(
                'hardwareOption' => 'Yes',
                'intNewSdi' => $this->getApplicationStateVariable('intNewSdi'),
                'intServiceId' => $serviceId
            );

            $cost = AccountChange_Controller::getHardwareCharges($arrData);
        }
        return $cost;
    }

    /**
     * Get Hardware Cost
     *
     * @param array $hardwareCost Hardware Cost Array
     *
     * @return double
     */
    private function getHardwareCost($hardwareCost)
    {
        return isset($hardwareCost) ? number_format($hardwareCost['amount'], 2) : 0;
    }

    /**
     * Get Hardware Cost excluding VAT
     *
     * @param array $hardwareCost Hardware Cost Array
     *
     * @return double
     */
    private function getExVatHardwareCost($hardwareCost)
    {
        return isset($hardwareCost) ? number_format($hardwareCost['exVatAmount'], 2) : 0;
    }

    /**
     * Checks if the current journey is a fibre upgrade campaign journey
     *
     * @return bool
     */
    private function isFibreUpgradeCampaign()
    {
        return in_array(
            $this->getApplicationStateVariable('campaignCode'),
            AccountChange_CampaignCodes::getAllFibreUpgradeCampaignCodes()
        );
    }

    /**
     * @return bool
     */
    protected function shouldShowWaivePostageCheckbox()
    {
        /* @var Auth_BusinessActor $businessActor */
        $businessActor = $this->getApplicationStateVariable('objBusinessActor');

        $isWorkplace = $businessActor->getUserType() === 'PLUSNET_STAFF';

        $waivePostageHelper = new AccountChange_WaivePostageHelper(
            (bool)$this->getApplicationStateVariable('bolHousemove'),
            (bool)$this->getApplicationStateVariable('selectedContractDuration'),
            $this->getRouterHandle(),
            $isWorkplace
        );

        return $waivePostageHelper->shouldShowWaivePostageCheckbox();
    }

    /**
     * @return AccountChange_HardwareRequirementHelper
     */
    protected function getHardwareHelper()
    {
        /* @var Core_Service $service */
        $coreService = $this->getApplicationStateVariable('objCoreService');

        /* @var HardwareClient_Client $hardwareClient */
        $hardwareClient = $this->getHardwareClient();

        /* @var AccountChange_FibreHelper $fibreHelper */
        $fibreHelper = new AccountChange_FibreHelper();

        $newSdi = (int)$this->getApplicationStateVariable('intNewSdi');
        $isHousemove = (bool)$this->getApplicationStateVariable('bolHousemove');
        $isRecontract = (bool)$this->getApplicationStateVariable('selectedContractDuration');

        return new AccountChange_HardwareRequirementHelper(
            $hardwareClient,
            $coreService,
            $isHousemove,
            $isRecontract,
            $newSdi,
            $fibreHelper,
            $this->isApplicationStateVariable('bolPortal')
        );
    }
}
