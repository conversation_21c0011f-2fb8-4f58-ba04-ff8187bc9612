<?php
/**
 * Home Phone Selection Requirement on Workplace
 *
 * Collecting the data for the home phone requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: SelectHomephoneWorkplace.req.php,v 1.4 2009-05-11 04:19:11 fzaki Exp $
 * @link      ed in
 * @since     File available since 2008-10-16
 */
/**
 * AccountChange_SelectHomephoneWorkplace class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      ed in
 */
class AccountChange_SelectHomephoneWorkplace extends AccountChange_SelectHomephoneRequirement
{
    const ACCOUNT_CHANGE_ADAPTOR = 'AccountChange';

    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
            'intNewWlrId'         => 'external:Custom',
            'selectedContractDuration' => 'external:custom:optional',
            'bolContractResetWlr' => 'external:Custom:optional',
            'callerDisplay' => 'external:custom:optional'
        );

    /**
     * Custom validation order
     *
     * @var array
     */
    protected $arrCustomValidatorOrder = array(
            'valNewWlrId',
            'valSelectedContractDuration',
            'valContractResetWlr',
            'valTakeContractChargesWlr'
    );


    /**
     * Show recontract option
     *
     * @var bool
     */
    private $showContractOption = false;


    /**
     * Get list of service component IDs belonging to WLR products that
     * shouldn't be visible to the outside world, specifically the portal
     * or anywhere customer-facing.
     *
     * This is predominantly for the two new Mobile Anytime trial products
     * that are being created. They will be complete products in their own
     * right and will be seen by the system as "available" products for
     * use.
     *
     * Workplace overrides this functionality because it should be the
     * only frontend that can see them.
     *
     * @return array
     **/
    protected function getExcludedWlrProducts()
    {
        // don't hide anything; return an empty array
        return array();
    }

    /**
     * Describe
     *
     * Pull all the information needed for the view of this requirement
     *
     * @param array &$arrValidatedApplicationData input data
     *
     * @return array
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $objDatabase = Db_Manager::getAdaptor(self::ACCOUNT_CHANGE_ADAPTOR, Db_Manager::DEFAULT_TRANSACTION);
        $objCoreService = $this->getApplicationStateVariable('objCoreService');

        $arrReturn = array();
        $arrReturn['availableContractDurations'] = null;

        // We do not want to show the "No Home Phone" option if the
        // customer already has home phone. This would mean they are wanting to cancel home phone
        // If that is the case then they need to cease or transfer out
        $bolIncludeNoWlr = false;
        $intOldComponentId = isset($arrValidatedApplicationData['arrWlrProduct']['intInstanceId']) ?
                                   $arrValidatedApplicationData['arrWlrProduct']['intInstanceId'] : 0;

        // If a dual play product is chosen, force the WLR selection
        $dualPlay = false;
        if ($this->isApplicationStateVariable('selectedProductFamily')) {
            $productFamily = $this->getApplicationStateVariable('selectedProductFamily');
            $dualPlay = $productFamily->isDualPlay();
        }

        if (empty($intOldComponentId) && !$dualPlay) {
            $bolIncludeNoWlr = true;
        }

        $newSdi = $this->getApplicationStateVariable('intNewSdi');

        $selectedContractDuration = $arrValidatedApplicationData['selectedContractDuration'];
        $keepingExistingProduct = $this->keepingExistingContract($selectedContractDuration);
        $arrWlrProducts = $this->getWlrProducts(
            $newSdi,
            null,
            $bolIncludeNoWlr,
            $keepingExistingProduct
        );

        $arrWlrDetails = $this->getApplicationStateVariable('arrWlrProduct');

        $currentWlrId = isset($arrWlrDetails['intOldWlrId']) ? $arrWlrDetails['intOldWlrId'] : null;
        $isBusiness = isset($arrValidatedApplicationData['bolIsBusiness'])
            ? $arrValidatedApplicationData['bolIsBusiness'] : false;

        $this->showContractOption = $arrValidatedApplicationData['bolWlrOnly'];
        if ($this->showContractOption) {
            $contractDurationOptions = (new AccountChange_ContractDurationHelper($objDatabase))->getContractDurationOptions(
                $arrReturn,
                $objCoreService->getServiceId(),
                $this->getApplicationStateVariable('intOldSdi'),
                $this->getMinimumContractDuration()
            );

            $arrReturn['availableContractDurations'] = $contractDurationOptions['availableContractDurations'];
            $arrReturn['showContractOption'] = $this->showContractOption;
        }

        if (!$isBusiness) {
            $wlrProductFilter = $this->getWlrProductFilter();

            $arrWlrProducts = $wlrProductFilter->filterProducts(
                $arrWlrProducts,
                $currentWlrId,
                $this->hasActiveLineRentalSaver(),
                $this->isFibreProduct($newSdi)
            );

            foreach ($arrWlrProducts as &$product) {
                $product['intServiceComponentId'] = $product['intNewWlrId'];
                if ($wlrProductFilter->isOldToNew($product, $currentWlrId)) {
                    $product['callChargesIncreasing'] = true;
                } else {
                    $product['callChargesIncreasing'] = false;
                }
            }
            $arrWlrProducts = $this->getWlrProductSorter()->sort($arrWlrProducts, $currentWlrId);
        } else {
            $objCoreService = $this->getApplicationStateVariable('objCoreService');
            $productFilter = AccountChange_Product_WlrProductFilterFactory::getFilter(
                $objCoreService->isPlusnetUser(),
                $isBusiness
            );

            $arrWlrProducts = $productFilter->filter($arrWlrProducts);
        }

        $arrReturn['intSelectedWlrId'] = $arrWlrDetails['intOldWlrId'];
        if (isset($arrValidatedApplicationData['intNewWlrId'])) {
            $arrReturn['intSelectedWlrId'] = $arrValidatedApplicationData['intNewWlrId'];
        }

        $arrReturn['arrNewProducts'] = $this->groupProducts($arrWlrProducts, $arrReturn['intSelectedWlrId']);

        $arrReturn['strSelectedContract'] = $arrWlrDetails['strContractHandle'];

        // now we need to decide whether or not to show recontracting information
        if ($arrWlrDetails['strContractHandle'] == 'ANNUAL') {
            $arrReturn['bolShowContractReset'] = true;
        }

        // return call features for the old phone component!
        $arrReturn['arrOldCallFeatures'] = $this->getWlrCallFeatures($intOldComponentId);

        $arrReturn['selectedBroadband'] = $this->getSelectedBroadband();

        $currentProductSpeedRanges = $this->getMinAndMaxSpeedRanges();
        $arrReturn['currentProductDownloadSpeedRangeMin'] = $currentProductSpeedRanges['downloadSpeedRangeMin'];
        $arrReturn['currentProductDownloadSpeedRangeMax'] = $currentProductSpeedRanges['downloadSpeedRangeMax'];
        $bolHousemove = $this->getApplicationStateVariable('bolHousemove');

        $arrReturn['bolHousemove'] = $bolHousemove;

        return $arrReturn;
    }

    /**
     * Group products together by line rental price
     *
     * @param array $products         flat list of products
     * @param int   $currentProductId current product id
     *
     * @return array products grouped by line rental
     */
    protected function groupProducts(array $products, $currentProductId)
    {
        $groups = array();

        // Grab last product if it is the current one
        $current = end($products);
        if ($current['intNewWlrId'] == $currentProductId) {
            array_pop($products);
        } else {
            $current = null;
        }

        $makeKey = function ($product) {

            if (!empty($product['isDefaultLrsProduct'])) {

                return 'LRS Call Plan';
            }

            return sprintf('Line Rental: %s', $product['intLineRentCost']);
        };

        foreach ($products as $product) {
            $key = $makeKey($product);
            if (!isset($groups[$key])) {
                $groups[$key] = array();
            }
            $groups[$key][] = $product;
        }

        // Put the current product back on the end if necessary
        if ($current) {
            $groups['Current'] = array($current);
        }

        return $groups;
    }

    /**
     * Validation for the new service component id
     *
     * @param int $intNewWlrId new service component id
     * @param array $callerDisplay array of caller display flag
     * @param string $selectedContractDuration selected contract duration
     *
     * @return array
     */
    public function valNewWlrId($intNewWlrId, $callerDisplay = array(), $selectedContractDuration = null)
    {
        $arrValidatedReturn = array();

        $arrValidIds = array();
        $arrProductDetails = array();

        $intSdi = $this->getApplicationStateVariable('intNewSdi');

        $includeNoWlr = true;
        if ($this->isApplicationStateVariable('selectedProductFamily')) {
            $productFamily = $this->getApplicationStateVariable('selectedProductFamily');

            // If we're on a dual play product, then we must select a wlr product
            if ($productFamily->isDualPlay()) {
                $includeNoWlr = false;
            }
        }

        $keepingExistingProduct = $this->keepingExistingContract($selectedContractDuration);
        $arrProducts = $this->getWlrProducts(
            $intSdi,
            null,
            $includeNoWlr,
            $keepingExistingProduct);

        foreach ($arrProducts as $arrProduct) {
            $arrValidIds[] = $arrProduct['intNewWlrId'];
            $arrProductDetails[$arrProduct['intNewWlrId']] = $arrProduct;
        }

        if (!is_numeric($intNewWlrId) || !in_array($intNewWlrId, $arrValidIds)) {

            $this->addValidationError('intNewWlrId', 'INVALID');
        } else {

            $arrValidatedReturn['intNewWlrId'] = $intNewWlrId;
            $arrValidatedReturn['callerDisplay'] = $callerDisplay;
            $arrValidatedReturn['selectedContractDuration'] = $selectedContractDuration;
            $arrValidatedReturn['phoneContractChange'] = $keepingExistingProduct === false && $this->showContractOption;
            $arrValidatedReturn['arrSelectedWlr'] = array(
                    'strNewProduct' => $arrProductDetails[$intNewWlrId]['strProductName'],
                    'intNewCost'    => $arrProductDetails[$intNewWlrId]['intProductCost'],
                    'bolSplitPrice' => $arrProductDetails[$intNewWlrId]['bolSplitPrice'],
                    'intNewLineRentCost' => $arrProductDetails[$intNewWlrId]['intLineRentCost'],
                    'intNewCallPlanCost' => $arrProductDetails[$intNewWlrId]['intCallPlanCost'],
                    'lineRentalDiscountedPrice' => $arrProductDetails[$intNewWlrId]['lineRentalDiscountedPrice'],
                    'lineRentalDiscountAmount' => $arrProductDetails[$intNewWlrId]['lineRentalDiscountAmount']
                );

            // Checking if there is an ACTIVE LRS contract in the account and
            // setting the line rental price to zero if there is any
            if ($this->hasActiveLineRentalSaver()) {

                $arrValidatedReturn['arrSelectedWlr']['intNewLineRentCost'] = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    0
                );
            }
        }

        return $arrValidatedReturn;
    }

    /**
     * Validator for selected contract duration.
     *
     * There is a distinction between a contract length of 0 and a null contract length:
     * 0 is something explicitly chosen by the agent, meaning do not recontract.
     * Null indicates that no contract length has been selected.
     *
     * You should only be able to get past SelectBroadbandWorkplace without selecting a contract length
     * if the product doesn't allow the contract length to be selected. In that case, the
     * TermsAndConditions requirement will set the default contract length for that product.
     *
     * @param integer $selectedContractDuration Selected contract duration
     *
     * @return array
     */
    public function valSelectedContractDuration($selectedContractDuration)
    {
        $validatedReturn = array('selectedContractDuration' => null);

        if (isset($selectedContractDuration)) {
            $validatedReturn['selectedContractDuration'] = $selectedContractDuration;
        }

        return $validatedReturn;
    }

    /**
     * Optional Validation for the resetting of the contract
     *
     * @param boolean $bolContractResetWlr should the contract be reset?
     *
     * @return array validated stuff
     */
    public function valContractResetWlr($bolContractResetWlr)
    {
        $arrValidatedReturn = array();

        if ($bolContractResetWlr == 1) {

            $arrValidatedReturn['bolContractResetWlr'] = true;
        } else {

            $arrValidatedReturn['bolContractResetWlr'] = false;
        }

        return $arrValidatedReturn;
    }

    /**
     * For mocking :'(
     *
     * @param array $data data to encrypt
     *
     * @return EncryptMan_EncryptedVar encrypted data
     */
    protected function encryptVar(array $data)
    {
        return new EncryptMan_EncryptedVar($data);
    }

    /**
     * Inclusion of legacy files so we can mock them
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CComponent.inc';
    }

    /**
     * create a new instance to the WlrProductSorter class
     *
     * @return AccountChange_Product_WlrProductSorter
     */
    public function getWlrProductSorter()
    {
        $sorter = new AccountChange_Product_WlrProductSorter();
        return $sorter;
    }

    /**
     * create a new instance to the WlrProductFilter class
     *
     * @return AccountChange_Product_WlrProductFilter
     */
    public function getWlrProductFilter()
    {
        $wlrProductFilter = new AccountChange_Product_WlrProductFilter(
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE
        );
        return $wlrProductFilter;
    }

    /**
     * Get the selected broadband product from an application state variable
     *
     * @return array
     **/
    public function getSelectedBroadband()
    {
        return $this->getApplicationStateVariable('arrSelectedBroadband');
    }

    /**
     * Unit test wrapper for AccountChange_Controller::getMinAndMaxSpeedRanges.
     *
     * @return array
     */
    public function getMinAndMaxSpeedRanges()
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $objLineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');

        return AccountChange_Controller::getMinAndMaxSpeedRanges($objCoreService, $objLineCheckResult);
    }

    /**
     * Check if customer is keeping their existing contract - will be null if they're keeping their broadband product
     *
     * @param int|null $selectedContractDuration The contract duration the user has selected for their new products
     *
     * @return bool Are they keeping their existing contract?
     */
    private function keepingExistingContract($selectedContractDuration)
    {
        return empty($selectedContractDuration);
    }

    /**
     * You can't recontract to a shorter contract than the time remaining on the existing contract
     *
     * @return int
     */
    private function getMinimumContractDuration()
    {
        // the new product has auto contracts and is contracted
        $minimumContractDuration = 0;

        if ($this->isApplicationStateVariable('existingBroadband')) {
            $existingBroadband = $this->getApplicationStateVariable('existingBroadband');

            if (!empty($existingBroadband) && !empty($existingBroadband['remainingTime'])) {
                // if contracted already can't recontract to less than remaining duration
                $minimumContractDuration = $existingBroadband['remainingTime'];
            }
        }

        return $minimumContractDuration;
    }
}
