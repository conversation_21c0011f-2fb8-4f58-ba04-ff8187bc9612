<?php

/**
 * Class AccountChange_EcommerceAdapter
 *
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_EcommerceAdapter
{
    const GENERIC_SALES_JOURNEY_PATH =
        '/local/services/Symfony/PlusnetSalesJourneyApplication/vendor/bundles/Plusnet/GenericSalesJourneyBundle/';

    /**
     * @return void
     */
    public static function includeEcommerceHelpers()
    {
        if (!class_exists('Plusnet\GenericSalesJourneyBundle\Helper\EcommerceHelper')) {
            require_once(self::GENERIC_SALES_JOURNEY_PATH . 'Helper/EcommerceHelper.php');
        }

        if (!class_exists('Plusnet\GenericSalesJourneyBundle\Helper\EcommerceRuleHelperAccountChange')) {
            require_once(self::GENERIC_SALES_JOURNEY_PATH. 'Helper/EcommerceRuleHelperAccountChange.php');
        }
    }

    /**
     * @return Object
     */
    public static function createEcommerceHelper()
    {
        self::includeEcommerceHelpers();
        return (new Plusnet\GenericSalesJourneyBundle\Helper\EcommerceHelper('account change'));
    }

    /**
     * @param $productName
     * @param string $productPrice
     * @param string $productVariant
     * @param string $productCoupon
     * @param string $currentProduct
     * @param null $currentContractLength
     * @param null $daysIntoContract
     * @return string
     */
    public static function createEcomProduct($productName,
                                             $productPrice = '0.00',
                                             $productVariant = '',
                                             $productCoupon = '',
                                             $currentProduct = '',
                                             $currentContractLength = null,
                                             $daysIntoContract = null,
                                             $daysUntilExpiry = null)
    {
        $ecommerceHelper = self::createEcommerceHelper();

        // We have to mutate the contract length that is passed into this to be
        // compatible with the ecom schema
        switch ($currentContractLength) {
            case '12':
                $currentContractLength = '12 month contract';
                break;
            case '18':
                $currentContractLength = '18 month contract';
                break;
            case '0':
                $currentContractLength = 'No annual contract';
                break;
            default:
                $currentContractLength = '';
        }

        $ecomData = array(
            'id' => $ecommerceHelper->buildEcomId($productName, $productVariant),
            'name' => $ecommerceHelper->lookupProductNameFromAlias($productName),
            'category' => $ecommerceHelper->getEcomCategory($productName),
            'price' => $productPrice,
            'variant' => $productVariant,
            'coupon' => $productCoupon,
            'brand' => $ecommerceHelper->getEcomBrand($productName),
            'dimension51' => $currentProduct,
            'dimension50' => $ecommerceHelper->buildEcomId($currentProduct, $currentContractLength),
            'dimension49' => $currentContractLength,
            'metric5' => $daysIntoContract,
            'metric6' => $daysUntilExpiry
        );

        $ecomProductJson = $ecommerceHelper->getEcomJson($ecomData);
        $ecomRulesJson = $ecommerceHelper->getEcomRulesJson($ecomProductJson);

        $ecomProductAttribute = "data-ecom-product='" . $ecomProductJson . "'";
        $ecomRulesAttribute = "data-ecom-rules='" . $ecomRulesJson . "'";

        return $ecomProductAttribute . ' ' . $ecomRulesAttribute;
    }
}
