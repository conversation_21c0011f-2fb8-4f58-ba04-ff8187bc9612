<?php
use Plusnet\MultiVariantTesting\Emails\AutomatedEmailABTest;

/**
 * AutomatedEmail_AutomatedEmail
 *
 * @package
 * @version $id$
 * @copyright PlusNet
 * <AUTHOR> <<EMAIL>>
 * @license PHP License Version 3.01 {@link http://www.php.net/license/3_01.txt}
 */

class AutomatedEmail_AutomatedEmail
{
    /**
     * strSmartyCompilePath
     * Path to compiled templates
     *
     * @var string
     * @access private
     */
    private $strSmartyCompilePath;

    /**
     * strContentBaseDir
     * Path where the templates are
     *
     * @var string
     * @access private
     */
    private $strContentBaseDir;

    /**
     * strTransaction
     *
     * @var string
     * @access private
     */
    private $strTransaction;

    /**
     * objActor
     *
     * @var integer
     * @access private
     */
    private $objActor;

    /**
     * objSmarty
     *
     * @var mixed
     * @access private
     */
    private $objSmarty;

    /**
     * objEmail
     *
     * @var mixed
     * @access private
     */
    private $objEmail;

    /**
     * strEmailHandle
     *
     * @var string
     * @access private
     */
    private $strEmailHandle;

    /**
     * bolCustomizedTemplate
     *
     * @var bool
     * @access private
     */
    private $bolCustomizedTemplate = false;

    /**
     * arrRecipients
     *
     * @var array
     * @access private
     */
    private $arrRecipients = null;

    /**
     * strRecipients
     * @var string
     * @access private
     */
    private $strRecipients = null;

    /**
     * arrTemplateTypeMap
     *
     * @var array
     * @access private
     */
    private $arrTemplateExtensionMap = array(
        'text' => array('.txt', '.tpl'),
        'html' => array('.html', '.ihtml'),
    );

    /**
     * __construct
     *
     * @access public
     * @param Auth_BusinessActor $objActor       Auth_BusinessActor
     * @param I18n_Locale        $objLocale      I18n_Locale
     * @param Object             $strTransaction Transaction
     * @return void
     */
    public function __construct(
        Auth_BusinessActor $objActor,
        I18n_Locale $objLocale = null,
        $strTransaction = Db_Manager::DEFAULT_TRANSACTION
    ) {
    
        $strModuleRoot              = Auto_ClassLoader::getModulesRoot();
        $this->strTransaction       = $strTransaction;
        $this->strSmartyCompilePath = AutomatedEmail_TemplateConfig::getCompilePath()->getValue();
        $this->strContentBaseDir    = AutomatedEmail_TemplateConfig::getContentBasePath()->getValue();

        if (is_null($objLocale)) {
            $arrActorData = $this->getActorData($objActor->getActorId());

            if (empty($arrActorData)) {
            // Couldn't get the data from the database. If objActor is a valid
                // BusinessActor we really shouldn't be able to get this error, as this
                // represents an impossible situation. To be sure, check the actor ID
                // being used, and whether valid data exists for it. Also check the getActorData
                // query to make sure the query isn't somehow broken.
                $strMessage = 'Unable to retrieve Business Actor data';
                throw new AutomatedEmail_Exception($strMessage, AutomatedEmail_Exception::NO_ACTOR_DATA);
            }

            $objLocale = new I18n_Locale($arrActorData['strLocale']);
        }

        $arrErrors = array();
        $this->objActor  = $objActor;
        $this->objEmail  = new Email_Email();
        $this->objSmarty = new AutomatedEmail_LocalizedSmarty($objLocale, $arrErrors, $this->strSmartyCompilePath);
        $this->abTest = new AutomatedEmailABTest();
    }

    /**
     * getActorData
     *
     * @param int $intActorId Actor id
     * @access public
     * @return void
     */
    public function getActorData($intActorId)
    {
        $objAdaptor   = Db_Manager::getAdaptor('Auth', $this->strTransaction);
        $arrActorData = $objAdaptor->getActorData($intActorId);

        if (empty($arrActorData)) {
            return false;
        }

        return $arrActorData;
    }

    /**
     * prepareAutomatedEmail
     *
     * @param string $strEmailHandle Template handle
     * @param string $arrEmailData   Data for template
     * @access public
     * @return bool
     */
    public function prepareAutomatedEmail($strEmailHandle, $arrEmailData = array())
    {
        $this->strEmailHandle = $strEmailHandle;

        $abTestData = array();

        // Restrict AB testing to Plusnet for now
        if (isset($arrEmailData['isp']) && $arrEmailData['isp'] == 'Plusnet') {
            try {
                $abTestData = $this->getABTestData($this->strEmailHandle);
            } catch (Exception $e) {
                // The show must go on!
                error_log('Error caught in AutomatedEmailABTest: '.$e->getMessage());
            }
        }

        if (!empty($abTestData)) {
            // If there's test data merge it into $arrEmailData
            $arrEmailData = array_merge($arrEmailData, $abTestData);
            // Additionally, if this is a template test, update the handle
            if (isset($abTestData['testTemplate'])) {
                $this->strEmailHandle = $abTestData['testTemplate'];
            }
        }

        // Assign in our data so it's available to Smarty
        if (!empty($arrEmailData)) {
            $this->objSmarty->assign($arrEmailData);
        }

        // Expose for testing
        $this->arrEmailData = $arrEmailData;

        if (Partner_Partner::getDataset() != Partner_Partner::PARTNER_PLUSNET) {
            return $this->prepareStandardAutomatedEmail();
        }

        // check for reseller end user customisation
        $adaptor = Db_Manager::getAdaptor('CustomEmail', $this->strTransaction);
        $enduser_actor_id = $this->objActor->getActorId();
        $reseller_actor_id = $adaptor->getResellerActorIdFromEndUser($enduser_actor_id);

        if ($reseller_actor_id) {
            $actor = Auth_BusinessActor::get($reseller_actor_id);
            $template_store = new CustomEmail_TemplateStore($actor, $this->strTransaction);
            $template = $template_store->retrieve(new String($this->strEmailHandle));

            if ($template != false) {
                $recipient_store = new CustomEmail_RecipientStore($actor, new String($this->strEmailHandle));
                $this->prepareCustomAutomatedEmail($template, $recipient_store->retrieve());
                return;
            }
        }

        return $this->prepareStandardAutomatedEmail();
    }

    /**
     * prepareCustomAutomatedEmail
     *
     * <AUTHOR> Jones <<EMAIL>>
     * @param CustomEmail_Template       $customTemplate object
     * @param CustomEmail_RecipientArray $recipients     object
     * @access private
     * @return void
     */
    private function prepareCustomAutomatedEmail(CustomEmail_Template $customTemplate, CustomEmail_RecipientArray $recipients)
    {
        $this->arrRecipients = $recipients;
        $this->bolCustomizedTemplate = true;

        $subject = (string) $customTemplate->subject;
        if (!empty($subject)) {
            $this->objEmail->setSubject($subject);
        }

        $from = (string) $customTemplate->from;
        if (!empty($from)) {
            $this->objEmail->setSender($from);
        }

        $reply_to = (string) $customTemplate->reply_to;
        if (!empty($reply_to)) {
            $this->objEmail->setReplyTo($reply_to);
        }

        if (!empty($customTemplate->cc)) {
            foreach ($customTemplate->cc as $cc) {
                if ($cc instanceof Val_EmailAddress) {
                    $this->objEmail->addCcRecipient((string) $cc);
                }
            }
        }

        if (!empty($customTemplate->bcc)) {
            foreach ($customTemplate->bcc as $bcc) {
                if ($bcc instanceof Val_EmailAddress) {
                    $this->objEmail->addBccRecipient((string) $bcc);
                }
            }
        }

        $paths = $customTemplate->paths;
        $template = $paths[CustomEmail_TemplateStore::TEMPLATE_PATH];

        if (!file_exists($template)) {
            $strMessage = 'Custom email template does not exist';
            throw new AutomatedEmail_Exception($strMessage);
        }

        // This is a kind of repeat of Rys' functionality in CustomEmail_Template.
        $htmlBody = $this->objSmarty->fetch($template);
        $textBody = str_replace("<br />", "\n", $htmlBody);
            $textBody = strip_tags($textBody);

        $this->objEmail->setTextBody($textBody);

        if ($htmlBody != $textBody) {
            $this->objEmail->setHtmlBody($htmlBody);
        }
    }

    /**
     * prepareStandardAutomatedEmail
     *
     * @return void
     */
    private function prepareStandardAutomatedEmail()
    {
        $strPartner = Partner_Partner::getDataset();

        // If there are no custom templates then we want to follow the traditional process
        $objTreePosition    = VisTree_TreePosition::get($this->objActor->getActorId(), false, 0, $this->strTransaction);
        $arrVisTree         = $objTreePosition->getVisTree();
        $objTemplateManager = new VisTree_ContentTemplateManager($arrVisTree['strVisTreeHandle'], $strPartner);
        $arrTemplates       = $objTemplateManager->getTemplatesForLevel($this->strEmailHandle, $objTreePosition, $this->strContentBaseDir);

        // If still empty there are no templates to be had
        if (empty($arrTemplates)) {
            // The specified Email Handle could not be found anywhere in the tree. Is the
            // Email handle that was passed in spelled correctly? Is the supplied Business
            // Actor ID valid for the context of the Email Handle supplied?
            $strMessage = "Unable to locate email templates for handle '{$this->strEmailHandle}' within the context for the specified actor";
            throw new AutomatedEmail_Exception($strMessage, AutomatedEmail_Exception::NO_TEMPLATE_FOR_HANDLE);
        }

        // Sort them into text / html
        foreach ($arrTemplates as $strTemplate) {
            $ext = array();
            foreach ($this->arrTemplateExtensionMap as $type) {
                $ext[] = implode('|', $type);
            }
            $exts = implode('|', $ext);

            if (!preg_match("/^.*({$exts})\$/i", $strTemplate, $arrMatch)) {
                // The template found is not a valid email template. What file has it found, and
                // should it be in the email path? Is this a new type that needs to be added to
                // the above preg to handle as an email template?
                $strMessage = 'Templates located with the context for the specified actor do not appear to be valid email templates';
                throw new AutomatedEmail_Exception($strMessage, AutomatedEmail_Exception::INVALID_TEMPLATE_FOR_HANDLE);
            }

            $strExt     = $arrMatch[1];
            $strContent = $this->objSmarty->fetch($strTemplate);

            // Finally, Determine what kind of email it is and attach it to the emailer
            // in the appropriate way.
            if (in_array($strExt, $this->arrTemplateExtensionMap['text'])) {
                $this->objEmail->setTextBody(html_entity_decode($strContent));
            }

            if (in_array($strExt, $this->arrTemplateExtensionMap['html'])) {
                $this->objEmail->setHtmlBody($strContent);
            }
        } // foreach ($arrTemplates as $strTemplate)

        $strSubject = trim($this->objSmarty->getSubject());

        if (!empty($strSubject)) {
            $this->objEmail->setSubject($strSubject);
        } else {
            $this->objEmail->setSubject('Information about your service');
        }

        return $this->objEmail;
    }


    /**
     * getABTestData
     *
     * <AUTHOR> Bunker <<EMAIL>>
     * @param  string $emailHandle template handle
     * @access private
     * @return array
     */
    private function getABTestData($emailHandle)
    {
        return $this->abTest->provideTestData($emailHandle);
    }

    /**
     * setEmail - Required for mocking
     *
     * <AUTHOR> Jones <<EMAIL>>
     * @param Email_Email $objEmail Instace of Email_Email object
     * @access public
     * @return void
     */
    public function setEmail(Email_Email $objEmail)
    {
        $this->objEmail = $objEmail;
    }

    /**
     * setABTest - Required for mocking
     *
     * <AUTHOR> Bunker <<EMAIL>>
     * @param AutomatedEmailABTest $abTest Instance of ABTest object
     * @access public
     * @return void
     */
    public function setABTest(AutomatedEmailABTest $abTest)
    {
        $this->abTest = $abTest;
    }

    /**
     * addRecipient
     *
     * @param string $strRecipient Email recipient
     * @access public
     * @return void
     */
    public function addRecipient($strRecipient)
    {
        $this->strRecipients .= "$strRecipient, ";
        return $this->objEmail->addRecipient($strRecipient);
    }

    /**
     * setSender
     *
     * @param string $strSender Email sender
     * @access public
     * @return void
     */
    public function setSender($strSender)
    {
        return $this->objEmail->setSender($strSender);
    }

    /**
     * addAttachment
     *
     * @param string $strAttachment attachment
     * @param string $strMimeType   mime type
     * @access public
     * @return void
     */
    public function addAttachment($strAttachment, $strMimeType)
    {
        return $this->objEmail->addAttachment($strAttachment, $strMimeType);
    }

    /**
     * Wrapper for Email_Email::addShareAttachment
     *
     * @param mixed  $unkAppClass Appclass
     * @param string $strFunction Function
     * @param string $strFile     File
     * @param string $strPartner  Partner
     * @param string $strMimeType Mime type
     * @access public
     * @return boolean
     */
    public function addShareAttachment($unkAppClass, $strFunction, $strFile, $strPartner, $strMimeType)
    {
        return $this->objEmail->addShareAttachment($unkAppClass, $strFunction, $strFile, $strPartner, $strMimeType);
    }

    /**
     * Wrapper for Email_Email::addStaticAttachment
     *
     * @param  string $strFile     The filename to attach
     * @param  string $strMimeType The mime type
     * @param  string $strName     The name for attached file
     * @return void
     */
    public function addStaticAttachment($strFile, $strMimeType, $strName)
    {
        return $this->objEmail->addStaticAttachment($strFile, $strMimeType, $strName);
    }

    /**
     * getSubject
     *
     * @access public
     * @return void
     */
    public function getSubject()
    {
        return $this->objEmail->getSubject();
    }

    /**
     * getEmailHandle
     *
     * @access public
     * @return void
     */
    public function getEmailHandle()
    {
        return $this->strEmailHandle;
    }

    /**
     * getEmailData
     *
     * @access public
     * @return void
     */
    public function getEmailData()
    {
        return $this->arrEmailData;
    }

    /**
     * getHtmlBody
     *
     * @access public
     * @return void
     */
    public function getHtmlBody()
    {
        return $this->objEmail->getHtmlBody();
    }

    /**
     * getTextBody
     *
     * @access public
     * @return void
     */
    public function getTextBody()
    {
        return $this->objEmail->getTextBody();
    }

    /**
     * send
     *
     * @access public
     * @return void
     */
    public function send()
    {
        try {
            return $this->objEmail->send();
        }
        catch(Email_Exception $emailException) {
            error_log('Error sending email: '. $emailException->getMessage());
            return false;
        }
    }

    /**
     * isCustomizedTemplate
     * To be used after the template has been parsed, to determine recipients
     *
     * @access public
     * @return bool
     */
    public function isCustomizedTemplate()
    {
        return $this->bolCustomizedTemplate;
    }

    /**
     * isCustomizedTemplate
     * To be used after the template has been parsed, to determine recipients
     *
     * @access public
     * @return bool
     */
    public function getRecipients()
    {
        return $this->arrRecipients;
    }

    /**
     * unsetRecipients
     * To be used after the template has been parsed, to determine recipients
     *
     * @access public
     * @return bool
     */
    public function unsetRecipients()
    {
        $this->strRecipients = "";
        $this->objEmail->unsetRecipients();
    }

    /**
     * getRecipientsString
     * To be used after the template has been parsed, to determine recipients
     *
     * @access public
     * @return string
     */
    public function getRecipientsString()
    {
        return trim($this->strRecipients, ", ");
    }

    /**
     * Unset headers
     *
     * @return void
     */
    public function unsetHeaders()
    {
        $this->objEmail->unsetHeaders();
    }
}
