<?php
/**
 * AccountChange Homephone Requirement Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-24
 */
/**
 * AccountChange Homephone Requirement Test
 *
 * Test class for AccountChange_SelectHomephoneRequirement Requirement
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_SelectHomephoneRequirement_Test extends PHPUnit_Framework_TestCase
{
    /**
    * Test the applyProductIm Currently on with different data
    *
    * @covers AccountChange_SelectHomephoneRequirement::applyProductImCurrentlyOn
    *
    * @dataProvider provideDataForApplyProductImCurrentlyOn
    *
    * @return void
    */
    public function testApplyProductImCurrentlyOnWithData($products, $wlrProduct, $expected)
    {
        $requirement = $this->getMock(
            'AccountChange_SelectHomephoneRequirement',
            array('getApplicationStateVariable'),
            array()
        );

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'),
            array()
        );

        $controller->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnValue($wlrProduct));

        $requirement->setAppStateCallback($controller);

        $output = $requirement->applyProductImCurrentlyOn($products);
    }

    /**
    * Data provider for testApplyProductImCurrentlyOnWithData
    *
    * @return array
    */
    public static function provideDataForApplyProductImCurrentlyOn()
    {
        return array(
            array(array(), array(), array()),
            array(
                array(array('intServiceComponentID' => 1)),
                array('intOldWlrId' => 1),
                array(array('intServiceComponentID' => 1))
            ),
            array(
                array(),
                array('intOldWlrId' => 1, 'strContractHandle' => 'contract', 'strProductName' => 'product'),
                array(array('intOldWlrId' => 1, 'strContractHandle' => 'contract', 'strProductName' => 'product'))
            )
        );
    }

    /**
     * Provide data for testWlrProductFilteringPerformsExpectedActions
     *
     * @return array
     **/
    public function provideDataForWlrProductFilteringPerformsExpectedActions()
    {
        $inputProducts = array(
            array('ProductName' => 'Product 1 (12 Month Contract)'),
            array('ProductName' => 'Product 2 (12 Month Contract)'),
            array('ProductName' => 'Product 3 (24 Month Contract)'),
            array('ProductName' => 'Product 4 (12 Month Contract)'),
            array('ProductName' => 'Product 5 (24 Month Contract)'),
            array('ProductName' => 'Product 6 (No Contract)'),
            array('ProductName' => 'Product 7 (No Contract)'),
            array('ProductName' => 'Product 8'),
        );

        $expected12MonthContracts[0] = array('ProductName' => 'Product 1 (12 Month Contract)');
        $expected12MonthContracts[1] = array('ProductName' => 'Product 2 (12 Month Contract)');
        $expected12MonthContracts[3] = array('ProductName' => 'Product 4 (12 Month Contract)');

        $expected24MonthContracts[2] = array('ProductName' => 'Product 3 (24 Month Contract)');
        $expected24MonthContracts[4] = array('ProductName' => 'Product 5 (24 Month Contract)');

        $expectedNoContracts[5] = array('ProductName' => 'Product 6 (No Contract)');
        $expectedNoContracts[6] = array('ProductName' => 'Product 7 (No Contract)');
        $expectedNoContracts[7] = array('ProductName' => 'Product 8');


        return array(
            array(12, $inputProducts, $expected12MonthContracts),
            array(24, $inputProducts, $expected24MonthContracts),
            array(null, $inputProducts, $expectedNoContracts),
            array(0, $inputProducts, $expectedNoContracts),
        );

    }

    /**
     * Test that the filtering for products based on contract length ties in the length of contracts
     * on WLR products to the selected ADSL product.
     *
     * @param int   $adslContractLength The contract length of the selected adsl product
     * @param array $allProducts        An array of unfiltered products
     * @param array $expectedProducts   The expected filtered product list
     *
     * @dataProvider provideDataForWlrProductFilteringPerformsExpectedActions
     * @covers       AccountChange_SelectHomephoneRequirement::filterWlrProductsBasedOnContractLength
     *
     * @return void
     **/
    public function testWlrProductFilteringPerformsExpectedActions($adslContractLength, $allProducts, $expectedProducts)
    {
        $requirement = $this->getMock(
            'AccountChange_SelectHomephoneRequirement',
            array('getProductDefaultContractLength'),
            array()
        );

        $requirement
            ->expects($this->once())
            ->method('getProductDefaultContractLength')
            ->with(1234)
            ->will($this->returnValue($adslContractLength));

        $wlrProductFilter = new AccountChange_Product_WlrProductFilter();

        $filteredActual = $requirement->filterWlrProductsBasedOnContractLength($allProducts, 1234, $wlrProductFilter);
        $this->assertEquals($filteredActual, $expectedProducts);
    }

    /**
     * @return void
     */
    public function testShouldNotFilterBizGoLargeProducts()
    {
        $selectHomephoneRequirement = new AccountChange_SelectHomephoneRequirement();

        $serviceDefinitionId = 1;
        $nonBizGoLargeProduct = 12345;
        $bizGoLargeProduct = 54321;

        $products = [
            ['intServiceComponentID' => $nonBizGoLargeProduct, 'ProductName' => 'Non BGL'],
            ['intServiceComponentID' => $bizGoLargeProduct, 'ProductName' => 'BGL'],
        ];

        $filter = Mockery::mock('AccountChange_Product_WlrProductFilter');
        $filter->shouldReceive('getProductFamily')->with($nonBizGoLargeProduct)->andReturn(1);
        $filter->shouldReceive('getProductFamily')->with($bizGoLargeProduct)->andReturn(3);

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getContractDefinitionLengthFromSdi')->with($serviceDefinitionId)->andReturn(12);
        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $result = $selectHomephoneRequirement->filterWlrProductsBasedOnContractLength(
            $products,
            $serviceDefinitionId,
            $filter
        );

        $this->assertCount(1, $result);
        $this->assertEquals([1 => ['intServiceComponentID' => $bizGoLargeProduct, 'ProductName' => 'BGL']], $result);
    }

    /**
     * @param $input
     * @param $expected
     *
     * @return void
     * @dataProvider dataProviderValCallerDisplay
     */
    public function testValCallerDisplay($input, $expected)
    {
        $sut = new AccountChange_SelectHomephoneRequirement();
        $this->assertEquals($expected, $sut->valCallerDisplay($input));
    }

    public function dataProviderValCallerDisplay()
    {
        return [
            [null, array('callerDisplay' => false)],
            [false, array('callerDisplay' => false)],
            [0, array('callerDisplay' => false)],
            ['', array('callerDisplay' => false)],
            [true, array('callerDisplay' => true)],
            ['on', array('callerDisplay' => true)],
        ];
    }
}
