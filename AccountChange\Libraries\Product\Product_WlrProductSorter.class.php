<?php

/**
 * File Product_WlrProductSorter
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://jira.internal.plus.net/browse/PRI-281
 */

/**
 * Context:
 *
 * Because of large list of products shown for customers with pre 2013 WLR
 * products, we need to change the sorting logic to group these together
 * sensibly
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://jira.internal.plus.net/browse/PRI-281
 */
class AccountChange_Product_WlrProductSorter extends AccountChange_Product_WlrProductBase
{

    /**
     * Sort the products
     *
     * Current product always goes at the bottom
     *
     * Product are grouped together by their line rental price ascending
     * Then sorted by total monthly cost ascending
     * Mobile bolt-on products should be next to their non-bolt-on products
     *
     * @param array $arrProducts All the WLR products in the database
     * @param int   $intOldId    Current WLR product
     *
     * @return array sorted products ready for display
     */
    public function sort(array $arrProducts, $intOldId)
    {
        // sort by intCallPlanCost
        $phoneHelper = $this->getPhoneHelper();
        $serviceComponentBoltOnMappings = $phoneHelper->getServiceComponentIdToToBoltOnMap();
        usort($arrProducts, array($this, 'sortCallback'));
        $arrProducts = $this->boltOnMobiles($arrProducts, $serviceComponentBoltOnMappings);

        // Move the old product to the end
        $oldProductKey = null;
        $arrOldProduct = null;
        foreach ($arrProducts as $key => $product) {
            if ($product['intNewWlrId'] == $intOldId) {
                $oldProductKey = $key;
                $arrOldProduct = $product;
                break;
            }
        }
        unset($arrProducts[$oldProductKey]);
        if ($arrOldProduct !== null) {
            $arrProducts[] = $arrOldProduct;
        }

        return $arrProducts;
    }

    /**
     * @return AccountChange_PhoneProductHelper
     */
    protected function getPhoneHelper()
    {
        return new AccountChange_PhoneProductHelper();
    }

    /**
     * Attach mobile bolt-ons to the products they bolt on to
     *
     * @param array $arrProducts                    products list
     * @param array $serviceComponentBoltOnMappings The mapping between service components and the bolt on versions.
     *
     * @return array The new sorted array with bolt-ons inserted
     */
    protected function boltOnMobiles($arrProducts, $serviceComponentBoltOnMappings)
    {
        $newArray = array();
        foreach ($arrProducts as $product) {
            $boltOnId = null;
            $bolIsBoltOn = false;
            if (array_key_exists('intNewWlrId', $product)) {
                $wlrId = (int) $product['intNewWlrId'];
                if (array_key_exists($wlrId, $serviceComponentBoltOnMappings)) {
                    $boltOnId = $serviceComponentBoltOnMappings[$wlrId];
                } elseif (in_array($wlrId, $serviceComponentBoltOnMappings)) {
                    $bolIsBoltOn = true;
                }
            }

            if ($bolIsBoltOn) {
                $boltOnId = (int) $product['intNewWlrId'];
                $parentId = array_flip($serviceComponentBoltOnMappings);
                $parentId = $parentId[$boltOnId];

                if (!$this->productExists($parentId, $arrProducts)) {
                    $newArray[] = $this->getProduct($boltOnId, $arrProducts);
                }
            }

            if (!$bolIsBoltOn) {
                $newArray[] = $product;
                if ($boltOnId != null) {
                    foreach ($arrProducts as $boltOn) {
                        if ($boltOn['intNewWlrId'] != $boltOnId) {
                            continue;
                        }
                        $newArray[] = $boltOn;
                        break;
                    }
                }
            }
        }
        return $newArray;
    }

    /**
     * Sort by:
     *      Product family with the newest product family appearing at the front of the list.
     *      Line rental ascending
     *      Call plan ascending
     *      Product name ascending
     *
     * @param array $a first thing to sort
     * @param array $b second thing to sort
     *
     * @return integer
     */
    protected function sortCallback($a, $b)
    {
        if ($this->familiesMatch($a['intNewWlrId'], self::DEFAULT_PRODUCT_FAMILY) && !$this->familiesMatch($b['intNewWlrId'], self::DEFAULT_PRODUCT_FAMILY)) {
            return -1;
        }

        if ($this->familiesMatch($b['intNewWlrId'], self::DEFAULT_PRODUCT_FAMILY) && !$this->familiesMatch($a['intNewWlrId'], self::DEFAULT_PRODUCT_FAMILY)) {
            return 1;
        }

        // shouldn't happen
        if (!isset($a['intLineRentCost']) || !isset($b['intLineRentCost'])) {
            return -1;
        }

        $lineRentalA = $a['intLineRentCost']->toDecimal();
        $lineRentalB = $b['intLineRentCost']->toDecimal();

        if ($lineRentalA != $lineRentalB) {
            return ($lineRentalA < $lineRentalB) ? -1 : 1;
        }

        // shouldn't happen
        if (!isset($a['intCallPlanCost']) || !isset($b['intCallPlanCost'])) {
            return -1;
        }

        $intCallPlanCostA = $a['intCallPlanCost']->toDecimal();
        $intCallPlanCostB = $b['intCallPlanCost']->toDecimal();

        if ($intCallPlanCostA != $intCallPlanCostB) {
            return ($intCallPlanCostA < $intCallPlanCostB) ? -1 : 1;
        }

        return $a['intNewWlrId']-$b['intNewWlrId'];
    }

    /**
     * Returns true if a product exists in the products array
     *
     * @param int   $productId   intNewWlrId
     * @param array $arrProducts products
     *
     * @return bool
     */
    private function productExists($productId, $arrProducts)
    {
        foreach ($arrProducts as $product) {
            if ($product['intNewWlrId'] == $productId) {
                return true;
            }
        }
        return false;
    }

    /**
     * Returns a product, or false if no product found
     *
     * @param int   $productId   intNewWlrId
     * @param array $arrProducts products
     *
     * @return bool
     */
    private function getProduct($productId, $arrProducts)
    {
        foreach ($arrProducts as $product) {
            if ($product['intNewWlrId'] == $productId) {
                return $product;
            }
        }
        return false;
    }
}
