<?php

/**
 * Test class for AccountChange_LineChecker
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 */

class AccountChange_LineChecker_Test extends PHPUnit_Framework_TestCase
{
    public function testAgentCanPerformPostcodeStepDown()
    {
        $cli = "***********";
        $thoroughfareNumber = 2;
        $postcode = "S12GU";
        $objCoreService = null;
        $objLineCheckResult = null;

        $mockLineChecker = $this->getMock(
            "AccountChange_LineChecker",
            array(
                "getLineCheckResult",
                "insertPendingServiceLineData")
        );

        $mockBadResponse = $this->getMock(
            "LineCheck_Result",
            array("getErrorId")
        );

        $mockGoodResponse = $this->getMock(
            "LineCheck_Result",
            array("getErrorId")
        );

        $this->setMockBusinessActor("PLUSNET_STAFF", 1);

        $mockBadResponse->expects($this->exactly(2))
            ->method("getErrorId")
            ->willReturn(1);

        $mockGoodResponse->expects($this->once())
            ->method("getErrorId")
            ->willReturn(0);

        $mockLineChecker->expects($this->once())
            ->method("insertPendingServiceLineData")
            ->willReturn('');

        $mockLineChecker->expects($this->exactly(3))
            ->method("getLineCheckResult")
            ->withConsecutive(
                [LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO,
                $cli,
                $objCoreService,
                $objLineCheckResult],
                [LineCheck_RequestManager::LINE_CHECK_TYPE_ADDRESS,
                array(
                    "thoroughfareNumber" => $thoroughfareNumber,
                    "postcode" => $postcode
                ),
                $objCoreService,
                $objLineCheckResult],
                [LineCheck_RequestManager::LINE_CHECK_TYPE_POSTCODE,
                $postcode,
                $objCoreService,
                $objLineCheckResult]
            )
            ->willReturnOnConsecutiveCalls($mockBadResponse, $mockBadResponse, $mockGoodResponse);

        $mockLineChecker->performLineCheck(
            $cli,
            $postcode,
            "",
            "",
            $thoroughfareNumber,
            false,
            $objCoreService,
            $objLineCheckResult
        );
    }

    public function testPostCodeStepDownNotPerformedWhenNotAgent()
    {
        $cli = "***********";
        $thoroughfareNumber = 2;
        $postcode = "S12GU";
        $objCoreService = null;
        $objLineCheckResult = null;

        $mockLineChecker = $this->getMock(
            "AccountChange_LineChecker",
            array(
                "getLineCheckResult",
                "insertPendingServiceLineData")
        );

        $mockBadResponse = $this->getMock(
            "LineCheck_Result",
            array("getErrorId")
        );

        $mockGoodResponse = $this->getMock(
            "LineCheck_Result",
            array("getErrorId")
        );

        $mockBadResponse->expects($this->once())
            ->method("getErrorId")
            ->willReturn(1);

        $mockGoodResponse->expects($this->exactly(2))
            ->method("getErrorId")
            ->willReturn(0);

        $mockLineChecker->expects($this->once())
            ->method("insertPendingServiceLineData")
            ->willReturn('');

        $mockLineChecker->expects($this->exactly(2))
            ->method("getLineCheckResult")
            ->withConsecutive(
                [LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO,
                $cli,
                $objCoreService,
                $objLineCheckResult],
                [LineCheck_RequestManager::LINE_CHECK_TYPE_ADDRESS,
                array(
                    "thoroughfareNumber" => $thoroughfareNumber,
                    "postcode" => $postcode
                ),
                $objCoreService,
                $objLineCheckResult]
            )
            ->willReturnOnConsecutiveCalls($mockBadResponse, $mockGoodResponse);

        $mockLineChecker->performLineCheck(
            $cli,
            $postcode,
            "",
            "",
            $thoroughfareNumber,
            false,
            $objCoreService,
            $objLineCheckResult
        );
    }

    public function testLineCheckResultExceptionThrownWhenLineCheckFails()
    {
        $this->setMockBusinessActor("NOT_STAFF", 1);

        $mockLineChecker = $this->getMock(
            "AccountChange_LineChecker",
            array(
                "getLineCheckResult")
        );

        $mockBadResponse = $this->getMock(
            "LineCheck_Result",
            array("getErrorId")
        );

        $mockBadResponse->expects($this->exactly(4))
            ->method("getErrorId")
            ->willReturn(1);

        $mockLineChecker->expects($this->exactly(2))
            ->method("getLineCheckResult")
            ->willReturn($mockBadResponse);

        $this->setExpectedException(AccountChange_LineCheckResultException::class);

        $mockLineChecker->performLineCheck(
            "",
            "",
            "",
            "",
            "",
            false,
            "",
            ""
        );
    }

/*
 * Sets the mock business actor object
 *
 */
    private function setMockBusinessActor($userType, $actorCount)
    {
        $objMockAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAdaptor->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getWrapperBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objMockAdaptor, Db_Manager::DEFAULT_TRANSACTION);

        $objBusinessActor = Auth_BusinessActor::get(1);
        $objBusinessActor->setRealm('workplace.plus.net');
        $objBusinessActor->setUserType($userType);
        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->exactly($actorCount))
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getWrapperBusinessActorData()
    {
        return array(
            array(
                "intActorId"        => 1,
                "strUsername"       => "testuser",
                "strRealm"          => "plusnet",
                "strUserType"       => "",
                "strExternalUserId" => "12345"
            )
        );
    }
}
