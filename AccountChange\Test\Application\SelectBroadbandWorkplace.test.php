<?php
/**
 * Account Change Select Broadband Workplace Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */

/**
 * Account Change Select Broadband Workplace Test
 *
 * Testing class for AccountChange_SelectBroadbandWorkplace requirement
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_SelectBroadbandWorkplaceTest extends PHPUnit_Framework_TestCase
{
    const PRODUCT_FAMILY = 'ProductFamily';

    /**
     * Current data being tested with
     *
     * @var array
     */
    private $currentData;

    /**
     * Set up
     *
     * @return void
     */
    public function setUp()
    {
        $mockPersistenceAdaptor = $this->getMockBuilder(
            'Plusnet\Feature\FeatureTogglePersistence'
        )
            ->setMethods(array('getToggleByName'))
            ->getMockForAbstractClass();

        $mockPersistenceAdaptor->expects($this->any())
            ->method('getToggleByName')
            ->will(
                $this->returnValue(
                    array(
                        'onDateTime' => '9999-12-01 00:00:00',
                        'offDateTime' => '0000-00-00 00:00:00'
                    )
                )
            );

        Plusnet\Feature\FeatureToggleManager::instance($mockPersistenceAdaptor);
    }

    /**
     * Tear down
     *
     * @return void
     */
    public function tearDown()
    {
        BusTier_BusTier::reset();
        Plusnet\Feature\FeatureToggleManager::reset();
        Db_Manager::reset();
    }

    /**
     * Test that the validator runs correctly
     *
     * @param array $components Array of components
     * @param array $output     Expected output
     *
     * @dataProvider provideValComponentsNotToKeepData
     *
     * @return void
     */
    public function testValComponentsNotToKeepWithData($components, $output)
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $requirement */
        $requirement = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('addValidationError'),
            array()
        );

        $requirement
            ->expects($this->any())
            ->method('addValidationError');

        $result = $requirement->valComponentsNotToKeep($components);

        $this->assertArrayHasKey('arrComponentsNotToKeep', $result);
        $this->assertEquals($output, $result['arrComponentsNotToKeep']);
    }

    /**
     * Data provider for testValComponentsNotToKeepWithData
     *
     * @return array
     */
    public static function provideValComponentsNotToKeepData()
    {
        return array(
            array(array(), array()),
            array(array('2w3'), array()),
            array(array(299, 300), array(299, 300)),
        );
    }

    /**
     * Test that the validator runs correctly and adds validation error if data is naughty
     *
     * @return void
     */
    public function testValComponentsNotToKeepAddsValidationErrorWhenComponentIsNotADigit()
    {
        $components = array ('e');

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $requirement */
        $requirement = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('addValidationError'),
            array()
        );

        $requirement
            ->expects($this->any())
            ->method('addValidationError');

        $result = $requirement->valComponentsNotToKeep($components);
        $errors = $requirement->getValidationErrors();

        $this->assertArrayHasKey('arrComponentsNotToKeep', $result);
        $this->assertEquals(array(), $result['arrComponentsNotToKeep']);

        $this->assertArrayHasKey('arrComponentsNotToKeep', $errors);
        $this->assertArrayHasKey('INCORRECT_ID', $errors['arrComponentsNotToKeep']);
    }


    /**
     * Provide data for testDescribeWithoutNewServiceDefinition
     *
     * @return array
     **/
    public function provideDataForDescribeWithoutNewServiceDefinition()
    {
        $provService21Cn = array(
            'vchSupplierPlatform' => 'BT21CN'
        );

        $provService = array(
            'vchSupplierPlatform' => 'MAX'
        );

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array(
                'isValidResult'
            ),
            [],
            '',
            false
        );

        $lineCheckResult->expects($this->any())
            ->method('isValidResult')
            ->will($this->returnValue(true));

        AccountChange_Registry::instance()->setEntry('objLineCheckResult', $lineCheckResult);

        return array(
            array($provService21Cn, 'Adsl2', $this->getBroadbandLegacyVariants()),
            array($provService, 'Adsl', $this->getBroadbandLegacyVariants()),
            array($provService21Cn, 'Adsl2', $this->getBroadbandLegacyVariants(), 'salesoffer'),
            array($provService, 'Adsl', $this->getBroadbandLegacyVariants(), 'salesoffer'),
        );
    }

    /**
     * Tests that describe returns expected data when the new service definition is
     * the same as the old one
     *
     * @param array  $provisionedService    Provisioned service
     * @param string $expectedProvisionedOn Expected prov platform based on above
     * @param array  $broadbandVariants     Data returned from broadband variant service
     * @param string $promoCode             Promo code
     *
     * @dataProvider provideDataForDescribeWithoutNewServiceDefinition
     *
     * @return void
     **/
    public function testDescribeWithoutNewServiceDefinition(
        $provisionedService,
        $expectedProvisionedOn,
        $broadbandVariants,
        $promoCode = ''
    ) {

        $oldSdi = 6755;
        $cli = '***********';
        $serviceId = 1234566;

        $this->mockProductChangePlanClient($broadbandVariants);
        $coreService = $this->mockCoreService($serviceId, $cli);
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();
        $controller = $this->mockAccountChangeController($oldSdi, $coreService, $lineCheckResult, $wlrProduct);
        $productFamily = $this->mockLegacyProductFamily();
        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $this->getAllTestProducts()
        );

        $validatedData = array('promoCode' => $promoCode);
        $actual = $mockRequirement->describe($validatedData);
        $expected = array(
            'intSelectedSdi' => 6755,
            'intSelectedTariffId' => '',
            'arrGroupedProducts' => array(
                'Current Product' => array($this->getLegacyProduct()),
                'DualPlay' => array($this->getDualPlayProduct()),
                'Solus' => array($this->getSolusProduct()),
                'JohnLewis Products' => array($this->getJohnLewisProduct())
            ),
            'bolShowDialupLink' => false,
            'bolShowComponents' => false,
            'objLinecheckResult' => $lineCheckResult,
            'strTelephoneNo' => '***********',
            'strProvisionOn' => $expectedProvisionedOn,
            'oldProductFibre' => true,
            'promoCode' => $promoCode,
            'availableContractDurations' => null,
            'filterProductFamily' => null,
            'hasHomephone' => false,
            'isResidentialSolusDualPlayProduct' => false,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'bolHousemove' => null,
            'fromSelectBroadbandWorkplace' => true,
            'promoCodeValidationErrors' => AccountChange_SelectBroadbandWorkplace::PROMO_CODE_VALIDATION_ERRORS,
        );

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test describe when currently on a legacy product
     *
     * @return void
     */
    public function testDescribeCurrentlyOnLegacyProduct()
    {
        // The only available product is the current legacy product
        $currentSdi = 6755;
        $cli = '***********';
        $serviceId = 1234566;
        $provisionedService = array(
            'vchSupplierPlatform' => 'MAX'
        );
        $expectedProvisionedOn = 'Adsl';
        $broadbandVariants = array();
        $promoCode = '';

        $this->mockProductChangePlanClient($broadbandVariants);
        $coreService = $this->mockCoreService($serviceId, $cli);
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();
        $controller = $this->mockAccountChangeController($currentSdi, $coreService, $lineCheckResult, $wlrProduct);
        $productFamily = $this-> mockLegacyProductFamily();
        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $this->getAllTestProducts()
        );

        $validatedData = array('promoCode' => '');
        $actual = $mockRequirement->describe($validatedData);
        $expected = array(
            'intSelectedSdi' => 6755,
            'intSelectedTariffId' => '',
            'arrGroupedProducts' => array(
                'Current Product' => array($this->getLegacyProduct()),
                'DualPlay' => array($this->getDualPlayProduct()),
                'Solus' => array($this->getSolusProduct()),
                'JohnLewis Products' => array($this->getJohnLewisProduct())
            ),
            'bolShowDialupLink' => false,
            'bolShowComponents' => false,
            'objLinecheckResult' => $lineCheckResult,
            'strTelephoneNo' => '***********',
            'strProvisionOn' => $expectedProvisionedOn,
            'oldProductFibre' => true,
            'promoCode' => $promoCode,
            'availableContractDurations' => null,
            'filterProductFamily' => null,
            'hasHomephone' => false,
            'isResidentialSolusDualPlayProduct' => false,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'bolHousemove' => null,
            'fromSelectBroadbandWorkplace' => true,
            'promoCodeValidationErrors' => AccountChange_SelectBroadbandWorkplace::PROMO_CODE_VALIDATION_ERRORS,

        );
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test describe when currently on a solus product
     *
     * @return void
     */
    public function testDescribeCurrentlyOnSolusProduct()
    {
        $currentSdi = 6842;
        $cli = '***********';
        $serviceId = 1234566;
        $provisionedService = array(
            'vchSupplierPlatform' => 'MAX'
        );
        $expectedProvisionedOn = 'Adsl';
        $broadbandVariants = $this->getBroadbandSolusDualPlayVariants();
        $promoCode = '';

        $this->mockProductChangePlanClient($broadbandVariants);
        $coreService = $this->mockCoreService($serviceId, $cli);
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();
        $controller = $this->mockAccountChangeController($currentSdi, $coreService, $lineCheckResult, $wlrProduct);
        $productFamily = $this-> mockSolusProductFamily();
        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $this->getAllTestProducts()
        );

        $validatedData = array('promoCode' => '');
        $actual = $mockRequirement->describe($validatedData);
        $expected = array(
            'intSelectedSdi' => 6842,
            'intSelectedTariffId' => '',
            'arrGroupedProducts' => array(
                'Current Solus Product' => array($this->getSolusProduct()),
                'Current DualPlay Product' => array($this->getDualPlayProduct()),
                'JohnLewis Products' => array($this->getJohnLewisProduct())
            ),
            'bolShowDialupLink' => false,
            'bolShowComponents' => false,
            'objLinecheckResult' => $lineCheckResult,
            'strTelephoneNo' => '***********',
            'strProvisionOn' => $expectedProvisionedOn,
            'oldProductFibre' => true,
            'promoCode' => $promoCode,
            'availableContractDurations' => null,
            'filterProductFamily' => null,
            'hasHomephone' => false,
            'isResidentialSolusDualPlayProduct' => true,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'bolHousemove' => null,
            'fromSelectBroadbandWorkplace' => true,
            'promoCodeValidationErrors' => AccountChange_SelectBroadbandWorkplace::PROMO_CODE_VALIDATION_ERRORS,
        );
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test describe when currently on a dual play product
     *
     * @return void
     */
    public function testDescribeCurrentlyOnDualPlayProduct()
    {
        $currentSdi = 6841;
        $cli = '***********';
        $serviceId = 1234566;
        $provisionedService = array(
            'vchSupplierPlatform' => 'MAX'
        );
        $expectedProvisionedOn = 'Adsl';
        $broadbandVariants = $this->getBroadbandSolusDualPlayVariants();
        $promoCode = '';

        $this->mockProductChangePlanClient($broadbandVariants);
        $coreService = $this->mockCoreService($serviceId, $cli);
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();
        $controller = $this->mockAccountChangeController($currentSdi, $coreService, $lineCheckResult, $wlrProduct);
        $productFamily = $this->mockDualPlayProductFamily();
        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $this->getAllTestProducts()
        );

        $validatedData = array('promoCode' => '');
        $actual = $mockRequirement->describe($validatedData);
        $expected = array(
            'intSelectedSdi' => 6841,
            'intSelectedTariffId' => '',
            'arrGroupedProducts' => array(
                'Current DualPlay Product' => array($this->getDualPlayProduct()),
                'JohnLewis Products' => array($this->getJohnLewisProduct())
            ),
            'bolShowDialupLink' => false,
            'bolShowComponents' => false,
            'objLinecheckResult' => $lineCheckResult,
            'strTelephoneNo' => '***********',
            'strProvisionOn' => $expectedProvisionedOn,
            'oldProductFibre' => true,
            'promoCode' => $promoCode,
            'availableContractDurations' => null,
            'filterProductFamily' => null,
            'hasHomephone' => false,
            'isResidentialSolusDualPlayProduct' => true,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'bolHousemove' => null,
            'fromSelectBroadbandWorkplace' => true,
            'promoCodeValidationErrors' => AccountChange_SelectBroadbandWorkplace::PROMO_CODE_VALIDATION_ERRORS,
        );
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider displayHardwareMessageProvider
     *
     * @return void
     */
    public function testDescribeSetsShowHardwareMessageCorrectly(
        $shouldUseHelper,
        $shouldShowHardwarePage,
        $shouldDisplayHarwdareMessage,
        $expectedResult
    ) {
        $currentSdi = 6841;
        $newSdi = 9876;
        $cli = '***********';
        $bolHousemove = false;
        $isRecontract = true;
        $serviceId = 1234566;
        $provisionedService = array(
            'vchSupplierPlatform' => 'MAX'
        );

        $broadbandVariants = $this->getBroadbandSolusDualPlayVariants();
        $this->mockProductChangePlanClient($broadbandVariants);
        $coreService = $this->mockCoreService($serviceId, $cli);
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );
        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->onConsecutiveCalls(
                    $coreService,
                    $newSdi,
                    $bolHousemove,
                    $isRecontract,
                    $currentSdi,
                    $coreService,
                    $lineCheckResult,
                    $wlrProduct
                )
            );

        $productFamily = $this->mockDualPlayProductFamily();

        $mockHelper = $this->getMock(
            'AccountChange_HardwareRequirementHelper',
            array('shouldUseHelper','shouldDisplayHardwareMessage','shouldShowHardwarePage'),
            array(),
            '',
            false
        );

        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $this->getAllTestProducts()
        );

        $mockRequirement->expects($this->once())
            ->method('getHardwareHelper')
            ->willReturn($mockHelper);

        $mockHelper->expects($this->exactly(2))
            ->method('shouldUseHelper')
            ->willReturn($shouldUseHelper);

        $mockHelper->expects($this->once())
            ->method('shouldShowHardwarePage')
            ->willReturn($shouldShowHardwarePage);

        $mockHelper->expects($this->once())
            ->method('shouldDisplayHardwareMessage')
            ->willReturn($shouldDisplayHarwdareMessage);

        $validatedData = array('promoCode' => '');
        $mockRequirement->isHardwareRequirementNeeded();
        $actual = $mockRequirement->describe($validatedData);

        $this->assertTrue($actual['displayHardwareMessage'] === $expectedResult);
    }


    /**
     * Test describe when business BGL products are available
     *
     * @return void
     */
    public function testDescribeCurrentlyOnBizGoLargeProduct()
    {
        $mockDbAdaptor = Mockery::mock(Db_Adaptor::class);
        $mockDbAdaptor->shouldReceive('getProductVariant')
            ->with(ProductFamily_BizGoLarge::PRODUCT_FAMILY_HANDLE)
            ->once()
            ->andReturn(array(array(
                'serviceDefinitionId' => 98765,
                'variant' => ProductFamily_BizGoLarge::BUSINESS_ADSL_VARIANT)));
        Db_Manager::setAdaptor(self::PRODUCT_FAMILY, $mockDbAdaptor);

        $this->mockProductChangePlanClient(array(
            'vchSupplierPlatform' => ProductFamily_BizGoLarge::BUSINESS_ADSL_VARIANT
        ));

        $currentSdi = 6841;
        $cli = '***********';
        $serviceId = 1234566;
        $provisionedService = array(
            'vchSupplierPlatform' => 'MAX'
        );
        $coreService = $this->mockCoreService($serviceId, $cli);
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();
        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );
        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->onConsecutiveCalls(
                    $currentSdi,
                    $coreService,
                    $lineCheckResult,
                    $wlrProduct,
                    false,
                    true
                )
            );
        $controller->expects(self::any())
        ->method('isApplicationStateVar')
        ->willReturn(true);

        $allProducts = $this->getAllTestProducts();
        $productFamily = new ProductFamily_BizGoLarge(98765);
        $bizGoLargeProduct = $this->getBizGoLargeProduct($productFamily);
        $allProducts['98765'] = $bizGoLargeProduct;

        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $allProducts
        );

        $validatedData = array('promoCode' => '');
        $actual = $mockRequirement->describe($validatedData);
        $expected = array(
            'intSelectedSdi' => 6841,
            'intSelectedTariffId' => '',
            'arrGroupedProducts' => array(
                'DualPlay' => array($this->getBizGoLargeProduct($productFamily)),
                'Current Product' => array(null)
            ),
            'bolShowDialupLink' => false,
            'bolShowComponents' => false,
            'objLinecheckResult' => $lineCheckResult,
            'strTelephoneNo' => '***********',
            'strProvisionOn' => 'Adsl',
            'oldProductFibre' => true,
            'promoCode' => '',
            'availableContractDurations' => null,
            'filterProductFamily' => null,
            'hasHomephone' => false,
            'isResidentialSolusDualPlayProduct' => false,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'bolHousemove' => false,
            'fromSelectBroadbandWorkplace' => true,
            'promoCodeValidationErrors' => AccountChange_SelectBroadbandWorkplace::PROMO_CODE_VALIDATION_ERRORS,
        );

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test describe when currently on a JLBB legacy product
     *
     * @return void
     */
    public function testDescribeCurrentlyOnJohnLewisLegacyProduct()
    {
        $currentSdi = 6756;
        $cli = '***********';
        $serviceId = 1234566;
        $provisionedService = array(
            'vchSupplierPlatform' => 'MAX'
        );
        $expectedProvisionedOn = 'Adsl';
        $broadbandVariants = $this->getBroadbandLegacyVariants();
        $promoCode = '';

        $this->mockProductChangePlanClient($broadbandVariants);
        $coreService = $this->mockCoreService($serviceId, $cli);
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();
        $controller = $this->mockAccountChangeController($currentSdi, $coreService, $lineCheckResult, $wlrProduct);
        $productFamily = $this->mockJohnLewisLegacyProductFamily();
        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $this->getAllTestProducts()
        );

        $validatedData = array('promoCode' => '');
        $actual = $mockRequirement->describe($validatedData);
        $expected = array(
            'intSelectedSdi' => 6756,
            'intSelectedTariffId' => '',
            'arrGroupedProducts' => array(
                'DualPlay' => array($this->getDualPlayProduct()),
                'Solus' => array($this->getSolusProduct()),
                'Current Product' => array($this->getJohnLewisProduct())
            ),
            'bolShowDialupLink' => false,
            'bolShowComponents' => false,
            'objLinecheckResult' => $lineCheckResult,
            'strTelephoneNo' => '***********',
            'strProvisionOn' => $expectedProvisionedOn,
            'oldProductFibre' => true,
            'promoCode' => $promoCode,
            'availableContractDurations' => null,
            'filterProductFamily' => null,
            'hasHomephone' => false,
            'isResidentialSolusDualPlayProduct' => false,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'bolHousemove' => null,
            'fromSelectBroadbandWorkplace' => true,
            'promoCodeValidationErrors' => AccountChange_SelectBroadbandWorkplace::PROMO_CODE_VALIDATION_ERRORS,
        );
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test describe when currently on a Madasafish product
     *
     * @return void
     */
    public function testDescribeCurrentlyOnMadasafishProduct()
    {
        $currentSdi = 6672;
        $cli = '***********';
        $serviceId = 1234566;
        $provisionedService = array(
            'vchSupplierPlatform' => 'MAX'
        );
        $expectedProvisionedOn = 'Adsl';
        $broadbandVariants = $this->getBroadbandLegacyVariants();
        $promoCode = '';

        $this->mockProductChangePlanClient($broadbandVariants);
        $coreService = $this->mockCoreService($serviceId, $cli, 'madasafish');
        $lineCheckResult = $this->mockLineCheckResult();
        $wlrProduct = array();
        $controller = $this->mockAccountChangeController($currentSdi, $coreService, $lineCheckResult, $wlrProduct);
        $productFamily = $this->mockLegacyProductFamily();
        $mockRequirement = $this->mockSelectBroadBandWorkplace(
            $controller,
            $productFamily,
            $serviceId,
            $provisionedService,
            $this->getAllMadasafishProducts()
        );

        $validatedData = array('promoCode' => '');
        $actual = $mockRequirement->describe($validatedData);

        $expected = array(
            'intSelectedSdi' => 6672,
            'intSelectedTariffId' => '',
            'arrGroupedProducts' => array(
                'Current Product' => array($this->getMadasafishProduct6672()),
                'Legacy Products' => array($this->getMadasafishProduct6671(), $this->getMadasafishProduct6660())
            ),
            'bolShowDialupLink' => false,
            'bolShowComponents' => false,
            'objLinecheckResult' => $lineCheckResult,
            'strTelephoneNo' => '***********',
            'strProvisionOn' => $expectedProvisionedOn,
            'oldProductFibre' => true,
            'promoCode' => $promoCode,
            'availableContractDurations' => null,
            'filterProductFamily' => null,
            'hasHomephone' => false,
            'isResidentialSolusDualPlayProduct' => false,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'bolHousemove' => null,
            'fromSelectBroadbandWorkplace' => true,
            'promoCodeValidationErrors' => AccountChange_SelectBroadbandWorkplace::PROMO_CODE_VALIDATION_ERRORS,
        );
        $this->assertEquals($expected, $actual);
    }

    /**
     * Generate a mock AccountChange_Controller
     *
     * @param int              $oldSdi          Old service definition ID
     * @param Core_Service     $coreService     Core_Service object
     * @param LineCheck_Result $lineCheckResult Line check result
     * @param array            $wlrProduct      WLR Product data
     *
     * @return PHPUnit_Framework_MockObject_MockObject|AccountChange_Controller
     */
    private function mockAccountChangeController($oldSdi, $coreService, $lineCheckResult, $wlrProduct)
    {
        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );
        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->onConsecutiveCalls(
                    $oldSdi,
                    $coreService,
                    $lineCheckResult,
                    $wlrProduct
                )
            );
        return $controller;
    }

    /**
     * Generate mock Core_Service object
     *
     * @param int    $serviceId Service ID
     * @param string $cli       Line CLI
     * @param string $isp       vISP
     *
     * @return PHPUnit_Framework_MockObject_MockObject|Core_Service
     */
    private function mockCoreService($serviceId, $cli, $isp = '')
    {
        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId', 'getCliNumber', 'getIsp'),
            array()
        );

        $coreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        $coreService
            ->expects($this->once())
            ->method('getCliNumber')
            ->will($this->returnValue($cli));

        $coreService
            ->expects($this->once())
            ->method('getIsp')
            ->will($this->returnValue($isp));

        return $coreService;
    }

    /**
     * Generate mock line check result
     *
     * @return LineCheck_Result|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockLineCheckResult()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|LineCheck_Result $lineCheckResult */
        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array(
                'getExchangeCode',
                'isValidResult'
            ),
            array()
        );
        $lineCheckResult
            ->expects($this->any())
            ->method('getExchangeCode')
            ->will($this->returnValue('E'));

        $lineCheckResult->expects($this->any())
            ->method('isValidResult')
            ->will($this->returnValue(true));

        AccountChange_Registry::instance()->setEntry('objLineCheckResult', $lineCheckResult);

        return $lineCheckResult;
    }

    /**
     * Generate mock legacy product family
     *
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_Value
     */
    private function mockLegacyProductFamily()
    {
        return $this->mockProductFamily('Legacy Product Family', true, false, false);
    }

    /**
     * Generate mock solus product family
     *
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_Value
     */
    private function mockSolusProductFamily()
    {
        return $this->mockProductFamily('Solus Product Family', false, true, false);
    }

    /**
     * Generate mock dual play product family
     *
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_Value
     */
    private function mockDualPlayProductFamily()
    {
        return $this->mockProductFamily('Dual Play Product Family', false, false, true);
    }

    /**
     * Generate mock John Lewis product family
     *
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_Value
     */
    private function mockJohnLewisLegacyProductFamily()
    {
        return $this->mockProductFamily('John Lewis Product Family', true, false, false, true);
    }

    /**
     * Generate mock product family
     *
     * @param string $productFamilyHandle Product family handle
     * @param bool   $residentialLegacy   Is this a residential legacy product family
     * @param bool   $isSolus             Is this a Solus product family
     * @param bool   $isDualPlay          Is this a Dual play product family
     * @param bool   $isJohnLewis         Is this a John Lewis product family
     * @param bool   $isFalcon            Is this a Falcon product family
     *
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_Value
     */
    private function mockProductFamily(
        $productFamilyHandle,
        $residentialLegacy,
        $isSolus,
        $isDualPlay,
        $isJohnLewis = false,
        $isFalcon = false
    ) {

        $family = $this->getMock(
            'ProductFamily_Value',
            array('isSolus', 'isResidentialLegacy', 'isDualPlay', 'isJohnLewis', 'isFalcon', 'getProductFamilyHandle'),
            array(6755),
            '',
            false
        );
        $family
            ->expects($this->any())
            ->method('isResidentialLegacy')
            ->will($this->returnValue($residentialLegacy));
        $family
            ->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue($isSolus));
        $family
            ->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue($isDualPlay));
        $family
            ->expects($this->any())
            ->method('isJohnLewis')
            ->will($this->returnValue($isJohnLewis));
        $family
            ->expects($this->any())
            ->method('isFalcon')
            ->will($this->returnValue($isFalcon));
        $family
            ->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue($productFamilyHandle));
        return $family;
    }

    /**
     * Generate mock AccountChange_SelectBroadbandWorkplace
     *
     * @param AccountChange_Controller    $controller         Account change controller
     * @param ProductFamily_ProductFamily $family             Product family
     * @param int                         $serviceId          Service ID to test against
     * @param array                       $provisionedService Service data
     * @param array[]                     $products           Data for each available product
     *
     * @return AccountChange_SelectBroadbandWorkplace|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockSelectBroadBandWorkplace($controller, $family, $serviceId, $provisionedService, $products)
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $mockRequirement */
        $mockRequirement = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array(
                'getLineCheckMarket',
                'getBroadbandProducts',
                'getProvisionedService',
                'isServiceDefinitionDialup',
                'isOldProductFibre',
                'includeLegacyFiles',
                'getProductFamily',
                'getMinAndMaxSpeedRanges',
                'getHardwareHelper'
            ),
            array()
        );

        $lineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );

        $lineCheckMarket
            ->expects($this->once())
            ->method('getMarketId')
            ->will($this->returnValue(1));

        $mockRequirement
            ->expects($this->once())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($lineCheckMarket));

        $mockRequirement
            ->expects($this->once())
            ->method('isOldProductFibre')
            ->will($this->returnValue(true));

        $mockRequirement
            ->expects($this->once())
            ->method('getBroadbandProducts')
            ->will($this->returnValue($products));

        $mockRequirement
            ->expects($this->any())
            ->method('getProvisionedService')
            ->with($serviceId)
            ->will($this->returnValue($provisionedService));

        $mockRequirement
            ->expects($this->once())
            ->method('isServiceDefinitionDialup')
            ->will($this->returnValue(false));

        $mockRequirement
            ->expects($this->any())
            ->method('getProductFamily')
            ->will($this->returnValue($family));

        $mockRequirement
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $mockRequirement->setAppStateCallback($controller);

        return $mockRequirement;
    }

    /**
     * Generate mock product change plan client
     *
     * @param array $broadbandVariants Data returned from broadband variant service
     *
     * @return void
     */
    private function mockProductChangePlanClient($broadbandVariants)
    {
        $mockProductChangeClient = $this->getMock(
            'Plusnet\ProductChangePlanClient\Client',
            array('getServiceDefinitionVariants')
        );
        $mockProductChangeClient
            ->expects($this->once())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue($broadbandVariants));

        BusTier_BusTier::setClient('productChangePlan', $mockProductChangeClient);
    }

    /**
     * Get legacy product data
     *
     * @return array
     */
    private function getLegacyProduct()
    {
        return array(
            'intSdi'                   => 6755,
            'strProductName'           => 'Legacy Essentials (No Contract)',
            'intTariffID'              => 1227,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => 999,
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $this->mockLegacyProductFamily(),
            'intContractLengthMonths'  => null,
        );
    }

    /**
     * Get dual play product data
     *
     * @return array
     */
    private function getDualPlayProduct()
    {
        return array(
            'intSdi'                   => 6841,
            'strProductName'           => 'Plusnet Essentials (No Contract)',
            'intTariffID'              => 1227,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => 999,
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $this->mockDualPlayProductFamily(),
            'intContractLengthMonths'  => null,
        );
    }

    /**
     * Get solus product data
     *
     * @return array
     */
    private function getSolusProduct()
    {
        return array(
            'intSdi'                   => 6842,
            'strProductName'           => 'Plusnet Essentials (No Contract) (Without Phone)',
            'intTariffID'              => 1227,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => 998,
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $this->mockSolusProductFamily(),
            'intContractLengthMonths'  => null,
        );
    }

    /**
     * Get Biz Go Large product data
     *
     * @param ProductFamily_ProductFamily $productFamily
     * @return array
     */
    private function getBizGoLargeProduct($productFamily)
    {
        return array(
            'intSdi'                   => 98765,
            'strProductName'           => 'Unlimited Business Broadband and phone (Contracted)',
            'intTariffID'              => 12345,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => 720,
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $productFamily,
            'intContractLengthMonths'  => null,
            'isBizADSLCapped'          => false,
            'isBizFibreCapped'         => false
        );
    }

    /**
     * Get John Lewis product data
     *
     * @return array
     */
    private function getJohnLewisProduct()
    {
        return array(
            'intSdi'                   => 6756,
            'strProductName'           => 'John Lewis Legacy Product',
            'intTariffID'              => 1227,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => 998,
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $this->mockJohnLewisLegacyProductFamily(),
            'intContractLengthMonths'  => 12,
        );
    }

    /**
     * Get Madasafish product data - Max Plus Broadband
     *
     * @return array
     */
    private function getMadasafishProduct6672()
    {
        return array(
            'intSdi'                   => 6672,
            'strProductName'           => 'Madasafish Max Plus Broadband ',
            'intTariffID'              => 1227,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 400),
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $this->mockLegacyProductFamily(),
            'intContractLengthMonths'  => 12,
        );
    }

    /**
     * Get Madasafish product data - Max Broadband
     *
     * @return array
     */
    private function getMadasafishProduct6671()
    {
        return array(
            'intSdi'                   => 6671,
            'strProductName'           => 'Madasafish Max Broadband ',
            'intTariffID'              => 1227,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 500),
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $this->mockLegacyProductFamily(),
            'intContractLengthMonths'  => 12,
        );
    }

    /**
     * Get Madasafish product data - Justmail
     *
     * @return array
     */
    private function getMadasafishProduct6660()
    {
        return array(
            'intSdi'                   => 6660,
            'strProductName'           => 'Madasafish Justmail',
            'intTariffID'              => 1227,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Monthly',
            'intProductCost'           => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 600),
            'objProductDiscountedCost' => null,
            'costLevel'                => 0,
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 8,
            'maxUploadSpeed'           => 0,
            'activationFee'            => 0,
            'productFamily'            => $this->mockLegacyProductFamily(),
            'intContractLengthMonths'  => null,
        );
    }

    /**
     * Some product data to test with
     *
     * @return array
     */
    private function getAllTestProducts()
    {
        return array(
            6755 => $this->getLegacyProduct(),
            6756 => $this->getJohnLewisProduct(),
            6841 => $this->getDualPlayProduct(),
            6842 => $this->getSolusProduct()
        );
    }

    /**
     * Get all Madasafish products
     *
     * @return array
     */
    private function getAllMadasafishProducts()
    {
        return array(
            6672 => $this->getMadasafishProduct6672(),
            6671 => $this->getMadasafishProduct6671(),
            6660 => $this->getMadasafishProduct6660()
        );
    }

    /**
     * Get all broadband legacy variants
     *
     * @return array
     */
    private function getBroadbandLegacyVariants()
    {
        return array();
    }

    /**
     * Get all broadband solus and dual play variants
     *
     * @return array
     */
    private function getBroadbandSolusDualPlayVariants()
    {
        return array (
            "solus" => 6842,
            "dualplay" => 6841
        );
    }

    /**
     * Tests for valNewSdi
     *
     * @param array $data Variable Data used for testing
     * @param int $newSdi Servide Definition Id
     * @param string $promoCode Promotion code
     * @param array $availableProducts Available products
     * @param array $financialErrors Financial Errors
     * @param bool $errorExpected Error expected
     * @param array $expected Expected results
     * @param bool $validPromoCode Is this an invalid promocode?
     * @param string $selectedContractDuration selected contract duration
     *
     * @return void
     * @dataProvider provideDataForTestValNewSdi
     *
     */
    public function testValNewSdi(
        $data,
        $newSdi,
        $promoCode,
        $availableProducts,
        $financialErrors,
        $errorExpected,
        $expected,
        $validPromoCode = true,
        $selectedContractDuration = '0'
    ) {
        $this->currentData = $data;

        $productFamily = $this->getMock(
            'ProductFamily_Res2012',
            array(),
            array(),
            '',
            false
        );

        $arrProductProvDetails = array(
           'intSupplierProductId'  => 1,
           'bolWbcProduct'         => true,
           'selectedProductFamily' => $productFamily,
           'strProvisionOn'        => 'Adsl2'
        );

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_ProductRules $rules */
        $rules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService'),
            array()
        );

        $rules
            ->expects($this->any())
            ->method('getProductProvisionForService')
            ->will($this->returnValue($arrProductProvDetails));

        AccountChange_ProductRules::setInstance($rules);

        $mockFpcStatusService = $this->getMock(
            '\Plusnet\PriceProtected\Services\StatusService',
            array('isServicePriceProtected')
        );

        $mockFpcStatusService
            ->expects($this->any())
            ->method('isServicePriceProtected')
            ->will($this->returnValue(true));

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $mockPage */
        $mockPage = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array(
                'addValidationError',
                'getApplicationStateVariable',
                'getBroadbandProducts',
                'getFinancialErrors',
                'isPromoCodeValidForProduct',
                'getProductsWithDiscount',
                'getProductFamily',
                'getFpcStatusService',
                'getLineCheckMarket',
                'isC2fToggleSet'
            ),
            array()
        );

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Controller $mockController */
        $mockController = $this->getMock(
            'AccountChange_Controller',
            array(
                'getApplicationStateVar'
            )
        );

        $mockPage->setAppStateCallback($mockController);

        $mockPage->expects($this->any())
            ->method('getLineCheckMarket')
            ->will($this->returnValue(LineCheck_Market::getDefault()));

        $mockPage
            ->expects($this->any())
            ->method('getFpcStatusService')
            ->will($this->returnValue($mockFpcStatusService));

        $mockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $mockPage->expects($this->any())
            ->method('addValidationError');

        $mockPage->expects($this->any())
            ->method('getBroadbandProducts')
            ->will($this->returnValue($availableProducts));

        $mockPage
            ->expects($this->any())
            ->method('getProductFamily')
            ->will($this->returnValue($productFamily));

        $mockPage->expects($this->any())
            ->method('getFinancialErrors')
            ->will($this->returnValue($financialErrors));

        if (!empty($promoCode)) {
            $mockPage->expects($this->any())
                ->method('isPromoCodeValidForProduct')
                ->will($this->returnValue($validPromoCode));

            $mockPage->expects($this->any())
                ->method('getProductsWithDiscount')
                ->will($this->returnValue(array($newSdi)));
        }

        $mockCore = $this->getMockBuilder(Core_Service::class)
            ->setMethods(['getServiceId'])
            ->getMock();

        $mockCore->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1212));

        $this->currentData = [
            'objLineCheckResult' => $this->getMockBuilder(LineCheck_Result::class)
                ->disableOriginalConstructor()
                ->setMethods(['getExchangeCode'])
                ->getMock(),
            'objCoreService'     => $mockCore,
        ];

        $mockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $result = $mockPage->valNewSdi($newSdi, $promoCode, $selectedContractDuration);

        if (!$errorExpected) {
            $this->assertEquals($expected, $result);
        } else {
            $errors = $mockPage->getValidationErrors();
            $this->assertEquals($expected, $errors);
        }
    }

    /**
     * Call back function so that we can pass back different values for the test for getApplicationStateVariable
     *
     * @return array
     */
    public function returnGetApplicationStateVar()
    {
        $arrArgs = func_get_args();
        if (isset($this->currentData[$arrArgs[1]])) {
            return $this->currentData[$arrArgs[1]];
        } else {
            return;
        }
    }

    /**
     * Provide data for testValNewSdi
     *
     * @return array
     */
    public function provideDataForTestValNewSdi()
    {
        $serviceId = 987654;
        $validNewSdi = "9687_1227";
        $validWithBundleSdi = "6704_1227";
        $validWithContractSdi = "4564_1227";
        $unmatchedNewSdi = '9999_1234';
        $invalidNewSdi = null;
        $nonNumericNewSdi = 'Wobble';
        $emptyPromoCode = '';
        $promoCode = '12halfprice';
        $inContractPrice = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 11.00);
        $basePrice = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 12.00);

        $availableProducts = array(
            array(
                'intSdi'                         => 9687,
                'strProductName'                 => 'Plusnet Essentials (No Contract)',
                'intTariffID'                    => 1227,
                'strContract'                    => 'MONTHLY',
                'strContractLength'              => 'Monthly',
                'intProductCost'                 => $inContractPrice,
                'objProductDiscountedCost'       => null,
                'costLevel'                      => 0,
                'provisioningProfile'            => '512k+ 50:1 SI',
                'isFibre'                        => false,
                'maxDownloadSpeed'               => 8,
                'maxUploadSpeed'                 => 0,
                'activationFee'                  => 0,
                'downloadSpeedRangeMin'          => 36000,
                'downloadSpeedRangeMax'          => 40000,
                'uploadSpeedRangeMin'            => 1000,
                'uploadSpeedRangeMax'            => 2000,
                'intMGALSInMB'                   => 27000,
                'isPartner'                      => null,
                'intContractLengthMonths'        => null,
                'uploadSpeedRangeMgsFormatted'   => null,
                'downloadSpeedRangeMgsFormatted' => null,
                'intMGSInMB'                     => null,
                'currentBasePriceInContract'     => $inContractPrice,
                'currentBasePrice'               => $basePrice,
                'selectedContractDuration'       => '0'
            ),

            array(
                'intSdi'                         => 6704,
                'strProductName'                 => 'Plusnet Essentials (No Contract)',
                'intTariffID'                    => 1227,
                'strContract'                    => 'MONTHLY',
                'strContractLength'              => 'Monthly',
                'intProductCost'                 => $inContractPrice,
                'objProductDiscountedCost'       => null,
                'costLevel'                      => 0,
                'provisioningProfile'            => '512k+ 50:1 SI',
                'isFibre'                        => false,
                'maxDownloadSpeed'               => 8,
                'maxUploadSpeed'                 => 0,
                'activationFee'                  => 0,
                'downloadSpeedRangeMin'          => 36000,
                'downloadSpeedRangeMax'          => 40000,
                'uploadSpeedRangeMin'            => 1000,
                'uploadSpeedRangeMax'            => 2000,
                'intContractLengthMonths'        => null,
                'uploadSpeedRangeMgsFormatted'   => null,
                'downloadSpeedRangeMgsFormatted' => null,
                'intMGSInMB'                     => null,
                'currentBasePriceInContract'     => $inContractPrice,
                'currentBasePrice'               => $basePrice,
                'selectedContractDuration'       => '18'
            ),

            array(
                'intSdi'                         => 4564,
                'strProductName'                 => 'Plusnet Essentials (12 months)',
                'intTariffID'                    => 1227,
                'strContract'                    => 'MONTHLY',
                'strContractLength'              => 'Monthly',
                'intProductCost'                 => $inContractPrice,
                'objProductDiscountedCost'       => null,
                'costLevel'                      => 0,
                'provisioningProfile'            => '512k+ 50:1 SI',
                'isFibre'                        => false,
                'maxDownloadSpeed'               => 8,
                'maxUploadSpeed'                 => 0,
                'activationFee'                  => 0,
                'downloadSpeedRangeMin'          => 36000,
                'downloadSpeedRangeMax'          => 40000,
                'uploadSpeedRangeMin'            => 1000,
                'uploadSpeedRangeMax'            => 2000,
                'intContractLengthMonths'        => 12,
                'uploadSpeedRangeMgsFormatted'   => null,
                'downloadSpeedRangeMgsFormatted' => null,
                'intMGSInMB'                     => null,
                'currentBasePriceInContract'     => $inContractPrice,
                'currentBasePrice'               => $basePrice,
                'selectedContractDuration'       => '0'
            )
        );

        $mockCore = $this->getMock(
            'Core_Service',
            array(
                'getServiceId'
            )
        );

        $mockCore->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        $mockLineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId', 'getMarketFromExchange'),
            array(),
            '',
            false
        );

        $mockLineCheckMarket
            ->expects($this->any())
            ->method('getMarketId')
            ->will($this->returnValue(1));

        $mockLineCheckMarket
            ->expects($this->any())
            ->method('getMarketFromExchange')
            ->will($this->returnValue(1));

        /** @var PHPUnit_Framework_MockObject_MockObject|Db_Adaptor $mockLineCheckDbAdaptor */
        $mockLineCheckDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getExchangeMarket'),
            array('LineCheck', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockLineCheckDbAdaptor
            ->expects($this->any())
            ->method('getExchangeMarket')
            ->will($this->returnValue(1));

        Db_Manager::setAdaptor('LineCheck', $mockLineCheckDbAdaptor);

        $mockLineCheckResult = $this->getMock(
            'LineCheck_Result',
            array(
                'getLineCheckMarket',
                'getMarketFromExchange'
            ),
            array(),
            '',
            false
        );

        $mockLineCheckResult
            ->expects($this->any())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($mockLineCheckMarket));

        $mockProductFamily = $this->getMock(
            'ProductFamily_Res2012',
            array(),
            array(),
            '',
            false
        );

        $data = array(
            'objCoreService'     => $mockCore,
            'objLineCheckResult' => $mockLineCheckResult,
        );

        $financialErrors = array(
            'error' => 'a financial error has happened'
        );

        $errorMissingSdi = array(
            'intNewSdi' => array('MISSING' => true)
        );

        $errorInvalidSdi = array(
            'intNewSdi' => array('INVALID' => true)
        );

        $errorInvalidPromoCode = array(
            'promoCode' => array('INVALIDPRODUCT' => true)
        );

        $errorFinancial = array(
            'intNewSdi' => array('FINANCIAL_ERROR' => array('strError' => 'a financial error has happened'))
        );

        $expected = array(
            'intNewSdi' => 9687,
            'arrSelectedBroadband' =>
                array(
                    'intSdi'                         => '9687',
                    'strNewProduct'                  => 'Plusnet Essentials (No Contract)',
                    'intNewCost'                     => $inContractPrice,
                    'intNewLeadingCost'              => null,
                    'strContractHandle'              => 'MONTHLY',
                    'intSelectedTariffID'            => 1227,
                    'provisioningProfile'            => '512k+ 50:1 SI',
                    'strProvisionOn'                 => '512k+ 50:1 SI',
                    'maxDownstreamSpeed'             => 8,
                    'maxUpstreamSpeed'               => 0,
                    'downloadSpeedRangeMin'          => 36000,
                    'downloadSpeedRangeMax'          => 40000,
                    'uploadSpeedRangeMin'            => 1000,
                    'uploadSpeedRangeMax'            => 2000,
                    'intMGALSInMB'                   => 27000,
                    'intImpactedMGALS'               => null,
                    'strBroadbandType'               => null,
                    'isPartner'                      => null,
                    'intContractLengthMonths'        => null,
                    'uploadSpeedRangeMgsFormatted'   => null,
                    'downloadSpeedRangeMgsFormatted' => null,
                    'intMGSInMB'                     => null,
                    'intInContractMonthlyPrice'      => $inContractPrice,
                    'intMonthlyPrice'                => $basePrice,
                    'selectedContractDuration'       => '0'
                ),
            'bolWbcProduct' => true,
            'selectedProductFamily' => $mockProductFamily,
            'bolSelectedBundle' => false,
        );

        $expectedWithBundle = array(
            'intNewSdi' => 6704,
            'arrSelectedBroadband' =>
                array(
                    'intSdi'                         => '6704',
                    'strNewProduct'                  => 'Plusnet Essentials (No Contract)',
                    'intNewCost'                     => $basePrice,
                    'intNewLeadingCost'              => null,
                    'strContractHandle'              => 'MONTHLY',
                    'intSelectedTariffID'            => 1227,
                    'provisioningProfile'            => '512k+ 50:1 SI',
                    'strProvisionOn'                 => '512k+ 50:1 SI',
                    'maxDownstreamSpeed'             => 8,
                    'maxUpstreamSpeed'               => 0,
                    'downloadSpeedRangeMin'          => 36000,
                    'downloadSpeedRangeMax'          => 40000,
                    'uploadSpeedRangeMin'            => 1000,
                    'uploadSpeedRangeMax'            => 2000,
                    'intMGALSInMB'                   => null,
                    'intImpactedMGALS'               => null,
                    'strBroadbandType'               => null,
                    'isPartner'                      => null,
                    'intContractLengthMonths'        => null,
                    'uploadSpeedRangeMgsFormatted'   => null,
                    'downloadSpeedRangeMgsFormatted' => null,
                    'intMGSInMB'                     => null,
                    'intInContractMonthlyPrice'      => $inContractPrice,
                    'intMonthlyPrice'                => $basePrice,
                    'selectedContractDuration'       => '18'
                ),
            'bolSelectedBundle' => true,
            'bolWbcProduct' => true,
            'selectedProductFamily' => $mockProductFamily,
            'arrSelectedWlr' => array(
                    'strNewProduct' => 'Plusnet Essential',
                    'intNewCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
            ),
            'intNewWlrId' => 596,
        );

        $expectedWithContract = array(
            'intNewSdi' => 4564,
            'arrSelectedBroadband' =>
                array(
                    'intSdi'                         => '4564',
                    'strNewProduct'                  => 'Plusnet Essentials (12 months)',
                    'intNewCost'                     => $inContractPrice,
                    'intNewLeadingCost'              => null,
                    'strContractHandle'              => 'MONTHLY',
                    'intSelectedTariffID'            => 1227,
                    'provisioningProfile'            => '512k+ 50:1 SI',
                    'strProvisionOn'                 => '512k+ 50:1 SI',
                    'maxDownstreamSpeed'             => 8,
                    'maxUpstreamSpeed'               => 0,
                    'downloadSpeedRangeMin'          => 36000,
                    'downloadSpeedRangeMax'          => 40000,
                    'uploadSpeedRangeMin'            => 1000,
                    'uploadSpeedRangeMax'            => 2000,
                    'intMGALSInMB'                   => null,
                    'intImpactedMGALS'               => null,
                    'strBroadbandType'               => null,
                    'isPartner'                      => null,
                    'intContractLengthMonths'        => 12,
                    'uploadSpeedRangeMgsFormatted'   => null,
                    'downloadSpeedRangeMgsFormatted' => null,
                    'intMGSInMB'                     => null,
                    'intInContractMonthlyPrice'      => $inContractPrice,
                    'intMonthlyPrice'                => $basePrice,
                    'selectedContractDuration'       => '0'
                ),
            'bolSelectedBundle' => false,
            'bolWbcProduct' => true,
            'selectedProductFamily' => $mockProductFamily,
        );

        $expectedWithPromoCode = array(
            'intNewSdi' => 9687,
            'bolSelectedBundle' => false,
            'arrSelectedBroadband' => $validNewSdi,
            'promoCode' => '12halfprice',
            'bolWbcProduct' => true,
            'selectedProductFamily' => $mockProductFamily,
        );

        return array(
            // Error raised - invalid new SDI
            array(
                $data, $invalidNewSdi, $emptyPromoCode, $availableProducts, array(), true, $errorMissingSdi
            ),

            // Error raised - financial problems
            array(
                $data, $validNewSdi, $emptyPromoCode, $availableProducts, $financialErrors, true, $errorFinancial
            ),

            // Error raised - invalid new SDI - non-numeric
            array(
                $data, $nonNumericNewSdi, $emptyPromoCode, $availableProducts, array(), true, $errorInvalidSdi
            ),

            // Error raised - invalid new SDI - unmatched
            array(
                $data, $unmatchedNewSdi, $emptyPromoCode, $availableProducts, array(), true, $errorInvalidSdi
            ),

            // Error raised - invalid promo code
            array(
                $data, $validNewSdi, $promoCode, $availableProducts, array(), true, $errorInvalidPromoCode, false
            ),

            // Everything ok! - without bundle item
            array(
                $data, $validNewSdi, $emptyPromoCode, $availableProducts, array(), false, $expected
            ),

            // Everything ok! - with bundle
            array(
                $data, $validWithBundleSdi, $emptyPromoCode, $availableProducts, array(), false, $expectedWithBundle, true, '18'
            ),

            // Everything ok! - with contract
            array(
                $data, $validWithContractSdi, $emptyPromoCode, $availableProducts, array(), false, $expectedWithContract
            ),

            // Everything ok! - with promo code
            array(
                $data, $validNewSdi, $promoCode, $availableProducts, array(), false, $expectedWithPromoCode
            ),
        );
    }

    /**
     * Test that valSelectedContractDuration validator runs correctly
     *
     * @param int   $selectedContractDuration Selected contract duration
     * @param array $output                   Expected output
     *
     * @dataProvider provideDataForValSelectedContractDuration
     *
     * @return void
     */
    public function testValSelectedContractDuration($selectedContractDuration, $output)
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $requirement */
        $requirement = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('addValidationError'),
            array()
        );

        $requirement
            ->expects($this->any())
            ->method('addValidationError');

        $result = $requirement->valSelectedContractDuration($selectedContractDuration);

        $this->assertSame($output, $result);
    }

    /**
     * Data provider for testValSelectedContractDuration
     *
     * @return array
     */
    public static function provideDataForValSelectedContractDuration()
    {
        return array(
            array(null, array('selectedContractDuration' => null)),
            array(0, array('selectedContractDuration' => 0)),
            array(24, array('selectedContractDuration' => 24)),
        );
    }

    /**
     * Test getMinimumContractDuration method
     *
     * @param int[] $existingBroadband       'remainingTime' key required
     * @param int   $expectedMinumumDuration expected result in months
     *
     * @dataProvider dataProviderGetMinimumContractDuration
     * @return void
     */
    public function testGetMinimumContractDuration($existingBroadband, $expectedMinumumDuration)
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $requirement */
        $requirement = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('isApplicationStateVariable', 'getApplicationStateVariable'),
            array()
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Controller $mockController */
        $mockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar', 'getApplicationStateVar'));

        $mockController->expects($this->atLeastOnce())
            ->method('isApplicationStateVar')
            ->with(get_class($requirement), 'existingBroadband')
            ->willReturn(true);

        if (isset($existingBroadband)) {
            $mockController->expects($this->atLeastOnce())
                ->method('getApplicationStateVar')
                ->with(get_class($requirement), 'existingBroadband')
                ->willReturn($existingBroadband);
        }

        $requirement->setAppStateCallback($mockController);

        $this->assertEquals($expectedMinumumDuration, $requirement->getMinimumContractDuration());
    }

    public function dataProviderGetMinimumContractDuration()
    {
        return array(
            array(null, 0),
            array(array('remainingTime' => 0), 0),
            array(array('remainingTime' => 1), 1),
            array(array('remainingTime' => 18), 18)
        );
    }

    /**
     * Test that isBackDatedAllowed runs correctly
     *
     * @param bool   $allowUser         allowUser Flag
     * @param int    $productInProgress product in progress status
     * @param bool   $validBillDate     invoice date is future or not
     * @param bool   $hasReRateReq      has rerate flag
     * @param bool   $expectedFlag      expected flag
     * @param bool   $expectedMsg       expected message
     *
     * @dataProvider provideDataForIsBackDatedAllowed
     *
     * @return void
     */
    public function testIsBackDatedAllowed(
        $allowUser,
        $productInProgress,
        $validBillDate,
        $hasReRateReq,
        $expectedFlag,
        $expectedMsg
    ) {
        $serviceId = 12345;

        $objCoreService = null;

        /** @var PHPUnit_Framework_MockObject_MockObject|Auth_BusinessActor $mockUser */
        $mockUser = $this->getMock(
            'Auth_BusinessActor',
            array(
                'getUserType',
                'getExternalUserId'
            ),
            array()
        );

        $mockUser->expects($this->any())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_STAFF'));

        $mockUser->expects($this->any())
            ->method('getExternalUserId')
            ->will($this->returnValue('mock_user'));

        /** @var PHPUnit_Framework_MockObject_MockObject|Db_Adaptor $objMockDbAdaptor */
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'isUserAllowedBackDated',
                'checkProductChangeInProgress',
                'getProductChangeInProgress'
            ),
            array(
                'AccountChange', Db_Manager::DEFAULT_TRANSACTION, false
            )
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isUserAllowedBackDated')
            ->with($mockUser->getExternalUserId())
            ->will($this->returnValue($allowUser));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $objMock */
        $objMock = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array(
                'getCurrentBusinessActor',
                'checkProductChangeInProgress',
                'checkBillingDate',
                'hasPendingReratingRequest'
            ),
            array()
        );

        $objMock->expects($this->any())
            ->method('getCurrentBusinessActor')
            ->will($this->returnValue($mockUser));

        $objMock->expects($this->any())
            ->method('checkProductChangeInProgress')
            ->will($this->returnValue($productInProgress));

        $objMock->expects($this->any())
            ->method('checkBillingDate')
            ->will($this->returnValue($validBillDate));

        $objMock->expects($this->any())
            ->method('hasPendingReratingRequest')
            ->will($this->returnValue($hasReRateReq));

        $response = array();
        $objMock->isBackDatedAllowed($response, $serviceId, $objCoreService);

        $this->assertEquals($expectedFlag, $response['isAgentAllowBackDated']);

        if (array_key_exists('backDatedProductMsg', $response)) {
            $this->assertEquals($expectedMsg, $response['backDatedProductMsg']);
        }
    }

    /**
     * Data provider for testIsBackDatedAllowed
     * $allowUser, $productInProgress, $validBillDate, $hasReRateReq
     *
     * @return array
     */
    public static function provideDataForIsBackDatedAllowed()
    {
        $commonErrMsg = "Cannot back date product change because ";

        return array(
            array(false,  0, 0, true, false, null),
            array(true,  80, 0, false, true, null),
            array(true,  -1, 0, true, true, $commonErrMsg.'a product change is already in progress.'),
            array(true,  0, 1, true, true, $commonErrMsg.'the customer has failed to make one or more payments.'),
            array(true,  0, 2, true, true, $commonErrMsg.'the next invoice date is either today or in the past.'),
            array(true,  0, 3, true, true, $commonErrMsg.'technical error occured. Please try again later.'),
            array(true,  0, 0, true, true, $commonErrMsg.'there is already a backdated product change in progress.')
        );
    }
    /**
     * Test that checkProductChangeInProgress validator runs correctly
     *
     * @param int $output       expected output
     * @param int $returnValue1 getProductChangeInProgress return value
     * @param int $returnValue2 getDaysDiffOfPreviousProductChange return value
     *
     * @dataProvider provideDataForBackdatedProductChange
     *
     * @return void
     */
    public function testCheckProductChangeInProgress($output, $returnValue1, $returnValue2)
    {
        $intServiceId = 9999;

        /** @var PHPUnit_Framework_MockObject_MockObject|Db_Adaptor $objMockDbAdaptor */
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getProductChangeInProgress',
                'getDaysDiffOfPreviousProductChange'
            ),
            array(
                'AccountChange', Db_Manager::DEFAULT_TRANSACTION, false
            )
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getProductChangeInProgress')
            ->with($intServiceId)
            ->will($this->returnValue($returnValue1));

        $objMockDbAdaptor->expects($this->any())
            ->method('getDaysDiffOfPreviousProductChange')
            ->with($intServiceId)
            ->will($this->returnValue($returnValue2));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $objMock */
        $objMock = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('NowIsTheTime_test'),
            array()
        );

        $intDaysRemaining = $objMock->checkProductChangeInProgress($objMockDbAdaptor, $intServiceId);

        $this->assertEquals($output, $intDaysRemaining);
    }
    /**
     * Data provider for testCheckProductChangeInProgress
     *
     * @return array
     */
    public static function provideDataForBackdatedProductChange()
    {
        return array(
            array(-1,1,0),
            array(-1,2,0),
            array(0,0,null),
            array(90,0,90),
            array(89,0,89),
        );
    }
     /**
     * Test that valSelectedContractDuration validator runs correctly
     *
     * @param int    $backDatedDate  Backdated Date
     * @param array  $expectedResult Expected output
     * @param string $expectedError  ErrorMessage
     * @param int    $returnValue2   no. of days from DB
     *
     * @dataProvider provideDataForValBackDatedDate
     *
     * @return void
     */
    public function testValBackDatedDate($backDatedDate, $expectedResult, $expectedError, $returnValue2 = null)
    {
        $intServiceId = 1234;

        $requirement = new AccountChange_SelectBroadbandWorkplace();

        $mockCore = $this->getMock(
            'Core_Service',
            array(
                'getServiceId'
            )
        );

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Controller $mockController */
        $mockController = $this->getMock(
            'AccountChange_Controller',
            array(
                'getApplicationStateVar'
            )
        );
        $requirement->setAppStateCallback($mockController);

        $mockCore->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($intServiceId));

        $this->currentData = array('objCoreService' => $mockCore);

        $mockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $result = $requirement->valBackDatedDate($backDatedDate);

        $errors = $requirement->getValidationErrors();
        $this->assertEquals($expectedError, $errors);
        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Data provider for testValBackDatedDate
     *
     * @return array
     */
    public static function provideDataForValBackDatedDate()
    {
        $errorInvalid = array(
            'backDatedDate' => array('INVALID' => true)
        );

        return array(
            array(null, array('backDatedDate' => ''), array(), null),
            array('2016/10/22', array('backDatedDate' => '2016/10/22'), $errorInvalid, null),
            array(date("d/m/Y", time() - 86400), array('backDatedDate' => date("d/m/Y", time() - 86400)),
                            array(), 10),
            array(date("d/m/Y", time() - (60*60*24*5)), array('backDatedDate' => date("d/m/Y", time() - (60*60*24*5))),
                            array(), 10)
        );
    }

    /**
     * Test that hasPendingReratingRequest validates is there any Pending Rerating Request
     *
     * @param integer $input  Response of RBM
     * @param bool    $output PendingRerating Status
     *
     * @dataProvider provideDataForHasPendingReratingRequest
     *
     * @return void
     */
    public function testHasPendingReratingRequest($input, $output)
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $mockReRatingRequest */
        $mockReRatingRequest = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('addValidationError'),
            array()
        );

        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('hasPendingReratingRequests'),
            array(),
            '',
            false
        );

        $billingApiFacade->expects($this->any())
            ->method('hasPendingReratingRequests')
            ->willReturn($input);

        $intServiceId = 12323;

        \Plusnet\BillingApiClient\Service\ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $pendingReRatingStatus = $mockReRatingRequest->hasPendingReratingRequest($intServiceId);

        $this->assertEquals($output, $pendingReRatingStatus);
    }

    /**
     * Data provider for testHasPendingReratingRequest
     *
     * @return array
     */
    public static function provideDataForHasPendingReratingRequest()
    {
        return array(
                array( array( 'accountNum'=>1, 'requestCount'=>5), true ),
                array( null, true ),
                array( array( 'accountNum'=>1, 'requestCount'=>0), false )
        );
    }

    /**
     * Test that checkBillingDate whether the Billing date is greater than current date
     *
     * @param array  $input         Mock response from RBM function getNextBillingDate
     * @param string $invoicePeriod Invoice period
     * @param int    $output        Expected output from checkBillingDate
     *
     * @dataProvider provideDataForCheckBillingDate
     *
     * @return void
     */
    public function testCheckBillingDate($input, $invoicePeriod, $output)
    {
        $intServiceId = 12323;

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadbandWorkplace $mockCheckBillingDate */
        $mockCheckBillingDate = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('addValidationError'),
            array()
        );

        /** @var PHPUnit_Framework_MockObject_MockObject|Core_Service $objCoreService */
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getInvoicePeriod')
        );

        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('getBillingAccount'),
            array(),
            '',
            false
        );

        $objCoreService->expects($this->any())
            ->method('getInvoicePeriod')
            ->willReturn($invoicePeriod);

        $billingApiFacade->expects($this->any())
            ->method('getBillingAccount')
            ->willReturn($input);

        \Plusnet\BillingApiClient\Service\ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $invoiceStatus= $mockCheckBillingDate->checkBillingDate($intServiceId, $objCoreService);

        $this->assertEquals($output, $invoiceStatus);
    }

    /**
     * Data provider for testCheckBillingDate
     *
     * @return array
     */
    public static function provideDataForCheckBillingDate()
    {
        return array(
            array(
                array(
                    'nextInvoiceDate' => date('Y-m-d', time() + 86400)
                ),
                'monthly',
                0
            ),
            array(
                array(
                    'nextInvoiceDate' => date('Y-m-d'),
                    'failedPaymentDetails' => array(
                        'status' => true
                    )
                ),
                'monthly',
                1
            ),
            array(
                array(
                    'nextInvoiceDate' => date('Y-m-d'),
                    'failedPaymentDetails' => array()
                ),
                'monthly',
                2
            ),
            array(
                array(
                    'nextInvoiceDate' => date('Y-m-d')
                ),
                'monthly',
                2
            ),
            array(
                array(
                    'nextInvoiceDate' => date('Y-m-d', time() - 86400)
                ),
                'monthly',
                2
            ),
            array(
                array(
                    'nextInvoiceDate' => date('Y-m-d', time() + (60*60*24*45))
                ),
                'monthly',
                0
            ),
            array(
                array(
                    'nextInvoiceDate' => date('Y-m-d', time() + (60*60*24*90))
                ),
                'monthly',
                0
            )
        );
    }

    /**
     * @return array
     */
    public function displayHardwareMessageProvider()
    {
        return [
            'Show message, hardware page true' => [
                'shouldUseHelper' => true,
                'shouldShowHardwarePage' => true,
                'shouldDisplayHardwareMessage' => true,
                'expectedResult' => true
            ],
            'Show message, hardware page false' => [
                'shouldUseHelper' => true,
                'shouldShowHardwarePage' => false,
                'shouldDisplayHardwareMessage' => true,
                'expectedResult' => true
            ],
            'Dont show message' => [
                'shouldUseHelper' => true,
                'shouldShowHardwarePage' => false,
                'shouldDisplayHardwareMessage' => false,
                'expectedResult' => false
            ],
        ];
    }
}
