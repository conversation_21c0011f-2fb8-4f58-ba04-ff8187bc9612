<?php
/**
 * Service Definition
 *
 * Holds information about a service definition product than an account may have
 *
 * PHP Version 5.2
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://www.plus.net/ plusnet
 * @since     File available since 2008-08-19
 */

use Plusnet\ContractsClient\Instance\InstanceInterface as Contract;
use Plusnet\Feature\FeatureToggleManager;
use Plusnet\InventoryEventClient\Context\RecontractContext;
use Plusnet\PromotionTools\PromotionToolsService;

/**
 * Service Definition class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://www.plus.net/ plusnet
 */
class AccountChange_Product_ServiceDefinition implements AccountChange_Product_Configuration
{
    /**
     * Service Definition Id
     *
     * @var int
     */
    private $_intServiceDefinitionId = null;

    /**
     * Action
     *
     * @var int
     */
    private $_intAction = null;

    /**
     * Service Id
     *
     * @var int
     */
    private $_intServiceId;

    /**
     * Product configuration moving too
     *
     * @var AccountChange_Product_Configuration
     */
    private $_objNewProductConfiguration = null;

    /**
     * Reset the contract for the broadband component
     *
     * @var boolean
     */
    private $_bolContractReset = false;

    /**
     * Account Change Operation
     *
     * AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE
     * AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE
     * AccountChange_Product_Manager::ACCOUNT_CHANGE_SAME
     *
     * @var int
     */
    protected $intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_SAME;

    /**
     * Boolean as to whether or not we are scheduling the change
     *
     * @var boolean
     */
    protected $bolScheduleChange = false;

    /**
     * Boolean as to whether or not we are scheduling the downgrade
     *
     * @var boolean
     */
    protected $bolScheduleDowngrade = false;

    /**
     * An array of components ID that we do not want to keep.
     * This array is populated from frontend
     *
     * @var array
     */
    private $_arrLegacyComponentNotToKeep = array();

    /**
     * Array of component types we simply want to ignore in the legacy change
     * These component types have been dealt with via this app,
     * and therefore do not need to be dealth with by legacy code
     *
     * @var array
     */
    private $_arrLegacyComponentTypesToIgnore = array();

    /**
     * Taking payment for the account change
     *
     * @var boolean
     */
    private $_bolTakePayment = false;

    /**
     * Array of AccountChange_Ticket's
     *
     * @var array
     */
    protected $_tickets = array();

    /**
     * Array of AccountChange_ServiceNotice's
     *
     * @var array
     */
    protected $_serviceNotices = array();

    /**
     * @var int
     */
    private $_intLineCheckId = null;

    /**
     * The Id of the business actor performing the account change
     *
     * @var int
     */
    private $_intBusinessActorId = null;

    /**
     * The desired contract length - if empty then we default to the value appropriate to the product
     * (for example Value family products only have 18 month contracts - so this wouldn't necessarily need to be
     * set, but BPR09 family products can have 12 or 24 month contracts, so it would be set in this case.
     *
     * @var string
     */
    private $_strContract;

    /**
     * Service component contract
     *
     * @var AccountChange_Product_ServiceDefinitionContract
     */
    protected $objServiceDefinitionContract = null;

    /**
     * Reference to the options passed into this object
     *
     * @var array
     */
    protected $arrOptions = array();

    /**
     * Old wlr service compoent id
     *
     * @var int
     **/
    private $_oldWlrScid;

    /**
     * New wlr service compoent id
     *
     * @var int
     **/
    private $_newWlrScid;

    /**
     * Old adsl INTERNET_CONNECTION component id
     *
     * @var int
     **/
    private $_oldAdslComponentId;

    /**
     * Defunct adsl INTERNET_CONNECTION component ids
     *
     * @var array
     **/
    private $_defunctAdslComponentIds;

    /**
     * Old Wlr component id
     *
     * @var int
     **/
    private $_oldWlrComponentId;

    /**
     * New Adsl scid for INTERNET_CONNECTION
     *
     * @var int
     **/
    private $_newAdslScid;

    /**
     * Flag to indicate if this is a House Move product change
     *
     * @var boolean
     **/
    private $_bolHousemove = false;

    /**
     * Constructor to make the product configuration object
     *
     * @param int   $intServiceDefinitionId the service definition id
     * @param int   $intAction              the action to be taken
     * @param array $arrOptions             an optional list of options
     *
     * @return void
     */
    public function __construct($intServiceDefinitionId, $intAction, array $arrOptions = array())
    {
        $this->initialise($intServiceDefinitionId, $intAction, $arrOptions);
    }

    /**
     * Initialise the Service Definition Product Configuration Object
     *
     * @param int   $intServiceDefinitionId the service definition id
     * @param int   $intAction              the action to be taken
     * @param array $arrOptions             an optional list of options
     *
     * @throws AccountChange_Product_ManagerException
     *
     * @return void
     */
    public function initialise($intServiceDefinitionId, $intAction, array $arrOptions)
    {
        if (!is_numeric($intServiceDefinitionId)) {
            throw new AccountChange_Product_ManagerException(
                'Non numeric service definition id',
                AccountChange_Product_ManagerException::ERR_INVALID_COMPONENT_ID_TYPE
            );
        }

        $this->_intServiceDefinitionId = $intServiceDefinitionId;
        $this->_intAction = $intAction;

        // Store the desired contract length
        if (isset($arrOptions['strContract'])) {
            $this->_strContract = $arrOptions['strContract'];
        } else {
            $this->_strContract = '';
        }

        // Are we wanting to reset the contract
        if (isset($arrOptions['bolContractReset'])) {
            $this->_bolContractReset = $arrOptions['bolContractReset'];
        }

        $registry = AccountChange_Registry::instance();
        if (!$this->_bolContractReset) {
            $this->_bolContractReset = $registry->getEntry('isRecontract');
        }

        // Are we wanting to schedule all types of changings
        if (isset($arrOptions['bolSchedule'])) {
            $this->bolScheduleChange = $arrOptions['bolSchedule'];
        }

        // Are we wanting to schedule downgrading changes (Mainly on the portal we will want to do this)
        if (isset($arrOptions['bolScheduleDowngrade'])) {
            $this->bolScheduleDowngrade = $arrOptions['bolScheduleDowngrade'];
        }

        // Are we taking payment for the account change
        if (isset($arrOptions['bolTakePayment'])) {
            $this->_bolTakePayment = $arrOptions['bolTakePayment'];
        }

        // Populate LineCheckId
        if (isset($arrOptions['objLineCheckResult'])
            && $arrOptions['objLineCheckResult'] instanceof LineCheck_Result
        ) {
            $objLineCheckResult = $arrOptions['objLineCheckResult'];
            $this->_intLineCheckId = $objLineCheckResult->getLineCheckId();
        }

        // Old wlr service component id
        if (isset($arrOptions['oldWlrScid'])) {
            $this->_oldWlrScid = $arrOptions['oldWlrScid'];
        }

        // New wlr service component id
        if (isset($arrOptions['newWlrScid'])) {
            $this->_newWlrScid = $arrOptions['newWlrScid'];
        }

        // Old adsl component id
        if (isset($arrOptions['oldAdslComponentId'])) {
            $this->_oldAdslComponentId = $arrOptions['oldAdslComponentId'];
        }

        // Defunct adsl component Ids
        if (isset($arrOptions['defunctAdslComponentIds'])) {
            $this->_defunctAdslComponentIds = $arrOptions['defunctAdslComponentIds'];
        }

        // Old wlr component id
        if (isset($arrOptions['oldWlrComponentId'])) {
            $this->_oldWlrComponentId = $arrOptions['oldWlrComponentId'];
        }

        // New adsl scid
        if (isset($arrOptions['newAdslScid'])) {
            $this->_newAdslScid = $arrOptions['newAdslScid'];
        }

        // House Move
        if (isset($arrOptions['bolHousemove'])) {
            $this->_bolHousemove = $arrOptions['bolHousemove'];
        }

        $this->arrOptions = $arrOptions;
    }

    /**
     * Getter for the new product configuration
     *
     * @return AccountChange_Product_Configuration
     */
    public function getNewProductConfiguration()
    {
        return $this->_objNewProductConfiguration;
    }

    /**
     * Getter for the Service Definition Id
     *
     * @return int
     */
    public function getProductId()
    {
        return $this->_intServiceDefinitionId;
    }

    /**
     * Check to see if the product is a key product
     *
     * @return boolean
     */
    public function isKeyProduct()
    {
        return true;
    }

    /**
     * Sets a serviceDefinitionContract - used for determining the desired contract length.
     *
     * @param AccountChange_Product_ServiceComponentContract $objContract service definition contract
     *
     * @return void
     */
    public function setContract($objContract)
    {
        $this->objServiceDefinitionContract = $objContract;
    }

    /**
     * Is the product going to be scheduled for the change
     *
     * @return boolean
     */
    public function isScheduled()
    {
        $registry = AccountChange_Registry::instance();
        $eventDate = $registry->getEntry('backDatedDate');
        // back dated cant be scheduled
        if (empty($eventDate) && (($this->bolScheduleChange)
            || ($this->bolScheduleDowngrade
            && $this->getAccountChangeOperation() == AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE))
        ) {
            return true;
        }

        return false;
    }

    /**
     * Get the cost of the product
     *
     * @access public
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return int
     * @throws AccountChange_ProductException
     */
    public function getProductCost()
    {
        // Update the database to save the new product
        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $arrDetails = $objDatabase->getServiceDefinitionDetails($this->_intServiceDefinitionId);
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        if (!is_array($arrDetails) || empty($arrDetails)) {
            throw new AccountChange_Product_Exception(
                "Cannot get product details for service definition ID " .
                $this->_intServiceDefinitionId,
                AccountChange_Product_Exception::ERR_CANNOT_RETRIEVE_PRODUCT_DATA
            );
        }

        return $arrDetails['minimum_charge'];
    }

    /**
     * Getter for the action of the account change
     *
     * @return int
     */
    public function getAction()
    {
        return $this->_intAction;
    }

    /**
     * Getter for the account change operation (upgrade or downgrade or same)
     *
     * @return int
     */
    public function getAccountChangeOperation()
    {
        return $this->intAccountChangeOperation;
    }

    /**
     * Getter for the ticket object
     *
     * @return AccountChange_Ticket
     */
    public function getTickets()
    {
        return $this->_tickets;
    }

    /**
     * Getter for service notices
     * (non-PHPdoc)
     *
     * @see Libraries/Product/AccountChange_Product_Configuration::getServiceNotices()
     *
     * @return array
     */
    public function getServiceNotices()
    {
        return $this->_serviceNotices;
    }

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration the account configuration
     *
     * @return void
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->_intAction === AccountChange_Product_Manager::ACTION_CHANGE) {
            $this->_objNewProductConfiguration = $objAccountConfiguration->getProductConfigurationByType(
                AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION
            );
        }
    }

    /**
     * Match the product configuration with another product configuration
     * This can be used if you have the matching object, and you need to perform some checks
     *
     * @param AccountChange_Product_Configuration $objConfiguration the product configuration
     *
     * @return void
     */
    public function setMatchingProductConfigurationManually(AccountChange_Product_Configuration $objConfiguration)
    {
        if ($this->_intAction === AccountChange_Product_Manager::ACTION_CHANGE) {
            if (!$objConfiguration instanceof AccountChange_Product_ServiceDefinition) {
                throw new AccountChange_Product_Exception(
                    'Matching product is not the same product type',
                    AccountChange_Product_Exception::ERR_MATCHING_PRODUCT_NOT_VALID
                );
            }

            $this->_objNewProductConfiguration = $objConfiguration;
            $this->setAccountChange();
        }
    }

    /**
     * Decide whether it will be an upgrade or downgrade for each product
     * Decision made by the value of the products
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration the account configuration object
     *
     * @return void
     */
    public function setAccountChangeOperation(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->_intAction === AccountChange_Product_Manager::ACTION_CHANGE) {
            // We need to have a new product configuration to do the checking we need
            if (!isset($this->_objNewProductConfiguration)) {
                Dbg_Dbg::write(
                    'AccountChange_Product_ServiceDefinition::setAccountChangeOperation : ' .
                    'No new product configuration had been specified, so trying to match one',
                    'AccountChange'
                );
                $this->setMatchingProductConfiguration($objAccountConfiguration);
            }

            $this->setAccountChange();
        }
    }

    /**
     * Setter for the account change
     *
     * Refactored out of the setAccountChangeOperation so that the setMatchingProductConfigurationManually function
     * can also use it, which means we can perform checks before the account change process starts
     *
     * @return void
     */
    protected function setAccountChange()
    {
        if (!isset($this->_objNewProductConfiguration)) {
            throw new AccountChange_Product_Exception(
                'There is no matching product set',
                AccountChange_Product_Exception::ERR_NEW_PRODUCT_NOT_SET
            );
        }

        // BPR09 - R77 - If we're changing between product types, then we don't reset the contract
        //        (hence treat it as an upgrade)
        $bolIsOldSdiBpr09 = Core_ServiceDefinition::instance($this->_intServiceDefinitionId)->isBpr09FamilyProduct();
        $bolIsNewSdiBpr09 = false;

        // We don't need to check if the new sdi is BPR09 if the old one isn't, so let's save the work..
        if ($bolIsOldSdiBpr09) {
            $bolIsNewSdiBpr09 = Core_ServiceDefinition::instance(
                $this->_objNewProductConfiguration->_intServiceDefinitionId
            )->isBpr09FamilyProduct();
        }

        if ($bolIsNewSdiBpr09 && $bolIsOldSdiBpr09) {
            // If we're changing from a 12 to 24 month contract, treat it as a downgrade
            // becuase we want to re-contract in this case and also not charge a fee..
            $objCoreService = $this->getCoreService();
            $strOldContract = $objCoreService->getBroadbandContract();

            if ($strOldContract != '24MONTH' && '24MONTH' == $this->_objNewProductConfiguration->getDesiredContract()) {
                Dbg_Dbg::write(
                    get_class($this) . ' has decided to downgrade because we\'re moving from a 12 to 24 month contract',
                    'AccountChange'
                );

                $this->intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE;
            } else {
                Dbg_Dbg::write(
                    get_class($this) . ' has decided to upgrade because the old and new accounts are BPR09',
                    'AccountChange'
                );

                $this->intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE;
            }
        } elseif ($this->getProductCost() > $this->_objNewProductConfiguration->getProductCost()) {
            Dbg_Dbg::write(get_class($this) . ' has decided to downgrade', 'AccountChange');

            $this->intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE;
        } else {
            Dbg_Dbg::write(get_class($this) . ' has decided to upgrade', 'AccountChange');

            $this->intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE;
        }
    }

    /**
     * Setter for the service id
     *
     * @param int $intServiceId the service id
     *
     * @return void
     */
    public function setServiceId($intServiceId)
    {
        $this->_intServiceId = (int) $intServiceId;
    }

    /**
     * Getter for the Action Manager
     *
     * @param int   $intServiceId     the service id
     * @param array $arrActions       the list of actions
     * @param array $arrActionOptions the options for the actions
     *
     * @return AccountChange_Action_Manager
     */
    protected function getActionManager($intServiceId, array $arrActions = array(), array $arrActionOptions = array())
    {
        return new AccountChange_Action_Manager($intServiceId, $arrActions, $arrActionOptions);
    }

    /**
     * Setter for the array of components we have decided not to keep
     *
     * @param array $arrLegacyComponentNotToKeep array of legacy components
     *
     * @return void
     */
    public function setLegacyComponentNotToKeep($arrLegacyComponentNotToKeep = array())
    {
        $this->_arrLegacyComponentNotToKeep = $arrLegacyComponentNotToKeep;
    }

    /**
     * Process the Internet Connection product
     *
     * We've got three possible scenarios here:
     *  1. You're moving from SD based product to another SD based product - nothing to do in here
     *     as there is no InternetConnection component
     *  2. You're moving from SD based product to SC based product (ValueFamily product in fact) -
     *     you need to add one in that case. That's only applicable for PlusNet ADSL customers moving over to
     *     Value products and won't work for JLP based customers (it's not meant to work
     *     for them anyway and will crash)
     *     LinecheckId passed in <i>$arrOptions</i>to {@link AccountChange_Product_Manager::factoryUsingFamilyObject()}
     *     is used by {@see ProductFamily_Value} class to select a proper Market for a customer
     *  3. Your changing your SC based product - that will simply call
     *     {@link CInternetConnectionProduct::accountTypeChangeHandler()}.
     *     {@see AccountChange_Product_InternetConnection::change()}
     *
     * <AUTHOR> Marek <<EMAIL>>
     * <AUTHOR> Selby <<EMAIL>>
     *
     * @uses CInternetConnectionProduct::getInternetConnectionProductFromServiceId()
     * @uses CInternetConnectionProduct::getDefaultServiceComponentIDForServiceDefinitionID()
     * @uses ProductFamily_Value
     * @uses AccountChange_Product_Manager::factoryUsingFamilyObject()
     * @uses AccountChange_Product_Manager::factoryUsingComponentId()
     *
     * @see AccountChange_Product_Manager
     * @see AccountChange_Product_ServiceComponent::execute()
     *
     * @return bool
     */
    protected function processInternetConnectionProduct()
    {
        $this->includeLegacyFiles();

        $objConnection = $this->getInternetConnectionProductFromServiceId($this->_intServiceId);
        $intNewServiceComponentId = $this->getDefaultServiceComponentIDForServiceDefinitionID(
            $this->_objNewProductConfiguration->getProductId()
        );
        $arrOptions['intNewServiceDefinitionId'] = $this->_objNewProductConfiguration->getProductId();
        $arrOptions['intLineCheckId'] = $this->_intLineCheckId;
        $arrOptions['strContract']  = $this->_objNewProductConfiguration->getDesiredContract();
        $arrOptions['intServiceId'] = $this->_intServiceId;
        $arrOptions['bolHousemove'] = $this->_bolHousemove;

        if (empty($objConnection) && empty($intNewServiceComponentId)) {
            // If staying on ServiceDefinition based product or changing to ServiceDefinition based product
            // then nothing to do here!
            return false;
        } elseif (empty($objConnection) && !empty($intNewServiceComponentId)) {
            // If moving from ServiceDefinition based product over to ServiceComponent based
            // then add it here!
            $objServiceDefinition = $this->getServiceDefinitionDetails($arrOptions['intNewServiceDefinitionId']);

            $intAction = AccountChange_Product_Manager::ACTION_ADD;

            Dbg_Dbg::write(
                'AccountChange_Product_ServiceDefinition::processInternetConnectionProduct not found - adding new one',
                'AccountChange'
            );

            $objFamily = $this->getProductFamily($this->_objNewProductConfiguration->getProductId());

            $objProduct = AccountChange_Product_Manager::factoryUsingFamilyObject($objFamily, $intAction, $arrOptions);
        } else {
            // I see you want to change your component
            $intAction = AccountChange_Product_Manager::ACTION_CHANGE;

            // In this case, we need to pass in the account change operation
            // (upgrade / downgrade) as determined in this class
            // so we decide whether or not to keep the old contracts
            // based on this decision...
            $arrOptions['intAccountChangeOperation'] = $this->getAccountChangeOperation();

            // If we want the boradband contract reset (by checking the recontract
            // option in the WP tool), then we need to pass this in, to override
            // the internet connection contract.
            $arrOptions['bolContractReset']          = $this->_bolContractReset;

            $objProduct = AccountChange_Product_Manager::factoryUsingComponentId(
                $intAction,
                $objConnection->getComponentID(),
                AccountChange_Product_Manager::PRODUCT_TYPE_INTERNET_CONNECTION,
                $arrOptions
            );

            Dbg_Dbg::write(
                'AccountChange_Product_ServiceDefinition::processInternetConnectionProduct found component ' .
                "({$objProduct->getProductId()}) with an instance of ({$objProduct->getComponentId()})",
                'AccountChange'
            );
        }

        $objProduct->setServiceId($this->_intServiceId);
        $this->_arrLegacyComponentTypesToIgnore[] = $objProduct->getProductId();

        return $objProduct->execute();
    }

    /**
     * Alternative flow to take billing snapshots during legacy recontract
     *
     * @return void
     */
    protected function processInternetConnectionProductForRecontract()
    {
        $inventoryEventService = \BusTier_BusTier::getClient('inventoryEventService');
        $inventoryEventService->takePreChangeSnapshot($this->_intServiceId, new RecontractContext());

        $this->processInternetConnectionProduct();

        $inventoryEventService->takePostChangeSnapshot($this->_intServiceId);
    }

    /**
     * Look up the current component id from service id, component status
     * and service component product hanndle
     *
     * @param int    $serviceId       Service id
     * @param string $componentStatus Status from userdata.components
     * @param string $handle          Service component product type handle
     *
     * @return int
     **/
    protected function getComponentIdForTypeAndStatus($serviceId, $componentStatus, $handle)
    {
        $db = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);

        return $db->getComponentIdForTypeAndStatus($componentStatus, $handle, $serviceId);
    }

    /**
     * Getter for the contract end date
     *
     * Returns 0 if the product is not an annual one
     *
     * @return unix time stamp
     */
    public function getContractEndDate()
    {
        $this->includeLegacyFiles();

        $uxtContractEndDate = 0;

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $arrOldProduct = $objDatabase->getServiceDefinitionDetails($this->_intServiceDefinitionId);
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        // Check if the product is an annual product
        if (isset($arrOldProduct['strContract']) && $arrOldProduct['strContract'] == 'Annual') {
            // Get contract information
            $arrEventCriteria = array(
                'service_id'    => $this->_intServiceId,
                'event_type_id' => array(
                    SERVICE_EVENT_12MONTH_CONTRACT_START_ADSL,
                    SERVICE_EVENT_ADSL_ACTIVATED
                )
            );

            $arrEvents = userdata_service_events_find($arrEventCriteria, 'event_date desc', 0, 1);

            if (isset($arrEvents[0]) && is_array($arrEvents[0]) && isset($arrEvents[0]['event_date'])) {
                $uxtEventDate = strtotime($arrEvents[0]['event_date']);
                $uxtContractEndDate = mktime(
                    0,
                    0,
                    0,
                    date("m", $uxtEventDate),
                    date("d", $uxtEventDate),
                    date("Y", $uxtEventDate)+1
                );
            }
        }

        return $uxtContractEndDate;
    }

    /**
     * Get the current contract
     *
     * @return string
     */
    public function getContract()
    {
        $arrOldProduct = Db_Manager::getAdaptor('AccountChange')->getServiceDefinitionDetails(
            $this->_intServiceDefinitionId
        );
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        return $arrOldProduct['strContract'];
    }

    /**
     * Returns the contract that we wish to move onto
     *
     * @return string
     */
    public function getDesiredContract()
    {
        return $this->_strContract;
    }

    /**
     * Calculates the days remaining on the contract
     *
     * Returns 0 if the contract end date is 0, which is set by getContractEndDate
     *
     * @return int
     */
    public function calculateDaysRemainingOnContract()
    {
        $intDaysRemaining = 0;

        $uxtContractEndDate = $this->getContractEndDate();

        if ($uxtContractEndDate > 0) {
            $uxtDifference = $uxtContractEndDate - mktime(0, 0, 0, date("m"), date("d"), date("Y"));
            $intDaysRemaining = round($uxtDifference / (60 * 60 * 24));
            $intDaysRemaining = $intDaysRemaining < 0 ? 0 : $intDaysRemaining;
        }

        return $intDaysRemaining;
    }

    /**
     * Calculate the pro rata charge that needs to be taken if we are upgrading
     *
     * @return array
     */
    public function calculateProRataCharge()
    {
        $this->includeLegacyFiles();
        $objCoreService = $this->getCoreService();
        $strNextInvoice = $objCoreService->getNextInvoiceDate()->toI18nStrHere('SHORT_DB_DATE_FORMAT');
        $arrUpgradeCost = financial_calculate_refund(
            $this->_intServiceId,
            $this->_objNewProductConfiguration->getProductId(),
            $this->_intServiceDefinitionId,
            '',
            array(),
            $strNextInvoice,
            $strNextInvoice,
            'monthly',
            $objCoreService->getInvoicePeriod()
        );

        if (!empty($arrUpgradeCost['error']) || !isset($arrUpgradeCost['amount']) || $arrUpgradeCost['amount'] == 0) {
            return array();
        }

        return array(
            'strOutstandingCharges' =>
                "Pro rata charge for the remaining ".
                "{$arrUpgradeCost['days_remaining']} days of broadband product subscription",
            'intOutstandingFees'    =>
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, abs($arrUpgradeCost['amount']))
        );
    }

    /**
     * Getter for whether are taking payment or not
     *
     * @return boolean
     */
    public function isTakingPayment()
    {
        Dbg_Dbg::write(
            "AccountChange_Product_ServiceDefinition::isTakingPayment ({$this->_bolTakePayment})",
            'AccountChange'
        );

        return $this->_bolTakePayment;
    }

    /**
     * Perform the product change
     *
     * @return void
     */
    public function execute()
    {
        switch ($this->_intAction) {
            case AccountChange_Product_Manager::ACTION_REMOVE:
                $this->remove();
                break;
            case AccountChange_Product_Manager::ACTION_ADD:
                $this->create();
                break;
            case AccountChange_Product_Manager::ACTION_REFRESH:
                $this->refresh();
                break;
            case AccountChange_Product_Manager::ACTION_CHANGE:
                $this->change();
                break;
            default:
                break;
        }
    }

    /**
     * Removal of the service definition configuration
     *
     * @return void
     */
    protected function remove()
    {
        // Not implemented yet
        // This may never be used
    }

    /**
     * Create the service definition configuration
     *
     * @return void
     */
    protected function create()
    {
        // Not implemented yet
        // This may never be used
    }

    /**
     * Refresh the service definition configuration
     *
     * @return void
     */
    protected function refresh()
    {
        // Not implemented yet
        // This may never be used
    }

    /**
     * Wrapper for resetting usage using GenerateCbcServiceBills_InstantAccountChangeUsageReset
     *
     * @return void
     */
    protected function resetUsage()
    {
        $usageReset = new GenerateCbcServiceBills_InstantAccountChangeUsageReset($this->_intServiceId);
        $usageReset->process();
    }

    /**
     * Make the decision about whether are scheduling the change or whether we actually
     * making the change instantly
     *
     * @return void
     */
    protected function change()
    {
        $currentIsFibre = $this->isFibreProduct($this->_intServiceDefinitionId);
        $selectedIsFibre = $this->isFibreProduct($this->getNewServiceDefinitionId());
        $adslToFttc = (!$currentIsFibre && $selectedIsFibre);

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('$adslToFttc', $adslToFttc);

        // Sets the 'selfInstallDate' registry entry with the self install date
        $schedulingHelper = $this->getSchedulingHelper();
        $schedulingHelper->calculateSelfInstallDate(
            $this->_intServiceDefinitionId,
            $this->getNewServiceDefinitionId()
        );
        $eventDate = $registry->getEntry('backDatedDate');

        $this->scheduleChange($this->bolScheduleChange);
        if ($this->isNotScheduledOrDowngrade($eventDate)) {
            if (!$this->isBvUser() && !$this->isPartnerEnduser()) {
                $this->resetUsage();
            }
            $registry->setEntry('completeServiceChangeSchedule', true);
            $this->changeAccount();
        }
    }

    /**
     * Checks if the change is not scheduled or a downgrade
     *
     * @param string $eventDate
     * @return bool
     */
    protected function isNotScheduledOrDowngrade($eventDate)
    {
        return !$this->bolScheduleChange &&
            ($this->intAccountChangeOperation !== AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE
                || !$this->bolScheduleDowngrade
                || !empty($eventDate));
    }

    /**
     * Get current cbc flex id
     *
     * @return int
     */
    protected function getCurrentFlexId()
    {
        //get current flex id
        $intCurrentFlexId = 0;
        $intFlexComponentId = GetCBCFlexActiveComponentID($this->_intServiceId);
        if ($intFlexComponentId > 0) {
            $intCurrentFlexId = ConfigFlexGetCBCFlexID($intFlexComponentId);
        }

        return $intCurrentFlexId;
    }

    /**
     * Change the account instantly
     *
     * Perform all the necessary actions that are needed to alter the broadband
     * aspect of a customers account
     *
     * @return void
     */
    protected function changeAccount()
    {
        $bolContractOnly = $this->alterContractOnly();

        if ($bolContractOnly) {
            return;
        }

        $intCurrentFlexId = $this->getCurrentFlexId();

        // If account has DTW, clear in RBM 'before' servicedef is changed as billingAPI will return a 500 for DTW
        // actions on unlimited accounts.
        $limitedAccount = (bool)$this->isLimitedAccount($this->_intServiceId);
        if ($limitedAccount) {
            if (!$this->isPartnerAccount() && !$this->isPartnerEnduser()) {
                $this->resetDtwRBM();
            }
        }

        // Update the database to save the new product
        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $objDatabase->setServiceDefinitionForService(
            $this->_objNewProductConfiguration->getProductId(),
            $this->_intServiceId
        );

        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $registry = AccountChange_Registry::instance();
        if (empty($registry->getEntry('instantRecontract'))) {
            $this->processInternetConnectionProduct();
        } else {
            $this->processInternetConnectionProductForRecontract();
        }

        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        // Actions that need to be performed as part of the account change process
        // **IMPORTANT!** It matters what order these actions are defined in
        // this array, so please be careful!!
        $actions = array(
            AccountChange_Action_Manager::ACTION_PRODUCTS,
            AccountChange_Action_Manager::ACTION_DISCOUNTS,
            AccountChange_Action_Manager::ACTION_LEGACY_COMPONENTS,
            AccountChange_Action_Manager::ACTION_ELLACOYA,
            AccountChange_Action_Manager::ACTION_CBC,
            AccountChange_Action_Manager::ACTION_RADIUS,
            AccountChange_Action_Manager::ACTION_STATIC_IP,
            AccountChange_Action_Manager::ACTION_BILLING,
            AccountChange_Action_Manager::ACTION_CONTRACT,
            AccountChange_Action_Manager::ACTION_HARDWARE,
            AccountChange_Action_Manager::ACTION_UPDATE_SUPPLIER_PRODUCT,
            AccountChange_Action_Manager::ACTION_GB_WLR_PRODUCT_SET_CHANGE_JL,
            AccountChange_Action_Manager::ACTION_GB_WLR_BULLGUARD_JL,
            AccountChange_Action_Manager::ACTION_CONSENT,
            AccountChange_Action_Manager::ACTION_REGISTER_CHARGES
        );

        if ($registry->getEntry('bolVariantSwitchForWlrAddOrRemove')) {
            $actions = array(
                AccountChange_Action_Manager::ACTION_PRODUCTS,
                AccountChange_Action_Manager::ACTION_ELLACOYA,
                AccountChange_Action_Manager::ACTION_CONTRACT,
                AccountChange_Action_Manager::ACTION_CONSENT
            );
        }

        $wlrChanging = false;
        if ($this->_oldWlrScid != $this->_newWlrScid) {
            $wlrChanging = true;
        }

        //If there is WLR change we need to process contracts after WLR component is created
        //which will be done in AccountChange_Product_Wlr class
        if ((empty($this->_newWlrScid) || !$wlrChanging) && !$this->_bolHousemove) {
            $actions[] = AccountChange_Action_Manager::ACTION_LTC_CONTRACTS;
        }

        // If broadband product is changing, add this action, which will remove
        // the customer cli if appropriate
        if ($this->_intServiceDefinitionId != $this->_objNewProductConfiguration->getProductId()) {
            $actions[] = AccountChange_Action_Manager::ACTION_BROADBAND_ONLY_CLI;
        }

        //If there's a request to not wipe the discounts, remove the Discount action.
        if ($registry->getEntry('retainDiscounts') === true && !$registry->getEntry('migrateBroadbandDiscount') === true) {
            if (($key = array_search(AccountChange_Action_Manager::ACTION_DISCOUNTS, $actions)) !== false) {
                unset($actions[$key]);
            }
        }

        $objActionManager = $this->buildActionManager($actions);
        $objActionManager->execute();

        $intServiceEventId = $this->logChange();
        $eventDate = $registry->getEntry('backDatedDate');
        if ($eventDate!=null && !empty($eventDate)) {
            $this->insertBackDatedProductChange($intServiceEventId, $eventDate, $objDatabase);
        }

        $this->processIncludedBandwith($intServiceEventId, $intCurrentFlexId);
        $this->processDataUsageLimit();

        // Update userdata.tblCustomerExchange with the latest market and exchange ids
        $exchangeHelper    = $this->getExchangeHelper();
        $exchangeAndMarket = $exchangeHelper->getMarketAndExchangeIdFromRegistry();

        // NOTE: it's possible to have a linecheck object which is valid but empty (i.e. the market/exchange values
        // are set to null) - this happens when the customer doesn't have a cached linecheck.
        // We therefore don't attempt to update in this scenario, as otherwise an exception will be thrown
        if ($exchangeAndMarket !== false
            && isset($exchangeAndMarket['marketId'], $exchangeAndMarket['exchangeId'])
        ) {
            $exchangeHelper->maintainCustomerExhangeData(
                $this->_intServiceId,
                $exchangeAndMarket['marketId'],
                $exchangeAndMarket['exchangeId']
            );
        }

        $this->raiseAccountChangeServiceNotice();
    }

    /**
     * Get an exchange helper object
     *
     * @return AccountChange_ExchangeHelper
     **/
    public function getExchangeHelper()
    {
        return new AccountChange_ExchangeHelper();
    }

    /**
     * Build the actions we want to run
     *
     * @param array $actions Array of actions to execute
     *
     * @return AccountChange_Action_Manager
     */
    public function buildActionManager($actions)
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intOldServiceDefinitionId', $this->_intServiceDefinitionId);
        $registry->setEntry('intNewServiceDefinitionId', $this->_objNewProductConfiguration->getProductId());
        $registry->setEntry('newWlrServiceComponentId', $this->_newWlrScid);
        $registry->setEntry('oldWlrServiceComponentId', $this->_oldWlrScid);
        $registry->setEntry('oldWlrComponentId', $this->_oldWlrComponentId);
        $registry->setEntry('newAdslServiceComponentId', $this->_newAdslScid);
        $registry->setEntry('oldAdslComponentId', $this->_oldAdslComponentId);
        $registry->setEntry('defunctAdslComponentIds', $this->_defunctAdslComponentIds);
        $registry->setEntry('bolScheduleChange', $this->bolScheduleChange);

        $newAdslComponentId = null;
        $newWlrComponentId = null;
        if (!$this->isScheduled()) {
            // After processInternetConnectionProduct has run here we will have a new
            // INTERNET_CONNECTION component id on the account.
            $adslComponentId = $this->getComponentIdForTypeAndStatus(
                $this->_intServiceId,
                'active',
                'INTERNET_CONNECTION'
            );

            if (is_array($adslComponentId) && count($adslComponentId) == 1) {
                $newAdslComponentId = $adslComponentId[0];
            }
        }

        // If we've got a new wlr product, at this point it will be in an
        // unconfigured state. Wlr components will be created when schduling an account change,
        // so we should already have a component id here.
        if (!empty($this->_newWlrScid)) {
            $wlrComponentId = $this->getComponentIdForTypeAndStatus(
                $this->_intServiceId,
                'unconfigured',
                'WLR'
            );

            if (is_array($wlrComponentId) && count($wlrComponentId) == 1) {
                $newWlrComponentId = $wlrComponentId[0];
            }
        }

        $registry->setEntry('newAdslComponentId', $newAdslComponentId);
        $registry->setEntry('newWlrComponentId', $newWlrComponentId);

        $registry->setEntry('arrLegacyComponentNotToKeep', $this->_arrLegacyComponentNotToKeep);
        $registry->setEntry('arrLegacyComponentTypesToIgnore', $this->_arrLegacyComponentTypesToIgnore);

        if ($registry->getEntry('hardwareOption')) {
            $this->arrOptions['hardwareOption'] = $registry->getEntry('hardwareOption');
        } else {
            $registry->setEntry(
                'hardwareOption',
                (isset($this->arrOptions['hardwareOption']) ? $this->arrOptions['hardwareOption'] : false)
            );
        }

        $registry->setEntry(
            'strProvisionOn',
            (isset($this->arrOptions['strProvisionOn']) ? $this->arrOptions['strProvisionOn'] : false)
        );
        $registry->setEntry(
            'bolWbcProduct',
            (isset($this->arrOptions['bolWbcProduct']) ? $this->arrOptions['bolWbcProduct'] : false)
        );
        $registry->setEntry(
            'objLineCheckResult',
            (isset($this->arrOptions['objLineCheckResult']) ? $this->arrOptions['objLineCheckResult'] : null)
        );
        $registry->setEntry('intAccountChangeOperation', $this->intAccountChangeOperation);
        $registry->setEntry('bolSchedule', $this->isScheduled());

        // Get an instance of an action manager with the actions we want to run
        $objActionManager = $this->getActionManager($this->_intServiceId, $actions, $this->arrOptions);

        return $objActionManager;
    }

    /**
     * Change/add flex CBC component if required
     *
     * @param int $intServiceEventId the service event id
     * @param int $intCurrentFlexId  the current flex id
     *
     * @return boolean
     */
    protected function processIncludedBandwith($intServiceEventId, $intCurrentFlexId)
    {
        $bolReturn = false;
        $this->includeLegacyFiles();
        $intNewSdi = $this->_objNewProductConfiguration->getProductId();
        $arrProduct = product_get_account_attributes($intNewSdi);

        //as we do not have a frontend to select flexid - use a default one

        //assume uncapped product
        $intZeroCostFlexId = 0;

        if (isset($arrProduct['bolCapped']) && ($arrProduct['bolCapped'] == true)) {
            //get flex ids for this type of account
            $arrCBCFlexIDs = GetCurrentCBCFlexIDs($intNewSdi);
            $intZeroCostFlexId = empty($arrCBCFlexIDs[0]) ? 0 : $arrCBCFlexIDs[0];
        }

        //change included bandwith with current and zerocost flex
        if (($intZeroCostFlexId > 0 || $intCurrentFlexId > 0)) {
            // Cancel any requested downgrades as this will override them
            $intCBCFlexDowngradeLogID = GetCurrentCBCFlexDowngradeLogID($this->_intServiceId);

            if ($intCBCFlexDowngradeLogID > 0) {
                EndCBCFlexDowngradeLogID($intCBCFlexDowngradeLogID);
            }

            //get usable flex component id
            $intCBCFlexComponentId = GetCBCFlexUsableComponentID($this->_intServiceId);

            // set the CBC Flex component to the zero cost included bandwidth option
            if ($intCBCFlexComponentId > 0) {
                $bolReturn = ConfigFlexConfigurator($intCBCFlexComponentId, 'auto_configure', $intZeroCostFlexId);
            }

            InsertCBCFlexServiceLog($this->_intServiceId, $intCurrentFlexId, $intZeroCostFlexId, $intServiceEventId);
        }

        return $bolReturn;
    }

    /**
     * Only change the contract if the product ids are the same,
     * We do not need change or run anything else on the account
     *
     * @return boolean
     */
    protected function alterContractOnly()
    {
        // If we are just restarting the contract, then we don't need to do
        // anything to their product, just specify a new service event
        if ($this->_intServiceDefinitionId == $this->_objNewProductConfiguration->getProductId()
            && $this->_bolContractReset
        ) {
            $arrActions = array(
                AccountChange_Action_Manager::ACTION_CONTRACT,
                AccountChange_Action_Manager::ACTION_LTC_CONTRACTS,
                AccountChange_Action_Manager::ACTION_ELLACOYA,
                AccountChange_Action_Manager::ACTION_CBC,
                AccountChange_Action_Manager::ACTION_RADIUS,
            );
            $objActionManager = $this->getActionManager($this->_intServiceId, $arrActions);
            $objActionManager->execute();

            return true;
        }

        return false;
    }

    /**
     * Log the actual product change
     *
     * @return void
     */
    protected function logChange()
    {
        $this->includeLegacyFiles();

        // BPR09 - we need to log whether the account change was actioned by a Workplace or end user.
        return userdata_service_log_type_change(
            $this->_intServiceId,
            $this->_intServiceDefinitionId,
            $this->_objNewProductConfiguration->getProductId(),
            '',
            '',
            $this->getBusinessActorId()
        );
    }

    /**
     * Method to check whether its product change happened is backdated
     * if so, then it will insert a new entry
     *
     * @param int        $intServiceEventId service event id
     * @param date       $eventDate         backdated date
     * @param Db_Manager $objDatabase       DB adaptor
     *
     * @return void
     */
    private function insertBackDatedProductChange($intServiceEventId, $eventDate, $objDatabase)
    {
        $backDatedOn = DateTime::createFromFormat('d/m/Y', $eventDate);
        $backDatedOn = $backDatedOn->format('Y-m-d');

        $externalActorId = $this->getBusinessActorId();
        $businessActor = Auth_Auth::getCurrentLogin()->getBusinessActor();
        if ($businessActor) {
            $externalActorId = $businessActor->getExternalUserId();
        }

        if ($backDatedOn <= date('Y-m-d')) {//backdated product change
            $objDatabase->insertBackDatedProductChange(
                $intServiceEventId,
                $backDatedOn,
                $externalActorId,
                date('Y-m-d H:i:s')
            );
        }
    }

    /**
     * Schedule the account change
     *
     * @param bool $isScheduledChange Activates specific behaviour for a scheduled account change that will not be
     * triggered for instant
     *
     * @return void
     */
    protected function scheduleChange($isScheduledChange = true)
    {
        $this->includeLegacyFiles();

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $arrCurrentProduct = $objDatabase->getServiceDefinitionDetails($this->_intServiceDefinitionId);
        $arrNewProduct = $objDatabase->getServiceDefinitionDetails(
            $this->_objNewProductConfiguration->getProductId()
        );

        $currentSdid = $this->_intServiceDefinitionId;
        $newSdid = $this->_objNewProductConfiguration->getProductId();

        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $hasAppointment = isset($this->arrOptions['appointing']['appointingType']);

        if ($hasAppointment) {
            // If there's an appointment involved, then the account change is scheduled for null.
            // It's completed on the day the engineer goes out.
            $processQueue = new ProcessQueue_AccountChange($this->_intServiceId);
            $processQueue->addOnHoldReason(ProcessQueue_Manager::FIBRE_NOT_ACTIVE);
        }

        $schedulingHelper = $this->getSchedulingHelper();
        if ($this->_bolHousemove) {
            $changeDate = null;
        } else {
            $changeDate = $schedulingHelper->calculateScheduledChangeDate($this->_intServiceId, $hasAppointment);
        }
        $overiddenChangeDate = $schedulingHelper->getOveriddenChangeDate();

        if (($this->_bolHousemove || $currentSdid != $newSdid) && $isScheduledChange) {
            // Only raise a service note if the BB product has changed

            $this->_serviceNotices[] = $schedulingHelper->buildProductChangeServiceNotice(
                $arrCurrentProduct['name'],
                $arrNewProduct['name'],
                $overiddenChangeDate,
                $changeDate
            );
        }

        $registry = AccountChange_Registry::instance();
        $intTariffID = $registry->getEntry('intSelectedTariffID');

        // process promoCode
        $promoCode = $registry->getEntry('promoCode');
        $impressionOfferId = $registry->getEntry('impressionOfferId');

        $recontractingLength = $registry->getEntry('selectedContractDuration');

        $promotionCodeId = null;

        if (!empty($promoCode)) {
            // get intPromotionCodeId for the promoCode
            $objDatabase = Db_Manager::getAdaptor('AccountChange');
            $promotionCodeId = $objDatabase->getPromotionCodeId(
                $promoCode,
                $this->_objNewProductConfiguration->getProductId()
            );
        }

        // If we're changing service definition id or are re-contracting only, record the
        // change in the scheduled changes table.
        if ($this->_bolHousemove || $currentSdid != $newSdid || $recontractingLength !== null) {
            $scheduleCreated = $this->addServiceScheduleToCoreDb($changeDate, $intTariffID, $promotionCodeId);

            if ($scheduleCreated && !empty($promoCode) && $promotionCodeId == null) {
                if ($this->isValidC2MPromotionCode($promoCode)) {
                    $this->insertC2MPromotionCode($promoCode, $recontractingLength, $impressionOfferId);
                    if (!empty($impressionOfferId)) {
                        $this->registerPegaInteraction($impressionOfferId, AccountChange_PegaInteractionHelper::ACCEPTED, $registry->getEntry('bolPortal'));
                    }
                } else {
                    $message = "Unable to apply promotion code [" .  $promoCode . "] to service ID ["
                        . $this->_intServiceId . "] during account change. This is not a valid component or C2M code.";
                    error_log($message);
                    $this->_serviceNotices[] = new AccountChange_ServiceNotice($message);
                }
            }
            if ($scheduleCreated) {
                $this->updateScheduledChangeWithLoggedInActorId();
            }
        }

        if ($isScheduledChange) {

            $registry->setEntry('changeDateUsedLast', $changeDate);

            $registry->setEntry(
                'appointing',
                (isset($this->arrOptions['appointing']) ? $this->arrOptions['appointing'] : null)
            );
            $registry->setEntry(
                'arrSelectedWlr',
                (isset($this->arrOptions['arrSelectedWlr']) ? $this->arrOptions['arrSelectedWlr'] : null)
            );

            // Need to configure the registry for http://jira.internal.plus.net/browse/JLPR-1481
            // GB/WR to John Lewis
            $marketId = 1;
            $objLineCheckResult = null;

            if (isset($this->arrOptions['objLineCheckResult'])) {
                $lineCheckMarket = LineCheck_Market::getMarketFromExchange(
                    $this->arrOptions['objLineCheckResult']->getExchangeCode()
                );
                $marketId = $lineCheckMarket->getMarketId();
                $objLineCheckResult = $this->arrOptions['objLineCheckResult'];
            }

            $productsAndError = $this->controllerGetBroadbandProducts(
                $this->_intServiceId,
                $marketId,
                false,
                $objLineCheckResult
            );

            if (isset($productsAndError['arrProducts'])) {
                // residential case (member centre)
                $productSet = $productsAndError['arrProducts'];
            } else {
                // business / john lewis case
                $productSet = $productsAndError;
            }

            foreach ($productSet as $product) {
                if ($product['intSdi'] == $this->_objNewProductConfiguration->getProductId()) {
                    $registry->setEntry('newProductName', $product['strProductName']);
                    $registry->setEntry('newProductCost', $product['intProductCost']);
                    break;
                }
            }

            // Actions that need to be performed as part of the account change process
            // **IMPORTANT!** It matters what order these actions are defined in
            // this array, so please be careful!!
            $actions = array(
                AccountChange_Action_Manager::ACTION_APPOINTMENT,
                AccountChange_Action_Manager::ACTION_REGRADE,
                AccountChange_Action_Manager::ACTION_HARDWARE,
                AccountChange_Action_Manager::ACTION_GB_WLR_PRODUCT_SET_CHANGE_JL,
                AccountChange_Action_Manager::ACTION_CONSENT,
                AccountChange_Action_Manager::ACTION_REGISTER_CHARGES,
                AccountChange_Action_Manager::ACTION_CALLER_DISPLAY
            );

            $wlrChanging = false;
            if ($this->_oldWlrScid != $this->_newWlrScid) {
                $wlrChanging = true;
            }

            //If there is WLR change we need to process contracts after WLR component is created
            //which will be done in AccountChange_Product_Wlr class
            if ((empty($this->_newWlrScid) || !$wlrChanging) && !($this->_bolHousemove)) {
                $actions[] = AccountChange_Action_Manager::ACTION_LINE_RENTAL_SAVER_ADD;
                $actions[] = AccountChange_Action_Manager::ACTION_LTC_CONTRACTS;
            }

            $objActionManager = $this->buildActionManager($actions);
            $objActionManager->execute();
        }
    }

    /**
     * Registers a successful offer with Pega
     *
     * @param int    $impressionOfferId  Impression offer id
     * @param string $interaction        Interaction status (e.g. 'Accepted')
     * @param bool   $isExternalCustomer Check to use customer or agent channels
     *
     * @return void
     */
    protected function registerPegaInteraction($impressionOfferId, $interaction, $isExternalCustomer)
    {
        $actor = $this->getLoggedInOrScriptActor();
        $pegaHelper = $this->getPegaHelper();

        $registry = AccountChange_Registry::instance();
        $sessionID = $registry->getEntry('overridenBusinessTierSessonId');
        $actorId = $registry->getEntry('overriddenActorId');
        if (!empty($sessionID) && !empty($actorId)) {
            $pegaHelper->registerInteraction($impressionOfferId,
                $interaction,
                $actorId,
                $isExternalCustomer,
                'Positive',
                'Inbound',
                null,
                $sessionID);
        } else {
            $pegaHelper->registerInteraction($impressionOfferId, $interaction, $actor->getActorId(), $isExternalCustomer);
        }
    }

    /**
     * @return AccountChange_PegaHelper
     */
    protected function getPegaHelper()
    {
        return new AccountChange_PegaHelper();
    }


    /**
     * Wrapper of AccountChange_Controller::getBroadbandProducts for Mocking
     *
     * @param integer          $serviceId  The Service Id
     * @param integer          $marketId   The Market Id for the customer
     * @param boolean          $includeAll Do we need to include all products
     * @param LineCheck_Result $lineCheck  Line check result
     *
     * @return array
     */
    public function controllerGetBroadbandProducts(
        $serviceId,
        $marketId,
        $includeAll,
        $lineCheck = null
    ) {
        // get promo code
        $promoCode = AccountChange_Registry::instance()->getEntry('promoCode');

        return AccountChange_Controller::getBroadbandProducts(
            $serviceId,
            $marketId,
            $includeAll,
            $lineCheck,
            $promoCode
        );
    }

    /**
     * Adds the service schedule change to the database
     *
     * @param $changeDate
     * @param $intTariffID
     * @param $promotionCodeId
     * @return int
     */
    protected function addServiceScheduleToCoreDb($changeDate, $intTariffID, $promotionCodeId)
    {
        return userdata_service_schedule_add(
            $this->_intServiceId,
            $this->_objNewProductConfiguration->getProductId(),
            $changeDate,
            $this->getBusinessActorId(),
            0,
            '',
            $this->getNewProductConfiguration()->getDesiredContract(),
            $intTariffID,
            $promotionCodeId
        );
    }

    /**
     * Getter for the core service
     *
     * @return Core_Service
     */
    protected function getCoreService()
    {
        // We already know that committing the default transaction
        // is wrong, however the legacy code uses private
        // transactions all over the place
        // A decision was made to commit the default transaction
        $service = new Core_Service($this->_intServiceId);
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        return $service;
    }

    /**
     * Raise a service notice stating what happened in the account change
     *
     * @return void
     */
    protected function raiseAccountChangeServiceNotice()
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $oldDetails = $db->getServiceDefinitionDetails($this->_intServiceDefinitionId);
        $newDetails = $db->getServiceDefinitionDetails($this->_objNewProductConfiguration->getProductId());
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $notice = 'User account type changed from ' . $oldDetails['name'] . ' to ' . $newDetails['name'];
        $this->_serviceNotices[] = new AccountChange_ServiceNotice($notice);
    }

    /**
     * Getter for the business actor id
     *
     * @return int
     */
    public function getBusinessActorId()
    {
        if (isset($this->_intBusinessActorId)) {
            return $this->_intBusinessActorId;
        } else {
            return Auth_Auth::getCurrentLogin()->getBusinessActor()->getExternalUserId();
        }
    }

    /**
     * Setter for the business actor id
     *
     * @param int $intBusinessActorId the business actor id
     *
     * @return void
     */
    public function setBusinessActorId($intBusinessActorId)
    {
        $this->_intBusinessActorId = $intBusinessActorId;
    }

    /**
     * Gets the service definition id of the new product
     *
     * @return int
     **/
    public function getNewServiceDefinitionId()
    {
        return $this->_objNewProductConfiguration->_intServiceDefinitionId;
    }

    /**
     * Gets line check speed caps for a given service definition id
     *
     * @param int $sdi Service definition id
     *
     * @return array
     **/
    protected function getSpeedCaps($sdi)
    {
        $productRules = AccountChange_ProductRules::instance();

        return $productRules->getLinecheckSpeedCaps($sdi);
    }

    /**
     * Send the emails to confirm what just happened
     *
     * @param array $arrData the data for the email
     *
     * @uses AccountChange_Product_ServiceDefinition::fetchContractDetails()
     * @uses AccountChange_Product_ServiceDefinition::fetchAnnualContractDetails()
     * @uses AccountChange_Product_ServiceDefinition::fetchCbcUsageDetails()
     * @uses AccountChange_Product_ServiceDefinition::sendEmail()
     *
     * @see LineCheck_Result
     * @see Core_Service
     *
     * @return void
     */
    public function sendConfirmationEmail(array $arrData = array())
    {
        $arrMailVariables = array();

        $arrMailVariables['bolSpeedChecked'] = false;

        $serviceDefinitionId = $this->getNewServiceDefinitionId();

        if (!empty($arrData['objLineCheckResult'])) {
            $arrMailVariables['bolSpeedChecked'] = true;

            $lineCheckResult = $arrData['objLineCheckResult'];

            $productSpeedRanges = $this->getMinAndMaxSpeedRanges($lineCheckResult);

            $arrMailVariables['estimatedSpeedRangeLow'] = $productSpeedRanges['minimumEstimatedDownloadSpeedMbs'];
            $arrMailVariables['estimatedSpeedRangeHigh'] = $productSpeedRanges['maximumEstimatedDownloadSpeedMbs'];

            $supplierProductRules = $this->getSupplierProductRules($lineCheckResult);

            // We need to change the line check result to reflect the new product, not the original one by passing in
            // new capping values for up/downstream speed based on the service definition id:
            $capping = $this->getSpeedCaps($serviceDefinitionId);

            $lineCheckResult->amendResultWithNewCapping($capping['downCap'], $capping['upCap']);

            $intMaxSpeedKbps = $lineCheckResult->getHighestAvailableSpeed(
                $supplierProductRules->getSupplierPlatform()->getSupplierPlatformId()->getValue(),
                $supplierProductRules->getSupplierProduct()->getSupplierProductId()->getValue()
            );

            $arrMailVariables['strEstimatedSpeed'] = $lineCheckResult->getOfcomCompliantSpeedEstimate($intMaxSpeedKbps);
        } else {
            $arrMailVariables['bolOptoutMessage'] = $arrData['bolLinecheckOptOut'];
        }

        $arrMailVariables['strProduct'] = $arrData['arrSelectedBroadband']['strNewProduct'];
        $arrMailVariables['objOngoingProductCost'] = $arrData['objOngoingProductCost'] ;
        $arrMailVariables['objDiscountedProductCost'] = $arrData['objDiscountedProductCost'] ;
        $arrMailVariables['floOngoingProductCost'] = $arrData['floOngoingProductCost'];
        $arrMailVariables['floDiscountedProductCost'] = $arrData['floDiscountedProductCost'];
        $arrMailVariables['intDiscountLength'] = $arrData['intDiscountLength'];

        $productFamily = $this->getProductFamily($serviceDefinitionId);

        $arrMailVariables['isNewProductDualPlay'] = $productFamily->isDualPlay();

        if ($productFamily->hasAutoContracts()) {
            $contractsClient = BusTier_BusTier::getClient('contracts');
            $contractsClient->setServiceId($this->_intServiceId);

            $contractsCriteria = array(
                'status' => Contract::STATUS_ACTIVE,
                'serviceId' => $this->_intServiceId,
            );

            $contract = $contractsClient->getContract($contractsCriteria);

            if ($contract && $contract->getStatus() == Contract::STATUS_ACTIVE) {
                $isContractRetained = true;
                $currentDate = I18n_Date::fromString(date('Y-m-d'));
                $contractStartDate = I18n_Date::fromString($contract->getStartDate());

                if ($currentDate->getTimestamp() == $contractStartDate->getTimestamp() &&
                    $contract->getCreationReason() == \Plusnet\ContractsClient\Entity\CreationReason::ACCOUNT_CHANGE
                ) {
                    $isContractRetained = false;
                }

                $arrMailVariables['isContractRetained'] = $isContractRetained;
                $duration = $contract->getDuration();
                $arrContractDetails['intContractLengthInMonths'] = $duration['value'];
                $arrContractDetails['objContractEndDate'] = $contract->getEndDate();
            }
        } else {
            $arrContractDetails = $this->fetchContractDetails();

            if (empty($arrContractDetails)) {
                $arrContractDetails = $this->fetchAnnualContractDetails();
            }
        }

        if (!empty($arrContractDetails)) {
            $arrMailVariables['intContractLengthInMonths'] = $arrContractDetails['intContractLengthInMonths'];
            $arrMailVariables['objContractEndDate'] = $arrContractDetails['objContractEndDate'];
        }

        $arrCbcUsageDetails = $this->fetchCbcUsageDetails();
        $arrMailVariables['bolDisplayUsageInfo'] = $arrCbcUsageDetails['bolDisplayUsageInfo'];
        $arrMailVariables['intCappedBandwidthLimit'] = $arrCbcUsageDetails['intCappedBandwidthLimit'];

        $arrMailVariables['strUserRealm'] = $this->adslGetUserRealm($this->_intServiceId);

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $arrDetails = $objDatabase->getServiceDefinitionDetails($this->_intServiceDefinitionId);

        if ('business' == $arrDetails['type']) {
            $strTemplateFile = 'adsl2adsl_business';
        } elseif ($this->checkCustomerSwitchingFromGbWrToJlp($arrData['intOldSdi'], $arrData['intNewSdi'])) {
             $strTemplateFile = 'adsl2adsl_from_gbwr';
        } else {
            $strTemplateFile = 'adsl2adsl';
        }

        $arrMailVariables['bolFibreToFibre'] = false;

        if ($this->isFibreProduct($this->getNewServiceDefinitionId()) && $this->isFibreProduct($this->getProductId())) {
            $arrMailVariables['bolFibreToFibre'] = true;
        }

        if (!empty($arrData['installationTypeFTTP'])) {
            $arrMailVariables['installationTypeFTTP'] = $arrData['installationTypeFTTP'];
        }

        $this->sendEmail($this->_intServiceId, $strTemplateFile, $arrMailVariables);

        return;
    }

    /**
     * Send a fibre appointment confirmation email if required
     *
     * @param array $arrData array of account change data
     *
     * @return void
     */
    public function sendAppointmentEmail(array $arrData = array())
    {
        $arrMailVariables['fibreAppointments'] = array();
        $bolFoundDates = false ;
        // check for fibre appointing
        if (isset($arrData['appointingType'])
            && is_array($arrData['appointingType'])
            && isset($arrData['appointingType']['serviceHandle'])
            && $arrData['appointingType']['serviceHandle'] == 'FTTC'
        ) {
            // check live appointing
            if (isset($arrData['liveAppointing']) && $arrData['liveAppointing'] == 1) {
                $arrMailVariables['fibreAppointments']['liveAppointing'] = true;
                //  arrData [appointment] should contain something like "03/07/2012AM"
                if (isset($arrData['appointment'])
                    && preg_match('/^\d\d\/\d\d\/\d\d\d\d(AM|PM)$/', $arrData['appointment'])
                ) {
                    $arrMailVariables['fibreAppointments']['Date1']
                        = substr($arrData['appointment'], 0, 10) . ' ' . substr($arrData['appointment'], 10, 2);
                    $bolFoundDates = true;
                }

                // check other appointments
            } else {
                for ($count=1; $count<=3; $count++) {
                    if (isset($arrData['appointmentdate'.$count])
                        && isset($arrData['appointmenttime'.$count])
                        && preg_match('/^\d{10}$/', $arrData['appointmentdate'.$count])
                        && preg_match('/^(AM|PM)$/', $arrData['appointmenttime'.$count])
                    ) {
                        $arrMailVariables['fibreAppointments']['Date'.$count]
                            = date('d/m/Y', $arrData['appointmentdate'.$count]).' '.$arrData['appointmenttime'.$count];
                        $bolFoundDates = true;
                    }
                }
            }

            if ($bolFoundDates == true) {
                $this->sendEmail($this->_intServiceId, 'fibre_install_dates', $arrMailVariables);
            }
        }
    }

    /**
     * Fetches InternetConnection subscription component contract details for given
     * customer
     *
     * Returns the following array:
     * <pre>
     *  array (
     *    'intContractLengthInMonths' => [INT],
     *    'objContractEndDate' => I18n_Date
     *  )
     * </pre>
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses CProductComponent::getProductComponentInstance()
     * @uses CInternetConnectionProduct::getActiveStatusesForProductComponent()
     * @uses ProductComponent_Contract::getCurrent()
     * @uses ProductComponent_Tariff::getContractLengthInMonths()
     *
     * @return array
     */
    protected function fetchContractDetails()
    {
        $arrDetails = array();
        $intContractLengthInMonths = null;
        $objContractEndDate = null;

        $intProductComponentId = CProductComponent::getProductComponentInstance(
            $this->_intServiceId,
            'SUBSCRIPTION',
            CInternetConnectionProduct::getActiveStatusesForProductComponent('SUBSCRIPTION'),
            'INTERNET_CONNECTION'
        );

        // There is no component based product
        if (!empty($intProductComponentId)) {
            $objProductComponentContract = ProductComponent_Contract::getCurrent($intProductComponentId);

            if ($objProductComponentContract instanceof ProductComponent_Contract) {
                $objContractEndDate = I18n_Date::fromTimestamp($objProductComponentContract->getEndDate());
                $intContractLengthInMonths = $objProductComponentContract->getTariff()->getContractLengthInMonths();
            }

            $arrDetails['intContractLengthInMonths'] = $intContractLengthInMonths;
            $arrDetails['objContractEndDate']        = $objContractEndDate;
        }

        return $arrDetails;
    }

    /**
     * Fetches Annual contract details defined by SERVICE_EVENT_12MONTH_CONTRACT_START_ADSL service event
     *
     * Returns the following array:
     * <pre>
     *  array (
     *    'intContractLengthInMonths' => [INT],
     *    'objContractEndDate' => I18n_Date
     *  )
     * </pre>
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses AccountChange_Product_ServiceDefinition::includeLegacyFiles()
     * @uses AccountChange_Product_ServiceDefinition::userdata_service_events_find()
     * @uses I18n_Date
     *
     * @return array
     */
    protected function fetchAnnualContractDetails()
    {
        $this->includeLegacyFiles();

        $arrDetails = array();
        $intContractLengthInMonths = 12;
        $objContractEndDate = null;

        $arrEventCriteria = array(
            'service_id'    => $this->_intServiceId,
            'event_type_id' => array(SERVICE_EVENT_12MONTH_CONTRACT_START_ADSL)
        );

        $arrEvents = $this->userdata_service_events_find($arrEventCriteria);

        if (!empty($arrEvents)) {
            $objContractEndDate = I18n_Date::fromString($arrEvents[0]['event_date']);
            $objContractEndDate->modify("+1", I18n_Date::YEARS);

            $arrDetails['intContractLengthInMonths'] = $intContractLengthInMonths;
            $arrDetails['objContractEndDate']        = $objContractEndDate;
        }

        return $arrDetails;
    }

    /**
     * Fetches Cbc usage details for given customer with Value or Value Pro product
     *
     * Returns the following array:
     * <pre>
     *  array (
     *    'bolDisplayUsageInfo' => [BOOL],
     *    'intCappedBandwidthLimit' => [INT]
     *  )
     * </pre>
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses userdataServiceProductGet()
     * @uses AccountChange_Product_ServiceDefinition::GetCBCUserCappedBandwidthLimit()
     *
     * @return array
     */
    public function fetchCbcUsageDetails()
    {
        $bolDisplayUsageInfo = false;
        $intCappedBandwidthLimit = 0;
        $gigabyte = $this->fetchGigabyteConstant();

        $arrProductInfo = $this->fetchUserdataServiceProductGet($this->_intServiceId);
        if (!empty($arrProductInfo['strVariantHandle'])) {
            if ($this->isJohnLewisProductFamily($arrProductInfo)) {
                $bolDisplayUsageInfo = $this->isCbcProduct($arrProductInfo);
            } else {
                switch ($arrProductInfo['strVariantHandle']) {
                    // Deliberate fall-through...
                    case 'STANDARD':
                    case 'PRO':
                    case 'EXTRA':
                    case 'JLBASIC':
                        $bolDisplayUsageInfo = true;
                        break;

                    // Currently, no action taken for any other value of products.tblProductVariant.vchHandle
                    default:
                        break;
                }
            }
        }

        $intCappedBandwidthLimit = ($bolDisplayUsageInfo) ?
            $this->GetCBCUserCappedBandwidthLimit($this->_intServiceId) / $gigabyte : null;

        //get at least included bandwith
        if ($bolDisplayUsageInfo && empty($intCappedBandwidthLimit)) {
            $arrCBCFlex = $this->GetCBCFlexDetails($this->getCurrentFlexId());
            $intCappedBandwidthLimit = isset($arrCBCFlex['intIncludedBandwidthBytes']) ?
                $arrCBCFlex['intIncludedBandwidthBytes'] / $gigabyte : 0;
        }

        $arrDetails = array();
        $arrDetails['bolDisplayUsageInfo']      = $bolDisplayUsageInfo;
        $arrDetails['intCappedBandwidthLimit']  = $intCappedBandwidthLimit;

        return $arrDetails;
    }

    /**
     * See if the product family is John Lewis
     *
     * @param array $productInfo From userdataServiceProductGet method
     *
     * @return bool
     */
    protected function isJohnLewisProductFamily(array $productInfo)
    {
        $isJohnLewisProductFamily = false;

        if (!empty($productInfo['strFamilyHandle']) &&
            ProductFamily_JohnLewis2011::PRODUCT_FAMILY_HANDLE == $productInfo['strFamilyHandle']) {
            $isJohnLewisProductFamily = true;
        }

        return $isJohnLewisProductFamily;
    }


    /**
     * Check to see if the Product
     *
     * @param array $productInfo From userdataServiceProductGet method
     *
     * @return bool
     */
    protected function isCbcProduct(array $productInfo)
    {
        $isCBCProduct = false;

        if (!empty($productInfo['intSDI'])) {
            $isCBCProduct = $this->getProductFamily($productInfo['intSDI'])->isCbcProduct();
        }

        return $isCBCProduct;
    }

    /**
     * Wrapper for legacy functionality - needed for unit testing
     *
     * @param int $intServiceId The service id of the customer
     *
     * @return array
     */
    protected function fetchUserdataServiceProductGet($intServiceId)
    {
        return userdataServiceProductGet($this->_intServiceId);
    }

    /**
     * Wrapper for legacy functionality - needed for unit testing
     * as the GIGABYTE constant is pulled in from the legacy codebase
     * (LegacyCodebase/data/mis/database/database_libraries/cbc-access.inc)
     *
     * @return int
     */
    protected function fetchGigabyteConstant()
    {

        if (defined('GIGABYTE')) {
            return GIGABYTE;
        } else {
            // Same definition as per cbc-access.inc - and several dozen other places in the codebase...
            return 1000000000;
        }
    }

    /**
     * Wrapper for {@link GetCBCFlexDetails()}
     *
     * @param int $intFlexId the flex id
     *
     * @return int
     */
    protected function GetCBCFlexDetails($intFlexId)
    {
        return GetCBCFlexDetails($intFlexId);
    }

    /**
     * Wrapper for {@link GetCBCUserCappedBandwidthLimit()}
     *
     * @param int $intServiceId the service id
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return int
     */
    protected function GetCBCUserCappedBandwidthLimit($intServiceId)
    {
        return GetCBCUserCappedBandwidthLimit($intServiceId);
    }

    /**
     * Wrapper for {@link adslGetUserRealm()}
     *
     * @param int $intServiceId the service id
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return int
     */
    protected function adslGetUserRealm($intServiceId)
    {
        return adslGetUserRealm($intServiceId);
    }

    /**
     * Wrapper for userdata_service_events_find
     *
     * @param array $arrEventCriteria event criteria
     *
     * @return array
     */
    protected function userdata_service_events_find(array $arrEventCriteria)
    {
        return userdata_service_events_find($arrEventCriteria, 'event_date desc', 0, 1);
    }

    /**
     * Function to just send the email.
     *
     * This isn't pleasant, but because we have a fair few emails that need to get sent, it is cleaner to return
     * out of sendConfirmationEmail once it has done sending the emails it needs to send
     *
     * With this method we can send the email and then bob out
     *
     * @param int    $intServiceId     The service id
     * @param string $strTemplateFile  Email to send
     * @param array  $arrMailVariables Extra data needed for the email
     *
     * @return void
     */
    protected function sendEmail($intServiceId, $strTemplateFile, array $arrMailVariables = array())
    {
        Dbg_Dbg::write(
            'AccountChange_Product_ServiceDefinition::sendEmail with template ' . $strTemplateFile,
            'AccountChange'
        );

        EmailHandler::sendEmail(
            $this->_intServiceId,
            $strTemplateFile,
            $arrMailVariables
        );
    }

    /**
     * Wrapper for Product_SupplierProductRules
     *
     * @param object $objLineCheckResult LineCheck_Result
     *
     * @return Product_SupplierProductRules
     */
    protected function getSupplierProductRules($objLineCheckResult)
    {
        $supplierProductRules = new Product_SupplierProductRules(
            $objLineCheckResult,
            new Int($this->_objNewProductConfiguration->getProductId())
        );

        return $supplierProductRules;
    }

    /**
     * An account change which requires an engineer installation is liable to a
     * setup fee
     *
     * @return array|false
     */
    public function getSetupFee()
    {
        // If there was no appointment, the fee does not apply
        if (!isset($this->arrOptions['appointing']['appointingType'])) {
            return false;
        }

        $db = Db_Manager::getAdaptor('AccountChange');
        $initialCharge = $db->getSetupFee($this->_intServiceDefinitionId);

        if ($initialCharge > 0) {
            $broadbandSetupFee = array();
            $broadbandSetupFee['charge'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $initialCharge);
            $broadbandSetupFee['description'] = 'Broadband Activation';

            return $broadbandSetupFee;
        }

        return false;
    }

    /**
     * Include legacy files in a way that can be mocked
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/class-access.inc';
        require_once '/local/data/mis/database/database_libraries/config-access.inc';
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
        require_once '/local/data/mis/database/database_libraries/cbc-access.inc';
        require_once '/local/data/mis/database/database_libraries/payment-limit-access.inc';
    }

    /**
     * Process data usage limit after product change
     *
     * @return void
     */
    protected function processDataUsageLimit()
    {
        if (true === $this->isJohnLewisProduct()) {
            // Reset usage limit back to 0 extra usage for JLP products

            $this->includeLegacyFiles();

            ResetUsageLimitForJlpProduct(
                $this->_intServiceId
            );
        }
    }

    /**
     * Check whether the product user is changing to is a John Lewis type
     *
     * @return bool
     */
    protected function isJohnLewisProduct()
    {
        $registry = AccountChange_Registry::instance();
        $newProductId = $registry->getEntry("intNewServiceDefinitionId");

        $database = Db_Manager::getAdaptor("AccountChange");

        $productDetails = $database->getServiceDefinitionDetails(
            $newProductId
        );

        if ("johnlewis" == strtolower($productDetails["isp"])) {
            return true;
        }

        return false;
    }

    /**
     * Wrapper for Mocking
     *
     * @param int $oldSdi the old Service Definition ID
     * @param int $newSdi the new Service Definition ID
     *
     * @return bool
     */
    public function checkCustomerSwitchingFromGbWrToJlp($oldSdi, $newSdi)
    {
        return AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJlp($oldSdi, $newSdi);
    }

    /**
     * Estimates how much the customer will pay pro-rata for switching to Fibre.
     *
     * @param int    $intServiceId                Service Id of the service requiring the estimate
     * @param string $strAppointmentDate          Date of the appointment which Fibre will be installed
     * @param object $intOldTariff                Old tariff we are moving from
     * @param object $intNewTariff                New tariff we are moving to
     * @param int    $intOldDiscountAmountInPence Amount of old discount in pence
     * @param int    $intNewDiscountAmountInPence Amount of new discount in pence
     *
     * @return array containing estimate
     */
    public function getProRataEstimate(
        $intServiceId,
        $strAppointmentDate,
        $intOldTariff,
        $intNewTariff,
        $intOldDiscountAmountInPence = 0,
        $intNewDiscountAmountInPence = 0
    ) {
        $arrResult = array();

        if (empty($intServiceId) || empty($strAppointmentDate) || empty($intOldTariff) || empty($intNewTariff)) {
            return $arrResult;
        }

        // get the prorata days on the old and new products based on the appointment date
        $arrResult = $this->_getProRataBillingDays($intServiceId, $strAppointmentDate);

        if (!empty($arrResult)) {
            // ***** Old product - get the tariff details and calulate the day rate
            $arrOldProductTariff = $this->getTariffDetails($intOldTariff);
            $intOldProductDayRateInPence
                = $this->_calculateProductCostPerDay(
                    $arrOldProductTariff['intCostIncVatPence'],
                    $arrOldProductTariff['strPaymentFrequencyHandle'],
                    $intOldDiscountAmountInPence
                );

            $intOldProductCostRemainingInPence
                = $arrResult['intDaysRemaining'] * $intOldProductDayRateInPence;

            // ***** New product - get the tariff details and calulate the day rate
            $arrNewProductTariff = $this->getTariffDetails($intNewTariff);
            $intNewProductDayRateInPence
                = $this->_calculateProductCostPerDay(
                    $arrNewProductTariff['intCostIncVatPence'],
                    $arrNewProductTariff['strPaymentFrequencyHandle'],
                    $intNewDiscountAmountInPence
                );

            $intNewProductCostRemainingInPence
                = $arrResult['intDaysRemaining'] * $intNewProductDayRateInPence;

            // ***** Calculate the estimate
            // Estimate is:
            //    What the customer owes us on the new contract to the end of the month
            //    Minus
            //    What we owe the customer on the old contract to the end of the month
            $arrResult['intFttcEstimateInPence']
                = intval(round($intNewProductCostRemainingInPence - $intOldProductCostRemainingInPence, 0));
        }

        return $arrResult;
    }

    /**
    * Return cost pre day base on cost of period and payment frequency
    *
    * @param int    $intCostIncVatInPence      Cost including VAT in pence
    * @param string $strPaymentFrequencyHandle Payment frequency handle e.g. Annual, Quaterly or Monthly
    * @param int    $intDiscountAmountInPence  DiscountAmount In Pence
    *
    * @return float
    */
    private function _calculateProductCostPerDay(
        $intCostIncVatInPence,
        $strPaymentFrequencyHandle,
        $intDiscountAmountInPence = 0
    ) {
        $arrPaymentFrequencyInDays = array('ANNUAL'    => 365,
                                           'QUARTERLY' => 90,
                                           'MONTHLY'   => 30);

        $intDaysInPeriod = $arrPaymentFrequencyInDays[$strPaymentFrequencyHandle];

        $floCostPerDay = 0;

        if ($intDaysInPeriod > 0) {
            $floCostPerDay = ($intCostIncVatInPence - $intDiscountAmountInPence) / $intDaysInPeriod;
        }

        return $floCostPerDay;
    }

    /**
     * Calculates the prorata days for a service based on an appointment date.
     *
     * So given:
     *     Appointment Date:       22/07/2012
     *     Billing Start Date:     28/06/2012
     *     Billing End Date:       27/07/2012
     *
     * This will calculate as:
     *     Days on old product:    24 days (28/06 -> 21/07)
     *     Days on new product:  6 days (22/07 -> 27/07)
     *
     * @param int    $intServiceId       Service Id of the service requiring the estimate
     * @param string $strAppointmentDate Date of the appointment which Fibre will be installed
     *
     * @return array containing days information or empty
     */
    private function _getProRataBillingDays($intServiceId, $strAppointmentDate)
    {
        $arrResult = array();

        $objCoreService = $this->getCoreService();

        if (!empty($objCoreService)) {
            $objNextBillingDate = Core_BillingDate::getBillingDateFromDateAndPeriod(
                $objCoreService->getNextInvoiceDate(),
                $objCoreService->getInvoicePeriod(),
                $objCoreService->getInvoiceDay()
            );

            // Need to obtain the billing period relevant to the date of the appointment
            $dteAppointment = I18n_Date::fromString($strAppointmentDate);
            $objBillingPeriod = $objNextBillingDate->getBillingServicePeriodForDate($dteAppointment);

            // Get the relevant dates and clean them up
            $dteBillingStart = $objBillingPeriod->getStart();
            $dteBillingEnd = $objBillingPeriod->getEnd();

            $dteAppointment->resetDay();
            $dteBillingStart->resetDay();
            $dteBillingEnd->resetDay();

            $intDaysOnOldProduct = 0;
            $intDaysOnNewProduct = 0;

            // Calculate the days on old product
            if ($dteAppointment->getSanitisedBaseValue() !== $dteBillingStart->getSanitisedBaseValue()) {
                // get days different between the appointment and the billing start date
                $intDaysOnOldProduct
                    = floor(
                        ($dteAppointment->getSanitisedBaseValue() - $dteBillingStart->getSanitisedBaseValue()) / 86400
                    );
            }

            // Calculate the days on new product
            $intDaysOnNewProduct = 1; // assume we are account changing on the last day

            if ($dteAppointment->getSanitisedBaseValue() !== $dteBillingEnd->getSanitisedBaseValue()) {
                // get days different between the appointment and the billing end date and add 1 for today
                $intDaysOnNewProduct
                    = floor(
                        ($dteBillingEnd->getSanitisedBaseValue() - $dteAppointment->getSanitisedBaseValue()) / 86400
                    ) + 1;
            }

            $arrResult['intDaysPassed'] = intval($intDaysOnOldProduct);
            $arrResult['intDaysRemaining'] = intval($intDaysOnNewProduct);
            $arrResult['dteBillingDateBeforeAppointment']
                = date('Y-m-d', $dteBillingStart->getSanitisedBaseValue());
            $arrResult['dteBillingDateAfterAppointment']
                = date('Y-m-d', $dteBillingEnd->getSanitisedBaseValue());
        }

        return $arrResult;
    }

    /**
     * Obtains the tariff details
     *
     * @param int $intTariffId Id of tariff we are obtaining details
     *
     * @return array
     */
    protected function getTariffDetails($intTariffId)
    {
        return CProductComponent::getTariffDetails($intTariffId);
    }

    /**
     * Returns true if supplied service definition id is a fibre product
     *
     * @param int $sdi Service definition id
     *
     * @return bool
     */
    protected function isFibreProduct($sdi)
    {
        $fibreHelper = new AccountChange_FibreHelper();

        return $fibreHelper->isFibreProduct($sdi);
    }

    /**
     * Get INTERNET_CONNECTION component for the given service id
     *
     * @param integer $intServiceId Service id
     *
     * @return CInternetConnectionProduct
     */
    protected function getInternetConnectionProductFromServiceId($intServiceId)
    {
        return CInternetConnectionProduct::getInternetConnectionProductFromServiceId($intServiceId);
    }

    /**
     * Get the default service component id for the given service definition id
     *
     * @param integer $intServiceDefinitionId Service definition id
     *
     * @return CInternetConnectionProduct
     */
    protected function getDefaultServiceComponentIDForServiceDefinitionID($intServiceDefinitionId)
    {
        return CInternetConnectionProduct::getDefaultServiceComponentIDForServiceDefinitionID($intServiceDefinitionId);
    }

    /**
     * Get service definition details
     *
     * @param integer $intServiceDefinitionId Service definition id
     *
     * @return Core_ServiceDefinition
     */
    protected function getServiceDefinitionDetails($intServiceDefinitionId)
    {
        return new Core_ServiceDefinition($intServiceDefinitionId);
    }

    /**
     * Get product family for the given service definition id
     *
     * @param integer $intServiceDefinitionId Service definition id
     *
     * @return ProductFamily_Generic
     */
    protected function getProductFamily($intServiceDefinitionId)
    {
        return ProductFamily_Factory::getFamily($intServiceDefinitionId);
    }

    /**
     * Whether the service associated with a partner account
     *
     * @return boolean
     */
    protected function isPartnerAccount()
    {
        $helper = Reseller_CustomerHelper::getByServiceId(
            new Int($this->_intServiceId)
        );

        if ($helper instanceof Reseller_CustomerHelper
            && (true == $helper->isPartner()->getValue())) {
            return true;
        }

        return false;
    }

    /**
     * Whether the service associated with a partner enduser account
     *
     * @return boolean
     */
    protected function isPartnerEnduser()
    {
        $helper = Reseller_CustomerHelper::getByServiceId(
            new Int($this->_intServiceId)
        );

        if ($helper instanceof Reseller_CustomerHelper
            && (true == $helper->isEndUser()->getValue())) {
            return true;
        }

        return false;
    }

    /**
     * Is a BV account ?
     *
     * @return boolean
     */
    protected function isBvUser()
    {
        $this->includeLegacyFiles();

        return userdataIsBvUser($this->_intServiceId);
    }

    /**
     * Get a scheduling helper object
     *
     * @return AccountChange_SchedulingHelper
     **/
    protected function getSchedulingHelper()
    {
        return new AccountChange_SchedulingHelper();
    }

    /**
     * Get min and max speed ranges.
     *
     * @param LineCheck_Result | LineCheck_ResultDao $objLineCheckResult Line check result object
     *
     * @return array
     **/
    public function getMinAndMaxSpeedRanges($objLineCheckResult)
    {
        $speedHelper = new AccountChange_EmailHandler_SpeedHelper(
            $objLineCheckResult->getLineCheckId(),
            $this->getNewServiceDefinitionId()
        );

        return $speedHelper->getSpeedData();
    }

    /**
     * Helper function to check whether a code is a valid c2m promotion code
     *
     * @param string $promotionCode promotion code
     *
     * @return bool
     */
    private function isValidC2MPromotionCode($promotionCode)
    {
        $promotionToolsService = $this->getPromotionToolService();
        return $promotionToolsService->isActivePromotion($promotionCode);
    }

    /**
     * @return PromotionToolsService
     */
    protected function getPromotionToolService()
    {
        return new PromotionToolsService();
    }

    /**
     * Updates userdata.service_change_schedule with the logged in actor id so we
     * can record who / what scheduled the change.
     *
     * @return void
     */
    protected function updateScheduledChangeWithLoggedInActorId()
    {
        // Commit is necessary when working on something added in legacy codebase..
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $scheduleId = $dbAdaptor->getScheduleIdForBroadband($this->_intServiceId);
        $actor = $this->getLoggedInOrScriptActor();
        $dbAdaptor->updateServiceChangeScheduleWithActorId($actor->getActorId(), $scheduleId);
    }


    /**
     * Return the logged in or script actor object
     *
     * @return Auth_BusinessActor
     */
    protected function getLoggedInOrScriptActor()
    {
        return BusTier_BusTier::getLoggedInOrScriptActor();
    }


    /**
     * @param string $promotionCode        promotionCode
     * @param string $contractLengthMonths contractLengthMonths
     * @param int    $impressionOfferId    impression offer id (from Pega)
     * @return void
     */
    private function insertC2MPromotionCode($promotionCode, $contractLengthMonths, $impressionOfferId)
    {
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $intScheduleId = $dbAdaptor->getScheduleIdForBroadband($this->_intServiceId);
        $dbAdaptor->insertC2MPromotionCode($intScheduleId, $promotionCode, $contractLengthMonths, $impressionOfferId);
    }

   /**
    * Check for existence of DTW in RBM and reset if found
    *
    * @return void
    */
    protected function resetDtwRBM()
    {
        $objDTW = new Products_ServiceDataTransferWatch($this->_intServiceId);
        $cbcPaymentLimit = $objDTW->getDataTransferWatch();

        if (!empty($cbcPaymentLimit['dataTransferWatchLimit'])) {
            $objDTW->deleteDataTransferWatch();
        }
    }

    /**
     * Helper to check if broadband product is limited
     *
     * @param int $intServiceId Service ID
     *
     * @return bool
     */
    protected function isLimitedAccount($intServiceId)
    {
        return Db_Manager::getAdaptor('AccountChange')->isLimitedAccount($intServiceId);
    }
}
