<?php
/**
 * Register charges with RBM
 *
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Action_RegisterCharges extends AccountChange_Action
{
    /**
     * @var array
     */
    private $invoiceItems;

    /**
     * @var string|null
     */
    private $paymentReference;

    /**
     * @param int   $intServiceId Service id
     * @param array $arrOptions   Options
     */
    public function __construct($intServiceId, array $arrOptions = array())
    {
        parent::__construct($intServiceId, $arrOptions);

        $registry = AccountChange_Registry::instance();
        $this->invoiceItems = $registry->getEntry('invoiceItems');
        $this->paymentReference = $registry->getEntry('paymentHandoverId');
    }

    /**
     * Execute the action
     * @return void
     */
    public function execute()
    {
        if (!empty($this->invoiceItems)) {
            $oneTimeCharge = $this->getOneTimeChargeObject();
            $oneTimeCharge->sendChargeGroup();
        }
    }

    /**
     * @return Products_OneTimeCharge
     */
    protected function getOneTimeChargeObject()
    {
        return new Products_OneTimeCharge(
            $this->intServiceId,
            $this->invoiceItems,
            $this->paymentReference
        );
    }
}
