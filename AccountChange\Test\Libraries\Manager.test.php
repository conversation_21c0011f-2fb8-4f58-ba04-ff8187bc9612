<?php
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/database_libraries/financial-access.inc';
/**
 * Manager
 *
 * Testing class for the AccountChange_Manager
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Manager.test.php,v 1.3.4.1 2009/06/29 12:06:25 mstarbuck Exp $
 * @since      File available since 2008-08-19
 */
/**
 * Manager Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Manager_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture of the AccountChange_Manager
     *
     * @var AccountChange_Manager
     */
    protected $_objAccountChangeManager;

    /**
     * Fixture of the Old Configuration
     *
     * @var AccountChange_Product_Configuration
     */
    protected $_objOldConfiguration;

    /**
     * Fixture of the New Configuration
     *
     * @var AccountChange_Product_Configuration
     */
    protected $_objNewConfiguration;

    /**
     * Fixture of the action manager
     *
     * @var AccountChange_Action_Manager
     */
    protected $_objActionManager;

    /**
     * Service ID
     *
     * @var int
     */
    protected $_intServiceId;

    /**
     * Setup function for phpunit
     *
     */
    public function setUp()
    {
        $this->_intServiceId = 1234;

        // Mock Db_Adaptor
        $this->objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getComponentDetails', 'getBlockedAccountChangeEmailProductNames'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $this->objMockDbAdaptor->expects($this->any())
                               ->method('getComponentDetails')
                               ->will($this->returnValue(array('component_type_id' => 515)));

        $this->objMockDbAdaptor->expects($this->any())
                               ->method('getBlockedAccountChangeEmailProductNames')
                               ->will($this->returnValue(array('Plusnet Broadband Pay As You Go Option1', 'Plusnet Broadband Your Way Option1')));

        Db_Manager::setAdaptor('AccountChange', $this->objMockDbAdaptor);

        // Mock Db_Adaptor
        $this->objMockDbAdaptor = $this->getMock('Db_Adaptor',
                             array('getServiceDefinitionDao'),
                             array('Core', Db_Manager::DEFAULT_TRANSACTION, false));

        $a = array("strProductFamily"=>'VALUE', "strType"=>'residential');
        $this->objMockDbAdaptor->expects($this->any())
                        ->method('getServiceDefinitionDao')
                        ->will($this->returnValue($a));

        Db_Manager::setAdaptor('Core', $this->objMockDbAdaptor);

        $objOldServiceDefinition = new AccountChange_Product_ServiceDefinition(1, true);
        $objOldServiceComponent  = new AccountChange_Product_ServiceComponent(1, true);

        $this->_objOldConfiguration = new AccountChange_AccountConfiguration(
            array($objOldServiceDefinition),
            array($objOldServiceComponent)
        );

        $objNewServiceDefinition = new AccountChange_Product_ServiceDefinition(2, true);
        $objNewServiceComponent  = new AccountChange_Product_ServiceComponent(2, true);
        $this->_objNewConfiguration = new AccountChange_AccountConfiguration(
            array($objNewServiceDefinition),
            array($objNewServiceComponent)
        );

        $this->_objActionManager = $this->getMock('AccountChange_Action_Manager',
                                                 array('initialise', 'execute'),
                                                 array($this->_intServiceId));

        $this->_objActionManager->expects($this->any())
                               ->method('initialise');

        $this->_objActionManager->expects($this->any())
                               ->method('execute');

        $this->_objAccountChangeManager = new AccountChange_Manager(
            $this->_intServiceId,
            $this->_objOldConfiguration,
            $this->_objNewConfiguration,
            $this->_objActionManager
        );

        // Setup the constants (Bad isn't it!!! PHPUnit 4 Will Help I've been told)
        if (!defined('COMPONENT_EMAIL'))            define('COMPONENT_EMAIL',            '1');
        if (!defined('COMPONENT_PLUS_EMAIL'))       define('COMPONENT_PLUS_EMAIL',       '1');
        if (!defined('COMPONENT_F9_EMAIL'))         define('COMPONENT_F9_EMAIL',         '1');
        if (!defined('COMPONENT_FOL_EMAIL'))        define('COMPONENT_FOL_EMAIL',        '1');
        if (!defined('COMPONENT_COMMUNITY_SITE'))   define('COMPONENT_COMMUNITY_SITE',   '1');
        if (!defined('COMPONENT_GENERIC_FIREWALL')) define('COMPONENT_GENERIC_FIREWALL', '1');
    }

    /**
     * Tear down function for phpunit
     *
     */
    public function tearDown()
    {
        Lib_Userdata::resetInstance();
    }


    /**
     * @covers AccountChange_Manager::getAccountChangeType
     */
    public function testGetAccountChangeTypeReturnsUndecidedIfTwoValuesAreTheSame()
    {
        $bolResult = AccountChange_Manager::getAccountChangeType(1, 1);

        $this->assertEquals(AccountChange_Manager::ACCOUNT_TYPE_CHANGE_UNDECIDED, $bolResult);
    }

    /**
     * @covers AccountChange_Manager::getAccountChangeType
     */
    public function testGetAccountChangeTypeReturnsUpgradeIfSecondValueIsGreaterThanFirst()
    {
        $intAccountTypeChange = AccountChange_Manager::getAccountChangeType(1, 2);

        $this->assertEquals($intAccountTypeChange, AccountChange_Manager::ACCOUNT_TYPE_CHANGE_UPGRADE);
    }

    /**
     * @covers AccountChange_Manager::getAccountChangeType
     */
    public function testGetAccountChangeTypeReturnsDowngradeIfTheSecondValueIsLowerThanTheFirst()
    {
        $intAccountTypeChange = AccountChange_Manager::getAccountChangeType(3, 2);

        $this->assertEquals($intAccountTypeChange, AccountChange_Manager::ACCOUNT_TYPE_CHANGE_DOWNGRADE);
    }

     /**
     * @covers AccountChange_Manager::getTotalOneOffCharge
     *
     */
     public function testGetTotalOneOffChargeReturnsCorrectValue()
     {
         $objExpectedCharge = new I18n_Currency('gbp', 99);
         $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
         $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);

         $objManager = new AccountChange_Manager($this->_intServiceId,
                             $objOldConfiguration,
                             $objNewConfiguration);

         $objManager->setTotalOneOffCharge($objExpectedCharge);

         $this->assertEquals($objExpectedCharge, $objManager->getTotalOneOffCharge());
     }

     /**
     * @covers AccountChange_Manager::setTotalOneOffCharge
     *
     */
     public function testSetTotalOneOffChargeSetsTheCorrectMemberVariable()
     {
         $objExpectedCharge = new I18n_Currency('gbp', 99);
         $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
         $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);

         $objManager = new AccountChange_Manager(
            $this->_intServiceId,
            $objOldConfiguration,
            $objNewConfiguration
         );

         $objManager->setTotalOneOffCharge($objExpectedCharge);

         $this->assertAttributeEquals($objExpectedCharge, '_totalOneOffCharge', $objManager);
     }
    /**
     * @covers AccountChange_Manager::changeAccount
     *
     */
    public function testChangeAccountCallsExecuteAndReturnsBoolean()
    {
        $bolExpectedResult = true;

        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);

        $objManager = $this->getMock(
            'AccountChange_Manager',
            array('execute'),
            array($this->_intServiceId, $objOldConfiguration, $objNewConfiguration)
        );

        $objManager->expects($this->once())
                   ->method('execute')
                   ->will($this->returnValue($bolExpectedResult));

        $bolActualResult = $objManager->changeAccount();

        $this->assertEquals($bolExpectedResult, $bolActualResult);
    }

    /**
     * @covers AccountChange_Manager::selectAccountChangeType
     */
    public function testSelectAccountChangeTypeSelectsUpgradeByCheckingNumberOfProducts()
    {
        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objOldConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(1));
        $objOldConfiguration->expects($this->never())
                            ->method('getTotalCostOfKeyProducts');

        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(2));
        $objNewConfiguration->expects($this->never())
                            ->method('getTotalCostOfKeyProducts');

        $intServiceId = 1234;
        $objManager = new AccountChange_Manager(
            $intServiceId,
            $objOldConfiguration,
            $objNewConfiguration,
            $this->_objActionManager
        );
        $objManager->selectAccountChangeType();

        $this->assertAttributeEquals(AccountChange_Manager::ACCOUNT_TYPE_CHANGE_UPGRADE, '_intAccountChangeType', $objManager);
    }

    /**
     * @covers AccountChange_Manager::selectAccountChangeType
     */
    public function testSelectAccountChangeTypeSelectsUpgradeByCheckingCostsOfProducts()
    {
        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objOldConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(1));
        $objOldConfiguration->expects($this->once())
                            ->method('getTotalCostOfKeyProducts')
                            ->will($this->returnValue(2.99));

        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(1));
        $objNewConfiguration->expects($this->once())
                            ->method('getTotalCostOfKeyProducts')
                            ->will($this->returnValue(3.99));

        $intServiceId = 1234;
        $objManager = new AccountChange_Manager(
            $intServiceId,
            $objOldConfiguration,
            $objNewConfiguration,
            $this->_objActionManager
        );
        $objManager->selectAccountChangeType();

        $this->assertAttributeEquals(
            AccountChange_Manager::ACCOUNT_TYPE_CHANGE_UPGRADE,
            '_intAccountChangeType',
            $objManager
        );
    }

    /**
     * @covers AccountChange_Manager::selectAccountChangeType
     */
    public function testSelectAccountChangeTypeSelectsDowngradeByCheckingNumberOfProducts()
    {
        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objOldConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(2));
        $objOldConfiguration->expects($this->never())
                            ->method('getTotalCostOfKeyProducts');

        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(1));
        $objNewConfiguration->expects($this->never())
                            ->method('getTotalCostOfKeyProducts');

        $intServiceId = 1234;
        $objManager = new AccountChange_Manager($intServiceId, $objOldConfiguration, $objNewConfiguration, $this->_objActionManager);
        $objManager->selectAccountChangeType();

        $this->assertAttributeEquals(AccountChange_Manager::ACCOUNT_TYPE_CHANGE_DOWNGRADE, '_intAccountChangeType', $objManager);
    }

    /**
     * @covers AccountChange_Manager::selectAccountChangeType
     *
     * @return void
     */
    public function testSelectAccountChangeTypeSelectsDowngradeByCheckingCostsOfProducts()
    {
        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objOldConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(1));
        $objOldConfiguration->expects($this->once())
                            ->method('getTotalCostOfKeyProducts')
                            ->will($this->returnValue(4.99));

        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration->expects($this->once())
                            ->method('getTotalNumberOfKeyProducts')
                            ->will($this->returnValue(1));
        $objNewConfiguration->expects($this->once())
                            ->method('getTotalCostOfKeyProducts')
                            ->will($this->returnValue(4.98));

        $intServiceId = 1234;
        $objManager = new AccountChange_Manager(
            $intServiceId,
            $objOldConfiguration,
            $objNewConfiguration,
            $this->_objActionManager
        );
        $objManager->selectAccountChangeType();

        $this->assertAttributeEquals(
            AccountChange_Manager::ACCOUNT_TYPE_CHANGE_DOWNGRADE,
            '_intAccountChangeType',
            $objManager
        );
    }

    /**
     * Make sure that the constructor sets up the correct information
     *
     * @covers AccountChange_Manager::__construct
     *
     * @return void
     */
    public function testAccountChangeConstructorIsSetupCorrectly()
    {
        $this->assertAttributeEquals($this->_intServiceId, '_intServiceId', $this->_objAccountChangeManager);
        $this->assertAttributeEquals(
            $this->_objOldConfiguration,
            '_objOldAccountConfiguration',
            $this->_objAccountChangeManager
        );
        $this->assertAttributeEquals(
            $this->_objNewConfiguration,
            '_objNewAccountConfiguration',
            $this->_objAccountChangeManager
        );
        $this->assertAttributeEquals($this->_intServiceId, '_intServiceId', $this->_objOldConfiguration);
        $this->assertAttributeEquals($this->_intServiceId, '_intServiceId', $this->_objNewConfiguration);
    }

    /**
     * @covers AccountChange_Manager::__construct
     * @covers AccountChange_Action_ManagerException
     */
    public function testAccountChangeConstructorThrowsExceptionIfServiceIdIsNonNumeric()
    {
        $this->setExpectedException('AccountChange_ManagerException', 'Non numeric service id',
                                    AccountChange_ManagerException::ERR_INVALID_SERVICE_ID_TYPE);

        $objManager = new AccountChange_Manager('invalidServiceId', $this->_objOldConfiguration,
                                                $this->_objNewConfiguration, $this->_objActionManager);
    }

    /**
     * Need to make sure that the getter is returning the correct configuration object
     * And that is is a configuration object that is getting returned
     */
    public function testGetOldProductCongfigurationReturnsTheCorrectConfigurationAndType()
    {
        $this->assertEquals($this->_objOldConfiguration, $this->_objAccountChangeManager->getOldAccountConfiguration());
    }

    /**
     * Need to make sure that the getter is returning the correct configuration object
     * And that is is a configuration object that is getting returned
     */
    public function testGetNewProductConfigurationReturnsTheCorrectConfigurationAndType()
    {
        $this->assertEquals($this->_objNewConfiguration, $this->_objAccountChangeManager->getNewAccountConfiguration());
    }

    /**
     * @covers AccountChange_Manager::matchProductConfigurations
     *
     */
    public function testMatchProductConfigurationsReturnsAnEmptyArrayIfNoProductConfigurations()
    {
        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objOldConfiguration->expects($this->once())
                            ->method('getProductConfigurations')
                            ->will($this->returnValue(array()));

        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration->expects($this->once())
                            ->method('getProductConfigurations')
                            ->will($this->returnValue(array()));

        $intServiceId = 1234;
        $objManager = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Manager',
            array(
                $intServiceId,
                $objOldConfiguration,
                $objNewConfiguration,
                $this->_objActionManager
            )
        );

        $arrConfigurations = $objManager->protected_matchProductConfigurations(
            $objOldConfiguration,
            $objNewConfiguration
        );

        $this->assertEquals(array(), $arrConfigurations);
    }

    /**
     * @covers AccountChange_Manager::matchProductConfigurations
     *
     */
    public function testMatchProductConfigurationsReturnsAnArrayOfConfigurationsFromOldAccountConfigurationThatAreToBeRemovedOrChangedOrRefreshed()
    {
        $objProductConfiguration1 = $this->getMock('AccountChange_Product_ServiceDefinition', array(), array(1,1));
        $objProductConfiguration1->expects($this->once())
                                 ->method('setMatchingProductConfiguration')
                                 ->will($this->returnValue(true));
        $objProductConfiguration1->expects($this->exactly(2))
                                 ->method('getAction')
                                 ->will($this->returnValue(AccountChange_Product_Manager::ACTION_REMOVE));

        $objProductConfiguration2 = $this->getMock('AccountChange_Product_ServiceDefinition', array(), array(1,1));
        $objProductConfiguration2->expects($this->once())
                                 ->method('setMatchingProductConfiguration')
                                 ->will($this->returnValue(true));
        $objProductConfiguration2->expects($this->exactly(2))
                                 ->method('getAction')
                                 ->will($this->returnValue(AccountChange_Product_Manager::ACTION_CHANGE));

        $objProductConfiguration3 = $this->getMock('AccountChange_Product_ServiceDefinition', array(), array(1,1));
        $objProductConfiguration3->expects($this->once())
                                 ->method('setMatchingProductConfiguration')
                                 ->will($this->returnValue(true));
        $objProductConfiguration3->expects($this->exactly(2))
                                 ->method('getAction')
                                 ->will($this->returnValue(AccountChange_Product_Manager::ACTION_NONE));

        $objProductConfiguration4 = $this->getMock('AccountChange_Product_ServiceDefinition', array(), array(1,1));
        $objProductConfiguration4->expects($this->once())
                                 ->method('setMatchingProductConfiguration')
                                 ->will($this->returnValue(true));
        $objProductConfiguration4->expects($this->exactly(2))
                                 ->method('getAction')
                                 ->will($this->returnValue(AccountChange_Product_Manager::ACTION_REFRESH));

        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objOldConfiguration->expects($this->once())
                            ->method('getProductConfigurations')
                            ->will($this->returnValue(array($objProductConfiguration1,
                                                            $objProductConfiguration2,
                                                            $objProductConfiguration3,
                                                            $objProductConfiguration4)));

        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration->expects($this->once())
                            ->method('getProductConfigurations')
                            ->will($this->returnValue(array()));

        $intServiceId = 1234;
        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Manager', array($intServiceId,
                                                                                        $objOldConfiguration,
                                                                                        $objNewConfiguration,
                                                                                        $this->_objActionManager));

        $arrConfigurations = $objManager->protected_matchProductConfigurations($objOldConfiguration, $objNewConfiguration);

        $this->assertEquals(array($objProductConfiguration1, $objProductConfiguration2, $objProductConfiguration4), $arrConfigurations);
    }

    /**
     * @covers AccountChange_Manager::matchProductConfigurations
     *
     */
    public function testMatchProductConfigurationsReturnsAnArrayOfConfigurationsFromNewAccountConfigurationThatAreToBeAdded()
    {
        $objProductConfiguration1 = $this->getMock('AccountChange_Product_ServiceDefinition', array(), array(1,1));
        $objProductConfiguration1->expects($this->never())
                                 ->method('setMatchingProductConfiguration')
                                 ->will($this->returnValue(true));
        $objProductConfiguration1->expects($this->once())
                                 ->method('getAction')
                                 ->will($this->returnValue(AccountChange_Product_Manager::ACTION_ADD));

        $objProductConfiguration2 = $this->getMock('AccountChange_Product_ServiceDefinition', array(), array(1,1));
        $objProductConfiguration2->expects($this->never())
                                 ->method('setMatchingProductConfiguration')
                                 ->will($this->returnValue(true));
        $objProductConfiguration2->expects($this->once())
                                 ->method('getAction')
                                 ->will($this->returnValue(AccountChange_Product_Manager::ACTION_CHANGE));

        $objOldConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objOldConfiguration->expects($this->once())
                            ->method('getProductConfigurations')
                            ->will($this->returnValue(array()));

        $objNewConfiguration = $this->getMock('AccountChange_AccountConfiguration', array(), array(), '', false);
        $objNewConfiguration->expects($this->once())
                            ->method('getProductConfigurations')
                            ->will($this->returnValue(array($objProductConfiguration1, $objProductConfiguration2)));

        $intServiceId = 1234;
        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Manager', array($intServiceId,
                                                                                        $objOldConfiguration,
                                                                                        $objNewConfiguration,
                                                                                        $this->_objActionManager));

        $arrConfigurations = $objManager->protected_matchProductConfigurations($objOldConfiguration, $objNewConfiguration);

        $this->assertEquals(array($objProductConfiguration1), $arrConfigurations);
    }

    /**
     * @covers AccountChange_Manager::execute
     *
     */
    public function testExecuteIteratesThroughProductConfigurationsAndCallsExecute()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isScheduleChange', true);

        $objProductConfiguration1 = $this->getMock('AccountChange_Product_ServiceDefinition',
                                                   array('execute'), array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objProductConfiguration1->expects($this->once())
                                 ->method('execute');

        $objProductConfiguration2 = $this->getMock('AccountChange_Product_ServiceComponent',
                                                   array('execute'), array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objProductConfiguration2->expects($this->once())
                                 ->method('execute');

        $objAccountConfiguration = $this->getMock('AccountChange_AccountConfiguration',
                                                   array(), array(), '', false);

        $objManager = $this->getMock('AccountChange_Manager',
                                     array('matchProductConfigurations', 'output'),
                                     array(1,
                                           $objAccountConfiguration,
                                           $objAccountConfiguration));

        $objManager->expects($this->once())
                   ->method('matchProductConfigurations')
                   ->with($this->equalTo($objAccountConfiguration), $this->equalTo($objAccountConfiguration))
                   ->will($this->returnValue(array($objProductConfiguration1, $objProductConfiguration2)));

        $objManager->expects($this->any())
                   ->method('output');

        $this->assertTrue($objManager->changeAccount());
    }

    /**
     * @covers AccountChange_Manager::execute
     *
     * @return void
     */
    public function testExecuteIteratesThroughProductConfigurationsAndCallsGetTicket()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isScheduleChange', true);

        $objTicket = new AccountChange_Ticket('Comment');

        $objProductConfiguration1 = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getTickets'),
            array(1, AccountChange_Product_Manager::ACTION_NONE)
        );

        $objProductConfiguration1->expects($this->once())
                                 ->method('getTickets')
                                 ->will($this->returnValue(array($objTicket)));

        $objProductConfiguration2 = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getTickets'),
            array(1, AccountChange_Product_Manager::ACTION_NONE)
        );

        $objProductConfiguration2->expects($this->once())
                                 ->method('getTickets')
                                 ->will($this->returnValue(array($objTicket)));

        $objAccountConfiguration = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(), array(), '', false
        );

        $objManager = $this->getMock(
            'AccountChange_Manager',
            array('matchProductConfigurations', 'raiseTickets', 'raiseServiceNotices', 'output'),
            array(1, $objAccountConfiguration, $objAccountConfiguration)
        );

        $objManager->expects($this->once())
                   ->method('matchProductConfigurations')
                   ->with($this->equalTo($objAccountConfiguration), $this->equalTo($objAccountConfiguration))
                   ->will($this->returnValue(array($objProductConfiguration1, $objProductConfiguration2)));

        $objManager->expects($this->once())
                   ->method('raiseTickets')
                   ->with($this->equalTo(array($objTicket, $objTicket)));

        $objManager->expects($this->once())
                   ->method('raiseServiceNotices');

        $objManager->expects($this->any())
                   ->method('output');

        $this->assertTrue($objManager->changeAccount());
    }

    /**
     * @covers AccountChange_Manager::isTakingPayment
     *
     */
    public function testIsTakingPaymentReturnsTrueIfAnyOfTheOldProductsIsTakingPayment()
    {
        $objProductConfiguration1 = $this->getMock('AccountChange_Product_ServiceDefinition',
                                                   array('execute'),
                                                   array(1,
                                                         AccountChange_Product_Manager::ACTION_NONE,
                                                         array('bolTakePayment' => true)));

        $objProductConfiguration2 = $this->getMock('AccountChange_Product_Wlr',
                                                   array('execute'),
                                                   array(1,
                                                         AccountChange_Product_Manager::ACTION_NONE,
                                                         array('bolTakePayment' => true)));

        $objAccountConfiguration = $this->getMock('AccountChange_AccountConfiguration',
                                                   array('getProductConfigurations', 'validateAccountConfiguration'),
                                                   array(array($objProductConfiguration1, $objProductConfiguration2)));

        $objAccountConfiguration->expects($this->once())
                                ->method('getProductConfigurations')
                                ->will($this->returnValue(array($objProductConfiguration1, $objProductConfiguration2)));

        $objAccountConfiguration->expects($this->never())
                                ->method('validateAccountConfiguration');

        $objManager = $this->getMock('AccountChange_Manager',
                                     array('matchProductConfigurations'),
                                     array(1,
                                           $objAccountConfiguration,
                                           $objAccountConfiguration));

        $bolResult = $objManager->isTakingPayment();

        $this->assertTrue($bolResult);
    }

    /**
     * @covers AccountChange_Manager::isTakingPayment
     *
     */
    public function testIsTakingPaymentReturnsFalseIfNoneOfTheOldProductsIsTakingPayment()
    {
        $objProductConfiguration1 = $this->getMock('AccountChange_Product_ServiceDefinition',
                                                   array('execute'),
                                                   array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objProductConfiguration2 = $this->getMock('AccountChange_Product_Wlr',
                                                   array('execute'),
                                                   array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objAccountConfiguration = $this->getMock('AccountChange_AccountConfiguration',
                                                   array('getProductConfigurations', 'validateAccountConfiguration'),
                                                   array(array($objProductConfiguration1, $objProductConfiguration2)));

        $objAccountConfiguration->expects($this->once())
                                ->method('getProductConfigurations')
                                ->will($this->returnValue(array($objProductConfiguration1, $objProductConfiguration2)));

        $objAccountConfiguration->expects($this->never())
                                ->method('validateAccountConfiguration');

        $objManager = $this->getMock('AccountChange_Manager',
                                     array('matchProductConfigurations'),
                                     array(1,
                                           $objAccountConfiguration,
                                           $objAccountConfiguration));

        $bolResult = $objManager->isTakingPayment();

        $this->assertFalse($bolResult);
    }

    /**
     * @covers AccountChange_Manager::sendConfirmationEmails
     */
    public function testSendEmailConfirmationsIteratesThroughProductConfigurationsAndCallsSendEmailConfirmation()
    {
        $objProductConfiguration1 = $this->getMock('AccountChange_Product_ServiceDefinition',
                                                   array('sendConfirmationEmail'),
                                                   array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objProductConfiguration1->expects($this->once())
                                 ->method('sendConfirmationEmail');

        $objProductConfiguration2 = $this->getMock('AccountChange_Product_ServiceComponent',
                                                   array('sendConfirmationEmail'),
                                                   array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objProductConfiguration2->expects($this->once())
                                 ->method('sendConfirmationEmail');

        $objAccountConfiguration = $this->getMock('AccountChange_AccountConfiguration',
                                                   array(), array(), '', false);

        $objManager = $this->getMock('AccountChange_Manager',
                                     array('matchProductConfigurations'),
                                     array(1,
                                           $objAccountConfiguration,
                                           $objAccountConfiguration));

        $objManager->expects($this->once())
                   ->method('matchProductConfigurations')
                   ->with($this->equalTo($objAccountConfiguration), $this->equalTo($objAccountConfiguration))
                   ->will($this->returnValue(array($objProductConfiguration1, $objProductConfiguration2)));

        $objManager->sendConfirmationEmails();
    }

    /**
     * @covers AccountChange_Manager::getTotalUpgradeCost
     *
     */
    public function testGetTotalCostIncVatReturnsCorrectValueFromTheInvoiceItemsPassedToIt()
    {
        $floExpectedCost = 35.95;

        $arrInvoiceItems = array(
            array('amount' => 10.00),
            array('amount' => 5.00),
            array('amount' => 5.00),
            array('amount' => 10.50),
            array('amount' => 5.45),
        );

        $floActualCost = $this->_objAccountChangeManager->getTotalUpgradeCost($arrInvoiceItems);

        $this->assertEquals($floExpectedCost, $floActualCost);
    }

    /**
     * Test for AccountChange_Manager::getBlockedAccountChangeEmailProductNames
     *
     * @covers AccountChange_Manager::getBlockedAccountChangeEmailProductNames
     */
    public function testGetBlockedAccountChangeEmailProductNames()
    {
        $arrAccountChangeEmailExcludedProductNames = AccountChange_Manager::getBlockedAccountChangeEmailProductNames();
        $this->assertEquals($arrAccountChangeEmailExcludedProductNames[0], 'Plusnet Broadband Pay As You Go Option1');
        $this->assertEquals($arrAccountChangeEmailExcludedProductNames[1], 'Plusnet Broadband Your Way Option1');
    }
}
