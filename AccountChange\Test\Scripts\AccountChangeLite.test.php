<?php

/**
 * AccountChange_AccountChangeLite scrip tests
 *
 * Testing class for the AccountChange_AccountChangeLite
 *
 * @package    AccountChange
 * <AUTHOR> <d<PERSON><PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright  2022 PlusNet
 * @since      File available since 2022-04-29
 */

use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;

/**
 * Testing class for the Test_AccountChange_AccountChangeLite
 *
 * @package    AccountChange
 * <AUTHOR> <dval<PERSON><EMAIL>>
 *
 * @copyright  2022 PlusNet
 */


class Test_AccountChange_AccountChangeLite extends PHPUnit_Framework_TestCase
{
    const SCRIPT_NAME = 'AccountChange_AccountChangeLite';

    /** @var org\bovigo\vfs\vfsStreamDirectory $vfsroot */
    private static $vfsroot;

    /** @var ConvertCsvToMappingSql $object */
    private $object;

    /**
     * @return vfsStreamDirectory
     */
    public static function getRoot()
    {
        if (!self::$vfsroot) {
            self::$vfsroot = vfsStream::setup('tmp');
        }
        return self::$vfsroot;
    }


    public function setUp()
    {
        $objBusTierMock = $this->getMock(
            'BusTier_BusTier',
            array('getClient'),
            array(), '', false
        );

        $objBusTierMock->expects($this->never())
            ->method('getClient');

        $this->mockDb = Mockery::mock('Db_Adaptor');
        Db_Manager::setAdaptor('AccountChange', $this->mockDb);

        $this->mockIec = Mockery::mock('inventoryEventService');
        BusTier_BusTier::setClient('inventoryEventService', $this->mockIec);
        $this->mockIec
            ->shouldReceive('takePreChangeSnapshot')
            ->once();
        $this->mockIec
            ->shouldReceive('takePostChangeSnapshot')
            ->once();

        Mockery::mock('\Plusnet\InventoryEventClient\Context\AccountChangeContext');
       // $billingContext->shouldReceive('setIsInternetSupplierOrder')->with(false)->once();

        $mockCoreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockCoreDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will(
                $this->returnValue(
                    array(
                        'service_definition_id' => '********',
                        'provisioningProfile' => null
                    )
                )
            );

        Db_Manager::setAdaptor('AccountChange', $mockCoreDbAdaptor);




    }


    /**
     * Create the constructor
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testPerformBroadbandChange()
    {
        $csvFileName = 'csvFileName.csv';
        $csvFileData = 'serviceId,currentSdi,newSdi'
            . PHP_EOL
            . '1,2,3'
            . PHP_EOL;
        $csvFileHand = $this->getFakeFile($csvFileName, $csvFileData);

        $script = $this->getMockBuilder(AccountChange_AccountChangeLite)
            ->setMethods(array('__construct','login', 'getAccountChangeApi','importLegacy'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

      /*  $script = $this->getMock(
            'AccountChange_AccountChangeLite',
            array(
                'login',
                'getAccountChangeApi',
                'importLegacy'),
            array(),
             '',  FALSE
        ); */

        $script
            ->expects($this->once())
            ->method('login');


        $accountChangeApi = $this->getMock(
            '\AccountChange_AccountChangeApi',
            array(
                'processAccountChangeRequest')
        );

        $script
            ->expects($this->once())
            ->method('getAccountChangeApi')
            ->will($this->returnValue($accountChangeApi));

        $accountChangeApi
            ->expects($this->once())
            ->method('processAccountChangeRequest');


        $script->run( array($csvFileHand, 'BROADBANDCHANGE'),array());
    }



    private function getFakeFile($filename, $data)
    {
        $filehandle = vfsStream::url('tmp/' . $filename);
        vfsStream::newFile($filename)->at(self::getRoot())->setContent($data);
        return $filehandle;
    }






}
