<?php
/**
 * Created by IntelliJ IDEA.
 * User: kbuthpur
 * Date: 2020-02-06
 * Time: 10:53
 */


class AccountChange_PromotionCodeContractLengthPolicyTest extends \PHPUnit_Framework_TestCase
{

    const SERVICE_ID = 12345;
    const PROMO_CODE = 'testPromotion';
    private $c2mClient;
    
    protected function setup()
    {
        $this->c2mClient = $this->getMockBuilder('Plusnet\C2mApiClient\V5\Client')
            ->disableOriginalConstructor()
            ->setMethods(array('getSalesChannelData'))
            ->getMock();
        
        BusTier_BusTier::setClient('c2mapi.v5', $this->c2mClient);
    }
    
    protected function teardown()
    {
        $c2mClient = null;
    }

    public function testGetFailure()
    {
        $actor = Mockery::mock('Auth_BusinessActor');

        $campaignNotificationClient =
            $this->getMock('CampaignNotificationClient', array('getPersonalisedPromotion'));

        $this->c2mClient
            ->method('getSalesChannelData')
            ->with('PlusnetResidential-AccountChange-NoAffiliate-NoCampaign')
            ->willReturn(null);

        $campaignNotificationClient->expects($this->any())
            ->method('getPersonalisedPromotion')
            ->will($this->returnValue(null));

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);
        
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor);
        $this->assertEquals(AccountChange_PromotionCodeContractLengthPolicy::ERROR_MESSAGE, $validator->getFailure());
    }

    public function testValidateErrorCodeIsCorrectError()
    {
        $actor = Mockery::mock('Auth_BusinessActor');

        $campaignNotificationClient =
            $this->getMock('CampaignNotificationClient', array('getPersonalisedPromotion'));

        $campaignNotificationClient->expects($this->any())
            ->method('getPersonalisedPromotion')
            ->will($this->returnValue(null));

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);
        
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor);
        $this->assertEquals(AccountChange_PromotionCodeContractLengthPolicy::ERROR_CODE, $validator->getErrorCode());
    }

    /*
     * Validate should return true when additionaldata is missing
     *
     * */
    public function testValidateReturnsTrueWhenAdditionalDataIsMissing()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        
        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor);

        $this->assertTrue($validator->validate());
    }

    /*
     * Validate should return true when Promotion is
     * */
    public function testValidateReturnsTrueWhenPromotionIsNotPersonalised()
    {

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(false);
        $additionalInfo = array('C2MPromotion' => $c2mPromotion,
            'C2MPromotionCode' => self::PROMO_CODE, 'ServiceId' => self::SERVICE_ID);
        
        $campaignNotificationClient = $this->getMockBuilder('campaignNotificationClient')
            ->disableOriginalConstructor();

        $salesChannelData = Mockery::mock('SalesChannelData');
        $salesChannelData->makePartial();
        $salesChannelData->shouldReceive('isCampaign')->andReturn(true);

        $this->c2mClient
            ->method('getSalesChannelData')
            ->willReturn($salesChannelData);
        
        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);
        
        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor, false, false, $additionalInfo);

        $this->assertTrue($validator->validate());
    }

    /*
     * Validate should return true when customer channel(Campain) doesn't match with promotion.
     *
     * */
    public function testValidateReturnsTrueWhenCampainIsFalse()
    {

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(true);
        $additionalInfo = array('C2MPromotion' => $c2mPromotion,
            'C2MPromotionCode' => self::PROMO_CODE, 'ServiceId' => self::SERVICE_ID);
        
        $salesChannelData = Mockery::mock('SalesChannelData');
        $salesChannelData->makePartial();
        $salesChannelData->shouldReceive('isCampaign')->andReturn(false);

        $this->c2mClient
            ->method('getSalesChannelData')
            ->willReturn($salesChannelData);
        
        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor, false, false, $additionalInfo);

        $this->assertTrue($validator->validate());
    }

    /*
     * Validate should return true when customer channel(Campain) doesn't match with promotion.
     *
     * */
    public function testValidateReturnsTrueWhenThereIsNoImpressionOfferIdSet()
    {

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(true);
        $c2mPromotion->shouldReceive('getCode')->andReturn(self::PROMO_CODE);
        $additionalInfo = array('C2MPromotion' => $c2mPromotion,
            'C2MPromotionCode' => self::PROMO_CODE,
            'ServiceId' => self::SERVICE_ID);
        
        $salesChannelData = Mockery::mock('SalesChannelData');
        $salesChannelData->makePartial();
        $salesChannelData->shouldReceive('isCampaign')->andReturn(true);

        $this->c2mClient
            ->method('getSalesChannelData')
            ->willReturn($salesChannelData);
        
        $campaignNotificationClient = $this->getMock('CampaignNotificationClient', array('getPersonalisedPromotion'));


        $promotionData = Mockery::mock('Plusnet\CampaignNotificationClient\Entity\PromotionData');
        $promotionData->makePartial();
        $promotionData->shouldReceive('getStartDate')->andReturn('2019-01-01');
        $promotionData->shouldReceive('getEndDate')->andReturn('2030-01-01');
        $campaignNotificationClient
            ->method('getPersonalisedPromotion')
            ->will($this->returnValue($promotionData));

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor, false, false, $additionalInfo);

        $this->assertTrue($validator->validate());
    }

    /*
     * Validate should return true when customer channel(Campain) doesn't match with promotion.
     *
    **/
    public function testValidateReturnsTrueWhenThereIsAnImpressionOfferAndTheContractLengthsMatch()
    {

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(true);
        $c2mPromotion->shouldReceive('getCode')->andReturn(self::PROMO_CODE);
        $additionalInfo = array('C2MPromotion' => $c2mPromotion,
            'C2MPromotionCode' => self::PROMO_CODE,
            'ServiceId' => self::SERVICE_ID,
            'impressionOfferId' => 1234,
            'contractDuration' => 12
        );
        $impressionOfferDetails = array(
          'OfferedContractDuration' => 12
        );

        $salesChannelData = Mockery::mock('SalesChannelData');
        $salesChannelData->makePartial();
        $salesChannelData->shouldReceive('isCampaign')->andReturn(true);

        $this->c2mClient
            ->method('getSalesChannelData')
            ->willReturn($salesChannelData);

        $campaignNotificationClient = $this->getMock('CampaignNotificationClient', array('getPersonalisedPromotion'));


        $promotionData = Mockery::mock('Plusnet\CampaignNotificationClient\Entity\PromotionData');
        $promotionData->makePartial();
        $promotionData->shouldReceive('getStartDate')->andReturn('2019-01-01');
        $promotionData->shouldReceive('getEndDate')->andReturn('2030-01-01');
        $campaignNotificationClient
            ->method('getPersonalisedPromotion')
            ->will($this->returnValue($promotionData));

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);


        $validator = Mockery::mock('AccountChange_PromotionCodeContractLengthPolicy');
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getImpressionOfferDetails')->with($additionalInfo['impressionOfferId'])->andReturn($impressionOfferDetails);

        $this->assertTrue($validator->validate());
    }

    /*
     * Validate should return true when customer channel(Campain) doesn't match with promotion.
     *
    **/
    public function testValidateReturnsFalseWhenThereIsAnImpressionOfferAndContractLengthsDoNotMatch()
    {

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(true);
        $c2mPromotion->shouldReceive('getCode')->andReturn(self::PROMO_CODE);
        $additionalInfo = array('C2MPromotion' => $c2mPromotion,
            'C2MPromotionCode' => self::PROMO_CODE,
            'ServiceId' => self::SERVICE_ID,
            'impressionOfferId' => 1234,
            'contractDuration' => 18
        );
        $impressionOfferDetails = array(
            'OfferedContractDuration' => 12
        );

        $salesChannelData = Mockery::mock('SalesChannelData');
        $salesChannelData->makePartial();
        $salesChannelData->shouldReceive('isCampaign')->andReturn(true);

        $this->c2mClient
            ->method('getSalesChannelData')
            ->willReturn($salesChannelData);

        $campaignNotificationClient = $this->getMock('CampaignNotificationClient', array('getPersonalisedPromotion'));


        $promotionData = Mockery::mock('Plusnet\CampaignNotificationClient\Entity\PromotionData');
        $promotionData->makePartial();
        $promotionData->shouldReceive('getStartDate')->andReturn('2019-01-01');
        $promotionData->shouldReceive('getEndDate')->andReturn('2030-01-01');
        $campaignNotificationClient
            ->method('getPersonalisedPromotion')
            ->will($this->returnValue($promotionData));

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);


        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor, false, false, $additionalInfo);
        $validator = Mockery::mock('AccountChange_PromotionCodeContractLengthPolicy');
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getImpressionOfferDetails')->with($additionalInfo['impressionOfferId'])->andReturn($impressionOfferDetails);
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);

        $this->assertFalse($validator->validate());
    }

    public function provideDataForTestConvertContractLengthToMonths()
    {
        return array(
            array('12 MONTHS', 12),
            array('1 YEAR 2 MONTHS', 14),
            array('2 YEARS', 24),
            array(12, 12),
            array(1,1),
            array('1 YEAR 1 MONTH', 13),
            array('nonsense', 0)
        );
    }

    /**
     * Tests that convertContractLengthToMonths works for expected inputs
     *
     * @dataProvider provideDataForTestConvertContractLengthToMonths
     */
    public function testConvertContractLengthToMonths($input, $expectedMonths)
    {
        $additionalInfo = array();
        $actor = Mockery::mock('Auth_BusinessActor');
        $validator = new AccountChange_PromotionCodeContractLengthPolicy($actor, false, false, $additionalInfo);

        $this->assertEquals($validator->convertContractLengthToMonths($input), $expectedMonths);
    }

    /**
     * Return a mock c2m promotion
     *
     * @return Promotion
     **/
    protected function getC2mPromotionMock()
    {
        $c2mPromotion = Mockery::mock('Plusnet\C2mApiClient\Entity\Promotion');
        $c2mPromotion->makePartial();
        
        return $c2mPromotion;
    }
}
