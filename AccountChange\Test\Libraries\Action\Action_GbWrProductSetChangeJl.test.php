<?php
/**
 * Action Greenbee/Waitrose Product Set Change to <PERSON>
 *
 * Testing class for the AccountChange_Action_Appointment class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 * @since      File available since 2011-09-07
 */
/**
 * Action Greenbee/Waitrose Product Set Change to <PERSON> Test Class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
class AccountChange_Action_GbWrProductSetChangeJl_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Reset the account change registry when we're done
     *
     * @return void
     */
    public function tearDown()
    {
        AccountChange_Registry::instance()->reset();
        Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Test that execute sends the CBC email when the conditions are met.
     *
     * @param array  $oldServiceDefinition   Old service definition
     * @param array  $newServiceDefinition   New service definition
     * @param int    $oldServiceDefinitionId Old service definition Id
     * @param int    $newServiceDefinitionId New service definition Id
     * @param array  $service                Service
     * @param string $billingDate            Billing date
     *
     * @covers AccountChange_Action_GbWrProductSetChangeJl::__construct
     * @covers AccountChange_Action_GbWrProductSetChangeJl::execute
     * @covers AccountChange_Action_GbWrProductSetChangeJl::gatherRequiredData
     * @covers AccountChange_Action_GbWrProductSetChangeJl::isCustomerSwitchingFromGbWrToJLP
     * @covers AccountChange_Action_GbWrProductSetChangeJl::beforeBillingDateForCurrentMonth
     * @covers AccountChange_Action::sendEmail
     * @covers AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJLP
     *
     * @dataProvider dataProviderForTestExecuteSendsCbcEmail
     *
     * @return void
     */
    public function testExecuteSendsCbcEmail(
        $oldServiceDefinition,
        $newServiceDefinition,
        $oldServiceDefinitionId,
        $newServiceDefinitionId,
        $service,
        $billingDate
    ) {
        $currentDay = 10;
        $currentMonth = 7;

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao', 'getServiceDao', 'getServiceComponentDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $adaptor->expects($this->at(0))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($oldServiceDefinition));
        $adaptor->expects($this->at(1))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($newServiceDefinition));
        Db_Manager::setAdaptor('Core', $adaptor);

        $objDate = I18n_Date::fromString($billingDate);
        $coreBillingDate = new Core_BillingDate($objDate, New UnsignedInt(1), New UnsignedInt($objDate->getDay()));

        $coreService = $this->getMock(
            'Core_Service',
            array('getAdslDetails', 'getBillingDate'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );
        $coreService->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue($coreBillingDate));

        $ispAutomatedEmail = $this->getMock(
            'IspAutomatedEmail_IspAutomatedEmail',
            array('prepareIspAutomatedEmail', 'send'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );
        $ispAutomatedEmail->expects($this->at(0))
            ->method('prepareIspAutomatedEmail')
            ->with('cbc_before_billing_date');
        $ispAutomatedEmail->expects($this->at(0))
            ->method('send');

        AccountChange_Registry::instance()->setEntry('intOldServiceDefinitionId', $oldServiceDefinitionId);
        AccountChange_Registry::instance()->setEntry('intNewServiceDefinitionId', $newServiceDefinitionId);

        $action = $this->getMock(
            'AccountChange_Action_GbWrProductSetChangeJl',
            array(
                'getCoreService',
                'getCurrentDay',
                'getCurrentMonth',
                'getIspAutomatedEmail'
            ), // Mocked functions
            array($service['service_id'], array()) // Constructor parameters
        );
        $action->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));
        $action->expects($this->once())
            ->method('getCurrentDay')
            ->will($this->returnValue($currentDay));
        $action->expects($this->once())
            ->method('getCurrentMonth')
            ->will($this->returnValue($currentMonth));
        $action->expects($this->exactly(1))
            ->method('getIspAutomatedEmail')
            ->with($service['service_id'])
            ->will($this->returnValue($ispAutomatedEmail));

        $action->execute();
    }

    /**
     * Data provider for testExecuteSendsCbcEmail
     *
     * @return array
     */
    public function dataProviderForTestExecuteSendsCbcEmail()
    {
        return array(
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                '2012-07-15', // Billing Date
            ),
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                '2012-07-15', // Billing Date
            ),
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                '2012-07-11', // Billing Date
            ),
        );
    }

    /**
     * Test that execute sends the scheduled and instant types of product change email when the conditions are met.
     *
     * @param array  $oldServiceDefinition        Old service definition
     * @param array  $newServiceDefinition        New service definition
     * @param int    $oldServiceDefinitionId      Old service definition Id
     * @param int    $newServiceDefinitionId      New service definition Id
     * @param array  $service                     Service
     * @param bool   $isAccountChangeScheduled    Is cccount change sheduled
     * @param string $expectedEmailTemplateHandle Expected email template handle
     *
     * @covers AccountChange_Action_GbWrProductSetChangeJl::__construct
     * @covers AccountChange_Action_GbWrProductSetChangeJl::execute
     * @covers AccountChange_Action_GbWrProductSetChangeJl::gatherRequiredData
     * @covers AccountChange_Action_GbWrProductSetChangeJl::isCustomerSwitchingFromGbWrToJLP
     * @covers AccountChange_Action::sendEmail
     * @covers AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJLP
     *
     * @dataProvider dataProviderForTestExecuteSendsProductChangeEmail
     *
     * @return void
     */
    public function testExecuteSendsProductChangeEmail(
        $oldServiceDefinition,
        $newServiceDefinition,
        $oldServiceDefinitionId,
        $newServiceDefinitionId,
        $service,
        $isAccountChangeScheduled,
        $expectedEmailTemplateHandle
    ) {
        $currentDay = 10;
        $currentMonth = 7;
        $billingDate = '2011-07-05';

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao', 'getServiceDao', 'getServiceComponentDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $adaptor->expects($this->at(0))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($oldServiceDefinition));
        $adaptor->expects($this->at(1))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($newServiceDefinition));
        Db_Manager::setAdaptor('Core', $adaptor);

        $objDate = I18n_Date::fromString($billingDate);
        $coreBillingDate = new Core_BillingDate($objDate, New UnsignedInt(1), New UnsignedInt($objDate->getDay()));

        $coreService = $this->getMock(
            'Core_Service',
            array('getAdslDetails', 'getBillingDate'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );
        $coreService->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue($coreBillingDate));

        $ispAutomatedEmail = $this->getMock(
            'IspAutomatedEmail_IspAutomatedEmail',
            array('prepareIspAutomatedEmail', 'send'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );
        $ispAutomatedEmail->expects($this->once())
            ->method('prepareIspAutomatedEmail')
            ->with($expectedEmailTemplateHandle);
        $ispAutomatedEmail->expects($this->once())
            ->method('send');

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getHighestAvailableSpeed')
        );

        $lineCheckResult->expects($this->once())
            ->method('getHighestAvailableSpeed')
            ->will($this->returnValue(16000));

        AccountChange_Registry::instance()->setEntry('intOldServiceDefinitionId', $oldServiceDefinitionId);
        AccountChange_Registry::instance()->setEntry('intNewServiceDefinitionId', $newServiceDefinitionId);
        AccountChange_Registry::instance()->setEntry('bolSchedule', $isAccountChangeScheduled);
        AccountChange_Registry::instance()->setEntry('objLineCheckResult', $lineCheckResult);

        $productRules = $this->getMock(
            'AccountChange_ProductRules',
            array(
                'getProductProvisionForService'
            ), // Mocked functions
            array() // Constructor parameters
        );

        $productProvDetails = array(
            'intSupplierPlatformID' => 1111,
            'intSupplierProductId' => 2,
            'strProvisionOn' => 'adsl2'
        );

        $productRules->expects($this->once())
            ->method('getProductProvisionForService')
            ->will($this->returnValue($productProvDetails));

        $action = $this->getMock(
            'AccountChange_Action_GbWrProductSetChangeJl',
            array(
                'getCoreService',
                'getCurrentDay',
                'getCurrentMonth',
                'getIspAutomatedEmail',
                'getProductRules'
            ), // Mocked functions
            array($service['service_id'], array()) // Constructor parameters
        );
        $action->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));
        $action->expects($this->once())
            ->method('getCurrentDay')
            ->will($this->returnValue($currentDay));
        $action->expects($this->once())
            ->method('getCurrentMonth')
            ->will($this->returnValue($currentMonth));
        $action->expects($this->once())
            ->method('getIspAutomatedEmail')
            ->with($service['service_id'])
            ->will($this->returnValue($ispAutomatedEmail));
        $action->expects($this->once())
            ->method('getProductRules')
            ->will($this->returnValue($productRules));


        $action->execute();
    }

    /**
     * Data provider for testExecuteSendsProductChangeEmail
     *
     * @return array
     */
    public function dataProviderForTestExecuteSendsProductChangeEmail()
    {
        return array(
            // Greenbee to John Lewis scheduled account change
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                true,
                'gb_wr_to_jlp_scheduled_account_change'
            ),

            // Waitrose to John Lewis scheduled account change
            array(
                array(
                    'isp' => 'waitrose'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                true,
                'gb_wr_to_jlp_scheduled_account_change'
            ),
        );
    }

    /**
     * Test that execute does not send a CBC email when the conditions are not met.
     *
     * @param array  $oldServiceDefinition             Old service definition
     * @param array  $newServiceDefinition             New service definition
     * @param int    $oldServiceDefinitionId           Old service definition Id
     * @param int    $newServiceDefinitionId           New service definition Id
     * @param array  $service                          Service
     * @param string $billingDate                      Billing date
     * @param int    $callCountForBillingDateFunctions billingDateFunction call
     *                                                 count
     *
     * @covers AccountChange_Action_GbWrProductSetChangeJl::__construct
     * @covers AccountChange_Action_GbWrProductSetChangeJl::execute
     * @covers AccountChange_Action_GbWrProductSetChangeJl::gatherRequiredData
     * @covers AccountChange_Action_GbWrProductSetChangeJl::isCustomerSwitchingFromGbWrToJLP
     * @covers AccountChange_Action_GbWrProductSetChangeJl::beforeBillingDateForCurrentMonth
     * @covers AccountChange_Action::sendEmail
     * @covers AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJLP
     *
     * @dataProvider dataProviderForTestExecuteDoesNotSendCbcEmail
     *
     * @return void
     */
    public function testExecuteDoesNotSendCbcEmail(
        $oldServiceDefinition,
        $newServiceDefinition,
        $oldServiceDefinitionId,
        $newServiceDefinitionId,
        $service,
        $billingDate,
        $callCountForBillingDateFunctions
    ) {
        $currentDay = 10;
        $currentMonth = 7;

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao', 'getServiceDao', 'getServiceComponentDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $adaptor->expects($this->at(0))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($oldServiceDefinition));
        $adaptor->expects($this->at(1))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($newServiceDefinition));
        Db_Manager::setAdaptor('Core', $adaptor);

        $objDate = I18n_Date::fromString($billingDate);
        $coreBillingDate = new Core_BillingDate($objDate, New UnsignedInt(1), New UnsignedInt($objDate->getDay()));

        $coreService = $this->getMock(
            'Core_Service',
            array('getAdslDetails', 'getBillingDate'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );
        $coreService->expects($this->exactly($callCountForBillingDateFunctions))
            ->method('getBillingDate')
            ->will($this->returnValue($coreBillingDate));

        AccountChange_Registry::instance()->setEntry('intOldServiceDefinitionId', $oldServiceDefinitionId);
        AccountChange_Registry::instance()->setEntry('intNewServiceDefinitionId', $newServiceDefinitionId);
        AccountChange_Registry::instance()->setEntry('bolSchedule', true);

        $action = $this->getMock(
            'AccountChange_Action_GbWrProductSetChangeJl',
            array(
                'getCoreService',
                'getCurrentDay',
                'getCurrentMonth',
                'sendEmail'
            ), // Mocked functions
            array($service['service_id'], array()) // Constructor parameters
        );
        $action->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));
        $action->expects($this->exactly($callCountForBillingDateFunctions))
            ->method('getCurrentDay')
            ->will($this->returnValue($currentDay));
        $action->expects($this->exactly($callCountForBillingDateFunctions))
            ->method('getCurrentMonth')
            ->will($this->returnValue($currentMonth));
        $action->expects($this->once())
            ->method('sendEmail')
            ->with('gb_wr_to_jlp_scheduled_account_change');

        $action->execute();
    }

    /**
     * Data provider for testExecuteDoesNotSendCbcEmail
     *
     * @return array
     */
    public function dataProviderForTestExecuteDoesNotSendCbcEmail()
    {
        return array(
            // Billing day is before today
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                '2012-07-05', // Billing Date
                1 // Call Count For Billing Date Functions
            ),
            // Billing day is today
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                '2012-07-10', // Billing Date
                1 // Call Count For Billing Date Functions
            ),
            // Billing day is after today but in next month
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                '2012-08-15', // Billing Date
                1 // Call Count For Billing Date Functions
            ),
        );
    }

    /**
     * Test that execute does not attempt to send an email.
     *
     * @param array $oldServiceDefinition   Old service definition
     * @param array $newServiceDefinition   New service definition
     * @param int   $oldServiceDefinitionId Old service definitio Id
     * @param int   $newServiceDefinitionId New service deinition Id
     * @param array $service                Service
     *
     * @covers AccountChange_Action_GbWrProductSetChangeJl::__construct
     * @covers AccountChange_Action_GbWrProductSetChangeJl::execute
     * @covers AccountChange_Action_GbWrProductSetChangeJl::gatherRequiredData
     * @covers AccountChange_Action_GbWrProductSetChangeJl::isCustomerSwitchingFromGbWrToJLP
     * @covers AccountChange_Action_GbWrProductSetChangeJl::beforeBillingDateForCurrentMonth
     * @covers AccountChange_Action::sendEmail
     * @covers AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJLP
     *
     * @dataProvider dataProviderForTestExecuteDoesNotSendEmail
     *
     * @return void
     */
    public function testExecuteDoesNotSendEmail(
        $oldServiceDefinition,
        $newServiceDefinition,
        $oldServiceDefinitionId,
        $newServiceDefinitionId,
        $service
    ) {
        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $adaptor->expects($this->at(0))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($oldServiceDefinition));
        $adaptor->expects($this->at(1))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($newServiceDefinition));
        Db_Manager::setAdaptor('Core', $adaptor);

        $coreService = $this->getMock(
            'Core_Service',
            array('getAdslDetails'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );

        AccountChange_Registry::instance()->setEntry('intOldServiceDefinitionId', $oldServiceDefinitionId);
        AccountChange_Registry::instance()->setEntry('intNewServiceDefinitionId', $newServiceDefinitionId);

        $action = $this->getMock(
            'AccountChange_Action_GbWrProductSetChangeJl',
            array(
                'getCoreService',
                'sendEmail'
            ), // Mocked functions
            array($service['service_id'], array()) // Constructor parameters
        );
        $action->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));
        $action->expects($this->never())
            ->method('sendEmail');

        $action->execute();
    }

    /**
     * Data provider for testExecuteDoesNotSendEmail
     *
     * @return array
     */
    public function dataProviderForTestExecuteDoesNotSendEmail()
    {
        return array(
            // Customer not switching from greenbee or waitrose
            array(
                array(
                    'isp' => 'plusnet'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
            ),
            // Greenbee customer not switching to john lewis
            array(
                array(
                    'isp' => 'plusnet'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
            ),
            // Waitrose customer not switching to john lewis
            array(
                array(
                    'isp' => 'plusnet'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
            ),
        );
    }


    /**
    * Test that the exception thrown whilst sending an email is handled correctly.
     *
     * @param array     $oldServiceDefinition   Old service definition
     * @param array     $newServiceDefinition   New service definition
     * @param integer   $oldServiceDefinitionId Old service definition id
     * @param integer   $newServiceDefinitionId New service definition Id
     * @param array     $service                Service
     * @param string    $billingDate            Billing data
     * @param Exception $expectedException      Expected exception
     *
     * @covers AccountChange_Action_GbWrProductSetChangeJl::__construct
     * @covers AccountChange_Action_GbWrProductSetChangeJl::execute
     * @covers AccountChange_Action_GbWrProductSetChangeJl::gatherRequiredData
     * @covers AccountChange_Action_GbWrProductSetChangeJl::isCustomerSwitchingFromGbWrToJLP
     * @covers AccountChange_Action_GbWrProductSetChangeJl::beforeBillingDateForCurrentMonth
     * @covers AccountChange_Action::sendEmail
     * @covers AccountChange_Action::raiseAutoProblem
     *
     * @dataProvider dataProviderForTestExceptionHandledDuringSendEmail
     *
     * @return void
     */
    public function testExceptionHandledDuringSendEmail(
        $oldServiceDefinition,
        $newServiceDefinition,
        $oldServiceDefinitionId,
        $newServiceDefinitionId,
        $service,
        $billingDate,
        $expectedException
    ) {
        $currentDay = 10;
        $currentMonth = 7;

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao', 'getServiceDao', 'getServiceComponentDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $adaptor->expects($this->at(0))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($oldServiceDefinition));
        $adaptor->expects($this->at(1))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($newServiceDefinition));
        Db_Manager::setAdaptor('Core', $adaptor);

        $objDate = I18n_Date::fromString($billingDate);
        $coreBillingDate = new Core_BillingDate($objDate, New UnsignedInt(1), New UnsignedInt($objDate->getDay()));

        $coreService = $this->getMock(
            'Core_Service',
            array('getAdslDetails', 'getBillingDate'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );
        $coreService->expects($this->once())
            ->method('getBillingDate')
            ->will($this->returnValue($coreBillingDate));

        $ispAutomatedEmail = $this->getMock(
            'IspAutomatedEmail_IspAutomatedEmail',
            array('prepareIspAutomatedEmail', 'send'), // Mocked functions
            array($service['service_id']), // Constructor parameters
            '', // Class name
            false // Don't call constructor
        );
        $ispAutomatedEmail->expects($this->exactly(1))
            ->method('prepareIspAutomatedEmail');
        $ispAutomatedEmail->expects($this->exactly(1))
            ->method('send')
            ->will($this->throwException($expectedException));

        $actorId = 3764253;
        $externalActorId = 872639;

        $actor = $this->getMock(
            'Auth_BusinessActor',
            array(
                'getActorId',
            ) // functions to mock
        );
        $actor->expects($this->exactly(1))
            ->method('getActorId')
            ->will($this->returnValue($actorId));

        $autoProblem = $this->getMock(
            'PomsClient_AutoProblem',
            array(
                'raiseProblem'
            ), // functions to mock
            array('bang') // constructor parameters
        );
        $autoProblem->expects($this->exactly(1))
            ->method('raiseProblem')
            ->will($this->returnValue(666));

        $action = $this->getMock(
            'AccountChange_Action_GbWrProductSetChangeJl',
            array(
                'getCoreService',
                'getCurrentDay',
                'getCurrentMonth',
                'getIspAutomatedEmail',
                'getAutoProblem',
                'getLoggedInOrScriptActor',
            ), // Mocked functions
            array($service['service_id'], array()) // Constructor parameters
        );

        $autoProblemTemplateParams = array(
            'className' => get_class($action),
            'actorId' => $actorId,
            'serviceId' => $service['service_id'],
            'exception' => $expectedException
        );

        $client = $this->getMock(
            'AutoProblem_AutoProblemClient',
            array(
                'prepareAutoProblem'
            ) // functions to mock
        );
        $client->expects($this->exactly(1))
            ->method('prepareAutoProblem')
            ->with(AccountChange_Action::AUTO_PROBLEM_TEMPLATE_HANDLE, $autoProblemTemplateParams, $actor)
            ->will($this->returnValue($autoProblem));

        AccountChange_Registry::instance()->setEntry('intOldServiceDefinitionId', $oldServiceDefinitionId);
        AccountChange_Registry::instance()->setEntry('intNewServiceDefinitionId', $newServiceDefinitionId);

        $action->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));
        $action->expects($this->once())
            ->method('getCurrentDay')
            ->will($this->returnValue($currentDay));
        $action->expects($this->once())
            ->method('getCurrentMonth')
            ->will($this->returnValue($currentMonth));
        $action->expects($this->exactly(1))
            ->method('getIspAutomatedEmail')
            ->with($service['service_id'])
            ->will($this->returnValue($ispAutomatedEmail));
        $action->expects($this->exactly(1))
            ->method('getLoggedInOrScriptActor')
            ->will($this->returnValue($actor));
        $action->expects($this->exactly(1))
            ->method('getAutoProblem')
            ->will($this->returnValue($client));

        $action->execute();
    }

    /**
     * Data provider for testExceptionHandledDuringSendEmail
     *
     * @return array
     */
    public function dataProviderForTestExceptionHandledDuringSendEmail()
    {
        return array(
            array(
                array(
                    'isp' => 'greenbee'
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                1, // Old Service Definition ID
                2, // New Service Definition ID
                array(
                    'service_id' => 123456,
                ), // Service Details
                '2012-07-15', // Billing Date
                new Exception('Bang'), // Expected Exception
            ),
        );
    }

    /**
     * Test that email sended by execute will have product details when customer
     * switching from GB or WR to JLP not before billing date
     *
     * @param bool   $estimatedSpeed    Estimated broadband speed
     * @param string $broadbandName     Broadband product name
     * @param float  $broadbandCost     Broadband cost
     * @param int    $broadbandSdi      Broadband service definition id
     * @param array  $wlrDetails        Wlr details array
     * @param array  $expectedEmailData Expected details passed to email
     *
     * @covers AccountChange_Action_GbWrProductSetChangeJl::execute
     *
     * @dataProvider dataProviderWithProductDetailsWhenCustomerSwitchingFromGbWrToJlpNotBeforeBillingDate
     *
     * @return void
     */
    public function testExecuteWillFillEmailWithProductDetailsWhenCustomerSwitchingFromGbWrToJlpNotBeforeBillingDate(
        $estimatedSpeed,
        $broadbandName,
        $broadbandCost,
        $broadbandSdi,
        $wlrDetails,
        $expectedEmailData
    ) {

        $switchGbWrToJlp   = true;
        $beforeBillingDate = false;
        $scheduled         = true;
        $oldSdi            = '********';

        $coreService = new Core_Service();

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getHighestAvailableSpeed')
        );

        $lineCheckResult->expects($this->once())
            ->method('getHighestAvailableSpeed')
            ->will($this->returnValue($estimatedSpeed));

        AccountChange_Registry::instance()->setEntry('intOldServiceDefinitionId', $oldSdi);
        AccountChange_Registry::instance()->setEntry('intNewServiceDefinitionId', $broadbandSdi);
        AccountChange_Registry::instance()->setEntry('bolSchedule', $scheduled);
        AccountChange_Registry::instance()->setEntry('newProductName', $broadbandName);
        AccountChange_Registry::instance()->setEntry('newProductCost', $broadbandCost);
        AccountChange_Registry::instance()->setEntry('objLineCheckResult', $lineCheckResult);
        AccountChange_Registry::instance()->setEntry('intNewServiceDefinitionId', $broadbandSdi);
        AccountChange_Registry::instance()->setEntry('arrSelectedWlr', $wlrDetails);

        $productRules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService'),
            array()
        );

        $productProvDetails = array(
            'intSupplierPlatformID' => 6,
            'intSupplierProductId'  => 143,
            'strProvisionOn'        => 'adsl2'
        );

        $productRules->expects($this->once())
            ->method('getProductProvisionForService')
            ->with(
                $this->equalTo($broadbandSdi),
                $this->equalTo($lineCheckResult)
            )
            ->will($this->returnValue($productProvDetails));

        $action = $this->getMock(
            'AccountChange_Action_GbWrProductSetChangeJl',
            array('getCoreService', 'isCustomerSwitchingFromGbWrToJlp',
                'beforeBillingDateForCurrentMonth', 'getProductRules',
                'sendEmail'),
            array($broadbandSdi, array())
        );

        $action->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));

        $action->expects($this->once())
            ->method('isCustomerSwitchingFromGbWrToJlp')
            ->will($this->returnValue($switchGbWrToJlp));

        $action->expects($this->once())
            ->method('beforeBillingDateForCurrentMonth')
            ->will($this->returnValue($beforeBillingDate));

        $action->expects($this->once())
            ->method('getProductRules')
            ->will($this->returnValue($productRules));

        $action->expects($this->once())
            ->method('sendEmail')
            ->with(
                AccountChange_Action_GbWrProductSetChangeJl::GB_WR_TO_JLP_SCHEDULED_ACCOUNT_CHANGE_EMAIL_TEMPLATE_HANDLE,
                $this->equalTo($expectedEmailData)
            );

        $action->execute();
    }

    /**
     * Data provider for
     * testExecuteWillFillEmailWithProductDetailsWhenCustomerSwitchingFromGbWrToJlpNotBeforeBillingDate
     *
     * @return array
     */
    public function dataProviderWithProductDetailsWhenCustomerSwitchingFromGbWrToJlpNotBeforeBillingDate()
    {
        $broadbandEstimatedSpeed    = 16000;
        $broadbandName              = 'Unlimited';
        $broadbandCost              = 18.0;
        $broadbandSdi               = ********;

        return array(
            //With wlr change
            array(
                'estimatedSpeed'        => $broadbandEstimatedSpeed,
                'broadbandName'         => $broadbandName,
                'broadbandCost'         => $broadbandCost,
                'broadbandSdi'          => $broadbandSdi,
                'wlrDetails'            => array(
                    'strNewProduct' => 'Talking Anytime International',
                    'intNewCost'    => 20.5
                ),
                'expectedEmailData'     => array(
                    'strProduct'                        => $broadbandName,
                    'floOngoingProductCost'             => $broadbandCost,
                    'strEstimatedSpeed'                 => $broadbandEstimatedSpeed / 1000,
                    'bolHomePhone'                      => true,
                    'strHomePhoneProduct'               => 'Talking Anytime International',
                    'floOngoingHomePhoneProductCost'    => 20.5
                )
            ),
            //Without wlr change -> no registry entry
            array(
                'estimatedSpeed'        => $broadbandEstimatedSpeed,
                'broadbandName'         => $broadbandName,
                'broadbandCost'         => $broadbandCost,
                'broadbandSdi'          => $broadbandSdi,
                'wlrDetails'            => null,
                'expectedEmailData'     => array(
                    'strProduct'                        => $broadbandName,
                    'floOngoingProductCost'             => $broadbandCost,
                    'strEstimatedSpeed'                 => $broadbandEstimatedSpeed / 1000,
                    'bolHomePhone'                      => false
                )
            ),
            //Without wlr change -> no key in wlrDetails array
            array(
                'estimatedSpeed'        => $broadbandEstimatedSpeed,
                'broadbandName'         => $broadbandName,
                'broadbandCost'         => $broadbandCost,
                'broadbandSdi'          => $broadbandSdi,
                'wlrDetails'            => array(
                    'intNewCost'    => 20.5
                ),
                'expectedEmailData'     => array(
                    'strProduct'                        => $broadbandName,
                    'floOngoingProductCost'             => $broadbandCost,
                    'strEstimatedSpeed'                 => $broadbandEstimatedSpeed / 1000,
                    'bolHomePhone'                      => false,
                )
            ),
            //Without wlr change -> no wlr name in wlrDetails array
            array(
                'estimatedSpeed'        => $broadbandEstimatedSpeed,
                'broadbandName'         => $broadbandName,
                'broadbandCost'         => $broadbandCost,
                'broadbandSdi'          => $broadbandSdi,
                'wlrDetails'            => array(
                    'strNewProduct' => null,
                    'intNewCost'    => 20.5
                ),
                'expectedEmailData'     => array(
                    'strProduct'                        => $broadbandName,
                    'floOngoingProductCost'             => $broadbandCost,
                    'strEstimatedSpeed'                 => $broadbandEstimatedSpeed / 1000,
                    'bolHomePhone'                      => false,
                )
            )
        );
    }
}
