<?php
include_once 'phing/Task.php';
class IsWritable extends Task {

    private $_strFile = null;
    private $_strUser = null;
    private $_strReturnName;

    public function setFile($str) {
        $this->_strFile = $str;
    }
    
    public function setUser($str) {
        $this->_strUser = $str;
    }

    public function setReturnName($str) {
        $this->_strReturnName = $str;
    }     

    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}  
		
		if(!file_exists($this->_strFile)) {
			throw new BuildException("$this->_strFile is not a valid file/directory", $this->getLocation());		
		}
		
		if (!$this->_strUser) {

			$arrUser['gid'] = posix_getgid();
			$arrUser['uid'] = posix_getuid();
			if($arrUser['uid']===0) {
				$this->project->setProperty($this->_strReturnName, "logged as root");
				return;
			} 
		} else {
			
			$arrUser = posix_getpwnam($this->_strUser);
		}  
		
		//get user user/group id
		$intPerms = fileperms($this->_strFile);
		$intGroup = filegroup($this->_strFile);
		$intOwner = fileowner($this->_strFile);
		
		//check permissions
		if($intPerms & 0x0002) {
			//world writable
        	$this->project->setProperty($this->_strReturnName, "world writable");
			return;
		}
    
		if(($intPerms & 0x0010) && ($intGroup ==  $arrUser['gid'])) {
			//group writable
			$this->project->setProperty($this->_strReturnName, "group writable");
			return;
		}
    	
		if(($intPerms & 0x0080) && ($intOwner ==  $arrUser['uid'])) {
			//owner writable
			$this->project->setProperty($this->_strReturnName, "owner writable");
			return;
		}
    }
}