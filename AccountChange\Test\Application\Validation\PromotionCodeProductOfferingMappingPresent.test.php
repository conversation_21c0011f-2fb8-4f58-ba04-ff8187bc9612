<?php

use Plusnet\BillingApiClient\Service\ServiceManager;
use Plusnet\BillingApiClient\Entity\Constant\BillingAccountStatus;

class AccountChange_PromotionCodeProductOfferingMappingPresentTest  extends PHPUnit_Framework_TestCase
{

    const SERVICE_ID = 12345;
    const PHONE_SCID = 1647;
    const BB_SCID = 1500;
    const BB_SDID = 6000;
    const MARKET_ID = 1;
    const PROMO_CODE = 'testPromotion';

    /**
     * Tests that getErrorCode gives the expected error code
     *
     * @return void
     **/
    public function testGetErrorCodeGivesCorrectCode()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $this->assertEquals(AccountChange_PromotionCodeProductOfferingMappingPresent::ERROR_CODE, $validator->getErrorCode());
    }

    /**
     * Tests that getFailure gives the expected error message
     *
     * @return void
     **/
    public function testGetFailureGivesExpectedErrorMessage()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $this->assertEquals(AccountChange_PromotionCodeProductOfferingMappingPresent::ERROR_MESSAGE, $validator->getFailure());
    }

    /**
     * Tests that when a promotion is checked where the users components all map correctly to c2m products in the
     * database, then the validator returns true.
     *
     * @return void
     **/
    public function testValidateReturnsTrueWhenAllOfTheServiceComponentsInTheDiscountHaveMappings()
    {

        $additionalInfo = array(
            'C2MPromotionCode' => self::PROMO_CODE,
            'phoneServiceComponentId' => self::PHONE_SCID,
            'broadbandServiceDefinitionId' => self::BB_SDID,
            'marketId' => self::MARKET_ID

        );
        $productOfferings[self::PHONE_SCID]['Anytime']['9'] = 1;
        $productOfferings[self::PHONE_SCID]['LineRental']['1'] = 1;
        $productOfferings[self::BB_SCID]['Unlimited']['1'] = 1;

        $promotion = new Plusnet\C2mApiClient\Entity\Promotion();

        $discountBb = new Plusnet\C2mApiClient\Entity\Discount();
        $discountLineRental = new Plusnet\C2mApiClient\Entity\Discount();
        $paymentInformationBb = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('Unlimited', 'SUBSCRIPTION', 'MONTHLY');
        $paymentInformationLineRental = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('LineRental', 'SUBSCRIPTION', 'MONTHLY');
        $discountLineRental->setProductOfferingPaymentInformations(array($paymentInformationLineRental));
        $discountBb->setProductOfferingPaymentInformations(array($paymentInformationBb));
        $discountLineRental->setType(Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING);
        $discountBb->setType(Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING);

        $discounts = array($discountBb, $discountLineRental);
        $promotion->setDiscounts($discounts);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn($promotion);
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getScidFromSdiAndMarket')->andReturn(self::BB_SCID);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn(false);
        $validator->shouldReceive('getC2mProductOfferings')->andReturn($productOfferings);

        $this->assertTrue($validator->validate());
    }

    /**
     * Tests that when we have multiple discount elements, and the users data maps onto one product mapping but not the
     * other, then the validator returns false.
     *
     * @return void
     **/
    public function testValidateReturnsFalseWhenOneOfTheDiscountsDoesNotMap()
    {

        $additionalInfo = array(
            'C2MPromotionCode' => self::PROMO_CODE,
            'phoneServiceComponentId' => self::PHONE_SCID,
            'broadbandServiceDefinitionId' => self::BB_SDID,
            'marketId' => self::MARKET_ID

        );
        $productOfferings[self::BB_SCID]['Unlimited']['1'] = 1;

        $promotion = new Plusnet\C2mApiClient\Entity\Promotion();

        $discountBb = new Plusnet\C2mApiClient\Entity\Discount();
        $discountLineRental = new Plusnet\C2mApiClient\Entity\Discount();
        $paymentInformationBb = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('Unlimited', 'SUBSCRIPTION', 'MONTHLY');
        $paymentInformationLineRental = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('LineRental', 'SUBSCRIPTION', 'MONTHLY');
        $discountLineRental->setProductOfferingPaymentInformations(array($paymentInformationLineRental));
        $discountBb->setProductOfferingPaymentInformations(array($paymentInformationBb));
        $discountLineRental->setType(Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING);
        $discountBb->setType(Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING);

        $discounts = array($discountBb, $discountLineRental);
        $promotion->setDiscounts($discounts);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn($promotion);
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getScidFromSdiAndMarket')->andReturn(self::BB_SCID);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn(false);
        $validator->shouldReceive('getC2mProductOfferings')->andReturn($productOfferings);

        $this->assertFalse($validator->validate());
    }

    /**
     * Tests that when we have multiple discount elements, and the users data maps onto one product mapping but not the
     * other, then the validator returns false.
     *
     * @return void
     **/
    public function testValidateReturnsFalseWhenAllOfTheDiscountsDoNotMap()
    {

        $additionalInfo = array(
            'C2MPromotionCode' => self::PROMO_CODE,
            'phoneServiceComponentId' => self::PHONE_SCID,
            'broadbandServiceDefinitionId' => self::BB_SDID,
            'marketId' => self::MARKET_ID

        );
        $productOfferings = array();

        $promotion = new Plusnet\C2mApiClient\Entity\Promotion();

        $discountBb = new Plusnet\C2mApiClient\Entity\Discount();
        $discountLineRental = new Plusnet\C2mApiClient\Entity\Discount();
        $paymentInformationBb = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('Unlimited', 'SUBSCRIPTION', 'MONTHLY');
        $paymentInformationLineRental = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('LineRental', 'SUBSCRIPTION', 'MONTHLY');
        $discountLineRental->setProductOfferingPaymentInformations(array($paymentInformationLineRental));
        $discountBb->setProductOfferingPaymentInformations(array($paymentInformationBb));
        $discountLineRental->setType(Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING);
        $discountBb->setType(Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING);

        $discounts = array($discountBb, $discountLineRental);
        $promotion->setDiscounts($discounts);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn($promotion);
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getScidFromSdiAndMarket')->andReturn(self::BB_SCID);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn(false);
        $validator->shouldReceive('getC2mProductOfferings')->andReturn($productOfferings);

        $this->assertFalse($validator->validate());
    }

    /**
     * Tests that when a promotion is checked where the users components all map correctly to c2m products in the
     * database, then the validator returns true.
     *
     * @return void
     **/
    public function testValidateOnlyValidatesAgainstOnGoingDiscounts()
    {

        $additionalInfo = array(
            'C2MPromotionCode' => self::PROMO_CODE,
            'phoneServiceComponentId' => self::PHONE_SCID,
            'broadbandServiceDefinitionId' => self::BB_SDID,
            'marketId' => self::MARKET_ID

        );
        $productOfferings[self::BB_SCID]['Unlimited']['1'] = 1;

        $promotion = new Plusnet\C2mApiClient\Entity\Promotion();

        $discountBb = new Plusnet\C2mApiClient\Entity\Discount();
        $discountLineRental = new Plusnet\C2mApiClient\Entity\Discount();
        $paymentInformationBb = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('Unlimited', 'SUBSCRIPTION', 'MONTHLY');
        $paymentInformationLineRental = new Plusnet\C2mApiClient\Entity\ProductOfferingPaymentInformation('LineRental', 'SUBSCRIPTION', 'MONTHLY');
        $discountLineRental->setType(Plusnet\C2mApiClient\Entity\DiscountType::UP_FRONT);
        $discountLineRental->setProductOfferingPaymentInformations(array($paymentInformationLineRental));
        $discountBb->setProductOfferingPaymentInformations(array($paymentInformationBb));

        // There's no mapping for this component, so it would fail - but the discount type is up front, so should be allowed through
        $discountBb->setType(Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING);

        $discounts = array($discountBb, $discountLineRental);
        $promotion->setDiscounts($discounts);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getC2mPromotionFromAdditionalData')->andReturn($promotion);
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getScidFromSdiAndMarket')->andReturn(self::BB_SCID);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn(false);
        $validator->shouldReceive('getC2mProductOfferings')->andReturn($productOfferings);

        $this->assertTrue($validator->validate());
    }

    /**
     * Tests that getScidFromSdiAndMarket defaults to market 3 if no market id is
     * passed in
     *
     * @return void
     **/
    public function testGetScidFromSdiAndMarketUsesDefaultMarketIfNonPassedIn()
    {
        $serviceDefId = 5555;
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator
            ->shouldReceive('getScid')
            ->with($serviceDefId, AccountChange_PromotionCodeProductOfferingMappingPresent::DEFAULT_MARKET_ID)
            ->andReturn(1234);

        $validator->getScidFromSdiAndMarket($serviceDefId, null);
    }

    /**
     * Tests that getScidFromSdiAndMarket returns null if the service definition id is null
     * passed in
     *
     * @return void
     **/
    public function testGetScidFromSdiAndMarketReturnsNullForMissingServiceDefId()
    {
        $serviceDefId = null;
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        $validator = Mockery::mock('AccountChange_PromotionCodeProductOfferingMappingPresent', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $this->assertNull($validator->getScidFromSdiAndMarket($serviceDefId, null));

    }



    /**
     * Return a mock c2m promotion
     *
     * @return Promotion
     **/
    protected function getC2mPromotionMock()
    {
        $c2mPromotion = Mockery::mock('Plusnet\C2mApiClient\Entity\Promotion');
        $c2mPromotion->makePartial();
        $c2mPromotion->shouldAllowMockingProtectedMethods();
        $c2mPromotion->shouldReceive('getDiscounts')->andReturn($mockDiscounts);
        return $c2mPromotion;
    }
}
