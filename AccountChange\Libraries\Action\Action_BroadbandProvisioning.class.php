<?php

/**
 * <AUTHOR>
 */

use Plusnet\InventoryEventClient\Context\ScheduledAccountChangeContext;
use Plusnet\InventoryEventClient\Service\ActiveContextService;

abstract class AccountChange_Action_BroadbandProvisioning extends AccountChange_Action
{
    /**
     * Update the supplier product for the given customer
     * @param bool $makeActive True to make active now, false to make the entry pending
     * @return void
     */
    protected function updateSupplierProduct($makeActive)
    {
        $this->includeLegacyFiles();

        $this->updateProvisionedService($makeActive);
        $this->addProvisioningHistory($makeActive);

        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * @param bool $makeActive whether we're activating now or scheduling
     * @return void
     */
    protected function updateProvisionedService($makeActive)
    {
        $intSupplierProductId = $this->getSupplierProduct()->getSupplierProductId()->getValue();

        //change speed function
        adslSetProvisionedService(
            $this->intServiceId,
            $intSupplierProductId,
            ADSL_PROVISIONED_EVENT_MANUAL,
            $makeActive
        );
    }

    /**
     * @param bool $makeActive whether we're activating now or scheduling
     * @return void
     */
    protected function addProvisioningHistory($makeActive)
    {
        $pendingText = '';
        if (!$makeActive) {
            $pendingText = 'Pending ';
        }

        $processText = '';
        if (array_key_exists('bolHousemove', $this->arrOptions) && $this->arrOptions['bolHousemove'] == true) {
            $processText = 'HouseMove ';
        }

        ADSLProvisionHistoryAdd(
            $this->intServiceId,
            0,
            sprintf(
                '%sProvisioned Service \'%s\' added as a part of %sAccount Change process',
                $pendingText,
                $this->getSupplierProduct()->getName(),
                $processText
            ),
            date('Y-m-d H:i:s')
        );
    }

    /**
     * Function to override options for unit testing
     *
     * @param array $options options
     * @return void
     */
    public function setOptions($options)
    {
        $this->arrOptions = $options;
    }

    /**
     * @return bool
     */
    protected function requiresOrderPlacement()
    {
        $identifyAction = $this->getIdentifyAction();

        return $identifyAction->isOrderPlacementRequiredForAction(
            $identifyAction->getActionRequired()
        );
    }

    /**
     * @return AccountChange_Action_IdentifyAction
     */
    protected function getIdentifyAction()
    {
        return new AccountChange_Action_IdentifyAction($this->intServiceId);
    }

    /**
     * Function to indicate if the account change is scheduled
     *
     * @return bool
     */
    protected function isScheduledAccountChange()
    {
        $activeContext = ActiveContextService::getActiveContext();
        return $activeContext instanceof ScheduledAccountChangeContext;
    }
}
