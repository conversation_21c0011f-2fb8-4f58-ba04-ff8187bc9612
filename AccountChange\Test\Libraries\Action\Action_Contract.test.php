<?php

/**
 * File Action_Contract.test.php
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2009 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */
/**
 * Account Change Contract Action Test
 *
 * Testing class for AccountChange_Action_Contract
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2009 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Action_Contract_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Service Id
     *
     * @var int
     */
    protected $intServiceId;

    /**
     * PHPUnit setup function
     *
     * @return void
     */
    public function setup()
    {
        $this->intServiceId = 999;
    }

    /**
     * PHPUnit TearDown function
     *
     * @return void
     */
    public function tearDown()
    {
        Lib_Userdata::resetInstance();
    }

    /**
     * Test that sub actions if not executed
     *
     * @covers AccountChange_Action_Contract::execute
     *
     * @return void
     */
    public function testExecuteNotCallsCorrectSubActions()
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue(array('isp' => 'johnlewis')));

        Db_Manager::setAdaptor('Core', $db, Db_Manager::DEFAULT_TRANSACTION);

        $objMock = $this->getMock(
            'AccountChange_Action_Contract',
            array('restartDeferredContracts', 'createContract'),
            array($this->intServiceId)
        );

        $objMock->expects($this->never())
            ->method('restartDeferredContracts');

        $objMock->execute();
    }

    /**
     *  Test that all of sub actions of execute are called
     *
     * @covers AccountChange_Action_Contract::execute
     *
     * @return void
     */
    public function testExecuteCallsCorrectSubActions()
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue(array('isp' => 'greenbee')));

        Db_Manager::setAdaptor('Core', $db, Db_Manager::DEFAULT_TRANSACTION);

        $objMock = $this->getMock(
            'AccountChange_Action_Contract',
            array('restartDeferredContracts', 'createContract'),
            array($this->intServiceId)
        );

        $objMock->expects($this->once())
            ->method('restartDeferredContracts');

        $objMock->execute();
    }

    /**
     * Tests 'restartDeferredContract'. Checks if it does not create contract if not JLP visp
     *
     * @covers AccountChange_Action_Contract::restartDeferredContracts
     *
     * @return void
     */
    public function testRestartDeferredContractDoesNotCreateContractIfNotAJlpVisp()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue(array('isp' => 'plus.net')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMock = $this->getMock(
            'AccountChange_Action_Contract',
            array('createContract'),
            array($this->intServiceId, array('intServiceDefinitionId' => 1234))
        );

        $objMock->expects($this->never())
            ->method('createContract');

        $objMock->execute();
    }

    /**
     * Tests for restartDeferredContract. Checks if it creates contract for Waitrose visp
     *
     * @covers AccountChange_Action_Contract::restartDeferredContracts
     *
     * @return void
     */
    public function testRestartDeferredContractDoesCreateContractIfWaitroseVisp()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue(array('isp' => 'waitrose')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMock = $this->getMock(
            'AccountChange_Action_Contract',
            array('createContract'),
            array($this->intServiceId, array('intServiceDefinitionId' => 1234))
        );

        $objMock->execute();
    }

    /**
     * Tests for restartDeferredContract
     *
     * @covers AccountChange_Action_Contract::restartDeferredContracts
     *
     * @return void
     */
    public function testRestartDeferredContractDoesCreateContractIfGreenbeeVisp()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue(array('isp' => 'greenbee')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMock = $this->getMock(
            'AccountChange_Action_Contract',
            array('createContract'),
            array($this->intServiceId, array('intServiceDefinitionId' => 1234))
        );

        $objMock->execute();
    }

    /**
     * Test for 'execute'
     *
     * @covers AccountChange_Action_Contract::restartDeferredContracts
     * @covers AccountChange_Action_Contract::createFibreContract
     *
     * @return void
     */
    public function testCreateFibreContractCreatesCancellationAndActivationContractsForWlrCustomer()
    {
        $contractDefinition1 = new Plusnet\ContractsClient\Entity\ContractDefinition();
        $contractDefinition1->setTypeHandle(\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_CANCELLATION);
        $contractDefinition1->setId(1);

        $contractDefinition2 = new Plusnet\ContractsClient\Entity\ContractDefinition();
        $contractDefinition2->setTypeHandle(\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_ACTIVATION);
        $contractDefinition2->setId(2);

        $contractsClient = $this->getMock(
            'Plusnet\\ContractsClient\\Client',
            array(
                'setServiceId',
                'getAllContractDefinitions',
                'createActiveContract'
            )
        );

        $contractsClient->expects($this->once())
            ->method('setServiceId')
            ->with($this->intServiceId)
            ->will($this->returnValue($contractsClient));

        $contractsClient->expects($this->once())
            ->method('getAllContractDefinitions')
            ->will($this->returnValue(array($contractDefinition1, $contractDefinition2)));

        $contractsClient->expects($this->any())
            ->method('createActiveContract')
            ->will($this->returnValue($contractsClient));

        BusTier_BusTier::setClient('contracts', $contractsClient);

        AccountChange_Registry::instance()->setEntry('bolActivationContract', true);

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will(
                $this->returnValue(
                    array(
                        'isp' => 'plusnet',
                        'provisioningProfile' => 'FTTC',
                    )
                )
            );

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMock = $this->getMock(
            'AccountChange_Action_Contract',
            array('createContract'),
            array($this->intServiceId, array('intServiceDefinitionId' => 1234))
        );

        $objMock->expects($this->never())
            ->method('createContract');

        $objMock->execute();
    }

    /**
     * Test for 'execute'. Checks 'createFibreContract' creates ACTIVATION contract only if
     * phone is taken.
     *
     * @covers AccountChange_Action_Contract::restartDeferredContracts
     * @covers AccountChange_Action_Contract::createFibreContract
     * @covers AccountChange_Action_Contract::createContract
     *
     * @return void
     */
    public function testCreateFibreContractCreatesActivationContractOnlyIfPhoneNotTaken()
    {
        $contractDefinition1 = new Plusnet\ContractsClient\Entity\ContractDefinition();
        $contractDefinition1->setTypeHandle(\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_CANCELLATION);
        $contractDefinition1->setId(1);

        $contract1 = new Plusnet\ContractsClient\Entity\Contract($contractDefinition1);
        $contractedService1 = new Plusnet\ContractsClient\Entity\ContractedService();
        $contractedService1->setSubjectHandle(
            \Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_CANCELLATION
        );
        $contract1->addContractedService($contractedService1);

        $contractDefinition2 = new Plusnet\ContractsClient\Entity\ContractDefinition();
        $contractDefinition2->setTypeHandle(\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_ACTIVATION);
        $contractDefinition2->setId(2);

        $contract2 = new Plusnet\ContractsClient\Entity\Contract($contractDefinition2);

        $contractsClient = $this->getMock(
            'Plusnet\\ContractsClient\\Client',
            array(
                'setServiceId',
                'getAllContractDefinitions',
                'createActiveContract',
                'getAllActiveContracts',
                'withdrawContractedService'
            )
        );

        $contractsClient->expects($this->any())
            ->method('setServiceId')
            ->with($this->intServiceId)
            ->will($this->returnValue($contractsClient));

        $contractsClient->expects($this->any())
            ->method('getAllContractDefinitions')
            ->will($this->returnValue(array($contractDefinition1, $contractDefinition2)));

        $contractsClient->expects($this->any())
            ->method('getAllActiveContracts')
            ->will($this->returnValue(array($contract1, $contract2)));

        $contractsClient->expects($this->any())
            ->method('createActiveContract')
            ->will($this->returnValue($contractsClient));

        BusTier_BusTier::setClient('contracts', $contractsClient);

        AccountChange_Registry::instance()->setEntry('bolActivationContract', false);

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will(
                $this->returnValue(
                    array(
                        'isp' => 'johnlewis',
                        'provisioningProfile' => 'FTTC',
                    )
                )
            );

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $contractAction = new AccountChange_Action_Contract(
            $this->intServiceId, array('intServiceDefinitionId' => 1234)
        );

        $contractAction->execute();
    }
}
