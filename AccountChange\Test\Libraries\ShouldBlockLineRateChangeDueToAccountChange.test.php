<?php

/**
 * <AUTHOR>
 */

class TestShouldBlockLineRateChangeDueToAccountChange extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    protected function tearDown()
    {
        Mockery::close();
    }

    /**
     * @return void
     */
    public function testResultReturnsFalse()
    {
        $serviceId = 12345;

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor
            ->shouldReceive('shouldBlockLineRateChangeDueToAccountChangeByServiceId')
            ->with($serviceId)
            ->andReturnFalse();
        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $result = (new AccountChange_ShouldBlockLineRateChangeDueToAccountChange())->query($serviceId);

        $this->assertEquals(false, $result);
    }

    /**
     * @return void
     */
    public function testResultReturnsTrue()
    {
        $serviceId = 12345;

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor
            ->shouldReceive('shouldBlockLineRateChangeDueToAccountChangeByServiceId')
            ->with($serviceId)
            ->andReturnTrue();
        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $result = (new AccountChange_ShouldBlockLineRateChangeDueToAccountChange())->query($serviceId);

        $this->assertEquals(true, $result);
    }
}
