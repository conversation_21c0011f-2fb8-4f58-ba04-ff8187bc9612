<?php

class AccountChange_EmailHandler_ContractMessageHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that the contract message created relates to the customer getting a new contract
     *
     * @covers AccountChange_EmailHandler_ContractMessageHelper::generateContractMessage()
     */
    public function testNewContractMessageIsGenerated()
    {
        $contract = new AccountChange_AccountChangeOrderContract();
        $contract->setLength(18);

        $contractMessageHelper = new AccountChange_EmailHandler_ContractMessageHelper(null, $contract);

        $expected = 'Choosing this package means you will be subject to a new 18 month contract';
        $actual   = $contractMessageHelper->generateContractMessage();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that the contract message created relates to the customer's current contract
     *
     * @covers AccountChange_EmailHandler_ContractMessageHelper::generateContractMessage()
     */
    public function testCurrentContractMessageIsGenerated()
    {
        $contractMessageHelper = $this->getMock(
            'AccountChange_EmailHandler_ContractMessageHelper',
            array('getActiveContract'),
            array(null, null)
        );

        $contractMessageHelper
            ->expects($this->once())
            ->method('getActiveContract')
            ->willReturn(array(
                'duration'     => '12',
                'currentMonth' => '6'
            ));

        $expected = 'You\'re in month 6 of your 12 month contract';
        $actual   = $contractMessageHelper->generateContractMessage();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that the contract message created relates to the customer changing their phone product
     *
     * @covers AccountChange_EmailHandler_ContractMessageHelper::generateContractMessage()
     */
    public function testPhoneOnlyMessageIsGenerated()
    {
        $contractMessageHelper = $this->getMock(
            'AccountChange_EmailHandler_ContractMessageHelper',
            array('getActiveContract'),
            array(null, null)
        );

        $contractMessageHelper
            ->expects($this->once())
            ->method('getActiveContract')
            ->willReturn(array());

        $expected = 'Your phone package is subject to a minimum notice period of 14 days';
        $actual   = $contractMessageHelper->generateContractMessage();

        $this->assertEquals($expected, $actual);
    }
}