<?php

/**
 * File Product_WlrProductFilter.test.php
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */

/**
 * Class AccountChange_Product_WlrProductFilterTest
 *
 * Unit tests for AccountChange_Product_WlrProductFilter
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */
require_once('Test/Libraries/Product/Product_WlrProductFilterBase.php');

class AccountChange_Product_WlrProductFilterNotDateSensitiveTest extends AccountChange_Product_WlrProductFilterBase
{

    /**
     * Test for getMappedMobileProduct
     *
     * @param int $wlrProductId               Wlr product Id
     * @param int $expectedMappedWlrProductId 'With Mobile' version of the product
     *
     * @covers AccountChange_Product_WlrProductFilter::getMappedMobileProduct
     * @dataProvider provideDataForGetMappedMobileProductTest
     *
     * @return void
     */
    public function testGetMappedMobileProduct($wlrProductId, $expectedMappedWlrProductId)
    {
        $filter = $this->getFilterMock();
        $mappedProductId = $filter->getMappedMobileProduct($wlrProductId);

        $this->assertEquals($expectedMappedWlrProductId, $mappedProductId);
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForLegacyProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = $this->legacyProducts;

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            919,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForADSLLegacyProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = 919,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForLegacyADSLProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts,
            $this->legacyMobileProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = 919,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForLegacyFibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = 919,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::PRE_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForPenguinAdslProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);
        $expectedResult = array_merge(
            $this->penguinMlrOnlyProducts,
            $this->penguinProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForPenguinAdslProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->penguinLrsOnly,
            $this->penguinProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForPenguinAdslProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->penguinLrsOnly,
            $this->penguinLrsOnlyWithMobile,
            $this->penguinProducts,
            $this->penguinProductsMobileVersions,
            $this->penguinMobileProduct
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForPenguinAdslWeekendsProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->penguinMlrOnlyProducts,
            $this->penguinProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     */
    public function testInvalidFilterType()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array ();

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
            "Invalid Filter Type",
            self::THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::PRE_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * Are we changing from an old product and this is a new one?
     *
     * Workplace will flag this per-item
     *
     * @covers AccountChange_Product_WlrProductFilter::isOldToNew
     *
     * @return void
     */
    public function testIsOldToNewForWorkplace()
    {
        $filter = $this->getFilterMock();

        // New product offered and current is old => true
        $this->assertTrue(
            $filter->isOldToNew(
                array(
                    'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
                    'strContract' => 'MONTHLY',
                    'strProductName' => 'Penguin Weekends',
                ),
                670
            )
        );

        // New product offered and current is new => false
        $this->assertFalse(
            $filter->isOldToNew(
                array (
                    'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
                    'strContract' => 'MONTHLY',
                    'strProductName' => 'Penguin Evening and Weekends',
                ),
                AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS
            )
        );

        // No new product offered and current is old => false
        $this->assertFalse(
            $filter->isOldToNew(
                array (
                    'intNewWlrId' => '670',
                    'strContract' => 'MONTHLY',
                    'strProductName' => 'Talk Evenings & Weekends',
                ),
                919
            )
        );
    }

    /**
     * Are we changing from an old product and offering new ones
     *
     * Portal will flag this for the whole page
     *
     * @covers AccountChange_Product_WlrProductFilter::isOldToNewList
     *
     * @return void
     */
    public function testIsOldToNewListForPortal()
    {
        $filter = $this->getFilterMock();

        // New product offered and current is old => true
        $this->assertTrue(
            $filter->isOldToNewList(
                array(
                    array (
                        'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
                        'strContract' => 'MONTHLY',
                        'strProductName' => 'Penguin Weekends',
                    ),
                ),
                670
            )
        );

        // New product offered and current is new => false
        $this->assertFalse(
            $filter->isOldToNewList(
                array(
                    array (
                        'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
                        'strContract' => 'MONTHLY',
                        'strProductName' => 'Penguin Evening and Weekends',
                    ),
                ),
                AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS
            )
        );

        // No new product offered and current is old => false
        $this->assertFalse(
            $filter->isOldToNewList(
                array(
                    array (
                        'intNewWlrId' => '670',
                        'strContract' => 'MONTHLY',
                        'strProductName' => 'Talk Evenings & Weekends',
                    ),
                ),
                919
            )
        );
    }

}
