CREATE TABLE `userdata`.`service_change_schedule` (
  `schedule_id` int(10) unsigned NOT NULL auto_increment,
  `service_id` int(8) unsigned zerofill NOT NULL default '00000000',
  `owner_id` varchar(32) NOT NULL default '',
  `old_type` int(8) unsigned NOT NULL default '0',
  `new_type` int(8) unsigned NOT NULL default '0',
  `intTariffID` int(10) unsigned default NULL,
  `intPromotionCodeId` int(10) unsigned default NULL,
  `change_complete` enum('yes','no') default 'no',
  `change_complete_date` date NOT NULL default '0000-00-00',
  `change_date` date default '0000-00-00',
  `active` enum('yes','no') NOT NULL default 'yes',
  `db_src` varchar(4) NOT NULL default '',
  `intFromCBCFlexID` int(10) NOT NULL default '0',
  `intToCBCFlexID` int(10) NOT NULL default '0',
  `bolCbcFix` tinyint(1) default NULL,
  `intContractLengthID` int(11) default NULL,
  `timestamp` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (`schedule_id`),
  KEY `service_id` (`service_id`),
  KEY `active` (`active`),
  KEY `idxTariffId` (`intTariffID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1