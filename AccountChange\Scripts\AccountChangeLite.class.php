<?php
/**
 * <AUTHOR> <deepthy.vals<PERSON><PERSON><PERSON>@plus.net>
 */
require_once '/local/data/mis/database/standard_include.inc';
require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
require_once '/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php';

/**
 * Account change script to migrate customer from Market A to Market B products.
 *
 * Market change Example: php /local/codebase2005/modules/Framework/Scripts/RunScript.php -c AccountChange_AccountChangeLite -p PLUSNET dataPar1.csv > dataPar1RunLog.log';
   For Broadband change please run with extra parameter BROADBANDCHANGE ,
   Example php /local/codebase2005/modules/Framework/Scripts/RunScript.php -c AccountChange_AccountChangeLite-p PLUSNET dataPar1.csv BROADBANDCHANGE> dataPar1RunLog.log';
 *
 * Class AccountChange_AccountChangeLite
 */
class Account<PERSON><PERSON><PERSON>_AccountChangeLite implements Mvc_Script
{
    const MISMATCH_LOG_FILE = 'CurrentSDIMismatch-';
    const INVALID_ORDER_LOG_FILE = 'InvalidOrder-';
    const ACCOUNT_CHANGE_NOT_ALLOWED_LOG_FILE = 'AccountChangeNotAllowed-';
    const ACCOUNT_CHANGE_API_FAILURE_LOG_FILE = 'AccountChangeApiFailure-';
    const ACCOUNT_CHANGE_API_SUCCESS_LOG_FILE = 'AccountChangeApiSuccess-';
    const DATE_FORMAT = 'Y-m-d';
    const TIME_FORMAT = 'H:i:s';
    const BROADBAND_CHANGE = 'BROADBANDCHANGE';

    private $broadbandChangeFlag=0;
    private $accountData;
    private $logDirectory;



    /**
     * constructor
     *
     * Do not pass any args here
     */
    public function __construct()
    {
        $this->importLegacy();
    }
    /**
     * Get a string version of what short opts we want to register
     *
     * Mvc_Script method
     *
     * @return string
     */
    public static function getShortOpts()
    {
        return '';
    }

    /**
     * Get an array of long opts we want to register
     *
     * Mvc_Script method
     *
     * @return array
     */
    public static function getLongOpts()
    {
        return array();
    }

    /**
     * Determines if parallel runs are allowed
     *
     * Mvc_Script method
     *
     * @return boolean
     */
    public function bolAllowParallelRuns()
    {
        return true;
    }

    /**
     * @param  array $args    Arguments
     * @param  array $options Run options
     * @return null
     */
    public function run(array $args, array $options)
    {
        echo "Log Dir is ". $this->logDirectory;

        $time_start = microtime(true);

        if (sizeof($args) > 0 && $this->isCsvFile($args[0])) {
            if (sizeof($args) > 1) {
                if ($args[1] === self::BROADBAND_CHANGE) {
                    $this->broadbandChangeFlag=1;
                } else {
                    echo "Invalid Option  $args[1]";
                    return;
                }
            }
            echo "Broadband change flag = ".$this->broadbandChangeFlag;
            $csvFileName = $args[0];
            $file = fopen($csvFileName, 'rb');
            $header = fgetcsv($file);

            $this->logDirectory = explode('.', $args[0])[0];
            if (!file_exists($this->logDirectory) && !mkdir($this->logDirectory) && !is_dir($this->logDirectory)) {
                throw new \RuntimeException(sprintf('Directory "%s" was not created', $this->logDirectory));
            }

            echo '[' . date('Y-m-d H:i:s') . ']: ' . "Beginning to process CSV file...\n";
            while ($row = fgetcsv($file)) {
                $this->accountData[] = array_combine($header, $row);
            }

            fclose($file);
            echo '[' . date('Y-m-d H:i:s') . ']: ' . "Successfully processed CSV file...\n";
            $this->login();
            echo '[' . date('Y-m-d H:i:s') . ']: ' . "Logged in successfully...\n";
            $this->processAccounts();
            $time_end = microtime(true);
            $execution_time = ($time_end - $time_start)/60;
            echo "<b>Total Execution Time:</b> '.$execution_time.' Mins\n";
        } else {
            $this->help();
        }
    }

    /**
     * Migrate given accounts onto new products
     *
     * @return null
     */
    private function processAccounts()
    {
        $inventoryEventService = BusTier_BusTier::getClient('inventoryEventService');
        $context = new \Plusnet\InventoryEventClient\Context\AccountChangeContext();

        foreach ($this->accountData as $account) {
            $serviceId = $account['serviceId'];
            $overrideFlag = array_key_exists('override', $account) ? $account['override'] : false;

            echo '[' . date('Y-m-d H:i:s') . ']: ' . "Beginning migration on account with service ID: $serviceId...\n";

            $currentServiceDefinition = str_pad($account['currentSdi'], 8, '0', STR_PAD_LEFT);
            $newServiceDefinition = str_pad($account['newSdi'], 8, '0', STR_PAD_LEFT);
            $currentLiveSdi = $this->getCurrentServiceDefinitionId($serviceId);
            if ($currentLiveSdi !== $currentServiceDefinition) {
                $message = '*****************************************************************'
                    . "\n" . 'Service ID: ' . $serviceId . "\n" .
                    'Current service definition ID (' . $currentLiveSdi
                    . ') does not match given current service definition ID (' . $currentServiceDefinition
                    . ')' . "\n" . '*****************************************************************' . "\n";
                echo $message;
                echo MISMATCH_LOG_FILE;
                $this->logToFile($message, self::MISMATCH_LOG_FILE . date('Y-m-d') . '.log', true);
                continue;
            }

            $inventoryEventService->takePreChangeSnapshot($serviceId, $context);
            echo '[' . date('Y-m-d H:i:s') . ']: ' . "Taking pre change snapshot...\n";
            if ($this->broadbandChangeFlag==1) {
                $this->performBroadbandChange($serviceId, $newServiceDefinition, $overrideFlag);
            } else {
                $this->performAccountChange($serviceId, $currentServiceDefinition, $overrideFlag);
            }
            $this->updateServiceChangeSchedule($serviceId);

            $inventoryEventService->takePostChangeSnapshot($serviceId);
            echo '[' . date('Y-m-d H:i:s') . ']: ' . "Taking post change snapshot and completing migration...\n";
        }
    }

    /**
     * Get the current service definition ID for the given service ID
     *
     * @param  $serviceId Service Id
     * @return int service definition ID
     */
    private function getCurrentServiceDefinitionId($serviceId)
    {
        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        return $objDatabase->getServiceDefinitionDetailsForService($serviceId)['service_definition_id'];
    }

    /**
     * Function to perform an instant account change using the AccountChangeApi
     *
     * @param $serviceId                Service Id
     * @param $currentServiceDefinition Current ServiceDefinitionId
     * @param $overrideFlag             Override Flag
     * @return null
     */
    private function performAccountChange($serviceId, $currentServiceDefinition, $overrideFlag)
    {
        $accountChangeOrder = new \AccountChange_AccountChangeOrder();
        $accountChangeOrder->setServiceId($serviceId);
        $accountChangeOrder->setMarket(3);
        $accountChangeOrder->setType(AccountChange_AccountChangeOrder::TYPE_MY_OFFERS_PRODUCT_CHANGE);
        $accountChangeOrder->setIsScheduledChange(false);
        $accountChangeOrder->setCommsRequired(false);
        $accountChangeOrder->setMigrateBroadbandDiscount(true);
        $accountChangeOrder->setRetainCurrentContracts(true);
        if ($overrideFlag === 'true') {
            $accountChangeOrder->setScript(true);
        }

        $accountChangeOrderProducts = new \AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId($currentServiceDefinition);

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->addConsent(AccountChange_AccountChangeOrder::CONSENT_L2C, 'PN-MM-1');

        $accountChangeApi = $this->getAccountChangeApi();

        try {
            $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
            $message = '[' . date(self::TIME_FORMAT)
                . '] The order was processed successfully for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile($message, self::ACCOUNT_CHANGE_API_SUCCESS_LOG_FILE . date('Y-m-d') . '.log', false);
        } catch (\AccountChange_InvalidAccountChangeOrderException $e) {
            $message = '[' . date(self::TIME_FORMAT)
                . '] The order was invalid for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile(
                $message,
                self::INVALID_ORDER_LOG_FILE
                . date(self::DATE_FORMAT) . '.log',
                true,
                $e
            );
        } catch (\AccountChange_AccountChangeNotAllowedException $e) {
            $message = '[' . date(self::TIME_FORMAT)
                . '] The account change was not allowed for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile(
                $message,
                self::ACCOUNT_CHANGE_NOT_ALLOWED_LOG_FILE
                . date(self::DATE_FORMAT) . '.log',
                true,
                $e
            );
        } catch (\AccountChange_AccountChangeApiFailureException $e) {
            $message = '[' . date(self::TIME_FORMAT)
                . '] The account change api failed for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile(
                $message,
                self::ACCOUNT_CHANGE_API_FAILURE_LOG_FILE
                . date(self::DATE_FORMAT) . '.log',
                true,
                $e
            );
        }
    }


    /**
     * Function to perform an instant broadband change using the AccountChangeApi
     *
     * @param $serviceId            Service Id
     * @param $newServiceDefinition Service Definition Id of the product we want to change to
     * @param $overrideFlag         override Flag
     * @return null
     */
    protected function performBroadbandChange($serviceId, $newServiceDefinition, $overrideFlag)
    {
        $accountChangeOrder = new \AccountChange_AccountChangeOrder();
        $accountChangeOrder->setServiceId($serviceId);
        $accountChangeOrder->setType(AccountChange_AccountChangeOrder::TYPE_MY_OFFERS_PRODUCT_CHANGE);
        $accountChangeOrder->setIsScheduledChange(false);
        $accountChangeOrder->setCommsRequired(false);
        $accountChangeOrder->setMigrateBroadbandDiscount(true);
        $accountChangeOrder->setRetainCurrentContracts(true);
        if ($overrideFlag === 'true') {
            $accountChangeOrder->setScript(true);
        }

        $accountChangeOrderProducts = new \AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId($newServiceDefinition);

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->addConsent(AccountChange_AccountChangeOrder::CONSENT_L2C, 'PN-MM-1');

        $accountChangeApi = $this->getAccountChangeApi();

        try {
            $accountChangeApi->processAccountChangeRequest($accountChangeOrder);
            $message = '[' . date(self::TIME_FORMAT)
                . '] The order was processed successfully for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile(
                $message,
                self::ACCOUNT_CHANGE_API_SUCCESS_LOG_FILE . date('Y-m-d') . '.log',
                false
            );
        } catch (\AccountChange_InvalidAccountChangeOrderException $e) {
            $message = '[' . date(self::TIME_FORMAT)
                . '] The order was invalid for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile(
                $message,
                self::INVALID_ORDER_LOG_FILE
                . date(self::DATE_FORMAT) . '.log',
                true,
                $e
            );
        } catch (\AccountChange_AccountChangeNotAllowedException $e) {
            $message = '[' . date(self::TIME_FORMAT)
                . '] The account change was not allowed for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile(
                $message,
                self::ACCOUNT_CHANGE_NOT_ALLOWED_LOG_FILE
                . date(self::DATE_FORMAT) . '.log',
                true,
                $e
            );
        } catch (\AccountChange_AccountChangeApiFailureException $e) {
            $message = '[' . date(self::TIME_FORMAT)
                . '] The account change api failed for service ID: ' . $accountChangeOrder->getServiceId();
            $this->logToFile(
                $message,
                self::ACCOUNT_CHANGE_API_FAILURE_LOG_FILE
                . date(self::DATE_FORMAT) . '.log',
                true,
                $e
            );
        }
    }



    /**
     * Function to update in progress account changes.
     *
     * @param $serviceId service Id
     * @return null
     */
    private function updateServiceChangeSchedule($serviceId)
    {
        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $activeChanges = $objDatabase->getActiveChangeScheduleRows($serviceId);
        foreach ($activeChanges as $activeChange) {
            if (!empty($activeChange)) {
                // Need to get the market 3 tariff ID for the given service definition ID
                $tariffID = $objDatabase->getTariffId(3, $activeChange['new_type']);
                $objDatabase->updateServiceChangeScheduleWithNewServiceDefinitionIds(
                    $tariffID,
                    $activeChange['schedule_id']
                );
            }
        }
    }

    /**
     * Get an account change api instance
     *
     * @return AccountChange_AccountChangeApi
     */
    protected function getAccountChangeApi()
    {
        return new \AccountChange_AccountChangeApi();
    }

    /**
     * Log a message to file
     *
     * @param string    $message    message to log
     * @param string    $fileName   file to log messages to
     * @param bool      $separators Should separators be added between messages
     * @param Exception $exception  Exception message
     *
     * @return void
     */
    private function logToFile($message, $fileName, $separators, $exception = null)
    {
        $file = $this->logDirectory . '/' . $fileName;

        echo "Log Dir is ". $this->logDirectory;
        if ($separators) {
            file_put_contents(
                $file,
                "\n******************************************************************",
                FILE_APPEND
            );
        }
        file_put_contents($file, "\n[" . date('Y-m-d H:i:s') . ']: ' . $message, FILE_APPEND);
        if ($exception !== null) {
            file_put_contents($file, "\n[" . date('Y-m-d H:i:s') . '] Exception: ', FILE_APPEND);
            file_put_contents($file, $exception->getMessage(), FILE_APPEND);
        }
        if ($separators) {
            file_put_contents(
                $file,
                "\n******************************************************************",
                FILE_APPEND
            );
        }
    }

    /**
     * Checks that file is a valid file
     *
     * @param string $filePath argument given by script user
     *
     * @return bool
     */
    private function isCsvFile($filePath)
    {
        return file_exists($filePath) && substr($filePath, -3) === 'csv';
    }

    /**
     * Prints the command help
     *
     * @return null
     */
    private function help()
    {
        print 'You need to run this script with a valid CSV file. The CSV file needs to have 3 different headers; 
        serviceId, currentSdi and newSdi.';
        print 'Example: php /local/codebase2005/modules/Framework/Scripts/RunScript.php 
        -c AccountChange_AccountChangeLite -p PLUSNET dataPar1.csv > dataPar1RunLog.log';
        print 'For Broadband change please run with extra parameter BROADBANDCHANGE , 
        Example : php /local/codebase2005/modules/Framework/Scripts/RunScript.php -c AccountChange_AccountChangeLite 
        -p PLUSNET dataPar1.csv BROADBANDCHANGE> dataPar1RunLog.log';
    }

    /**
     * Log in to the business tier,
     * this has been added as a fix for P69075
     *
     * @return void
     */
    protected function login()
    {
        $login = Auth_Auth::getCurrentLogin();
        $authSettings = $login->getAuthSettings();
        BusTier_BusTier::reset();
        $session = BusTier_BusTier::login(
            $authSettings->getUsername(),
            $authSettings->getRealm(),
            $authSettings->getPassword()
        );
        $login->setLoginSession($session);
    }

    /**
     * Import Legacy libraries
     *
     * @return null
     */
    public function importLegacy()
    {
        include_once '/local/www/database-admin/config/config.inc';
        include_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProduct.inc';
    }
}
