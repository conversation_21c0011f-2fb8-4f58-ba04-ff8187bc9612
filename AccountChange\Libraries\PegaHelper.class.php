<?php

/**
 * Helper functions that allow us to interact with Pega
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2020 PlusNet
 */

class AccountChange_PegaHelper
{
    /**
     * Registers an interaction with <PERSON><PERSON><PERSON>, either success or failure
     *
     * @param int    $impressionOfferId  Impresssion offer id
     * @param string $interaction        Status to register (e.g. Success, Failure)
     * @param int    $actorId            Business actor making the change
     * @param bool   $isExternalCustomer Check to use customer or agent channels
     * @param string $behaviour          Behaviour of PEGA interaction
     * @param string $direction          Direction of PEGA interaction
     * @param string $correlationId      Correlation ID
     * @param string $sessionId          Session ID
     */
    public function registerInteraction($impressionOfferId, $interaction, $actorId, $isExternalCustomer, $behaviour = 'Positive', $direction='Inbound', $correlationId = null, $sessionId = null)
    {
        try {
            $client = $this->getNextBestActionClient();
            $client->registerImpressionInteraction($impressionOfferId, $interaction, $actorId, $isExternalCustomer, $behaviour, $direction, $correlationId, $sessionId);
        } catch (Exception $e) {
            // We don't want to stop the process if there's a problem registering the interaction, so log it in a searchable way
            $message = "[AccountChange][PEGA] There was a problem registering a PEGA Interaction (impressionOfferId = $impressionOfferId, interaction = $interaction, actorId = $actorId)";
            $message .= " Error: ".$e->getMessage();
            $this->logError($message);
        }
    }

    /**
     * Returns a next best action client
     *
     * @return NextBestActionClient
     */
    protected function getNextBestActionClient()
    {
        return BusTier_BusTier::getClient('nextBestActionServiceClient');
    }

    /**
     * Logs an error message
     *
     * @param $message
     */
    protected function logError($message)
    {
        error_log($message);
    }

}

