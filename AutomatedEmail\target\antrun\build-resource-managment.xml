<?xml version="1.0" encoding="UTF-8"?>
<project name="maven-antrun-" default="resource-managment">
  <target unless="skipExecution" name="resource-managment">
    <mkdir dir="C:\local\codebase2005\modules\AutomatedEmail\target/logs" />
    <echo>Run Composer In Module</echo>
    <exec failonerror="true" dir="C:\local\codebase2005\modules\AutomatedEmail/" executable="composer" logerror="on">
      <arg line="install" />
    </exec>
    <echo>-------------</echo>
    <echo>Run Composer In Framework</echo>
    <exec failonerror="true" dir="/local/codebase2005/modules/Framework" executable="composer" logerror="on">
      <arg line="install" />
    </exec>
    <exec failonerror="true" dir="/local/codebase2005/modules/Framework/Install" executable="composer" logerror="on">
      <arg line="install" />
    </exec>
    <echo>-------------</echo>
    <echo>Generate Config Files</echo>
    <echo></echo>
    <exec failonerror="true" dir="/local/codebase2005/modules/Framework/Install" executable="php" logerror="on">
      <arg line="generateConfigFiles.php" />
    </exec>
    <echo>-------------</echo>
  </target>
</project>
