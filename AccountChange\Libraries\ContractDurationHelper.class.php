<?php

/**
 * <AUTHOR>
 */

class AccountChange_ContractDurationHelper
{
    const JLP_ISP = 'johnlewis';
    const JLP_CONTRACT_DURATION = 12;
    const DEFAULT_CONTRACT_DURATION_ADSL = 0;
    const DEFAULT_CONTRACT_DURATION_FIBRE = 18;

    private $dbAdaptor;
    private $fibreHelper;

    /**
     * @param Db_Adaptor                $dbAdaptor   the data source
     * @param AccountChange_FibreHelper $fibreHelper fibre helper
     */
    public function __construct(
        Db_Adaptor $dbAdaptor,
        AccountChange_FibreHelper $fibreHelper = null
    ) {
        $this->dbAdaptor = $dbAdaptor;
        $this->fibreHelper = $fibreHelper;
    }

    /**
     * @param int          $oldProductServiceDefinition old SDI
     * @param int          $newProductServiceDefinition new SDI
     * @param array        $applicationData             the application data from the account change
     * @param Core_Service $coreService                 core service
     *
     * @return boolean
     */
    public function shouldDisplayContractDuration(
        $oldProductServiceDefinition,
        $newProductServiceDefinition,
        array $applicationData,
        Core_Service $coreService
    ) {
        if (!$this->dbAdaptor->isBroadbandProduct($newProductServiceDefinition)) {
            return false;
        }

        $isOldProductFibre = $this->fibreHelper->isFibreProduct($oldProductServiceDefinition);
        $isNewProductFibre = $this->fibreHelper->isFibreProduct($newProductServiceDefinition);

        return (($coreService->isJohnLewisVispCustomer() || ($isOldProductFibre != $isNewProductFibre))
            && !isset($applicationData['selectedContractDuration']));
    }

    /**
     * Gets the contract duration for the account change journey
     *
     * @param Val_ISP $isp                 the isp object
     * @param int     $serviceDefinitionId the service definition ID
     * @param array   $applicationData     the application data from the account change
     *
     * @return int
     */
    public function getContractDuration(Val_ISP $isp, $serviceDefinitionId, array $applicationData)
    {
        if (!empty($applicationData['promoCode'])) {
            return $applicationData['intDiscountLength'];
        } else {
            if (!empty($applicationData['selectedContractDuration'])) {
                return $applicationData['selectedContractDuration'];
            } else {
                if (isset($applicationData['selectedContractDuration']) &&
                    $applicationData['selectedContractDuration'] === 0 ) {
                    return $applicationData['existingBroadband']['contractDuration'];
                } else {
                    return $this->getDefaultContractDuration($isp, $serviceDefinitionId);
                }
            }
        }
    }

    /**
     * Gets the contract duration options
     *
     * @param array $data                              The account change data
     * @param int   $serviceId                         The service ID
     * @param int   $serviceDefinition                 The service definition
     * @param int   $minimumContractDuration           The minimum contract duration
     * @param int   $overrideDefaultContractLengthWith If set, uses this value as the default selected option
     *
     * @return array
     */
    public function getContractDurationOptions($data, $serviceId, $serviceDefinition, $minimumContractDuration, $overrideDefaultContractLengthWith = 0)
    {
        $data['availableContractDurations'] = $this->dbAdaptor->getAvailableContractDurationsForProduct(
            $serviceDefinition,
            $minimumContractDuration
        );

        if ($this->isFibreProduct($serviceDefinition)) {
            $data['intDefaultContractDurationOption'] = self::DEFAULT_CONTRACT_DURATION_FIBRE;
        } else {
            $data['intDefaultContractDurationOption'] = self::DEFAULT_CONTRACT_DURATION_ADSL;
        }

        if ($overrideDefaultContractLengthWith > 0) {
            $data['intDefaultContractDurationOption'] = $overrideDefaultContractLengthWith;
        }

        $data['bolShowNoRecontractOption'] = $this->hasActiveContract($serviceId);

        return $data;
    }

    /**
     * Gets the default contract duration for the particular ISP
     *
     * @param Val_ISP $isp                 the ISP
     * @param int     $serviceDefinitionId the service definition ID
     *
     * @return int
     */
    private function getDefaultContractDuration(Val_ISP $isp, $serviceDefinitionId)
    {
        if ($isp->getISP() == self::JLP_ISP) {
            return self::JLP_CONTRACT_DURATION;
        } else {
            return $this->getContractDefinitionLength($serviceDefinitionId);
        }
    }

    /**
     * Gets the contract definition length from the database
     *
     * @param int $serviceDefinitionId the service definition ID
     *
     * @return int
     */
    private function getContractDefinitionLength($serviceDefinitionId)
    {
        return $this->dbAdaptor->getContractDefinitionLengthFromSdi($serviceDefinitionId);
    }

    /**
     * Checks if the customer has an active contract
     *
     * @param int $serviceId the account service ID
     *
     * @return bool
     */
    private function hasActiveContract($serviceId)
    {
        $contractsClient = BusTier_BusTier::getClient('contracts');
        $contractsClient->setServiceId($serviceId);

        return !empty($contractsClient->getContracts());
    }


    /**
     * Is the given product a fibre product
     *
     * @param int $serviceDefinitionId the service definition ID
     *
     * @return boolean
     */
    private function isFibreProduct($serviceDefinitionId)
    {
        return (new AccountChange_FibreHelper())->isFibreProduct($serviceDefinitionId);
    }
}
