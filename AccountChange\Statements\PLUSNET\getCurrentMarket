server: coredb
role: slave
rows: single
statement:

SELECT
	scm.intMarketId
FROM userdata.services s
INNER JOIN userdata.components c USING (service_id)
INNER JOIN products.tblServiceComponentProduct scp ON c.component_type_id = scp.intServiceComponentId
INNER JOIN products.tblServiceComponentProductType scpt USING (intServiceComponentProductTypeID)
INNER JOIN products.tblServiceComponentMarket scm ON scp.intServiceComponentId = scm.intServiceComponentId
WHERE
	scpt.vchHandle = 'INTERNET_CONNECTION'
	AND s.service_id = :intServiceId