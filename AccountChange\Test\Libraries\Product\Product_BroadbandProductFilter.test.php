<?php

/**
 * File Product_BroadbandProductFilter.test.php
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       http://confluence.internal.plus.net/display/PRI/Solus+and+Dual+Play+Product+Combinations
 */

/**
 * Class AccountChange_Product_BroadbandProductFilterTest
 *
 * Unit tests for AccountChange_Product_BroadbandProductFilter
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       http://confluence.internal.plus.net/display/PRI/Solus+and+Dual+Play+Product+Combinations
 */
class AccountChange_Product_BroadbandProductFilterTest extends PHPUnit_Framework_TestCase
{
    const MAX_FIBRE_EXTRA_SPEED = 80000;
    const MAX_FIBRE_SPEED = 40000;

    /**
     * @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_Legacy
     */
    private $legacyProductFamily;

    /**
     * @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_PlusnetSolus2013
     */
    private $solusCbcProductFamily;

    /**
     * @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_PlusnetSolus2013
     */
    private $solusUnlimitedProductFamily;

    /**
     * @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_PlusnetDualPlay2013
     */
    private $dualPlayCbcProductFamily;

    /**
     * @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_PlusnetDualPlay2013
     */
    private $dualPlayUnlimitedProductFamily;

    /**
     * @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_Res2012
     */
    private $falconProductFamily;

    /**
     * @var PHPUnit_Framework_MockObject_MockObject|\Plusnet\Feature\FeatureTogglePersistence
     */
    private $mockFeaturePersistence;

    public function setUp()
    {
        $this->mockFeaturePersistence = $this->getMockBuilder(\Plusnet\Feature\FeatureTogglePersistenceImpl::class)
            ->setMethods(['getToggleByName'])
            ->getMock();
        \Plusnet\Feature\FeatureToggleManager::setInstance($this->mockFeaturePersistence);
    }

    public function tearDown()
    {
        \Plusnet\Feature\FeatureToggleManager::reset();
    }

    /**
     * Tests for keep current product filter
     *
     * @return void
     */
    public function testFilterForKeepCurrentProductOnly()
    {
        $availableProducts = [
            $this->productSolusPlusnetEssentialsFibre(),
            $this->productSolusUnlimitedPlusnetUnlimitedFibre(),
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productLegacyPlusnetUnlimitedContracted(),
            $this->productDualPlayUnlimitedPlusnetUnlimitedFibreDualPlay(),
        ];

        $currentProduct = [
            'intSdi' => 6793,
            'productFamily' => $this->dualPlayCbcProductFamily,
            'hasPhone' => true,
            'isFibre' => false
        ];
        $expectedResult = [
            $this->productLegacyPlusnetUnlimitedContracted(),
        ];

        $lineCheckResult = $this->getMockLineCheckResult(self::MAX_FIBRE_EXTRA_SPEED, false);

        $mockFilter = $this->getMockBuilder(AccountChange_Product_BroadbandProductFilter_KeepCurrentProductOnly::class)
            ->setMethods(['getServiceDefinitionVariants'])
            ->disableOriginalConstructor()->getMock();

        $mockFilter->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue([]));

        $actualResult = $mockFilter->filter(
            $lineCheckResult,
            $availableProducts,
            $currentProduct
        );

        //Using array_values as we don't worry about the keys
        $this->assertEquals(array_values($expectedResult), array_values($actualResult));
    }

    /**
     * Tests for 'filter' method
     *
     * @param array $availableProducts Available products
     * @param ProductFamily_ProductFamily $currentProductFamily Current product family
     * @param bool $hasHomephone Has homephone
     * @param int $currentSdid Current service definition id
     * @param array $expectedResult Expected result
     * @param int $lineCheckMaxSpeed The result of the line check
     *
     * @dataProvider provideDataForFilterForWorkplaceTest
     *
     * @return void
     */
    public function testFilterForWorkplace(
        $availableProducts,
        $currentProductFamily,
        $hasHomephone,
        $currentSdid,
        $expectedResult,
        $lineCheckMaxSpeed
    )
    {
        $lineCheckResult = $this->getMockLineCheckResult($lineCheckMaxSpeed, true);

        /**
         * @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_BroadbandProductFilter_Workplace $mockFilter
         */
        $mockFilter = $this->getMockBuilder(AccountChange_Product_BroadbandProductFilter_Workplace::class)
            ->setMethods(['getServiceDefinitionVariants'])
            ->disableOriginalConstructor()->getMock();

        $mockFilter->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(['solus' => 6791]));

        $currentProduct = [
            'intSdi' => $currentSdid,
            'productFamily' => $currentProductFamily,
            'hasPhone' => $hasHomephone,
            'isp' => 'plus.net'
        ];

        $actualResult = $mockFilter->filter(
            $lineCheckResult,
            $availableProducts,
            $currentProduct
        );

        $this->assertEquals($expectedResult, $actualResult);
    }

    /**
     * Data provider for workplace test
     *
     * @return array
     */
    public function provideDataForFilterForWorkplaceTest()
    {
        $this->setupSolusCbcProductFamily();
        $this->setupDualPlayCbcProductFamily();
        $this->setupFalconProductFamily();
        $this->setupLegacyProductFamily();

        $availableProducts = [
            $this->productFalconPlusnetEssentialsContracted(),
            $this->productFalconPlusnetEssentialsFibre(),
            $this->productFalconPlusnetUnlimitedFibre(),
            $this->productSolusPlusnetEssentialsContracted(),
            $this->productSolusPlusnetEssentialsFibre(),
            $this->productSolusPlusnetUnlimitedFibre(),
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
            $this->productLegacyPlusnetUnlimited(),
            $this->productLegacyPlusnetPremium(),
            $this->productLegacyExtra(),
            $this->productLegacyPlusnetBroadbandYourWay(1),
            $this->productLegacyPlusnetBroadbandYourWay(2),
            $this->productLegacyPlusnetUnlimitedContracted(),
            $this->productSolusPlusnetFibreContracted(),
            $this->productDualPlayPlusnetFibreContracted(),
        ];

        /**
         * Expected products available for all solus customers (Falcon and legacy)
         * Solus products:
         *  Plusnet Essentials (Contracted)
         *  Plusnet Essentials Fibre
         *  Plusnet Unlimited Fibre
         * DualPlay products:
         *  Plusnet Essentials (Contracted) Dual Play
         *  Plusnet Essentials Fibre Dual Play
         *  Plusnet Unlimited Fibre Dual Play
         * Current solus product
         *  Plusnet Essentials (Contracted) - Falcon
         */
        $expectedResultForSolusCustomers = [
            'Solus' => [
                $this->productSolusPlusnetEssentialsContracted(),
                $this->productSolusPlusnetEssentialsFibre(),
                $this->productSolusPlusnetUnlimitedFibre()
            ],
            'DualPlay' => [
                $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
                $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
                $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
            ],
            'Current Solus Product' => [
                $this->productFalconPlusnetEssentialsContracted(),
            ]
        ];

        /**
         * Expected products available for all dual play customers (Falcon and legacy)
         * DualPlay products:
         *  Plusnet Essentials (Contracted) Dual Play
         *  Plusnet Essentials Fibre Dual Play
         *  Plusnet Unlimited Fibre Dual Play
         * Includes available dual play products only
         */
        $expectedResultForDualPlayCustomers = [
            'DualPlay' => [
                $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
                $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
                $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
            ],
        ];

        /**
         * Expected results for a customer with a solus customer only being offered dual play products
         * Current solus product is Plusnet Essentials (Contracted), SDI 6791
         */
        $expectedResultForDualPlayCustomersWithSolus = $expectedResultForDualPlayCustomers;
        $expectedResultForDualPlayCustomersWithSolus['Current Solus Product'] = [
            $this->productFalconPlusnetEssentialsContracted(),
        ];

        /**
         * Expected results for a legacy customer with phone
         * DualPlay products:
         *  Plusnet Essentials (Contracted) Dual Play
         *  Plusnet Essentials Fibre Dual Play
         *  Plusnet Unlimited Fibre Dual Play
         * Current solus product
         *  Plusnet Essentials (Contracted) - Falcon
         */
        $expectedResultForLegacyCustomersWithPhone = [
            'DualPlay' => [
                $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
                $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
                $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
            ],
            'Current Solus Product' => [
                $this->productFalconPlusnetEssentialsContracted(),
            ]
        ];

        /**
         * Expected results for a legacy customer without phone
         * Solus products:
         *  Plusnet Essentials (Contracted)
         *  Plusnet Essentials Fibre
         *  Plusnet Unlimited Fibre
         * DualPlay products:
         *  Plusnet Essentials (Contracted) Dual Play
         *  Plusnet Essentials Fibre Dual Play
         *  Plusnet Unlimited Fibre Dual Play
         * Current solus product
         *  Plusnet Essentials (Contracted) - Falcon
         */
        $expectedResultForLegacyCustomersWithoutPhone = [
            'Solus' => [
                $this->productSolusPlusnetEssentialsContracted(),
                $this->productSolusPlusnetEssentialsFibre(),
                $this->productSolusPlusnetUnlimitedFibre(),
            ],
            'DualPlay' => [
                $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
                $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
                $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
            ],
            'Current Solus Product' => [
                $this->productFalconPlusnetEssentialsContracted(),
            ]
        ];

        /**
         * Expected results for a Falcon customer with phone
         * Added DualPlay products
         *  PlusnetFibre (Contracted) 6867
         */
        $expectedResultForFalconCustomerWithPhone = $expectedResultForDualPlayCustomersWithSolus;
        $expectedResultForFalconCustomerWithPhone['DualPlay'][] = $this->productDualPlayPlusnetFibreContracted();


        /**
         * Expected results for a Falcon customer without phone
         * Added DualPlay products
         *  Plusnet Fibre (Contracted) 6867
         * Added Solus products
         *  Plusnet Fibre (Contracted) 6863
         */
        $expectedResultForFalconCustomerWithoutPhone = $expectedResultForSolusCustomers;
        $expectedResultForFalconCustomerWithoutPhone['DualPlay'][] = $this->productDualPlayPlusnetFibreContracted();
        $expectedResultForFalconCustomerWithoutPhone['Solus'][] = $this->productSolusPlusnetFibreContracted();


        /*
         * @param array                       $availableProducts    Available products
         * @param ProductFamily_ProductFamily $currentProductFamily Current product family
         * @param bool                        $hasHomephone         Has homephone
         * @param int                         $currentSdid          Current service definition id
         * @param array                       $expectedResult       Expected result
         * @param int                         $lineCheckMaxSpeed    The result of the line check
         */
        return [
            [
                $availableProducts,
                $this->dualPlayCbcProductFamily,
                true,
                '6793',
                $expectedResultForDualPlayCustomers,
                self::MAX_FIBRE_SPEED
            ],
            [
                $availableProducts,
                $this->solusCbcProductFamily,
                false,
                '6793',
                $expectedResultForSolusCustomers,
                39000
            ],
            [
                $availableProducts,
                $this->falconProductFamily,
                true,
                '6793',
                $expectedResultForFalconCustomerWithPhone,
                41000
            ],
            [
                $availableProducts,
                $this->falconProductFamily,
                false,
                '6793',
                $expectedResultForFalconCustomerWithoutPhone,
                self::MAX_FIBRE_EXTRA_SPEED
            ],
            [
                $availableProducts,
                $this->legacyProductFamily,
                true,
                '6793',
                $expectedResultForLegacyCustomersWithPhone,
                self::MAX_FIBRE_SPEED
            ],
            [
                $availableProducts,
                $this->legacyProductFamily,
                false,
                '6793',
                $expectedResultForLegacyCustomersWithoutPhone,
                self::MAX_FIBRE_SPEED
            ],
        ];
    }

    /**
     * Test filtering for house move account change
     *
     * @param array $availableProducts All available product
     * @param array $currentProduct User's current product
     * @param bool $wantPhone Do we want phone service at the new address?
     * @param array $expectedResult Expected result
     *
     * @return void
     *
     * @dataProvider provideDataForFilterForHouseMovesTest
     */
    public function testFilterForHouseMoves(
        array $availableProducts,
        array $currentProduct,
        $wantPhone,
        array $expectedResult
    )
    {
        $lineCheckResult = $this->getMockLineCheckResult(self::MAX_FIBRE_EXTRA_SPEED, true);

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_BroadbandProductFilter_Housemove $mockFilter */
        $mockFilter = $this->getMockBuilder(AccountChange_Product_BroadbandProductFilter_Housemove::class)
            ->setMethods(['getServiceDefinitionVariants'])
            ->disableOriginalConstructor()->getMock();

        $mockFilter->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will(
                $this->returnCallback(
                    function ($serviceDefinitionId) {
                        switch ($serviceDefinitionId) {
                            // ADSL Solus
                            case $this->productSolusPlusnetEssentialsContracted()['intSdi']:
                                return [
                                    'dualplay' => $this->productDualPlayPlusnetEssentialsContractedDualPlay()['intSdi']
                                ];
                            // ADSL Dual Play
                            case $this->productDualPlayPlusnetEssentialsContractedDualPlay()['intSdi']:
                                return [
                                    'solus' => $this->productSolusPlusnetEssentialsContracted()['intSdi']
                                ];
                            // FTTC Solus
                            case $this->productSolusPlusnetUnlimitedFibre()['intSdi']:
                                return [
                                    'dualplay' => $this->productDualPlayPlusnetUnlimitedFibreDualPlay()['intSdi']
                                ];
                            // FTTC Dual Play
                            case $this->productDualPlayPlusnetEssentialsFibreDualPlay()['intSdi']:
                                return [
                                    'solus' => $this->productSolusPlusnetEssentialsFibre()['intSdi']
                                ];
                            default:
                                return [];
                        }
                    }
                )
            );

        $mockFilter->setForceAllowSolusProducts(!$wantPhone);
        $mockFilter->setForceAllowDualPlayProducts($wantPhone);

        $actualResult = $mockFilter->filter($lineCheckResult, $availableProducts, $currentProduct);

        $this->assertEquals($expectedResult, $actualResult);
    }

    /**
     * Data provider for house moves testing
     *
     * @return array
     */
    public function provideDataForFilterForHouseMovesTest()
    {
        $this->setUpProductFamilies();

        /*
         * Covers:
         * 0.  ADSL Solus
         * 1.  ADSL Solus -> dual
         * 2.  ADSL Dual
         * 3.  ADSL Dual -> solus
         * 4.  Fibre solus
         * 5.  Fibre solus -> dual
         * 6.  Fibre dual
         * 7.  Fibre dual -> solus
         * 8.  Legacy solus
         * 9.  Legacy solus -> dual
         * 10. Legacy dual
         * 11. Legacy dual -> solus
         *
         * To add/remove a phone package, the HouseMoves application will first replace the current product with its
         * dual play/solus equivalent. This is the responsibility of HouseMoves, and so is not tested here
         *
         */

        $adslSolusProducts = [
            $this->productSolusPlusnetEssentialsContracted(),
        ];

        $adslDualPlayProducts = [
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
        ];

        $fttcSolusProducts = [
            $this->productSolusPlusnetEssentialsFibre(),
            $this->productSolusPlusnetFibreContracted(),
            $this->productSolusPlusnetUnlimitedFibre(),
        ];

        $fttcDualPlayProducts = [
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayPlusnetFibreContracted(),
            $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
        ];

        $legacySolusProducts = [
            $this->productLegacyExtra(),
            $this->productLegacyPlusnetBroadbandYourWay(1),
            $this->productLegacyPlusnetBroadbandYourWay(2),
            $this->productLegacyPlusnetPremium(),
            $this->productLegacyPlusnetUnlimited(),
            $this->productLegacyPlusnetUnlimitedContracted(),
        ];

        $legacyDualPlayProducts = [
            $this->productLegacyDualPlayLegacyBroadband(),
        ];

        $allProducts = array_merge(
            $adslSolusProducts,
            $adslDualPlayProducts,
            $fttcSolusProducts,
            $fttcDualPlayProducts,
            $legacySolusProducts,
            $legacyDualPlayProducts
        );

        /*
         * @param array $availableProducts
         * @param array $currentProduct
         * @param bool  $wantPhone
         * @param array $expectedResult
         */
        return [
            // ADSL Solus 0-1
            [
                $allProducts,
                $this->productSolusPlusnetEssentialsContracted(),
                false,
                [
                    'Solus' => array_merge($adslSolusProducts, $fttcSolusProducts),
                ]
            ],
            [
                $allProducts,
                $this->productSolusPlusnetEssentialsContracted(),
                true,
                [
                    'DualPlay' => $fttcDualPlayProducts, // ADSL DP is current DP product
                    'Current DualPlay Product' => [$this->productDualPlayPlusnetEssentialsContractedDualPlay()],
                ]
            ],

            // ADSL dual play 2-3
            [
                $allProducts,
                $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
                true,
                [
                    'DualPlay' => array_merge($adslDualPlayProducts, $fttcDualPlayProducts),
                ]
            ],
            [
                $allProducts,
                $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
                false,
                [
                    'Solus' => $fttcSolusProducts,
                    'Current Solus Product' => [$this->productSolusPlusnetEssentialsContracted()]
                ]
            ],

            // FTTC Solus 4-5
            [
                $allProducts,
                $this->productSolusPlusnetUnlimitedFibre(),
                false,
                [
                    'Solus' => array_merge($adslSolusProducts, $fttcSolusProducts),
                ]
            ],
            [
                $allProducts,
                $this->productSolusPlusnetUnlimitedFibre(),
                true,
                [
                    'DualPlay' => [
                        $this->productDualPlayPlusnetEssentialsContractedDualPlay(),

                        $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
                        $this->productDualPlayPlusnetFibreContracted(),
                        // $this->productDualPlayPlusnetUnlimitedFibreDualPlay(), - Not this one
                    ],

                    'Current DualPlay Product' => [
                        $this->productDualPlayPlusnetUnlimitedFibreDualPlay()
                    ],
                ]
            ],

            // FTTC dual play 6-7
            [
                $allProducts,
                $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
                true,
                [
                    // No solus
                    'DualPlay' => array_merge($adslDualPlayProducts, $fttcDualPlayProducts),
                ]
            ],
            [
                $allProducts,
                $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
                false,
                [
                    'Solus' => [
                        $this->productSolusPlusnetEssentialsContracted(),

                        // $this->productSolusPlusnetEssentialsFibre(), - Not this one
                        $this->productSolusPlusnetFibreContracted(),
                        $this->productSolusPlusnetUnlimitedFibre(),
                    ],

                    'Current Solus Product' => [$this->productSolusPlusnetEssentialsFibre()],
                ]
            ],

            // Legacy solus 8-9
            [
                $allProducts,
                $this->productLegacyPlusnetBroadbandYourWay(1),
                false,
                [
                    'Solus' => array_merge($adslSolusProducts, $fttcSolusProducts),
                    'Current Product' => [$this->productLegacyPlusnetBroadbandYourWay(1)],
                ]
            ],
            [
                $allProducts,
                $this->productLegacyPlusnetBroadbandYourWay(1),
                true,
                [
                    'DualPlay' => array_merge($adslDualPlayProducts, $fttcDualPlayProducts),
                    'Current Product' => [$this->productLegacyPlusnetBroadbandYourWay(1)],
                ]
            ],

            // Legacy dual play 10-11
            [
                $allProducts,
                $this->productLegacyDualPlayLegacyBroadband(),
                true,
                [
                    'DualPlay' => array_merge($adslDualPlayProducts, $fttcDualPlayProducts),
                    'Current Product' => [$this->productLegacyDualPlayLegacyBroadband()]
                ]
            ],
            [
                $allProducts,
                $this->productLegacyDualPlayLegacyBroadband(),
                true,
                [
                    'DualPlay' => array_merge($adslDualPlayProducts, $fttcDualPlayProducts),
                    'Current Product' => [$this->productLegacyDualPlayLegacyBroadband()]
                ]
            ],
        ];
    }

    /**
     * Tests for 'filter' method
     *
     * @param array $availableProducts Available products
     * @param array $currentProduct Current service definition id
     * @param array $expectedResult Expected result
     *
     * @dataProvider provideDataForFilterForPortalTest
     *
     * @return void
     */
    public function testFilterForPortal(array $availableProducts, array $currentProduct, array $expectedResult)
    {
        $this->mockFeaturePersistence->expects($this->any())
            ->method('getToggleByName')
            ->willReturnCallback(function ($toggleName) {
                if ($toggleName === \Plusnet\Feature\FeatureToggleManager::REMOVE_PLUSNET_RES_SOLUS) {
                    return [
                        'onDateTime' => date('Y-m-d', strtotime('+1 year')),
                        'offDateTime' => date('Y-m-d', strtotime('+2 year'))
                    ];
                }

                return null;
            });

        $lineCheckResult = $this->getMockLineCheckResult(self::MAX_FIBRE_EXTRA_SPEED, false);

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_BroadbandProductFilter_Portal $mockFilter */
        $mockFilter = $this->getMockBuilder(AccountChange_Product_BroadbandProductFilter_Portal::class)
            ->setMethods(['getServiceDefinitionVariants', 'getLineCheck'])
            ->disableOriginalConstructor()->getMock();

        $mockFilter->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(['solus' => 6791]));

        $actualResult = $mockFilter->filter(
            $lineCheckResult,
            $availableProducts,
            $currentProduct
        );

        //Using array_values as we don't worry about the keys
        $this->assertEquals(array_values($expectedResult), array_values($actualResult));
    }

    /**
     * Tests for 'filter' method
     *
     * @param array $availableProducts Available products
     * @param array $currentProduct Current service definition id
     * @param array $expectedResult Expected result
     *
     * @dataProvider provideDataForFilterForPortalWithSolusRemovedTest
     *
     * @return void
     */
    public function testFilterForPortalWithSolusRemoved(
        array $availableProducts,
        array $currentProduct,
        array $expectedResult
    ) {
        $this->mockFeaturePersistence->expects($this->any())
            ->method('getToggleByName')
            ->willReturnCallback(function () {
                return null;
            });

        $lineCheckResult = $this->getMockLineCheckResult(self::MAX_FIBRE_EXTRA_SPEED, false);

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_BroadbandProductFilter_Portal $mockFilter */
        $mockFilter = $this->getMockBuilder(AccountChange_Product_BroadbandProductFilter_Portal::class)
            ->setMethods(['getServiceDefinitionVariants', 'getLineCheck'])
            ->disableOriginalConstructor()->getMock();

        $mockFilter->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(['solus' => 6791]));

        $actualResult = $mockFilter->filter(
            $lineCheckResult,
            $availableProducts,
            $currentProduct
        );

        //Using array_values as we don't worry about the keys
        $this->assertEquals(array_values($expectedResult), array_values($actualResult));
    }

    /**
     * Data provider for portal test
     *
     * @return array
     */
    public function provideDataForFilterForPortalTest()
    {
        $data = [];

        $this->setUpProductFamilies();

        $availableProducts = [
            $this->productSolusPlusnetEssentialsFibre(),
            $this->productSolusUnlimitedPlusnetUnlimitedFibre(),
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayUnlimitedPlusnetUnlimitedFibreDualPlay(),
        ];

        // Data set 0
        /**
         * Expected results for a customer with phone
         *  Plusnet Essentials (Contracted) Dual Play 6827
         *  Plusnet Essentials Fibre Dual Play 6831
         *  Plusnet Unlimited Fibre Dual Play 6832
         */
        $expectedResultForCustomersWithPhone = [
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
        ];

        $currentProduct = [
            'intSdi' => 6793,
            'productFamily' => $this->dualPlayCbcProductFamily,
            'hasPhone' => true,
            'isFibre' => false
        ];

        $data[] = [$availableProducts, $currentProduct, $expectedResultForCustomersWithPhone];

        // Data set 1
        /**
         * Expected result for unlimited ADSL customers - all available products
         */
        $expectedResultForUnlimitedAdslCustomers = $expectedResultForCustomersWithPhone;

        $currentProduct = [
            'intSdi' => 6794,
            'productFamily' => $this->solusUnlimitedProductFamily,
            'hasPhone' => false,
            'isFibre' => false
        ];

        $data[] = [$availableProducts, $currentProduct, $expectedResultForUnlimitedAdslCustomers];

        return $data;
    }

    /**
     * Data provider for portal test
     *
     * @return array
     */
    public function provideDataForFilterForPortalWithSolusRemovedTest()
    {
        $data = [];

        $this->setUpProductFamilies();

        $availableProducts = [
            $this->productSolusPlusnetEssentialsFibre(),
            $this->productSolusUnlimitedPlusnetUnlimitedFibre(),
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayUnlimitedPlusnetUnlimitedFibreDualPlay(),
        ];

        $expectedResultForCustomersWithDualPlayAdsl = [
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
        ];

        $currentProduct = [
            'intSdi' => 6793,
            'productFamily' => $this->dualPlayCbcProductFamily,
            'hasPhone' => true,
            'isFibre' => false
        ];

        $data['customersWithDualPlayAdsl'] = [$availableProducts, $currentProduct, $expectedResultForCustomersWithDualPlayAdsl];

        $expectedResultForCustomersWithSolusWhosPackageIsStillAvailable = [
            $this->productSolusUnlimitedPlusnetUnlimitedFibre(),
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
        ];

        $currentProduct = [
            'intSdi' => 6826,
            'productFamily' => $this->solusUnlimitedProductFamily,
            'hasPhone' => false,
            'isFibre' => false
        ];

        $data['customersWithSolusWhosPackageIsStillAvailable'] = [$availableProducts, $currentProduct, $expectedResultForCustomersWithSolusWhosPackageIsStillAvailable];

        $expectedResultForUnlimitedFibreSolusCustomers = [
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayPlusnetUnlimitedFibreDualPlay(),
        ];

        $currentProduct = [
            'intSdi' => 6794,
            'productFamily' => $this->solusUnlimitedProductFamily,
            'hasPhone' => false,
            'isFibre' => true
        ];

        $data['unlimitedFibreSolusCustomers'] = [$availableProducts, $currentProduct, $expectedResultForUnlimitedFibreSolusCustomers];

        $expectedResultForUnlimitedAdslSolusCustomers = [
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
            $this->productDualPlayUnlimitedPlusnetUnlimitedFibreDualPlay(),
        ];

        $currentProduct = [
            'intSdi' => 6794,
            'productFamily' => $this->solusUnlimitedProductFamily,
            'hasPhone' => false,
            'isFibre' => false
        ];

        $data['unlimitedAdslSolusCustomers'] = [$availableProducts, $currentProduct, $expectedResultForUnlimitedAdslSolusCustomers];

        return $data;
    }

    /**
     * Test forcing dual play & solus to be displayed or hidden
     *
     * @return void
     */
    function testApplyForcedFilters()
    {
        $this->setUpProductFamilies();

        $lineCheckResult = $this->getMockLineCheckResult(self::MAX_FIBRE_EXTRA_SPEED, true);

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_BroadbandProductFilter_Housemove $mockFilter */
        $mockFilter = $this->getMockBuilder(AccountChange_Product_BroadbandProductFilter_Workplace::class)
            ->setMethods(['getServiceDefinitionVariants'])
            ->disableOriginalConstructor()->getMock();

        $mockFilter->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue([]));

        // The current product and variants are NOT included here, so we don't get the current product variant groups
        // TODO: However, this triggers a notice when running the test. Would be nice to fix this.
        $availableProducts = [
            $this->productSolusPlusnetEssentialsContracted(),
            $this->productDualPlayPlusnetEssentialsContractedDualPlay(),
            $this->productSolusPlusnetEssentialsFibre(),
            $this->productDualPlayPlusnetEssentialsFibreDualPlay(),
        ];

        // Important that this is dual play - by default Solus will NOT be offered
        $currentProduct = $this->productDualPlayPlusnetUnlimitedFibreDualPlay();

        $actualResult = $mockFilter->filter($lineCheckResult, $availableProducts, $currentProduct);

        // First, test without setting any limits. Should allow all dual play products, no solus.
        // Don't care about the current product
        $this->assertArrayNotHasKey("Solus", $actualResult);
        $this->assertArrayHasKey("DualPlay", $actualResult);
        $this->assertCount(2, $actualResult['DualPlay']);

        // Disallow dual play
        $mockFilter->setForceAllowDualPlayProducts(false);
        $actualResult = $mockFilter->filter($lineCheckResult, $availableProducts, $currentProduct);
        $this->assertArrayNotHasKey("Solus", $actualResult);
        $this->assertArrayNotHasKey("DualPlay", $actualResult);

        // Disallow solus too
        $mockFilter->setForceAllowSolusProducts(false);
        $actualResult = $mockFilter->filter($lineCheckResult, $availableProducts, $currentProduct);
        $this->assertArrayNotHasKey("Solus", $actualResult);
        $this->assertArrayNotHasKey("DualPlay", $actualResult);

        // Require solus
        $mockFilter->setForceAllowSolusProducts(true);
        $actualResult = $mockFilter->filter($lineCheckResult, $availableProducts, $currentProduct);
        $this->assertArrayHasKey("Solus", $actualResult);
        $this->assertArrayNotHasKey("DualPlay", $actualResult);

        // Require dual play
        $mockFilter->setForceAllowDualPlayProducts(true);
        $actualResult = $mockFilter->filter($lineCheckResult, $availableProducts, $currentProduct);
        $this->assertArrayHasKey("Solus", $actualResult);
        $this->assertArrayHasKey("DualPlay", $actualResult);
        $this->assertCount(2, $actualResult['Solus']);
        $this->assertCount(2, $actualResult['DualPlay']);

        // Now remove the limits
        $mockFilter->setForceAllowDualPlayProducts(null);
        $mockFilter->setForceAllowSolusProducts(null);
        $actualResult = $mockFilter->filter($lineCheckResult, $availableProducts, $currentProduct);
        $this->assertArrayNotHasKey("Solus", $actualResult);
        $this->assertArrayHasKey("DualPlay", $actualResult);
    }

    /*
     * Product definitions used by unit tests
     * Ideally these would be stored as data outside the test code, like in InventoryEventClient,
     * but I don't think it's worth spending the time on that now.
     */

    /**
     * Plusnet Essentials (Contracted) on solus CBC product family
     *
     * @return array
     */
    private function productSolusPlusnetEssentialsContracted()
    {
        return [
            'intSdi' => '00006821',
            'strProductName' => 'Plusnet Essentials (Contracted)',
            'intTariffID' => '1791',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->solusCbcProductFamily,
            'vchProductFamily' => 'PLUSNETSOLUS',
        ];
    }

    /**
     * Plusnet Essentials (Contracted) on Falcon product family
     *
     * @return array
     */
    private function productFalconPlusnetEssentialsContracted()
    {
        return [
            'intSdi' => '00006791',
            'strProductName' => 'Plusnet Essentials (Contracted)',
            'isFibre' => false,
            'maxDownloadSpeed' => 7.5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'intTariffID' => '1225',
            'strContract' => 'MONTHLY',
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => '12MONTH',
            'intContractLengthMonths' => '12',
            'productFamily' => $this->falconProductFamily,
            'vchProductFamily' => 'PNRESRPR12'
        ];
    }

    /**
     * Plusnet Essentials Fibre on solus CBC product family
     *
     * @return array
     */
    private function productSolusPlusnetEssentialsFibre()
    {
        return [
            'intSdi' => '00006825',
            'strProductName' => 'Plusnet Essentials Fibre',
            'intTariffID' => '1799',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->solusCbcProductFamily,
            'vchProductFamily' => 'PLUSNETSOLUS',
        ];
    }

    /**
     * Plusnet Essentials Fibre on Falcon product family
     *
     * @return array
     */
    private function productFalconPlusnetEssentialsFibre()
    {
        return [
            'intSdi' => '00006795',
            'strProductName' => 'Plusnet Essentials Fibre',
            'intTariffID' => '1233',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => true,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => '18MONTH',
            'intContractLengthMonths' => '18',
            'maxDownloadSpeed' => 40,
            'maxUploadSpeed' => 4,
            'activationFee' => '25.00',
            'productFamily' => $this->falconProductFamily,
            'vchProductFamily' => 'PNRESRPR12',
        ];
    }

    /**
     * Plusnet Unlimited Fibre on solus CBC product family
     * (That combination was on the original unit test)
     *
     * @return array
     */
    private function productSolusPlusnetUnlimitedFibre()
    {
        return [
            'intSdi' => '00006826',
            'strProductName' => 'Plusnet Unlimited Fibre',
            'intTariffID' => '1801',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->solusCbcProductFamily,
            'vchProductFamily' => 'PLUSNETSOLUS',
        ];
    }

    /**
     * Plusnet Unlimited Fibre on solus unlimited product family
     *
     * @return array
     */
    private function productSolusUnlimitedPlusnetUnlimitedFibre()
    {
        return [
            'intSdi' => '00006826',
            'strProductName' => 'Plusnet Unlimited Fibre',
            'intTariffID' => '1801',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->solusUnlimitedProductFamily,
            'vchProductFamily' => 'PLUSNETSOLUS',
        ];
    }

    /**
     * Plusnet Unlimited Fibre on Falcon product family
     *
     * @return array
     */
    private function productFalconPlusnetUnlimitedFibre()
    {
        return [
            'intSdi' => '00006796',
            'strProductName' => 'Plusnet Unlimited Fibre',
            'intTariffID' => '1235',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => true,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => '18MONTH',
            'intContractLengthMonths' => '18',
            'maxDownloadSpeed' => 40,
            'maxUploadSpeed' => 4,
            'activationFee' => '25.00',
            'productFamily' => $this->falconProductFamily,
            'vchProductFamily' => 'PNRESRPR12',
        ];
    }

    /**
     * Plusnet Essentials (Contracted) Dual Play on dual play CBC product family
     *
     * @return array
     */
    private function productDualPlayPlusnetEssentialsContractedDualPlay()
    {
        return [
            'intSdi' => '00006827',
            'strProductName' => 'Plusnet Essentials (Contracted) Dual Play',
            'intTariffID' => '1803',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => true,
            'hasPhone' => true,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->dualPlayCbcProductFamily,
            'vchProductFamily' => 'PLUSNETDUALPLAY',
        ];
    }

    /**
     * Plusnet Essentials Fibre Dual Play on dual play CBC product family
     *
     * @return array
     */
    private function productDualPlayPlusnetEssentialsFibreDualPlay()
    {
        return [
            'intSdi' => '00006831',
            'strProductName' => 'Plusnet Essentials Fibre Dual Play',
            'intTariffID' => '1811',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => true,
            'hasPhone' => true,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->dualPlayCbcProductFamily,
            'vchProductFamily' => 'PLUSNETDUALPLAY',
        ];
    }

    /**
     * Plusnet Unlimited Fibre Dual Play on dual play product family
     * (Unlimited on CBC product family was on the original unit test)
     *
     * @return array
     */
    private function productDualPlayPlusnetUnlimitedFibreDualPlay()
    {
        return [
            'intSdi' => '00006832',
            'strProductName' => 'Plusnet Unlimited Fibre Dual Play',
            'intTariffID' => '1813',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => true,
            'hasPhone' => true,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->dualPlayCbcProductFamily,
            'vchProductFamily' => 'PLUSNETDUALPLAY',
        ];
    }

    /**
     * Plusnet Unlimited Fibre Dual Play on dual play unlimited product family
     *
     * @return array
     */
    private function productDualPlayUnlimitedPlusnetUnlimitedFibreDualPlay()
    {
        return [
            'intSdi' => '00006832',
            'strProductName' => 'Plusnet Unlimited Fibre Dual Play',
            'intTariffID' => '1813',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => true,
            'hasPhone' => true,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->dualPlayUnlimitedProductFamily,
            'vchProductFamily' => 'PLUSNETDUALPLAY',
        ];
    }

    /**
     * Plusnet Fibre (Contracted) on dual play CBC product family
     *
     * @return array
     */
    private function productDualPlayPlusnetFibreContracted()
    {
        return [
            'intSdi' => '00006867',
            'strProductName' => 'Plusnet Fibre (Contracted)',
            'intTariffID' => '1791',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => true,
            'isDual' => true,
            'hasPhone' => true,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->dualPlayCbcProductFamily,
            'vchProductFamily' => 'PLUSNETDUALPLAY'
        ];
    }

    /**
     * Plusnet Fibre (Contracted) on solud CBC product family
     *
     * @return array
     */
    private function productSolusPlusnetFibreContracted()
    {
        return [
            'intSdi' => '00006863',
            'strProductName' => 'Plusnet Fibre (Contracted)',
            'intTariffID' => '1791',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => true,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->solusCbcProductFamily,
            'vchProductFamily' => 'PLUSNETSOLUS'
        ];
    }

    /**
     * Plusnet Unlimited on legacy product family
     *
     * @return array
     */
    private function productLegacyPlusnetUnlimited()
    {
        return [
            'intSdi' => '00006719',
            'strProductName' => 'Plusnet Unlimited',
            'intTariffID' => '707',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 7.5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->legacyProductFamily,
            'vchProductFamily' => 'VALUE',
        ];
    }

    /**
     * Plusnet Unlimited (Contracted) on legacy product family
     *
     * @return array
     */
    private function productLegacyPlusnetUnlimitedContracted()
    {
        return [
            'intSdi' => '00006793',
            'strProductName' => 'Plusnet Unlimited (Contracted)',
            'isFibre' => false,
            'maxDownloadSpeed' => 5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'intTariffID' => '1230',
            'intCostIncVatPence' => '999',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'productFamily' => $this->legacyProductFamily,
            'hasPhone' => false,
        ];
    }


    /**
     * Plusnet Premium on legacy product family
     *
     * @return array
     */
    private function productLegacyPlusnetPremium()
    {
        return [
            'intSdi' => '00006751',
            'strProductName' => 'Plusnet Premium',
            'intTariffID' => '513',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => 'MONTHLY',
            'intContractLengthMonths' => 1,
            'maxDownloadSpeed' => 7.5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->legacyProductFamily,
            'vchProductFamily' => 'VALUE',
        ];
    }

    /**
     * Extra on legacy product family
     *
     * @return array
     */
    private function productLegacyExtra()
    {
        return [
            'intSdi' => '00006754',
            'strProductName' => 'Extra',
            'intTariffID' => '522',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => '12MONTH',
            'intContractLengthMonths' => '12',
            'maxDownloadSpeed' => 7.5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->legacyProductFamily,
            'vchProductFamily' => 'VALUE',
        ];
    }

    /**
     * Plusnet Broadband Your Way on legacy product family
     *
     * @param int $option Option number for BBYW. Currently supports 1 or 2
     *
     * @return array
     */
    private function productLegacyPlusnetBroadbandYourWay($option)
    {
        $sdi = 0;
        if ($option == 1) {
            $sdi = '00006629';
        } elseif ($option == 2) {
            $sdi = '00006630';
        }

        return [
            'intSdi' => $sdi,
            'strProductName' => sprintf('PlusNet Broadband Your Way Option %d (Monthly Contract)', $option),
            'intTariffID' => null,
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => false,
            'hasPhone' => false,
            'defaultContractLength' => '12MONTH',
            'intContractLengthMonths' => '12',
            'maxDownloadSpeed' => 7.5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->legacyProductFamily,
        ];
    }

    private function productLegacyDualPlayLegacyBroadband()
    {
        // This is NOT based on a real product
        return [
            'intSdi' => '00009999',
            'strProductName' => 'Dual Play Legacy Broadband',
            'intTariffID' => '564',
            'strContract' => 'MONTHLY',
            'strContractLength' => 'Monthly',
            'isFibre' => false,
            'isDual' => true,
            'hasPhone' => true,
            'defaultContractLength' => '12MONTH',
            'intContractLengthMonths' => '12',
            'maxDownloadSpeed' => 7.5,
            'maxUploadSpeed' => null,
            'activationFee' => null,
            'productFamily' => $this->legacyProductFamily,
            'vchProductFamily' => 'VALUE',
        ];
    }

    /*
     * Product families used by unit tests
     * Ideally these would be stored as data outside the test code, like in InventoryEventClient,
     * but I don't think it's worth spending the time on that now.
     */

    /**
     * Sets up all the product families.
     *
     * This is NOT a PHPUnit setUp method: it needs to be run before the dataProviders.
     * PHPUnit runs the setUp method after dataProviders but before tests.
     *
     * @return void
     */
    public function setUpProductFamilies()
    {
        $this->setupSolusCbcProductFamily();
        $this->setupDualPlayCbcProductFamily();
        $this->setupFalconProductFamily();
        $this->setupLegacyProductFamily();
        $this->setupSolusUnlimitedProductFamily();
        $this->setupDualPlayUnlimitedProductFamily();
    }

    /**
     * Sets up a legacy product family
     *
     * @return void
     */
    private function setupLegacyProductFamily()
    {
        $this->legacyProductFamily = $this->getMockBuilder(ProductFamily_Value::class)
            ->setMethods([
                'getProductFamilyHandle',
                'isResidentialLegacy',
                'isSolus',
                'isDualPlay',
                'isJohnLewis',
                'isFalcon'
            ])
            ->disableOriginalConstructor()->getMock();

        $this->legacyProductFamily->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue(ProductFamily_Value::PRODUCT_FAMILY_HANDLE));

        $this->legacyProductFamily->expects($this->any())
            ->method('isResidentialLegacy')
            ->will($this->returnValue(true));

        $this->legacyProductFamily->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue(false));

        $this->legacyProductFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        $this->legacyProductFamily->expects($this->any())
            ->method('isJohnLewis')
            ->will($this->returnValue(false));

        $this->legacyProductFamily->expects($this->any())
            ->method('isFalcon')
            ->will($this->returnValue(false));
    }

    /**
     * Solus product family with CBC
     *
     * @return void
     */
    private function setupSolusCbcProductFamily()
    {
        $this->solusCbcProductFamily = $this->getSolusProductFamily();

        $this->solusCbcProductFamily->expects($this->any())
            ->method('isCbcProduct')
            ->will($this->returnValue(true));
    }

    /**
     * Solus product family with unlimited usage
     *
     * @return void
     */
    private function setupSolusUnlimitedProductFamily()
    {
        $this->solusUnlimitedProductFamily = $this->getSolusProductFamily();

        $this->solusUnlimitedProductFamily->expects($this->any())
            ->method('isCbcProduct')
            ->will($this->returnValue(false));
    }

    /**
     * Common definition of a solus product family, shared between CBC and unlimited versions
     *
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_PlusnetSolus2013
     */
    private function getSolusProductFamily()
    {
        $solusProductFamily = $this->getMockBuilder(ProductFamily_PlusnetSolus2013::class)
            ->setMethods([
                'getProductFamilyHandle',
                'isResidentialLegacy',
                'isSolus',
                'isDualPlay',
                'isJohnLewis',
                'isFalcon',
                'isCbcProduct',
            ])
            ->disableOriginalConstructor()->getMock();

        $solusProductFamily->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue(ProductFamily_PlusnetSolus2013::PRODUCT_FAMILY_HANDLE));

        $solusProductFamily->expects($this->any())
            ->method('isResidentialLegacy')
            ->will($this->returnValue(false));

        $solusProductFamily->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue(true));

        $solusProductFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        $solusProductFamily->expects($this->any())
            ->method('isJohnLewis')
            ->will($this->returnValue(false));

        $solusProductFamily->expects($this->any())
            ->method('isFalcon')
            ->will($this->returnValue(false));

        return $solusProductFamily;
    }

    /**
     * Dual play product family with CBC
     *
     * @return void
     */
    private function setupDualPlayCbcProductFamily()
    {
        $this->dualPlayCbcProductFamily = $this->getDualPlayProductFamily();

        $this->dualPlayCbcProductFamily->expects($this->any())
            ->method('isCbcProduct')
            ->will($this->returnValue(true));
    }

    /**
     * Dual play product family with unlimited usage
     *
     * @return void
     */
    private function setupDualPlayUnlimitedProductFamily()
    {
        $this->dualPlayUnlimitedProductFamily = $this->getDualPlayProductFamily();

        $this->dualPlayUnlimitedProductFamily->expects($this->any())
            ->method('isCbcProduct')
            ->will($this->returnValue(false));
    }

    /**
     * Common definition of a dual play product family, shared between CBC and unlimited versions
     *
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_PlusnetDualPlay2013
     */
    private function getDualPlayProductFamily()
    {
        $dualPlayProductFamily = $this->getMockBuilder(ProductFamily_PlusnetDualPlay2013::class)
            ->setMethods([
                'getProductFamilyHandle',
                'isResidentialLegacy',
                'isSolus',
                'isDualPlay',
                'isJohnLewis',
                'isFalcon',
                'isCbcProduct',
            ])
            ->disableOriginalConstructor()->getMock();

        $dualPlayProductFamily->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue(ProductFamily_PlusnetDualPlay2013::PRODUCT_FAMILY_HANDLE));

        $dualPlayProductFamily->expects($this->any())
            ->method('isResidentialLegacy')
            ->will($this->returnValue(false));

        $dualPlayProductFamily->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue(false));

        $dualPlayProductFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $dualPlayProductFamily->expects($this->any())
            ->method('isJohnLewis')
            ->will($this->returnValue(false));

        $dualPlayProductFamily->expects($this->any())
            ->method('isFalcon')
            ->will($this->returnValue(false));

        return $dualPlayProductFamily;
    }

    /**
     * Falcon product family
     *
     * @return void
     */
    private function setupFalconProductFamily()
    {
        $this->falconProductFamily = $this->getMockBuilder(ProductFamily_Res2012::class)
            ->setMethods([
                'getProductFamilyHandle',
                'isResidentialLegacy',
                'isSolus',
                'isDualPlay',
                'isJohnLewis',
                'isFalcon'
            ])
            ->disableOriginalConstructor()->getMock();

        $this->falconProductFamily->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue(ProductFamily_Res2012::PRODUCT_FAMILY_HANDLE));

        $this->falconProductFamily->expects($this->any())
            ->method('isResidentialLegacy')
            ->will($this->returnValue(false));

        $this->falconProductFamily->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue(false));

        $this->falconProductFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        $this->falconProductFamily->expects($this->any())
            ->method('isJohnLewis')
            ->will($this->returnValue(false));

        $this->falconProductFamily->expects($this->any())
            ->method('isFalcon')
            ->will($this->returnValue(true));
    }

    /**
     * @return PHPUnit_Framework_MockObject_MockObject|LineCheck_Result
     */
    private function getMockLineCheckResult($speed, $isValid)
    {
        $lineCheckResult = $this->getMockBuilder(LineCheck_Result::class)
            ->setMethods(['getFttcUncappedDownSpeed', 'isValidResult'])
            ->getMock();

        $lineCheckResult->expects($this->any())
            ->method('getFttcUncappedDownSpeed')
            ->will($this->returnValue($speed));

        $lineCheckResult->expects($this->any())
            ->method('isValidResult')
            ->will($this->returnValue($isValid));

        return $lineCheckResult;
    }
}
