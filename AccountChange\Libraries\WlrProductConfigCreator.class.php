<?php
/**
 * Wrapper class for terms and conditions
 *
 * @package AccountChange
 *
 * <AUTHOR> <<EMAIL>>
 */
/**
 * A wrapper class for terms and conditions to make it think we are a Mvc application
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */


class AccountChange_WlrProductConfigCreator implements Mvc_ApplicationStateCallback
{
    /**
     * The core service
     *
     * @var Core_Service
     */
    private $objCoreService;

    /**
     * Wlr product details array
     *
     * @var Array
     */
    private $arrWlrProduct;

    /**
     * scheduled?
     *
     * @var bool
     */
    private $bolSchedule;

    /**
     * business actor
     *
     * @var Auth_BusinessActor
     */
    private $objBusinessActor;

    /**
     * new wlr id
     *
     * @var int
     */
    private $intNewWlrId;

    /**
     * line check results
     *
     * @var LineCheck_Result
     */
    private $objLineCheckResults;

    /**
     * reset contract wlr
     *
     * @var bool
     */
    private $bolContractResetWlr;

    /**
     * the wlr context
     *
     * @var string
     */
    private $strWlrContext;

    /**
     * the old sdi
     *
     * @var int
     */
    private $intOldSdi;

    /**
     * the new sdi
     *
     * @var int
     */
    private $intNewSdi;

    /**
     * The terms and conditions we are wrapping
     *
     * @var AccountChange_TermsAndConditions
     */
    private $termsAndConditions;

    /**
     * @var bool
     */
    private $isHouseMove;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->setTermsAndConditions(new AccountChange_TermsAndConditions());
    }

    /**
     * @param bool $isHouseMove is this order for a house move
     * @return void
     */
    public function setIsHouseMove($isHouseMove)
    {
        $this->isHouseMove = $isHouseMove;
    }

    /**
     * @return bool
     */
    public function isHouseMove()
    {
        return $this->isHouseMove;
    }

    /**
     * Getter for termsAndConditions
     *
     * @return AccountChange_TermsAndConditions
     */
    public function getTermsAndConditions()
    {
        return $this->termsAndConditions;
    }

    /**
     * Setter for termsAndConditions
     *
     * @param AccountChange_TermsAndConditions $termsAndConditions
     *
     * @return AccountChange_WlrProductConfigCreator
     */
    public function setTermsAndConditions(AccountChange_TermsAndConditions $termsAndConditions)
    {
        $this->termsAndConditions = $termsAndConditions;

        return $this;
    }

    /**
     * Wrapper class for TermsAndConditions to make it think we are and mvc application, we're
     * not but don't tell terms and conditions
     *
     * Generates an old wlr change configuration and/or a new change configuration based
     * on the set variables
     *
     * @return array old and/or new change configurations
     */
    public function createWlrConfigurations()
    {
        $this->termsAndConditions->setAppStateCallback($this);
        $arrReturn = array();
        $this->termsAndConditions->createWlrConfigurations($arrReturn);
        return $arrReturn;
    }

    /**
     * Setter
     *
     * @param Array $arrWlrProduct Wlr Information
     *
     * @return AccountChange_WlrProductConfigCreator
     */
    public function setArrWlrProduct($arrWlrProduct)
    {
        $this->arrWlrProduct = $arrWlrProduct;
        return $this;
    }

    /**
     * Getter
     *
     * @return Array wlr product details
     */
    public function getArrWlrProduct()
    {
        return $this->arrWlrProduct;
    }

    /**
     * Setter
     *
     * @param bool $bolContractResetWlr Is this a contract reset
     *
     * @return AccountChangeWlrProductConfigCreator
     */
    public function setBolContractResetWlr($bolContractResetWlr)
    {
        $this->bolContractResetWlr = $bolContractResetWlr;

        return $this;
    }

    /**
     * Getter
     *
     * @return bool probably contract reset wlr
     */
    public function getBolContractResetWlr()
    {
        return $this->bolContractResetWlr;
    }

    /**
     * Setter
     *
     * @param bool $bolSchedule is this a scheduled change
     *
     * @return AccountChangeWlrProductConfigCreator
     */
    public function setBolSchedule($bolSchedule)
    {
        $this->bolSchedule = $bolSchedule;

        return $this;
    }

    /**
     * Getter
     *
     * @return bool Is this scheduled
     */
    public function getBolSchedule()
    {
        return $this->bolSchedule;
    }

    /**
     * Setter
     *
     * @param int $intNewWlrId new Wlr Component ID
     *
     * @return AccountChangeWlrProductConfigCreator
     */
    public function setIntNewWlrId($intNewWlrId)
    {
        $this->intNewWlrId = $intNewWlrId;

        return $this;
    }

    /**
     * Getter
     *
     * @return int The new wlr id
     */
    public function getIntNewWlrId()
    {
        return $this->intNewWlrId;
    }

    /**
     * Setter
     *
     * @param Auth_BusinessActor $objBusinessActor The Business Actor
     *
     * @return AccountChangeWlrProductConfigCreator
     */
    public function setObjBusinessActor($objBusinessActor)
    {
        $this->objBusinessActor = $objBusinessActor;

        return $this;
    }

    /**
     * Getter
     *
     * @return Auth_BusinessActor The business actor
     */
    public function getObjBusinessActor()
    {
        return $this->objBusinessActor;
    }

    /**
     * Setter
     *
     * @param Core_Service $objCoreService The Core Service
     *
     * @return AccountChange_WlrProductConfigCreator
     */
    public function setObjCoreService(Core_Service $objCoreService)
    {
        $this->objCoreService = $objCoreService;

        return $this;
    }

    /**
     * get the core service
     *
     * @return Core_Service
     */
    public function getObjCoreService()
    {
        return $this->objCoreService;
    }

    /**
     * Setter
     *
     * @param LineCheck_Result $objLineCheckResults Line check results of the customer
     *
     * @return AccountChange_WlrProductConfigCreator
     */
    public function setObjLineCheckResults($objLineCheckResults)
    {
        $this->objLineCheckResults = $objLineCheckResults;

        return $this;
    }

    /**
     * Getter
     *
     * @return mixed line check object
     */
    public function getObjLineCheckResults()
    {
        return $this->objLineCheckResults;
    }

    /**
     * Setter
     *
     * @param string $strWlrContext The Wlr Context
     *
     * @return AccountChange_WlrProductConfigCreator
     */
    public function setStrWlrContext($strWlrContext)
    {
        $this->strWlrContext = $strWlrContext;

        return $this;
    }

    /**
     * Getter
     *
     * @return string wlr context
     */
    public function getStrWlrContext()
    {
        return $this->strWlrContext;
    }



    /**
     * is this variable defined within the application state?
     *
     * @param string $strCallingClass calling class
     * @param string $strVariableName variable name
     *
     * @return boolean
     */
    public function isApplicationStateVar($strCallingClass, $strVariableName)
    {
        $isApplicationStateVariable = false;

        $value = $this->getApplicationStateVar($strCallingClass, $strVariableName);

        if (isset($value)) {
            $isApplicationStateVariable = true;
        }

        return $isApplicationStateVariable;
    }

    /**
     * Setter
     *
     * @param int $intOldSdi The old service definition Id
     *
     * @return AccountChangeWlrProductConfigCreator
     */
    public function setIntOldSdi($intOldSdi)
    {
        $this->intOldSdi = $intOldSdi;

        return $this;
    }

    /**
     * Getter
     *
     * @return int old sdi
     */
    public function getIntOldSdi()
    {
        return $this->intOldSdi;
    }

    /**
     * Setter
     *
     * @param int $intNewSdi The new service definition id
     *
     * @return AccountChange_WlrProductConfigCreator
     */
    public function setIntNewSdi($intNewSdi)
    {
        $this->intNewSdi = $intNewSdi;

        return $this;
    }

    /**
     * Getter
     *
     * @return int new sdi
     */
    public function getIntNewSdi()
    {
        return $this->intNewSdi;
    }

    /**
     * get the value of this variable from the application state
     *
     * @param string $strCallingClass calling class
     * @param string $strVariableName variable name
     *
     * @return mixed the variable
     */
    public function getApplicationStateVar($strCallingClass, $strVariableName)
    {
        if ($strCallingClass == 'AccountChange_TermsAndConditions') {
            switch ($strVariableName)
            {
                case 'objCoreService':
                    return $this->objCoreService;
                    break;
                case 'arrWlrProduct':
                    return $this->arrWlrProduct;
                    break;
                case 'bolSchedule':
                    return $this->bolSchedule;
                    break;
                case 'objBusinessActor':
                    return $this->objBusinessActor;
                    break;
                case 'intNewWlrId':
                    return $this->intNewWlrId;
                    break;
                case 'objLineCheckResult':
                    return $this->objLineCheckResults;
                    break;
                case 'bolContractResetWlr':
                    return $this->bolContractResetWlr;
                    break;
                case 'strWlrContext':
                    return $this->strWlrContext;
                    break;
                case 'intOldSdi':
                    return $this->intOldSdi;
                    break;
                case 'intNewSdi':
                    return $this->intNewSdi;
                    break;
                case 'bolHousemove':
                    return $this->isHouseMove;
                    break;
            }
        }
    }
}
