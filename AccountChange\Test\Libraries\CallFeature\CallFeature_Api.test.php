<?php

class AccountChange_CallFeature_Api_Test extends PHPUnit_Framework_TestCase
{
    /** @var PHPUnit_Framework_MockObject_MockObject|Db_Adaptor */
    private $mockDbAdaptor;
    /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_CallFeature_Api */
    private $mockAccountChangeApi;

    public function setUp()
    {
        $this->mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses',
                'getCallerDisplayPreRegistrationIdForServiceId',
                'insertServiceIdIntoCallerDisplayPreRegistrationTable'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        Db_Manager::setAdaptor('AccountChange', $this->mockDbAdaptor);

        $this->mockAccountChangeApi = $this->getMock(
            'AccountChange_CallFeature_Api',
            array(
                'requireLegacyFiles',
                'getActiveWlrComponentIdFromServiceId',
                'createCallFeatureComponent',
                'getCliFromServiceId',
                'sendWlr3Order',
                'destroyProductComponentInstance',
                'createCallFeatureBundleComponentIfApplicable',
                'createCProductComponentInstance',
                'getProductComponentInstanceFromComponentId'
            )
        );

        $this->mockAccountChangeApi
            ->expects($this->any())
            ->method('requireLegacyFiles');
    }

    public function tearDown()
    {
        Db_Manager::restoreAdaptor('AccountChange');
    }

    /**
     *
     *
     * @param $productComponentInstances
     * @param $featureExists
     *
     * @return void
     * @dataProvider dataProviderCustomerHasCallFeature
     */
    public function testCustomerHasCallFeature($productComponentInstances, $featureExists)
    {
        $serviceId = 124233;
        $this->mockDbAdaptor->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->with(
                $this->equalTo('ExampleFeature'),
                $this->equalTo($serviceId),
                $this->anything()
            )
            ->willReturn($productComponentInstances);

        $this->assertEquals(
            $featureExists,
            $this->mockAccountChangeApi->customerHasCallFeature('ExampleFeature', $serviceId)
        );

    }

    public function dataProviderCustomerHasCallFeature()
    {
        return [
            [[], false],
            [[[123451]], true],
        ];
    }

    public function testExceptionIsThrownWhenCustomerHasCallerDisplay()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue("111111"));

        $this->setExpectedException(AccountChange_CallFeature_ExistingCallFeatureException::class);
        $this->mockAccountChangeApi->preRegisterForCallerDisplay("222222");
    }

    public function testExceptionIsThrownWhenCustomerHasAlreadyPreRegistered()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue(null));

        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getCallerDisplayPreRegistrationIdForServiceId')
            ->will($this->returnValue("111111"));

        $this->setExpectedException(AccountChange_CallFeature_ExistingPreRegistrationEntryException::class);
        $this->mockAccountChangeApi->preRegisterForCallerDisplay("222222");
    }

    public function testPreRegistrationEntryIsInsertedIfCustomerDoesNotHaveCallerDisplayAndHasNotPreRegistered()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue(null));

        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getCallerDisplayPreRegistrationIdForServiceId')
            ->will($this->returnValue(null));

        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('insertServiceIdIntoCallerDisplayPreRegistrationTable');

        $this->mockAccountChangeApi->preRegisterForCallerDisplay("111111");
    }

    public function testExceptionIsThrownWhenInvalidCallFeatureIsRequestedForAddition()
    {
        $this->setExpectedException(InvalidArgumentException::class);
        $this->mockAccountChangeApi->addCallFeature(
            'InvalidCallFeatureComponentHandle',
            "111111");
    }

    public function testExceptionIsThrownWhenCustomerAlreadyHasRequestedCallFeature()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue("111111"));

        $this->setExpectedException(AccountChange_CallFeature_ExistingCallFeatureException::class);
        $this->mockAccountChangeApi->addCallFeature(
            AccountChange_CallFeature_Api::PLUSNET_CALL_PROTECT_COMPONENT_HANDLE,
            "222222");
    }

    public function testExceptionIsThrownWhenCustomerDoesNotHaveAnActiveWlrComponent()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue(null));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getActiveWlrComponentIdFromServiceId')
            ->will($this->returnValue(null));

        $this->setExpectedException(AccountChange_CallFeature_InactiveWlrComponentException::class);
        $this->mockAccountChangeApi->addCallFeature(
            AccountChange_CallFeature_Api::PLUSNET_CALL_PROTECT_COMPONENT_HANDLE,
            "111111");
    }

    public function testExceptionIsThrownWhenWlr3CannotProvisionOrder()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue(null));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getActiveWlrComponentIdFromServiceId')
            ->will($this->returnValue("111111"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('createCallFeatureComponent')
            ->will($this->returnValue("222222"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('createCallFeatureBundleComponentIfApplicable')
            ->will($this->returnValue("333333"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getCliFromServiceId')
            ->will($this->returnValue("444444"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('sendWlr3Order')
            ->will($this->throwException(new Exception()));

        $this->mockAccountChangeApi
            ->expects($this->exactly(2))
            ->method('destroyProductComponentInstance');

        $this->setExpectedException(AccountChange_CallFeature_CallFeatureProvisioningException::class);
        $this->mockAccountChangeApi->addCallFeature(
            AccountChange_CallFeature_Api::PLUSNET_CALL_PROTECT_COMPONENT_HANDLE,
            "555555");
    }

    public function testExceptionIsThrownWhenWlr3CannotProvisionOrderAndProductComponentInstanceIsNull()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue(null));


        $mockAccountChangeApi = $this->getMock(
            'AccountChange_CallFeature_Api',
            array(
                'requireLegacyFiles',
                'getActiveWlrComponentIdFromServiceId',
                'createCallFeatureComponent',
                'getCliFromServiceId',
                'sendWlr3Order',
                'createCallFeatureBundleComponentIfApplicable',
                'createCProductComponentInstance',
                'getProductComponentInstanceFromComponentId',
                'getProductComponentInstance'
            )
        );

        $mockAccountChangeApi
            ->expects($this->once())
            ->method('getActiveWlrComponentIdFromServiceId')
            ->will($this->returnValue("111111"));

        $mockAccountChangeApi
            ->expects($this->once())
            ->method('createCallFeatureComponent')
            ->will($this->returnValue("222222"));

        $mockAccountChangeApi
            ->expects($this->once())
            ->method('createCallFeatureBundleComponentIfApplicable')
            ->will($this->returnValue("333333"));

        $mockAccountChangeApi
            ->expects($this->once())
            ->method('getCliFromServiceId')
            ->will($this->returnValue("444444"));

        $mockAccountChangeApi
            ->expects($this->once())
            ->method('sendWlr3Order')
            ->will($this->throwException(new Exception()));

        $mockAccountChangeApi
            ->expects($this->exactly(2))
            ->method('getProductComponentInstance')
            ->will($this->returnValue(null));

        $this->setExpectedException(AccountChange_CallFeature_CallFeatureProvisioningException::class);
        $mockAccountChangeApi->addCallFeature(
            AccountChange_CallFeature_Api::PLUSNET_CALL_PROTECT_COMPONENT_HANDLE,
            "555555");
    }

    public function testComponentIsCreatedAndCallFeatureIsProvisioned()
    {
        $this->mockDbAdaptor
            ->expects($this->once())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->will($this->returnValue(null));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getActiveWlrComponentIdFromServiceId')
            ->will($this->returnValue("111111"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('createCallFeatureComponent')
            ->will($this->returnValue("222222"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('createCallFeatureBundleComponentIfApplicable')
            ->will($this->returnValue("333333"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getCliFromServiceId')
            ->will($this->returnValue("444444"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('sendWlr3Order');

        $this->mockAccountChangeApi->addCallFeature(
            AccountChange_CallFeature_Api::PLUSNET_CALL_PROTECT_COMPONENT_HANDLE,
            "555555");
    }

    public function testRemoveCallFeature()
    {
        $serviceId = 555555;
        $wlrComponentId = 422241;
        $callFeaturePcId = 1234890;

        $mockInstance = $this->getMock('CWlrCallProtect', ['markForRemoval','getProductComponentInstanceID'], [], '', false);
        $mockInstance->expects($this->once())
            ->method('markForRemoval');
	$mockInstance->expects($this->any())
            ->method('getProductComponentInstanceID')
            ->will($this->returnValue($wlrComponentId));

        $this->mockDbAdaptor
            ->expects($this->atLeastOnce())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->willReturn($callFeaturePcId);

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getActiveWlrComponentIdFromServiceId')
            ->with($this->equalTo($serviceId))
            ->will($this->returnValue($wlrComponentId));

        $this->mockAccountChangeApi
            ->expects($this->any())
            ->method('destroyProductComponentInstance')
            ->with($this->equalTo($callFeaturePcId));

        $this->mockAccountChangeApi
            ->expects($this->any())
            ->method('getProductComponentInstanceFromComponentId')
            ->willReturn($mockInstance);

	$this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getCliFromServiceId')
            ->will($this->returnValue("444444"));

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('sendWlr3Order');

        $this->mockAccountChangeApi->removeCallFeature(
            AccountChange_CallFeature_Api::PLUSNET_CALL_PROTECT_COMPONENT_HANDLE, $serviceId
        );
    }

    public function testRemoveCallFeatureFailsIfNoWlrComponent()
    {
        $serviceId = 555555;

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getActiveWlrComponentIdFromServiceId')
            ->with($this->equalTo($serviceId))
            ->will($this->returnValue(null));

        $this->setExpectedException('AccountChange_CallFeature_InactiveWlrComponentException');
        $this->mockAccountChangeApi->removeCallFeature(AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE, $serviceId);
    }

    public function testRemoveCallFeatureFailsIfCallFeatureNotPresent()
    {
        $serviceId = 555555;
        $wlrComponentId = 422241;

        $this->mockAccountChangeApi
            ->expects($this->once())
            ->method('getActiveWlrComponentIdFromServiceId')
            ->with($this->equalTo($serviceId))
            ->will($this->returnValue($wlrComponentId));

        $this->mockDbAdaptor
            ->expects($this->atLeastOnce())
            ->method('getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses')
            ->willReturn(null);

        $this->setExpectedException('AccountChange_CallFeature_ExistingCallFeatureException');
        $this->mockAccountChangeApi->removeCallFeature(AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE, $serviceId);
    }
}
