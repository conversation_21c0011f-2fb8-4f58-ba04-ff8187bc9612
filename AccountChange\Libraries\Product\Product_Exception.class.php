<?php
/**
 * Account Change Product Exception
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_Exception.class.php,v 1.2 2009-01-27 07:07:23 bselby Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Account Change Product exception class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_Exception extends Exception
{
    const ERR_CANNOT_RETRIEVE_PRODUCT_DATA = 1;

    const ERR_MATCHING_PRODUCT_NOT_VALID   = 2;

    const ERR_NEW_PRODUCT_NOT_SET          = 3;
}
