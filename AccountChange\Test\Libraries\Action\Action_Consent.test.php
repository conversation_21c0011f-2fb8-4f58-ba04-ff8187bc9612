<?php
/**
 * Test Consent Storing Action
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 */

/**
 * Test the Consent Storing Action class
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2014 PlusNet
 */
class AccountChange_Action_Consent_Test extends Plusnet_Database_TestCase
{
    protected $arrServers = array(
        'Coredb_master' => array(),
        'Coredb_slave' => array()
    );

    protected $arrDataStructureDirectories = array('/local/codebase2005/modules/AccountChange/Test/datastructure/');

    protected $dbName = 'userdata';

    protected $dataSet = '/local/codebase2005/modules/AccountChange/Test/dataset/ConsentTestData.xml';

    /**
     * PHPUnit tearDown functionality
     *
     * @see Plusnet_Database_TestCase::tearDown()
     *
     * @return voidÏ
     */
    public function tearDown()
    {
        parent::tearDown();
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Test that all of sub actions of execute are called
     *
     * @covers AccountChange_Action_Consent::execute
     *
     * @return void
     */
    public function testConsentActionExecute()
    {
        $registry = AccountChange_Registry::instance();

        $serviceId = 1574456;

        $registry->setEntry('newWlrServiceComponentId', 1640);
        $registry->setEntry('newAdslServiceComponentId', 1000);
        $registry->setEntry('bolPortal', true);
        $registry->setEntry('adslConsentRecorded', false);
        $registry->setEntry('wlrConsentRecorded', false);
        $registry->setEntry('bolAdslTakeover', true);
        $registry->setEntry('bolWlrTakeover', true);
        $registry->setEntry('vchConsentVersionHandle', 'WP-DP-1');
        $registry->setEntry('intServiceId', $serviceId);

        $consentAction = new AccountChange_Action_Consent($serviceId, array());

        $consentAction->execute();
        Db_Manager::commit();

        $rowCount = $this->getConnection()->getRowCount('userdata.tblConsumerConsent');

        $this->assertEquals(2, $rowCount);

        $registry->reset();
    }
}
