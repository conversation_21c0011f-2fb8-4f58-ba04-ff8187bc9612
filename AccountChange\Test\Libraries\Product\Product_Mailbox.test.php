<?php
/**
 * Mailbox Product Configuration
 *
 * Testing class for the AccountChange_Product_Mailbox class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Product_Mailbox.test.php,v 1.2 2009-01-27 09:07:53 bselby Exp $
 * @since      File available since 2008-08-28
 */
/**
 * Mailbox Product Configuration Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Product_Mailbox_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for the constructor: intServiceComponentId
     *
     * @var unknown_type
     */
    private $intComponentId;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        $this->intComponentId = 515;
    }

    /**
     * @covers AccountChange_Product_Mailbox::isKeyProduct
     */
    public function testKeyProductIsFalse()
    {
        $objMailbox = new AccountChange_Product_Mailbox($this->intComponentId, AccountChange_Product_Manager::ACTION_NONE);

        $this->assertFalse($objMailbox->isKeyProduct());
    }

    /**
     * @covers AccountChange_Product_Mailbox::refresh
     */
    public function testRefreshCallsComponentsQueueSignal()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getComponentSignal', 'queueSignal'),
                                           array('Components', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getComponentSignal')
                         ->will($this->returnValue(false));
        $objMockDbAdaptor->expects($this->once())
                         ->method('queueSignal');

        Db_Manager::setAdaptor('Components', $objMockDbAdaptor);

        $objMailbox = new AccountChange_Product_Mailbox($this->intComponentId, AccountChange_Product_Manager::ACTION_REFRESH);

        $objMailbox->execute();
    }

    /**
     * @covers AccountChange_Product_Mailbox::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getServiceDefinitionDao'),
                                           array('Core', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_Mailbox(1, AccountChange_Product_Manager::ACTION_CHANGE);
        $objProduct2 = new AccountChange_Product_Mailbox(2, AccountChange_Product_Manager::ACTION_CHANGE);

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct1));
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct2));

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertEquals($objProduct2, $objProduct1->getNewProductConfiguration());
    }

    /**
     * @covers AccountChange_Product_Mailbox::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenNotChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getServiceDefinitionDao'),
                                           array('Core', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_Mailbox(1, AccountChange_Product_Manager::ACTION_NONE);
        $objProduct2 = new AccountChange_Product_Mailbox(2, AccountChange_Product_Manager::ACTION_NONE);

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct1));
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct2));

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }
}
