<?php

/**
 * <AUTHOR>
 */

class AccountChange_ServiceManagerTest extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function testGetServiceReturnsObjectOfRequestedService()
    {
        $service = AccountChange_ServiceManager::getService('ContractDurationHelper');

        $this->assertInstanceOf(AccountChange_ContractDurationHelper::class, $service);
    }

    /**
     * @return void
     */
    public function testSetServiceStoresTheChosenServiceAgainstTheAlias()
    {
        $service = new stdClass();

        AccountChange_ServiceManager::setService('DummyService', $service);

        $this->assertSame($service, AccountChange_ServiceManager::getService('DummyService'));
    }
}
