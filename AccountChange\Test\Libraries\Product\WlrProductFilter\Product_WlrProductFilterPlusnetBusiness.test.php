<?php

/**
 * <AUTHOR>
 */

use Plusnet\Feature\FeatureTogglePersistenceImpl;

class AccountChange_Product_WlrProductFilterPlusnetBusinessTest extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function tearDown()
    {
        Mockery::close();
        FeatureTogglePersistenceImpl::reset();
    }

    /**
     * @return void
     */
    public function testShouldFilterProductsThatAreNotBglWhenBglIsLive()
    {
        $twoDaysAgo = (new DateTime())->modify('- 2 days');

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getFeatureToggleByName')->andReturn([
            'onDateTime' => $twoDaysAgo->format('Y-m-d H:i:s'),
            'offDateTime' => null,
            'audience' => null
        ]);
        Db_Manager::setAdaptor('FeatureSwitch', $dbAdaptor);

        $products = [
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::BGL_BUSINESS_PAYG],
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::BGL_BUSINESS_UNLIMITED_UK_CALLS],
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::BGL_BUSINESS_UNLIMITED_AND_500_INTERNATIONAL],
            ['intNewWlrId' => '************'],
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::JUNE2016_UNLIMITED_UK_WITH_MOBILE],
        ];

        $filter = new AccountChange_Product_WlrProductFilterPlusnetBusiness(
            new AccountChange_Product_WlrProductFilter()
        );

        $result = $filter->filter($products);

        $this->assertCount(3, $result);
        $this->assertEquals(
            AccountChange_Product_WlrProductBase::BGL_BUSINESS_PAYG,
            $result[0]['intNewWlrId']
        );
        $this->assertEquals(
            AccountChange_Product_WlrProductBase::BGL_BUSINESS_UNLIMITED_UK_CALLS,
            $result[1]['intNewWlrId']
        );
        $this->assertEquals(
            AccountChange_Product_WlrProductBase::BGL_BUSINESS_UNLIMITED_AND_500_INTERNATIONAL,
            $result[2]['intNewWlrId']
        );
    }

    /**
     * @return void
     */
    public function testShouldNotFilterProductsBeforeBglIsLive()
    {
        $twoDaysAway = (new DateTime())->modify('+ 2 days');

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getFeatureToggleByName')->andReturn([
            'onDateTime' => $twoDaysAway->format('Y-m-d H:i:s'),
            'offDateTime' => null,
            'audience' => null
        ]);
        Db_Manager::setAdaptor('FeatureSwitch', $dbAdaptor);

        $products = [
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::BGL_BUSINESS_PAYG],
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::BGL_BUSINESS_UNLIMITED_UK_CALLS],
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::BGL_BUSINESS_UNLIMITED_AND_500_INTERNATIONAL],
            ['intNewWlrId' => '************'],
            ['intNewWlrId' => AccountChange_Product_WlrProductBase::JUNE2016_UNLIMITED_UK_WITH_MOBILE],
        ];

        $filter = new AccountChange_Product_WlrProductFilterPlusnetBusiness(
            new AccountChange_Product_WlrProductFilter()
        );

        $result = $filter->filter($products);

        $this->assertCount(5, $result);
    }
}
