server: coredb
role: slave
rows: single
statement:

SELECT
   pp.vchDescription AS costLevel 
FROM
   userdata.components c
INNER JOIN
   userdata.tblProductComponentInstance pci ON pci.intComponentID = c.component_id
INNER JOIN
   dbProductComponents.tblTariff t ON t.intTariffID = pci.intTariffID
INNER JOIN
   dbProductComponents.tblTariffPricePlan tpp ON tpp.intTariffId = t.intTariffID
INNER JOIN
   dbProductComponents.tblPricePlan pp ON pp.intPricePlanId = tpp.intPricePlanId
WHERE
   c.service_id = :serviceId
