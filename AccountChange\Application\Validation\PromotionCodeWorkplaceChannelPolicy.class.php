<?php
/**
 * Created by IntelliJ IDEA.
 * User: mstarbuck
 * Date: 2020-10-26
 */

use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\CampaignNotificationClient\Exception\PersonalisedPromotionNotFoundException;

class AccountChange_PromotionCodeWorkplaceChannelPolicy extends \AccountChange_AbstractValidationPolicy
{

    const C2M_PROMOTION_CODE = 'C2MPromotionCode';
    const C2M_PROMOTION_PERSONALISED = 'PersonalisedOffer';
    const C2M_CLIENT = 'C2mClient';
    const CAMPAIGN_NOTIFICATION_CLIENT = 'CampaignNotificationClient';
    const CURRENT_DATE = 'CurrentDate';
    const ERROR_MESSAGE = 'Oops, that offer isn\'t available. Please call us on 0800 432 0080 to find out the latest deal we can give you.';
    const SALES_CHANNEL = 'SalesChannel';
    const STAFF_ACCOUNT_CHANGE_SITUATION_KEY = 'StaffAccountChange';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_PROMO_WORKPLACE_PROMO_WITH_EXTERNAL_CHANNEL';

    private $c2mClient;
    private $currentDate;
    protected $actor;

    public function __construct(Auth_BusinessActor $actor, $isWorkplace = false, $isScript = false, $additionalInformation = array())
    {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);

        $this->actor = $actor;

        $this->c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
        $this->currentDate = (new \DateTime())->format('Y-m-d');
    }

    /**
     * Validates that a promotion code entered during the workplace account change journey is valid for an internal
     * channel (i.e. StaffAccountChange)
     *
     *
     * @return bool
     */
    public function validate()
    {
        $additionalInfo = $this->getAdditionalInformation();

        // promoOfferCode signifies a submitted code (via workplace member centre)
        $promoOfferCode = isset($additionalInfo[self::C2M_PROMOTION_CODE])
            && !is_null($additionalInfo[self::C2M_PROMOTION_CODE])
            && !empty(trim($additionalInfo[self::C2M_PROMOTION_CODE]));

        if ($promoOfferCode) {
            $c2mPromotion = $this->getC2mPromotionFromAdditionalData();
            $channel = isset($additionalInfo[self::SALES_CHANNEL]) ? $additionalInfo[self::SALES_CHANNEL] : '';
            $channelParts = explode('-', $channel);

            if ($channelParts[1] == self::STAFF_ACCOUNT_CHANGE_SITUATION_KEY) {
                // Check if the promotion has a channel that matches STAFF_ACCOUNT_CHANGE
                return $this->doesSalesChannelSituationMatchPromo(self::STAFF_ACCOUNT_CHANGE_SITUATION_KEY, $c2mPromotion->getSalesChannels());
            }
        }
        return true;
    }

    /**
     *
     * @param string $situation      Situation part of a sales channel to check against
     * @param array  $promoChannels  A list of channels belonging to a promo code
     *
     * @return bool
     */
    protected function doesSalesChannelSituationMatchPromo($situation, $promoChannels)
    {
        if (is_array($promoChannels)) {
            foreach ($promoChannels as $promoChannel) {
                $salesChannelParts = explode('-', $promoChannel);
                if ($salesChannelParts[1] == $situation) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
