#                         'AutomatedEmail' Module Dependancies
#
# A comma seperated, whitespace padded list of dependacies on other Plusnet Framework
# modules the 'Framework' Module has.
#
# a Namespace is a functional division  within a module identified by a unique
# prefix (classes conform to <Namespace>_<Prefix>). Modules are collections of
# namespaces sharing a single CVS module.  A module is the smallest deployable
# code grouping. For multiple namespaces to share a # module there must be inherent 
# interdependancies between those namespaces.
#
# Each application is a single module. Library functions shared between several
# applictions also form a disinct modules. Often a namespace will begin as an
# intrisic part of one application and then migrate into its own module when 
# another appliction becomes dependent upon it.
#
# Dependancies can be specified to either an entire module or a namespace within
# that module. Specifying individual namespaces is preferred.
#
#
#    Local Namespace  |                Dependent Upon                     |
#       Name          |  module / namespace   |            Name           |
#==========================================================================
#  SignupApplication  ,     namespace         ,           BuyNet             
#
AutomatedEmail        ,     module            ,         Framework
AutomatedEmail        ,     module            ,         MultiVariantTesting

