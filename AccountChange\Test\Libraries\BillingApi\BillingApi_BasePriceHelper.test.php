<?php

use \Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;
use \Plusnet\BillingApiClient\Entity\AvailableProduct;
use \Plusnet\BillingApiClient\Service\ServiceManager;
use \AccountChange_BillingApi_BasePriceHelper as BasePriceHelper;

class AccountChange_BillingApi_BasePriceHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that given a service id and an array of product offering price point pair, getBasePrices() returns us
     * the in contract and out of contract prices in an array, keyed by the unique combination of product offering and
     * price point ids.
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::getBasePrices()
     */
    public function testGetBasePricesReturnsArrayOfPricesKeyedByProductOfferingPricePointId()
    {
        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('getCustomerAvailableProducts'),
            array(),
            '',
            false
        );

        $availableProduct = new AvailableProduct();
        $availableProduct->setProductOfferingId(123);
        $availableProduct->setPricePointId(456);
        $availableProduct->setCurrentBasePriceInContractInPounds(5);
        $availableProduct->setCurrentBasePriceInPounds(10);
        $availableProducts = array($availableProduct);

        $billingApiFacade
            ->expects($this->any())
            ->method('getCustomerAvailableProducts')
            ->willReturn($availableProducts);

        ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $expected = array(
            '123:456' => array(
                'currentBasePriceInContract' => 5,
                'currentBasePrice'           => 10
            )
        );

        $productOfferingPricePointPair = new ProductOfferingPricePointPair(123, 456);
        $actual = BasePriceHelper::getBasePrices(0, array($productOfferingPricePointPair));

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that when BillingApi only returns the base price, getBasePrices() sets the in contract base price to be the
     * same as the base price in the returned array
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::getBasePrices()
     */
    public function testGetBasePricesSetsInContractPriceToSameAsBasePrice()
    {
        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('getCustomerAvailableProducts'),
            array(),
            '',
            false
        );

        $availableProduct = new AvailableProduct();
        $availableProduct->setProductOfferingId(123);
        $availableProduct->setPricePointId(456);
        $availableProduct->setCurrentBasePriceInPounds(10);
        $availableProducts = array($availableProduct);

        $billingApiFacade
            ->expects($this->any())
            ->method('getCustomerAvailableProducts')
            ->willReturn($availableProducts);

        ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $expected = array(
            '123:456' => array(
                'currentBasePriceInContract' => 10,
                'currentBasePrice'           => 10
            )
        );

        $productOfferingPricePointPair = new ProductOfferingPricePointPair(123, 456);
        $actual = \AccountChange_BillingApi_BasePriceHelper::getBasePrices(0, array($productOfferingPricePointPair));

        $this->assertEquals($expected, $actual);
    }

    /**
     * For certain products e.g. call plans that include a mobile bolt-on, we get multiple sets of base prices from BillingAPI.
     * In this case they will share the same product offering id and price point id.
     *
     * Test that these prices get merged together under a single id.
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::getBasePrices()
     */
    public function testGetBasePricesMergesBasePricesTogetherWhenTheyShareTheSameId()
    {
        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('getCustomerAvailableProducts'),
            array(),
            '',
            false
        );

        $availableProduct1 = new AvailableProduct();
        $availableProduct1->setProductOfferingId(123);
        $availableProduct1->setPricePointId(456);
        $availableProduct1->setCurrentBasePriceInContractInPounds(5);
        $availableProduct1->setCurrentBasePriceInPounds(10);

        $availableProduct2 = new AvailableProduct();
        $availableProduct2->setProductOfferingId(123);
        $availableProduct2->setPricePointId(456);
        $availableProduct2->setCurrentBasePriceInContractInPounds(50);
        $availableProduct2->setCurrentBasePriceInPounds(100);

        $availableProducts = array($availableProduct1, $availableProduct2);

        $billingApiFacade
            ->expects($this->any())
            ->method('getCustomerAvailableProducts')
            ->willReturn($availableProducts);

        ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $expected = array(
            '123:456' => array(
                'currentBasePriceInContract' => 55,
                'currentBasePrice'           => 110
            )
        );

        $productOfferingPricePointPair = new ProductOfferingPricePointPair(123, 456);
        $actual = BasePriceHelper::getBasePrices(0, array($productOfferingPricePointPair));

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that given an empty array of product offering price point pairs, an empty array is returned
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::getBasePrices()
     */
    public function testGetBasePricesReturnsEmptyArrayIfProductOfferingPricePointPairArrayIsEmpty()
    {
        $actual = BasePriceHelper::getBasePrices(0, array());

        $this->assertEquals(array(), $actual);
    }

    /**
     * Test that when given a service component id and tariff id the returned ProductOfferingPricePointPair uses both
     * ids for the product offering id and price point id respectively
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::createProductOfferingPricePointPairFromIds()
     */
    public function testCreateProductOfferingPricePointPairFromIdsCreatesIdFromServiceComponentIdAndTariffId()
    {
        $expected = new ProductOfferingPricePointPair(123, 456);
        $actual   = BasePriceHelper::createProductOfferingPricePointPairFromIds(1, 123, 456);

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that when given a service definition id the returned ProductOfferingPricePointPair uses the service
     * definition id for the product offering id and 1 for the price point id
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::createProductOfferingPricePointPairFromIds()
     */
    public function testCreateProductOfferingPricePointPairFromIdsCreatesIdFromServiceDefinitionId()
    {
        $expected = new ProductOfferingPricePointPair(6704, 1);
        $actual   = BasePriceHelper::createProductOfferingPricePointPairFromIds(6704);

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that when given a service definition id that is not a known legacy id, the id returned is null.
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::createProductOfferingPricePointPairFromIds()
     */
    public function testCreateProductOfferingPricePointPairFromIdsReturnsNullIfServiceDefinitionIsNotKnownLegacyId()
    {
        $expected = null;
        $actual   = BasePriceHelper::createProductOfferingPricePointPairFromIds(9999);

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that when given a service definition id that is not in RBM, the id returned is null.
     *
     * @covers AccountChange_BillingApi_BasePriceHelper::createProductOfferingPricePointPairFromIds()
     */
    public function testCreateProductOfferingPricePointPairFromIdsReturnsNullIfServiceDefinitionIsNotInRBM()
    {
        $expected = null;
        $actual   = BasePriceHelper::createProductOfferingPricePointPairFromIds(304);

        $this->assertEquals($expected, $actual);
    }
}
