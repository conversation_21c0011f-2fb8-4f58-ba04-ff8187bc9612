<?php

/**
 * Helper functions that allow us to determine if a service definition is fibre or not
 * Caches based on service definition as this is a commonly used query.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 * @link      http://jira.internal.plus.net/browse/PROF-284
 */

/**
 * Helper functions that allow us to determine if a service definition is fibre or not
 * Caches based on service definition as this is a commonly used query.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 PlusNet
 * @link      http://jira.internal.plus.net/browse/PROF-284
 */
class AccountChange_FibreHelper
{
    /**
     * Returns true if the given service definition id relates to a fibre (FTTC / FTTP) product.
     *
     * @param int $serviceDefinitionId Service definition id
     *
     * @return boolean
     **/
    public function isFibreProduct($serviceDefinitionId)
    {

        // Check cache
        $cache = $this->getIsFibreCache();
        if (isset($cache[$serviceDefinitionId])) {
            return $cache[$serviceDefinitionId];
        }

        // Not cached, use db lookup
        $isFibre = false;
        if (isset($serviceDefinitionId)) {
            $adaptor = Db_Manager::getAdaptor('AccountChange');
            $isFibre = (bool)$adaptor->isFibreProduct($serviceDefinitionId);
            $cache[$serviceDefinitionId] = $isFibre;
            $this->setIsFibreCache($cache);
        }
        return $isFibre;
    }

    /**
     * Retrieves the isFibre cache from the registry
     *
     * @return array
     **/
    protected function getIsFibreCache()
    {
        $registry = AccountChange_Registry::instance();
        return $registry->getEntry('isFibreCache');
    }

    /**
     * Sets the isFibre cache back into the registry
     *
     * @param array $cache Cache to add to the registry
     *
     * @return void
     **/
    protected function setIsFibreCache($cache)
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isFibreCache', $cache);
    }
}
