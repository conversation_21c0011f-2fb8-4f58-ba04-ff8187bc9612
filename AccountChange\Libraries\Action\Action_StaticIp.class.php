<?php
/**
 * Static Ip Action
 *
 * Action that performs checks to see if a static ip is still needed and available
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_StaticIp.class.php,v 1.2 2009-01-27 07:07:47 bselby Exp $
 * @since     File available since 2008-10-22
 */
/**
 * AccountChange_Action_StaticIp class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Action_StaticIp extends AccountChange_Action
{
    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $arrIps = array();

        $this->includeLegacyFiles();

        $arrProductStaticComponentTypeIds = $this->getStaticComponentTypeIds();

        $arrIps = userdata_component_find(
            array(
                'service_id' => $this->intServiceId,
                'type'       => $arrProductStaticComponentTypeIds,
                'status'     => array('active')
            )
        );

        if (empty($arrIps)) {

            $arrComponentsIp = array();
            $intCounter = 0;
            $intMinNumberAllocatedIndex = 0;
            $intComponentId = null;

            $arrIps = userdata_component_find(
                array(
                    'service_id' => $this->intServiceId,
                    'type'       => $arrProductStaticComponentTypeIds,
                    'status'     => array('unconfigured')
                )
            );

            foreach ($arrIps as $arrIp) {

                $objDatabase = Db_Manager::getAdaptor('AccountChange');
                $numberAllocatedForIpZone = $objDatabase->getNumberAllocatedForIpZone($arrIp['component_type_id']);
                // In order to deal with the legacy and framework code locking each other
                // it was decided to commit the default transaction everytime we called the database
                Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

                if ($numberAllocatedForIpZone > 0) {

                    $arrComponentsIp[$intCounter]['component_id'] = $arrIp['component_id'];
                    $arrComponentsIp[$intCounter]['component_type_id'] = $arrIp['component_type_id'];
                    $arrComponentsIp[$intCounter]['number_allocated'] =  $numberAllocatedForIpZone;

                    $intCounter++;
                }
            }

            $intCounter = 0;

            foreach ($arrComponentsIp as $arrComponentIp) {

                if ($arrComponentIp['number_allocated'] < $arrComponentsIp[$intMinNumberAllocatedIndex]['number_allocated']) {

                    $intMinNumberAllocatedIndex = $intCounter;
                }

                $intCounter++;
            }

            // Done so we can write tests for this, without it throwing errors that component id index is not set
            if (isset($arrComponentsIp[$intMinNumberAllocatedIndex]['component_id'])) {

                $intComponentId = $arrComponentsIp[$intMinNumberAllocatedIndex]['component_id'];

                $this->configureStaticIp($intComponentId, 'request');
            }
        }
    }

    /**
     * Legacy wrapper function to configure the static ip component
     *
     * @param int    $intComponentId component Id
     * @param string $strAction      the action
     *
     * @return void
     */
    public function configureStaticIp($intComponentId, $strAction = 'request')
    {
        $this->includeLegacyFiles();

        config_staticip_manual_configure($intComponentId, $strAction);
    }

    /**
     * Legacy getter for static component type ids
     *
     * @return array
     */
    public function getStaticComponentTypeIds()
    {
        $arrProductStaticComponentTypeIds = array();

        $this->includeLegacyFiles();

        $arrProductStaticComponentTypes = product_staticip_component_types_get();

        foreach ($arrProductStaticComponentTypes as $arrProductStaticComponentType) {

            $arrProductStaticComponentTypeIds[] = $arrProductStaticComponentType['service_component_id'];
        }

        return $arrProductStaticComponentTypeIds;
    }

    /**
     * Include all the legacy files we need, so we can also mock this for unit tests
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/components/config-dialup-access.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
    }
}
