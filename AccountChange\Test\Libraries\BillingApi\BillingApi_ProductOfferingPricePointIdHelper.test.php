<?php

use \Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;
use \Plusnet\BillingApiClient\Entity\AvailableProduct;
use \AccountChange_BillingApi_ProductOfferingPricePointIdHelper as ProductOfferingPricePointIdHelper;

class AccountChange_BillingApi_ProductOfferingPricePointIdHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that the helper can generate a product offering price point id from an available product
     *
     * @covers AccountChange_BillingApi_ProductOfferingPricePointIdHelper::generateIdFromAvailableProduct()
     */
    public function testThatProductOfferingPricePointIdIsGeneratedFromAvailableProduct()
    {
        $availableProduct = new AvailableProduct();
        $availableProduct->setProductOfferingId(123);
        $availableProduct->setPricePointId(456);

        $expected = '123:456';
        $actual   = ProductOfferingPricePointIdHelper::generateIdFromAvailableProduct($availableProduct);

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that the helper can generate a product offering price point id from a product offering price point pair
     *
     * @covers AccountChange_BillingApi_ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair()
     */
    public function testThatProductOfferingPricePointIdIsGeneratedFromProductOfferingPricePointPair()
    {
        $productOfferingPricePointPair = new ProductOfferingPricePointPair();
        $productOfferingPricePointPair->setProductOfferingId(123);
        $productOfferingPricePointPair->setPricePointId(456);

        $expected = '123:456';
        $actual   = ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that the helper returns null for a null available product
     *
     * @covers AccountChange_BillingApi_ProductOfferingPricePointIdHelper::generateIdFromAvailableProduct()
     */
    public function testThatNullIsReturnedForNullAvailableProduct()
    {
        $actual = ProductOfferingPricePointIdHelper::generateIdFromAvailableProduct(null);
        $this->assertEquals(null, $actual);
    }

    /**
     * Test that the helper returns null for a null product offering price point pair
     *
     * @covers AccountChange_BillingApi_ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair()
     */
    public function testThatNullIsReturnedForNullProductOfferingPricePointPair()
    {
        $actual = ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair(null);
        $this->assertEquals(null, $actual);
    }
}
