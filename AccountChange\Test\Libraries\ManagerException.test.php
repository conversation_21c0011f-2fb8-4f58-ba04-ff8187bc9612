<?php
/**
 * Manage Exception File
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @since      File available since 2011-12-07
 */
/**
 * Test class for AccountChange_ManagerException
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_ManagerExceptionTest extends PHPUnit_Framework_TestCase
{
    /**
     * Test that AccountChange_ManagerException is an exception class
     *
     * @covers AccountChange_ManagerException
     *
     * @return void
     */
    public function testManageExceptionIsExtendingException()
    {
        $exception = new AccountChange_ManagerException();

        $this->assertInstanceOf('Exception', $exception);
    }
}
