<?php


class AccountChange_PromotionCodePolicy extends AccountChange_AbstractValidationPolicy
{

    const ERROR_MESSAGE = 'Sorry, because you\'ve already got Line Rental Saver we can\'t offer you this deal online. Give us a call on 0800 432 0200 instead and we\'ll sort you out there.';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_LRS_PROMO_CODE';

    /**
     * @return bool
     */
    public function validate()
    {
        $c2mPromotion = $this->getC2mPromotionFromAdditionalData();
        if ($c2mPromotion !== false && $this->hasActiveOrPendingLrs() && $this->promotionHasLineRentalDiscount($c2mPromotion)) {
            return false;
        }
        return true;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }

    /**
     * Checks whether the customer has active LRS
     *
     * @return boolean
     */
    protected function hasActiveOrPendingLrs()
    {
        $serviceId = $this->actor->getExternalUserId();
        $subscriptionsHelper = new AccountChange_BillingApi_SubscriptionsHelper();
        return $subscriptionsHelper->hasActiveOrPendingLineRentalSaver($serviceId);
    }

    /**
     * Wraper to AccountChange_C2mPromotionsHelper - returns true if the given promotion has a line rental
     * discount.
     *
     * @param Plusnet\C2mApiClient\Entity\Promotion $promotion A promotion object
     *
     * @return bool
     **/
    protected function promotionHasLineRentalDiscount($promotion)
    {
        require_once(__DIR__ . '/../../Libraries/C2mPromotionsHelper.php');
        return AccountChange_C2mPromotionsHelper::promotionHasLineRentalDiscount($promotion);
    }
}
