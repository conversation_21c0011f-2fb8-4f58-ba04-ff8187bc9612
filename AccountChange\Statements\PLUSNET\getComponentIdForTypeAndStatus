server: coredb
role: slave
rows: multiple
statement:

    SELECT c.component_id
      FROM userdata.services as s 
INNER JOIN userdata.components as c ON c.service_id = s.service_id 
       AND c.status = :status
INNER JOIN products.service_components as sc ON sc.service_component_id = c.component_type_id 
INNER JOIN products.tblServiceComponentProduct AS scp ON scp.intServiceComponentID = sc.service_component_id 
INNER JOIN products.tblServiceComponentProductType AS scpt 
        ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID 
       AND scpt.vchHandle = :typeHandle
     WHERE s.service_id = :serviceId
