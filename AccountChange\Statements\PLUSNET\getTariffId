server: coredb
role: master
rows: single
statement:

SELECT
    t.intTariffID
FROM
    products.service_definitions AS sd
INNER JOIN products.adsl_product AS ap
    ON sd.service_definition_id = ap.service_definition_id
INNER JOIN products.tblProvisioningProfile AS provProfile
    ON provProfile.intProvisioningProfileID = ap.intProvisioningProfileID
INNER JOIN products.service_component_config scc
    ON sd.service_definition_id = scc.service_definition_id
INNER JOIN products.service_components sc
    ON (sc.service_component_id = scc.service_component_id)
INNER JOIN products.tblServiceComponentProduct scp
    ON sc.service_component_id = scp.intServiceComponentId
INNER JOIN products.tblServiceComponentProductType scpt
    USING (intServiceComponentProductTypeID)
INNER JOIN dbProductComponents.tblProductComponentConfig pcc
    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
INNER JOIN dbProductComponents.tblTariff t
    USING (intProductComponentConfigID)
INNER JOIN products.tblServiceComponentMarket scm
    ON scm.intServiceComponentId = sc.service_component_id
WHERE
    scpt.vchHandle = 'INTERNET_CONNECTION'
    AND (scm.intMarketId = :intMarketId OR scm.intMarketId IS NULL)
    AND (t.dtmEnd IS NULL OR t.dtmEnd > NOW())
    AND sd.service_definition_id = :intServiceDefinitionId
ORDER BY
    sd.signup_via_portal ASC;