server: coredb
role: slave
rows: multiple
statement:

SELECT
      pci.intProductComponentInstanceID AS productComponentInstanceId,
      pc.vchDisplayName AS name
  FROM
      userdata.tblProductComponentInstance AS pci
 INNER JOIN dbProductComponents.tblProductComponent pc
    ON pc.intProductComponentID = pci.intProductComponentID
 INNER JOIN dbProductComponents.tblStatus AS st
    ON st.intStatusID = pci.intStatusID
 INNER JOIN userdata.components AS c
    ON c.component_id = pci.intComponentID
 INNER JOIN dbProductComponents.tblProductComponentConfig pcc
    ON pcc.intProductComponentID = pci.intProductComponentID
 INNER JOIN dbProductComponents.tblWlrCallFeatureConfig cf
    ON pcc.intProductComponentConfigID = cf.intProductComponentConfigID
 INNER JOIN products.tblServiceComponentProduct scp
    ON scp.intServiceComponentID = c.component_type_id
   AND pcc.intServiceComponentProductID = scp.intServiceComponentProductID
 INNER JOIN products.tblServiceComponentProductType scpt
    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
 INNER JOIN userdata.services AS s on c.service_id = s.service_id
 WHERE st.vchHandle IN ('ACTIVE') AND scpt.vchHandle = 'WLR' AND s.service_id = :serviceId