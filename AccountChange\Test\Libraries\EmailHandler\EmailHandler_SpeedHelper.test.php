<?php


use Plusnet\C2mApiClient\Entity\BroadbandSpeedData;
use <PERSON><PERSON><PERSON>\MockInterface;

class AccountChange_EmailHandler_SpeedHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that we can get the estimated speeds from the database and advertised speeds from C2M
     *
     * @covers AccountChange_EmailHandler_SpeedHelper::getSpeedData()
     * @return void
     */
    public function testEstimatedAndAdvertisedSpeedsAreRetrievedCorrectly()
    {
        $broadbandSpeedData = new BroadbandSpeedData(1000, 2000, 3000, 4000, 5000, 6000);

        $speedHelper = $this->setupSpeedHelper($broadbandSpeedData);

        $expected = array(
            'minimumEstimatedDownloadSpeedMbs' => '2',
            'maximumEstimatedDownloadSpeedMbs' => '1',
            'minimumEstimatedUploadSpeedMbs'   => '4',
            'maximumEstimatedUploadSpeedMbs'   => '3',
            'broadbandSpeedRange'              => true,
            'guaranteedSpeedValue'             => '5',
            'maximumDownloadSpeedMbs'          => '6Mb',
            'advertisedDownloadSpeedMbs'       => '2',
            'advertisedUploadSpeedMbs'         => '5',
            'maximumDownloadSpeedMbps'         => '3',
            'maximumUploadSpeedMbps'           => '6',
            'minUpSpeed'                       => '0.5',
            'maxUpSpeed'                       => '1',
            'maxDownSpeed'                     => '6'
        );

        $actual = $speedHelper->getSpeedData();

        $this->assertEquals($expected, $actual);
    }

    /**
     * @return void
     */
    public function testSpeedsAreFormattedToNullWhenNotPresent()
    {
        $broadbandSpeedData = new BroadbandSpeedData(0, false, null, '', 0, null);

        $speedHelper = $this->setupSpeedHelper($broadbandSpeedData);

        $expected = array(
            'minimumEstimatedDownloadSpeedMbs' => '2',
            'maximumEstimatedDownloadSpeedMbs' => '1',
            'minimumEstimatedUploadSpeedMbs'   => '4',
            'maximumEstimatedUploadSpeedMbs'   => '3',
            'broadbandSpeedRange'              => true,
            'guaranteedSpeedValue'             => '5',
            'maximumDownloadSpeedMbs'          => '6Mb',
            'advertisedDownloadSpeedMbs'       => null,
            'advertisedUploadSpeedMbs'         => null,
            'maximumDownloadSpeedMbps'         => null,
            'maximumUploadSpeedMbps'           => null,
            'minUpSpeed'                       => '0.5',
            'maxUpSpeed'                       => '1',
            'maxDownSpeed'                     => '6'
        );

        $actual = $speedHelper->getSpeedData();

        $this->assertEquals($expected, $actual);
    }

    /**
     * @param BroadbandSpeedData $broadbandSpeedData broadband speed data
     * @return AccountChange_EmailHandler_SpeedHelper
     */
    private function setupSpeedHelper($broadbandSpeedData)
    {
        /** @var AccountChange_EmailHandler_SpeedHelper | MockInterface $speedHelper */
        $speedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array(
                'getWelcomeEmailSpeeds',
                'getC2MApiClient',
                'getC2MProductOfferingNameByServiceId'),
            array(null, null)
        );

        $welcomeEmailSpeeds = $this->getMock(
            'SignupApplication_WelcomeEmailSpeeds',
            array('getSpeedData')
        );

        $welcomeEmailSpeeds
            ->expects($this->once())
            ->method('getSpeedData')
            ->willReturn(
                array(
                    'estMaxDownSpeed' => '1000',
                    'estMinDownSpeed' => '2000',
                    'estMaxUpSpeed'   => '3000',
                    'estMinUpSpeed'   => '4000',
                    'mgs'             => '5000',
                    'maxDownSpeed'    => '6000',
                    'minUpSpeed'      => '500',
                    'maxUpSpeed'      => '1000',

                )
            );

        $speedHelper
            ->expects($this->once())
            ->method('getWelcomeEmailSpeeds')
            ->willReturn($welcomeEmailSpeeds);

        $c2mApiClient = $this->getMockBuilder('Plusnet\C2mApiClient\V5\Client')
            ->setMethods(array('getBroadbandSpeedData'))
            ->disableOriginalConstructor()
            ->getMock();

        $c2mApiClient
            ->expects($this->once())
            ->method('getBroadbandSpeedData')
            ->with('Unlimited')
            ->willReturn($broadbandSpeedData);

        $speedHelper
            ->expects($this->once())
            ->method('getC2MApiClient')
            ->willReturn($c2mApiClient);

        $speedHelper
            ->expects($this->once())
            ->method('getC2MProductOfferingNameByServiceId')
            ->willReturn('Unlimited');

        return $speedHelper;
    }

    /**
     * @param boolean $expected expected result
     * @dataProvider dataForOptOut
     */
    public function testOptOut($expected)
    {
        $helper = $this->getMockBuilder('AccountChange_EmailHandler_SpeedHelper')
            ->setMethods(array('getLineCheckResultByLineCheckId'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockLineCheck = $this->getMockBuilder('LineCheck_Result')
            ->setMethods(array('didCustomerOptOut'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockLineCheck->expects($this->once())
            ->method('didCustomerOptOut')
            ->willReturn($expected);

        $helper->expects($this->once())
            ->method('getLineCheckResultByLineCheckId')
            ->willReturn($mockLineCheck);

        $this->assertEquals($expected, $helper->getOptOut(1234));
    }

    public function dataForOptOut()
    {
        return [
            [true],
            [false]
        ];
    }
}
