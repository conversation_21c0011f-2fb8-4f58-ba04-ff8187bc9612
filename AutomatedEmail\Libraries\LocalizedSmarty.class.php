<?php
/**
 * AutomatedEmail_LocalizedSmarty
 *
 * @uses Mvc
 * @uses _LocalizedSmarty
 * @package
 * @version $id$
 * @copyright PlusNet
 * <AUTHOR> <<EMAIL>>
 * @license PHP License Version 3.01 {@link http://www.php.net/license/3_01.txt}
 */
class AutomatedEmail_LocalizedSmarty extends Mvc_LocalizedSmarty
{
	/**
	 * strSubject
	 *
	 * @var string
	 * @access private
	 */
	private $strSubject;

	/**
	 * __construct
	 *
	 * @param I18n_Locale $objLocale
	 * @param array $arrErrors
	 * @param string $strAltCompileDir
	 * @access public
	 * @return void
	 */
	public function __construct(I18n_Locale $objLocale, array $arrErrors = array(), $strAltCompileDir = '')
	{
		if (empty($strAltCompileDir))
		{
			$strModuleRoot    = Auto_ClassLoader::getModulesRoot();
			$strAltCompileDir = $strModuleRoot . '/../shared/Email';
		}

		// Call the constructor to set ourselves up
		parent::__construct($objLocale, $arrErrors, $strAltCompileDir);

		/// HACK to re-enable date_format modifier restricted by parent (see bug:19646)
		$this->unregister_modifier('date_format');

		$this->register_block('subject', array($this, 'getEmbeddedSubject'));
	}

	/**
	 * getEmbeddedSubject
	 *
	 * @param array $arrParams
	 * @param string $strContent
	 * @param Mvc_LocalizedSmarty $objSmarty
	 * @param boolean $bolRepeat
	 * @static
	 * @access public
	 * @return void
	 */
	static public function getEmbeddedSubject(array $arrParams, $strContent,
	                                          Mvc_LocalizedSmarty &$objSmarty, &$bolRepeat)
	{
		if (!$bolRepeat)
		{
			$objSmarty->setSubject($strContent);
		}
	}

	static public function noTidy(array $arrParams, Mvc_LocalizedSmarty &$objSmarty)
	{
		// Nothing to do. We just want to handle its presence.
	}

	/**
	 * getSubject
	 *
	 * @access public
	 * @return string
	 */
	public function getSubject()
	{
		return $this->strSubject;
	}

	/**
	 * setSubject
	 *
	 * @param string $strSubject
	 * @access public
	 * @return void
	 */
	public function setSubject($strSubject)
	{
		$this->strSubject = $strSubject;
	}
} // class AutomatedEmail_LocalizedSmarty extends Mvc_LocalizedSmarty
