<?xml version="1.0"?>
<phpunit
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd"
        bootstrap="./Test/Bootstrap.php"
        colors="true">
    <filter>
        <whitelist addUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./</directory>
            <exclude>
                <directory suffix=".php">./Test/</directory>
                <directory suffix=".php">./vendor</directory>
                <directory suffix=".php">./Test-PhpUnit9/</directory>
            </exclude>
        </whitelist>
    </filter>
    <coverage>
        <include>
            <directory suffix=".php">./</directory>
        </include>
        <exclude>
            <directory suffix=".php">./Test/</directory>
            <directory suffix=".php">./vendor/</directory>
            <directory suffix=".php">./Test-PhpUnit9/</directory>
        </exclude>
    </coverage>
    <testsuites>
        <testsuite name="UnitTests">
            <directory phpVersion="7.0" phpVersionOperator="lt"  suffix=".php">./Test/</directory>
        </testsuite>
        <testsuite name="UnitTests-9.5">
            <directory phpVersion="8.0" phpVersionOperator="gt" suffix=".php">./Test-PhpUnit9/</directory>
        </testsuite>
    </testsuites>
</phpunit>
