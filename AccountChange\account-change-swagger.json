{"openapi": "3.0.1", "info": {"title": "AccountChangeApi", "description": "Account Change Api documentation ", "version": "1.4.0"}, "servers": [{"url": "https://workplace.plus.net"}], "paths": {"/api/accountchange": {"post": {"description": "place a change order", "operationId": "place change", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountChangeDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}}}}}, "components": {"schemas": {"ErrorResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}}}, "AccountChangeDto": {"required": ["serviceId", "products", "type"], "type": "object", "properties": {"serviceId": {"type": "integer", "format": "int32"}, "actorId": {"type": "integer", "format": "int32"}, "contract": {"$ref": "#/components/schemas/ContractDto"}, "products": {"$ref": "#/components/schemas/ProductsDto"}, "consents": {"$ref": "#/components/schemas/ConsentsDto"}, "promotionCode": {"type": "string"}, "impressionOfferId": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["PRODUCT_CHANGE", "RE_CONTRACT"]}, "appointment": {"type": "object", "properties": {"notes": {"type": "string"}, "live": {"$ref": "#/components/schemas/LiveAppointmentDto"}, "manual": {"type": "array", "items": {"$ref": "#/components/schemas/ManualAppointmentDto"}}}}, "instant": {"type": "boolean"}, "retainContract": {"type": "boolean"}, "houseMove": {"type": "boolean"}, "oneOffCharges": {"type": "array", "items": {"$ref": "#/components/schemas/OneOffChangeDto"}}, "hardwareServiceComponentId": {"type": "integer", "format": "int32"}, "address": {"$ref": "#/components/schemas/AddressDto"}, "backDatedDate": {"type": "string", "format": "date"}}}, "ContractDto": {"required": ["type", "length", "agreementDate"], "type": "object", "properties": {"type": {"type": "string", "enum": ["F", "V"]}, "subType": {"type": "string"}, "length": {"type": "integer", "format": "int32"}, "agreementDate": {"type": "string", "format": "YYYY-MM-DD"}}}, "ProductsDto": {"required": ["componentIds"], "type": "object", "properties": {"serviceDefinition": {"type": "integer", "format": "int32"}, "serviceComponentId": {"type": "integer", "format": "int32"}, "newPhoneServiceComponentId": {"type": "integer", "format": "int32"}, "hardwareServiceComponentId": {"type": "integer", "format": "int32"}, "componentIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "products": {"type": "object", "properties": {"type": {"type": "string"}, "serviceComponentId": {"type": "integer", "format": "int32"}}}}}, "ConsentsDto": {"type": "object", "properties": {"proceedWithoutLineCheck": {"type": "boolean"}, "L2CConsent": {"type": "boolean"}}}, "OneOffChangeDto": {"required": ["name", "amount", "amountExVat"], "type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "integer", "format": "int32"}, "amountExVat": {"type": "integer", "format": "int32"}, "gross": {"type": "boolean"}}}, "LiveAppointmentDto": {"allOf": [{"$ref": "#/components/schemas/ManualAppointmentDto"}], "required": ["ref", "date", "timeSlot"], "type": "object", "properties": {"ref": {"type": "string"}}}, "ManualAppointmentDto": {"required": ["date", "timeSlot"], "type": "object", "properties": {"date": {"type": "string", "format": "DD-MM-YYYY"}, "timeSlot": {"type": "string", "enum": ["AM", "PM"]}}}, "AddressDto": {"required": ["buildingName", "thoroughfareName", "postTown", "country", "county", "postCode", "addressReference", "cssDatabaseCode"], "type": "object", "properties": {"addressCategory": {"type": "string"}, "addressReference": {"type": "string"}, "country": {"type": "string"}, "county": {"type": "string"}, "cssDatabaseCode": {"type": "string"}, "organisationName": {"type": "string"}, "poBox": {"type": "string"}, "postTown": {"type": "string"}, "postCode": {"type": "string"}, "premisesName": {"type": "string"}, "subPremises": {"type": "string"}, "thoroughfareName": {"type": "string"}, "thoroughfareNumber": {"type": "string"}, "buildingName": {"type": "string"}}}}}}