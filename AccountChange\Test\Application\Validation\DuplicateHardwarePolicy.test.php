<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_DuplicateHardwarePolicyTest extends PHPUnit_Framework_TestCase
{
    const TEST_SERVICE_ID = 1234;
    const TEST_SERVICE_COMPONENT_ID = 5678;

    /** @var Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject $mockBusinessActor */
    private $mockBusinessActor;

    /** @var HardwareClient_Client|PHPUnit_Framework_MockObject_MockObject $mockHardwareClient */
    private $mockHardwareClient;

    /**
     * @return void
     */
    public function setUp()
    {
        $this->mockBusinessActor = $this->mockBusinessActor();
        $this->mockHardwareClient = $this->mockHardwareClient();
    }

    /**
     * @return void
     */
    public function tearDown()
    {
        BusTier_BusTier::reset();
    }

    /**
     * @dataProvider dataForTestValidate
     * @param array $hardwareArray  hardwareArray
     * @param bool  $expectedResult expectedResult
     * @return void
     */
    public function testValidate($hardwareArray, $expectedResult)
    {
        $this->mockHardwareClient->expects($this->once())
            ->method('getHardwareForService')
            ->with($this->isInstanceOf(Int::class))
            ->willReturn($hardwareArray);

        $policy = $this->initialisePolicy();

        $this->assertEquals($expectedResult, $policy->validate());
    }

    /**
     * @return array[]
     */
    public function dataForTestValidate()
    {
        return [
            'No Hardware' => [
                'hardwareArray' => [],
                'expectedResult' => true,
            ],
            'One Hardware, no match' => [
                'hardwareArray' => [[
                    'intComponentTypeId' => '999',
                    'strStatus' => 'active',
                ]],
                'expectedResult' => true,
            ],
            'A match, but in destroyed state' => [
                'hardwareArray' => [[
                    'intComponentTypeId' => static::TEST_SERVICE_COMPONENT_ID,
                    'strStatus' => 'destroyed',
                ]],
                'expectedResult' => true,
            ],
            'Duplicate hardware detected' => [
                'hardwareArray' => [[
                    'intComponentTypeId' => static::TEST_SERVICE_COMPONENT_ID,
                    'strStatus' => 'active',
                ]],
                'expectedResult' => false,
            ],
            'Multiple hardware, no match' => [
                'hardwareArray' => [
                    [
                        'intComponentTypeId' => '666',
                        'strStatus' => 'active',
                    ],
                    [
                        'intComponentTypeId' => '999',
                        'strStatus' => 'active',
                    ],
                    [
                        'intComponentTypeId' => static::TEST_SERVICE_COMPONENT_ID,
                        'strStatus' => 'queued-destroy',
                    ],
                ],
                'expectedResult' => true,
            ],
            'Multiple hardware, exact match' => [
                'hardwareArray' => [
                    [
                        'intComponentTypeId' => '666',
                        'strStatus' => 'active',
                    ],
                    [
                        'intComponentTypeId' => static::TEST_SERVICE_COMPONENT_ID,
                        'strStatus' => 'queued-destroy',
                    ],
                    [
                        'intComponentTypeId' => static::TEST_SERVICE_COMPONENT_ID,
                        'strStatus' => 'active',
                    ],
                    [
                        'intComponentTypeId' => '999',
                        'strStatus' => 'active',
                    ],
                ],
                'expectedResult' => false,
            ],
        ];
    }

    /**
     * @return void
     */
    public function testGetErrorCodeAndFailure()
    {
        $policy = $this->initialisePolicy();

        $this->assertEquals($policy::ERROR_MESSAGE, $policy->getFailure());
        $this->assertEquals($policy::ERROR_CODE, $policy->getErrorCode());
    }

    /**
     * @return AccountChange_DuplicateHardwarePolicy
     */
    private function initialisePolicy()
    {
        return new AccountChange_DuplicateHardwarePolicy(
            $this->mockBusinessActor,
            false,
            false,
            [
                'serviceId' => static::TEST_SERVICE_ID,
                'serviceComponentId' => static::TEST_SERVICE_COMPONENT_ID,
            ]
        );
    }

    /**
     * @return Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockBusinessActor()
    {
        return $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->getMock();
    }

    /**
     * @return HardwareClient_Client|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockHardwareClient()
    {
        $mockClient = $this->getMockBuilder(HardwareClient_Client::class)
            ->disableOriginalConstructor()
            ->setMethods(['getHardwareForService'])
            ->getMock();

        BusTier_BusTier::setClient('hardware', $mockClient);

        return $mockClient;
    }
}
