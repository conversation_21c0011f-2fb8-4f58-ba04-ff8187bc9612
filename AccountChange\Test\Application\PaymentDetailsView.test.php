<?php
class AccountChange_PaymentDetailsView_Test extends PHPUnit_Framework_TestCase
{
    CONST template = '/local/codebase2005/content/cvsmodules/SharedAccountChangeContent/Linechecker.tpl';

    /**
     * Clear any database connections
     *
     * @see PHPUnit_Framework_TestCase::tearDown()
     * 
     * @return void
     */
    protected function tearDown()
    {
        parent::tearDown();

        Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('Financial');
        Db_Manager::restoreAdaptor('Core');
    }

    /**
     * Setup the CoreService object for JLP tests.
     *
     * @param integer $oldServiceDefinition Old Service Definition Details
     * @param integer $newServiceDefinition New Service Definition Details
     * 
     * @return void
     */
    private function setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition)
    {
        $coreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreDbAdaptor->expects($this->at(0))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($oldServiceDefinition));
        $coreDbAdaptor->expects($this->at(1))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($newServiceDefinition));
        Db_Manager::setAdaptor('Core', $coreDbAdaptor);
    }

    /**
     * - adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> or <i>$arrInput['intNewWlrId']</i> is set)
     * - no DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - taking one off charge payment
     * - no greenbee/waitrose to john lewis upgrade
     *
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     *
     * @return void
     */
    public function testAddHomePhoneWithNoDirectDebitDetailsAndNoCreditCardDetailsAndThereIsOneOffChargePayment()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $arrInput = array(
            'arrDirectDebitDetails'    => null,
            'intNewWlrId'              => 509,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // No active existing DD details - do not show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> or <i>$arrInput['intNewWlrId']</i> is set)
     * - existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is set)
     * - taking one off charge payment
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testAddHomePhoneWithExistingDirectDebitDetailsAndNoCreditCardDetailsAndThereIsOneOffChargePayment()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $strDdName                = 'Mr Fake';
        $strDdAccountNumberHashed = '******789';
        $strDdSortCodeHashed      = '****45';

        $arrInput = array(
            'arrDirectDebitDetails' => array(
                'name' => $strDdName,
                'accountNumber' => $strDdAccountNumberHashed,
                'sortCode' => $strDdSortCodeHashed
            ),
            'intNewWlrId'              => 509,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // Existing DD details - show them
        $this->assertTrue($output['bolDdDetailsReadOnlySection']);
        $this->assertEquals($strDdName, $output['strDdName']);
        $this->assertEquals($strDdAccountNumberHashed, $output['strDdAccountNumber']);
        $this->assertEquals($strDdSortCodeHashed, $output['strDdSortCode']);

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> or <i>$arrInput['intNewWlrId']</i> is set)
     * - existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is set)
     * - taking one off charge payment
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testAddHomePhoneWithExistingDirectDebitDetailsAndNotUsingExistingCreditCardDetailsAndThereIsOneOffChargePayment()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $strDdName                = 'Mrs Fake';
        $strDdAccountNumberHashed = '******321';
        $strDdSortCodeHashed      = '****10';

        $arrInput = array(
            'arrDirectDebitDetails' => array(
                'name' => $strDdName,
                'accountNumber' => $strDdAccountNumberHashed,
                'sortCode' => $strDdSortCodeHashed
            ),
            'intNewWlrId'              => 509,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // Existing DD details - show them
        $this->assertTrue($output['bolDdDetailsReadOnlySection']);
        $this->assertEquals($strDdName, $output['strDdName']);
        $this->assertEquals($strDdAccountNumberHashed, $output['strDdAccountNumber']);
        $this->assertEquals($strDdSortCodeHashed, $output['strDdSortCode']);
        
        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> or <i>$arrInput['intNewWlrId']</i> is set)
     * - existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is set)
     * - taking one off charge payment
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testAddHomePhoneWithExistingDirectDebitDetailsAndUsingExistingCreditCardDetailsAndThereIsOneOffChargePayment()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $strDdName                = 'Mrs Fake';
        $strDdAccountNumberHashed = '******321';
        $strDdSortCodeHashed      = '****10';

        $arrInput = array(
            'arrDirectDebitDetails' => array(
                'name' => $strDdName,
                'accountNumber' => $strDdAccountNumberHashed,
                'sortCode' => $strDdSortCodeHashed
            ),
            'intNewWlrId' => 509,
            'intOldSdi'   => 1234,
            'intNewSdi'   => 4321,
            'productIsp'  => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // Existing DD details - show them
        $this->assertTrue($output['bolDdDetailsReadOnlySection']);
        $this->assertEquals($strDdName, $output['strDdName']);
        $this->assertEquals($strDdAccountNumberHashed, $output['strDdAccountNumber']);
        $this->assertEquals($strDdSortCodeHashed, $output['strDdSortCode']);

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - not adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> and <i>$arrInput['intNewWlrId']</i> is not set)
     * - existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is set)
     * - taking one off charge payment
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testNotAddingHomePhoneWithExistingDirectDebitDetailsAndUsingExistingCreditCardDetailsAndThereIsOneOffChargePayment()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $strDdName                = 'Mrs Fake';
        $strDdAccountNumber       = '*********';
        $strDdSortCode            = '543210';
        $arrDirectDebitDetails = array(
            'account_id'                         => 1234,
            'bank_sort_code'                     => $strDdSortCode,
            'direct_debit_instruction_id'        => 456,
            'bank_account_number'                => $strDdAccountNumber,
            'name'                               => $strDdName,
            'our_reference'                      => 'PNET',
            'instruction_version'                => 1,
            'hsbc_reference'                     => '',
            'submission_date'                    => '',
            'acceptance_date'                    => '',
            'end_date'                           => '',
            'direct_debit_instruction_status_id' => 5
        );

        $objAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getDirectDebitDetailsDao'),
            array('Financial', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objAdaptor->expects($this->once())
            ->method('getDirectDebitDetailsDao')
            ->will($this->returnValue($arrDirectDebitDetails));

        Db_Manager::setAdaptor('Financial', $objAdaptor);

        $objDirectDebitDetails = new Financial_DirectDebitDetails(999);
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $arrInput = array(
            'arrDirectDebitDetails'    => $objDirectDebitDetails,
            'intNewWlrId'              => null,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus,net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // Not adding HomePhone - do not show existing DD details
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - not adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> and <i>$arrInput['intNewWlrId']</i> is not set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - taking one off charge payment
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testNotAddingHomePhoneWithNoDirectDebitDetailsAndUsingExistingCreditCardDetailsAndThereIsOneOffChargePayment()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $arrInput = array(
            'arrDirectDebitDetails'    => null,
            'intNewWlrId'              => null,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // No existing DD details - do not show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - not adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> and <i>$arrInput['intNewWlrId']</i> is not set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - taking one off charge payment
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testNotAddingHomePhoneWithNoDirectDebitDetailsAndUsingExistingCreditCardDetailsWhenTheirNotThereAndThereIsOneOffChargePayment()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $arrInput = array(
            'arrDirectDebitDetails'    => null,
            'intNewWlrId'              => null,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // No existing DD details - do not show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - Adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> or <i>$arrInput['intNewWlrId']</i> is set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - nothing to pay
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testAddingHomePhoneWithNoDirectDebitDetailsAndNoExistingCreditCardDetailsAndNothingToPay()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $arrInput = array(
            'arrDirectDebitDetails'    => null,
            'intNewWlrId'              => 509,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // No existing DD details - do not show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - Adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> or <i>$arrInput['intNewWlrId']</i> is set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - nothing to pay
     * - no greenbee/waitrose to john lewis upgrade
     */
    public function testAddingHomePhoneWithNoDirectDebitDetailsAndNotUsingExistingCreditCardDetailsAndNothingToPay()
    {
        $oldServiceDefinition = array('isp' => 'plusnet');
        $newServiceDefinition = array('isp' => 'plusnet');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $arrInput = array(
            'arrDirectDebitDetails'    => null,
            'intNewWlrId'              => 509,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // No existing DD details - do not show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - not adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> and <i>$arrInput['intNewWlrId']</i> is not set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - taking one off charge payment
     * - greenbee/waitrose to john lewis upgrade
     */
    public function testNotAddingHomePhoneWithNoDirectDebitDetailsAndUpgradingFromWrGbToJlp()
    {
        $oldServiceDefinition = array('isp' => 'greenbee');
        $newServiceDefinition = array('isp' => 'johnlewis');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $arrInput = array(
            'arrDirectDebitDetails'    => null,
            'intNewWlrId'              => null,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // No existing DD details - do not show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - not adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> and <i>$arrInput['intNewWlrId']</i> is not set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - taking one off charge payment
     * - greenbee/waitrose to john lewis upgrade
     */
    public function testNotAddingHomePhoneWithDirectDebitDetailsAndUpgradingFromWrGbToJlp()
    {
        $oldServiceDefinition = array('isp' => 'waitrose');
        $newServiceDefinition = array('isp' => 'johnlewis');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $strDdName                = 'Mrs Fake';
        $strDdAccountNumber       = '*********';
        $strDdSortCode            = '543210';

        $arrInput = array(
            'arrDirectDebitDetails' => array(
                'name' => $strDdName,
                'accountNumber' => $strDdAccountNumber,
                'sortCode' => $strDdSortCode
            ),
            'intNewWlrId'              => null,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'plus.net',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // Existing DD details - so show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertTrue(isset($output['strDdName']));
        $this->assertTrue(isset($output['strDdAccountNumber']));
        $this->assertTrue(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - not adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> and <i>$arrInput['intNewWlrId']</i> is not set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - taking one off charge payment
     * - Bauer to Plusnet upgrade
     */
    public function testNotAddingHomePhoneWithNoDirectDebitDetailsAndUpgradingFromBauToPn()
    {
        $oldServiceDefinition = array('isp' => 'bauer_yc');
        $newServiceDefinition = array('isp' => 'plus.net');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $arrInput = array(
            'arrDirectDebitDetails'    => null,
            'intNewWlrId'              => null,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'bauer_yc',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // Switching from BAU to PN
        $this->assertTrue($output['isCustomerSwitchingFromBauToPn']);

        // No existing DD details - do not show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertFalse(isset($output['strDdName']));
        $this->assertFalse(isset($output['strDdAccountNumber']));
        $this->assertFalse(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }

    /**
     * @covers AccountChange_PaymentDetailsView::processInput
     * @group medium
     * @desc Test scenario for:
     * - not adding HP (<i>$arrInput['arrWlrProduct']['intOldWlrId']</i> and <i>$arrInput['intNewWlrId']</i> is not set)
     * - no existing DD details on the account (<i>$arrInput['arrDirectDebitDetails']</i> is not set)
     * - taking one off charge payment
     * - Bauer to Plus.net upgrade
     */
    public function testNotAddingHomePhoneWithDirectDebitDetailsAndUpgradingFromBauToPn()
    {
        $oldServiceDefinition = array('isp' => 'bauer_yc');
        $newServiceDefinition = array('isp' => 'plus.net');
        $this->setupCoreServiceForJLP($oldServiceDefinition, $newServiceDefinition);

        $objView = new AccountChange_PaymentDetailsView(new I18n_Locale('en_gb'), '', '', '', '', self::template);

        $strDdName                = 'Mrs Fake';
        $strDdAccountNumber       = '*********';
        $strDdSortCode            = '543210';

        $arrInput = array(
            'arrDirectDebitDetails'    => array(
                'name' => $strDdName,
                'accountNumber' => $strDdAccountNumber,
                'sortCode' => $strDdSortCode
            ),
            'intNewWlrId'              => null,
            'intOldSdi'                => 1234,
            'intNewSdi'                => 4321,
            'productIsp'               => 'bauer_yc',
        );

        $output = $objView->testProcessInput($this, $arrInput);

        // Switching from BAU to PN
        $this->assertTrue($output['isCustomerSwitchingFromBauToPn']);

        // Existing DD details - so show them
        $this->assertFalse($output['bolDdDetailsReadOnlySection']);
        $this->assertTrue(isset($output['strDdName']));
        $this->assertTrue(isset($output['strDdAccountNumber']));
        $this->assertTrue(isset($output['strDdSortCode']));

        // No validation errors or DD instruction are active - there are valid payment details then
        $this->assertEquals('yes', $output['strPaymentDetailsValid']);
    }
}
