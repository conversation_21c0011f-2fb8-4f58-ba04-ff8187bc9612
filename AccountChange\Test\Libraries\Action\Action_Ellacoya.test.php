<?php

/**
 * Action Ellacoya
 *
 * Testing class for the AccountChange_Action_Ellacoya class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Action_Ellacoya.test.php,v 1.2 2009-01-27 09:07:37 bselby Exp $
 * @since      File available since 2008-09-02
 */
/**
 * Action Ellacoya Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Action_Ellacoya_Test extends PHPUnit_Framework_TestCase
{
    /**
    * @covers AccountChange_Action_Ellacoya::execute
    *
    */
    public function testExecuteCallsCorrectSubActions()
    {
        $intServiceId = 999;

        $objMock = $this->getMock('AccountChange_Action_Ellacoya',
                                array('updateEllacoya', 'setConnectionProfiles'),
                                array($intServiceId));

        $objMock->expects($this->once())
                ->method('updateEllacoya');

        $objMock->expects($this->once())
                ->method('setConnectionProfiles');

        $objMock->execute();
    }
}
