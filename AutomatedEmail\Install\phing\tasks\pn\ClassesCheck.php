<?php
include_once 'phing/Task.php';
class ClassesCheck extends Task {

    private $_strReturnName;
    
    private $_arrClasses;

    private $_strDir;

    public function setDir($strDir) {
        $this->_strDir = $strDir;
    }
    
    public function setReturnName($strName) {
        $this->_strReturnName = $strName;
    }
    
    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {

		if (!$this->_strReturnName) {
		    throw new BuildException('You must specify the returnName attribute', $this->getLocation());
		}
		
		$strFrameworkDir = $this->project->getProperty('frameworkDir');
		
		if(!@include_once($strFrameworkDir.'/Libraries/bootstrap.inc.php')) {
			throw new BuildException('Cannot load Framework Autoloader', $this->getLocation());
		}		
		
		//grep Framework's Library for classes
		$this->_scanDirForClasses("$strFrameworkDir/Libraries");
		//grep application for classes		
		if(isset($this->_strDir)) $this->_scanDirForClasses("$this->_strDir");
		//check if class is loadable
		foreach ($this->_arrClasses as $strClassname => $strFilename) {

			if(!class_exists($strClassname)) {
			
				$this->log("Class $strClassname declaration in $strFilename is invalid!");
				$failed = true;	
			}	
		}

		if(!isset($failed)) $this->project->setProperty($this->_strReturnName, true);
    }

    private function _scanDirForClasses($strPath)
    {
		$arrPhpFiles = $this->rglob($strPath,'*.php');
		foreach ($arrPhpFiles as $strPhpFile) {
			if (substr($strPhpFile,-8) != 'test.php') {
				
				$fileContents = php_strip_whitespace($strPhpFile);
				$arrMatches = array();
				if(preg_match('/class\s*(\w+)/',$fileContents,$arrMatches)) 
				{
					
					//hardcode 3rd party classes to omit
					if($arrMatches[1] == 'wurfl_class' || $arrMatches[1]  == 'lib_filter')
					{
						return;
					}

					if(preg_match('/class (.*) extends +UnitTestCase/',$fileContents))
					{
						return;
					}
					$this->_arrClasses[$arrMatches[1]] = $strPhpFile;
				}
			}
		}		
    }
	
	/**
	 * Recursive version of glob
	 */
	function rglob($strDir, $strPattern)
	{
		$strDir = escapeshellcmd($strDir);
		$aFiles = glob("$strDir/$strPattern");

		foreach (glob("$strDir/*", GLOB_ONLYDIR) as $sSubDir)
		{
			$aSubFiles = $this->rglob($sSubDir, $strPattern);
			$aFiles = array_merge($aFiles, $aSubFiles);
		}
		return $aFiles;
	} 
}
