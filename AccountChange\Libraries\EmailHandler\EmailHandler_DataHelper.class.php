<?php

/**
 * Class AccountChange_EmailHandler_DataHelper
 *
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_EmailHandler_DataHelper
{

    /**
     * Wrapper function for unit testing
     *
     * @param int $serviceComponentId integer Service component id if the call plan
     *
     * @return array
     */
    public function getCallPlanDetailsByWlrServiceComponentId($serviceComponentId)
    {
        return CWlrProduct::getCallPlanDetailsFromServiceComponentId($serviceComponentId);
    }

    /**
     * @param int $workingDays working days
     * @return int
     */
    public function getTodayPlusWorkingDays($workingDays)
    {
        return I18n_Date::addWorkingDays(I18n_Date::now()->getTimestamp(), $workingDays);
    }
}
