<?php
/**
 * Confirmation Email Handler Tests
 *
 * @package AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 */
/**
 * Confirmation Email Handler Tests
 *
 * @package AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_ConfirmationEmailHandler_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Setup Confirmation Email object
     *
     * @param AccountChange_ConfirmationEmailHandler $confEmail Confimation Email Handler
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setupObject($confEmail)
    {
        $currentDualPlay = true;
        $usageAllowFixed = true;
        $newDirectDebit = true;
        $newDirectDebitReady = true;
        $recontracted = true;
        $specialOffer = true;
        $currentBroadbandProduct = 'currentBroadbandProduct';
        $newBroadbandProduct = 'newBroadbandProduct';
        $newUsageAllowance = 'newUsageAllowance';
        $lineRentalFrequency = 'lineRentalFrequency';
        $currentCallPlan = 'currentCallPlan';
        $currentLineRental = 14.99;
        $newCallPlan = 'newCallPlan';
        $newLineRental = 14.50;
        $callFeatures = 'callFeatures';
        $contractDuration = 'contractDuration';
        $newPrice = 'newPrice';
        $offerPrice = 'offerPrice';
        $offerDuration = 'offerDuration';
        $changeDate = 'changeDate';
        $isBroadbandChanging = true;
        $broadbandCost = 9.99;
        $lineRentalCost = 17.99;
        $callPlanCost = 3.99;
        $callFeatureCost = 5.99;
        $callerClassName = "TestClassName";
        $discountAmount = 1.00;
        $advertisedDownloadSpeedMbs = 1;
        $advertisedUploadSpeedMbs = 2;
        $broadbandSpeedRange = 3;
        $minimumEstimatedDownloadSpeedMbs = 4;
        $maximumEstimatedDownloadSpeedMbs = 5;
        $minimumEstimatedUploadSpeedMbs = 6;
        $maximumEstimatedUploadSpeedMbs = 7;
        $maximumDownloadSpeedMbs = 8;
        $guaranteedSpeedAvailable = true;
        $guaranteedSpeedValue = 10;
        $isSogea = true;

        $confEmail
            ->setCurrentDualPlay($currentDualPlay)
            ->setUsageAllowanceFixed($usageAllowFixed)
            ->setNewDirectDebit($newDirectDebit)
            ->setNewDirectDebitReady($newDirectDebitReady)
            ->setRecontracted($recontracted)
            ->setSpecialOffer($specialOffer)
            ->setCurrentBroadbandProduct($currentBroadbandProduct)
            ->setNewBroadbandProduct($newBroadbandProduct)
            ->setNewUsageAllowance($newUsageAllowance)
            ->setLineRentalFrequency($lineRentalFrequency)
            ->setCurrentCallPlan($currentCallPlan)
            ->setCurrentLineRental($currentLineRental)
            ->setNewCallPlan($newCallPlan)
            ->setNewLineRental($newLineRental)
            ->setCallFeatures($callFeatures)
            ->setContractDuration($contractDuration)
            ->setNewPrice($newPrice)
            ->setOfferPrice($offerPrice)
            ->setOfferDuration($offerDuration)
            ->setChangeDate($changeDate)
            ->setIsBroadbandChanging($isBroadbandChanging)
            ->setBroadbandSubscriptionCost($broadbandCost)
            ->setLineRentalSubscriptionCost($lineRentalCost)
            ->setCallPlanSubscriptionCost($callPlanCost)
            ->setCallFeatureSubscriptionCost($callFeatureCost)
            ->setCallerClassName($callerClassName)
            ->setDiscountAmount($discountAmount)
            ->setAdvertisedDownloadSpeedMbs($advertisedDownloadSpeedMbs)
            ->setAdvertisedUploadSpeedMbs($advertisedUploadSpeedMbs)
            ->setBroadbandSpeedRange($broadbandSpeedRange)
            ->setMinimumEstimatedDownloadSpeedMbs($minimumEstimatedDownloadSpeedMbs)
            ->setMaximumEstimatedDownloadSpeedMbs($maximumEstimatedDownloadSpeedMbs)
            ->setMinimumEstimatedUploadSpeedMbs($minimumEstimatedUploadSpeedMbs)
            ->setMaximumEstimatedUploadSpeedMbs($maximumEstimatedUploadSpeedMbs)
            ->setMaximumDownloadSpeedMbs($maximumDownloadSpeedMbs)
            ->setGuaranteedSpeedAvailable($guaranteedSpeedAvailable)
            ->setGuaranteedSpeedValue($guaranteedSpeedValue)
            ->setSogeaProduct($isSogea);

         return $confEmail;
    }

    /**
     * Test Getters and Setters
     *
     * @covers AccountChange_ConfirmationEmailHandler::setCurrentDualPlay
     * @covers AccountChange_ConfirmationEmailHandler::getCurrentDualPlay
     * @covers AccountChange_ConfirmationEmailHandler::setUsageAllowanceFixed
     * @covers AccountChange_ConfirmationEmailHandler::getUsageAllowanceFixed
     * @covers AccountChange_ConfirmationEmailHandler::setNewDirectDebit
     * @covers AccountChange_ConfirmationEmailHandler::getNewDirectDebit
     * @covers AccountChange_ConfirmationEmailHandler::setNewDirectDebitReady
     * @covers AccountChange_ConfirmationEmailHandler::getNewDirectDebitReady
     * @covers AccountChange_ConfirmationEmailHandler::setRecontracted
     * @covers AccountChange_ConfirmationEmailHandler::getRecontracted
     * @covers AccountChange_ConfirmationEmailHandler::setSpecialOffer
     * @covers AccountChange_ConfirmationEmailHandler::getSpecialOffer
     * @covers AccountChange_ConfirmationEmailHandler::setNewBroadbandProduct
     * @covers AccountChange_ConfirmationEmailHandler::getNewBroadbandProduct
     * @covers AccountChange_ConfirmationEmailHandler::setNewUsageAllowance
     * @covers AccountChange_ConfirmationEmailHandler::getNewUsageAllowance
     * @covers AccountChange_ConfirmationEmailHandler::setLineRentalFrequency
     * @covers AccountChange_ConfirmationEmailHandler::getLineRentalFrequency
     * @covers AccountChange_ConfirmationEmailHandler::setNewCallPlan
     * @covers AccountChange_ConfirmationEmailHandler::getNewCallPlan
     * @covers AccountChange_ConfirmationEmailHandler::setCallFeatures
     * @covers AccountChange_ConfirmationEmailHandler::getCallFeatures
     * @covers AccountChange_ConfirmationEmailHandler::setContractDuration
     * @covers AccountChange_ConfirmationEmailHandler::getContractDuration
     * @covers AccountChange_ConfirmationEmailHandler::setNewPrice
     * @covers AccountChange_ConfirmationEmailHandler::getNewPrice
     * @covers AccountChange_ConfirmationEmailHandler::setOfferPrice
     * @covers AccountChange_ConfirmationEmailHandler::getOfferPrice
     * @covers AccountChange_ConfirmationEmailHandler::setOfferDuration
     * @covers AccountChange_ConfirmationEmailHandler::getOfferDuration
     * @covers AccountChange_ConfirmationEmailHandler::setChangeDate
     * @covers AccountChange_ConfirmationEmailHandler::getChangeDate
     * @covers AccountChange_ConfirmationEmailHandler::isSogeaProduct
     * @covers AccountChange_ConfirmationEmailHandler::isFttcProduct
     * @covers AccountChange_ConfirmationEmailHandler::isFttpProduct
     *
     * @return void
     */
    public function testGettersAndSetters()
    {
        $currentDualPlay = true;
        $usageAllowFixed = true;
        $newDirectDebit = true;
        $newDirectDebitReady = true;
        $recontracted = true;
        $specialOffer = true;
        $newBroadbandProduct = 'newBroadbandProduct';
        $newUsageAllowance = 'newUsageAllowance';
        $lineRentalFrequency = 'lineRentalFrequency';
        $newCallPlan = 'newCallPlan';
        $callFeatures = 'callFeatures';
        $contractDuration = 'contractDuration';
        $newPrice = 'newPrice';
        $offerPrice = 'offerPrice';
        $offerDuration = 'offerDuration';
        $changeData = 'changeDate';
        $isBroadbandChanging = true;
        $callerClassName = "TestClassName";
        $discountAmount = 1.00;
        $advertisedDownloadSpeedMbs = 1;
        $advertisedUploadSpeedMbs = 2;
        $broadbandSpeedRange = 3;
        $minimumEstimatedDownloadSpeedMbs = 4;
        $maximumEstimatedDownloadSpeedMbs = 5;
        $minimumEstimatedUploadSpeedMbs = 6;
        $maximumEstimatedUploadSpeedMbs = 7;
        $maximumDownloadSpeedMbs = 8;
        $guaranteedSpeedAvailable = true;
        $guaranteedSpeedValue = 10;
        $isSogea = true;
        $isFttc = false;
        $isFttp = false;

        $confEmail = $this->setupObject(new AccountChange_ConfirmationEmailHandler());

        $this->assertEquals($currentDualPlay, $confEmail->getCurrentDualPlay());
        $this->assertEquals($usageAllowFixed, $confEmail->getUsageAllowanceFixed());
        $this->assertEquals($newDirectDebit, $confEmail->getNewDirectDebit());
        $this->assertEquals($newDirectDebitReady, $confEmail->getNewDirectDebitReady());
        $this->assertEquals($recontracted, $confEmail->getRecontracted());
        $this->assertEquals($specialOffer, $confEmail->getSpecialOffer());
        $this->assertEquals($newBroadbandProduct, $confEmail->getNewBroadbandProduct());
        $this->assertEquals($newUsageAllowance, $confEmail->getNewUsageAllowance());
        $this->assertEquals($lineRentalFrequency, $confEmail->getLineRentalFrequency());
        $this->assertEquals($newCallPlan, $confEmail->getNewCallPlan());
        $this->assertEquals($callFeatures, $confEmail->getCallFeatures());
        $this->assertEquals($contractDuration, $confEmail->getContractDuration());
        $this->assertEquals($newPrice, $confEmail->getNewPrice());
        $this->assertEquals($offerPrice, $confEmail->getOfferPrice());
        $this->assertEquals($offerDuration, $confEmail->getOfferDuration());
        $this->assertEquals($changeData, $confEmail->getChangeDate());
        $this->assertEquals($isBroadbandChanging, $confEmail->isBroadbandChanging());
        $this->assertEquals($callerClassName, $confEmail->getCallerClassName());
        $this->assertEquals($discountAmount, $confEmail->getDiscountAmount());
        $this->assertEquals($advertisedDownloadSpeedMbs, $confEmail->getAdvertisedDownloadSpeedMbs());
        $this->assertEquals($advertisedUploadSpeedMbs, $confEmail->getAdvertisedUploadSpeedMbs());
        $this->assertEquals($broadbandSpeedRange, $confEmail->getBroadbandSpeedRange());
        $this->assertEquals($minimumEstimatedDownloadSpeedMbs, $confEmail->getMinimumEstimatedDownloadSpeedMbs());
        $this->assertEquals($maximumEstimatedDownloadSpeedMbs, $confEmail->getMaximumEstimatedDownloadSpeedMbs());
        $this->assertEquals($minimumEstimatedUploadSpeedMbs, $confEmail->getMinimumEstimatedUploadSpeedMbs());
        $this->assertEquals($maximumEstimatedUploadSpeedMbs, $confEmail->getMaximumEstimatedUploadSpeedMbs());
        $this->assertEquals($maximumDownloadSpeedMbs, $confEmail->getMaximumDownloadSpeedMbs());
        $this->assertEquals($guaranteedSpeedAvailable, $confEmail->getGuaranteedSpeedAvailable());
        $this->assertEquals($guaranteedSpeedValue, $confEmail->getGuaranteedSpeedValue());
        $this->assertEquals($isSogea, $confEmail->isSogeaProduct());
        $this->assertEquals($isFttc, $confEmail->isFttcProduct());
        $this->assertEquals($isFttp, $confEmail->isFttpProduct());
    }

    /**
     * Tests Sendmail process
     *
     * @covers AccountChange_ConfirmationEmailHandler::setCurrentDualPlay
     * @covers AccountChange_ConfirmationEmailHandler::getCurrentDualPlay
     * @covers AccountChange_ConfirmationEmailHandler::setUsageAllowanceFixed
     * @covers AccountChange_ConfirmationEmailHandler::getUsageAllowanceFixed
     * @covers AccountChange_ConfirmationEmailHandler::setNewDirectDebit
     * @covers AccountChange_ConfirmationEmailHandler::getNewDirectDebit
     * @covers AccountChange_ConfirmationEmailHandler::setNewDirectDebitReady
     * @covers AccountChange_ConfirmationEmailHandler::getNewDirectDebitReady
     * @covers AccountChange_ConfirmationEmailHandler::setRecontracted
     * @covers AccountChange_ConfirmationEmailHandler::getRecontracted
     * @covers AccountChange_ConfirmationEmailHandler::setSpecialOffer
     * @covers AccountChange_ConfirmationEmailHandler::getSpecialOffer
     * @covers AccountChange_ConfirmationEmailHandler::setNewBroadbandProduct
     * @covers AccountChange_ConfirmationEmailHandler::getNewBroadbandProduct
     * @covers AccountChange_ConfirmationEmailHandler::setNewUsageAllowance
     * @covers AccountChange_ConfirmationEmailHandler::getNewUsageAllowance
     * @covers AccountChange_ConfirmationEmailHandler::setLineRentalFrequency
     * @covers AccountChange_ConfirmationEmailHandler::getLineRentalFrequency
     * @covers AccountChange_ConfirmationEmailHandler::setNewCallPlan
     * @covers AccountChange_ConfirmationEmailHandler::getNewCallPlan
     * @covers AccountChange_ConfirmationEmailHandler::setCallFeatures
     * @covers AccountChange_ConfirmationEmailHandler::getCallFeatures
     * @covers AccountChange_ConfirmationEmailHandler::setContractDuration
     * @covers AccountChange_ConfirmationEmailHandler::getContractDuration
     * @covers AccountChange_ConfirmationEmailHandler::setNewPrice
     * @covers AccountChange_ConfirmationEmailHandler::getNewPrice
     * @covers AccountChange_ConfirmationEmailHandler::setOfferPrice
     * @covers AccountChange_ConfirmationEmailHandler::getOfferPrice
     * @covers AccountChange_ConfirmationEmailHandler::setOfferDuration
     * @covers AccountChange_ConfirmationEmailHandler::getOfferDuration
     * @covers AccountChange_EmailHandler::populateEmailVariables
     * @covers AccountChange_ConfirmationEmailHandler::populateEmailVariables
     * @covers AccountChange_EmailHandler::sendEmail
     * @covers AccountChange_ConfirmationEmailHandler::sendEmail
     * @covers AccountChange_EmailHandler::getIspAutomatedEmail
     * @covers AccountChange_ConfirmationEmailHandler::getIspAutomatedEmail
     * @covers AccountChange_EmailHandler::getEmailName
     * @covers AccountChange_ConfirmationEmailHandler::getEmailName
     * @covers AccountChange_ConfirmationEmailHandler::isSogeaProduct
     * @covers AccountChange_ConfirmationEmailHandler::isFttcProduct
     * @covers AccountChange_ConfirmationEmailHandler::isFttpProduct
     *
     * @return void
     */
    public function testSendEmail()
    {
        $serviceId = 3245422;
        $emailName = 'upgrade_confirmation';

        $currentDualPlay = true;
        $usageAllowFixed = true;
        $newDirectDebit = true;
        $newDirectDebitReady = true;
        $recontracted = true;
        $specialOffer = true;
        $currentBroadbandProduct = 'currentBroadbandProduct';
        $newBroadbandProduct = 'newBroadbandProduct';
        $newUsageAllowance = 'newUsageAllowance';
        $lineRentalFrequency = 'lineRentalFrequency';
        $currentCallPlan = 'currentCallPlan';
        $currentLineRental = 14.99;
        $newCallPlan = 'newCallPlan';
        $newLineRental = 14.50;
        $callFeatures = 'callFeatures';
        $contractDuration = 'contractDuration';
        $newPrice = 'newPrice';
        $offerPrice = 'offerPrice';
        $offerDuration = 'offerDuration';
        $changeDate = 'changeDate';
        $isBroadbandChanging = true;
        $broadbandCost = 9.99;
        $lineRentalCost = 17.99;
        $callPlanCost = 3.99;
        $callFeatureCost = 5.99;
        $callerClassName = "TestClassName";
        $discountAmount = 1.00;
        $advertisedDownloadSpeedMbs = 1;
        $advertisedUploadSpeedMbs = 2;
        $broadbandSpeedRange = 3;
        $minimumEstimatedDownloadSpeedMbs = 4;
        $maximumEstimatedDownloadSpeedMbs = 5;
        $minimumEstimatedUploadSpeedMbs = 6;
        $maximumEstimatedUploadSpeedMbs = 7;
        $maximumDownloadSpeedMbs = 8;
        $guaranteedSpeedAvailable = 9;
        $guaranteedSpeedValue = 10;
        $isSogea = true;

        $populatedResults = array(
            'currentDualPlay' => $currentDualPlay,
            'usageAllowanceFixed' => $usageAllowFixed,
            'newDirectDebit' => $newDirectDebit,
            'newDirectDebitReady' => $newDirectDebitReady,
            'recontracted' => $recontracted,
            'specialOffer' => $specialOffer,
            'currentBroadbandProduct' => $currentBroadbandProduct,
            'newBroadbandProduct' => $newBroadbandProduct,
            'newUsageAllowance' => $newUsageAllowance,
            'lineRentalFrequency' => $lineRentalFrequency,
            'currentCallPlan' => $currentCallPlan,
            'currentLineRental' => $currentLineRental,
            'newCallPlan' => $newCallPlan,
            'newLineRental' => $newLineRental,
            'callFeatures' => $callFeatures,
            'contractDuration' => $contractDuration,
            'newPrice' => $newPrice,
            'offerPrice' => $offerPrice,
            'offerDuration' => $offerDuration,
            'changeDate' => $changeDate,
            'isBroadbandChanging' => $isBroadbandChanging,
            'broadbandCost' => $broadbandCost,
            'lineRentalCost' => $lineRentalCost,
            'callPlanCost' => $callPlanCost,
            'callFeatureCost' => $callFeatureCost,
            'callerClassName' => $callerClassName,
            'discountAmount' => $discountAmount,
            'advertisedDownloadSpeedMbs' => $advertisedDownloadSpeedMbs,
            'advertisedUploadSpeedMbs' => $advertisedUploadSpeedMbs,
            'broadbandSpeedRange' => $broadbandSpeedRange,
            'minimumEstimatedDownloadSpeedMbs' => $minimumEstimatedDownloadSpeedMbs,
            'maximumEstimatedDownloadSpeedMbs' => $maximumEstimatedDownloadSpeedMbs,
            'minimumEstimatedUploadSpeedMbs' => $minimumEstimatedUploadSpeedMbs,
            'maximumEstimatedUploadSpeedMbs' => $maximumEstimatedUploadSpeedMbs,
            'maximumDownloadSpeedMbs' => $maximumDownloadSpeedMbs,
            'guaranteedSpeedAvailable' => $guaranteedSpeedAvailable,
            'guaranteedSpeedValue' => $guaranteedSpeedValue,
            'installationTypeFTTP' => null,
            'accessTechnologyChanging' => null,
            'isSogea' => $isSogea,
            'isFttc' => null,
            'isFttp' => null
        );

        $mockConfEmail = $this->getMock(
            'AccountChange_ConfirmationEmailHandler',
            array(
                'getIspAutomatedEmail',
            )
        );

        $mockIspAutomatedEmail = $this->getMock(
            'IspAutomatedEmail_IspAutomatedEmail',
            array(
                'prepareIspAutomatedEmail',
                'send'
            ),
            array(),
            '',
            false
        );
        $mockIspAutomatedEmail
            ->expects($this->once())
            ->method('prepareIspAutomatedEmail')
            ->with(
                $emailName,
                $populatedResults
            );

        $mockIspAutomatedEmail
            ->expects($this->once())
            ->method('send');

        $mockConfEmail
            ->expects($this->once())
            ->method('getIspAutomatedEmail')
            ->will($this->returnValue($mockIspAutomatedEmail));

        $mockConfEmail = $this->setupObject($mockConfEmail);

        $mockConfEmail->sendEmail($serviceId);
    }

    /**
     * Covering last part of send email
     *
     * @covers  AccountChange_EmailHandler::sendEmail
     *
     * @return void
     */
    public function testSendEmailThrowsException()
    {
        $serviceId = 3245422;
        $emailName = 'upgrade_confirmation';

        $currentDualPlay = true;
        $usageAllowFixed = true;
        $newDirectDebit = true;
        $newDirectDebitReady = true;
        $recontracted = true;
        $specialOffer = true;
        $currentBroadbandProduct = 'currentBroadbandProduct';
        $newBroadbandProduct = 'newBroadbandProduct';
        $newUsageAllowance = 'newUsageAllowance';
        $lineRentalFrequency = 'lineRentalFrequency';
        $currentCallPlan = 'currentCallPlan';
        $currentLineRental = 14.99;
        $newCallPlan = 'newCallPlan';
        $newLineRental = 14.50;
        $callFeatures = 'callFeatures';
        $contractDuration = 'contractDuration';
        $newPrice = 'newPrice';
        $offerPrice = 'offerPrice';
        $offerDuration = 'offerDuration';
        $changeDate = 'changeDate';
        $isBroadbandChanging = true;
        $broadbandCost = 9.99;
        $lineRentalCost = 17.99;
        $callPlanCost = 3.99;
        $callFeatureCost = 5.99;
        $callerClassName = "TestClassName";
        $discountAmount = 1.00;
        $isSogea = true;

        $populatedResults = array(
            'currentDualPlay' => $currentDualPlay,
            'usageAllowanceFixed' => $usageAllowFixed,
            'newDirectDebit' => $newDirectDebit,
            'newDirectDebitReady' => $newDirectDebitReady,
            'recontracted' => $recontracted,
            'specialOffer' => $specialOffer,
            'currentBroadbandProduct' => $currentBroadbandProduct,
            'newBroadbandProduct' => $newBroadbandProduct,
            'newUsageAllowance' => $newUsageAllowance,
            'lineRentalFrequency' => $lineRentalFrequency,
            'currentCallPlan' => $currentCallPlan,
            'currentLineRental' => $currentLineRental,
            'newCallPlan' => $newCallPlan,
            'newLineRental' => $newLineRental,
            'callFeatures' => $callFeatures,
            'contractDuration' => $contractDuration,
            'newPrice' => $newPrice,
            'offerPrice' => $offerPrice,
            'offerDuration' => $offerDuration,
            'changeDate' => $changeDate,
            'isBroadbandChanging' => $isBroadbandChanging,
            'broadbandCost' => $broadbandCost,
            'lineRentalCost' => $lineRentalCost,
            'callPlanCost' => $callPlanCost,
            'callFeatureCost' => $callFeatureCost,
            'callerClassName' => $callerClassName,
            'discountAmount' => $discountAmount,
            'advertisedDownloadSpeedMbs' => 1,
            'advertisedUploadSpeedMbs' => 2,
            'broadbandSpeedRange' => 3,
            'minimumEstimatedDownloadSpeedMbs' => 4,
            'maximumEstimatedDownloadSpeedMbs' => 5,
            'minimumEstimatedUploadSpeedMbs' => 6,
            'maximumEstimatedUploadSpeedMbs' => 7,
            'maximumDownloadSpeedMbs' => 8,
            'guaranteedSpeedAvailable' => true,
            'guaranteedSpeedValue' => 10,
            'installationTypeFTTP' => null,
            'accessTechnologyChanging' => null,
            'isSogea' => $isSogea,
            'isFttc' => null,
            'isFttp' => null
        );

        $mockConfEmail = $this->getMock(
            'AccountChange_ConfirmationEmailHandler',
            array(
                'getIspAutomatedEmail',
                'raiseAutoProblem'
            )
        );

        $mockIspAutomatedEmail = $this->getMock(
            'IspAutomatedEmail_IspAutomatedEmail',
            array(
                'prepareIspAutomatedEmail',
                'send'
            ),
            array(),
            '',
            false
        );

        $mockIspAutomatedEmail
            ->expects($this->once())
            ->method('prepareIspAutomatedEmail')
            ->with(
                $emailName,
                $populatedResults
            );

        $mockIspAutomatedEmail
            ->expects($this->once())
            ->method('send')
            ->will($this->throwException(new RuntimeException('exception')));

        $mockConfEmail
            ->expects($this->once())
            ->method('getIspAutomatedEmail')
            ->will($this->returnValue($mockIspAutomatedEmail));

        $mockConfEmail
            ->expects($this->once())
            ->method('raiseAutoProblem');

        $mockConfEmail = $this->setupObject($mockConfEmail);

        $mockConfEmail->sendEmail($serviceId);
    }

    /**
     * Covering raiseAutoProblem
     *
     * @covers  AccountChange_EmailHandler::raiseAutoProblem
     *
     * @return void
     */
    public function testRaiseAutoProblem()
    {
        $serviceId = 3245422;

        $mockAutoProblem = $this->getMock(
            'AutoProblem_AutoProblemClient',
            array(
                'prepareAutoProblem',
                'raiseProblem'
            )
        );

        $mockPomsClient = $this->getMock(
            'PomsClient_AutoProblem',
            array(
                'raiseProblem'
            ),
            array(),
            '',
            false
        );

        $mockPomsClient
            ->expects($this->once())
            ->method('raiseProblem')
            ->will($this->returnValue(5));

        $mockAutoProblem
            ->expects($this->once())
            ->method('prepareAutoProblem')
            ->will($this->returnValue($mockPomsClient));

        $mockBuinessActor = $this->getMock(
            'Auth_BusinessActor',
            array('getActorId')
        );

        $mockBuinessActor
            ->expects($this->once())
            ->method('getActorId')
            ->will($this->returnValue(5));

        $mockConfEmail = $this->getMock(
            'AccountChange_ConfirmationEmailHandler',
            array(
                'getAutoProblem',
                'getLoggedInOrScriptActor'
            )
        );

        $mockConfEmail
            ->expects($this->once())
            ->method('getAutoProblem')
            ->will($this->returnValue($mockAutoProblem));

        $mockConfEmail
            ->expects($this->once())
            ->method('getLoggedInOrScriptActor')
            ->will($this->returnValue($mockBuinessActor));

        $mockConfEmail->raiseAutoProblem(new RuntimeException('exception'), $serviceId);
    }
}
