server: coredb
role: slave
rows: multiple
statement:

   SELECT d.intDiscountId AS DiscountId,
          d.intComponentId,
          d.vchPromotionCode AS StrPromotionCode,
          d.intDiscountTypeId AS DiscountTypeId,
          d.decValue AS Value,
          d.vchDescription AS Description,
          d.intPresetDiscountId AS PresetDiscountId,
          d.dteCreatedDate AS CreatedDate,
          dt.vchHandle AS DiscountType,
          dc.intCancellationReasonId,
          dc.vchAdditionalReason AS AdditionalReason,
          dc.vchCancelledBy,
          cr.vchHandle AS CancellationReason,
          dr.intSalesInvoiceId AS InvoiceId,
          dr.dteDueDate AS DueDate,
          dr.intDiscountRedemptionId AS DiscountRedemptionId,
          ep.forename,
          ep.surname,
   CONCAT (ep1.forename," ",ep1.surname) AS CancelledBy,
   CONCAT (i.vchInvoicePrefix, ii.intInvoiceRef) AS strInvoiceRef,
          d.intProductComponentInstanceID as ProductComponentInstanceID
     FROM financial.tblDiscount d
LEFT JOIN financial.tblDiscountRedemption dr ON d.intDiscountId = dr.intDiscountId
LEFT JOIN financial.tblInvoiceIssuer AS ii ON ii.intSalesInvoiceId = dr.intSalesInvoiceId
LEFT JOIN financial.tblIssuer AS i ON i.intCompanyNo = ii.intCompanyNo
LEFT JOIN financial.tblDiscountCancellation dc ON dr.intDiscountRedemptionId = dc.intDiscountRedemptionId
LEFT JOIN financial.tblDiscountType dt ON dt.intDiscountTypeId = d.intDiscountTypeId
LEFT JOIN financial.tblCancellationReason cr ON cr.intCancellationReasonId = dc.intCancellationReasonId
LEFT JOIN php_lib.employee_profile ep ON d.vchCreatedBy = ep.user_id
LEFT JOIN php_lib.employee_profile ep1 ON dc.vchCancelledBy = ep1.user_id
    WHERE d.intComponentId = :componentId
      AND d.intProductComponentInstanceID = :productComponentInstanceId
      AND dc.intCancellationReasonId IS NULL
      AND dr.dteDueDate > NOW();