<?php
/**
 * <AUTHOR> <<EMAIL>>
 */


class AccountChange_NoCeaseRequestPolicyTest extends PHPUnit_Framework_TestCase
{
    const TEST_SERVICE_ID = 1234;

    /** @var Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject $mockBusinessActor */
    private $mockBusinessActor;

    /** @var CeaseCommunications_UnsolicitedCease|PHPUnit_Framework_MockObject_MockObject $mockCeaseObject */
    private $mockCeaseObject;

    /**
     * @return void
     */
    public function setUp()
    {
        $this->mockBusinessActor = $this->mockBusinessActor();
        $this->mockCeaseObject = $this->mockCeaseObject();
    }

    /**
     * @dataProvider dataForTestValidate
     * @param string|null $ceaseId        cease ID or null
     * @param bool        $expectedResult expected result
     * @return void
     */
    public function testValidate($ceaseId, $expectedResult)
    {
        $this->mockCeaseObject->expects($this->once())
            ->method('loadUsingServiceId')
            ->with(static::TEST_SERVICE_ID);
        $this->mockCeaseObject->expects($this->once())
            ->method('getUnsolicitedCeaseId')
            ->willReturn($ceaseId);

        $policy = $this->initialisePolicy();

        $policy->expects($this->once())
            ->method('getCeaseObject')
            ->willReturn($this->mockCeaseObject);

        $this->assertEquals($expectedResult, $policy->validate());
    }

    /**
     * @return array
     */
    public function dataForTestValidate()
    {
        return [
            'Cease Request' => [
                'ceaseId' => '2022-01-01',
                'expectedResult' => false,
            ],
            'No Cease Request' => [
                'ceaseId' => null,
                'expectedResult' => true,
            ],
        ];
    }

    /**
     * @return void
     */
    public function testGetErrorCodeAndFailure()
    {
        $policy = $this->initialisePolicy();

        $this->assertEquals($policy::ERROR_MESSAGE, $policy->getFailure());
        $this->assertEquals($policy::ERROR_CODE, $policy->getErrorCode());
    }

    /**
     * @return AccountChange_NoCeaseRequestPolicy|PHPUnit_Framework_MockObject_MockObject
     */
    private function initialisePolicy()
    {
        return $this->getMockBuilder(AccountChange_NoCeaseRequestPolicy::class)
            ->setConstructorArgs([
                $this->mockBusinessActor,
                false,
                false,
                [
                    'serviceId' => static::TEST_SERVICE_ID,
                ]
            ])->setMethods(['getCeaseObject'])
            ->getMock();
    }

    /**
     * @return Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockBusinessActor()
    {
        return $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->getMock();
    }

    /**
     * @return CeaseCommunications_UnsolicitedCease|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockCeaseObject()
    {
        return $this->getMockBuilder(CeaseCommunications_UnsolicitedCease::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'loadUsingServiceId',
                'getUnsolicitedCeaseId',
            ])->getMock();
    }
}
