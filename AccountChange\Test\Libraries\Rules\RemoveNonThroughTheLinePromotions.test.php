<?php
use Plusnet\C2mApiClient\Entity\Promotion;

class RemoveNonThroughTheLinePromotionsTest extends PHPUnit_Framework_TestCase
{

    /**
     * @test
     */
    public function itRemovesPromotionsThatAreNotThroughTheLine()
    {
        $promotion1 = new Promotion();
        $promotion1->setCode('Promo1');
        $promotion1->setIsVisibleOnSalesHomePage(false);
        $promotion2 = new Promotion();
        $promotion2->setCode('Promo2');
        $promotion2->setIsVisibleOnSalesHomePage(true);
        $promotion3 = new Promotion();
        $promotion3->setCode('Promo3');
        $promotion3->setIsVisibleOnSalesHomePage(false);
        $promotion4 = new Promotion();
        $promotion4->setCode('Promo4');
        $promotion4->setIsVisibleOnSalesHomePage(true);
        $promotion5 = new Promotion();
        $promotion5->setCode('Promo5');
        $promotion5->setIsVisibleOnSalesHomePage(false);


        $promotions = [$promotion1, $promotion2, $promotion3, $promotion4, $promotion5];

        $promotions = (new RemoveNonThroughTheLinePromotions())->handle($promotions);

        $this->assertEquals(2, count($promotions));
        $this->assertEquals('Promo2', $promotions[1]->getCode());
        $this->assertEquals('Promo4', $promotions[3]->getCode());
    }
}
