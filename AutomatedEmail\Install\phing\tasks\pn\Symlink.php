<?php
include_once 'phing/Task.php';
class Symlink extends Task {

    private $_strLink;
    private $_strTarget;
    private $_strAction;
    private $_strReturnName;
    private $_bolModule = false;
        
    public function setLink($str) {
        $this->_strLink = $str;
    }

    public function setTarget($str) {
        $this->_strTarget = $str;
    }
    
	public function setReturnName($str) {
        $this->_strReturnName = $str;
    } 
	
	public function setAction($str) {
        $this->_strAction = $str;
    }

	public function setModule($str) {
        if ($str == 'true') $this->_bolModule = true;
    }
    
    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		} 
		
		if (!$this->_strLink) {
		    throw new BuildException("You must specify the link attribute", $this->getLocation());
		} 
		
		if (!$this->_strTarget) {
		    throw new BuildException("You must specify the target attribute", $this->getLocation());
		} 		 
		
		if ($this->_bolModule) {
			$this->_strTarget = str_replace('/Install','',$this->_strTarget);
		}
		
		if(isset($this->_strAction)) {

			switch($this->_strAction) {
				
				case 'check':
					$this->_checkSymlink();
					return;
				case 'create':
					$this->_createSymlink();
					return;
			}

			throw new BuildException("$this->_strAction is not a valid action for sqlInstallDbs", $this->getLocation());		
		}
    }
    
    private function _checkSymlink()
    {
		if (file_exists($this->_strLink) && @readlink($this->_strLink) == $this->_strTarget) {
			$this->log("Symlink '$this->_strLink' to '$this->_strTarget' already exists");
			$this->project->setProperty($this->_strReturnName, true);
			return true;
		}
    }
    	
    private function _createSymlink()
    {
        if(!$this->_checkSymlink()) {
        	
        	if (false === @symlink($this->_strTarget, $this->_strLink)) {
	            $this->log("Cannot symlink '$this->_strLink' to '$this->_strTarget'");
		        $this->project->setProperty($this->_strReturnName, true);
        	}
        }
    }
    	
    	
    
    
}
