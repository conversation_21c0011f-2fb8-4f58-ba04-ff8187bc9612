<?php

use Plusnet\PriceProtected\Services\Factory\StatusServiceFactory;
use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;
use AccountChange_BillingApi_BasePriceHelper as BasePriceHelper;

/**
 * Product Configuration for Wlr component
 *
 * Holds information about a service definition product than an account may have
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_Wlr.class.php,v 1.7.2.3 2009/07/15 12:52:14 mstarbuck Exp $
 * @link      http://documentation.plus.net/index.php/Account_Change
 * @since     File available since 2008-08-19
 */
/**
 * Product Wlr class
 *
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Product_Wlr extends AccountChange_Product_ServiceComponent
{
    /**
     * Keys for base price and base price in contract values returned from Billing API getCustomerAvailableProducts()
     *
     * @var string
     */
    const CURRENT_BASE_PRICE_KEY             = 'currentBasePrice';
    const CURRENT_BASE_PRICE_IN_CONTRACT_KEY = 'currentBasePriceInContract';

    /**
     * CLI Number
     *
     * @var string
     */
    private $strCliNumber;

    /**
     * Are we taking payment for the product change
     *
     * @var boolean
     */
    private $bolTakePayment = false;

    /**
     * Old service definition id
     *
     * @var int
     **/
    private $oldSdi;

    /**
     * New service definition id
     *
     * @var int
     **/
    private $newSdi;

    /**
     * Old wlr service component id
     *
     * @var int
     **/
    private $oldWlrScid;

    /**
     * New wlr service component id
     *
     * @var int
     **/
    private $newWlrScid;

    /**
     * Old adsl INTERNET_CONNECTION component id
     *
     * @var int
     **/
    private $oldAdslComponentId;

    /**
     * Old Wlr component id
     *
     * @var int
     **/
    private $oldWlrComponentId;

    /**
     * New Adsl scid for INTERNET_CONNECTION
     *
     * @var int
     **/
    private $newAdslScid;

    /**
     * If the wlr component is an annual one, we need to mark that so we can
     * raise a ticket
     *
     * @var boolean
     */
    private $bolEssentialProduct = false;

    /**
     * Allows the calling code to override the schedule date.
      *
     * @var unix_timestamp
     */
    protected $uxtScheduledDate = null;

    /**
     * Holds the value which define whether phone order add request need to process manually or not
     *
     * If the add wlr component is requested and account change also placing broadband order
     * then we need to process wlr order after broadband order.
     * So we need to process the wlr order manually
     *
     *
     * @var boolean
     */
    private $manuallyAddPhone = false;

    /**
     * Flag to indicate if product change originated from a house move
     *
     * @var boolean
     */
    protected $bolHousemove = false;

    /**
     * Flag to indicate if customer is changing onto a new broadband contract - this affects the line rental price
     *
     * @var boolean
     */
    protected $newContract = null;

    /**
     * Desired contract length
     *
     * @var string
     */
    private $strContract;

    /**
     * Getter for the new wlr product service component id
     *
     * @return int
     **/
    public function getNewWlrScid()
    {
        return $this->newWlrScid;
    }

    /**
     * Initialise the Service Component Product Configuration object
     *
     * @param int $intServiceComponentId Service component id
     * @param int $intAction Action to initialise
     * @param array $arrOptions An array of optional params
     *
     * @return void
     * @throws AccountChange_Product_ManagerException
     */
    public function initialise($intServiceComponentId, $intAction, array $arrOptions)
    {
        parent::initialise($intServiceComponentId, $intAction, $arrOptions);

        if (isset($arrOptions['strCliNumber'])) {
            $this->strCliNumber = $arrOptions['strCliNumber'];
        }

        if (isset($arrOptions['bolEssentialProduct'])) {
            $this->bolEssentialProduct = $arrOptions['bolEssentialProduct'];
        }

        if (isset($arrOptions['bolTakePayment'])) {
            $this->bolTakePayment = $arrOptions['bolTakePayment'];
        }

        //do we want to customise the schduled date?
        if (isset($arrOptions['uxtScheduledDate'])) {
            $this->uxtScheduledDate = I18n_Date::fromTimestamp($arrOptions['uxtScheduledDate']);
        }

        //do we want to customise the schduled date?
        if (isset($arrOptions['manuallyAddPhone'])) {
            $this->manuallyAddPhone = $arrOptions['manuallyAddPhone'];
        }

        // Old service definition id for broadband
        if (isset($arrOptions['oldSdi'])) {
            $this->oldSdi = $arrOptions['oldSdi'];
        }

        // New service defintion id for broadband
        if (isset($arrOptions['newSdi'])) {
            $this->newSdi = $arrOptions['newSdi'];
        }

        // Old wlr service component id
        if (isset($arrOptions['oldWlrScid'])) {
            $this->oldWlrScid = $arrOptions['oldWlrScid'];
        }

        // New wlr service component id
        if (isset($arrOptions['newWlrScid'])) {
            $this->newWlrScid = $arrOptions['newWlrScid'];
        }

        // Old adsl component id
        if (isset($arrOptions['oldAdslComponentId'])) {
            $this->oldAdslComponentId = $arrOptions['oldAdslComponentId'];
        }

        // Old wlr component id
        if (isset($arrOptions['oldWlrComponentId'])) {
            $this->oldWlrComponentId = $arrOptions['oldWlrComponentId'];
        }

        // New adsl scid
        if (isset($arrOptions['newAdslScid'])) {
            $this->newAdslScid = $arrOptions['newAdslScid'];
        }

        // HouseMove
        if (isset($arrOptions['bolHousemove'])) {
            $this->bolHousemove = $arrOptions['bolHousemove'];
        }

        if (isset($arrOptions['newContract'])) {
            $this->newContract = $arrOptions['newContract'];
        }

        // Store the desired contract length
        if (isset($arrOptions['strContract'])) {
            $this->strContract = $arrOptions['strContract'];
        } else {
            $this->strContract = '';
        }
    }

    /**
     * Is the product going to be scheduled for the change
     *
     * @return boolean
     */
    public function isScheduled()
    {
        $registry = AccountChange_Registry::instance();
        $eventDate = $registry->getEntry('backDatedDate');
        // back dated cant be scheduled
        if (empty($eventDate) && (($this->bolScheduleChange)
            || ($this->intAccountChangeOperation == AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE
            && !$this->bolEssentialProduct))
        ) {
            return true;
        }

        return false;
    }

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration Config object
     *
     * @return void
     * @throws AccountChange_AccountConfigurationException
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {
            $this->objNewProductConfiguration = $objAccountConfiguration->getProductConfigurationByType(
                AccountChange_Product_Manager::PRODUCT_TYPE_WLR
            );
        }
    }

    /**
     * Match the product configuration with another product configuration
     *
     * This can be used if you have the matching object, and you need to perform some checks
     *
     * @param AccountChange_Product_Configuration $objConfiguration Configuration
     * @throws AccountChange_Product_Exception
     * @return void
     */
    public function setMatchingProductConfigurationManually(AccountChange_Product_Configuration $objConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {
            if (!$objConfiguration instanceof AccountChange_Product_Wlr) {
                throw new AccountChange_Product_Exception(
                    'Matching product is not the same product type',
                    AccountChange_Product_Exception::ERR_MATCHING_PRODUCT_NOT_VALID
                );
            }

            $this->objNewProductConfiguration = $objConfiguration;
            $this->setAccountChange();
        }
    }

    /**
     * Create the WLR component for the account
     *
     * We need to not signup the Wlr component if it is part of the essential bundle
     *
     * @throws AccountChange_Product_ManagerException
     * @return boolean
     * @throws AccountChange_Action_ManagerException
     */
    public function create()
    {
        $isBboToDslMove = $this->isBBOToDslMove();

        if (empty($this->strCliNumber) && !$isBboToDslMove) {
            throw new AccountChange_Product_ManagerException(
                'A valid Cli number is needed to perform signup on Wlr',
                AccountChange_Product_ManagerException::ERR_CLI_ERROR
            );
        }

        $this->includeLegacyFiles();

        if ($this->manuallyAddPhone == true && !$this->bolHousemove && !$isBboToDslMove) {
            // LTC-4567 - LtcContracts should still be called
            $actions = array(AccountChange_Action_Manager::ACTION_LTC_CONTRACTS);
            $actionManager = $this->buildActionManager($actions);
            $actionManager->execute();

            return $this->raiseManaullyAddPhoneRequestTicket();
        }

        $arrProduct = $this->getServiceComponentDetails($this->intServiceComponentId);

        $notice = "This customer has requested {$arrProduct['strName']} via the account change tool.\n";
        $this->_serviceNotices[] = new AccountChange_ServiceNotice($notice);

        return $this->doSignup(
            $this->intServiceId,
            $this->getProductId(),
            $this->strCliNumber,
            $this->getContract()
        );
    }

    /**
     * Wrapper function for CWlrProduct::doSignup..
     * This allows us to test other functionality and mock this method for unit tests
     *
     * @param int $intServiceId Service id
     * @param int $intServiceComponentId Service component id
     * @param string $strCliNumnber Cli number
     * @param AccountChange_Product_ServiceComponentContract $objContract Contract
     *
     * @return boolean
     * @throws AccountChange_Product_ManagerException
     * @throws AccountChange_Action_ManagerException
     */
    protected function doSignup(
        $intServiceId,
        $intServiceComponentId,
        $strCliNumnber,
        AccountChange_Product_ServiceComponentContract $objContract
    ) {
        Dbg_Dbg::write(
            "AccountChange_Product_Wlr::doSignup for component {$intServiceComponentId}
            with the cli of {$strCliNumnber}",
            'AccountChange'
        );

        $this->includeLegacyFiles();

        $arrOptions['strContractHandle'] = $objContract->getContractLengthHandle();
        $arrOptions['strPaymentFrequencyHandle'] = $objContract->getPaymentFrequencyHandle();
        $arrOptions['arrProvisionDetails']['orderOrigin'] = $this->bolHousemove ? Wlr3_OrderOrigin::HOUSE_MOVE : Wlr3_OrderOrigin::ACCOUNT_CHANGE;
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        $objProduct = $this->makeNewWlrProduct(0);

        $registry = AccountChange_Registry::instance();
        $userWantsCallerDisplay = $registry->getEntry('callerDisplay');
        $callerDisplayFeatureHandle = null;
        if ($userWantsCallerDisplay) {
            $callerDisplayFeatureHandle = AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE;
        }

        $objWlrProduct = $objProduct->doSignup(
            $intServiceId,
            $intServiceComponentId,
            $strCliNumnber,
            array($callerDisplayFeatureHandle),
            $arrOptions
        );

        if ($this->shouldSendConfirmationEmail()) {
            $this->sendConfirmationEmail(
                [
                    'arrSelectedWlr' => [
                        'strNewProduct' => $objWlrProduct->getFullName()
                    ]
                ]
            );
        }

        $this->intComponentId = $objWlrProduct->getComponentID();

        if (!$this->bolHousemove) {
            $actions = array(
                AccountChange_Action_Manager::ACTION_LINE_RENTAL_SAVER_ADD,
                AccountChange_Action_Manager::ACTION_LTC_CONTRACTS,
                AccountChange_Action_Manager::ACTION_CONSENT
            );
        } else {
            $actions = array(
                AccountChange_Action_Manager::ACTION_LINE_RENTAL_SAVER_ADD,
                AccountChange_Action_Manager::ACTION_CONSENT
            );
        }
        $actionManager = $this->buildActionManager($actions);
        $actionManager->execute();

        if ($objWlrProduct->getError()) {
            error_log(
                'An issue occured during the account change process for Wlr: ' .
                'Message = ' .     $objWlrProduct->getError()->getErrorMessage() . ' ::: ' .
                'Source File = ' . $objWlrProduct->getError()->getSourceFile() . ' ::: ' .
                'Line Number = ' . $objWlrProduct->getError()->getLineNumber()
            );
        }

        return $objWlrProduct->getComponentID();
    }

    /**
     * @param $intComponentID
     *
     * @return CWlrProduct
     * @codeCoverageIgnore
     */
    protected function makeNewWlrProduct($intComponentID)
    {
        return new CWlrProduct($intComponentID);
    }

    /**
     * @param $componentId - component id
     * @return CProduct
     */
    protected function makeNewStaticProduct($componentId)
    {
        return new CProduct($componentId);
    }

    /**
     * @return CProductChange
     */
    protected function makeNewStaticProductChange()
    {
        return new CProductChange();
    }

    /**
     * @param $componentId - component id
     * @return CComponent
     */
    protected function makeNewComponent($componentId)
    {
        return new CComponent($componentId);
    }


    /**
     * Change the WLR component for the account
     *
     * Depending on the flags we have sent into the product,
     * whilst being created, we may be scheduling it,
     * performing the action straight away, or just raising a ticket
     * (In the case of Plusnet Essential wlr component)
     *
     * @throws AccountChange_Product_ManagerException
     * @return void
     */
    public function change()
    {
        if ($this->objServiceComponentContract == null) {
            throw new AccountChange_Product_ManagerException(
                'Wlr product changes need a service component contract - one was not specified',
                AccountChange_Product_ManagerException::ERR_MISSING_SERVICE_COMPONENT_CONTRACT
            );
        }

        // allows only instant product change if it's backdate
        $eventDate = $this->getBackdatedDate();
        if ($this->bolScheduleChange && empty($eventDate)) {
            if ($this->bolEssentialProduct) {
                $this->scheduleEssentialProductChange();
            } else {
                $this->scheduleChange();
            }
        } else {
            if ($this->intAccountChangeOperation === AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE
                && !$this->bolEssentialProduct && empty($eventDate)
            ) {
                $this->scheduleChange();
            } else {
                $this->changeAccount();
            }
        }
    }

    /**
     * @return mixed
     */
    protected function getBackdatedDate()
    {
        $registry = AccountChange_Registry::instance();
        return $registry->getEntry('backDatedDate');
    }

    /**
     * Change the product instantly
     *
     * @return void
     * @throws AccountChange_Action_ManagerException
     * @throws AccountChange_CallFeature_CallFeatureProvisioningException
     * @throws AccountChange_CallFeature_ExistingCallFeatureException
     * @throws AccountChange_CallFeature_InactiveWlrComponentException
     */
    protected function changeAccount()
    {
        Dbg_Dbg::write("AccountChange_Product_Wlr::changeAccount", 'AccountChange');
        $this->includeLegacyFiles();

        $objStaticWlrProduct = $this->makeNewWlrProduct(0);

        $intTariffId = $this->objNewProductConfiguration->getContract()->fetchTariffId(
            $this->objNewProductConfiguration->getProductId()
        );

        $uxtContractEndDate = $this->objNewProductConfiguration->getContract()->getContractEndDate();
        $arrOptionalProductComponents = $this->getOptionalProductComponents();
	    if (!$this->bolHousemove) {
            $callerDisplayFlag =
                $this->addOrRemoveCallerDisplayForNewWlrComponent($arrOptionalProductComponents, false);
	    }

        $objNewWlrComponent = $objStaticWlrProduct->create(
            $this->intServiceId,
            $this->objNewProductConfiguration->getProductId(),
            $intTariffId,
            $arrOptionalProductComponents,
            $uxtContractEndDate
        );

        $this->objNewProductConfiguration->setComponentId($objNewWlrComponent->getComponentID());

        $objStaticWlrProduct->changeAccountType(
            $this->intComponentId,
            $this->objNewProductConfiguration->getComponentId(),
            $this->bolScheduleChange,
            time()
        );

        //Add Feature for SI
        if ($callerDisplayFlag) {
            $this->callerDisplaySendWlrOrder($objNewWlrComponent->getComponentID());
        }

        $registry = AccountChange_Registry::instance();
        if ($this->bolHousemove || ($this->isPhoneOnlyProductChange() && !$registry->getEntry('phoneContractChange'))) {
            $actions = array(
                AccountChange_Action_Manager::ACTION_LINE_RENTAL_SAVER_ADD,
                AccountChange_Action_Manager::ACTION_CONSENT
            );
        } else {
            $actions = array(
                AccountChange_Action_Manager::ACTION_LINE_RENTAL_SAVER_ADD,
                AccountChange_Action_Manager::ACTION_LTC_CONTRACTS,
                AccountChange_Action_Manager::ACTION_CONSENT
            );
        }

        $actionManager = $this->buildActionManager($actions);
        $actionManager->execute();
    }

    /**
     * Schedule the change to take place
     *
     * Currently this places the event in the dbSystemEvents.tblProductChange
     * Which then gets picked up by PerformProductChanges.php
     *
     * @return void
     * @throws AccountChange_Action_ManagerException
     * @throws AccountChange_CallFeature_ExistingCallFeatureException
     * @throws AccountChange_CallFeature_InactiveWlrComponentException
     * @throws AccountChange_Product_ManagerException
     */
    protected function scheduleChange()
    {
        Dbg_Dbg::write('AccountChange_Product_Wlr::scheduleChange', 'AccountChange');
        $this->includeLegacyFiles();

        // Objects that represent a static function call in legacy code! I'm sorry!!
        $objStaticWlrProduct       = $this->makeNewWlrProduct(0);
        $objStaticProduct          = $this->makeNewStaticProduct(0);
        $objStaticProductChange    = $this->makeNewStaticProductChange();

        $intTariffId = $this->objNewProductConfiguration->getContract()->fetchTariffId(
            $this->objNewProductConfiguration->getProductId()
        );

        $objCoreService = $this->getCoreService();
        $uxtInvoiceDate = $objCoreService->getNextInvoiceDate();
        if ($uxtInvoiceDate->fShort() == I18n_Date::now()->fShort()) {
            $uxtInvoiceDate = I18n_Date::fromString(UserdataServiceGetNextNextInvoiceDate($this->intServiceId));
        }

        $arrInvoiceDate = explode('-', $uxtInvoiceDate);
        $isoDate = sprintf("%4d-%02d-%02d", $arrInvoiceDate[2], $arrInvoiceDate[1], $arrInvoiceDate[0]);
        $uxtScheduledDate = strtotime($isoDate);

        $contractStatus = $this->getContractStatus();
        $registry = AccountChange_Registry::instance();
        $willAccountChangeResultInRecontract = $registry->getEntry('willAccountChangeResultInRecontract');

        $registry->setEntry(
            'willCustomerBeOnAFixedPriceContractFollowingChange',
            $contractStatus->isServicePriceProtected($this->intServiceId, false) && $willAccountChangeResultInRecontract
        );


        //override the scheduled date
        if (!is_null($this->uxtScheduledDate)) {
            $uxtScheduledDate = $this->uxtScheduledDate->getTimestamp();
        }

        $arrOptionalProductComponents = $this->getOptionalProductComponents();
	    if (!$this->bolHousemove) {
           $this->addOrRemoveCallerDisplayForNewWlrComponent($arrOptionalProductComponents, true);
	    }

        // Create the new component
        $objNewWlrComponent = $objStaticWlrProduct->create(
            $this->intServiceId,
            $this->objNewProductConfiguration->getProductId(),
            $intTariffId,
            $arrOptionalProductComponents,
            $uxtInvoiceDate
        );

        $schedulingHelper = $this->getSchedulingHelper();
        if ($this->bolHousemove) {
            $changeDate = null;
        } else {
            $changeDate = $schedulingHelper->calculateScheduledChangeDate($this->intServiceId);
        }

        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $phoneContractChange = $registry->getEntry('phoneContractChange');
        if ($phoneContractChange === true) {
            userdata_service_schedule_add(
                $this->intServiceId,
                $this->oldSdi,
                $changeDate,
                Auth_Auth::getCurrentLogin()->getBusinessActor()->getExternalUserId(),
                0,
                '',
                $this->strContract,
                $dbAdaptor->getServiceComponentDetailsForService($this->intServiceId)['intTariffID'],
                null
            );
        }

        $intNewComponentId = $objNewWlrComponent->getComponentID();
        $this->objNewProductConfiguration->setComponentId($intNewComponentId);

        // Transfer any existing line rental saver subscriptions to the new wlr component
        // should it exist..
        $actions = array(AccountChange_Action_Manager::ACTION_LINE_RENTAL_SAVER_ADD);
        $actionManager = $this->buildActionManager($actions);
        $actionManager->execute();

        $objStaticProduct->cancelAllOutstandingProductScheduledActions($this->intComponentId);

        $objStaticProductChange->create(
            $this->intComponentId,
            $intNewComponentId,
            $uxtScheduledDate,
            true,
            $uxtInvoiceDate,
            $this->bolHousemove
        );

        $this->createServiceNotice($intNewComponentId, $this->intComponentId, $uxtScheduledDate);

        $strNewComponentName = $objNewWlrComponent->getComponentName();

        $objExistingComponent = $this->makeNewComponent($this->intComponentId);
        $strExistingComponentName = $objExistingComponent->getComponentName();

        $arrEmailData['arrSelectedWlr']['strNewProduct'] = $strNewComponentName;
        $arrEmailData['arrWlrProduct']['strProductName'] = $strExistingComponentName;
        $arrEmailData['uxtScheduledDate']                = $uxtScheduledDate;

        if ($this->bolHousemove || ($this->isPhoneOnlyProductChange() && !$phoneContractChange)) {
            $actions = array(
                AccountChange_Action_Manager::ACTION_CONSENT
            );
        } else {
            $actions = array(
                AccountChange_Action_Manager::ACTION_LTC_CONTRACTS,
                AccountChange_Action_Manager::ACTION_CONSENT
            );
        }

        $actionManager = $this->buildActionManager($actions);
        $actionManager->execute();

        $this->bolScheduleChange = true;

        if ($this->shouldSendConfirmationEmail()) {
            $this->sendConfirmationEmail($arrEmailData);
        }
    }

    /**
     * Gets the contract status from StatusServiceFactory
     *
     * @return \Plusnet\PriceProtected\Services\StatusService
     */
    protected function getContractStatus()
    {
        return StatusServiceFactory::createService();
    }

    /**
     * Add or remove caller display Send Order to SI
     *
     * @param int $intNewComponentId Component ID for New WLR Component
     *
     * @return void
     * @throws AccountChange_CallFeature_CallFeatureProvisioningException
     */
    public function callerDisplaySendWlrOrder($intNewComponentId) {

        $api = $this->getCallFeatureApi();

        $api->provisionCallFeature(
            AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
            $this->intServiceId,
            $intNewComponentId
        );
    }

    /**
     * Add or remove caller display to the new WLR component, depending on the user's choice stored in the registry
     *
     * @param array   $arrOptionalProductComponents Existing optional product components, expects an array of product component handles
     * @param boolean $accountChange                Checking Account Change Instant or Schedule
     *
     * @return bool
     * @throws AccountChange_CallFeature_ExistingCallFeatureException
     * @throws AccountChange_CallFeature_InactiveWlrComponentException
     */
    public function addOrRemoveCallerDisplayForNewWlrComponent(array &$arrOptionalProductComponents, $accountChange=false)
    {
        $registry = AccountChange_Registry::instance();
        $callerDisplayID = null;
        $userHasCallerDisplay = false;
        foreach ($arrOptionalProductComponents as $id => $component) {
            if ($component == AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE) {
                $callerDisplayID = $id;
                $userHasCallerDisplay = true;
                break;
            }
        }

        $userWantsCallerDisplay = $registry->getEntry('callerDisplay');
        $api = $this->getCallFeatureApi();
        $callerDisplayFlag = false;

        if ($userHasCallerDisplay && !$userWantsCallerDisplay) {
            // Remove caller display
            unset($arrOptionalProductComponents[$callerDisplayID]);
            // Remove Feature from SI
            $api->removeCallFeature(
                AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                $this->intServiceId
            );
        } elseif (!$userHasCallerDisplay && $userWantsCallerDisplay) {
            // Add caller display
            $arrOptionalProductComponents[] = AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE;
            if ($accountChange) {
                $api->addCallFeature(
                    AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                    $this->intServiceId
                );
            } else {
                $callerDisplayFlag = true;
            }

        }

        return $callerDisplayFlag;
    }

    /**
     * Get the call feature API
     *
     * @return AccountChange_CallFeature_Api
     * @codeCoverageIgnore
     */
    protected function getCallFeatureApi()
    {
        return new AccountChange_CallFeature_Api();
    }

    /**
     * Determines if the confirmation email should be sent by checking
     * a registry entry set in GenericSalesBundle_CampaignHelper
     *
     * @return boolean true if the email should be sent, otherwise false
     */
    public function shouldSendConfirmationEmail()
    {
        $suppressConfirmationEmail = AccountChange_Registry::instance()->getEntry(
            'bolSuppressWlrConfirmationEmail'
        );

        if ($suppressConfirmationEmail === true) {
            return false;
        }

        return true;
    }

    /**
     * Due to the way that the Plusnet Essential product was built
     * we need to schedule all account changes involving it.
     *
     * Because Wlr performs product changes instantly if upgrading,
     * and schedules them when downgrading, we need to overwrite that functionality
     * and make sure that if the wlr component is an essential one,
     * that nothing happens
     *
     * This is not great, but it is what we have.
     *
     * @return void
     */
    protected function scheduleEssentialProductChange()
    {
        $this->includeLegacyFiles();

        $objExistingComponent = new CComponent($this->intComponentId);
        $strExistingComponentName = $objExistingComponent->getComponentName();

        // Objects that represent a static function call in legacy code! I'm sorry!!
        $arrNewProduct = $this->getServiceComponentDetails(
            $this->objNewProductConfiguration->getProductId()
        );
        $strNewComponentName = $arrNewProduct['strName'];

        $objCoreService = $this->getCoreService();
        $uxtInvoiceDate = $objCoreService->getNextInvoiceDate();
        $registry = AccountChange_Registry::instance();
        $userWantsCallerDisplay = $registry->getEntry('callerDisplay');

        $strTicketText = 'This Customer has chosen to change from "' .
                         $strExistingComponentName . '" to "' .
                         $strNewComponentName . '" on their next invoice date (' . $uxtInvoiceDate . ') ' .
                        ($userWantsCallerDisplay ? 'with' : 'without') . ' caller display';

        $this->_tickets[] = new AccountChange_Ticket($strTicketText, 'ACCOUNT_CHANGE');

        $this->bolScheduleChange = true;
    }

    /**
     * Raise a service notice with the scheduled event details
     *
     * @param int $intNewComponentId      New component id
     * @param int $intExistingComponentId Old component id
     * @param uxt $uxtDateDue             Change due date
     *
     * @return void
     */
    public function createServiceNotice($intNewComponentId, $intExistingComponentId, $uxtDateDue)
    {
        Dbg_Dbg::write("AccountChange_Product_Wlr::raiseScheduledTicket", 'AccountChange');
        $this->includeLegacyFiles();

        // Getting the new and old components so we can create emails/tickets etc
        $objExistingComponent = new CComponent($intExistingComponentId);
        $strExistingComponentName = $objExistingComponent->getComponentName();

        $objNewComponent = new CComponent($intNewComponentId);
        $strNewComponentName = $objNewComponent->getComponentName();

        // Raise a ticket for action
        $notice = "Product change scheduled successfully.\n";
        $notice .= 'Existing product: <b>' . $strExistingComponentName . "</b>\n";
        $notice .= 'New product: <b>' . $strNewComponentName . "</b>\n";
        $notice .= 'Due date: <b>' . I18n_Date::fromTimestamp($uxtDateDue) . "</b>\n\n";
        $notice .= "This has overwritten any scheduled changes or cancellations on home phone products.\n";

        $this->_serviceNotices[] = new AccountChange_ServiceNotice($notice);
    }

    /**
     * Getter for the additional product components
     *
     * @return array
     */
    protected function getOptionalProductComponents()
    {
        Dbg_Dbg::write("AccountChange_Product_Wlr::getOptionalProductComponents", 'AccountChange');
        $arrProductComponentOptions = array();

        $this->includeLegacyFiles();

        // Objects that represent a static function call in legacy code! I'm sorry!!
        $objStaticWlrProduct = $this->makeNewWlrProduct(0);

        // Get optional components to be transferred
        $objOldWlrProduct = $objStaticWlrProduct->createInstance($this->intComponentId);

        $arrOptionalProductComponents = $objOldWlrProduct->getOptionalProductComponents(array('ACTIVE'));
        foreach ($arrOptionalProductComponents as $objProductComponent) {
            $arrProductComponentOptions[] = $objProductComponent->getHandle();
        }

        Dbg_Dbg::write(
            'AccountChange_Product_Wlr::getOptionalProductComponents has retrieved (' .
            implode(', ', $arrProductComponentOptions) . ')',
            'AccountChange'
        );

        return $arrProductComponentOptions;
    }

    /**
     * Remove the WLR component from the account
     *
     * @return void
     */
    public function remove()
    {
        $this->removeWlrProduct($this->intComponentId);
    }

    /**
     * Wrapper function for CWlrProduct::createInstance :: ->destroy..
     * This allows us to test other functionality and mock this method for unit tests
     *
     * @param int $intOldComponentId Old component id
     *
     * @return void
     */
    protected function removeWlrProduct($intOldComponentId)
    {
        Dbg_Dbg::write(
            "AccountChange_Product_Wlr::removeWlrProduct for component {$intOldComponentId}",
            'AccountChange'
        );
        $this->includeLegacyFiles();

        $objWlrProduct = $this->makeNewWlrProduct(0);

        $objProduct = $objWlrProduct->createInstance($intOldComponentId);
        $objProduct->destroy();
    }

    /**
     * Fetch the upgrade cost details
     *
     * @param int $uxtProRataCalculationDate Pro rata calc date
     *
     * @return array
     * @throws AccountChange_AccountConfigurationException
     */
    protected function fetchUpgradeCostDetails($uxtProRataCalculationDate = null)
    {
        Dbg_Dbg::write("AccountChange_Product_Wlr::fetchUpgradeCostDetails", 'AccountChange');
        if (empty($uxtProRataCalculationDate)) {
            $uxtProRataCalculationDate = time();
        }

        $intTariffId = $this->objServiceComponentContract->fetchTariffId($this->intServiceComponentId);

        return CWlrProduct::getUpgradeCost(
            $this->intServiceId,
            $this->intServiceComponentId,
            $intTariffId,
            $uxtProRataCalculationDate
        );
    }

    /**
     * Getter for whether are taking payment or not
     *
     * @return boolean
     */
    public function isTakingPayment()
    {
        Dbg_Dbg::write("AccountChange_Product_Wlr::isTakingPayment ({$this->bolTakePayment})", 'AccountChange');

        return $this->bolTakePayment;
    }

    /**
     * Calculate the pro rata charge that needs to be taken if we are upgrading
     *
     * @return array
     * @throws AccountChange_AccountConfigurationException
     */
    public function calculateProRataCharge()
    {
        Dbg_Dbg::write("AccountChange_Product_Wlr::calculateProRataCharge", 'AccountChange');
        $arrReturn = array();

        $this->includeLegacyFiles();

        $objWlrProduct = $this->makeNewWlrProduct(0);

        $arrUpgradeCost = $objWlrProduct->getUpgradeCost(
            $this->intServiceId,
            $this->intServiceComponentId,
            $this->objServiceComponentContract->fetchTariffId($this->intServiceComponentId),
            time()
        );

        $arrUpgradeCost = $arrUpgradeCost['base'];

        if (isset($arrUpgradeCost['WlrLineRent']) && $arrUpgradeCost['WlrLineRent'] > 0) {
            $arrReturn = array(
                'strOutstandingCharges' => 'Call plan upgrade one-off fee',
                'intOutstandingFees' => new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $arrUpgradeCost['WlrLineRent']/100
                )
            );
        }

        return $arrReturn;
    }

    /**
     * Send the emails to confirm what just happened
     *
     * @param array $arrEmailData Array of email data
     *
     * @return void
     * @throws AccountChange_Product_ManagerException
     */
    public function sendConfirmationEmail(array $arrEmailData = array())
    {
        $this->includeLegacyFiles();

        $strTemplateFile = '';

        $objService = new Core_Service($this->intServiceId);
        $objServiceDefinition = new Core_ServiceDefinition($objService->getType());

        $arrSignUpMailTemplates = array (
            WLR_TALK_COMPONENT_ID                   => 'welcome_email_talk_eb003',
            WLR_TALK_ANYTIME_COMPONENT_ID           => 'welcome_email_talk_eb003'
        );

        $arrUpgradeMailTemplates = array('SUB_SUB' => $this->getPhoneProductChangeTemplate(
            $arrEmailData,
            $objServiceDefinition->isBusiness()
        ));

        $arrMailVariables = array();

        switch ($this->intAction) {
            case AccountChange_Product_Manager::ACTION_ADD:
                if (isset($arrSignUpMailTemplates[$this->intServiceComponentId])) {
                    $strTemplateFile = $arrSignUpMailTemplates[$this->intServiceComponentId];
                } elseif ($objServiceDefinition->isBusiness()) {
                    $strTemplateFile = 'welcome_email_business_phone';
                } else {
                    $strTemplateFile = 'welcome_email_home_phone';
                }
                $strProduct = '';

                if (isset($arrEmailData['arrSelectedWlr']['strNewProduct'])) {
                    $strProduct = $arrEmailData['arrSelectedWlr']['strNewProduct'];
                }

                $arrMailVariables['strProduct']         = $strProduct;
                $arrMailVariables['intTelephoneNumber'] = $this->strCliNumber;
                $arrMailVariables['strContractLength']  = $this->getContract()->getContractLengthDisplay();

                break;
            case AccountChange_Product_Manager::ACTION_CHANGE:
                $strTemplateFile                       = $arrUpgradeMailTemplates['SUB_SUB'];
                $arrMailVariables['strOldProduct']     = $arrEmailData['arrWlrProduct']['strProductName'];
                $arrMailVariables['strProduct']        = $arrEmailData['arrSelectedWlr']['strNewProduct'];
                $arrMailVariables['strContractLength']
                    = $this->objNewProductConfiguration->getContract()->getContractLengthDisplay();
                $newProductMonthlyCost = $this->getNewMonthlyCost();
                $arrMailVariables['monthlyCost'] = $newProductMonthlyCost;

                if ($this->bolScheduleChange) {
                    $arrMailVariables['uxtStartDate'] = I18n_Date::fromTimestamp($arrEmailData['uxtScheduledDate']);
                }

                break;
        }

        $this->sendEmail($this->intServiceId, $strTemplateFile, $arrMailVariables);
    }

    /**
     * @param array $emailData  email data
     * @param bool  $isBusiness is business
     * @return string
     */
    private function getPhoneProductChangeTemplate($emailData, $isBusiness)
    {
        if (isset($emailData['intOldSdi'])
            && isset($emailData['intNewSdi'])
            && $this->checkCustomerSwitchingFromGbWrToJlp(
                $emailData['intOldSdi'],
                $emailData['intNewSdi']
            )
        ) {
            return 'wlr_changing_sub_to_sub_from_gbwr';
        } elseif ($isBusiness) {
            return 'wlr_changing_sub_to_sub_business';
        } else {
            return 'wlr_changing_sub_to_sub';
        }
    }

    /**
     * Get the new monthly cost of the customer's wlr products
     *
     * @return int
     * @throws AccountChange_Product_ManagerException
     */
    private function getNewMonthlyCost()
    {
        $newProductConfig = $this->objNewProductConfiguration;

        if (!is_null($this->newContract) && $newProductConfig instanceof AccountChange_Product_Wlr) {

            return $newProductConfig->getPriceFromBillingApi();
        } else {

            return $newProductConfig->getProductCost();
        }
    }

    /**
     * Get the price of the new wlr product from BillingApi
     *
     * @return int
     * @throws AccountChange_Product_ManagerException
     */
    private function getPriceFromBillingApi()
    {
        $productOfferingPricePointPairs = array();
        $productOfferingId = $this->intServiceComponentId;

        $callPlanPricePointId = $this->getCallPlanPricePointId();
        $productOfferingPricePointPairs[] = new ProductOfferingPricePointPair($productOfferingId, $callPlanPricePointId);

        if (!$this->hasActiveLineRentalSaver()) {

            $lineRentalPricePointId = $this->getLineRentalPricePointId();
            $productOfferingPricePointPairs[] = new ProductOfferingPricePointPair($productOfferingId, $lineRentalPricePointId);
        }

        $price = 0;
        $basePrices = $this->getBasePrices($productOfferingPricePointPairs);

        foreach ($basePrices as $basePrice) {

            if ($this->newContract) {

                $price += $basePrice[self::CURRENT_BASE_PRICE_KEY];
            } else {

                $price += $basePrice[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY];
            }
        }

        return sprintf("%.2f", $price);
    }

    /**
     * Get the price point id for the new call plan
     *
     * @return string
     */
    protected function getCallPlanPricePointId()
    {
        $callPlanDetails = CWlrProduct::getCallPlanDetailsFromServiceComponentId($this->intServiceComponentId);
        return $callPlanDetails['intTariffID'];
    }

    /**
     * Get the price point id for the new line rental
     *
     * @return string
     * @throws AccountChange_Product_ManagerException
     */
    protected function getLineRentalPricePointId()
    {
        $arrProductContractOptions = (new CProduct())->getProductContractOptions(
            $this->intServiceComponentId,
            'SUBSCRIPTION',
            true
        );

        if (sizeof($arrProductContractOptions) != 1) {

            throw new AccountChange_Product_ManagerException(
                "Expected exactly one line rental tariff for new call plan but found: " .
                print_r($arrProductContractOptions, true));
        }

        return $arrProductContractOptions[0]['intTariffID'];
    }

    protected function getBasePrices($productOfferingPricePointPairs)
    {
        return BasePriceHelper::getBasePrices($this->intServiceId, $productOfferingPricePointPairs);
    }

    protected function hasActiveLineRentalSaver()
    {
        return AccountChange_Manager::hasActiveLineRentalSaver($this->intServiceId);
    }

    /**
     * Send the email
     *
     * @param int    $intServiceId     Serivce id
     * @param string $strTemplateFile  Template handle
     * @param array  $arrMailVariables Additional variables for email
     *
     * @return void
     */
    protected function sendEmail($intServiceId, $strTemplateFile, array $arrMailVariables = array())
    {
        Dbg_Dbg::write(
            'AccountChange_Product_Wlr::sendConfirmationEmail with template ' . $strTemplateFile,
            'AccountChange'
        );

        EmailHandler::sendEmail($intServiceId, $strTemplateFile, $arrMailVariables);
    }

    /**
     * Inclusion of legacy includes so we can mock this for unit tests
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/config-access.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CComponent.inc';
        require_once '/local/data/mis/database/database_libraries/CoreObjects/SystemEvents/CProductChange.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProvisionType.class.php';
    }

    /**
     * Wrapper for Mocking
     *
     * @param int $oldSdi Old service definition id
     * @param int $newSdi New service definition id
     *
     * @return bool
     */
    public function checkCustomerSwitchingFromGbWrToJlp($oldSdi, $newSdi)
    {
        return AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJlp($oldSdi, $newSdi);
    }

    /**
     * Function to raise ticket for manually add phone
     *
     * @return bool
     */
    protected function raiseManaullyAddPhoneRequestTicket()
    {
        $arrProduct = $this->getServiceComponentDetails($this->intServiceComponentId);
        $registry = AccountChange_Registry::instance();
        $userWantsCallerDisplay = $registry->getEntry('callerDisplay');
        if ($userWantsCallerDisplay) {
            $strTicketText =
            "We need to place the order for your phone service once the broadband order has completed. ".
            "Our support team will pick up this ticket and advise you of the order date once we have placed it. \n\n".
            "[INTERNAL] \n\n".
            "Account has gone from broadband only to broadband and WLR as chosen by the customer - ".
            "the broadband order has been placed, please place the WLR order once the broadband order completes. ".
            "The customer has chosen '{$arrProduct['strName']}'. \n\n".
            "Also customer has chosen 'Caller Display' call feature as well, please place this along with WLR order";
        } else {
            $strTicketText =
            "We need to place the order for your phone service once the broadband order has completed. ".
            "Our support team will pick up this ticket and advise you of the order date once we have placed it. \n\n".
            "[INTERNAL] \n\n".
            "Account has gone from broadband only to broadband and WLR as chosen by the customer - ".
            "the broadband order has been placed, please place the WLR order once the broadband order completes. ".
            "The customer has chosen '{$arrProduct['strName']}' ";
        }

        $this->_tickets[] = new AccountChange_Ticket($strTicketText, 'WLR_PROVISIONING');
        return true;
    }

    /**
     * Function to get serviceComponent details
     *
     * @param int $serviceComponentId service componentId
     *
     * @return array
     */
    protected function getServiceComponentDetails($serviceComponentId)
    {
        $staticProduct = new CProduct(0);
        return $staticProduct->getServiceComponentDetails($serviceComponentId);
    }

    /**
     * Getter for the Action Manager
     *
     * @param int $serviceId service id
     * @param array $actions actions
     * @param array $actionOptions action options
     *
     * @return AccountChange_Action_Manager
     * @throws AccountChange_Action_ManagerException
     */
    protected function getActionManager($serviceId, array $actions = array(), array $actionOptions = array())
    {
        return new AccountChange_Action_Manager($serviceId, $actions, $actionOptions);
    }

    /**
     * Build the actions we want to run
     *
     * @param array $actions Array of actions to execute
     *
     * @return AccountChange_Action_Manager
     * @throws AccountChange_Action_ManagerException
     */
    public function buildActionManager($actions)
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intOldServiceDefinitionId', $this->oldSdi);
        $registry->setEntry('intNewServiceDefinitionId', $this->newSdi);
        $registry->setEntry('newWlrServiceComponentId', $this->newWlrScid);
        $registry->setEntry('oldWlrServiceComponentId', $this->oldWlrScid);
        $registry->setEntry('oldWlrComponentId', $this->oldWlrComponentId);
        $registry->setEntry('oldAdslComponentId', $this->oldAdslComponentId);
        $registry->setEntry('newAdslServiceComponentId', $this->newAdslScid);

        $wlrComponentId = null;

        if (!empty($this->objNewProductConfiguration)) {
            $wlrComponentId = $this->objNewProductConfiguration->getComponentId();

        } else {
            if (!empty($this->intComponentId)) {
                $wlrComponentId = $this->intComponentId;
            }
        }
        $registry->setEntry('newWlrComponentId', $wlrComponentId);
        $registry->setEntry('bolSchedule', $this->isScheduled());

        // Get an instance of an action manager with the actions we want to run
        $objActionManager = $this->getActionManager($this->intServiceId, $actions);

        return $objActionManager;
    }

    /**
     * Get a scheduling helper object
     *
     * @return AccountChange_SchedulingHelper
     **/
    protected function getSchedulingHelper()
    {
        return new AccountChange_SchedulingHelper();
    }

    /**
     * checks if the account change is only changing the call plan - this will prevent LtcContract actions being performed if true
     *
     * @return boolean
     */
    private function isPhoneOnlyProductChange()
    {
        return $this->oldSdi === $this->newSdi && $this->oldWlrScid !== null;
    }

    /**
     * @return bool
     */
    private function isBBOToDslMove()
    {
        $bboHelper = $this->getBBOHelper();
        $oldIsBbo = $bboHelper->isBroadbandOnlyProduct($this->oldSdi);
        $newIsBbo = $bboHelper->isBroadbandOnlyProduct($this->newSdi);

        return $oldIsBbo && !$newIsBbo;
    }

    /**
     * @return AccountChange_BroadbandOnlyHelper
     */
    protected function getBBOHelper()
    {
        return new AccountChange_BroadbandOnlyHelper();
    }
}
