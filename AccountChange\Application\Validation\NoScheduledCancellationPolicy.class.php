<?php

/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_NoScheduledCancellationPolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE = 'You cannot make changes to your account as you have a scheduled cancellation request.';
    const ERROR_CODE = 'ERROR_CUSTOMER_HAS_SCHEDULED_CANCELLATION';

    /** @var string $endDate */
    private $endDate;

    /**
     * @param Auth_BusinessActor $actor                 Business Actor
     * @param bool               $isWorkplace           is workplace flag
     * @param bool               $isScript              is script flag
     * @param array              $additionalInformation extra params
     */
    public function __construct(
        Auth_BusinessActor $actor,
        $isWorkplace = false,
        $isScript = false,
        $additionalInformation = array()
    ) {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);
        $this->endDate = $this->additionalInformation['serviceEndDate'];
    }
    /**
     * @return bool
     */
    public function validate()
    {
        return empty($this->endDate);
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return static::ERROR_MESSAGE;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return static::ERROR_CODE;
    }
}
