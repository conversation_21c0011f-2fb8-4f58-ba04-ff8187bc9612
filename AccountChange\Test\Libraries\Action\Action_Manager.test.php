<?php
/**
 * Action Manager
 *
 * Testing class for the AccountChange_Action_Manager class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_Manager.test.php,v 1.3 2009-02-04 17:07:21 bselby Exp $
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-09-01
 */
/**
 * Action Manager Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_Action_Manager_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for Service Id
     *
     * @var int
     */
    private $_intServiceId;

    /**
     * PHPUnit setup function
     *
     * @return void
     */
    public function setup()
    {
        $this->_intServiceId = 919191;
    }

    /**
     * testConstructorThrowsExceptionIfServiceIdIsNonNumeric
     *
     * @covers AccountChange_Action_Manager::__construct
     * @covers AccountChange_Action_ManagerException
     *
     * @return void
     */
    public function testConstructorThrowsExceptionIfServiceIdIsNonNumeric()
    {
        $this->setExpectedException(
            'AccountChange_Action_ManagerException',
            'Non numeric service id',
            AccountChange_Action_ManagerException::ERR_INVALID_SERVICE_ID_TYPE
        );

        $objActionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('initialise'),
            array('invalidServiceId')
        );

        $objActionManager->expects($this->never())
            ->method('initialise');
    }

    /**
     * testValidateActionNamesThrowsExceptionIfNameIsNotValidActionClass
     *
     * @covers AccountChange_Action_Manager::validateActionNames
     * @covers AccountChange_Action_ManagerException
     *
     * @return void
     */
    public function testValidateActionNamesThrowsExceptionIfNameIsNotValidActionClass()
    {
        $this->setExpectedException(
            'AccountChange_Action_ManagerException',
            'Invalid action name provided - NaughtyActionName',
            AccountChange_ManagerException::ERR_NO_ACCOUNT_CHANGE_TYPE_SELECTED
        );

        $objManagerMock = $this->getMock(
            'AccountChange_Action_Manager',
            array('createActions'),
            array(1, array('NaughtyActionName'))
        );

        $objManagerMock->expects($this->once())
            ->method('createActions')
            ->will($this->returnValue(array()));
    }

    /**
     * testValidateActionNamesReturnsTrueIfAllActionNamesAreValid
     *
     * @covers AccountChange_Action_Manager::validateActionNames
     *
     * @return void
     */
    public function testValidateActionNamesReturnsTrueIfAllActionNamesAreValid()
    {
        $arrActionNames = array('Radius');

        $objManager = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Action_Manager',
            array($this->_intServiceId, $arrActionNames)
        );

        $this->assertTrue($objManager->protected_validateActionNames($arrActionNames));
    }

    /**
     * testValidateActionNamesReturnsTrueIfActionNamesIsEmpty
     *
     * @covers AccountChange_Action_Manager::validateActionNames
     *
     * @return void
     */
    public function testValidateActionNamesReturnsTrueIfActionNamesIsEmpty()
    {
        $arrActionNames = array();

        $objManager = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Action_Manager',
            array($this->_intServiceId, $arrActionNames)
        );

        $this->assertTrue($objManager->protected_validateActionNames($arrActionNames));
    }

    /**
     * testCreateActionsWithDefaultActionsReturnsArrayWithDefaultActions
     *
     * @covers AccountChange_Action_Manager::createActions
     *
     * @return void
     */
    public function testCreateActionsWithDefaultActionsReturnsArrayWithDefaultActions()
    {
        $arrExpectedActions = array();
        $arrExpectedActions[] = new AccountChange_Action_Products(1, array());
        $arrExpectedActions[] = new AccountChange_Action_LegacyComponents(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Ellacoya(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Cbc(1, array());
        $arrExpectedActions[] = new AccountChange_Action_CallerDisplay(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Radius(1, array());
        $arrExpectedActions[] = new AccountChange_Action_StaticIp(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Billing(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Contract(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Hardware(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Appointment(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Regrade(1, array());
        $arrExpectedActions[] = new AccountChange_Action_UpdateSupplierProduct(1, array());
        $arrExpectedActions[] = new AccountChange_Action_GbWrProductSetChangeJl(1, array());
        $arrExpectedActions[] = new AccountChange_Action_GbWrBullGuardJl(1, array());
        $arrExpectedActions[] = new AccountChange_Action_LineRentalSaverAdd(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Discounts(1, array());
        $arrExpectedActions[] = new AccountChange_Action_LtcContracts(1, array());
        $arrExpectedActions[] = new AccountChange_Action_Consent(1, array());
        $arrExpectedActions[] = new AccountChange_Action_RegisterCharges(1, array());
        $arrExpectedActions[] = new AccountChange_Action_BroadbandOnlyCli(1, array());


        $objManagerMock = $this->getMock(
            'AccountChange_Action_Manager',
            array('validateActionNames'),
            array(1, AccountChange_Action_Manager::$arrValidActions, array())
        );

        $objManagerMock->expects($this->any())
            ->method('validateActionNames');

        $arrActions = $objManagerMock->getActions();

        $this->assertEquals($arrExpectedActions, $arrActions);
    }

    /**
     * testCreateActionsPassesTheCorrectOptionsArrayToTheAction
     *
     * @covers AccountChange_Action_Manager::createActions
     * @covers AccountChange_Action_Manager::getActions
     *
     * @return void
     */
    public function testCreateActionsPassesTheCorrectOptionsArrayToTheAction()
    {
        $arrActionNames = array('Billing');
        $arrOptions     = array(
                'Billing' => array('1', '2')
            );

        $objBillingAction = new AccountChange_Action_Billing(1, array(1,2));

        $objManagerMock = $this->getMock(
            'AccountChange_Action_Manager',
            array('validateActionNames'),
            array(1, $arrActionNames, $arrOptions)
        );

        $objManagerMock->expects($this->any())
            ->method('validateActionNames');

        $arrActions = $objManagerMock->getActions();

        $objAction = $arrActions[0];

        $this->assertEquals($objBillingAction, $objAction);
    }

    /**
     * testExecuteCallsExecuteOnEveryActionTheManagerHasAndReturnsTrue
     *
     * @covers AccountChange_Action_Manager::execute
     * @covers AccountChange_Action_Manager::initialise
     *
     * @return void
     */
    public function testExecuteCallsExecuteOnEveryActionTheManagerHasAndReturnsTrue()
    {
        $intServiceId = 100;

        $objActionMock = $this->getMock(
            'AccountChange_Action_Billing',
            array('execute'),
            array($intServiceId, array('Billing' => array(1,2)))
        );

        $objActionMock->expects($this->once())
            ->method('execute');

        $objManagerMock = $this->getMock(
            'AccountChange_Action_Manager',
            array('createActions', 'validateActionNames'),
            array($intServiceId)
        );

        $objManagerMock->expects($this->once())
            ->method('createActions')
            ->will($this->returnValue(array($objActionMock)));

        $objManagerMock->expects($this->once())
            ->method('validateActionNames');

        $objManagerMock->initialise();

        $this->assertTrue($objManagerMock->execute());
    }
}
