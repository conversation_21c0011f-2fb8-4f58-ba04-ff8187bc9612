<?php
/**
 * Direct Debit Details Requirement
 *
 * Gathering information regarding the direct debit details
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: DirectDebitDetails.req.php,v 1.2 2009-01-27 07:07:01 bselby Exp $
 * @since     File available since 2008-10-14
 */
/**
 * AccountChange_DirectDebitDetails class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_DirectDebitDetails extends Mvc_WizardRequirement
{
    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'strDirectDebitName'          => 'external:Custom:conditional',
        'intDirectDebitSortCode1'     => 'external:Custom:conditional',
        'intDirectDebitSortCode2'     => 'external:Custom:conditional',
        'intDirectDebitSortCode3'     => 'external:Custom:conditional',
        'intDirectDebitAccountNumber' => 'external:Custom:conditional'
    );

    /**
     * Ordering of the custom validation that the wizard will perform
     *
     * @var array
     */
    protected $arrCustomValidatorOrder = array(
        'strDirectDebitName',
        'valDirectDebitSortCode',
        'valDirectDebitAccountNumber'
    );

    /**
     * Validation for the direct debit name
     *
     * @param string $strDirectDebitName first name
     *
     * @return array
     */
    public function valDirectDebitName($strDirectDebitName)
    {
        $arrValidatedReturn = array();

        if (empty($strDirectDebitName)) {

            $this->addValidationError('strDirectDebitName', 'MISSING');
            $arrValidatedReturn['strDirectDebitName'] = '';
        } else {

            $strDirectDebitName = Val_Name::getValidated($strDirectDebitName);

            if (!(isset($strDirectDebitName) &&
                is_object($strDirectDebitName) &&
                $strDirectDebitName instanceof Val_Name)) {

                $this->addValidationError('strDirectDebitName', 'INVALID');
            }

            $arrValidatedReturn['strDirectDebitName'] = $strDirectDebitName;
        }

        return $arrValidatedReturn;
    }

    /**
     * Validation for the direct debit sort code
     *
     * @param int $intDirectDebitSortCode1 sort code part one
     * @param int $intDirectDebitSortCode2 sort code part two
     * @param int $intDirectDebitSortCode3 sort code part three
     *
     * @return string
     */
    public function valDirectDebitSortCode($intDirectDebitSortCode1, $intDirectDebitSortCode2, $intDirectDebitSortCode3)
    {
        $arrValidatedReturn = array();

        $unkDirectDebitSortCode = Val_DirectDebit_SortCode::getValidated(
            $intDirectDebitSortCode1,
            $intDirectDebitSortCode2,
            $intDirectDebitSortCode3
        );

        // If it is an array of errors, then add validation errors
        if (is_array($unkDirectDebitSortCode)) {
            $arrValidatedReturn['intDirectDebitSortCode1'] = '';
            $arrValidatedReturn['intDirectDebitSortCode2'] = '';
            $arrValidatedReturn['intDirectDebitSortCode3'] = '';
            $arrValidatedReturn['objDirectDebitSortCode']  = null;

            foreach ($unkDirectDebitSortCode as $strError => $unkData) {
                $this->addValidationError('intDirectDebitSortCode', $strError, $unkData);
            }
        } else {

            $arrValidatedReturn['intDirectDebitSortCode1'] = $intDirectDebitSortCode1;
            $arrValidatedReturn['intDirectDebitSortCode2'] = $intDirectDebitSortCode2;
            $arrValidatedReturn['intDirectDebitSortCode3'] = $intDirectDebitSortCode3;
            $arrValidatedReturn['objDirectDebitSortCode']  = $unkDirectDebitSortCode;
        }

        return $arrValidatedReturn;
    }

    /**
     * Validation for the direct debit account number
     *
     * @param int $intDirectDebitAccountNumber the account number
     *
     * @return array
     */
    public function valDirectDebitAccountNumber($intDirectDebitAccountNumber)
    {
        $arrValidatedReturn = array();

        if (empty($intDirectDebitAccountNumber)) {

            $this->addValidationError('intDirectDebitAccountNumber', 'MISSING');
            $arrValidatedReturn['intDirectDebitAccountNumber'] = '';
        } else {

            $intDirectDebitAccountNumber = Val_DirectDebit_AccountNumber::getValidated($intDirectDebitAccountNumber);

            if (!(isset($intDirectDebitAccountNumber) &&
                is_object($intDirectDebitAccountNumber) &&
                $intDirectDebitAccountNumber instanceof Val_DirectDebit_AccountNumber)) {

                $this->addValidationError('intDirectDebitAccountNumber', 'INVALID');
            }

            $arrValidatedReturn['intDirectDebitAccountNumber'] = $intDirectDebitAccountNumber;
        }
        return $arrValidatedReturn;
    }
}
