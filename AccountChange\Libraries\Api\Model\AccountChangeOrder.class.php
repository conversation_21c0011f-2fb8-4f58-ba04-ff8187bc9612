<?php

require_once(__DIR__.'/../../C2mPromotionsHelper.php');
class AccountChange_AccountChangeOrder
{
    const TYPE_WORKPLACE_ACCOUNT_CHANGE = 1;
    const TYPE_MEMBER_CENTRE_ACCOUNT_CHANGE = 2;
    const TYPE_MY_OFFERS_PRODUCT_CHANGE = 3;
    const TYPE_MY_OFFERS_RE_CONTRACT = 4;

    const VALID_ACCOUNT_CHANGE_TYPES = array(
        self::TYPE_WORKPLACE_ACCOUNT_CHANGE,
        self::TYPE_MEMBER_CENTRE_ACCOUNT_CHANGE,
        self::TYPE_MY_OFFERS_PRODUCT_CHANGE,
        self::TYPE_MY_OFFERS_RE_CONTRACT
    );

    /**
     * Consent strings - l2c or proceed without line check
     *
     * @var string
     */
    const CONSENT_L2C                        = 'l2CConsent';
    const CONSENT_PROCEED_WITHOUT_LINE_CHECK = 'proceedWithoutLineCheck';
    const C2M_PROMOTION_CODE_KEY             = 'C2MPromotionCode';
    const PHONE_SCID_KEY                     = 'phoneServiceComponentId';
    const BROADBAND_SERVICE_DEF_KEY          = 'broadbandServiceDefinitionId';
    const MARKET_ID_KEY                      = 'marketId';

    /**
     * @var int
     */
    private $serviceId;

    /**
     * @var AccountChange_AccountChangeOrderContract
     */
    private $contract;

    /**
     * @var AccountChange_AccountChangeAddress
     */
    private $address;

    /**
     * @var AccountChange_AccountChangeOrderProducts
     */
    private $products;

    /**
     * @var array
     */
    private $consents;

    /**
     * @var string
     */
    private $promotionCode;

    /**
     * @var int
     */
    private $type;

    /**
     * @var bool
     */
    private $isScheduledChange = true;

    /**
     * @var bool
     */
    private $isRecontract = false;

    /**
     * @var array
     */
    private $additionalValidators;

    /**
     * @var string
     */
    private $market;

    /**
     * @var int
     */
    private $oldPhoneId;

    /**
     * @var int
     */
    private $impressionOfferId;

    /**
     * @var int
     */
    private $actorId;

    /**
     * @var string
     */
    private $overridenBusinessTierSessionId = null;

    /**
     * @var bool
     */
    private $commsRequired = true;

    /**
     * @var bool
     */
    private $migrateBroadbandDiscount = false;

    /**
     * @var bool
     */
    private $retainCurrentContracts;

    /**
     * @var bool
     */
    private $isScript = false;

    /**
     * @var array
     */
    private $additionalValidatorInformation = array();

    /**
     * @var bool
     */
    private $isInstantRecontract = false;

    /**
     * @var bool
     */
    private $isHouseMove = false;

    /**
     * @var array
     */
    private $oneOffCharges = [];

    /**
     * @var AccountChange_AccountChangeAppointment
    */
    private $appointment;

    /**
     * @var DateTime|null
     */
    private $backDatedDate;

    /**
     * @var bool
     */
    private $requiresSnapshot = false;

    /**
     * @var int
     */
    private $lineCheckId;

    /**
     * @var AccountChange_AccountChangePcics
     */
    private $pcics;

    /**
    * @return string
    */
    public function getOverridenBusinessTierSessionId()
    {
        return $this->overridenBusinessTierSessionId;
    }

    /**
     * @param string $overridenBusinessTierSessionId overridden biz tier id
     * @return void
     */
    public function setOverridenBusinessTierSessionId($overridenBusinessTierSessionId)
    {
        $this->overridenBusinessTierSessionId = $overridenBusinessTierSessionId;
    }

    /**
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @return void
     */
    public function validate()
    {
        $this->checkServiceIdValid();
        $this->checkProductsPresent();
        $this->checkPromotionCode();
        $this->checkType();
        $this->checkAddress();
        $this->checkBackDatedDate();
        $this->checkContractDetails();

        if (!empty($this->additionalValidators) && sizeof($this->additionalValidators) > 0) {
            $this->handleAdditionalValidators();
        }
    }

    /**
     * Runs through any additional validators that have been registered, throws exception if any fail.
     *
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @throws AccountChange_InvalidValidatorException
     * @return void
     */
    protected function handleAdditionalValidators()
    {
        $validationCheck = $this->getValidationCheck($this->additionalValidators);
        if (!$validationCheck->isAccountChangeAllowed()) {
            throw new AccountChange_InvalidAccountChangeOrderException($validationCheck->getReasonForBlockage());
        }
    }

    /**
     * @param array $additionalValidators additional validators
     * @return AccountChange_ValidationCheck
     */
    protected function getValidationCheck(array $additionalValidators)
    {
        return new AccountChange_ValidationCheck(
            $this->getEndUserActor($this->getServiceId()),
            $additionalValidators,
            false,
            false,
            $this->getValidatorAdditionalInformation()
        );
    }

    /**
     * Gets any additional information required to pass to validators.
     *
     * @return array
     */
    protected function getValidatorAdditionalInformation()
    {
        // Default information
        $phoneId = $this->getProducts()->getPhoneComponentId();
        if (empty($phoneId) || is_null($phoneId)) {
            $phoneId = $this->getOldPhoneId();
        }

        $default =  array(
            self::C2M_PROMOTION_CODE_KEY    => $this->getPromotionCode(),
            self::PHONE_SCID_KEY            => $phoneId,
            self::BROADBAND_SERVICE_DEF_KEY => $this->getProducts()->getServiceDefinitionId(),
            self::MARKET_ID_KEY             => $this->getMarket()
        );

        // Any additional api set information
        return array_merge($default, $this->additionalValidatorInformation);
    }

    /**
     * Sets additional information required by validators (in addition to default data added in
     * getValidatorAdditionalInformation)
     *
     * @param array $validatorInformation additional information
     *
     * @return void
     */
    public function setAdditionalValidatorInformation(array $validatorInformation)
    {
        $this->additionalValidatorInformation = array_merge(
            $this->additionalValidatorInformation,
            $validatorInformation
        );
    }

    /**
     * @param string $validationPolicy A class name for a validator implementing AccountChange_ValidationPolicy
     * @return void
     */
    public function registerAdditionalValidator($validationPolicy)
    {
        $this->additionalValidators[$validationPolicy] = $validationPolicy;
    }

    /**
     * @return void
     */
    public function resetAdditionalValidators()
    {
        $this->additionalValidators = array();
    }

    /**
     * @return int
     */
    public function getServiceId()
    {
        return $this->serviceId;
    }

    /**
     * @param int $serviceId service id
     * @return void
     */
    public function setServiceId($serviceId)
    {
        $this->serviceId = $serviceId;
    }

    /**
     * @return AccountChange_AccountChangeOrderContract
     */
    public function getContract()
    {
        return $this->contract;
    }

    /**
     * @param AccountChange_AccountChangeOrderContract $contract contract object
     * @return void
     */
    public function setContract($contract)
    {
        $this->contract = $contract;
    }

    /**
     * @return AccountChange_AccountChangeOrderProducts
     */
    public function getProducts()
    {
        return $this->products;
    }

    /**
     * @param AccountChange_AccountChangeOrderProducts $products products object
     * @return void
     */
    public function setProducts($products)
    {
        $this->products = $products;
    }

    /**
     * @return array
     */
    public function getConsents()
    {
        return $this->consents;
    }

    /**
     * @param array $consents consents
     * @return void
     */
    public function setConsents($consents)
    {
        $this->consents = $consents;
    }

    /**
     * @return string
     */
    public function getMarket()
    {
        return $this->market;
    }

    /**
     * @param string $market Plusnet market
     * @return void
     **/
    public function setMarket($market)
    {
        $this->market = $market;
    }


    /**
     * @param int $phoneId phone id for old product
     * @return void
     */
    public function setOldPhoneId($phoneId)
    {
        $this->oldPhoneId = $phoneId;
    }

    /**
     * @return int
     */
    public function getOldPhoneId()
    {
        return $this->oldPhoneId;
    }

    /**
     * @param string $consentType  consent type
     * @param string $consentValue consent value
     * @return void
     */
    public function addConsent($consentType, $consentValue)
    {
        if ($this->consents === null) {
            $this->consents = array();
        }
        $this->consents[$consentType] = $consentValue;
    }

    /**
     * @param string $consentType consent type
     *
     * @return string
     * @throws AccountChange_InvalidAccountChangeOrderException
     */
    public function getConsentValue($consentType)
    {
        if ($this->hasConsentValue($consentType)) {
            return $this->consents[$consentType];
        }

        throw new AccountChange_InvalidAccountChangeOrderException(
            "No consent provided for L2C value: " . $consentType
        );
    }

    /**
     * @param string $consentType consent type
     *
     * @return bool
     */
    public function hasConsentValue($consentType)
    {
        return $this->consents != null && array_key_exists($consentType, $this->consents);
    }

    /**
     * @return string
     */
    public function getPromotionCode()
    {
        return $this->promotionCode;
    }

    /**
     * @param string $promotionCode promo code
     * @return void
     */
    public function setPromotionCode($promotionCode)
    {
        $this->promotionCode = $promotionCode;
    }

    /**
     * @param int $impressionOfferId impression offer id
     * @return void
     */
    public function setImpressionOfferId($impressionOfferId)
    {
        $this->impressionOfferId = $impressionOfferId;
    }

    /**
     * @return int
     */
    public function getImpressionOfferId()
    {
        return $this->impressionOfferId;
    }

    /**
     * @return int
     */
    public function getActorId()
    {
        return $this->actorId;
    }

    /**
     * @param int $actorId actor id
     * @return void
     */
    public function setActorId($actorId)
    {
        $this->actorId = $actorId;
    }

    /**
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param int $type type
     * @return void
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * @return bool
     */
    public function getIsScheduledChange()
    {
        return $this->isScheduledChange;
    }

    /**
     * @param bool $isScheduledChange flag for if scheduled change
     *
     * @return void
     */
    public function setIsScheduledChange($isScheduledChange)
    {
        $this->isScheduledChange = $isScheduledChange;
    }

    /**
     * @return bool
     */
    public function getIsRecontract()
    {
        return $this->isRecontract;
    }

    /**
     * @param bool $isRecontract flag for if this is a recontract change
     *
     * @return void
     */
    public function setIsRecontract($isRecontract)
    {
        $this->isRecontract = $isRecontract;
    }

    /**
     * @return bool
     */
    public function getIsInstantRecontract()
    {
        return $this->isInstantRecontract;
    }

    /**
     * @param bool $isInstantRecontract flag for if this is an instant recontract
     *
     * @return void
     */
    public function setIsInstantRecontract($isInstantRecontract)
    {
        $this->isInstantRecontract = $isInstantRecontract;
    }

    /**
     * @return bool
     */
    public function getIsHouseMove()
    {
        return $this->isHouseMove;
    }

    /**
     * @param bool $isHouseMove flag for if this is a house move
     *
     * @return void
     */
    public function setIsHouseMove($isHouseMove)
    {
        $this->isHouseMove = $isHouseMove;
    }

    /**
     * @return bool
     */
    public function hasPromotionCode()
    {
        return !empty($this->promotionCode);
    }

    /**
     * @return bool
     */
    public function hasContract()
    {
        return $this->contract != null;
    }

    /**
     * @return bool
     */
    public function isCommsRequired()
    {
        return $this->commsRequired;
    }

    /**
     * @param bool $commsRequired are comms required
     * @return void
     */
    public function setCommsRequired($commsRequired)
    {
        $this->commsRequired = $commsRequired;
    }

    /**
     * @return bool
     */
    public function isScript()
    {
        return $this->isScript;
    }

    /**
     * @param bool $isScript is script
     * @return void
     */
    public function setScript($isScript)
    {
        $this->isScript = $isScript;
    }

    /**
     * @return bool
     */
    public function doMigrateBroadbandDiscount()
    {
        return $this->migrateBroadbandDiscount;
    }

    /**
     * @param bool $migrateBroadbandDiscount migrate broadband discount
     * @return void
     */
    public function setMigrateBroadbandDiscount($migrateBroadbandDiscount)
    {
        $this->migrateBroadbandDiscount = $migrateBroadbandDiscount;
    }

    /**
     * @return bool
     */
    public function isRetainCurrentContracts()
    {
        return $this->retainCurrentContracts;
    }

    /**
     * @param bool $retainCurrentContracts retain current contracts
     * @return void
     */
    public function setRetainCurrentContracts($retainCurrentContracts)
    {
        $this->retainCurrentContracts = $retainCurrentContracts;
    }

    /**
     * @return int
     * @throws AccountChange_InvalidAccountChangeOrderException
     */
    public function convertTypeToAppId()
    {
        switch ($this->getType()) {
            case self::TYPE_WORKPLACE_ACCOUNT_CHANGE:
                return AccountChange_Action_Consent::WORKPLACE_ACCOUNT_CHANGE;
            case self::TYPE_MEMBER_CENTRE_ACCOUNT_CHANGE:
                return AccountChange_Action_Consent::PORTAL_ACCOUNT_CHANGE;
            case self::TYPE_MY_OFFERS_PRODUCT_CHANGE:
            case self::TYPE_MY_OFFERS_RE_CONTRACT:
                return AccountChange_Action_Consent::MY_OFFERS;
            default:
                throw new AccountChange_InvalidAccountChangeOrderException(
                    sprintf('Cannot map type [%s] to app id.', $this->getType())
                );
        }
    }

    /**
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @throws Db_TransactionException
     * @return void
     */
    private function checkServiceIdValid()
    {
        if (empty($this->getServiceId()) || $this->getServiceId() < 0 || $this->doesNotHaveValidAccount()) {
            throw new AccountChange_InvalidAccountChangeOrderException(
                sprintf('Supplied service id [%s] is not valid.', $this->getServiceId())
            );
        }
    }

    /**
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @return void
     */
    private function checkContractDetails()
    {
        if (!is_null($this->getContract()) && !is_null($this->isRetainCurrentContracts()) ||
            !is_null($this->isRetainCurrentContracts()) && !$this->isRetainCurrentContracts()) {
            throw new AccountChange_InvalidAccountChangeOrderException(
                sprintf('Contract details supplied are invalid, either retain or contract needs to be set not both')
            );
        }
    }

    /**
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @return void
     */
    private function checkAddress()
    {
        if (!empty($this->address) && !$this->address->isValid()) {
            throw new AccountChange_InvalidAccountChangeOrderException(
                sprintf('Supplied address is not valid.')
            );
        }
    }

    /**
     * @return bool
     * @throws Db_TransactionException
     */
    protected function doesNotHaveValidAccount()
    {
        $adaptor = Db_Manager::getAdaptor('AccountChange');
        return empty($adaptor->getAccountIdByServiceId($this->getServiceId()));
    }

    /**
     * @throws AccountChange_InvalidAccountChangeOrderException
     */
    private function checkProductsPresent()
    {
        if (!$this->hasBroadbandServiceDefinitionId() && !$this->hasBroadbandServiceComponentId()) {
            
            throw new AccountChange_InvalidAccountChangeOrderException(
                                                                       'Account change order must have a valid broadband service definition or service compoenent id');
        }
    }


    /**
     * @return bool
     */
    private function hasBroadbandServiceDefinitionId()
    {
        if (empty($this->getProducts()->getServiceDefinitionId()) || $this->getProducts()->getServiceDefinitionId() < 0) {
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    private function hasBroadbandServiceComponentId()
    {
        if (empty($this->getProducts()->getServiceComponentId()) || $this->getProducts()->getServiceComponentId() < 0) {
            return false;
        }
        return true;
    }
    
    /**
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @return void
     */
    private function checkType()
    {
        if (!in_array($this->getType(), self::VALID_ACCOUNT_CHANGE_TYPES)) {
            throw new AccountChange_InvalidAccountChangeOrderException(
                sprintf('Account change type [%s] is not recognised.', $this->getType())
            );
        }
    }

    /**
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @throws Db_TransactionException
     * @return void
     */
    private function checkPromotionCode()
    {
        if (!empty($this->getPromotionCode())
            && empty($this->getPromotionFromC2M())
            && empty($this->getPromotionFromPresetDiscountTables())) {
            throw new AccountChange_InvalidAccountChangeOrderException(
                sprintf('Supplied promotion code [%s] is not valid.', $this->getPromotionCode())
            );
        }
    }

    /**
     * @return void
     */
    private function checkBackDatedDate()
    {
        if (!empty($this->getBackDatedDate())) {
            $this->registerAdditionalValidator(AccountChange_BackDatedDatePolicy::class);
            $this->registerAdditionalValidator(AccountChange_BillingPendingReratingPolicy::class);
            $this->setAdditionalValidatorInformation(
                [
                    'isScheduledChange' => $this->getIsScheduledChange(),
                    'backDatedDate' => $this->getBackDatedDate()
                ]
            );
        }
    }

    /**
     * @return false|mixed
     */
    protected function getPromotionFromC2M()
    {
        return AccountChange_C2mPromotionsHelper::getActivePromotionWithPromocode($this->getPromotionCode());
    }

    /**
     * @return string
     * @throws Db_TransactionException
     */
    protected function getPromotionFromPresetDiscountTables()
    {
        $adaptor = Db_Manager::getAdaptor('AccountChange');
        return $adaptor->getInitialPresetDiscountId(
            $this->promotionCode,
            $this->getProducts()->getServiceDefinitionId()
        );
    }

    /**
     * Return an end user actor for a given service id
     *
     * @param int $serviceId Service id
     *
     * @return Auth_BusinessActor
     */
    protected function getEndUserActor($serviceId)
    {
        return Auth_BusinessActor::getActorByExternalUserId(ltrim($serviceId, '0'));
    }

    /**
     * @return array
     */
    public function getOneOffCharges()
    {
        return $this->oneOffCharges;
    }

    /**
     * @param array $oneOffCharges one off charges
     * @return void
     */
    public function setOneOffCharges($oneOffCharges)
    {
        $this->oneOffCharges = $oneOffCharges;
    }

    /**
     * @return AccountChange_AccountChangeAddress
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @param AccountChange_AccountChangeAddress $address address object
     * @return void
     */
    public function setAddress($address)
    {
        $this->address = $address;
    }

    /**
     * @return AccountChange_AccountChangeAppointment
     */
    public function getAppointment()
    {
        return $this->appointment;
    }

    /**
     * @param AccountChange_AccountChangeAppointment $appointment appointment class
     * @return void
     */
    public function setAppointment($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * @return DateTime
     */
    public function getBackDatedDate()
    {
        return $this->backDatedDate;
    }

    /**
     * @param string $backDatedDate backdated date (YYYY/MM/DD)
     * @return void
     * @throws Exception
     */
    public function setBackDatedDate($backDatedDate)
    {
        $this->backDatedDate = $this->createDateTime($backDatedDate);
    }

    /**
     * @param string $backDatedDate backdated date (YYYY/MM/DD)
     * @return bool|DateTime
     */
    protected function createDateTime($backDatedDate)
    {
        return DateTime::createFromFormat('Y-m-d', $backDatedDate);
    }

    /**
     * @param bool $requiresSnapshot requires snapshot
     * @return bool
     */
    public function setRequiresSnapshot($requiresSnapshot)
    {
        return $this->requiresSnapshot = $requiresSnapshot;
    }

    /**
     * @return bool
     */
    public function requiresSnapshot()
    {
        return $this->requiresSnapshot;
    }

    /**
     * @return int
     */
    public function getLineCheckId()
    {
        return $this->lineCheckId;
    }

    /**
     * @param int $lineCheckId LineCheck Id
     * @return void
     */
    public function setLineCheckId($lineCheckId)
    {
        $this->lineCheckId = $lineCheckId;
    }

    /**
     * @return AccountChange_AccountChangePcics
     */
    public function getPcics()
    {
        return $this->pcics;
    }

    /**
     * @param int $pcics PCICS data
     * @return void
     */
    public function setPcics($pcics)
    {
        $this->pcics = $pcics;
    }
}
