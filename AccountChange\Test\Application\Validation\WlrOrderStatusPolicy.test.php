<?php


class AccountChange_WlrOrderStatusPolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;
    const COMPONENT_TYPE_ID = 3;

    /**
     * @dataProvider provideInstanceArrays
     */
    public function testValidate($unc, $qa, $expected)
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->should<PERSON><PERSON>eive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_WlrOrderStatusPolicy', [$actor])->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldR<PERSON>eive('getPhoneComponentTypeID')->once()->with(self::SERVICE_ID)->andReturn(self::COMPONENT_TYPE_ID);
        $validator->shouldR<PERSON>eive('getCallFeatureTypes')->andReturn([]);
        $validator->shouldR<PERSON>eive('getComponentInstances')->andReturn($unc, $qa);

        $this->assertEquals($expected, $validator->validate(self::SERVICE_ID));
    }

    public function provideInstanceArrays() {
        return [
            [[], [], true],
            [[1,2,3], [], false],
            [[], [4,5,6], false],
            [[1,2,3], [4,5,6], false],
        ];
    }
}
