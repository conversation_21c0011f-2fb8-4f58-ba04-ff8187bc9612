<?php
/**
 * AccountChange Show Line Checker
 *
 * Testing class for AccountChange_ShowLinechecker
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 */
/**
 * AccountChange Show Line Checker Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 */
class AccountChange_ShowLinechecker_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @covers AccountChange_ShowLinechecker::valLineCheckInputs
     * Checks the success case of validation of line check input.
     */
    public function testValLineCheckInputsWhenPerformLineCheckSuccess()
    {
        $intPhoneNumber = '**********';
        $strPostCode	= 'S1 4BY';

        $objGetPostRecordMock = $this->getMock(
            'Db_Adaptor',
            array('getPostcodeRecord'),
            array("Val", Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $objGetPostRecordMock->expects($this->any())
                ->method('getPostcodeRecord')
                ->will($this->returnValue(array(1)));

        Db_Manager::setAdaptor('Val', $objGetPostRecordMock, Db_Manager::DEFAULT_TRANSACTION);

        $result =array(
            'intLineCheckId' => 1,
            'intLineCheckStatusId' => 1,
            'intServiceId' => 1,
            'intServiceDefinitionId' => 1,
            'intErrorId' => 0,
            'vchLineCheckInput' => '***********',
            'vchLineCheckType' => 'TELNO',
            'ch.dtmLineCheckDate' => '2009-03-26 18:42:43',
            'chrReasonCode' => 'L',
            'vchExchangeName' => 'LSSID',
            'vchExchangeCode' => 'LSSID',
            'chrFixedRateRag' => 'G',
            'dteFixedRateReadyDate' => '',
            'chrFixedRateExchState' => 'E',
            'chrRateAdaptiveRag' => 'G',
            'dteRateAdaptiveReadyDate' => '',
            'chrRateAdaptiveExchState' => 'E',
            'chrMaxRag' => 'G',
            'intMaxSpeed' => 4500,
            'dteMaxReadyDate' => '',
            'chrMaxExchState' => 'E',
            'chrWbcRag' => 'G',
            'intWbcSpeed' => 6000,
            'dteWbcReadyDate' => '',
            'chrWbcExchState' => 'E',
            'vchPostCode' => 'DA5 3EQ'
        );

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getResultDao'),
            array('LineCheck', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                 ->method('getResultDao')
                 ->will($this->returnValue($result));

        Db_Manager::setAdaptor('LineCheck', $objMockDbAdaptor);

        $objCliNumber = Val_UKLandlinePhoneNumber::getValidated($intPhoneNumber);
        $objPostCode = Val_Postcode::getValidated($strPostCode);

        $mockShowLinechecker = $this->getMock('AccountChange_ShowLinechecker', array('getLineCheckResult'));

        $result = $mockShowLinechecker->valLineCheckInputs($intPhoneNumber, $strPostCode);
        $this->assertInternalType('array', $result);
        $this->assertArrayHasKey('intPhoneNumber', $result);
        $this->assertArrayHasKey('strPostCode', $result);
        $this->assertEquals($objCliNumber, $result['intPhoneNumber']);
        $this->assertEquals($objPostCode, $result['strPostCode']);
    }


    /**
     * @covers AccountChange_ShowLinechecker::valLineCheckInputs
     * Checks the failure case when post code is null.
     */

    public function testValLineCheckInputsWhenPerformLineCheckFailureNullPostCode()
    {
        $intPhoneNumber = '**********';
        $strPostCode	= '';

        $objShowLinechecker = new AccountChange_ShowLinechecker();

        $result = $objShowLinechecker->valLineCheckInputs($intPhoneNumber, $strPostCode);
        $arrValidationErrors = $objShowLinechecker->getValidationErrors();

        $this->assertInternalType('array', $result);
         $this->assertArrayHasKey('strPostCode', $arrValidationErrors);
        $this->assertArrayHasKey('MISSING', $arrValidationErrors['strPostCode']);
        $this->assertEquals('', $result['strPostCode']);
    }


    /**
     * @covers AccountChange_ShowLinechecker::valLineCheckInputs
     * Checks the failure case when phone number is null
     */

    public function testValLineCheckInputsWhenPerformLineCheckFailureNullPhoneNumber()
    {
        $intPhoneNumber = '';
        $strPostCode	= 'S1 4BY';

        $objGetPostRecordMock = $this->getMock(
            'Db_Adaptor',
            array('getPostcodeRecord'),
            array("Val", Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $objGetPostRecordMock->expects($this->any())
                ->method('getPostcodeRecord')
                ->will($this->returnValue(array(1)));

        Db_Manager::setAdaptor('Val', $objGetPostRecordMock, Db_Manager::DEFAULT_TRANSACTION);

        $objShowLinechecker = new AccountChange_ShowLinechecker();

        $result = $objShowLinechecker->valLineCheckInputs($intPhoneNumber, $strPostCode);
        $arrValidationErrors = $objShowLinechecker->getValidationErrors();

        $this->assertInternalType('array', $result);
        $this->assertArrayHasKey('intPhoneNumber', $arrValidationErrors);
        $this->assertArrayHasKey('MISSING', $arrValidationErrors['intPhoneNumber']);
        $this->assertEquals('', $result['intPhoneNumber']);
    }


    /**
     * @covers AccountChange_ShowLinechecker::valLineCheckInputs
     * Checks the failure case when post code is invalid.
     */
    public function testValLineCheckInputsWhenPerformLineCheckFailureInvalidPostCode()
    {
        $intPhoneNumber = '**********';
        $strPostCode	= '4BY';

        $objGetPostRecordMock = $this->getMock(
            'Db_Adaptor',
            array('getPostcodeRecord'),
            array("Val", Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $objGetPostRecordMock->expects($this->any())
            ->method('getPostcodeRecord')
            ->will($this->returnValue(array(1)));

        Db_Manager::setAdaptor('Val', $objGetPostRecordMock, Db_Manager::DEFAULT_TRANSACTION);

        $objShowLinechecker = new AccountChange_ShowLinechecker();

        $result = $objShowLinechecker->valLineCheckInputs($intPhoneNumber, $strPostCode);
        $arrValidationErrors = $objShowLinechecker->getValidationErrors();

        $this->assertInternalType('array', $result);
        $this->assertArrayHasKey('strPostCode', $arrValidationErrors);
        $this->assertArrayHasKey('INVALID', $arrValidationErrors['strPostCode']);
        $this->assertEquals('', $result['strPostCode']);

        Db_Manager::restoreAdaptor('Val');
    }


    /**
     * @covers AccountChange_ShowLinechecker::valLineCheckInputs
     * Checks the failure case when phone number is invalid.
     */

    public function testValLineCheckInputsWhenPerformLineCheckFailureInvalidPhoneNumber()
    {
        $intPhoneNumber = '*********';
        $strPostCode	= 'S1 4BY';

        $objGetPostRecordMock = $this->getMock(
            'Db_Adaptor',
            array('getPostcodeRecord'),
            array("Val", Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $objGetPostRecordMock->expects($this->any())
            ->method('getPostcodeRecord')
            ->will($this->returnValue(array(1)));

        Db_Manager::setAdaptor('Val', $objGetPostRecordMock, Db_Manager::DEFAULT_TRANSACTION);

        $objShowLinechecker = new AccountChange_ShowLinechecker();

        $result = $objShowLinechecker->valLineCheckInputs($intPhoneNumber, $strPostCode);
        $arrValidationErrors = $objShowLinechecker->getValidationErrors();

        $this->assertInternalType('array', $result);
        $this->assertArrayHasKey('intPhoneNumber', $arrValidationErrors);
        $this->assertArrayHasKey('INVALID', $arrValidationErrors['intPhoneNumber']);
        $this->assertEquals('', $result['intPhoneNumber']);

        Db_Manager::restoreAdaptor('Val');
    }

    /**
     * @covers AccountChange_ShowLinechecker::performLineCheck
     *
     * @dataProvider provideDataForPerformLineCheck
     *
     */
    public function testPerformLineCheckWithData($error, $expected, $expectedErrors)
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('getPostcodeRecord', 'getPostcodeDetailsByID'),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->any())
           ->method('getPostcodeRecord')
           ->will($this->returnValue(array()));

        $db->expects($this->any())
           ->method('getPostcodeDetailsByID')
           ->will($this->returnValue(array()));

        Db_Manager::setAdaptor('Val', $db);

        $landline = new Val_UKLandlinePhoneNumber("***********");
        $postcode = new Val_Postcode('S4 QWE');

        $lineChecker = $this->getMock(
            'AccountChange_LineChecker',
            array('performLineCheck', 'insertPendingServiceLineData')
        );

        $lineChecker
            ->expects($this->any())
            ->method('performLineCheck')
            ->will($this->throwException(new AccountChange_LineCheckResultException($error)));

        $requirement = $this->getMock(
            'AccountChange_ShowLinechecker',
            array('lineChecker'));

        $requirement
            ->expects($this->any())
            ->method('lineChecker')
            ->will($this->returnValue($lineChecker));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'));

        $requirement->setAppStateCallback($objMockController);

        $output = $requirement->performLineCheck($landline, $postcode);
        $actualErrors = $requirement->getValidationErrors();

        $this->assertEquals($expected, $output);
        $this->assertEquals($expectedErrors, $actualErrors);
    }

    /**
     * @covers AccountChange_ShowLinechecker::performLineCheck
     */
    public function testPerformLineCheckWithNoErrors()
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('getPostcodeRecord', 'getPostcodeDetailsByID'),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->any())
            ->method('getPostcodeRecord')
            ->will($this->returnValue(array()));

        $db->expects($this->any())
            ->method('getPostcodeDetailsByID')
            ->will($this->returnValue(array()));

        Db_Manager::setAdaptor('Val', $db);

        $landline = new Val_UKLandlinePhoneNumber("***********");
        $postcode = new Val_Postcode('S4 QWE');

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            [],
            [],
            '',
            false
        );

        $lineChecker = $this->getMock(
            'AccountChange_LineChecker',
            array('performLineCheck', 'insertPendingServiceLineData')
        );

        $lineChecker
            ->expects($this->any())
            ->method('performLineCheck')
            ->will($this->returnValue($lineCheckResult));

        $requirement = $this->getMock(
            'AccountChange_ShowLinechecker',
            array('lineChecker'));

        $requirement
            ->expects($this->any())
            ->method('lineChecker')
            ->will($this->returnValue($lineChecker));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'));

        $requirement->setAppStateCallback($objMockController);

        $expectedResult = array(
            'LineCheckPhoneNumber'=> $landline,
            'LineCheckPostCode' => $postcode,
            'objLinecheckResult' => $lineCheckResult
        );

        $output = $requirement->performLineCheck($landline, $postcode);
        $actualErrors = $requirement->getValidationErrors();

        $this->assertEquals($expectedResult, $output);
        $this->assertEquals(array(), $actualErrors);
    }

    /**
     * @covers AccountChange_ShowLinechecker::performLineCheck
     *
     * @dataProvider provideDataForPerformLineCheckThrowsExceptionTest
     */
    public function testPerformLineCheckThrowsException($exception, $expectedResult)
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('getPostcodeRecord', 'getPostcodeDetailsByID'),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->any())
            ->method('getPostcodeRecord')
            ->will($this->returnValue(array()));

        $db->expects($this->any())
            ->method('getPostcodeDetailsByID')
            ->will($this->returnValue(array()));

        Db_Manager::setAdaptor('Val', $db);

        $lineChecker = $this->getMock(
            'AccountChange_LineChecker',
            array('performLineCheck', 'insertPendingServiceLineData')
        );

        $lineChecker
            ->expects($this->any())
            ->method('performLineCheck')
            ->will($this->throwException($exception));

        $requirement = $this->getMock(
            'AccountChange_ShowLinechecker',
            array('lineChecker'));

        $requirement
            ->expects($this->any())
            ->method('lineChecker')
            ->will($this->returnValue($lineChecker));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'));

        $requirement->setAppStateCallback($objMockController);

        $landline = new Val_UKLandlinePhoneNumber("***********");
        $postcode = new Val_Postcode('S4 QWE');

        $output = $requirement->performLineCheck($landline, $postcode);

        $this->assertEquals($expectedResult, $output);
    }

    public static function provideDataForPerformLineCheck()
    {
        return array(
            array(LineCheck_Result::ERR_ID_INVALID_TELNO, array('LineCheckPhoneNumber' => ''), array('intPhoneNumber' => array('LINE_CHECK_INVALID_TELNO' => true))),
            array(LineCheck_Result::ERR_ID_NOT_BT_LINE, array('LineCheckPhoneNumber' => ''), array('intPhoneNumber' => array('LINE_CHECK_TELNO_NOT_FOUND' => true))),
            array(LineCheck_Result::ERR_ID_TELNO_CEASED, array('LineCheckPhoneNumber' => ''), array('intPhoneNumber' => array('LINE_CHECK_TELNO_NOT_FOUND' => true))),
            array(LineCheck_Result::ERR_ID_TELNO_NOT_FOUND, array('LineCheckPhoneNumber' => ''), array('intPhoneNumber' => array('LINE_CHECK_TELNO_NOT_FOUND' => true))),
            array(LineCheck_Result::ERR_ID_POST_CODE_NOT_FOUND, array('LineCheckPostCode' => ''), array('strPostCode' => array('LINE_CHECK_POST_CODE_NOT_FOUND' => true))),
            array(LineCheck_Result::ERR_ID_INVALID_POST_CODE, array('LineCheckPostCode' => ''), array('strPostCode' => array('LINE_CHECK_INVALID_POST_CODE' => true))),
            array(9999999999, array('LineCheckPhoneNumber' => ''), array('intPhoneNumber' => array('LINE_CHECK_FAILED_PN' => true)))
        );
    }

    public function provideDataForPerformLineCheckThrowsExceptionTest()
    {
        $cases = array();

        $btRequestException = new LineCheck_BtRequestException();
        $expectedResult = array(
            'LineCheckPhoneNumber' => $btRequestException
        );

        $cases[] = array($btRequestException, $expectedResult);

        $exception = new Exception();
        $expectedResult = array(
            'LineCheckPhoneNumber' => ''
        );

        $cases[] = array($exception, $expectedResult);

        return $cases;
    }
}
