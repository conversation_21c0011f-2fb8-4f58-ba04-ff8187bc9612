server: coredb
role: master
rows: single
statement:

SELECT
    (t.intCostIncVatPence/100) intCostIncVatPounds
FROM
 products.service_component_config AS scc
INNER JOIN products.service_components AS sc
    on scc.service_component_id = sc.service_component_id
INNER JOIN products.tblServiceComponentProduct AS scp
    on sc.service_component_id = scp.intServiceComponentID
INNER JOIN dbProductComponents.tblProductComponentConfig AS pcc
    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
INNER JOIN dbProductComponents.tblTariff AS t
    ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
INNER JOIN products.tblServiceComponentMarket AS scm
    ON scp.intServiceComponentId = scm.intServiceComponentId
WHERE
    scc.service_definition_id = :intserviceDefinitionId
AND scp.intServiceComponentProductTypeID = 6
AND intMarketId = :intMarket;