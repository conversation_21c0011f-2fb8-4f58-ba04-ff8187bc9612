<?php
/**
 * Testing class for the AccountChange_Action_UpdateSupplierProduct class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
/**
 * UpdateSupplierProduct Action Test Class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */

use Plusnet\InventoryEventClient\Service\ActiveContextService;

class AccountChange_Action_UpdateSupplierProduct_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @return void
     */
    public function testSupplierProductIsUpdatedWhenOrderIsRequired()
    {
        $identifyAction = Mockery::mock('AccountChange_Action_IdentifyAction');
        $identifyAction->shouldReceive('getActionRequired')->andReturn('Modify');
        $identifyAction->makePartial();

        /** @var  $updateSupplierProduct AccountChange_Action_UpdateSupplierProduct | Mockery\MockInterface */
        $updateSupplierProduct = Mockery::mock('AccountChange_Action_UpdateSupplierProduct');
        $updateSupplierProduct->makePartial();
        $updateSupplierProduct->shouldAllowMockingProtectedMethods();
        $updateSupplierProduct->shouldReceive('includeLegacyFiles');
        $updateSupplierProduct->shouldReceive('getIdentifyAction')->andReturn($identifyAction);
        $updateSupplierProduct->shouldReceive('setDefaultConnectionProfile')->once();
        $updateSupplierProduct->shouldReceive('updateProvisionedService')->once();
        $updateSupplierProduct->shouldReceive('addProvisioningHistory')->once();

        $updateSupplierProduct->execute();
    }

    /**
     * @return void
     */
    public function testSupplierProductIsNotUpdatedWhenOrderIsNotRequired()
    {
        $identifyAction = Mockery::mock('AccountChange_Action_IdentifyAction');
        $identifyAction->shouldReceive('getActionRequired')->andReturnNull();
        $identifyAction->makePartial();

        $billingContext = Mockery::mock('Plusnet\InventoryEventClient\Context\ScheduledAccountChangeContext');
        $billingContext->shouldReceive('setIsInternetSupplierOrder')->with(false)->once();
        ActiveContextService::setActiveContext($billingContext);

        /** @var  $updateSupplierProduct AccountChange_Action_UpdateSupplierProduct | Mockery\MockInterface */
        $updateSupplierProduct = Mockery::mock('AccountChange_Action_UpdateSupplierProduct');
        $updateSupplierProduct->makePartial();
        $updateSupplierProduct->shouldAllowMockingProtectedMethods();
        $updateSupplierProduct->shouldReceive('getIdentifyAction')->andReturn($identifyAction);
        $updateSupplierProduct
            ->shouldReceive('isScheduledAccountChange')
            ->andReturn(true);
        $updateSupplierProduct->shouldReceive('updateSupplierProduct')->never();
        $updateSupplierProduct->shouldReceive('setDefaultConnectionProfile')->never();

        $updateSupplierProduct->execute();
    }

    /**
     * @return void
     */
    public function testBillingContextIsUpdatedWhenScheduledAccountChangeHouseMoveInProgress()
    {
        $billingContext = Mockery::mock('Plusnet\InventoryEventClient\Context\ScheduledAccountChangeContext');
        $billingContext->shouldReceive('setIsInternetSupplierOrder')->with(true)->once();
        ActiveContextService::setActiveContext($billingContext);

        $identifyAction = Mockery::mock('AccountChange_Action_IdentifyAction');
        $identifyAction->shouldReceive('getActionRequired')->andReturnNull();
        $identifyAction->makePartial();

        $options = ['bolHousemove' => true];

        /** @var  $updateSupplierProduct AccountChange_Action_UpdateSupplierProduct | Mockery\MockInterface */
        $updateSupplierProduct = Mockery::mock('AccountChange_Action_UpdateSupplierProduct');
        $updateSupplierProduct->makePartial();
        $updateSupplierProduct->shouldAllowMockingProtectedMethods();
        $updateSupplierProduct->shouldReceive('getIdentifyAction')->andReturn($identifyAction);
        $updateSupplierProduct->setOptions($options);

        $updateSupplierProduct->execute();
    }

    /**
     * @return void
     */
    public function testBillingContextIsUpdatedWhenScheduledAccountChangeInProgress()
    {
        $billingContext = Mockery::mock('Plusnet\InventoryEventClient\Context\ScheduledAccountChangeContext');
        $billingContext->shouldReceive('setIsInternetSupplierOrder')->with(false)->once();
        ActiveContextService::setActiveContext($billingContext);

        $identifyAction = Mockery::mock('AccountChange_Action_IdentifyAction');
        $identifyAction->shouldReceive('getActionRequired')->andReturnNull();
        $identifyAction->makePartial();

        /** @var  $updateSupplierProduct AccountChange_Action_UpdateSupplierProduct | Mockery\MockInterface */
        $updateSupplierProduct = Mockery::mock('AccountChange_Action_UpdateSupplierProduct');
        $updateSupplierProduct->makePartial();
        $updateSupplierProduct->shouldAllowMockingProtectedMethods();
        $updateSupplierProduct->shouldReceive('getIdentifyAction')->andReturn($identifyAction);

        $updateSupplierProduct->execute();
    }

    /**
     * @return void
     */
    public function testSupplierProductIsUpdatedWhenInstantAccountChange()
    {
        $identifyAction = Mockery::mock('AccountChange_Action_IdentifyAction');
        $identifyAction->shouldReceive('getActionRequired')->andReturnNull();
        $identifyAction->makePartial();

        /** @var  $updateSupplierProduct AccountChange_Action_UpdateSupplierProduct | Mockery\MockInterface */
        $updateSupplierProduct = Mockery::mock('AccountChange_Action_UpdateSupplierProduct');
        $updateSupplierProduct->makePartial();
        $updateSupplierProduct->shouldAllowMockingProtectedMethods();
        $updateSupplierProduct
            ->shouldReceive('getIdentifyAction')->andReturn($identifyAction);
        $updateSupplierProduct
            ->shouldReceive('isScheduledAccountChange')->andReturn(false);
        $updateSupplierProduct
            ->shouldReceive('includeLegacyFiles');
        $updateSupplierProduct
            ->shouldReceive('updateSupplierProduct')->once();
        $updateSupplierProduct
            ->shouldReceive('setDefaultConnectionProfile')->once();

        $updateSupplierProduct->execute();
    }
}
