<?php
/**
 * Current Account Requirement
 *
 * Testing class for the AccountChange_ShowCurrentAccount requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: ShowCurrentAccount.test.php,v 1.2 2009-01-27 09:06:55 bselby Exp $
 * @since     File available since 2008-12-17
 */
/**
 * Current Account Requirement Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_ShowCurrentAccount_Test extends PHPUnit_Framework_TestCase
{
    /**
     * PHPUnit setUp function
     *
     */
    public function setUp()
    {

    }

    /**
     * PHPUnit tear down function
     *
     */
    public function tearDown()
    {

    }

    /**
     * Check that the inputs for the requirement are correct
     *
     */
    public function testArrInputIsSetupCorrectly()
    {
        $arrInputs = array(
                'bolChangeAccount' => 'external:Bool'
            );

        $objReq = new AccountChange_ShowCurrentAccount();

        $this->assertAttributeEquals($arrInputs, 'arrInputs', $objReq);
    }
}
