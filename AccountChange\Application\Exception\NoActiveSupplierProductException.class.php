<?php

class AccountChange_NoActiveSupplierProductException extends RuntimeException
{
    protected $message = 'There is no active supplier product for the serviceId - %s.';

    /**
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct($message = "", $code = 0, Throwable $previous = null)
    {
        parent::__construct(sprintf($this->message, $message), $code, $previous);
    }
}
