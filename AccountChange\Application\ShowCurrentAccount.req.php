<?php
/**
 * Show current account Requirement
 *
 * Collecting the data for the broadband selection requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: ShowCurrentAccount.req.php,v 1.2 2009-01-27 07:07:09 bselby Exp $
 * @since     File available since 2008-09-29
 */
/**
 * AccountChange_ShowCurrentAccount class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_ShowCurrentAccount extends Mvc_WizardRequirement
{
    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'bolChangeAccount' => 'external:Bool'
    );
}
