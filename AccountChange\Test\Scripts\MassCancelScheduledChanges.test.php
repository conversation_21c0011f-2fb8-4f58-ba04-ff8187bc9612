<?php
/**
 * AccountChange_MassCancelScheduledChanges scrip tests
 *
 * Testing class for the AccountChange_MassCancelScheduledChanges
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2023 BT
 */
/**
 * AccountChange_MassCancelScheduledChanges scrip tests
 *
 * Testing class for the AccountChange_MassCancelScheduledChanges
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2023 BT
 */
class Test_AccountChange_MassCancelScheduledChanges extends PHPUnit_Framework_TestCase
{
    const SCRIPT_NAME = 'AccountChange_MassCancelScheduledChanges';

    public function setUp()
    {
        $objBusTierMock = $this->getMock(
            'BusTier_BusTier',
            array('getClient'),
            array(), '', false
        );

        $objBusTierMock->expects($this->never())
            ->method('getClient');

        $this->mockDb = Mockery::mock('Db_Adaptor');
        Db_Manager::setAdaptor('AccountChange', $this->mockDb);
    }

    /**
     * Checks the script attempts to process each account change passed in.
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testScriptProcessesListOfServiceIdsCorrectly()
    {
        $inputParams = array(
            'sids' => '123,456,666,7600',
            'suppress-output' => true
        );

        $script = $this->getMockBuilder(AccountChange_MassCancelScheduledChanges)
            ->setMethods(array( 'processCancellation'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->exactly(4))
            ->method('processCancellation');

        $script->run(array(), $inputParams);
    }

    /**
     * Checks the script attempts to process each account change passed in, and skips over any
     * non-numeric service ids
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testScriptOnlyProcessesNumericServiceIds()
    {
        $inputParams = array(
            'sids' => '123,thisIsNotASid,666,7600',
            'suppress-output' => true
        );

        $script = $this->getMockBuilder(AccountChange_MassCancelScheduledChanges)
            ->setMethods(array( 'processCancellation'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->exactly(3))
            ->method('processCancellation');

        $script->run(array(), $inputParams);
    }

}
