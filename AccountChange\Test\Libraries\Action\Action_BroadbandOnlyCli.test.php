<?php

/**
 * <AUTHOR> <<EMAIL>>
 */
class Action_BroadbandOnlyCliTest extends PHPUnit_Framework_TestCase
{
    const OLD_SDI = 123;
    const NEW_SDI = 654;
    const MOCK_SERVICE_ID = ********;

    /**
     * tearDown
     */
    public function tearDown()
    {
        \Mockery::close();
        Db_Manager::reset();
    }

    /**
     * @test
     * @dataProvider removeCliProvider
     */
    public function shouldCallRemoveCliStatementCorrectly($oldIsBbo, $newIsBbo, $dbCallExpected)
    {
        $mockDb = Mockery::mock(Db_Adaptor::class);
        Db_Manager::setAdaptor('AccountChange', $mockDb);

        $mockBroadbandOnlyHelper = Mockery::mock(AccountChange_BroadandOnlyHelper::class);

        $reg = AccountChange_Registry::instance();
        $reg->reset();

        $reg->setEntry('intOldServiceDefinitionId', static::OLD_SDI);
        $reg->setEntry('intNewServiceDefinitionId', static::NEW_SDI);

        $test = $this->getTest();

        $test
            ->shouldReceive('getBroadbandOnlyHelper')
            ->once()
            ->andReturn($mockBroadbandOnlyHelper);

        $mockBroadbandOnlyHelper
            ->shouldReceive('isBroadbandOnlyProduct')
            ->with(static::OLD_SDI)
            ->andReturn($oldIsBbo);

        if (!$oldIsBbo) {
            $mockBroadbandOnlyHelper
                ->shouldReceive('isBroadbandOnlyProduct')
                ->with(static::NEW_SDI)
                ->andReturn($newIsBbo);
        } else {
            $mockBroadbandOnlyHelper
                ->shouldReceive('isBroadbandOnlyProduct')
                ->with(static::NEW_SDI)
                ->never();
        }

        if ($dbCallExpected) {
            $mockDb
                ->shouldReceive('removeCli', 'deactivatePrimaryCliRecords')
                ->once()
                ->with(static::MOCK_SERVICE_ID);
        } else {
            $mockDb
                ->shouldReceive('removeCli', 'deactivatePrimaryCliRecords')
                ->never();
        }

        $this->assertNull($test->execute());
    }

    /**
     * @return AccountChange_Action_BroadbandOnlyCli|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private function getTest()
    {
        return \Mockery::mock(
            'AccountChange_Action_BroadbandOnlyCli[getBroadbandOnlyHelper]',
            [static::MOCK_SERVICE_ID, []]
        )->shouldAllowMockingProtectedMethods();
    }

    /**
     * @return array
     */
    public function removeCliProvider()
    {
        return [
            'DSL to BBO' => [false, true, true],
            'BBO to BBO' => [true, true, false],
            'BBO to DSL' => [true, false, false]
        ];
    }
}
