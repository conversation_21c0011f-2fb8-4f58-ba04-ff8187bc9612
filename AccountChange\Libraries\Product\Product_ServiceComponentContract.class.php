<?php
/**
 * Service Component Contract
 *
 * Holds information about a service component contract
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_ServiceComponentContract.class.php,v 1.2 2009-01-27 07:07:37 bselby Exp $
 * @link      http://confluence.internal.plus.net/display/LRS
 * @since     File available since 2008-08-19
 */
/**
 * Service Component Contract class
 *
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://confluence.internal.plus.net/display/LRS
 */
class AccountChange_Product_ServiceComponentContract
{
    /**
     * Contract Handle
     *
     * Default to MONTHLY, which is our standard length
     *
     * @var string
     */
    protected $strContractLengthHandle = 'MONTHLY';

    /**
     * Payment Frequency
     *
     * Default to MONTHLY, which is our standard frequency
     *
     * @var string
     */
    protected $strPaymentFrequencyHandle = 'MONTHLY';

    /**
     * Product Component Handle
     *
     * Default to SUBSCRIPTION, as this is generally the component with the price
     *
     * @var string
     */
    protected $strProductComponentHandle = 'SUBSCRIPTION';

    /**
     * Tariff Id for the Contract
     *
     * @var int
     */
    protected $intTariffId = null;

    /**
     * Contract end date (Recontracting may want to specify the end date)
     *
     * @var unix time stamp
     */
    protected $uxtContractEndDate = null;

    /**
     * Contract start date
     *
     * @var unix time stamp
     */
    protected $uxtContractStartDate = null;

    /**
     * Tariff type handle
     * LINE_RENTAL_SAVER/DEFAULT
     * @var string
     */
    protected $strTariffTypeHandle = 'DEFAULT';

    /**
     * Constructor for the Service Component Contract
     *
     * @param string $strContractLengthHandle   contract length handle
     * @param string $strPaymentFrequencyHandle payment frequency handle
     * @param string $strProductComponentHandle product component handle
     * @param string $uxtContractEndDate        contract end date
     * @param string $uxtContractStartDate      contract start date
     * @param string $strTariffTypeHandle       tariff type handle DEFAULT/LINE_RENTAL_SAVER
     */
    public function __construct(
        $strContractLengthHandle,
        $strPaymentFrequencyHandle,
        $strProductComponentHandle = 'SUBSCRIPTION',
        $uxtContractEndDate = null,
        $uxtContractStartDate = null,
        $strTariffTypeHandle = 'DEFAULT'
    ) {

        $this->strProductComponentHandle = $strProductComponentHandle;
        $this->strContractLengthHandle   = $strContractLengthHandle;
        $this->strPaymentFrequencyHandle = $strPaymentFrequencyHandle;
        $this->uxtContractEndDate        = $uxtContractEndDate;
        $this->uxtContractStartDate      = $uxtContractStartDate;
        $this->strTariffTypeHandle       = $strTariffTypeHandle;
    }

    /**
     * Getter for the contract lenght handle
     *
     * @return string
     */
    public function getContractLengthHandle()
    {
        return $this->strContractLengthHandle;
    }

    /**
     * Getter for the contract length to display
     *
     * @return string
     */
    public function getContractLengthDisplay()
    {
        return ucfirst(strtolower($this->strContractLengthHandle));
    }

    /**
     * Getter for the payment frequency handle
     *
     * @return string
     */
    public function getPaymentFrequencyHandle()
    {
        return $this->strPaymentFrequencyHandle;
    }

    /**
     * Getter for the product component handle
     *
     * @return string
     */
    public function getProductComponentHandle()
    {
        return $this->strProductComponentHandle;
    }

    /**
     * Getter for the contract end date
     *
     * @return unix time stamp
     */
    public function getContractEndDate()
    {
        return $this->uxtContractEndDate;
    }

    /**
     * Getter for the contract start date
     *
     * @return unix time stamp
     */
    public function getContractStartDate()
    {
        return $this->uxtContractStartDate;
    }

    /**
     * Fetch the tariff id from the database, or if already set just return the instance we already have
     *
     * @param int     $intServiceComponentId service component id
     * @param boolean $bolForce              force flag
     *
     * @return int
     */
    public function fetchTariffId($intServiceComponentId, $bolForce = false)
    {
        if (empty($this->intTariffId) || $bolForce) {

            Dbg_Dbg::write(
                'AccountChange_Product_ServiceComponentContract::fetchTariffId from the database',
                'AccountChange'
            );

            $arrDetails = $this->getProductPaymentFrequencyOptions(
                $intServiceComponentId,
                $this->strProductComponentHandle,
                $this->strContractLengthHandle
            );

            // If the requested contract length is not available, try the default.
            if (empty($arrDetails)) {
                $arrDetails = $this->getProductPaymentFrequencyOptions(
                    $intServiceComponentId,
                    $this->strProductComponentHandle,
                    ''
                );
            }

            if (empty($arrDetails)) {
                throw new AccountChange_AccountConfigurationException(
                    'Unable to identify a contract for Service Component ' . $intServiceComponentId
                );
            }

            $this->intTariffId = $arrDetails[0]['intTariffID'];
        }

        return $this->intTariffId;
    }

    /**
     * Get the cost of the product
     *
     * @param int $intServiceComponentId service component id
     *
     * @return int
     */
    public function getProductCost($intServiceComponentId)
    {
        $objDatabase = Db_Manager::getAdaptor('AccountChange');

        $intProductComponentPrice = $objDatabase->getProductComponentPrice(
            $this->strContractLengthHandle,
            $this->strPaymentFrequencyHandle,
            $intServiceComponentId,
            $this->strProductComponentHandle
        );
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        return sprintf("%.2f", $intProductComponentPrice / 100);
    }

    /**
     * Wrapper function for {@link CProduct::getProductPaymentFrequencyOptions()}
     *
     * @param int    $intServiceComponentId     service component id
     * @param string $strProductComponentHandle product component handle
     * @param string $strContractLengthHandle   contract length handle
     *
     * @return array
     */
    protected function getProductPaymentFrequencyOptions(
        $intServiceComponentId,
        $strProductComponentHandle,
        $strContractLengthHandle
    ) {
        $objProduct = new CProduct();

        return $objProduct->getProductPaymentFrequencyOptions(
            $intServiceComponentId,
            $strProductComponentHandle,
            $strContractLengthHandle,
            $this->strTariffTypeHandle
        );
    }
}
