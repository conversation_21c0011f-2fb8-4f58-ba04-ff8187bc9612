<?php
use Plusnet\C2mApiClient\Entity\Promotion;

class RemoveExpiredPromotionsTest extends PHPUnit_Framework_TestCase
{

    /**
     * @test
     */
    public function itFiltersOutExpiredOrNotYetActivePromotions()
    {
        $promotion1 = new Promotion();
        $promotion1->setCode('Promo1');
        $promotion1->setActiveFrom($this->oneWeekAgo());
        $promotion1->setActiveTo($this->nextWeek());
        $promotion2 = new Promotion();
        $promotion2->setCode('Promo2');
        $promotion2->setActiveFrom($this->oneWeekAgo());
        $promotion2->setActiveTo($this->twoDaysAgo());
        $promotion3 = new Promotion();
        $promotion3->setCode('Promo3');
        $promotion3->setActiveFrom($this->twoDaysAgo());
        $promotion3->setActiveTo($this->nextWeek());
        $promotion4 = new Promotion();
        $promotion4->setCode('Promo4');
        $promotion4->setActiveFrom($this->nextWeek());
        $promotion4->setActiveTo($this->nextMonth());

        $promotions = [$promotion1, $promotion2, $promotion3, $promotion4];

        $promotions = (new RemoveExpiredPromotions())->handle($promotions);

        $this->assertEquals(2, count($promotions));
        $this->assertEquals('Promo1', $promotions[0]->getCode());
        $this->assertEquals('Promo3', $promotions[2]->getCode());
    }

    public function testAllowsPersonalisedPromotionsWhereEndDatePassedButPersonalEndDateHasNot()
    {
        $promotion = new Promotion();
        $promotion->setCode('PromoTest');
        $promotion->setIsPersonalisedOffer(true);
        $promotion->setActiveFrom($this->oneWeekAgo());
        $promotion->setActiveTo($this->twoDaysAgo());
        $promotion->setPersonalisedEndDate($this->nextWeek());
        $promotions = [$promotion];
        $promotions = (new RemoveExpiredPromotions())->handle($promotions);
        $this->assertEquals(1, count($promotions));
        $this->assertEquals('PromoTest', $promotions[0]->getCode());
    }

    public function oneWeekAgo()
    {
        return (new DateTime())->modify('-1 week')->format('Ymd');
    }

    public function twoDaysAgo()
    {
        return (new DateTime())->modify('-2 days')->format('Ymd');
    }

    public function nextWeek()
    {
        return (new DateTime())->modify('+1 week')->format('Ymd');
    }

    public function nextMonth()
    {
        return (new DateTime())->modify('+1 month')->format('Ymd');
    }
}
