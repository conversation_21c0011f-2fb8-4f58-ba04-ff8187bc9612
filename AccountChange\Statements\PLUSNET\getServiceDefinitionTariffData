server: coredb
role: slave
rows: single
statement:

SELECT
	t.intTariff<PERSON>,
	t.intCostIncVatPence,
	cl.vch<PERSON><PERSON><PERSON> as str<PERSON>ont<PERSON>t,
	pf.vchDisplayName as strContractLength,
	scp.intServiceComponentId
FROM userdata.services s
INNER JOIN userdata.components c USING (service_id)
INNER JOIN products.tblServiceComponentProduct scp ON c.component_type_id = scp.intServiceComponentId
INNER JOIN products.tblServiceComponentProductType scpt USING (intServiceComponentProductTypeID)
INNER JOIN userdata.tblProductComponentInstance pci ON c.component_id = pci.intComponentId
INNER JOIN dbProductComponents.tblTariff t USING (intTariffID)
INNER JOIN dbProductComponents.tblContractLength cl USING (intContractLengthID)
INNER JOIN dbProductComponents.tblPaymentFrequency pf ON t.intPaymentFrequencyID = pf.intPaymentFrequencyID
WHERE
	scpt.vchHandle = 'INTERNET_CONNECTION'
	AND s.service_id = :intServiceId
	AND pci.intStatusID = 4