<?php
/**
 * Home Phone Requirement
 *
 * Testing class for the AccountChange_SelectHomephone requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @since     File available since 2008-10-02
 */
/**
 * Home Phone Requirement Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_SelectHomephone_Test extends PHPUnit_Framework_TestCase
{
    /**
     * PHPUnit tear down function
     *
     */
    public function tearDown()
    {
        unset($_SERVER['PLUSNET-FEATURE-TOGGLE']);
    }

    /**
     * @covers AccountChange_SelectHomePhone::describe
     *
     */
    public function testDescribeReturnsAnArrayOfNewProductsThatCanBeSelected()
    {
        $arrProducts = array(array(
                'intNewWlrId'       => AccountChange_Product_WlrProductFilter::JUNE2016_UNLIMITED_UK_WITH_MOBILE,
                'strContractHandle' => 'ANNUAL',
                'strProductName'    => 'Test Wlr Product',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
            ));

        $arrOldCallFeatures = array();

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'wlrChangesAllowed',
                'getWlrCallFeatures',
                'hasActiveLineRentalSaver',
                'isFibreProduct',
                'getWlrProductSorter',
                'getWlrProductFilter',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getDiscountContractLength',
                'isC2fToggleSet',
                'isBroadbandOnlyJourney',
                'getAccountChangeValidator'
            )
        );


        $objReq->method('isC2fToggleSet')->willReturn(false);

        $objReq->method('isBroadbandOnlyJourney')->willReturn(true);

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->will($this->returnValue($arrProducts));

        $objReq->expects($this->once())
               ->method('wlrChangesAllowed');

        $objReq->expects($this->once())
               ->method('getWlrCallFeatures')
               ->will($this->returnValue($arrOldCallFeatures));

        $objReq->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $objReq->expects($this->any())
                ->method('isFibreProduct')
                ->will($this->returnValue(false));

        $objReq->expects($this->any())
                ->method('getSelectedBroadband')
                ->will($this->returnValue(array()));

        $objReq->expects($this->any())
                ->method('getMinAndMaxSpeedRanges')
                ->will($this->returnValue(array('downloadSpeedRangeMin' => null, 'downloadSpeedRangeMax' => null)));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );

        $objCoreService->expects($this->any())
                   ->method('getServiceId')
                   ->will($this->returnValue(1110));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);


        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                 ->method('getServiceDefinitionDetailsForService')
                 ->will($this->returnValue(array('type' => 'residential')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController->expects($this->at(0))
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue(546));

        $objMockController->expects($this->at(1))
                          ->method('isApplicationStateVar')
                          ->will($this->returnValue(true));
        $objReq->setAppStateCallback($objMockController);

        $arrCollectedDataMock = array(
                'arrWlrProduct'  => array('intInstanceId' => 12, 'strContractHandle' => 'ANNUAL'),
                'intNewSdi'      => 546,
                'objCoreService' => $objCoreService
            );

        $mockFilter =  $this->getFilterMock();

        $objReq->expects($this->any())
                ->method('getWlrProductFilter')
                ->will($this->returnValue($mockFilter));

        $mockSorter =  $this->getSorterMock();

        $objReq->expects($this->any())
                ->method('getWlrProductSorter')
                ->will($this->returnValue($mockSorter));

        $validator = $this->getMockBuilder('AccountChange_ValidationCheck')
            ->disableOriginalConstructor()
            ->getMock();

        $validator->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $objReq->expects($this->any())
            ->method('getAccountChangeValidator')
            ->will($this->returnValue($validator));

        $arrReturn = $objReq->describe($arrCollectedDataMock);

        $this->assertArrayHasKey('arrNewProducts', $arrReturn);

        $this->assertEquals($arrProducts, $arrReturn['arrNewProducts']);
    }

    /**
     * @covers AccountChange_SelectHomePhone::describe
     *
     */
    public function testDescribeKnowsWhenSwitchingToIncreasedCharges()
    {
        $_SERVER['PLUSNET-FEATURE-TOGGLE'] = 'wlr-pricing-2013';

        $arrProducts = array(array(
                'intNewWlrId'       => AccountChange_Product_WlrProductFilter::JUNE2016_UNLIMITED_UK_WITH_MOBILE,
                'strContractHandle' => 'MONTHLY',
                'strProductName'    => 'Plusnet Anytime',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
            ));

        $arrOldCallFeatures = array();

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'wlrChangesAllowed',
                'getWlrCallFeatures',
                'hasActiveLineRentalSaver',
                'isFibreProduct',
                'getWlrProductSorter',
                'getWlrProductFilter',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getDiscountContractLength',
                'isC2fToggleSet',
                'isBroadbandOnlyJourney',
                'getAccountChangeValidator'
            )
        );

        $objReq->method('isC2fToggleSet')->willReturn(false);

        $objReq->method('isBroadbandOnlyJourney')->willReturn(true);

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->will($this->returnValue($arrProducts));

        $objReq->expects($this->once())
               ->method('wlrChangesAllowed');

        $objReq->expects($this->once())
               ->method('getWlrCallFeatures')
               ->will($this->returnValue($arrOldCallFeatures));

        $objReq->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(array()));

        $objReq->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will($this->returnValue(array('downloadSpeedRangeMin' => null, 'downloadSpeedRangeMax' => null)));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );

        $objCoreService->expects($this->any())
                   ->method('getServiceId')
                   ->will($this->returnValue(1110));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);

        $validator = $this->getMockBuilder('AccountChange_ValidationCheck')
            ->disableOriginalConstructor()
            ->getMock();

        $validator->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $objReq->expects($this->any())
            ->method('getAccountChangeValidator')
            ->will($this->returnValue($validator));

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                 ->method('getServiceDefinitionDetailsForService')
                 ->will($this->returnValue(array('type' => 'residential')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $map = array(
            'intNewSdi' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL,
            'objCoreService' => $objCoreService,
            'arrWlrProduct' => array(
                'intOldWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
                'strContractHandle' => 'MONTHLY'
            ),
            true
        );
        $objMockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function ($_, $arg) use ($map) {
                        return $map[$arg];
                    }
                )
            );

        $objMockController->expects($this->any())
            ->method('isApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function ($_, $arg) use ($map) {
                        return isset($map[$arg]);
                    }
                )
            );
        $objReq->setAppStateCallback($objMockController);

        $arrCollectedDataMock = array(
                'arrWlrProduct'  => array(
                    'intInstanceId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
                    'strContractHandle' => 'MONTHLY'),
                'intNewSdi'      => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL,
                'objCoreService' => $objCoreService
            );

        $mockFilter = $this->getFilterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductFilter')
            ->will($this->returnValue($mockFilter));

        $mockSorter =  $this->getSorterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductSorter')
            ->will($this->returnValue($mockSorter));

        $arrReturn = $objReq->describe($arrCollectedDataMock);
        $this->assertArrayHasKey('arrNewProducts', $arrReturn);
        $this->assertEquals($arrProducts, $arrReturn['arrNewProducts']);
        $this->assertfalse($arrReturn['callChargesIncreasing']);
    }

    /**
     * @covers AccountChange_SelectHomePhone::describe
     *
     */
    public function testDescribeSelectsProductsWithAnnualContractAndDoesNotIncludeTheNoWlrOption()
    {
        $arrProducts = array(array(
                'intNewWlrId'       => AccountChange_Product_WlrProductFilter::JUNE2016_UNLIMITED_UK_WITH_MOBILE,
                'strContractHandle' => 'ANNUAL',
                'strProductName'    => 'Test Wlr Product',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
            ));

        $arrOldCallFeatures = array();

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'wlrChangesAllowed',
                'getWlrCallFeatures',
                'hasActiveLineRentalSaver',
                'isFibreProduct',
                'getWlrProductSorter',
                'getWlrProductFilter',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getDiscountContractLength',
                'isC2fToggleSet',
                'isBroadbandOnlyJourney',
                'getAccountChangeValidator'
            )
        );

        $objReq->method('isC2fToggleSet')->willReturn(false);

        $objReq->method('isBroadbandOnlyJourney')->willReturn(true);

        $objReq->expects($this->once())
               ->method('wlrChangesAllowed');

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->with($this->equalTo(546), $this->equalTo('ANNUAL'), $this->equalTo(false))
               ->will($this->returnValue($arrProducts));

        $objReq->expects($this->once())
               ->method('getWlrCallFeatures')
               ->will($this->returnValue($arrOldCallFeatures));

        $objReq->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(array()));

        $objReq->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will($this->returnValue(array('downloadSpeedRangeMin' => null, 'downloadSpeedRangeMax' => null)));

        $validator = $this->getMockBuilder('AccountChange_ValidationCheck')
            ->disableOriginalConstructor()
            ->getMock();

        $validator->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $objReq->expects($this->any())
            ->method('getAccountChangeValidator')
            ->will($this->returnValue($validator));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );

        $objCoreService->expects($this->any())
                   ->method('getServiceId')
                   ->will($this->returnValue(1110));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);


        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                 ->method('getServiceDefinitionDetailsForService')
                 ->will($this->returnValue(array('type' => 'residential')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController->expects($this->any())
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue(546));

        $objMockController->expects($this->at(1))
                          ->method('isApplicationStateVar')
                          ->will($this->returnValue(true));

        $objReq->setAppStateCallback($objMockController);

        $arrCollectedDataMock = array(
                'arrWlrProduct' => array('intInstanceId' => 12, 'strContractHandle' => 'ANNUAL'),
                'intNewSdi'     => 546,
                'objCoreService' => $objCoreService
            );

        $mockFilter =  $this->getFilterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductFilter')
            ->will($this->returnValue($mockFilter));

        $mockSorter =  $this->getSorterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductSorter')
            ->will($this->returnValue($mockSorter));

        $arrReturn = $objReq->describe($arrCollectedDataMock);

        $this->assertArrayHasKey('arrNewProducts', $arrReturn);
        $this->assertEquals($arrProducts, $arrReturn['arrNewProducts']);
    }

    /**
     * @covers AccountChange_SelectHomePhone::describe
     *
     */
    public function testDescribeSelectsProductsWithMonthlyContractAndDoesIncludeTheNoWlrOption()
    {
        $arrProducts = array(array(
                'intNewWlrId'       => AccountChange_Product_WlrProductFilter::JUNE2016_UNLIMITED_UK_WITH_MOBILE,
                'strContractHandle' => 'ANNUAL',
                'strProductName'    => 'Test Wlr Product',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
            ));

        $arrOldCallFeatures = array();

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'wlrChangesAllowed',
                'getWlrCallFeatures',
                'hasActiveLineRentalSaver',
                'isFibreProduct',
                'getWlrProductSorter',
                'getWlrProductFilter',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getDiscountContractLength',
                'isC2fToggleSet',
                'isBroadbandOnlyJourney',
                'getAccountChangeValidator'
            )
        );

        $objReq->method('isC2fToggleSet')->willReturn(false);

        $objReq->method('isBroadbandOnlyJourney')->willReturn(true);

        $objReq->expects($this->once())
               ->method('wlrChangesAllowed');

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->with($this->equalTo(546), $this->equalTo('MONTHLY'), $this->equalTo(true))
               ->will($this->returnValue($arrProducts));

        $objReq->expects($this->once())
               ->method('getWlrCallFeatures')
               ->will($this->returnValue($arrOldCallFeatures));

        $objReq->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(array()));

        $objReq->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will($this->returnValue(array('downloadSpeedRangeMin' => null, 'downloadSpeedRangeMax' => null)));

        $validator = $this->getMockBuilder('AccountChange_ValidationCheck')
            ->disableOriginalConstructor()
            ->getMock();

        $validator->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $objReq->expects($this->any())
            ->method('getAccountChangeValidator')
            ->will($this->returnValue($validator));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );

        $objCoreService->expects($this->any())
                   ->method('getServiceId')
                   ->will($this->returnValue(1110));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);


        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                 ->method('getServiceDefinitionDetailsForService')
                 ->will($this->returnValue(array('type' => 'residential')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController->expects($this->any())
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue(546));

        $objMockController->expects($this->at(1))
                          ->method('isApplicationStateVar')
                          ->will($this->returnValue(true));

        $objReq->setAppStateCallback($objMockController);

        $arrCollectedDataMock = array(
                'arrWlrProduct' => array(),
                'intNewSdi'     => 546,
                'objCoreService' => $objCoreService

            );

        $mockFilter =  $this->getFilterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductFilter')
            ->will($this->returnValue($mockFilter));

        $mockSorter =  $this->getSorterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductSorter')
            ->will($this->returnValue($mockSorter));

        $arrReturn = $objReq->describe($arrCollectedDataMock);

        $this->assertInternalType('array', $arrReturn);
        $this->assertInternalType('array', $arrReturn['arrNewProducts']);
        $this->assertArrayHasKey('arrNewProducts', $arrReturn);
        $this->assertEquals($arrProducts, $arrReturn['arrNewProducts']);
    }

    /**
     * Checks for the arrInput variables for Home Phone requirement
     *
     */
    public function testArrInputIsSetupCorrectly()
    {
        $arrInputs = array(
            'intNewWlrId' => 'external:Custom',
            'mobileBoltOn' => 'external:custom:optional',
            'callerDisplay' => 'external:custom:optional'
        );

        $objReq = new AccountChange_SelectHomephone();

        $this->assertAttributeEquals($arrInputs, 'arrInputs', $objReq);
    }

    /**
     * @covers AccountChange_SelectHomePhone::valNewWlrId
     *
     */
    public function testNewWlrIdValidatorReturnsAnInvalidErrorIfNewWlrIdIsNotNumeric()
    {
        $arrProducts = array(array(
                'intNewWlrId'       => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
                'strContractHandle' => 'ANNUAL',
                'strProductName'    => 'Test Wlr Product',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
            ));

        $strInvalidWlrId = array('aBadString' => 'on');

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'getApplicationStateVariable'
            )
        );

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->will($this->returnValue($arrProducts));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('isDualPlay'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->once())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(0))
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue(546));

        $objMockController->expects($this->at(1))
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue(array()));

        $objMockController->expects($this->at(2))
                          ->method('isApplicationStateVar')
                          ->will($this->returnValue(true));

        $objMockController->expects($this->at(3))
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue($mockFamily));

        $objReq->setAppStateCallback($objMockController);

        $arrResult = $objReq->valNewWlrId($strInvalidWlrId);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayNotHasKey('intNewWlrId', $arrResult);
        $this->assertArrayNotHasKey('arrSelectedWlr', $arrResult);

        $arrErrors = $objReq->getValidationErrors();
        $this->assertArrayHasKey('intNewWlrId', $arrErrors);
        $this->assertArrayHasKey('INVALID', $arrErrors['intNewWlrId']);
    }

    /**
     * @covers AccountChange_SelectHomePhone::valNewWlrId
     *
     */
    public function testNewWlrIdValidatorLetsCustomerStayOnNoPhone()
    {
        $arrProducts = array(
            array(
                'intNewWlrId' => 0,
                'strContract' => 'MONTHLY',
                'strProductName' => 'No Home Phone',
                'bolSplitPrice'     => false,
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                'intLineRentCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                'intCallPlanCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
            ),
            array(
                'intNewWlrId'       => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
                'strContractHandle' => 'MONTHLY',
                'strProductName'    => 'Test Wlr Product',
                'bolSplitPrice'     => true,
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                'intLineRentCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                'intCallPlanCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 30),
            ));

        // Selected ID => co-ordinate clicked on button image
        $formInput = array(0 => '23');

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'getApplicationStateVariable',
                'hasActiveLineRentalSaver',
                'isFibreProduct'
            )
        );

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->with(123, 'MONTHLY', true)
               ->will($this->returnValue($arrProducts));

        $objReq->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $productFamily = $this->getMock('Object', array('isDualPlay'));
        $productFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        $map = array(
            'intNewSdi' => 123,
            'arrWlrProduct' => array(),
            'selectedProductFamily' => $productFamily
        );
        $objMockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function ($_, $arg) use ($map) { return $map[$arg];
                    }
                )
            );
        $objMockController->expects($this->any())
            ->method('isApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function ($_, $arg) use ($map) { return isset($map[$arg]);
                    }
                )
            );
        $objReq->setAppStateCallback($objMockController);

        $arrResult = $objReq->valNewWlrId($formInput);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('intNewWlrId', $arrResult);
        $this->assertArrayHasKey('arrSelectedWlr', $arrResult);
        $this->assertInternalType('array', $arrResult['arrSelectedWlr']);
        $this->assertEquals('No Home Phone', $arrResult['arrSelectedWlr']['strNewProduct']);
        $this->assertEquals(0, $arrResult['arrSelectedWlr']['intNewCost']->toDecimal());
        $this->assertEquals(false, $arrResult['arrSelectedWlr']['bolSplitPrice']);
        $this->assertEquals(0, $arrResult['arrSelectedWlr']['intNewLineRentCost']->toDecimal());
        $this->assertEquals(0, $arrResult['arrSelectedWlr']['intNewCallPlanCost']->toDecimal());

        $arrErrors = $objReq->getValidationErrors();
        $this->assertArrayNotHasKey('intNewWlrId', $arrErrors);
    }

    /**
     * @covers AccountChange_SelectHomePhone::valNewWlrId
     *
     */
    public function testNewWlrIdValidatorReturnsAValidWlrIdAndNameAndCost()
    {
        $intValidWlrId   = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME;
        $strValidName    = 'Test Wlr Product';
        $intProductCost  = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10);
        $bolSplitPrice   = 1;
        $intLineRentCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20);
        $intCallPlanCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 30);

        $arrProducts = array(array(
                'intNewWlrId'       => $intValidWlrId,
                'strContractHandle' => 'ANNUAL',
                'strProductName'    => $strValidName,
                'intProductCost'    => $intProductCost,
                'intLineRentCost'   => $intLineRentCost,
                'intCallPlanCost'   => $intCallPlanCost,
                'bolSplitPrice'     => $bolSplitPrice
            ));

        $strInvalidWlrId = array($intValidWlrId => 'on');

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'getApplicationStateVariable',
                'hasActiveLineRentalSaver',
                'isFibreProduct',
                'getWlrProductSorter',
                'getWlrProductFilter'
            )
        );

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->will($this->returnValue($arrProducts));

        $objReq->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('isDualPlay'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->once())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController->expects($this->at(0))
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue(123));

        $objMockController->expects($this->at(1))
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue(array()));

        $objMockController->expects($this->at(2))
                          ->method('isApplicationStateVar')
                          ->will($this->returnValue(true));

        $objMockController->expects($this->at(3))
                          ->method('getApplicationStateVar')
                          ->will($this->returnValue($mockFamily));

        $objReq->setAppStateCallback($objMockController);

        $mockFilter =  $this->getFilterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductFilter')
            ->will($this->returnValue($mockFilter));

        $mockSorter =  $this->getSorterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductSorter')
            ->will($this->returnValue($mockSorter));

        $arrResult = $objReq->valNewWlrId($strInvalidWlrId);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('intNewWlrId', $arrResult);
        $this->assertArrayHasKey('arrSelectedWlr', $arrResult);
        $this->assertInternalType('array', $arrResult['arrSelectedWlr']);
        $this->assertEquals($strValidName, $arrResult['arrSelectedWlr']['strNewProduct']);
        $this->assertEquals($intProductCost, $arrResult['arrSelectedWlr']['intNewCost']);
        $this->assertEquals($bolSplitPrice, $arrResult['arrSelectedWlr']['bolSplitPrice']);
        $this->assertEquals($intLineRentCost, $arrResult['arrSelectedWlr']['intNewLineRentCost']);
        $this->assertEquals($intCallPlanCost, $arrResult['arrSelectedWlr']['intNewCallPlanCost']);

        $arrErrors = $objReq->getValidationErrors();
        $this->assertArrayNotHasKey('intNewWlrId', $arrErrors);
    }

    /**
     * Test for valNewWlrId with mobile bolt-on option
     *
     * @covers AccountChange_SelectHomePhone::valNewWlrId
     *
     * @return void
     */
    public function testNewWlrIdWithMobileBoltOnOptionReturnsAValidWlrIdAndNameAndCost()
    {
        $intValidWlrId = AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS;
        $intMappedWlrId = AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS_WITH_MOBILE;

        $strValidName    = 'Test Wlr Product';
        $intProductCost  = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10);
        $bolSplitPrice   = 1;
        $intLineRentCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20);
        $intCallPlanCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 30);

        $arrProducts = array(array(
            'intNewWlrId'       => $intMappedWlrId,
            'strContractHandle' => 'MONTHLY',
            'strProductName'    => $strValidName,
            'intProductCost'    => $intProductCost,
            'intLineRentCost'   => $intLineRentCost,
            'intCallPlanCost'   => $intCallPlanCost,
            'bolSplitPrice'     => $bolSplitPrice
        ));

        $objReq = $this->getMock(
            'AccountChange_SelectHomephone',
            array(
                'getWlrProducts',
                'getApplicationStateVariable',
                'hasActiveLineRentalSaver',
                'getWlrProductSorter',
                'getWlrProductFilter'
            )
        );

        $objReq->expects($this->once())
            ->method('getWlrProducts')
            ->will($this->returnValue($arrProducts));

        $objReq->expects($this->any())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnValue(123));

        $objMockController->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnValue(false));

        $objReq->setAppStateCallback($objMockController);

        $mockFilter =  $this->getFilterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductFilter')
            ->will($this->returnValue($mockFilter));

        $mockSorter =  $this->getSorterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductSorter')
            ->will($this->returnValue($mockSorter));


        $arrResult = $objReq->valNewWlrId(array($intValidWlrId => 'on'), array($intValidWlrId => 'on'));

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('intNewWlrId', $arrResult);
        $this->assertArrayHasKey('arrSelectedWlr', $arrResult);
        $this->assertInternalType('array', $arrResult['arrSelectedWlr']);
        $this->assertEquals($strValidName, $arrResult['arrSelectedWlr']['strNewProduct']);
        $this->assertEquals($intProductCost, $arrResult['arrSelectedWlr']['intNewCost']);
        $this->assertEquals($bolSplitPrice, $arrResult['arrSelectedWlr']['bolSplitPrice']);
        $this->assertEquals($intLineRentCost, $arrResult['arrSelectedWlr']['intNewLineRentCost']);
        $this->assertEquals($intCallPlanCost, $arrResult['arrSelectedWlr']['intNewCallPlanCost']);

        $arrErrors = $objReq->getValidationErrors();
        $this->assertArrayNotHasKey('intNewWlrId', $arrErrors);
    }

    public function testGetPresetDiscountContractLength()
    {
        $data = array(
            'arrSelectedBroadband' => array(
                    'presetDiscount' => array(
                        'intDiscountLength' => 12
                    )
                )
        );

        $homephoneRequirement = new AccountChange_SelectHomephone();
        $discountLength = $homephoneRequirement->getDiscountContractLength($data);

        $this->assertEquals($discountLength, 12);
    }


    /**
     * @expectedException Exception
     * @expectedExceptionMessage A preset discount exists, but it has no contract length.
     */
    public function testThrowExceptionIfPresetDiscountContractLengthEmpty()
    {
        $data = array(
            'arrSelectedBroadband' => array(
                'presetDiscount' => array(
                    'intDiscountLength' => null
                )
            )
        );

        $homephoneRequirement = new AccountChange_SelectHomephone();
        $discountLength = $homephoneRequirement->getDiscountContractLength($data);
    }


    public function provideDataForGetMappedMobileProductTest()
    {
        return array(
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL,
                'intBoltOnServiceComponentID' =>
                    AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
                'intBoltOnServiceComponentID' =>
                    AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_WITH_MOBILE
            )
        );
    }

    private function getFilterMock()
    {
        $filterMock = $this->getMock(
            'AccountChange_Product_WlrProductFilter',
            array(
                'getPhoneHelper'
            )
        );

        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );

        $phoneHelperMock->expects($this->any())
            ->method('getServiceComponentBoltOnMappings')
            ->will(
                $this->returnValue(
                    $this->provideDataForGetMappedMobileProductTest()
                )
            );

        $filterMock
            ->expects($this->any())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        return $filterMock;
    }


    private function getSorterMock()
    {
        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );

        $phoneHelperMock->expects($this->any())
            ->method('getServiceComponentBoltOnMappings')
            ->will(
                $this->returnValue(
                    $this->provideDataForGetMappedMobileProductTest()
                )
            );



        $sorterMock = $this->getMock(
            'AccountChange_Product_WlrProductSorter',
            array('getPhoneHelper')
        );

        $sorterMock
            ->expects($this->any())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        return $sorterMock;
    }
}
