{"name": "plusnet/active-directory-client", "description": "Active directory Client", "homepage": "https://bitbucket.int.plus.net/projects/SERVICES/repos/activedirectoryclient/browse", "license": "proprietary", "type": "plusnet-framework", "keywords": ["plusnet", "php8.0"], "require": {"php": "~8.0"}, "require-dev": {"composer/installers": "~1.0", "elendev/composer-push": "~0.8.0", "oomphinc/composer-installers-extender": "~2.0.0"}, "autoload": {"psr-0": {"Plusnet\\ActiveDirectoryClient": "src/"}}, "autoload-dev": {"psr-4": {"Plusnet\\ActiveDirectoryClient\\Test\\": "Test/"}}, "config": {"secure-http": false}}