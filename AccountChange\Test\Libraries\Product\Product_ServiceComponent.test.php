<?php
/**
 * Service Component Product Configuration
 *
 * Testing class for the AccountChange_Product_ServiceComponent class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Product_ServiceComponent.test.php,v 1.2 2009-01-27 09:07:53 bselby Exp $
 * @since      File available since 2008-08-19
 */
/**
 * Service Definition Product Configuration Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Product_ServiceComponent_Test extends PHPUnit_Framework_TestCase
{
    /**
     * PHPUnit function
     *
     * All the test needs to mock getServiceDefinitionDao
     */
    protected function setup()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getServiceDefinitionDao'),
                                           array('Core', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);
    }

    /**
     * PHPUnit function
     *
     * Need to clear of the Db_Manager actions
     */
    public function tearDown()
    {
        Db_Manager::commit();
        Db_Manager::restoreAdaptor('AccountChange');
        Db_Manager::restoreAdaptor('Core');
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::getProductId
     *
     */
    public function testGetProductIdReturnsCorrectId()
    {
        $intProductId = 500;

        $objProductConfiguration = new AccountChange_Product_ServiceComponent($intProductId, true);

        $this->assertEquals($intProductId, $objProductConfiguration->getProductId());
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::isKeyProduct
     *
     */
    public function testIsKeyProductReturnsTrueIfTheProductIsAKeyProduct()
    {
        $objProductConfiguration = new AccountChange_Product_Wlr(1, AccountChange_Product_Manager::ACTION_ADD);

        $this->assertTrue($objProductConfiguration->isKeyProduct());
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenAddingNewProduct()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                         ->method('isServiceComponentAllowedOnServiceDefinition')
                         ->will($this->returnValue(1));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        $objProduct1 = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_ADD);
        $objProduct2 = new AccountChange_Product_ServiceComponent(2, AccountChange_Product_Manager::ACTION_NONE);

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objServiceDefinition, $objProduct1));
        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objServiceDefinition, $objProduct2));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenRemovingAProduct()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                         ->method('isServiceComponentAllowedOnServiceDefinition')
                         ->will($this->returnValue(1));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        $objProduct1 = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_REMOVE);
        $objProduct2 = new AccountChange_Product_ServiceComponent(2, AccountChange_Product_Manager::ACTION_NONE);

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objServiceDefinition, $objProduct1));
        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objServiceDefinition, $objProduct2));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenChangingAProduct()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                         ->method('isServiceComponentAllowedOnServiceDefinition')
                         ->will($this->returnValue(1));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        $objProduct1 = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_CHANGE);
        $objProduct2 = new AccountChange_Product_ServiceComponent(2, AccountChange_Product_Manager::ACTION_CHANGE);

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objServiceDefinition, $objProduct1));
        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objServiceDefinition, $objProduct2));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertEquals($objProduct2, $objProduct1->getNewProductConfiguration());
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setMatchingProductConfigurationManually
     *
     */
    public function testSetMatchingProductConfigurationManuallyThrowsExceptionIfTheConfigurationTypesAreDifferent()
    {
        $this->setExpectedException('AccountChange_Product_Exception',
                                    'Matching product is not the same product type',
                                    AccountChange_Product_Exception::ERR_MATCHING_PRODUCT_NOT_VALID);

        $objProduct1 = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_CHANGE);
        $objProduct2 = new AccountChange_Product_ServiceDefinition(2, AccountChange_Product_Manager::ACTION_CHANGE);

        $objProduct1->setMatchingProductConfigurationManually($objProduct2);
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setAccountChangeOperation
     *
     */
    public function testSetAccountChangeOperationWhenUpgradingAProduct()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Service Component
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('getContract', 'getProductCost'),
                                              array(1, AccountChange_Product_Manager::ACTION_CHANGE,
                                                    array('intOldComponentId' => 1, 'intNewComponentId' => 2)));

        $objServiceComponent->expects($this->at(0))
                            ->method('getProductCost')
                            ->will($this->returnValue(10));

        $objServiceComponent->expects($this->at(1))
                            ->method('getProductCost')
                            ->will($this->returnValue(50));

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);
        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objServiceComponent));

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);

        $this->assertAttributeEquals(AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE,
                                     'intAccountChangeOperation',
                                     $objServiceComponent);
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setAccountChangeOperation
     *
     */
    public function testSetAccountChangeOperationWhenDowngradingAProduct()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Service Component
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('getContract', 'getProductCost'),
                                              array(1, AccountChange_Product_Manager::ACTION_CHANGE,
                                                    array('intOldComponentId' => 1, 'intNewComponentId' => 2)));

        $objServiceComponent->expects($this->at(0))
                            ->method('getProductCost')
                            ->will($this->returnValue(100));

        $objServiceComponent->expects($this->at(1))
                            ->method('getProductCost')
                            ->will($this->returnValue(50));

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);
        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objServiceComponent));

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);

        $this->assertAttributeEquals(AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                                     'intAccountChangeOperation',
                                     $objServiceComponent);
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setAccountChangeOperation
     *
     */
    public function testSetAccountChangeOperationWhenKeepingTheProductTheSame()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Service Component
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('getContract', 'getProductCost'),
                                              array(1, AccountChange_Product_Manager::ACTION_NONE,
                                                    array('intOldComponentId' => 1, 'intNewComponentId' => 2)));

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);
        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objServiceComponent));

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);

        $this->assertAttributeEquals(AccountChange_Product_Manager::ACCOUNT_CHANGE_SAME,
                                     'intAccountChangeOperation',
                                     $objServiceComponent);
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setAccountChangeOperation
     *
     */
    public function testSetAccountChangeOpertaionCallsSetAccountChange()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Service Component
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('getContract', 'setAccountChange'),
                                              array(1, AccountChange_Product_Manager::ACTION_CHANGE,
                                                    array('intOldComponentId' => 1, 'intNewComponentId' => 2)));

        $objServiceComponent->expects($this->once())
                            ->method('setAccountChange');

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);
        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objServiceComponent));

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);
    }

    /**
     * @covers AccountChange_Product_ServiceDefinition::setAccountChange
     *
     */
    public function testSetAccountChangeThrowsExceptionIfThereIsNoNewProduct()
    {
        $this->setExpectedException('AccountChange_Product_Exception',
                                    'There is no matching product set',
                                    AccountChange_Product_Exception::ERR_NEW_PRODUCT_NOT_SET);

        $objProduct = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Product_ServiceComponent',
                                                         array(1,1));

        $objProduct->protected_setAccountChange();
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::getAction
     *
     */
    public function testGetAction()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;

        $objProductConfiguration = new AccountChange_Product_ServiceComponent(1, $intAction);

        $this->assertEquals($intAction, $objProductConfiguration->getAction());
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::execute
     *
     */
    public function testExecuteCallsRemoveIfTheActionIsSetToRemove()
    {
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('remove', 'create', 'refresh', 'change'),
                                              array(1, AccountChange_Product_Manager::ACTION_REMOVE));

        $objServiceComponent->expects($this->once())
                            ->method('remove');
        $objServiceComponent->expects($this->never())
                            ->method('create');
        $objServiceComponent->expects($this->never())
                            ->method('refresh');
        $objServiceComponent->expects($this->never())
                            ->method('change');

        $objServiceComponent->execute();
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::execute
     *
     */
    public function testExecuteCallsRefreshIfTheActionIsSetToRefresh()
    {
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('remove', 'create', 'refresh', 'change'),
                                              array(1, AccountChange_Product_Manager::ACTION_REFRESH));

        $objServiceComponent->expects($this->never())
                            ->method('remove');
        $objServiceComponent->expects($this->never())
                            ->method('create');
        $objServiceComponent->expects($this->once())
                            ->method('refresh');
        $objServiceComponent->expects($this->never())
                            ->method('change');

        $objServiceComponent->execute();
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::execute
     *
     */
    public function testExecuteCallsChangeIfTheActionIsSetToChange()
    {
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('remove', 'create', 'refresh', 'change'),
                                              array(1, AccountChange_Product_Manager::ACTION_CHANGE));

        $objServiceComponent->expects($this->never())
                            ->method('remove');
        $objServiceComponent->expects($this->never())
                            ->method('create');
        $objServiceComponent->expects($this->never())
                            ->method('refresh');
        $objServiceComponent->expects($this->once())
                            ->method('change');

        $objServiceComponent->execute();
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::execute
     *
     */
    public function testExecuteCallsCreateIfTheActionIsSetToAdd()
    {
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('remove', 'create', 'refresh', 'change'),
                                              array(1, AccountChange_Product_Manager::ACTION_ADD));

        $objServiceComponent->expects($this->never())
                            ->method('remove');
        $objServiceComponent->expects($this->once())
                            ->method('create');
        $objServiceComponent->expects($this->never())
                            ->method('refresh');
        $objServiceComponent->expects($this->never())
                            ->method('change');

        $objServiceComponent->execute();
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::execute
     *
     */
    public function testExecuteCallsNothingIfTheActionIsNoneOrNoneOfTheValidActions()
    {
        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent',
                                              array('remove', 'create', 'refresh', 'change'),
                                              array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objServiceComponent->expects($this->never())
                            ->method('remove');
        $objServiceComponent->expects($this->never())
                            ->method('create');
        $objServiceComponent->expects($this->never())
                            ->method('refresh');
        $objServiceComponent->expects($this->never())
                            ->method('change');

        $objServiceComponent->execute();
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::initialise
     *
     */
    public function testInitialiseThrowsExceptionIfServiceComponentIdIsNonNumeric()
    {
        $this->setExpectedException('AccountChange_Product_ManagerException',
                                    'Non numeric service component id',
                                    AccountChange_Product_ManagerException::ERR_INVALID_COMPONENT_ID_TYPE);

        $objServiceComponent = new AccountChange_Product_ServiceComponent('strInvalidIntServiceComponentId',
                                                                          AccountChange_Product_Manager::ACTION_NONE);
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setServiceId
     *
     */
    public function testSetServiceIdCorrectlySetsTheServiceId()
    {
        $intServiceId = 9191;

        $objProduct = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_NONE);

        $objProduct->setServiceId($intServiceId);

        $this->assertAttributeEquals($intServiceId, 'intServiceId', $objProduct);
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::setContract
     * @covers AccountChange_Product_ServiceComponent::getContract
     * @covers AccountChange_Product_ServiceComponent::setServiceId
     * @covers AccountChange_Product_ServiceComponent::getServiceId
     * @covers AccountChange_Product_ServiceComponent::setComponentId
     * @covers AccountChange_Product_ServiceComponent::getComponentId
     * @covers AccountChange_Product_ServiceComponent::getTickets
     * @covers AccountChange_Product_ServiceComponent::getServiceNotices
     */
    public function testGetterAndSetters()
    {
        $objContract = new AccountChange_Product_ServiceComponentContract('MONTHLY', 'MONTHLY');
        $intServiceId = 1234;
        $intComponentId = 7654;

        $objProduct = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_NONE);
        $objProduct->setContract($objContract);
        $objProduct->setServiceId($intServiceId);
        $objProduct->setComponentId($intComponentId);

        $this->assertAttributeEquals($objContract, 'objServiceComponentContract', $objProduct);
        $this->assertAttributeEquals($intServiceId, 'intServiceId', $objProduct);
        $this->assertAttributeEquals($intComponentId, 'intComponentId', $objProduct);
        $this->assertAttributeEquals($objProduct->getContract(), 'objServiceComponentContract', $objProduct);
        $this->assertAttributeEquals($objProduct->getServiceId(), 'intServiceId', $objProduct);
        $this->assertAttributeEquals($objProduct->getComponentId(), 'intComponentId', $objProduct);
        $this->assertAttributeEquals($objProduct->getTickets(), '_tickets', $objProduct);
        $this->assertAttributeEquals($objProduct->getServiceNotices(), '_serviceNotices', $objProduct);
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::calculateCancellationCharges
     *
     */
    public function testCalculateCancellationChargesReturnsEmptyArray()
    {
        $objComponent = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_NONE);

        $this->assertEquals(array(), $objComponent->calculateCancellationCharges());
    }

    /**
     * @covers AccountChange_Product_ServiceComponent::isTakingPayment
     *
     */
    public function testIsTakingPaymentReturnsCorrectValue()
    {
        $objComponent = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_NONE);

        $this->assertFalse($objComponent->isTakingPayment());
    }

    /**
     * Test for isScheduled
     *
     * @param bool $bolSchedule          Wether is scheduled account change
     * @param bool $bolScheduleDowngrade Whether is scheduled downgrade
     * @param int  $intType              Account change operation
     * @param bool $output               Expected qoutput
     *
     * @covers AccountChange_Product_ServiceComponent::isScheduled
     *
     * @dataProvider provideIsScheduledData
     *
     * @return void
     */
    public function testIsScheduledWithData($bolSchedule, $bolScheduleDowngrade, $intType, $output)
    {
        $options = array(
            'bolSchedule' => $bolSchedule,
            'bolScheduleDowngrade' => $bolScheduleDowngrade
        );

        $product = $this->getMock(
            'AccountChange_Product_ServiceComponent',
            array('getAccountChangeOperation'),
            array(1234, AccountChange_Product_Manager::ACTION_NONE, $options)
        );

        $product->expects($this->any())
            ->method('getAccountChangeOperation')
            ->will($this->returnValue($intType));

        $actual = $product->isScheduled();

        $this->assertEquals($output, $actual);
    }

    /**
     * Data provider for testIsScheduledWithData
     *
     * @return void
     */
    public static function provideIsScheduledData()
    {
        return array(
            array(
                true,
                true,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                true
            ),
            array(
                true,
                false,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                true
            ),
            array(
                false,
                false,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                false
            ),
        );
    }
}
