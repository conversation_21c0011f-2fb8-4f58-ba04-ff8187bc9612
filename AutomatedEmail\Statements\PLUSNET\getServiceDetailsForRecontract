server: coredb
role: slave
rows: multiple
statement:
SELECT 
	se.service_id,
	se.cli_number
FROM 
	userdata.services AS se
INNER JOIN 
	userdata.components AS co
ON 
	se.service_id = co.service_id
INNER JOIN
	userdata.tblProductComponentInstance AS pci
ON
	co.component_id = pci.intComponentID
INNER JOIN
	dbProductComponents.tblProductComponent AS pc
ON
	pc.intProductComponentID = pci.intProductComponentID
INNER JOIN
	userdata.tblProductComponentContract AS pcc
ON
	pcc.intProductComponentInstanceID = pci.intProductComponentInstanceID
INNER JOIN 
	products.service_components AS sc
ON
	sc.service_component_id = co.component_type_id
INNER JOIN 
	products.tblServiceComponentProduct AS scp
ON 
	scp.intServiceComponentId = sc.service_component_id
INNER JOIN 
	products.tblServiceComponentProductType AS scpt
ON
	scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
WHERE
	pc.vchHandle = 'SUBSCRIPTION'
AND
	scpt.vchHandle = 'INTERNET_CONNECTION' 
AND 
	sc.name LIKE '%Market 3'
AND
	((DATE(NOW()) = DATE_SUB(pcc.dteContractEnd, INTERVAL 2 MONTH)) OR (DATE(NOW()) = DATE_SUB(pcc.dteContractEnd, INTERVAL 14 DAY)));