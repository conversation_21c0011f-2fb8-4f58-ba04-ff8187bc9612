<?php

class AccountChange_AccountChangeApi
{
    /**
     * @var AccountChange_PerformChangeApiFactory|null
     */
    private $performChangeApiFactory;

    /**
     * AccountChange_AccountChangeApi constructor.
     *
     * @param AccountChange_PerformChangeApiFactory $performChangeApiFactory performchange api factory
     */
    public function __construct($performChangeApiFactory = null)
    {
        if ($performChangeApiFactory != null) {
            $this->performChangeApiFactory = $performChangeApiFactory;
        } else {
            $this->performChangeApiFactory = new AccountChange_PerformChangeApiFactory();
        }
    }

    /**
     * @param AccountChange_AccountChangeOrder $accountChangeOrder AccountChangeOrder object
     *
     * @throws AccountChange_InvalidAccountChangeOrderException
     * @throws AccountChange_AccountChangeNotAllowedException
     * @throws AccountChange_AccountChangeApiFailureException
     *
     * @return void
     */
    public function processAccountChangeRequest($accountChangeOrder)
    {
        $accountChangeOrder->validate();
        $performChangeApi = $this->buildPerformChangeApi($accountChangeOrder);

        $allowed = $performChangeApi->isAllowed($accountChangeOrder->isScript());
        if ($allowed === true) {
            try {
                if ($accountChangeOrder->getIsScheduledChange()) {
                    $performChangeApi->doScheduledChange();
                } else {
                    if ($accountChangeOrder->requiresSnapshot()) {
                        $instantChangeHelper = $this->getInstantChangeHelper($accountChangeOrder, $performChangeApi);
                        $instantChangeHelper->takePreChangeSnapshot();
                    }

                    $performChangeApi->doInstantChange();

                    if (isset($instantChangeHelper)) {
                        $instantChangeHelper->takePostChangeSnapshot();
                    }
                }
                $this->createPciCsServiceNotice(
                    $accountChangeOrder->getServiceId(),
                    $accountChangeOrder->getPcics()
                );
                $this->updateJourneyCompletionStatus(
                    $accountChangeOrder->getPcics(),
                    $this->getAccountId($accountChangeOrder->getServiceId())
                );
            } catch (\Exception $exception) {
                throw new AccountChange_AccountChangeApiFailureException(
                    'Exception occurred when scheduling an account change through the account change api. '.
                    'The error message was: ' . $exception->getMessage()
                );
            }

            if ($accountChangeOrder->isCommsRequired()) {
                $emailHandlerFactory = $this->getEmailHandlerFactory($performChangeApi, $accountChangeOrder);
                $emailHandler = $emailHandlerFactory->buildConfirmationEmailForPerformChangeApi();
                foreach($emailHandler as $mail) {
                    $mail->sendEmail($performChangeApi->getServiceId());
                }
            }
        } else {
            throw new AccountChange_AccountChangeNotAllowedException(
                "Account Change is not allowed for the following reason(s): \n" . print_r($allowed, true)
            );
        }
    }

    /**
     * Gets Account id for a service id
     *
     * @param int $serviceId service id
     *
     * @return int
     */
    public function getAccountId($serviceId)
    {
        $db = \Db_Manager::getAdaptor('AccountChange');
        return $db->getAccountIdByServiceId($serviceId);
    }

    /**
     * Gets the details of a scheduled change for a service id
     *
     * @param int $serviceId service id
     *
     * @return array
     */
    public function getScheduledChangeDetails($serviceId)
    {
        $db = \Db_Manager::getAdaptor('AccountChange');
        return $db->getAdslScheduledChange($serviceId);
    }


    /**
     * @param AccountChange_AccountChangeOrder $accountChangeOrder account change order
     *
     * @return AccountChange_PerformChangeApi
     * @throws AccountChange_InvalidAccountChangeOrderException
     */
    private function buildPerformChangeApi(AccountChange_AccountChangeOrder $accountChangeOrder)
    {
        $performChangeApi = $this->performChangeApiFactory->createPerformChangeApi();
        $performChangeApi->init($accountChangeOrder->getServiceId());
        $serviceDefinitionId = $accountChangeOrder->getProducts()->getServiceDefinitionId();
        if ($serviceDefinitionId != null && $serviceDefinitionId != 0) {
            $performChangeApi->setToSdi($serviceDefinitionId);
        } else {
            $getServiceDefinitionId = $this->findServiceDefinitionIdFromServiceDefinitionComponentId(
                $accountChangeOrder->getProducts()->getServiceComponentId()
            );
            $performChangeApi->setToSdi($getServiceDefinitionId);
        }
        if (((int)$accountChangeOrder->getProducts()->getPhoneComponentId()) !== ((int) $accountChangeOrder->getOldPhoneId())) {
            $performChangeApi->setNewWlrServiceComponentId($accountChangeOrder->getProducts()->getPhoneComponentId());
        }
        if (!empty($accountChangeOrder->getOldPhoneId())) {
            $performChangeApi->setOldWlrServiceComponentId($accountChangeOrder->getOldPhoneId());
        }

        $performChangeApi->setTariffIdByServiceDefinitionAndMarket(
            $accountChangeOrder->getProducts()->getServiceDefinitionId(),
            $accountChangeOrder->getMarket()
        );
        $performChangeApi->suppressWlrConfirmationEmail();

        if ($accountChangeOrder->getProducts()->isKeepingPhone()) {
            if ($accountChangeOrder->getProducts()->doesUserWantCallerDisplay()) {
                $performChangeApi->setIncludeCallerDisplay(true);
            } elseif ($accountChangeOrder->getProducts()->doesUserWantCallerDisplay() === false) {
                $performChangeApi->setIncludeCallerDisplay(false);
            }
        }

        if ($accountChangeOrder->getIsRecontract() === true) {
            $performChangeApi->setIsRecontract(true);
        }

        if ($accountChangeOrder->getIsInstantRecontract() === true) {
            $performChangeApi->setIsInstantRecontract(true);
        }

        // If scheduled or not is explicitly set, then set in on the api (else let it default).
        if (!empty($accountChangeOrder->getIsScheduledChange())) {
            $performChangeApi->setIsScheduled($accountChangeOrder->getIsScheduledChange());
        }

        if ($accountChangeOrder->hasPromotionCode()) {
            $performChangeApi->setPromoCode($accountChangeOrder->getPromotionCode());
        }

        if ($accountChangeOrder->getImpressionOfferId() !== null) {
            $performChangeApi->setImpressionOfferId($accountChangeOrder->getImpressionOfferId());
        }

        if ($accountChangeOrder->getOverridenBusinessTierSessionId() !== null) {
            $performChangeApi->setOverrideBusinessTierSessionId(
                $accountChangeOrder->getOverridenBusinessTierSessionId()
            );
        }

        if ($accountChangeOrder->getActorId() !== null) {
            $performChangeApi->setActorId($accountChangeOrder->getActorId());
        }

        if ($accountChangeOrder->hasContract()) {
            $performChangeApi->setContractType($accountChangeOrder->getContract()->getType());
            $performChangeApi->setContractSubType($accountChangeOrder->getContract()->getSubType());
            $performChangeApi->setAgreementDate($accountChangeOrder->getContract()->getAgreementDate());
            $performChangeApi->setRecontractLengthMonths($accountChangeOrder->getContract()->getLength());
            $performChangeApi->setNewContractOrder(true);
        }

        $performChangeApi->setMigrateBroadbandDiscount($accountChangeOrder->doMigrateBroadbandDiscount());
        $performChangeApi->setProductChangeForHouseMove($accountChangeOrder->getIsHouseMove());

        $performChangeApi->setConsentToSwitchData(
            $accountChangeOrder->getServiceId(),
            $accountChangeOrder->convertTypeToAppId(),
            true,
            false,
            $accountChangeOrder->getConsentValue(
                AccountChange_AccountChangeOrder::CONSENT_L2C
            )
        );

        if ($accountChangeOrder->hasConsentValue(
            AccountChange_AccountChangeOrder::CONSENT_PROCEED_WITHOUT_LINE_CHECK
        )) {
            $performChangeApi->setContinueWithoutLineCheckConsentGiven(true);
        }

        if ($accountChangeOrder->getProducts()->getHardwareComponentId()) {
            /** @var HardwareClient_Client $hardwareClient $hardwareClient */
            $hardwareClient = BusTier_BusTier::getClient('hardware');
            $hardwareOption = $hardwareClient->getHandleByServiceComponentId(
                $accountChangeOrder->getProducts()->getHardwareComponentId()
            );

            if ($hardwareOption) {
                $performChangeApi->setHardwareOption($hardwareOption);
            } else {
                error_log(sprintf(
                    'Failed to add hardware to service ID (%s) because cannot find hardware handle from %s',
                    $accountChangeOrder->getServiceId(),
                    $accountChangeOrder->getProducts()->getHardwareComponentId()
                ));
            }
        }
        $oneOffCharges = $accountChangeOrder->getOneOffCharges();
        if (!empty($oneOffCharges)) {
            $performChangeApi->setOneOffCharges($oneOffCharges);
        }
        if ($accountChangeOrder->getAddress() != null) {
            $performChangeApi->setAddress($accountChangeOrder->getAddress());
        }
        $appointment = $accountChangeOrder->getAppointment();
        if ($appointment != null) {
            if ($appointment->getManualAppointment() != null) {
                $helper = $this->getAppointmentRebookerHelper($accountChangeOrder, $appointment);
                $appointment = $helper->attemptToRebookFromManualAppointment();
                $accountChangeOrder->setAppointment($appointment);
            } elseif ($appointment->getLiveAppointment() != null) {
                $helper = $this->getAppointmentSavingHelper(
                    $appointment,
                    $accountChangeOrder->getServiceId()
                );
                $helper->saveLiveAppointmentToDb();
            }
            $performChangeApi->setAppointment($appointment);
        }

        if ($accountChangeOrder->isRetainCurrentContracts() != null) {
            $performChangeApi->setRetainContract($accountChangeOrder->isRetainCurrentContracts());
        }

        if (!empty($accountChangeOrder->getBackDatedDate())) {
            $performChangeApi->setBackDatedDate(
                $accountChangeOrder->getBackDatedDate()->format('d/m/Y')
            );
        }

        if (empty($performChangeApi->getCurrentSdi())) {
            $arrExistingDetails = $this->getExistingProductDetails($accountChangeOrder->getServiceId());
            $performChangeApi->setCurrentSdi($arrExistingDetails['broadbandDetails']['service_definition_id']);
        }

        $schedulingHelper = $this->getSchedulingHelper();
        $performChangeApi->setScheduledChangeDate(
            $schedulingHelper->calculateChangeDateByOrder(
                $accountChangeOrder,
                $performChangeApi
            )
        );

        $saveMgals = true;
        if ($performChangeApi->isRecontract() || $this->isPhoneOnlyChange($performChangeApi)) {
            $saveMgals = false;
        }

        $performChangeApi->setShouldSaveScheduledMGALS($saveMgals);

        return $performChangeApi;
    }

    /**
     * @param AccountChange_AccountChangeAppointment $appointment Appointment to save
     * @param int                                    $serviceId   Service id
     * @return AccountChange_AppointmentSavingHelper
     */
    protected function getAppointmentSavingHelper($appointment, $serviceId)
    {
        return new AccountChange_AppointmentSavingHelper(
            $appointment,
            $serviceId
        );
    }

    /**
     * @param AccountChange_AccountChangeOrder       $accountChangeOrder account change order
     * @param AccountChange_AccountChangeAppointment $appointment        appointment
     * @return AccountChange_AppointmentRebookerHelper
     */
    protected function getAppointmentRebookerHelper($accountChangeOrder, $appointment)
    {
        return new AccountChange_AppointmentRebookerHelper(
            $accountChangeOrder->getAddress(),
            $appointment,
            $accountChangeOrder->getServiceId()
        );
    }

    /**
     * @return AccountChange_SchedulingHelper
     */
    protected function getSchedulingHelper()
    {
        return new AccountChange_SchedulingHelper();
    }

    /**
     * Wrapper function for unit testing
     *
     * @param AccountChange_PerformChangeApi   $performChangeApi   perform change api object
     * @param AccountChange_AccountChangeOrder $accountChangeOrder account change order object
     *
     * @return AccountChange_EmailHandler_Factory
     */
    protected function getEmailHandlerFactory($performChangeApi, $accountChangeOrder)
    {
        return new AccountChange_EmailHandler_Factory($performChangeApi, $accountChangeOrder);
    }

    /**
     * Update product scheduled change date
     *
     * @param string $scheduledDate new scheduled Date
     * @param int    $intServiceId  service id
     *
     * @throws Db_TransactionException
     *
     * @return boolean true if valid data passed
     */
    public function updateProductScheduledChangeDate($scheduledDate, $intServiceId)
    {
        if (is_int($intServiceId)
            && is_string($scheduledDate)
            && (false !== DateTime::createFromFormat('Y-m-d', $scheduledDate))
        ) {
            $db = \Db_Manager::getAdaptor('AccountChange');
            $db->updateProductChangeScheduledDate($scheduledDate, $intServiceId);
            \Db_Manager::commit();
            return true;
        } else {
            return false;
        }
    }

    /**
     * Gets the current products an account has from the service id.
     * If an optional AccountChange_AccountChangeOrder object is passed in, then current phone product
     * will be added to that object.
     *
     * @param int $serviceId Service id
     *
     * @return array
     */
    public function getExistingProductDetails($serviceId)
    {
        if (is_numeric($serviceId)) {
            $account = new AccountChange_Account(new Int($serviceId));
            return array(
              'wlrDetails' => $account->getWlrInformation(),
              'broadbandDetails' => $account->getBroadbandDetails()
            );
        }
        return false;
    }

    /**
     * Function to find service definition id from service definition component Id.
     * @param int $toComponentSdi component service definition
     * @return int
     */
    private function findServiceDefinitionIdFromServiceDefinitionComponentId($toComponentSdi)
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $toSdi = $db->getAccountChangeServiceDefinitionIdFromServiceComponentID($toComponentSdi);
        return $toSdi;
    }

    /**
     * @param AccountChange_AccountChangeOrder $accountChangeOrder account change order
     * @param AccountChange_PerformChangeApi   $performChangeApi   perform change api
     * @return AccountChange_InstantChangeHelper
     * @throws Exception
     */
    protected function getInstantChangeHelper($accountChangeOrder, $performChangeApi)
    {
        $broadbandChanging = $performChangeApi->getToSdi() !== $performChangeApi->getOldSdi();

        return new AccountChange_InstantChangeHelper(
            $accountChangeOrder->getServiceId(),
            $broadbandChanging,
            $accountChangeOrder->getBackDatedDate()
        );
    }

    /**
     * @param AccountChange_PerformChangeApi $performChangeApi performChangeApi
     * @return bool
     */
    private function isPhoneOnlyChange(AccountChange_PerformChangeApi $performChangeApi)
    {
        $broadbandChanging = $performChangeApi->getCurrentSdi() !== $performChangeApi->getToSdi();
        $phoneChanging = $performChangeApi->getOldWlrServiceComponentId() !== $performChangeApi->getNewWlrServiceComponentId();

        return !$broadbandChanging && $phoneChanging;
    }

    /**
     * Raise a service notice for PCI CS
     *
     * @param int                              $serviceId Service Id
     * @param AccountChange_AccountChangePcics $pcics     PCICS data
     *
     * @return void
     */
    public function createPciCsServiceNotice(
        $serviceId,
        $pcics
    )
    {
        if($pcics !== null) {
            $client = \BusTier_BusTier::getClient('serviceNotices');

            $recipientActor = $this->getBusinessActor($serviceId);

            $serviceNoticeType = $client->getNoticeTypeByHandle('Script');
            $serviceNoticeTypeId = $serviceNoticeType->getServiceNoticeTypeId();

            $serviceNotice = $client->createServiceNotice($recipientActor);

            $serviceNotice->comment(
                $recipientActor,
                $this->buildPciCsServiceNote(
                    $pcics->getNotificationSent(),
                    $pcics->getStreamingUrl()
                ),
                $serviceNoticeTypeId
            );

            \Db_Manager::commit(\Db_Manager::DEFAULT_TRANSACTION);
        }
    }

    /**
     * Sends a request to PDFGen to update Journey Completion Status.
     *
     * @return void
     */
    private function updateJourneyCompletionStatus($pcics, $accountId)
    {
        if($pcics !== null) {
            $client = \BusTier_BusTier::getClient('pdfgenClient');

            $client->updateJourneyCompletionStatus(
                $pcics->getStreamingId(),
                array(
                    'orderId' => $pcics->getOrderId(),
                    'accountId' => $accountId,
                )
            );
            \Db_Manager::commit(\Db_Manager::DEFAULT_TRANSACTION);
        }
    }

    /**
     * Gets the customer's business actor from legacy code.
     * The method is protected to allow for mocking.
     *
     * @param int $serviceId Service Id
     *
     * @return \Auth_BusinessActor The business actor
     */
    protected function getBusinessActor($serviceId)
    {
        return \Auth_BusinessActor::getActorByExternalUserId($serviceId);
    }

    /**
     * Generates text for the PCI/CS ticket.
     *
     * @param bool   $notificationSent Notification Sent Status
     * @param string $streamingUrl     Streaming Url
     *
     * @return string The ticket text
     */
    private function buildPciCsServiceNote($notificationSent = false, $streamingUrl = null)
    {
        if ($notificationSent) {
            $message = "PCI/CS was generated for this customer on the above date.\n\n";
            $emailText = 'Yes';
        } else {
            $message = "PCI/CS failed to generate for this customer on the above date.\n\n";
            $emailText = 'No';
        }

        $pdfText = empty($streamingUrl) ? 'Not generated' : $streamingUrl;

        $message .= "PDF link: $pdfText\n" . "Email sent: $emailText\n";

        return $message;
    }
}
