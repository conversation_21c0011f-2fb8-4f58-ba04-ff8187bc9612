<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_HardwareRequirementHelper
{
    const PRODUCT_TYPE_COPPER = 'Copper';
    const PRODUCT_TYPE_FTTC = 'Fttc';
    const JOURNEY_TYPE_STRING_TEMPLATE = '%sTo%s';

    /**
     * @var int $oldServiceDefinition
     */
    private $oldServiceDefinitionId;

    /**
     * @var int $newServiceDefinition
     */
    private $newServiceDefinitionId;

    /**
     * @var AccountChange_FibreHelper
     */
    private $fibreHelper;

    /**
     * @var string
     */
    private $journeyType;

    /**
     * @var HardwareClient_HardwareToggleHelper
     */
    private $toggleHelper;

    /**
     * @var HardwareClient_HardwareOfferHelper
     */
    private $hardwareOfferHelper;

    /**
     * @var HardwareClient_HardwareScenario
     */
    private $hardwareScenario;

    /**
     * @var Core_Service
     */
    private $coreService;

    /**
     * @var bool
     */
    private $isHouseMove;

    /**
     * @var bool
     */
    private $isRecontract;

    /**
     * @var bool
     */
    private $isPortal;

    /**
     * @param HardwareClient_Client     $hardwareClient
     * @param Core_Service              $coreService
     * @param bool                      $isHouseMove
     * @param bool                      $isRecontract
     * @param int                       $newServiceDefinitionId
     * @param AccountChange_FibreHelper $fibreHelper
     * @param bool                      $isPortal
     */
    public function __construct(
        HardwareClient_Client $hardwareClient,
        Core_Service $coreService,
        $isHouseMove,
        $isRecontract,
        $newServiceDefinitionId,
        AccountChange_FibreHelper $fibreHelper,
        $bolPortal = false
    ) {
        $this->isPortal = $bolPortal;
        $this->coreService = $coreService;
        $this->isHouseMove = $isHouseMove;
        $this->isRecontract = $isRecontract;
        $this->oldServiceDefinitionId = (int)$coreService->getType();
        $this->newServiceDefinitionId = $newServiceDefinitionId;
        $this->fibreHelper = $fibreHelper;
        $this->determineJourneyType();
        $this->setHelpers($hardwareClient);
        $this->buildHardwareScenario();
    }

    /**
     * Based on isp, customer type, should we use this helper to retrieve
     * the hardware offer or
     *
     * @return bool
     */
    public function shouldUseHelper()
    {
        return $this->toggleHelper->checkAllToggles();
    }

    /**
     * @return bool
     */
    public function shouldDisplayHardwareMessage()
    {
        return $this->toggleHelper->isHubTwoToggleOn();
    }

    /**
     * @return bool
     */
    public function shouldShowHardwarePage()
    {
        if ($this->hardwareOfferHelper->canOfferHardware()) {
            $offer = $this->getHardwareOfferDetails();
            return $offer->shouldOfferHardware();
        }

        return false;
    }

    /**
     * @return HardwareClient_HardwareOffer
     */
    public function getHardwareOffer()
    {
        return $this->getHardwareOfferDetails();
    }

    /**
     * @return void
     */
    private function determineJourneyType()
    {
        $this->journeyType = sprintf(
            static::JOURNEY_TYPE_STRING_TEMPLATE,
            $this->determineProductType($this->oldServiceDefinitionId),
            $this->determineProductType($this->newServiceDefinitionId)
        );
    }

    /**
     * @param int $serviceDefinitionId service definition id
     * @return string
     */
    private function determineProductType($serviceDefinitionId)
    {
        return $this->fibreHelper->isFibreProduct($serviceDefinitionId) ?
            static::PRODUCT_TYPE_FTTC :
            static::PRODUCT_TYPE_COPPER;
    }

    /**
     * @return bool
     */
    private function determineReContract(){
        if ($this->isPortal) {
            return $this->isChangingProduct();
        }
        return $this->isRecontract;
    }

    /**
     * @return bool
     */
    protected function isChangingProduct()
    {
        return $this->oldServiceDefinitionId !== $this->newServiceDefinitionId;
    }

    /**
     * @return HardwareClient_HardwareOffer
     */
    private function getHardwareOfferDetails()
    {
        return $this->hardwareOfferHelper->getHardwareOfferDetails(
            $this->hardwareScenario
        );
    }

    /**
     * @param HardwareClient_Client $hardwareClient
     * @return void
     */
    private function setHelpers($hardwareClient)
    {
        $this->toggleHelper = $hardwareClient->getHardwareToggleHelper(
            $this->coreService->getIsp(),
            $this->coreService->getReportSector()
        );

        $this->hardwareOfferHelper = $hardwareClient->getHardwareOfferHelper(
            $this->newServiceDefinitionId
        );
    }

    /**
     * @return void
     */
    private function buildHardwareScenario()
    {
        $this->hardwareScenario = new HardwareClient_HardwareScenario();
        $this->hardwareScenario->setServiceId(
            (int)$this->coreService->getServiceId()
        );
        $this->hardwareScenario->setIsChangingProduct($this->isChangingProduct());
        $this->hardwareScenario->setIsRecontract($this->determineReContract());
        $this->hardwareScenario->setIsHousemove($this->isHouseMove);
        $this->hardwareScenario->setJourneyType($this->journeyType);
    }
}
