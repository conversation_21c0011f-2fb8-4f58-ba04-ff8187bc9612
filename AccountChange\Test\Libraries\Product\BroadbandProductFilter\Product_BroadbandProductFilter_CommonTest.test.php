<?php

/**
 * <AUTHOR>
 */

class AccountChange_Product_BroadbandProductFilter_CommonTest extends PHPUnit_Framework_TestCase
{
    const PRODUCT_FAMILY = 'ProductFamily';

    /**
     * Tear down
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::reset();
    }

    /**
     * @return void
     */
    public function testShouldGetOnlyBizGoLargeProductsFromAvailableProducts()
    {
        $mockDbAdaptor = Mockery::mock(Db_Adaptor::class);
        $mockDbAdaptor->shouldReceive('getProductVariant')
            ->andReturns(
                array(array(
                    'serviceDefinitionId' => 0,
                    'variant' => ProductFamily_BizGoLarge::BUSINESS_ADSL_VARIANT)),
                array(array(
                    'serviceDefinitionId' => 1,
                    'variant' => ProductFamily_BizGoLarge::BUSINESS_FIBRE_VARIANT)),
                array(array(
                    'serviceDefinitionId' => 3,
                    'variant' => ProductFamily_Brp12Dual::B_FIB_12M_DP_VARIANT)),
                array(array(
                    'serviceDefinitionId' => 4,
                    'variant' => ProductFamily_Brp12Solus::B_FIB_24M_SOL_VARIANT)),
                array(array(
                    'serviceDefinitionId' => 5,
                    'variant' => ProductFamily_PlusnetSolusResApr2014::ULTD_FIBRE_80))
            );
        Db_Manager::setAdaptor(self::PRODUCT_FAMILY, $mockDbAdaptor);

        $adslProduct = new ProductFamily_BizGoLarge(0);
        $fibreProduct = new ProductFamily_BizGoLarge(1);
        $products = [
            ['name' => 'product 1', 'productFamily' => $adslProduct],
            ['name' => 'product 2', 'productFamily' => new ProductFamily_Brp12Dual(3)],
            ['name' => 'product 3', 'productFamily' => $fibreProduct],
            ['name' => 'product 4', 'productFamily' => new ProductFamily_Brp12Solus(4)],
            ['name' => 'product 5', 'productFamily' => new ProductFamily_PlusnetSolusResApr2014(5)],
        ];

        $expectedProducts = [
            0 => ['name' => 'product 1', 'productFamily' => $adslProduct],
            2 => ['name' => 'product 3', 'productFamily' => $fibreProduct],
        ];

        $this->assertEquals(
            $expectedProducts,
            AccountChange_Product_BroadbandProductFilter_Common::getAvailableBizGoLargeProducts($products)
        );
    }
}
