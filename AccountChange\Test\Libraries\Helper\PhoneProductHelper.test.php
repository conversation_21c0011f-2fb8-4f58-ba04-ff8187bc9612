<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_PhoneProductHelperTest extends PHPUnit_Framework_TestCase
{
    /**
     * @covers AccountChange_Product_WlrProductFilter::getMobileBoltOnMapping
     * @covers AccountChange_PhoneProductHelper::getServiceComponentIdToToBoltOnMap
     * @covers AccountChange_PhoneProductHelper::getServiceComponentBoltOnMappings
     */
    public function testGetServiceComponentIdToToBoltOnMap()
    {
        $boltOnMapping = array(
            array('intServiceComponentID' => 1333, 'intBoltOnServiceComponentID' => 1443),
            array('intServiceComponentID' => 1334, 'intBoltOnServiceComponentID' => 1444),
            array('intServiceComponentID' => 1335, 'intBoltOnServiceComponentID' => 1445),
            array('intServiceComponentID' => 1445, 'intBoltOnServiceComponentID' => null)
        );

        $expectedResult = array(
            1333 => 1443,
            1334 => 1444,
            1335 => 1445,
            1445 => null
        );

        $db = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentMobileBoltOnMapping'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $db->expects($this->once())
            ->method('getServiceComponentMobileBoltOnMapping')
            ->will($this->returnValue($boltOnMapping));

        Db_Manager::setAdaptor('AccountChange', $db);

        $helper = new AccountChange_PhoneProductHelper();
        $this->assertEquals($expectedResult, $helper->getServiceComponentIdToToBoltOnMap());
    }
}
