<?php
require_once '/local/data/mis/database/database_libraries/product-access.inc';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/database_libraries/financial-access.inc';
/**
 * Account Change LegacyComponents Action Test
 *
 * @category  AccountChnage_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-14
 */
/**
 * Account Change LegacyComponents Action Test
 *
 * Testing class for AccountChange_Action_LegacyComponents_Test
 *
 * @category  AccountChange_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Action_LegacyComponents_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Service Id fixture
     *
     * @var void
     */
    private $serviceId = 123;

    /**
     * Test that execute gets called and setup correctly
     *
     * @covers AccountChange_Action_LegacyComponents::execute
     *
     * @return void
     */
    public function testExecuteCorrectlySetsUpTheObjectAllCallsCorrectSubFunctions()
    {
        $this->registry = AccountChange_Registry::instance();
        $this->registry->setEntry('intNewServiceDefinitionId', 1);
        $this->registry->setEntry('arrLegacyComponentTypesToIgnore', array());
        $this->registry->setEntry('arrLegacyComponentsToAdd', array());
        $this->registry->setEntry('arrLegacyComponentsToRemove', array());
        $this->registry->setEntry('arrLegacyComponentNotToKeep', array());

        $action = $this->getMock('AccountChange_Action_LegacyComponents',
                                 array('processLegacyComponents'),
                                 array($this->serviceId));

        $action->expects($this->once())
               ->method('processLegacyComponents');

        $action->execute();
    }

    /**
     * @covers AccountChange_Action_LegacyComponents::processLegacyComponents
     *
     * @return void
     */
    public function testProcessLegacyComponentsDoesNotCallLegacyFunctionToSetupTheConfigurationIfThereAreNoComponentsToAddOrRemove()
    {
        $action = $this->getMock('AccountChange_Action_LegacyComponents',
                                 array('setupGlobalConfigurator', 'populateLegacyComponentArrays'),
                                 array($this->serviceId));

        $action->expects($this->never())
               ->method('setupGlobalConfigurator');

        $action->expects($this->once())
               ->method('populateLegacyComponentArrays');

        $action->processLegacyComponents();
    }

    /**
     * @covers AccountChange_Action_LegacyComponents::processLegacyComponents
     *
     * @return void
     */
    public function testProcessLegacyComponentsCallsSetupGlobalConfigurationIfThereAreComponentsToAdd()
    {
        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_component_add'), array(), '', false);

        $objUserdataSplitter->expects($this->atLeastOnce())
                            ->method('userdata_component_add')
                            ->will($this->returnValue(1));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $action = $this->getMock('AccountChange_Action_LegacyComponents',
                                 array('setupGlobalConfigurator', 'includeLegacyFiles', 'populateLegacyComponentArrays'),
                                 array($this->serviceId));

        $action->expects($this->atLeastOnce())
               ->method('setupGlobalConfigurator');

        $action->expects($this->any())
               ->method('includeLegacyFiles');

        $action->expects($this->once())
               ->method('populateLegacyComponentArrays');

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('arrLegacyComponentsToAdd', array(1, 2));
        $registry->setEntry('arrLegacyComponentsToRemove', array());
        $registry->setEntry('arrLegacyComponentTypesToIgnore', array(9));
        $registry->setEntry('arrLegacyComponentNotToKeep', array());

        $action->execute();
    }

    /**
     * @covers AccountChange_Action_LegacyComponents::processLegacyComponents
     *
     * @return void
     */
    public function testProcessLegacyComponentsDoesNotCallSetupGlobalConfigurationIfTheArrayOfLegacyComponentsToAddDoesNotContainNumericValues()
    {
        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_component_add'), array(), '', false);

        $objUserdataSplitter->expects($this->never())
                            ->method('userdata_component_add')
                            ->will($this->returnValue(1));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $action = $this->getMock('AccountChange_Action_LegacyComponents',
                                 array('setupGlobalConfigurator', 'includeLegacyFiles', 'populateLegacyComponentArrays'),
                                 array($this->serviceId));

        $action->expects($this->never())
               ->method('setupGlobalConfigurator');

        $action->expects($this->any())
               ->method('includeLegacyFiles');

        $action->expects($this->once())
               ->method('populateLegacyComponentArrays');

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('arrLegacyComponentsToAdd', array('invalidStringOne', 'invalidStringTwo'));
        $registry->setEntry('arrLegacyComponentsToRemove', array());
        $registry->setEntry('arrLegacyComponentTypesToIgnore', array(9));
        $registry->setEntry('arrLegacyComponentNotToKeep', array());

        $action->execute();
    }

    /**
     * @covers AccountChange_Action_LegacyComponents::processLegacyComponents
     *
     * @return void
     */
    public function testProcessLegacyComponentsCallsSetupGlobalConfigurationIfThereAreComponentsToRemove()
    {
        $arrComponent1 = array('component_type_id' => 1, 'component_id' => 1234);
        $arrComponent2 = array('component_type_id' => 2, 'component_id' => 1234);
        $arrComponents = array($arrComponent1, $arrComponent2);

        $action = $this->getMock('AccountChange_Action_LegacyComponents',
                                 array('setupGlobalConfigurator', 'includeLegacyFiles', 'populateLegacyComponentArrays'),
                                 array($this->serviceId));

        $action->expects($this->atLeastOnce())
               ->method('setupGlobalConfigurator');

        $action->expects($this->once())
               ->method('populateLegacyComponentArrays');

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('arrLegacyComponentsToAdd', array());
        $registry->setEntry('arrLegacyComponentsToRemove', $arrComponents);
        $registry->setEntry('arrLegacyComponentTypesToIgnore', array(9));

        $action->execute();
    }

    /**
     * @covers AccountChange_Action_LegacyComponents::processLegacyComponents
     *
     * @return void
     */
    public function testProcessLegacyComponentsCallsSetupGlobalConfigurationIfThereAreComponentsNotToKeep()
    {
        $arrComponent1 = array('component_type_id' => 1, 'component_id' => 1234);
        $arrComponent2 = array('component_type_id' => 2, 'component_id' => 1234);
        $arrComponents = array($arrComponent1, $arrComponent2);

        $action = $this->getMock('AccountChange_Action_LegacyComponents',
                                 array('setupGlobalConfigurator', 'includeLegacyFiles', 'populateLegacyComponentArrays'),
                                 array($this->serviceId));

        $action->expects($this->exactly(2))
               ->method('setupGlobalConfigurator');

        $action->expects($this->once())
               ->method('populateLegacyComponentArrays');

        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_component_get'), array(), '', false);

        $objUserdataSplitter->expects($this->exactly(2))
                            ->method('userdata_component_get')
                            ->will($this->returnValue(array('component_type_id' => 123, 'component_id' => 123)));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('arrLegacyComponentsToAdd', array());
        $registry->setEntry('arrLegacyComponentsToRemove', array());
        $registry->setEntry('arrLegacyComponentNotToKeep', $arrComponents);

        $action->execute();
    }

    /**
     * @covers AccountChange_Action_LegacyComponents::setupGlobalConfigurator
     *
     * @return void
     */
    public function testThatChangingFromADSLToFibreSupressesTheBtOrder() {

        $componentTypeIdforAnnexM = 938;
        $destroySignal = 'auto_destroy';

        $supressOrderKey = "placeOrderToBT";

        $action = new AccountChange_Action_LegacyComponents($this->serviceId);

        $result = $action->getConfiguratorOptions($componentTypeIdforAnnexM, $destroySignal, true);

        $this->assertArrayHasKey($supressOrderKey, $result);
        $this->assertFalse($result[$supressOrderKey]);
    }

    public function testHardwareComponentsRemoval() {
        $action = $this->getMock('AccountChange_Action_LegacyComponents',
            array('setupGlobalConfigurator',
                'includeLegacyFiles',
                'userdataGetComponentsForService',
                'userdataGetComponentsToRemove',
                'removeHardwareComponentsFromComponentIdsArray',
                'userdataGetLegacyComponentsToAdd',
                'getEntry'),
            array($this->serviceId));

        $serviceId = $this->serviceId;

        $componentsForService = [
            0 => [
                'component_id' => "********",
                'service_id' => $serviceId,
                'component_type_id' => "1651",
                'config_id' => "-1",
                'description' => "",
                'status' => "active",
                'db_src' => "",
                'creationdate' => "2022-08-01 14:01:13",
                'timestamp' => "2022-08-01 14:01:22"
            ],
            1 => [
                'component_id' => "********",
                'service_id' => $serviceId,
                'component_type_id' => "1952",
                'config_id' => "-1",
                'description' => "",
                'status' => "queued-activate",
                'db_src' => "",
                'creationdate' => "2022-08-01 14:01:13",
                'timestamp' => "2022-08-01 14:01:22"
            ],
            2 => [
                'component_id' => "49839659",
                'service_id' => $serviceId,
                'component_type_id' => "1523",
                'config_id' => "-1",
                'description' => "",
                'status' => "active",
                'db_src' => "",
                'creationdate' => "2022-08-01 14:01:13",
                'timestamp' => "2022-08-01 14:01:22"
            ],
            3 => [
                'component_id' => "59839656",
                'service_id' => $serviceId,
                'component_type_id' => "2322",
                'config_id' => "-1",
                'description' => "",
                'status' => "queued-activate",
                'db_src' => "",
                'creationdate' => "2022-08-01 14:01:13",
                'timestamp' => "2022-08-01 14:01:22"
            ],
        ];

        $action->expects($this->once())->method('userdataGetComponentsForService')->will($this->returnValue($componentsForService));

        $componentsToRemove = [
            0 => [
                'component_id' => "********",
                'service_id' => $serviceId,
                'component_type_id' => "1651",
                'config_id' => "-1",
                'description' => "",
                'status' => "active",
                'db_src' => "",
                'creationdate' => "2022-08-01 14:01:13",
                'timestamp' => "2022-08-01 14:01:22"
            ],
            1 => [
                'component_id' => "********",
                'service_id' => $serviceId,
                'component_type_id' => "1952",
                'config_id' => "-1",
                'description' => "",
                'status' => "queued-activate",
                'db_src' => "",
                'creationdate' => "2022-08-01 14:01:13",
                'timestamp' => "2022-08-01 14:01:22"
            ],
        ];

        $action->expects($this->once())->method('userdataGetComponentsToRemove')->will($this->returnValue($componentsToRemove));

        $legacyComponentsToAdd = [
            0 => [
                'component_id' => "12345678",
                'service_id' => $serviceId,
                'component_type_id' => "1234",
                'config_id' => "-1",
                'description' => "",
                'status' => "active",
                'db_src' => "",
                'creationdate' => "2022-08-01 14:01:13",
                'timestamp' => "2022-08-01 14:01:22"
            ],
        ];

        $action->expects($this->once())->method('userdataGetLegacyComponentsToAdd')->will($this->returnValue($legacyComponentsToAdd));

        $action->expects($this->once())->method('removeHardwareComponentsFromComponentIdsArray')
            ->with([0 => '1651', 1 => '1952'])
            ->willReturn(['component_type_id' => '1651']);

        $action->expects($this->once())->method('setupGlobalConfigurator')->with('1651', '********', 'auto_destroy');

        $action->processLegacyComponents();
    }

    /**
     * @covers AccountChange_Action_LegacyComponents::processLegacyComponents
     *
     * @return void
     */
    public function testProcessLegacyComponentsDoesNotCallSetupGlobalConfigurationIfSameComponenetAvailableOnLegacyComponentsToAddAndLegacyComponentTypesToIgnore()
    {
        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_component_add'), array(), '', false);

        $objUserdataSplitter->expects($this->never())
            ->method('userdata_component_add')
            ->will($this->returnValue(1));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $action = $this->getMock('AccountChange_Action_LegacyComponents',
            array('setupGlobalConfigurator', 'includeLegacyFiles', 'populateLegacyComponentArrays'),
            array($this->serviceId));

        $action->expects($this->never())
            ->method('setupGlobalConfigurator');

        $action->expects($this->any())
            ->method('includeLegacyFiles');

        $action->expects($this->once())
            ->method('populateLegacyComponentArrays');

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('arrLegacyComponentsToAdd', array(5));
        $registry->setEntry('arrLegacyComponentsToRemove', array());
        $registry->setEntry('arrLegacyComponentTypesToIgnore', array(5));
        $registry->setEntry('arrLegacyComponentNotToKeep', array());

        $action->execute();
    }

}
