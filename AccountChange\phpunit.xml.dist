<?xml version="1.0" encoding="UTF-8"?>
<phpunit
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://schema.phpunit.de/4.5/phpunit.xsd"
         bootstrap="./Test/Bootstrap.php"
         colors="true">

    <testsuite name="AccountChange">
        <directory suffix=".test.php">./Test/AccountChange</directory>
        <directory suffix=".test.php">./Test/Application</directory>
        <directory suffix=".test.php">./Test/Libraries</directory>
        <directory suffix=".test.php">./Test/Scripts</directory>
        <directory suffix=".test.php">./Test/Service</directory>
    </testsuite>

    <filter>
        <whitelist addUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./</directory>
            <exclude>
                <directory suffix=".php">./Test/</directory>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
