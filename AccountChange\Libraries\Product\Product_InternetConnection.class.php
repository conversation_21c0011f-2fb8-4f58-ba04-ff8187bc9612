<?php
/**
 * Product Configuration for InternetConnection component
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_InternetConnection.class.php,v 1.3.2.3 2009/07/13 14:02:32 mstarbuck Exp $
 * @link      http://documentation.plus.net/index.php/Account_Change
 * @since     File available since 2008-08-28
 */

/**
 * Product InternetConnection class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Product_InternetConnection extends AccountChange_Product_ServiceComponent
{
    /**
     * Is the product a key product
     *
     * @var boolean
     */
    protected $bolKeyProduct = false;

    /**
     * New Service Definition Id
     *
     * @var int
     */
    protected $intNewServiceDefinitionId;

    /**
     * @var ProductFamily_Value
     */
    private $objFamilyProduct = null;

    /**
     * @var int
     */
    private $intLineCheckId = null;


    /**
     * Store the desired contract lenght (as we can have 12 or 24 months on BPR09 products
     *
     * @var string
    */
    private $strContract = '';


    /*
     * If we've checked the 'recontract' option in workplace, then the change() function needs to
     * know about it so we can set $bolKeepCurrentContractLength to false when calling the legacy change
     * function.   Initialised through arrOptions['bolContractReset']
     *
     * @var boolean
    */
    private $bolContractReset = false;


    /**
     * Initilisation of the configuration
     *
     * @param int   $intServiceComponentId Service component id
     * @param int   $intAction             Action
     * @param array $arrOptions            Options
     *
     * @return void
     */
    public function initialise($intServiceComponentId, $intAction, array $arrOptions)
    {
        parent::initialise($intServiceComponentId, $intAction, $arrOptions);

        if (isset($arrOptions['intNewServiceDefinitionId'])) {

            $this->intNewServiceDefinitionId = $arrOptions['intNewServiceDefinitionId'];

        } else {

            throw new AccountChange_Product_ManagerException(
                'The new service definition id is needed for this product configuration to be actioned',
                AccountChange_Product_ManagerException::ERR_NEW_SERVICE_DEFINITION_ID_NOT_PRESENT
            );
        }

        if (!empty($arrOptions['intLineCheckId'])) {

            $this->intLineCheckId = $arrOptions['intLineCheckId'];
        }

        if (isset($arrOptions['strContract'])) {

            $this->strContract = $arrOptions['strContract'];

        }

        if (isset($arrOptions['intAccountChangeOperation'])) {

            $this->intAccountChangeOperation = $arrOptions['intAccountChangeOperation'];
        }

        if (isset($arrOptions['bolContractReset'])) {

            $this->bolContractReset = $arrOptions['bolContractReset'];
        }
    }

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration Account configuration object
     *
     * @return void
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {

            $this->objNewProductConfiguration = $objAccountConfiguration->getProductConfigurationByType(
                AccountChange_Product_Manager::PRODUCT_TYPE_INTERNET_CONNECTION
            );
        }
    }

    /**
     * Attaches {@link ProductFamily_Value} object to {@link AccountChange_Product_InternetConnection}
     *
     * @param ProductFamily_(Value|Bpr09) $objFamilyProduct Family product object
     *
     * <AUTHOR> Marek <<EMAIL>>
     * @uses AccountChange_Product_InternetConnection::$objFamilyProduct
     * @see AccountChange_Product_InternetConnection::create()
     * @see AccountChange_Product_Manager::factoryUsingFamilyObject()
     * @return void
     */
    public function attachFamilyProductObject($objFamilyProduct)
    {
        $this->objFamilyProduct = $objFamilyProduct;
    }


    /**
     * Creates new InternetConnection component and activates it
     *
     * Two additional values are passed into {@link ProductFamily_Value::ProductFamily_Value()}:
     * - intLineCheckId - required to determine correct service component id for a customer based on linecheck results
     * - bolDialup - which against misleading name activates component straight away
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses ProductFamily_Value::ProductFamily_Value()
     * @return void
     */
    protected function create()
    {
        $this->includeLegacyFiles();

        // Pass LineCheck Id so correct component can be selected
        $arrOptions['intLineCheckId'] = $this->intLineCheckId;
        // activate internet connection component
        $arrOptions['bolDialup'] = true;

        // Pass in the desired contract length
        $arrOptions['strContractLengthHandle'] = $this->strContract;
        $arrOptions['bolWlrSkipRefundProrata'] = true;

        // We want scheduled payments to be created from the next invoice date.
        $service = $this->getCoreService();
        $arrOptions['objPaymentStart'] = $service->getNextInvoiceDate();

        // create InternetConnection and discount components
        $this->objFamilyProduct->performComponentConfiguration($this->intServiceId, $arrOptions);
    }

    /**
     * Legacy wrapper for the account change handler
     *
     * @return boolean
     */
    protected function change()
    {
        $this->includeLegacyFiles();
        $this->output(__METHOD__ . ' called');

        $intMarketId = $this->getMarketId();

        $intAccountChangeOperation = $this->getAccountChangeOperation();

        $bolKeepCurrentContractLength = true;

        $intSelectedTariffID = $this->getTariffID();

        if ($this->bolContractReset
            || $intAccountChangeOperation == AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE
        ) {
            $bolKeepCurrentContractLength = false;
        }

        $service = $this->getCoreService();
        $objPaymentStart = $service->getNextInvoiceDate();

        //Commit the default transaction (core service) started with the service id.
        //From here the execution control shifted to Legacy code base, where there are no need of existing core service.
        //New private transactions will be started (if needed) in Legacy ccode base.
        //For eg: tickets_open_ticket_summary_add.method.class.php.
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        $this->output('Default transaction committed');

        $this->accountTypeChangeHandler(
            'MONTHLY',
            $bolKeepCurrentContractLength,
            $intMarketId,
            $intSelectedTariffID,
            $objPaymentStart
        );
        $this->output('Call to accountTypeChangeHandler complete');

        return true;
    }

    /**
     * Need to include the legacy files, but we need to be able to mock it for the unit tests
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProduct.inc';
    }

    /**
     * Get the marketId for latest Line check
     *
     * @return Integer
     */
    protected function getMarketId()
    {
        $objLineResult = new LineCheck_Result($this->intLineCheckId);
        $objMarket = LineCheck_Market::getMarketFromExchange($objLineResult->getExchangeCode());

        return $objMarket->getMarketId();
    }

    /**
     * Call to a legacy function CInternetConnectionProduct::accountTypeChangeHandler
     *
     * @param String    $strPaymentFrequencyHandle    Payment frequency handle
     * @param bool      $bolKeepCurrentContractLength Keep current contract length flag
     * @param int       $intMarketId                  Market id
     * @param int       $intSelectedTariffID          Selected tariff id
     * @param I18n_Date $objPaymentStart              Payment start date
     *
     * @return bool
     */
    protected function accountTypeChangeHandler(
        $strPaymentFrequencyHandle,
        $bolKeepCurrentContractLength,
        $intMarketId,
        $intSelectedTariffID,
        $objPaymentStart
    ) {
        $this->output('Calling legacy function CInternetConnectionProduct::accountTypeChangeHandler()');

        CInternetConnectionProduct::accountTypeChangeHandler(
            $this->intServiceId,
            $this->intNewServiceDefinitionId,
            $strPaymentFrequencyHandle,
            $bolKeepCurrentContractLength,
            $this->strContract,
            $intMarketId,
            $intSelectedTariffID,
            $objPaymentStart
        );
        $this->output('Call complete');

        return true;
    }

    /**
     * Returns the tariff Id selected during scheduled account change
     *
     * @return int $intSelectedTariffID
     */
    protected function getTariffID()
    {
        $intSelectedTariffID = null;

        $registry = AccountChange_Registry::instance();
        $runFromScript = $registry->getEntry('runFromPerformScheduledAccountChange');

        // LTC-4541 - should NOT take $intSelectedTariffID from DB for instant account change
        if ($runFromScript) {
            $arrServiceScheduleDetails = userdata_service_schedule_get($this->intServiceId);
            $intSelectedTariffID
                = isset($arrServiceScheduleDetails['intTariffID']) ? $arrServiceScheduleDetails['intTariffID'] : null;
        }

        return $intSelectedTariffID;
    }

    /**
     * Function to output a formatted message to standard output - needs to be mockable for unit tests
     *
     * @param string  $message    the message to output
     * @param boolean $printDebug used to decide if the output should be written to either STDOUT or Dbg_Dbg
     *
     * @return void
     */
    protected function output($message, $printDebug=false)
    {
        if ($printDebug) {
            $line = date(DATE_COOKIE) . ": $message\n";
            print $line;
        } else {
            Dbg_Dbg::write($message, 'AccountChange');
        }
    }
}
