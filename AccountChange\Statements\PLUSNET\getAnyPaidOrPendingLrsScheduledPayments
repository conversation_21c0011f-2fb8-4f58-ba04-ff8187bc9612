server: coredb
role: master
rows: single
statement:
SELECT sp.intScheduledPaymentID 
FROM financial.tblScheduledPayment sp
INNER JOIN financial.tblConfigProductComponent cpc
  ON sp.intScheduledPaymentID = cpc.intScheduledPaymentID
INNER JOIN userdata.tblProductComponentInstance pci
  ON  pci.intProductComponentInstanceID = cpc.intProductComponentInstanceID
INNER JOIN dbProductComponents.tblStatus s 
  ON s.intStatusID = pci.intStatusID
INNER JOIN dbProductComponents.tblProductComponent pc
  ON pc.intProductComponentID = pci.intProductComponentID
INNER JOIN userdata.components c
  ON c.component_id = pci.intComponentID
INNER JOIN 
       userdata.services as s on s.service_id = c.service_id 
INNER JOIN products.tblServiceComponentProduct scp
  ON scp.intServiceComponentID = c.component_type_id
  AND scp.intServiceComponentProductTypeID = 4
WHERE
  sp.dtmCancelled IS NULL
  AND sp.dtmFailed IS NULL
  AND pc.vchHandle = 'LINE_RENTAL_SAVER'
  AND scp.intServiceComponentID IN (509,510,515,669,670,919,920,921)
  AND c.service_id = :serviceId
ORDER BY sp.intScheduledPaymentID DESC
LIMIT 1
