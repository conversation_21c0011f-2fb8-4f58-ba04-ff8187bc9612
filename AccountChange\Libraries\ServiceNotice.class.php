<?php
/**
 * Account Change Service Notice
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 Plusnet
 * @since     File available since 2010-07-12
 */
/**
 * Account Change Service Notice class
 *
 * Holds information about an account change service notice so the
 * AccountChange_Manager can raise them all at the end of the process
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 Plusnet
 */
class AccountChange_ServiceNotice
{
    /**
     * The service notice type id
     *
     * @var string
     */
    protected $_serviceNoticeTypeId = 1;

    /**
     * The actual comment we want to raise the service notice with
     *
     * @var string
     */
    protected $_comment = '';

    /**
     * Constructor
     *
     * @param string $strComment          Service Notice Comment
     * @param int    $serviceNoticeTypeId Type of service notice
     *
     * @return AccountChange_ServiceNotice
     */
    public function __construct($comment = '', $serviceNoticeTypeId = 1)
    {
        $this->_comment = $comment;
        $this->_serviceNoticeTypeId = $serviceNoticeTypeId;
    }

    /**
     * Getter for the comment
     *
     * @return string
     */
    public function getComment()
    {
        return $this->_comment;
    }

    /**
     * Setter for the comment
     *
     * @param $strComment
     *
     * @return void
     */
    public function setComment($strComment)
    {
        $this->_comment = $strComment;
    }

    /**
     * Getter for the service notice type id
     *
     * @return int
     */
    public function getServiceNoticeTypeId()
    {
        return $this->_serviceNoticeTypeId;
    }

    /**
     * Setter for the service notice type id
     *
     * @param int $serviceNoticeTypeId Type of service notice
     *
     * @return void
     */
    public function setServiceNoticeTypeId($serviceNoticeTypeId)
    {
        $this->_serviceNoticeTypeId = $serviceNoticeTypeId;
    }
}