<?php

class AccountChange_EmailHandler_ContractMessageHelper
{
    /**
     * Contract related messages to put in the email
     *
     * @string
     */
    const NEW_CONTRACT_MESSAGE    = 'Choosing this package means you will be subject to a new %s month contract';
    const ACTIVE_CONTRACT_MESSAGE = 'You\'re in month %s of your %s month contract';
    const PHONE_ONLY_MESSAGE      = 'Your phone package is subject to a minimum notice period of 14 days';

    const ACTIVE_CONTRACT_STATUS = 'ACTIVE';

    /**
     * @var string
     */
    private $serviceId;

    /**
     * @var AccountChange_AccountChangeOrderContract
     */
    private $contract;

    /**
     * AccountChange_EmailHandler_ContractMessageHelper constructor.
     *
     * @param AccountChange_AccountChangeOrderContract|null $contract
     * @param string                                        $serviceId
     */
    public function __construct($serviceId, AccountChange_AccountChangeOrderContract $contract = null)
    {
        $this->serviceId = $serviceId;
        $this->contract  = $contract;
    }

    /**
     * Generate the contract message to display in the email, calculated based on their new contract or current contract
     *
     * @return string The contract message to display in the account change email
     */
    public function generateContractMessage()
    {
        $message = null;

        if (!empty($this->contract)) {

            $message = $this->generateNewContractMessage();
        } else {

            $activeContract = $this->getActiveContract();
            if (!empty($activeContract)) {

                $message = static::generateCurrentActiveContractMessage($activeContract);
            } else {

                $message = static::generatePhoneOnlyMessage();
            }
        }

        return $message;
    }

    private function generateNewContractMessage()
    {
        return sprintf(self::NEW_CONTRACT_MESSAGE, $this->contract->getLength());
    }

    private function generateCurrentActiveContractMessage($activeContract)
    {
        $monthsIntoContract = $activeContract['currentMonth'];
        $contractLength     = $activeContract['duration'];

        return sprintf(self::ACTIVE_CONTRACT_MESSAGE, $monthsIntoContract, $contractLength);
    }

    private function generatePhoneOnlyMessage()
    {
        return self::PHONE_ONLY_MESSAGE;
    }

    /**
     * Retrieve the customer's current active contract using contracts client
     *
     * @return array|null The customer's current active contract (they may not have one)
     */
    protected function getActiveContract()
    {
        $activeContractDetails = array();
        $contractsClient = \BusTier_BusTier::getClient('contracts')->setServiceId($this->serviceId);

        $criteria['status'] = self::ACTIVE_CONTRACT_STATUS;
        $activeContract = $contractsClient->getContract($criteria);

        if (!empty($activeContract) && !empty($activeContract->getId())) {

            $duration      = $activeContract->getDurationValue();
            $remainingTime = $activeContract->getRemainingTime();

            $activeContractDetails['duration']     = $duration;
            $activeContractDetails['currentMonth'] = $duration - $remainingTime['value'];
        }

        return $activeContractDetails;
    }
}