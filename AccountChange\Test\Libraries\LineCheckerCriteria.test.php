<?php
/**
 * AccountChange Line Checker Array Test
 *
 * Testing class for AccountChange_LinecheckerCriteria
 *
 * @category   AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 */
class AccountChange_LineCheckerCriteria_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test whether the post code returned by the array is a string
     *
     * @param object $postCode postcode object that supports toString
     * @param string $thoroughFareNumber an address line
     *
     * @dataProvider  dataProviderForBuildLineCheckCriteria
     *
     * @covers AccountChange_LineCheckerCriteria::buildLineCheckCriteria
     */
    public function testBuildLineCheckCriteria($postCode, $thoroughFareNumber)
    {
        $result = AccountChange_LineCheckerCriteria::buildLineCheckCriteria($postCode, $thoroughFareNumber);
        $this->assertInternalType('array', $result);
        $this->assertArrayHasKey('thoroughfareNumber', $result);
        $this->assertArrayHasKey('postcode', $result);
        $this->assertEquals($thoroughFareNumber, $result['thoroughfareNumber']);
        $this->assertEquals(strval($postCode), $result['postcode']);
    }
    
    public function dataProviderForBuildLineCheckCriteria()
    {
        return array(
            'postcode as string literal' => array('BD1 5AE', 'East BrookHall'),
            'postcode as string object' => array(new String('S21 1FR'), 'Testing')
        );
    }
}
