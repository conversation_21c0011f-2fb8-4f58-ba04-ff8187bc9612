<?php
/**
 * AccountChange_Address_Test
 *
 * Tests for AccountChange ConfirmationDetails requirement
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @link       http://documentation.plus.net/index.php/AccountChange_Module
 */
/**
 * AccountChange_Address_Test
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @link       http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_Address_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that describe correctly decorates an address
     *
     * @param array $inputAddresses Addresses
     * @param array $expected       Expected result
     *
     * @covers       AccountChange_Address::describe
     * @dataProvider addressProvider
     *
     * @return void
     */
    public function testDescribeDecoratesAddresses($inputAddresses, $expected)
    {
        $addresses = new Wlr3_Addresses();
        foreach ($inputAddresses as $inputAddress) {
            $address = new Wlr3_StrategicImperatives_ValueObject_Address();
            $address->addressReference = $inputAddress['addressReference'];
            $address->cssDatabaseCode  = $inputAddress['cssDatabaseCode'];
            $address->addressCategory  = $inputAddress['addressCategory'];

            $addresses->addAddress($address);
        }

        $page = $this->getMock(
            'AccountChange_Address',
            array('getSelectedBroadband')
        );

        $page
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(null));

        $arrValidatedApplicationData = array('addresses' => $addresses);
        $output = $page->describe($arrValidatedApplicationData);
        $this->assertEquals($expected, array_keys($output['addresses']));
    }

    /**
     * Provider for testDescribeDecoratesAddresses
     *
     * @return void
     */
    public function addressProvider()
    {
        return array(
            array(
                array(
                    array(
                        'addressCategory' => 'GOLD',
                        'addressReference' => 'A90000006368',
                        'country' => 'United Kingdom',
                        'county' => 'S Yorks',
                        'cssDatabaseCode' => 'ND',
                        'dependentThoroughfareName' => null,
                        'doubleDependentLocality' => null,
                        'exchangeGroupCode' => null,
                        'locality' => null,
                        'organisationName' => null,
                        'poBox' => null,
                        'postTown' => 'Sheffield',
                        'postcode' => 'S1 2GU',
                        'premisesName' => 'StoppedLine',
                        'subPremises' => null,
                        'thoroughfareName' => 'Pinfold Street',
                        'thoroughfareNumber' => '2',
                    ),
                    array(
                        'addressCategory' => 'GOLD',
                        'addressReference' => 'A90000006369',
                        'country' => 'United Kingdom',
                        'county' => 'S Yorks',
                        'cssDatabaseCode' => 'ND',
                        'dependentThoroughfareName' => null,
                        'doubleDependentLocality' => null,
                        'exchangeGroupCode' => null,
                        'locality' => null,
                        'organisationName' => null,
                        'poBox' => null,
                        'postTown' => 'Sheffield',
                        'postcode' => 'S1 2GU',
                        'premisesName' => 'MPF',
                        'subPremises' => null,
                        'thoroughfareName' => 'Pinfold Street',
                        'thoroughfareNumber' => '2',
                    ),
                    array(
                        'addressCategory' => 'GOLD',
                        'addressReference' => 'A90000006370',
                        'country' => 'United Kingdom',
                        'county' => 'S Yorks',
                        'cssDatabaseCode' => 'ND',
                        'dependentThoroughfareName' => null,
                        'doubleDependentLocality' => null,
                        'exchangeGroupCode' => null,
                        'locality' => null,
                        'organisationName' => null,
                        'poBox' => null,
                        'postTown' => 'Sheffield',
                        'postcode' => 'S1 2GU',
                        'premisesName' => 'NoLines',
                        'subPremises' => null,
                        'thoroughfareName' => 'Pinfold Street',
                        'thoroughfareNumber' => '2',
                    ),
                ),

                array(
                    'A90000006368:ND:GOLD',
                    'A90000006369:ND:GOLD',
                    'A90000006370:ND:GOLD',
                ),
            ),
        );
    }

    /**
     * Check that the validator for the address ref works correctly.
     *
     * @param unknown_type $addressRef    Address reference
     * @param unknown_type $expected      Expected result
     * @param unknown_type $expectedError Expected error
     *
     * @covers AccountChange_Address::valAddressRef
     * @dataProvider valAddressProvider
     *
     * @return void
     */
    public function testAddressRefValidation($addressRef, $expected, $expectedError)
    {
        $addresses = new AccountChange_Address();
        $result = $addresses->valAddressRef($addressRef);
        $this->assertEquals($expected, $result);
        $this->assertEquals($expectedError, $addresses->getValidationErrors());
    }

    /**
     * Provider for testAddressRefValidation
     *
     * @return array
     */
    public function valAddressProvider()
    {
        return array(
            // data set 0, successful validation
            array(
                'A90000006368:SL:GOLD',
                array('addressRef' => 'A90000006368:SL:GOLD'),
                array(),
            ),
            // data set 1, no address catagory
            array(
                'A90000006368:SL:',
                array('addressRef' => ''),
                array('addressRef' => array('INVALID' => true)),
            ),
            // data set 2, no css database code
            array(
                'A90000006368::SILVER',
                array('addressRef' => ''),
                array('addressRef' => array('INVALID' => true)),
            ),
           // data set 2, complete nonsense
            array(
                new StdClass(),
                array('addressRef' => ''),
                array('addressRef' => array('INVALID' => true)),
            ),
        );
    }

    /**
     * Check that getSelectedBroadband returns the application state
     * variable that we're expecting
     *
     * @covers AccountChange_Address::getSelectedBroadband
     *
     * @return void
     **/
    public function testGetSelectedBroadbandReturnsExpected()
    {
        $selectedBroadband = array(
            'serviceDefinitionId' => 6768
        );

        $address = new AccountChange_Address();

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController
            = $this->getMock('AccountChange_Controller', array('getApplicationStateVar'));

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with('AccountChange_Address', 'arrSelectedBroadband')
            ->will($this->returnValue($selectedBroadband));

        // Set callback object within the requirement itself to application controller
        $address->setAppStateCallback($mockController);
        $actual = $address->getSelectedBroadband();
        $this->assertEquals($actual, $selectedBroadband);
    }
}
