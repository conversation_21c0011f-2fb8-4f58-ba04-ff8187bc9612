<?php
/**
 * Consent Storing Action
 *
 * Stores the customers record of consent to switch their services to us
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 */

/**
 * Consent Storing Action class
 *
 * Stores the customers record of consent to switch their services to us
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2014 PlusNet
 */
class AccountChange_Action_Consent extends AccountChange_Action
{
    /**
     * Constants for application Ids
     *
     * @var int
     */
    const WORKPLACE_ACCOUNT_CHANGE = 1;
    const PORTAL_ACCOUNT_CHANGE = 2;
    const MY_OFFERS = 3;

    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $registry = AccountChange_Registry::instance();

        $bolPortal = $registry->getEntry('bolPortal');

        $appId = $registry->getEntry('intAppId');

        if ($appId === null) {
            if ($bolPortal === true) {
                $appId = self::PORTAL_ACCOUNT_CHANGE;
            } else {
                $appId = self::WORKPLACE_ACCOUNT_CHANGE;
            }
        }

        $serviceId = $registry->getEntry('intServiceId');
        $bolWlrTakeover = $registry->getEntry('bolWlrTakeover');
        $bolAdslTakeover = $registry->getEntry('bolAdslTakeover');
        $intAgentActorId = $registry->getEntry('intAgentActorId');
        $vchConsentVersionHandle = $registry->getEntry('vchConsentVersionHandle');
        $newWlrServiceComponentId = $registry->getEntry('newWlrServiceComponentId');
        $newAdslServiceComponentId = $registry->getEntry('newAdslServiceComponentId');

        $bolAdslConsentRecorded = $registry->getEntry('adslConsentRecorded');
        $bolWlrConsentRecorded = $registry->getEntry('wlrConsentRecorded');

        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');

        $intConsentVersionId =  $dbAdaptor->getConsumerConsentVersionId($vchConsentVersionHandle);
        if ($intConsentVersionId == null) {
            if ($appId == self::WORKPLACE_ACCOUNT_CHANGE) {
                error_log("Failed to capture consent to switch text version from workplace account change");
            } else if ($appId == self::PORTAL_ACCOUNT_CHANGE) {
                error_log("Failed to capture consent to switch text version from portal account change");
            } else if ($appId == self::MY_OFFERS) {
                 error_log("Failed to capture consent to switch text version from my offers");
            }
        }

        if ($serviceId !== null) {
            if ($bolAdslTakeover === true && $newAdslServiceComponentId !== null && $bolAdslConsentRecorded != true) {
                $dbAdaptor->insertConsumerConsent($serviceId, $intAgentActorId, $newAdslServiceComponentId, $appId, null, $intConsentVersionId);
                $registry->setEntry('adslConsentRecorded', true);
            }

            if ($bolWlrTakeover === true && $newWlrServiceComponentId !== null && $bolWlrConsentRecorded != true) {
                $dbAdaptor->insertConsumerConsent($serviceId, $intAgentActorId, $newWlrServiceComponentId, $appId, null, $intConsentVersionId);
                $registry->setEntry('wlrConsentRecorded', true);
            }
        }
    }
}
