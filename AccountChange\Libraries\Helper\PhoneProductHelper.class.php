<?php

class AccountChange_PhoneProductHelper
{
    /**
     * Return the "* With Mobile" equivalent of a WLR product
     *
     * @param int $wlrProductId Wlr product Id
     *
     * @return int Id of the mapped product, null otherwise
     */
    public function getMappedMobileProduct($wlrProductId)
    {
        $mobileBoltOnMapping = $this->getServiceComponentIdToToBoltOnMap();
        return isset($mobileBoltOnMapping[$wlrProductId]) ?  $mobileBoltOnMapping[$wlrProductId] : null;
    }

    /**
     * Return the "* WithOUT Mobile" equivalent of a WLR product
     *
     * @param int $wlrProductId Wlr product Id
     *
     * @return int Id of the mapped product, null otherwise
     */ 
    public function getMappedNonMobileProduct($wlrProductId)
    {
        $mobileBoltOnMapping = array_flip($this->getServiceComponentIdToToBoltOnMap());
        return isset($mobileBoltOnMapping[$wlrProductId]) ?  $mobileBoltOnMapping[$wlrProductId] : null; 
    }

    /**
     * Retrieves the mapping between service components for products and
     * the service components representing their bolt on components.
     *
     * With the Key being the service components id on the value the service components boltonId.
     *
     * @return array The mapping extracted from the database
     */
    public function getServiceComponentIdToToBoltOnMap()
    {
        $serviceComponentIdToBolton = array();
        $mappings = $this->getServiceComponentBoltOnMappings();
        foreach ($mappings as $mapping) {
            $serviceComponentIdToBolton[$mapping['intServiceComponentID']]= $mapping['intBoltOnServiceComponentID'];
        }
        return $serviceComponentIdToBolton;
    }

    /**
     * Retrieves the mapping between service components for products and
     * the service components representing their bolt on components.
     *
     * @return array The mapping extracted from the database
     */
    protected function getServiceComponentBoltOnMappings()
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        return $db->getServiceComponentMobileBoltOnMapping();
    }
}
