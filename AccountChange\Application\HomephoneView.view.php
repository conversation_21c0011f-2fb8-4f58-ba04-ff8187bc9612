<?php
/**
 * AccountChange Homephone View
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <dcal<PERSON><EMAIL>>
 *
 * @copyright 2012 Plusnet
 * @since     File available since 2012-01-26
 */
/**
 * AccountChange_HomephoneView class
 *
 * Currently this view does the following...
 *
 * Sorts on Call Plan
 * - JLP, Greenbee and Waitrose Call Plans should be sorted low to high
 * - Anything else should be left alone
 *
 * @package   AccountChange
 * <AUTHOR> <dcal<PERSON><PERSON>@plus.net>
 *
 * @copyright 2012 PlusNet
 */

class AccountChange_HomephoneView extends Mvc_View
{
    /**
     * Sorts the products for the view and returns them
     *
     * @param array $arrInput Input for the view
     *
     * @return array
     */
    protected function processInput(array $arrInput)
    {
        if (isset($arrInput['productIsp']) && isset($arrInput['arrNewProducts'])) {

            if (in_array($arrInput['productIsp'], array('greenbee', 'waitrose', 'johnlewis'))) {

                $arrInput['arrNewProducts'] = $this->sortOnCallPlan($arrInput['arrNewProducts']);
            }
        }
        return $arrInput;
    }

    /**
     * Sorts the Home Phone Call Plans according to the sort array defined in the function
     *
     * @param array $products Input Array
     *
     * @return array
     */
    private function sortOnCallPlan(array $products)
    {
        // Sort the phone options according to the order in this array:
        $phoneOptionsSort = array(
            '995',
            '997',
            '1980',
            '996',
            '1979'
        );

        foreach ($products as &$phoneOption) {
            $phoneOption['intSort'] = array_search($phoneOption['intNewWlrId'], $phoneOptionsSort, true);
        }

        usort($products, function ($a, $b) {
            return ($a['intSort'] > $b['intSort']);
        });

        return $products;
    }
}
