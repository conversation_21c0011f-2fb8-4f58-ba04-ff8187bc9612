server: coredb
role: slave
rows: single
statement:

SELECT EXISTS(
  SELECT cs.service_id
  FROM userdata.service_change_schedule cs
  JOIN products.adsl_product apOld
    ON cs.old_type = apOld.service_definition_id
  JOIN products.adsl_product apNew
    ON cs.new_type = apNew.service_definition_id
  JOIN products.tblProvisioningProfile ppOld
    ON ppOld.intProvisioningProfileID = apOld.intProvisioningProfileID
  JOIN products.tblProvisioningProfile ppNew
    ON ppNew.intProvisioningProfileID = apNew.intProvisioningProfileID
  WHERE
    ((ppNew.vchName = 'FTTC' AND ppOld.vchName != 'FTTC') OR (ppNew.vchName = 'FTTP' AND ppOld.vchName != 'FTTP')) AND
    cs.change_complete = 'no' AND
    cs.active = 'yes' AND
    cs.change_date IS NOT NULL AND
    cs.change_date <= date(now()) AND
    cs.service_id = :serviceId
);