<?php
require_once(__DIR__ . '/../Libraries/C2mPromotionsHelper.php');
require_once(__DIR__ . '/../Libraries/SalesJourneyViewHelper.class.php');

/**
 * Home Phone Selection Requirement
 *
 * Collecting the data for the home phone requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: SelectHomephone.req.php,v 1.4.2.2 2009/07/09 09:06:52 mstarbuck Exp $
 * @since     File available since 2008-09-29
 */
/**
 * AccountChange_SelectHomephone class
 *
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_SelectHomephone extends AccountChange_SelectHomephoneRequirement
{
    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'intNewWlrId' => 'external:Custom',
        'mobileBoltOn' => 'external:custom:optional',
        'callerDisplay' => 'external:custom:optional'
    );

    /**
     * Redirect to the summary page if $_SESSION['nextPage'] is set to Summary.
     *
     * The session will only be set if the previous page was navigated to direct
     * from the C2FSummary page.  This would mean the user has interrupted the
     * normal flow of the wizard to alter their. basket so we now want to return
     * to the summary page.
     */
    private function redirectToSummaryPage()
    {
        $_SESSION['nextPage']  = '';
        header("Location: https://" . AccountChange_SalesJourneyViewHelper::getWizardUri('AccountChange') . "/" . AccountChange_SalesJourneyViewHelper::getWizardInstanceID('AccountChange') . "/C2FSummary");
        die;
    }

    protected function getAccountChangeValidator() {
        return new AccountChange_ValidationCheck($this->getCurrentBusinessActor(),
            AccountChange_ValidationCheck::getDefaultPolicies());
    }

    /**
     * Describe
     *
     * Pull all the information needed for the view of this requirement
     *
     * @param array &$arrValidatedApplicationData application data
     *
     * @return array
     * @throws Exception
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $previousPageAccessedFromSummaryPage = (isset($_SESSION['nextPage']) && $_SESSION['nextPage'] === 'C2FSummary');

        if($previousPageAccessedFromSummaryPage) {
            $this->redirectToSummaryPage();
        }
        $arrReturn = array();

        $intServiceId = $arrValidatedApplicationData['objCoreService']->getServiceId();

        $objDatabase = Db_Manager::getAdaptor('Core');
        $arrServiceDefinitionDetails = $objDatabase->getServiceDefinitionDetailsForService($intServiceId);

        if (Core_ServiceDefinition::BUSINESS_ACCOUNT == $arrServiceDefinitionDetails['type']) {
            $strContractHandle = 'ANNUAL';
        } else {

            $strContractHandle = 'MONTHLY';
        }

        if (!empty($arrValidatedApplicationData['arrWlrProduct']['strContractHandle'])) {

            $strContractHandle = $arrValidatedApplicationData['arrWlrProduct']['strContractHandle'];
        }

        // We do not want to show the "No Home Phone" option on the portal if the
        // customer already has home phone. This would mean they are wanting to cancel home phone
        // If that is the case then they need to cease or transfer out
        $bolIncludeNoWlr = false;
        $intOldComponentId = isset($arrValidatedApplicationData['arrWlrProduct']['intInstanceId']) ?
                                   $arrValidatedApplicationData['arrWlrProduct']['intInstanceId'] : 0;


        // If a dual play product is chosen, force the WLR selection
        $dualPlay = false;

        if ($this->isApplicationStateVariable('selectedProductFamily') || $this->isBroadbandOnlyJourney() == false) {
            $productFamily = $this->getApplicationStateVariable('selectedProductFamily');
            $dualPlay = $productFamily->isDualPlay();
        }

        if (empty($intOldComponentId) && !$dualPlay) {

            $bolIncludeNoWlr = true;
        }

        $newSdi = $this->getApplicationStateVariable('intNewSdi');
        $keepingExistingContract = $this->keepingExistingContract($newSdi);

        $arrReturn['arrNewProducts'] = $this->getWlrProducts(
            $newSdi,
            $strContractHandle,
            $bolIncludeNoWlr,
            $keepingExistingContract
        );

        $oldWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');
        $oldWlrId = $oldWlrProduct['intOldWlrId'];
        $isBusiness = isset($arrValidatedApplicationData['bolIsBusiness'])
            ? $arrValidatedApplicationData['bolIsBusiness'] : false;

        $arrReturn['callChargesIncreasing'] = false;
        $arrReturn['arrMobileBoltonMapping'] = array();

        $objCoreService = $this->getApplicationStateVariable('objCoreService');

        if (!$isBusiness && \Plusnet\Feature\Toggle::isOn('wlr-pricing-2013')) {
            $wlrProductFilter = $this->getWlrProductFilter();

            $arrReturn['arrNewProducts'] = $wlrProductFilter->filterProducts(
                $arrReturn['arrNewProducts'],
                $oldWlrId,
                $this->hasActiveLineRentalSaver(),
                $this->isFibreProduct($newSdi)
            );

            $arrReturn['callChargesIncreasing'] = $wlrProductFilter->isOldToNewList(
                $arrReturn['arrNewProducts'],
                $oldWlrId
            );

            $arrReturn['arrNewProducts'] = $this->getWlrProductSorter()->sort(
                $arrReturn['arrNewProducts'],
                $oldWlrId
            );

            $arrReturn['arrMobileBoltonMapping'] = $wlrProductFilter->getMobileBoltOnMapping();
        } else {
            $productFilter = AccountChange_Product_WlrProductFilterFactory::getFilter(
                $objCoreService->isPlusnetUser(),
                $isBusiness
            );

            $arrReturn['arrNewProducts'] = $productFilter->filter($arrReturn['arrNewProducts']);
        }

        $firstElement = reset($arrReturn['arrNewProducts']);
        if (isset($firstElement['intLineRentCost'])) {
            $arrReturn['intLineRentCost'] = $firstElement['intLineRentCost'];
        } else {
            $arrReturn['intLineRentCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
        }

        // Checking if there is an ACTIVE LRS contract in the account and
        // setting the line rental price to zero if there is any
        $arrReturn['intLineRentCost'] = ($this->hasActiveLineRentalSaver()) ?
            new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0) :
            $arrReturn['intLineRentCost'];

        $bolWlrChangesAllowed = $this->wlrChangesAllowed($intOldComponentId);
        $arrReturn['bolCanModifyWlr'] = $bolWlrChangesAllowed;

        $validator = $this->getAccountChangeValidator();
        $arrReturn['bolWlrChangeAllowed'] = $validator->isAccountChangeAllowed();

        // return call features for the old phone component!
        $arrReturn['arrOldCallFeatures'] = $this->getWlrCallFeatures($intOldComponentId);

        $arrReturn['selectedBroadband'] = $this->getSelectedBroadband();

        $currentProductSpeedRanges = $this->getMinAndMaxSpeedRanges();
        $arrReturn['currentProductDownloadSpeedRangeMin'] = $currentProductSpeedRanges['downloadSpeedRangeMin'];
        $arrReturn['currentProductDownloadSpeedRangeMax'] = $currentProductSpeedRanges['downloadSpeedRangeMax'];

        $arrReturn['discountContractLength'] = $this->getDiscountContractLength($arrValidatedApplicationData);

        $objBusinessActor = $this->getCurrentBusinessActor();

        if ($this->isC2fToggleSet()
            && $objCoreService->isPlusnetUser()
            && $objCoreService->getAdslDetails()['type'] == 'residential'
            && $objBusinessActor->getUserType() != 'PLUSNET_STAFF') {

            $serviceId = $objCoreService->getServiceId();
            $account = $this->getAccountInstance($serviceId);
            $arrReturn['contractDetails'] = $account->getContractDetails($serviceId);

            $arrReturn['comparators'] = $this->createProductComparators($arrReturn['arrMobileBoltonMapping'], $arrValidatedApplicationData['arrWlrProduct']['intOldWlrId']);
            $arrReturn['arrNewProducts'] = $this->addBoltonData($arrReturn);
            $arrReturn['currentPhoneProduct'] = $this->getCurrentPhoneProduct($arrReturn['arrNewProducts'], $arrReturn['comparators']) ?: [];
            $arrReturn['arrNewProducts'] = $this->removeProductFromCollection($arrReturn['currentPhoneProduct']['intNewWlrId'], $arrReturn['arrNewProducts']);
            $arrReturn['wizardInstanceID'] = AccountChange_SalesJourneyViewHelper::getWizardInstanceID('AccountChange');
            // the can change home package is used to show to correct link in the progress menu
            $arrReturn['currentProduct']['canChangeHomePhonePackage'] = $_SESSION['canChangeHomePhonePackage'];
            AccountChange_SalesJourneyViewHelper::setCompletedViews(['C2FBroadband']);
            $arrReturn['completedViews'] = AccountChange_SalesJourneyViewHelper::getCompletedViews();
        }
        return $arrReturn;
    }

    /**
     *
     * @return bool
     */
    public function isC2fToggleSet()
    {
       return AccountChange_C2mSalesChannels::isC2fToggleSet();
    }

    /**
     * Returns a Auth_BusinessActor object for current logged in user.
     *
     * @return Auth_BusinessActor
     */
    protected function getCurrentBusinessActor()
    {
        $objLogin = Auth_Auth::getCurrentLogin();

        if ($objLogin instanceof Auth_Login) {
            return $objLogin->getBusinessActor();
        }

        return false;
    }

    /**
     * @return bool
     */
    public function isBroadbandOnlyJourney()
    {
        if ($this->isApplicationStateVariable('bolChangeBroadbandOnly')) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Get an instance of AccountChange_Account for the current service
     *
     * @param integer $serviceId Service id
     *
     * @return AccountChange_Account
     */
    protected function getAccountInstance($serviceId)
    {
        return AccountChange_Account::instance(new Int($serviceId));
    }

    /**
     * Remove a product from the collection.
     *
     * @param string $remove
     * @param array $fromCollection
     *
     * @return array
     */
    private function removeProductFromCollection($remove, $fromCollection)
    {
        return array_filter($fromCollection, function($product) use($remove){
            return $product['intNewWlrId'] !== $remove;
        });
    }

    /**
     * Create helper variables used in templates and product filtering.
     *
     * @param array $data
     * @param array $arrValidatedApplicationData
     *
     * @return array
     */
    private function createProductComparators($arrMobileBoltonMapping, $oldWlrID)
    {
        $currentMappedWlrProductId = array_search($oldWlrID, $arrMobileBoltonMapping);

        return [
            'oldWlrId' => $oldWlrID,
            'currentMappedWlrProductId' => $currentMappedWlrProductId,
            'currentProductHasMobileBolton' => $currentMappedWlrProductId ? 'true' : 'false',
        ];

    }

    /**
     * Extract the current product.
     *
     * @param array $arrReturn
     *
     * @return mixed
     */
    private function getCurrentPhoneProduct($arrNewProducts, $comparators)
    {
        $currentProduct = array_filter($arrNewProducts, function($product) use ($comparators){
            return $product['intNewWlrId'] === $comparators['oldWlrId']
            || $product['intNewWlrId'] === $comparators['currentMappedWlrProductId'];
        });

        return array_pop($currentProduct);
    }

    /**
     * Add boolean to product to indicate whether product can have the
     * 100 mobile minutes bolton added to it.
     *
     * @param array $arrReturn
     *
     * @return array
     */
    private function addBoltonData($arrReturn)
    {
        $arrMobileBoltonMapping = $arrReturn['arrMobileBoltonMapping'];
        return array_map(function($product) use ($arrMobileBoltonMapping){
            $product['displayMobileBoltonCheckbox'] = array_key_exists($product['intNewWlrId'], $arrMobileBoltonMapping);
            return $product;
        }, $arrReturn['arrNewProducts']);
    }


    /**
     * Check to see if we can deal with Wlr Changes
     *
     * @param int $intOldComponentId the accounts old service component id
     *
     * @return bool true if change allowed
     */
    protected function wlrChangesAllowed($intOldComponentId = 0)
    {
        $this->includeLegacyFiles();
        $objCoreService = $this->getApplicationStateVariable('objCoreService');

        // no current WLR component - lets check if we can add
        if (0 == $intOldComponentId) {
            return CWlrProduct::canAddWlr($objCoreService->getServiceId());
        } else {
            // there is a component - lets' check if we can change
            return CWlrProduct::canChangeWlr($objCoreService->getServiceId());
        }
    }

    /**
     * Validation for the new WLR service component id
     *
     * @param int   $intNewWlrId  possible new service component id
     * @param array $mobileBoltOn mobile bolt-on options
     *
     * @return array
     */
    public function valNewWlrId($intNewWlrId, $mobileBoltOn = array())
    {
        $arrValidatedReturn = array();

        $arrValidIds = array();
        $arrProductDetails = array();
        $intNewSdi = $this->getApplicationStateVariable('intNewSdi');

        $arrCurrentWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');

        $strContractHandle = 'MONTHLY';

        if (isset($arrCurrentWlrProduct['arrWlrProduct']['strContractHandle'])) {
            $strContractHandle = $arrCurrentWlrProduct['arrWlrProduct']['strContractHandle'];
        }

        $includeNoWlr = true;
        if ($this->isApplicationStateVariable('selectedProductFamily')) {
            $productFamily = $this->getApplicationStateVariable('selectedProductFamily');

            // If we're on a dual play product, then we must select a wlr product
            if ($productFamily->isDualPlay()) {
                $includeNoWlr = false;
            }
        }

        $keepingExistingContract = $this->keepingExistingContract($intNewSdi);
        $arrProducts = $this->getWlrProducts($intNewSdi,
                                             $strContractHandle,
                                             $includeNoWlr,
                                             $keepingExistingContract);

        $arrKeys = array_keys($intNewWlrId);
        $intNewWlrId = $arrKeys[0];

        //If the customer has opted to have additional mobile minutes, pick the appropriate WLR product Id
        if (isset($mobileBoltOn[$intNewWlrId])) {

            $wlrProductFilter = $this->getWlrProductFilter();
            $intNewWlrId = $wlrProductFilter->getMappedMobileProduct($intNewWlrId);
        }

        foreach ($arrProducts as $arrProduct) {
            $arrValidIds[] = $arrProduct['intNewWlrId'];
            $arrProductDetails[$arrProduct['intNewWlrId']] = $arrProduct;
        }

        if (!is_numeric($intNewWlrId) || !in_array($intNewWlrId, $arrValidIds)) {

            $this->addValidationError('intNewWlrId', 'INVALID');
        } else {

            $arrValidatedReturn['intNewWlrId'] = $intNewWlrId;
            $arrValidatedReturn['mobileBoltOn'] = $mobileBoltOn;
            $arrValidatedReturn['arrSelectedWlr'] = array(
                'strNewProduct' => $arrProductDetails[$intNewWlrId]['strProductName'],
                'intNewCost'    => $arrProductDetails[$intNewWlrId]['intProductCost'],
                'bolSplitPrice' => $arrProductDetails[$intNewWlrId]['bolSplitPrice'],
                'intNewLineRentCost' => $arrProductDetails[$intNewWlrId]['intLineRentCost'],
                'intNewCallPlanCost' => $arrProductDetails[$intNewWlrId]['intCallPlanCost'],
                'lineRentalDiscountedPrice' => $arrProductDetails[$intNewWlrId]['lineRentalDiscountedPrice'],
                'lineRentalDiscountAmount' => $arrProductDetails[$intNewWlrId]['lineRentalDiscountAmount']
            );

            // Checking if there is an ACTIVE LRS contract in the account and
            // setting the line rental price to zero if there is any

            if ($this->hasActiveLineRentalSaver()) {

                $arrValidatedReturn['arrSelectedWlr']['intNewLineRentCost'] = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    0
                );
            }
        }

        return $arrValidatedReturn;
    }

    /**
     * Inclusion of legacy files so we can mock them
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
    }

    /**
     * create a new instance to the WlrProductSorter class
     *
     * @return AccountChange_Product_WlrProductSorter
     */
    public function getWlrProductSorter()
    {
        $sorter = new AccountChange_Product_WlrProductSorter();
        return $sorter;
    }

    /**
     * create a new instance to the WlrProductFilter class
     *
     * @return AccountChange_Product_WlrProductFilter
     */
    public function getWlrProductFilter()
    {
        $wlrProductFilter = new AccountChange_Product_WlrProductFilter();
        return $wlrProductFilter;
    }

    /**
     * Get the selected broadband product from an application state variable
     *
     * @return array
     **/
    public function getSelectedBroadband()
    {
        return $this->getApplicationStateVariable('arrSelectedBroadband');
    }

    /**
     * Unit test wrapper for AccountChange_Controller::getMinAndMaxSpeedRanges.
     *
     * @return array
     */
    public function getMinAndMaxSpeedRanges()
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $objLineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');

        return AccountChange_Controller::getMinAndMaxSpeedRanges($objCoreService, $objLineCheckResult);
    }

    /**
     * @param array $arrValidatedApplicationData
     * @return bool|string
     * @throws Exception
     */
    public function getDiscountContractLength(array $arrValidatedApplicationData)
    {
        $selectedBroadband = $arrValidatedApplicationData['arrSelectedBroadband'];

        if ($selectedBroadband && array_key_exists('presetDiscount', $selectedBroadband))
        {
            $presetDiscount = $selectedBroadband['presetDiscount'];

            if (is_array($presetDiscount)
                && array_key_exists('intDiscountLength', $presetDiscount)
                && !empty($presetDiscount['intDiscountLength'])) {
                return $presetDiscount['intDiscountLength'];
            } else {
                throw new Exception("A preset discount exists, but it has no contract length.");
            }
        }

        return false;
    }

    /**
     * Check if customer is keeping their existing contract
     *
     * @param $intNewServiceDefinitionId integer The service definition ID of the product the customer is taking
     *
     * @return bool Are they keeping their existing contract?
     */
    private function keepingExistingContract($intNewServiceDefinitionId)
    {
        $intOldServiceDefinitionId = $this->getApplicationStateVariable('intOldSdi');

        return is_numeric($intNewServiceDefinitionId)
            && is_numeric($intOldServiceDefinitionId)
            && ((int)$intNewServiceDefinitionId === (int)$intOldServiceDefinitionId);
    }
}
