<?php
/**
 * Account Change Manager Exception
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: ManagerException.class.php,v 1.2 2009-01-27 07:07:17 bselby Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Account Change Manager exception class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_ManagerException extends Exception
{
    /**
     * Enter description here...
     *
     */
    const ERR_NO_ACCOUNT_CHANGE_TYPE_SELECTED = 1;

    /**
     * Service Id not in a valid format (basically int)
     *
     */
    const ERR_INVALID_SERVICE_ID_TYPE         = 2;

    /**
     * Credit card details are not set correctly
     *
     */
    const ERR_INVALID_CREDIT_CARD_DETAILS_ID  = 3;
}
