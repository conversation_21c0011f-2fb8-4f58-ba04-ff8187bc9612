<?php
/**
 * Account Change Product Manager
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_Manager.class.php,v 1.3.2.3 2009/07/06 14:02:09 mstarbuck Exp $
 * @since     File available since 2008-08-22
 */
/**
 * Account Change Product Manager class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_Manager
{
    /**
     * Actions allowed
     *
     */
    const ACTION_ADD     = 1;
    const ACTION_CHANGE  = 2;
    const ACTION_REMOVE  = 3;
    const ACTION_NONE    = 4;
    const ACTION_REFRESH = 5;

    /**
     * Products
     *
     */
    const PRODUCT_TYPE_SERVICE_DEFINITION  = 'ServiceDefinition';
    const PRODUCT_TYPE_INTERNET_CONNECTION = 'InternetConnection';
    const PRODUCT_TYPE_SERVICE_COMPONENT   = 'ServiceComponent';
    const PRODUCT_TYPE_WLR                 = 'Wlr';
    const PRODUCT_TYPE_MAILBOX             = 'Mailbox';
    const PRODUCT_TYPE_FIREWALL            = 'Firewall';
    const PRODUCT_TYPE_COMMUNITY           = 'Community';

    /**
     * Account Change Operations
     */
    const ACCOUNT_CHANGE_UPGRADE   = 1;
    const ACCOUNT_CHANGE_DOWNGRADE = 2;
    const ACCOUNT_CHANGE_SAME      = 3;

    /**
     * Factory method for creating the products
     *
     * @param int    $intAction      Which action
     * @param int    $intId          Action id
     * @param string $strProductType Product type
     * @param array  $arrOptions     Options
     *
     * @return AccountChange_Product_Configuration
     * @throws AccountChange_Product_ManagerException
     */
    public static function factory($intAction, $intId, $strProductType, array $arrOptions = array())
    {
        self::validateAction($intAction);
        self::validateProductId($intId);
        self::validateProductType($strProductType);

        $strClassName = "AccountChange_Product_{$strProductType}";

        if (!class_exists($strClassName)) {

            throw new AccountChange_Product_ManagerException(
                "Product configuration class doesn't exists: '$strClassName'",
                AccountChange_Product_ManagerException::ERR_PRODUCT_CONFIGURATION_CLASS_NOT_EXIST
            );
        }

        $objProduct = new $strClassName($intId, $intAction, $arrOptions);

        if (!($objProduct instanceof AccountChange_Product_Configuration)) {

            throw new AccountChange_Product_ManagerException(
                "Invalid Product configuration class: '$strClassName'",
                AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_CONFIGURATION_CLASS
            );
        }

        return $objProduct;
    }

    /**
     * Factory method for creating the product using a component instance id
     *
     * @param int    $intAction      Action
     * @param int    $intComponentId Component id
     * @param string $strProductType Product type
     * @param array  $arrOptions     Options
     *
     * @return AccountChange_Product_Configuration
     */
    public static function factoryUsingComponentId(
        $intAction,
        $intComponentId,
        $strProductType,
        array $arrOptions = array()
    ) {
        self::validateAction($intAction);
        self::validateProductId($intComponentId);
        self::validateProductType($strProductType);

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $arrComponentDetails = $objDatabase->getComponentDetails($intComponentId);
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        if (!isset($arrComponentDetails['component_type_id'])) {

            throw new AccountChange_Product_ManagerException(
                'Component type Id not set in arrComponentDetails',
                AccountChange_Product_ManagerException::ERR_COMPONENT_TYPE_ID_NOT_SET
            );
        }

        $objProduct = self::factory(
            $intAction,
            $arrComponentDetails['component_type_id'],
            $strProductType,
            $arrOptions
        );
        $objProduct->setComponentId($intComponentId);

        return $objProduct;
    }


    /**
     * Returns {@link AccountChange_Product_Configuration} object based on {@link ProductFamily_Value}
     *
     * It morphs {@link ProductFamily} object into proper {@link AccountChange_Product_Configuration} object,
     * so AccountCHange Application can deal with it nicely
     *
     * @param ProductFamily_Bpr09 $objFamilyProduct Product Family object
     * @param int                 $intAction        Action
     * @param array               $arrOptions       Options
     *
     * <AUTHOR> Starbuck (<EMAIL>)
     * @uses   AccountChange_Product_Manager::validateAction()
     * @uses   ProductFamily_Bpr09::getMarketFromLineCheckId()
     * @uses   ProductFamily_Bpr09::getBpr09FamilyInternetConnectionComponent()
     * @uses   AccountChange_Product_InternetConnection::attachFamilyProductObject()
     * @see    AccountChange_Product_Configuration
     * @return AccountChange_Product_Configuration
     * @throws AccountChange_Product_ManagerException
     */
    public static function factoryUsingFamilyObject($objFamilyProduct, $intAction, array $arrOptions = array())
    {
        self::validateAction($intAction);

        $market = $objFamilyProduct->getMarketFromLineCheckId($arrOptions['intLineCheckId']);

        //If no market has been returned, default to market 3
        if (is_object($market)) {
            $intServiceComponentId = $objFamilyProduct->getInternetConnectionComponent($market->getMarketId());
        } else {
            $intServiceComponentId = $objFamilyProduct->getInternetConnectionComponent(
                $marketId = LineCheck_Market::getDefault()->getMarketId()
            );

            //Raise a p3 to log the accounts so that someone
            //can check the amount of affected accounts.
            AccountChange_Product_Manager::raiseAutoProblem($arrOptions['intServiceId']);
        }

        $objProduct = new AccountChange_Product_InternetConnection($intServiceComponentId, $intAction, $arrOptions);
        $objProduct->attachFamilyProductObject($objFamilyProduct);

        return $objProduct;
    }


    /**
     * Validates Manager action
     *
     * @param int $intAction (Valid actions: add, change, refresh, remove or none)
     *
     * @see AccountChange_Product_Manager::ACTION_ADD
     * @see AccountChange_Product_Manager::ACTION_CHANGE
     * @see AccountChange_Product_Manager::ACTION_REMOVE
     * @see AccountChange_Product_Manager::ACTION_NONE
     * @see AccountChange_Product_Manager::ACTION_REFRESH
     *
     * @return bool
     * @throws AccountChange_Product_ManagerException
     */
    protected static function validateAction($intAction)
    {
        $arrValidActions = array(
            self::ACTION_ADD,
            self::ACTION_CHANGE,
            self::ACTION_REMOVE,
            self::ACTION_NONE,
            self::ACTION_REFRESH
        );

        if (!in_array($intAction, $arrValidActions)) {

            throw new AccountChange_Product_ManagerException(
                'Invalid action called - ' . print_r($intAction, 1),
                AccountChange_Product_ManagerException::ERR_INVALID_ACTION
            );
        }

        return true;
    }

    /**
     * Validates Manager product id
     *
     * @param int $intId Id
     *
     * @return bool
     * @throws AccountChange_Product_ManagerException
     */
    protected static function validateProductId($intId)
    {
        if (empty($intId) || !is_numeric($intId)) {

            throw new AccountChange_Product_ManagerException(
                'Invalid product id provided - ' . print_r($intId, 1),
                AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_ID
            );
        }

        return true;
    }

    /**
     * Validates Manager product type
     *
     * @param string $strProductType (Valid products: service definition based, wlr)
     *
     * @see AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION
     * @see AccountChange_Product_Manager::PRODUCT_TYPE_WLR
     *
     * @return bool
     * @throws AccountChange_Product_ManagerException
     */
    protected static function validateProductType($strProductType)
    {
        $arrValidProductTypes = array(self::PRODUCT_TYPE_SERVICE_DEFINITION,
                                      self::PRODUCT_TYPE_WLR,
                                      self::PRODUCT_TYPE_SERVICE_COMPONENT,
                                      self::PRODUCT_TYPE_FIREWALL,
                                      self::PRODUCT_TYPE_COMMUNITY,
                                      self::PRODUCT_TYPE_INTERNET_CONNECTION,
                                      self::PRODUCT_TYPE_MAILBOX);

        if (!in_array($strProductType, $arrValidProductTypes)) {

            throw new AccountChange_Product_ManagerException(
                'Invalid product type provided - ' . print_r($strProductType, 1),
                AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_TYPE
            );
        }

        return true;
    }

    /**
     * Raises an auto problem using the template stored in
     * /local/codebase2005/content/templatedautoproblems/PLUSNET/WORKPLACE_ISP_ADMIN
     *
     * @param int $serviceId Service id
     *
     * @return void
     */
    protected static function raiseAutoProblem($serviceId)
    {
        $scriptUser = SoapSession::getScriptUserDetails(Partner_Partner::getDataset());
        $scriptActor = Auth_BusinessActor::get($scriptUser['actorId']);

        $autoProblemClient = BusTier_BusTier::getClient('autoproblem');

        $data['serviceId'] = $serviceId;
        $data['market'] = LineCheck_Market::getDefault()->getMarketId();

        $autoProblem = $autoProblemClient->prepareAutoProblem(
            'account_change_picked_default_market',
            $data,
            $scriptActor
        );

        $autoProblem->raiseProblem();
    }
}
