<?php
/**
 * Account Configuration Exception
 *
 * Exception for AccountChange_AccountConfiguration
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: AccountConfigurationException.class.php,v 1.2 2009-01-27 07:07:14 bselby Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Account Configuration Exception Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_AccountConfigurationException extends Exception
{
    /**
     * Invalid Product Configuration Object
     *
     */
    const ERR_INVALID_PRODUCT_CONFIGURATION_OBJECT = 1;

    /**
     * Enter description here...
     *
     */
    const ERR_INVALID_COMPONENT_FOR_SERVICE_DEFINITION = 2;

    /**
     * Enter description here...
     *
     */
    const ERR_NO_VALID_PRODUCT_CONFIGURATION_FOUND = 3;

    /**
     * An Account Configuration must have a service definition product
     *
     */
    const ERR_MUST_HAVE_SERVICE_DEFINITION_PRODUCT = 4;

    /**
     * Invalid service Id
     *
     */
    const ERR_INVALID_SERVICE_ID = 5;
}
