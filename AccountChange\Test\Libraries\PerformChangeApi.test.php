<?php
/**
 * Tests for the PerformChangeApi
 *
 * PHP version 5
 *
 * @category   AccountChange
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2013 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */

/**
 * Tests for the PerformChangeApi action
 *
 * @category   AccountChange
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2013 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_PerformChangeApi_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Make sure the registry is cleared between tests
     *
     * @return void
     */
    protected function setUp()
    {
        AccountChange_Registry::instance()->reset();
        Db_Manager::reset();
    }

    /**
     * Test getters and setters
     *
     * @return void
     **/
    public function testGettersAndSetters()
    {
        $api = new AccountChange_PerformChangeApi();
        $api->setServiceId(1);
        $api->setPromoCode('A');
        $api->setToSdi(2);
        $api->setCurrentSdi(3);
        $api->setOldSdi(4);
        $api->setOldProductName('B');
        $api->setNewProductName('C');
        $api->setContractLengthHandle('D');
        $api->setNewWlrServiceComponentId(5);
        $api->setRetainDiscounts(true);
        $api->setAgreementDate('2009-09-09');
        $api->setContractType('V');
        $api->setContractSubType('CPI');
        $api->setRetainContract(true);
        $api->setHardwareOption('H');
        $api->setNewContractOrder(false);
        $api->setOneOffCharges(array(
            array(
                'name' => 'test',
                'amount' => 12,
                'amountExVat' => 10
            )
        ));
        $this->assertEquals(1, $api->getServiceId());
        $this->assertEquals('A', $api->getPromoCode());
        $this->assertEquals(2, $api->getToSdi());
        $this->assertEquals(3, $api->getCurrentSdi());
        $this->assertEquals(4, $api->getOldSdi());
        $this->assertEquals('B', $api->getOldProductName());
        $this->assertEquals('C', $api->getNewProductName());
        $this->assertEquals('D', $api->getContractLengthHandle());
        $this->assertEquals(5, $api->getNewWlrServiceComponentId());
        $this->assertEquals(true, $api->getRetainDiscounts());
        $this->assertEquals('2009-09-09', $api->getAgreementDate());
        $this->assertEquals('V', $api->getContractType());
        $this->assertEquals('CPI', $api->getContractSubType());
        $this->assertEquals(true, $api->getRetainContract());
        $this->assertEquals('H', $api->getHardwareOption());
        $this->assertEquals(false, $api->isNewContractOrder());
        $this->assertEquals(array(
            array(
                'name' => 'test',
                'amount' => 12,
                'amountExVat' => 10
            )
        ), $api->getOneOffCharges());
    }

    /**
     * Tests that init correctly populates account change details
     *
     * @covers AccountChange_PerformChangeApi::__construct
     * @covers AccountChange_PerformChangeApi::setServiceId
     * @covers AccountChange_PerformChangeApi::getServiceId
     * @covers AccountChange_PerformChangeApi::setPromoCode
     * @covers AccountChange_PerformChangeApi::getPromoCode
     * @covers AccountChange_PerformChangeApi::setToSdi
     * @covers AccountChange_PerformChangeApi::getToSdi
     * @covers AccountChange_PerformChangeApi::setCurrentSdi
     * @covers AccountChange_PerformChangeApi::getCurrentSdi
     * @covers AccountChange_PerformChangeApi::setOldSdi
     * @covers AccountChange_PerformChangeApi::getOldSdi
     * @covers AccountChange_PerformChangeApi::setOldProductName
     * @covers AccountChange_PerformChangeApi::getOldProductName
     * @covers AccountChange_PerformChangeApi::setNewProductName
     * @covers AccountChange_PerformChangeApi::getNewProductName
     * @covers AccountChange_PerformChangeApi::getContractLengthHandle
     * @covers AccountChange_PerformChangeApi::setContractLengthHandle
     * @covers AccountChange_PerformChangeApi::init
     *
     * @return void
     **/
    public function testInitCorrectlyInitialisesChange()
    {
        $serviceId = 12345;
        $toSdi = 8888;
        $contractLengthHandle = 'HANDLE';
        $promoCode = 'FREESTUFF';

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getProductDetailsForAccountChange'),
            array()
        );

        $mockApi->init($serviceId, $contractLengthHandle, $promoCode);

        $mockApi->setToSdi($toSdi);

        $this->assertEquals($serviceId, $mockApi->getServiceId());
        $this->assertEquals($promoCode, $mockApi->getPromoCode());
        $this->assertEquals($toSdi, $mockApi->getToSdi());
        $this->assertEquals($contractLengthHandle, $mockApi->getContractLengthHandle());
    }

    /**
     * Test getAccountChangeDetailsArray correctly returns array
     *
     * @covers AccountChange_PerformChangeApi::getAccountChangeDetailsArray
     *
     * @return void
     **/
    public function testGetAccountChangeDetailsArrayCorrectlyReturns()
    {
        $api = new AccountChange_PerformChangeApi();

        $date = date('Y-m-d');
        $appointmentData = array(
            "notes" => "Test the notes for appointment",
            "live" => array(
                "date" => "10-09-2021",
                "timeSlot" => "AM",
                "ref" => "Andy"
            )
        );

        $address = new AccountChange_AccountChangeAddress();

        $appointment = new AccountChange_AccountChangeAppointment($appointmentData);
        $api->setServiceId(1);
        $api->setPromoCode('A');
        $api->setToSdi(2);
        $api->setCurrentSdi(3);
        $api->setOldSdi(4);
        $api->setOldProductName('B');
        $api->setNewProductName('C');
        $api->setContractLengthHandle('D');
        $api->setContractType('F');
        $api->setAgreementDate($date);
        $api->setChangeScheduleId(13422);
        $api->setAddress($address);
        $api->setAppointment($appointment);
        $api->setIsRecontract(true);



        $expectedReturn = array(
            'promoCode' => 'A',
            'intOldSdi' => 4,
            'intCurrentSdi' => 3,
            'intNewSdi' => 2,
            'intServiceId' => 1,
            'strContractLengthHandle' => 'D',
            'strOldProductName' => 'B',
            'strNewProductName' => 'C',
            'newWlrScid' => null,
            'agreementDate' => $date,
            'contractType' => 'F',
            'contractSubType' => null,
            'retainContract' => null,
            'intServiceChangeScheduleId' => 13422,
            'oneOffCharges' => null,
            'bolHouseMove' => false,
            'address' => $address,
            'fttpAppointment' => $appointment,
            'backDatedDate' => null,
            'newContractOrder' => false
        );
        $accountChange = $api->getAccountChangeDetailsArray();
        $this->assertEquals($accountChange, $expectedReturn);

        $api->setRetainContract(true);
        $api->setContractType('V');
        $api->setContractSubType('CPI');

        $expectedReturn = array(
            'promoCode' => 'A',
            'intOldSdi' => 4,
            'intCurrentSdi' => 3,
            'intNewSdi' => 2,
            'intServiceId' => 1,
            'strContractLengthHandle' => 'D',
            'strOldProductName' => 'B',
            'strNewProductName' => 'C',
            'newWlrScid' => null,
            'agreementDate' => $date,
            'contractType' => 'V',
            'contractSubType' => 'CPI',
            'retainContract' => true,
            'intServiceChangeScheduleId' => 13422,
            'oneOffCharges' => null,
            'bolHouseMove' => false,
            'address' => $address,
            'fttpAppointment' => $appointment,
            'backDatedDate' => null,
            'newContractOrder' => false
        );
        $accountChange = $api->getAccountChangeDetailsArray();
        $this->assertEquals($accountChange, $expectedReturn);
    }


    /**
     * Tests that doInstantChange performs the expected actions on change
     *
     * @covers AccountChange_PerformChangeApi::doInstantChange
     * @covers AccountChange_PerformChangeApi::output
     * @covers AccountChange_PerformChangeApi::preRun
     *
     * @return void
     **/
    public function testDoInstantChangePerformChangeFailsWhenOldSidNoEqualToCurrentSdi()
    {
        $serviceId = 12345;
        $toSdi = 8888;
        $contractLengthHandle = 'HANDLE';
        $promoCode = 'FREESTUFF';
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getProductDetailsForAccountChange',
                'isCustomerUser',
                'setAccessTechnologyValues',
                'getWlrInformation'
            ),
            array()
        );

        $mockApi->setServiceId($serviceId);

        $mockApi
            ->expects($this->any())
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue(array('intOldWlrId' => '')));

        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 3,
                        'intNewSdi' => 2,
                        'intServiceId' => 1,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct'
                    )
                )
            );
        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(false));

        $mockApi->setAuditLogger(new TestLogger());

        $mockApi->doInstantChange();

        $loggedMessages = $mockApi->getAuditLogger()->getMessages();
        $expectedMessages = array(
            0 => 'Customer changing from OldProduct to NewProduct',
            1 => 'Old SDI (4 not equal current SDI 3)',
            2 => 'Account change can not be performed.'
        );

        $this->assertEquals($loggedMessages, $expectedMessages);
    }

    /**
     * Tests that doInstantChange performs the expected actions on change
     *
     * @covers AccountChange_PerformChangeApi::doInstantChange
     * @covers AccountChange_PerformChangeApi::output
     * @covers AccountChange_PerformChangeApi::preRun
     *
     * @return void
     **/
    public function testDoInstantChangePerformChange()
    {
        $serviceId = 12345;
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';
        $scheduleId = 1337;
        AccountChange_Registry::instance()->setEntry('completeServiceChangeSchedule', true);

        $mockDb = $this->getMock(
            'Db_Adaptor',
            [],
            ['AccountChange', Db_Manager::DEFAULT_TRANSACTION, false]
        );

        Db_Manager::setAdaptor('AccountChange', $mockDb);

        $mockCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId')
        );

        $mockMGALSHelper = $this->getMock(
            'AccountChange_MGALSHelper',
            ['makeScheduledMGALSLiveForServiceId', 'getMakeLiveResultMessage'],
            [],
            '',
            false
        );
        AccountChange_ServiceManager::setService('MgalsHelper', $mockMGALSHelper);

        $mockAccountConfig = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(),
            array(array())
        );

        $mockManager = $this->getMock(
            'AccountChange_Manager',
            array(
                'changeAccount',
                'getServiceChangeScheduleId',
                'completeServiceChangeSchedule',
                'sendConfirmationEmails'),
            array($serviceId, $mockAccountConfig, $mockAccountConfig)
        );

        $mockManager
            ->expects($this->once())
            ->method('changeAccount')
            ->will($this->returnValue(true));

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getCoreServiceForServiceId',
                'getDiscountLength',
                'setMgals',
                'restoreAccountManager',
                'getProductDetailsForAccountChange',
                'isCustomerUser',
                'getLineCheckFromDatabase',
                'restoreEmailData',
                'getWlrInformation'
            ),
            array()
        );

        $mockApi->setServiceId($serviceId);

        $mockApi
            ->expects($this->any())
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue(array('intOldWlrId' => '')));

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            array()
        );

        $mockApi->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->will($this->returnValue($mockLineCheck));

        $mockApi->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $mockApi->expects($this->never())
            ->method('doLineCheck');

        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array('saveAddressRefAndDatabaseCode'),
            array()
        );

        $mockManager
            ->expects($this->any())
            ->method('getServiceChangeScheduleId')
            ->willReturn($scheduleId);

        $mockManager
            ->expects($this->once())
            ->method('completeServiceChangeSchedule')
            ->with($scheduleId, $mockDb)
            ->willReturn($scheduleId);

        $mockApi
            ->expects($this->never())
            ->method('restoreEmailData');

        $mockManager
            ->expects($this->never())
            ->method('sendConfirmationEmails');

        $mockAddress->expects($this->once())
            ->method('saveAddressRefAndDatabaseCode')
            ->with($serviceId);

        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 4,
                        'intNewSdi' => 2,
                        'intServiceId' => $serviceId,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct',
                        'agreementDate =>' => '2009-09-09',
                        'contractType' => 'V',
                        'contractSubType' => 'CPI',
                        'address' => $mockAddress
                    )
                )
            );

        $mockMGALSHelper
            ->expects($this->once())
            ->method('makeScheduledMGALSLiveForServiceId');

        $mockMGALSHelper
            ->expects($this->once())
            ->method('getMakeLiveResultMessage')
            ->willReturn('Log message from MGALS Helper');

        $mockApi
            ->expects($this->once())
            ->method('getCoreServiceForServiceId')
            ->with($serviceId)
            ->will($this->returnValue($mockCoreService));

        $mockApi
            ->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->with($mockCoreService)
            ->will($this->returnValue($mockLineCheck));


        $mockApi
            ->expects($this->once())
            ->method('getDiscountLength')
            ->will($this->returnValue(0));

        $mockApi
            ->expects($this->any())
            ->method('setMgals')
            ->will($this->returnValue(true));

        $mockApi
            ->expects($this->once())
            ->method('restoreAccountManager')
            ->will($this->returnValue($mockManager));

        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(false));

        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        $mockApi->setAddress($mockAddress);

        $mockApi->setAuditLogger(new TestLogger());

        $mockApi->doInstantChange(true);

        $loggedMessages = $mockApi->getAuditLogger()->getMessages();
        $expectedMessages = array(
            0 => 'Customer changing from OldProduct to NewProduct',
            1 => 'Checks fine - account ready for processing',
            2 => 'Account change manager restored, calling changeAccount.',
            3 => 'Log message from MGALS Helper'
        );

        $this->assertEquals($loggedMessages, $expectedMessages);
    }

    /**
     * Tests that doInstantChange performs the expected action when the request originates from BBCR
     *
     * @covers AccountChange_PerformChangeApi::doInstantChange
     * @covers AccountChange_PerformChangeApi::output
     * @covers AccountChange_PerformChangeApi::preRun
     *
     * @return void
     *
     * @throws Exception
     */
    public function testDoInstantChangePerformChangeForBBCR()
    {
        $serviceId = 12345;
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';
        $scheduleId = 1337;
        $mockEmailData = array('some data');
        AccountChange_Registry::instance()->setEntry('completeServiceChangeSchedule', true);
        AccountChange_Registry::instance()->setEntry('runFromBBCRScript', true);

        $mockDb = $this->getMock(
            'Db_Adaptor',
            [],
            ['AccountChange', Db_Manager::DEFAULT_TRANSACTION, false]
        );

        Db_Manager::setAdaptor('AccountChange', $mockDb);

        $mockCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId')
        );

        $mockMGALSHelper = $this->getMock(
            'AccountChange_MGALSHelper',
            ['makeScheduledMGALSLiveForServiceId', 'getMakeLiveResultMessage'],
            [],
            '',
            false
        );
        AccountChange_ServiceManager::setService('MgalsHelper', $mockMGALSHelper);

        $mockAccountConfig = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(),
            array(array())
        );

        $mockManager = $this->getMock(
            'AccountChange_Manager',
            array(
                'changeAccount',
                'getServiceChangeScheduleId',
                'completeServiceChangeSchedule',
                'sendConfirmationEmails'),
            array($serviceId, $mockAccountConfig, $mockAccountConfig)
        );

        $mockManager
            ->expects($this->once())
            ->method('changeAccount')
            ->will($this->returnValue(true));

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getCoreServiceForServiceId',
                'getDiscountLength',
                'setMgals',
                'restoreAccountManager',
                'getProductDetailsForAccountChange',
                'isCustomerUser',
                'getLineCheckFromDatabase',
                'restoreEmailData',
                'getWlrInformation'
            ),
            array()
        );

        $mockApi->setServiceId($serviceId);

        $mockApi
            ->expects($this->any())
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue(array('intOldWlrId' => '')));

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            array()
        );

        $mockApi->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->will($this->returnValue($mockLineCheck));

        $mockApi->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $mockApi->expects($this->never())
            ->method('doLineCheck');

        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array('saveAddressRefAndDatabaseCode'),
            array()
        );

        $mockManager
            ->expects($this->any())
            ->method('getServiceChangeScheduleId')
            ->willReturn($scheduleId);

        $mockManager
            ->expects($this->once())
            ->method('completeServiceChangeSchedule')
            ->with($scheduleId, $mockDb)
            ->willReturn($scheduleId);

        $mockApi
            ->expects($this->once())
            ->method('restoreEmailData')
            ->will($this->returnValue($mockEmailData)
            );

        $mockManager
            ->expects($this->once())
            ->method('sendConfirmationEmails');

        $mockAddress->expects($this->once())
            ->method('saveAddressRefAndDatabaseCode')
            ->with($serviceId);

        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 4,
                        'intNewSdi' => 2,
                        'intServiceId' => $serviceId,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct',
                        'agreementDate =>' => '2009-09-09',
                        'contractType' => 'V',
                        'contractSubType' => 'CPI',
                        'address' => $mockAddress
                    )
                )
            );

        $mockMGALSHelper
            ->expects($this->once())
            ->method('makeScheduledMGALSLiveForServiceId');

        $mockMGALSHelper
            ->expects($this->once())
            ->method('getMakeLiveResultMessage')
            ->willReturn('Log message from MGALS Helper');

        $mockApi
            ->expects($this->exactly(2))
            ->method('getCoreServiceForServiceId')
            ->with($serviceId)
            ->will($this->returnValue($mockCoreService));

        $mockApi
            ->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->with($mockCoreService)
            ->will($this->returnValue($mockLineCheck));


        $mockApi
            ->expects($this->once())
            ->method('getDiscountLength')
            ->will($this->returnValue(0));

        $mockApi
            ->expects($this->any())
            ->method('setMgals')
            ->will($this->returnValue(true));

        $mockApi
            ->expects($this->once())
            ->method('restoreAccountManager')
            ->will($this->returnValue($mockManager));

        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(false));

        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        $mockApi->setAddress($mockAddress);

        $mockApi->setAuditLogger(new TestLogger());

        $mockApi->doInstantChange(true);

        $loggedMessages = $mockApi->getAuditLogger()->getMessages();
        $expectedMessages = array(
            0 => 'Customer changing from OldProduct to NewProduct',
            1 => 'Checks fine - account ready for processing',
            2 => 'Account change manager restored, calling changeAccount.',
            3 => 'Log message from MGALS Helper'
        );

        $this->assertEquals($loggedMessages, $expectedMessages);
    }

    /**
     * Tests that doInstantChange performs the expected actions on change with one off charges
     *
     * @covers AccountChange_PerformChangeApi::doInstantChange
     * @covers AccountChange_PerformChangeApi::output
     * @covers AccountChange_PerformChangeApi::preRun
     *
     * @return void
     **/
    public function testDoInstantChangePerformChangeWithOneOffCharge()
    {
        $serviceId = 12345;
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            array()
        );

        $mockCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId')
        );

        $mockAccountConfig = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(),
            array(array())
        );

        $mockManager = $this->getMock(
            'AccountChange_Manager',
            array('changeAccount'),
            array($serviceId, $mockAccountConfig, $mockAccountConfig)
        );

        $mockManager
            ->expects($this->once())
            ->method('changeAccount')
            ->will($this->returnValue(true));

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getCoreServiceForServiceId',
                'getDiscountLength',
                'setMgals',
                'restoreAccountManager',
                'getProductDetailsForAccountChange',
                'isCustomerUser',
                'getLineCheckFromDatabase',
                'setAccessTechnologyValues',
                'getWlrInformation'
            ),
            array()
        );

        $mockApi->setServiceId($serviceId);

        $mockApi
            ->expects($this->any())
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue(array('intOldWlrId' => '')));

        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 4,
                        'intNewSdi' => 2,
                        'intServiceId' => $serviceId,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct',
                        'agreementDate =>' => '2009-09-09',
                        'contractType' => 'V',
                        'contractSubType' => 'CPI',
                        'oneOffCharges' => array(
                            array(
                                'name' => 'Test Charge',
                                'amount' => 12,
                                'amountExVat' => 10
                            )
                        )
                    )
                )
            );

        $mockApi
            ->expects($this->once())
            ->method('getCoreServiceForServiceId')
            ->with($serviceId)
            ->will($this->returnValue($mockCoreService));

        $mockApi
            ->expects($this->once())
            ->method('getCoreServiceForServiceId')
            ->with($serviceId)
            ->will($this->returnValue($mockCoreService));

        $mockApi
            ->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->with($mockCoreService)
            ->will($this->returnValue($mockLineCheck));

        $mockApi
            ->expects($this->once())
            ->method('getDiscountLength')
            ->will($this->returnValue(0));

        $mockApi
            ->expects($this->any())
            ->method('setMgals')
            ->will($this->returnValue(true));

        $mockApi
            ->expects($this->once())
            ->method('restoreAccountManager')
            ->will($this->returnValue($mockManager));

        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(false));

        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        $mockApi->setAuditLogger(new TestLogger());

        $mockApi->doInstantChange(true);

        $loggedMessages = $mockApi->getAuditLogger()->getMessages();
        $expectedMessages = array(
            0 => 'Customer changing from OldProduct to NewProduct',
            1 => 'Checks fine - account ready for processing',
            2 => 'Account change manager restored, calling changeAccount.'
        );

        $this->assertEquals($loggedMessages, $expectedMessages);
    }

    /**
     * Tests that doInstantChange sets the instant recontract flag correctly
     *
     * @test
     * @dataProvider instantRecontractProvider
     * @covers       AccountChange_PerformChangeApi::doInstantChange
     * @covers       AccountChange_PerformChangeApi::output
     * @covers       AccountChange_PerformChangeApi::preRun
     * @covers       AccountChange_PerformChangeApi::setIsInstantRecontract
     * @covers       AccountChange_PerformChangeApi::getIsInstantRecontract
     * @param $isInstantRecontract
     * @param $expectedResult
     * @return void
     */
    public function shouldCorrectlySetInstantRecontractFlagInRegistryOnDoInstantChange(
        $isInstantRecontract,
        $expectedResult
    ) {
        $serviceId = 12345;
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            array()
        );

        $mockCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId')
        );

        $mockAccountConfig = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(),
            array(array())
        );

        $mockManager = $this->getMock(
            'AccountChange_Manager',
            array('changeAccount'),
            array($serviceId, $mockAccountConfig, $mockAccountConfig)
        );

        $mockManager
            ->expects($this->once())
            ->method('changeAccount')
            ->will($this->returnValue(true));

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getCoreServiceForServiceId',
                'getDiscountLength',
                'setMgals',
                'restoreAccountManager',
                'getProductDetailsForAccountChange',
                'isCustomerUser',
                'getLineCheckFromDatabase',
                'setAccessTechnologyValues',
                'getWlrInformation'
            ),
            array()
        );

        $mockApi->setServiceId($serviceId);

        $mockApi
            ->expects($this->any())
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue(array('intOldWlrId' => '')));

        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 4,
                        'intNewSdi' => 2,
                        'intServiceId' => $serviceId,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct',
                        'agreementDate =>' => '2009-09-09',
                        'contractType' => 'V',
                        'contractSubType' => 'CPI'
                    )
                )
            );

        $mockApi
            ->expects($this->once())
            ->method('getCoreServiceForServiceId')
            ->with($serviceId)
            ->will($this->returnValue($mockCoreService));

        $mockApi
            ->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->with($mockCoreService)
            ->will($this->returnValue($mockLineCheck));

        $mockApi
            ->expects($this->once())
            ->method('getDiscountLength')
            ->will($this->returnValue(0));

        $mockApi
            ->expects($this->any())
            ->method('setMgals')
            ->will($this->returnValue(true));

        $mockApi
            ->expects($this->once())
            ->method('restoreAccountManager')
            ->will($this->returnValue($mockManager));

        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(false));

        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        if ($isInstantRecontract) {
            $mockApi->setIsInstantRecontract($isInstantRecontract);
        }

        $mockApi->doInstantChange(true);
        $result = $result = AccountChange_Registry::instance()->getEntry('instantRecontract');
        $this->assertEquals($expectedResult, $result);
    }

    /**
     * @return array
     */
    public function instantRecontractProvider()
    {
        return [
            'Instant Recontract' => [true, true],
            'Not an Instant Recontract' => [false, null]
        ];
    }

    /**
     * Tests that isFibreProduct gives the correct expected result based on the
     * service definition id being a non-fibre product
     *
     * @covers AccountChange_PerformChangeApi::isFibreProduct
     *
     * @return void
     **/
    public function testIsFibreProductWithNonFibreProduct()
    {
        $sdi = 6755;
        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockDbAdaptor
            ->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $api = new AccountChange_PerformChangeApi();
        $result = $api->isFibreProduct($sdi);
        $this->assertEquals($result, false);
    }

    /**
     * Tests that isFibreProduct gives the correct expected result based on the
     * service definition id being a fibre product
     *
     * @covers AccountChange_PerformChangeApi::isFibreProduct
     *
     * @return void
     **/
    public function testIsFibreProductWithFibreProduct()
    {
        $sdi = 6888;
        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockDbAdaptor
            ->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(array('service_definition_id' => $sdi)));

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $api = new AccountChange_PerformChangeApi();
        $result = $api->isFibreProduct($sdi);
        $this->assertEquals($result, true);
    }

    /**
     * Tests that doInstantChange performs the expected actions on change,
     * and correctly deals with any exceptions
     *
     * @covers AccountChange_PerformChangeApi::doInstantChange
     * @covers AccountChange_PerformChangeApi::output
     * @covers AccountChange_PerformChangeApi::preRun
     *
     * @return void
     **/
    public function testDoInstantChangeDealsWithExceptions()
    {
        $serviceId = 12345;
        $toSdi = 8888;
        $contractLengthHandle = 'HANDLE';
        $promoCode = 'FREESTUFF';
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';

        $mockCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId')
        );

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            array()
        );

        $mockAccountConfig = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(),
            array(array())
        );

        $mockManager = $this->getMock(
            'AccountChange_Manager',
            array('changeAccount'),
            array($serviceId, $mockAccountConfig, $mockAccountConfig)
        );

        $mockManager
            ->expects($this->once())
            ->method('changeAccount')
            ->will($this->throwException(new Exception('oops')));

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getCoreServiceForServiceId',
                'getDiscountLength',
                'setMgals',
                'restoreAccountManager',
                'getProductDetailsForAccountChange',
                'errorLog',
                'isCustomerUser',
                'getLineCheckFromDatabase',
                'setAccessTechnologyValues',
                'getWlrInformation'
            ),
            array()
        );

        $eventLogger = $this->getMock(
            Plusnet\PhpMonitoringLogger\EndToEndOrderLogger::class,
            array('log')
        );

        $mockApi->setServiceId($serviceId);

        $mockApi
            ->expects($this->any())
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue(array('intOldWlrId' => '')));

        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 4,
                        'intNewSdi' => 2,
                        'intServiceId' => $serviceId,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct',
                        ''

                    )
                )
            );

        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        $mockApi
            ->expects($this->once())
            ->method('getCoreServiceForServiceId')
            ->with($serviceId)
            ->will($this->returnValue($mockCoreService));

        $mockApi
            ->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->with($mockCoreService)
            ->will($this->returnValue($mockLineCheck));

        $mockApi
            ->expects($this->once())
            ->method('getDiscountLength')
            ->will($this->returnValue(0));

        $mockApi
            ->expects($this->any())
            ->method('setMgals')
            ->will($this->returnValue(true));

        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $mockApi
            ->expects($this->once())
            ->method('restoreAccountManager')
            ->will($this->returnValue($mockManager));

        $eventLogger->expects($this->any())
            ->method('log');

        $mockApi->setAuditLogger(new TestLogger());

        $mockApi->doInstantChange();

        $loggedMessages = $mockApi->getAuditLogger()->getMessages();
        $expectedMessages = array(
            0 => 'Customer changing from OldProduct to NewProduct',
            1 => 'Checks fine - account ready for processing',
            2 => 'Account change manager restored, calling changeAccount.',
            3 => 'AccountChange for service id 12345 failed.  The message returned was: oops'
        );

        $this->assertEquals($loggedMessages, $expectedMessages);
    }

    /**
     * Tests that doScheduledChange calls to doInstantChange and in turn calls
     * to _doProcessWlrRequest
     * @dataProvider scheduledChangeProvider
     * @covers       AccountChange_PerformChangeApi::doInstantChange
     * @covers       AccountChange_PerformChangeApi::output
     * @covers       AccountChange_PerformChangeApi::preRun
     * @covers       AccountChange_PerformChangeApi::doProcessWlrRequest
     * @covers       AccountChange_WlrProductConfigCreator::getObjCoreService
     * @covers       AccountChange_WlrProductConfigCreator::getObjBusinessActor
     * @covers       AccountChange_WlrProductConfigCreator::getObjLineCheckResults
     * @covers       AccountChange_WlrProductConfigCreator::setObjCoreService
     * @covers       AccountChange_WlrProductConfigCreator::setArrWlrProduct
     * @covers       AccountChange_WlrProductConfigCreator::setBolSchedule
     * @covers       AccountChange_WlrProductConfigCreator::setObjBusinessActor
     * @covers       AccountChange_WlrProductConfigCreator::setIntNewWlrId
     * @covers       AccountChange_WlrProductConfigCreator::setObjLineCheckResults
     * @covers       AccountChange_WlrProductConfigCreator::setBolContractResetWlr
     * @covers       AccountChange_WlrProductConfigCreator::setStrWlrContext
     * @covers       AccountChange_WlrProductConfigCreator::setIntOldSdi
     * @covers       AccountChange_WlrProductConfigCreator::setIntNewSdi
     * @covers       AccountChange_PerformChangeApi::getHouseMovePostScheduledChangeHelper
     *
     * @param bool $isHousemove is order a house move
     * @param bool $newContract Is order creating a new contract?
     * @return void
     */
    public function testDoScheduledChange($isHousemove, $newContract, $saveMgals, $lineCheckValid)
    {
        $serviceId = 12345;
        $toSdi = 8888;
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';
        $newWlrServiceComponentId = 5;
        $wlrInformation = array();
        $mockWlrProductConfigCreatorReturn = array();
        $contractLength = '12';
        $scheduleId = 1337;
        $lineCheckId = 666;

        $expectedMessages = array(
            0 => 'Customer changing from OldProduct to NewProduct',
            1 => 'Checks fine - account ready for processing',
            2 => 'Account change manager restored, calling changeAccount.'
        );

        $mockWlrProductConfigCreator = $this->getMock(
            'AccountChange_WlrProductConfigCreator',
            array('createWlrConfigurations', 'setIsHouseMove')
        );

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getTechnologyType'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $mockDbAdaptor
            ->method('getTechnologyType')
            ->withConsecutive([$currentSdi], [$toSdi])
            ->willReturnOnConsecutiveCalls('FTTC', 'FTTP');

        $mockWlrProductConfigCreator
            ->expects($this->once())
            ->method('setIsHouseMove')
            ->with($isHousemove);

        $mockWlrProductConfigCreator
            ->expects($this->once())
            ->method('createWlrConfigurations')
            ->will($this->returnValue($mockWlrProductConfigCreatorReturn));

        $mockCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId')
        );

        $mockLineCheckResults = $this->getMock(
            'LineCheck_Result',
            ['isValidResult', 'getLineCheckId']
        );

        $mockMGALSHelper = $this->getMock(
            'AccountChange_MGALSHelper',
            ['saveMGALSFromLineCheckData'],
            [],
            '',
            false
        );
        AccountChange_ServiceManager::setService('MgalsHelper', $mockMGALSHelper);

        $mockBusinessActor = $this->getMock(
            'Auth_BusinessActor'
        );
        $mockAccountConfig = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(),
            array(array())
        );
        $mockManager = $this->getMock(
            'AccountChange_Manager',
            array('changeAccount','getServiceChangeScheduleId'),
            array($serviceId, $mockAccountConfig, $mockAccountConfig)
        );
        $mockManager
            ->expects($this->once())
            ->method('changeAccount');

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getCoreServiceForServiceId',
                'getDiscountLength',
                'setMgals',
                'restoreAccountManager',
                'getProductDetailsForAccountChange',
                'getCoreService',
                'getWlrInformation',
                'getBusinessActor',
                'getLineCheckResults',
                'getWlrProductConfigCreator',
                'errorLog',
                'isCustomerUser',
                'getHouseMovePostScheduledChangeHelper',
                'getLineCheckFromDatabase'
            ),
            array()
        );
        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 4,
                        'intNewSdi' => 2,
                        'intServiceId' => $serviceId,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct',
                        ''
                    )
                )
            );
        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        $mockApi
            ->expects($this->exactly(2))
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue($wlrInformation));
        $mockApi
            ->expects($this->once())
            ->method('getWlrProductConfigCreator')
            ->will($this->returnValue($mockWlrProductConfigCreator));
        $mockApi
            ->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue($mockBusinessActor));
        $mockApi
            ->expects($this->any())
            ->method('getCoreService')
            ->will($this->returnValue($mockCoreService));
        $mockApi
            ->expects($this->once())
            ->method('getLineCheckResults')
            ->will($this->returnValue($mockLineCheckResults));

        $mockApi
            ->expects($this->once())
            ->method('getLineCheckFromDatabase')
            ->will($this->returnValue($mockLineCheckResults));

        $mockApi
            ->expects($this->once())
            ->method('getDiscountLength')
            ->will($this->returnValue(0));
        $mockApi
            ->expects($this->any())
            ->method('setMgals')
            ->will($this->returnValue(true));
        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));
        $mockApi
            ->expects($this->once())
            ->method('restoreAccountManager')
            ->will($this->returnValue($mockManager));

        $mockApi->setRecontractLengthMonths($contractLength);

        $mockManager
            ->expects($this->once())
            ->method('getServiceChangeScheduleId')
            ->willReturn($scheduleId);

        if ($isHousemove) {
            $houseMoveHelperMock = $this->getMockBuilder('AccountChange_HouseMovePostScheduledChangeHelper')
                ->setMethods(array('execute'))
                ->disableOriginalConstructor()
                ->getMock();

            $mockApi
                ->expects($this->once())
                ->method('getHouseMovePostScheduledChangeHelper')
                ->with($serviceId, $contractLength)
                ->willReturn($houseMoveHelperMock);

            $houseMoveHelperMock->expects($this->once())
                ->method('execute');
        } else {
            $mockApi
                ->expects($this->never())
                ->method('getHouseMovePostScheduledChangeHelper');
        }


        $mockApi->setShouldSaveScheduledMGALS($saveMgals);
        $lineCheckMatcher = $saveMgals ? $this->once() : $this->never();
        $mockLineCheckResults
            ->expects($lineCheckMatcher)
            ->method('isValidResult')
            ->willReturn($lineCheckValid);

        if ($lineCheckValid) {
            $mockLineCheckResults
                ->expects($this->once())
                ->method('getLineCheckId')
                ->willReturn($lineCheckId);

            $mockMGALSHelper
                ->expects($this->once())
                ->method('saveMGALSFromLineCheckData')
                ->with($lineCheckId, $toSdi, $scheduleId);
        } elseif ($saveMgals) {
            $expectedMessages[] = "No line check result available to retrieve MGS Data";
        }


        $mockApi->setAuditLogger(new TestLogger());
        $mockApi->setProductChangeForHouseMove($isHousemove);
        $mockApi->init($serviceId);
        $mockApi->setToSdi($toSdi);
        $mockApi->setNewWlrServiceComponentId($newWlrServiceComponentId);
        $mockApi->setNewContractOrder($newContract);
        $mockApi->doScheduledChange();

        $registry = AccountChange_Registry::instance();
        $this->assertEquals($newContract ? true : null, $registry->getEntry('phoneContractChange'));
        $this->assertEquals($mockApi->getAuditLogger()->getMessages(), $expectedMessages);
        $this->assertEquals($mockCoreService, $mockWlrProductConfigCreator->getObjCoreService());
        $this->assertEquals($mockBusinessActor, $mockWlrProductConfigCreator->getObjBusinessActor());
        $this->assertEquals($mockLineCheckResults, $mockWlrProductConfigCreator->getObjLineCheckResults());
        $this->assertEquals('FTTC', $mockApi->getCurrentAccessTechnology());
        $this->assertEquals('FTTP', $mockApi->getNewAccessTechnology());
    }

    /**
     * @return array
     */
    public function scheduledChangeProvider()
    {
        return [
            'With house move, new contract order, no MGAL Save' => [true, true, false, null],
            'With house move, no new contract, no MGAL Save' => [true, false, false, null],
            'Without house move, new contract order, no MGAL Save' => [false, true, false, null],
            'Without house move, no new contract, no MGAL Save' => [false, false, false, null],
            'MGAL Save True' => [false, true, true, true],
            'MGAL Save But no valid line check' => [false, true, true, false],
        ];
    }

    /**
     * Test setIsVariantSwitchForWlrAddOrRemoveFlag works properly
     *
     * @covers AccountChange_PerformChangeApi::setIsVariantSwitchForWlrAddOrRemoveFlag
     *
     * @return void
     */
    public function testSetIsVariantSwitchForWlrAddOrRemoveFlagWorksProperly()
    {
        $performChangeApi = new \AccountChange_PerformChangeApi();

        $registry = AccountChange_Registry::instance();

        $performChangeApi->setIsVariantSwitchForWlrAddOrRemoveFlag(true);

        $this->assertEquals(true, $registry->getEntry('bolVariantSwitchForWlrAddOrRemove'));

        $performChangeApi->setIsVariantSwitchForWlrAddOrRemoveFlag(false);

        $this->assertEquals(false, $registry->getEntry('bolVariantSwitchForWlrAddOrRemove'));
    }

    /**
     * Test if getNewWlrComponentId returns proper value
     *
     * @covers AccountChange_PerformChangeApi::getNewWlrComponentId
     *
     * @return void
     */
    public function testGetNewWlrComponentIdReturnsProperValue()
    {
        $performChangeApi = new \AccountChange_PerformChangeApi();

        $expectedNewWlrComponentId = ********;

        $registry = AccountChange_Registry::instance();

        $registry->setEntry('newWlrComponentId', $expectedNewWlrComponentId);

        $createdNewWlrComponentId = $performChangeApi->getNewWlrComponentId();

        $this->assertEquals($expectedNewWlrComponentId, $createdNewWlrComponentId);
    }

    /**
     * Tests that performNewLineCheckAndStoreResultFollowsCorrectPath calls the expected
     * functions in order to perform and store a line check
     *
     * @covers AccountChange_PerformChangeApi::performNewLineCheckAndStoreResult
     *
     * @return void
     **/
    public function testPerformNewLineCheckAndStoreResultFollowsCorrectPath()
    {

        $sid = 123455;
        $mockCoreService = $this->getMock(
            'Core_Service',
            array()
        );

        $mockLineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('isValidResult')
        );


        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getCoreServiceForServiceId',
                'getServiceId',
                'doLineCheck',
                'writeLineCheckResultToDb',
                'isCustomerUser'
            ),
            array()
        );

        $mockApi
            ->expects($this->once())
            ->method('getCoreServiceForServiceId')
            ->will($this->returnValue($mockCoreService));

        $mockApi
            ->expects($this->once())
            ->method('getServiceId')
            ->will($this->returnValue($sid));

        $mockApi
            ->expects($this->once())
            ->method('doLineCheck')
            ->will($this->returnValue($mockLineCheckResult));

        $mockApi->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $mockApi
            ->expects($this->once())
            ->method('writeLineCheckResultToDb')
            ->with($mockCoreService, $mockLineCheckResult);

        $mockApi->performNewLineCheckAndStoreResult();
    }

    /**
     * Tests the Setter function for populating the data required for recording the cusomers consent to switch
     *
     * @return void
     **/
    public function testSetConsentToSwitchData()
    {
        $registry = AccountChange_Registry::instance();

        $api = new AccountChange_PerformChangeApi();

        $api->setConsentToSwitchData(123456, 3, true, false, "PN-SP-1");

        $this->assertEquals(123456, $registry->getEntry('intServiceId'));
        $this->assertEquals(3, $registry->getEntry('intAppId'));
        $this->assertEquals(true, $registry->getEntry('bolWlrTakeover'));
        $this->assertEquals(false, $registry->getEntry('bolAdslTakeover'));
        $this->assertEquals("PN-SP-1", $registry->getEntry('vchConsentVersionHandle'));

        $registry->reset();
    }


    /**
     * Tests calculateScheduledChangeDate calls self install code if requested
     *
     * @return void
     **/
    public function testCalculateScheduledChangeDate()
    {
        $oldSdi = 1234;
        $toSdi = 2345;
        $changeDate = '2017-01-01';
        $sid = 2000122;
        $hasAppointment = true;
        $selfInstall = true;

        $mockHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('calculateSelfInstallDate', 'calculateScheduledChangeDate'),
            array()
        );

        $mockHelper
            ->expects($this->once())
            ->method('calculateSelfInstallDate');

        $mockHelper
            ->expects($this->once())
            ->method('calculateScheduledChangeDate')
            ->will($this->returnValue($changeDate));

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getOldSdi', 'getToSdi', 'getSchedulingHelper', 'isCustomerUser'),
            array()
        );

        $mockApi
            ->expects($this->once())
            ->method('getOldSdi')
            ->will($this->returnValue($oldSdi));

        $mockApi
            ->expects($this->once())
            ->method('getToSdi')
            ->will($this->returnValue($toSdi));

        $mockApi
            ->expects($this->once())
            ->method('getSchedulingHelper')
            ->will($this->returnValue($mockHelper));

        $mockApi->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $this->assertEquals($changeDate, $mockApi->calculateScheduledChangeDate($sid, $hasAppointment, $selfInstall));
    }

    /**
     * @test
     */
    public function whenBBCRCompleteAccountChangeIsCalledTheRegistryEntryIsUpdated()
    {
        $performChangeApi = $this
            ->getMockBuilder('AccountChange_PerformChangeApi')
            ->setMethods(array('doInstantChange'))
            ->getMock();

        $performChangeApi->bbcrCompleteAccountChange();

        $result = AccountChange_Registry::instance()->getEntry('runFromBBCRScript');

        $this->assertEquals(true, $result);
    }

    /**
     * Test default validators are used if house move is false
     */
    public function testDefaultValidatorsAreUsedIfHouseMoveIsFalse()
    {
        $performChangeApi = $this
            ->getMockBuilder('AccountChange_PerformChangeApi')
            ->setMethods(['checkValidationPolicies'])
            ->getMock();

        $performChangeApi->expects($this->once())
            ->method('checkValidationPolicies');

        $performChangeApi->setProductChangeForHouseMove(false);
        $performChangeApi->isAllowed();
        $expectedPolicies = AccountChange_ValidationCheck::getDefaultPolicies();
        $actualPolicies = $performChangeApi->getValidationPolicies();
        $this->assertEquals($expectedPolicies, $actualPolicies);
    }

    /**
     * Test validator filter is working
     */
    public function testHouseMoveValidatorIsRemovedIfProductChangeForHouseMoveIsSet()
    {
        $performChangeApi = $this
            ->getMockBuilder('AccountChange_PerformChangeApi')
            ->setMethods(['checkValidationPolicies'])
            ->getMock();

        $performChangeApi->expects($this->once())
            ->method('checkValidationPolicies');

        $performChangeApi->setProductChangeForHouseMove(true);
        $performChangeApi->isAllowed();
        $validationPolicyArray = $performChangeApi->getValidationPolicies();
        $this->assertNotContains('AccountChange_HouseMovePolicy', $validationPolicyArray);
    }

    public function testGetTechnologyType()
    {
        $performChangeApi = $this
            ->getMockBuilder('AccountChange_PerformChangeApi')
            ->setMethods(array('getProductDetailsForAccountChange','getServiceId','getToSdi','getAccountChangeDetailsArray'))
            ->getMock();

        $performChangeApi->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will($this->returnValue(
                array('intCurrentSdi'=>'',
                    'intOldSdi'=>'',
                    'strOldProductName'=>'')
            ));

        $performChangeApi->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will($this->returnValue(
                array('intNewSdi' => 3453)
            ));

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getTechnologyType'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $expectedReturn = "ADSL2+";

        $mockDbAdaptor
            ->expects($this->once())
            ->method('getTechnologyType')
            ->with(3453)
            ->will($this->returnValue($expectedReturn));

        $returned = $performChangeApi->getTechnologyType();

        $this->assertEquals($expectedReturn, $returned);
    }

    /**
     * @return void
     * @throws Exception
     */
    public function testCurrentWlrComponentIdSet()
    {
        $serviceId = 12345;
        $currentSdi = 3;
        $oldSdi = 4;
        $oldProductName = 'OldProduct';
        $newProductName = 'NewProduct';
        $intOldWlrId = 54321;

        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAccountChangeDetailsArray',
                'getProductDetailsForAccountChange',
                'isCustomerUser',
                'setAccessTechnologyValues',
                'getWlrInformation'
            ),
            array()
        );

        $mockApi->setServiceId($serviceId);

        $mockApi
            ->expects($this->any())
            ->method('getWlrInformation')
            ->with($serviceId)
            ->will($this->returnValue(array('intOldWlrId' => $intOldWlrId)));

        $mockApi
            ->expects($this->once())
            ->method('getAccountChangeDetailsArray')
            ->will(
                $this->returnValue(
                    array(
                        'promoCode' => 'A',
                        'intOldSdi' => 4,
                        'intCurrentSdi' => 3,
                        'intNewSdi' => 2,
                        'intServiceId' => 1,
                        'strContractLengthHandle' => 'D',
                        'strOldProductName' => 'OldProduct',
                        'strNewProductName' => 'NewProduct'
                    )
                )
            );
        $mockApi
            ->expects($this->once())
            ->method('getProductDetailsForAccountChange')
            ->will(
                $this->returnValue(
                    array(
                        'intCurrentSdi' => $currentSdi,
                        'intOldSdi' => $oldSdi,
                        'strOldProductName' => $oldProductName,
                        'strNewProductName' => $newProductName
                    )
                )
            );

        $mockApi
            ->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(false));

        $mockApi->setAuditLogger(new TestLogger());

        $mockApi->doInstantChange();

        $this->assertEquals($intOldWlrId, AccountChange_Registry::instance()->getEntry('currentWlrComponentId'));
    }
}


/**
 * Simple class to keep track of the output message we're creating by injecting
 * out own logger.
 *
 * @category   AccountChange
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> Starbuck <<EMAIL>>
 * @copyright  2013 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */
class TestLogger
{
    /**
     * Keep track of messages
     *
     * @var array
     **/
    protected $messages;

    /**
     * Log messages that we're writing to the audit log
     *
     * @param string $message Message we're logging
     * @param string $class   Name of calling class
     *
     * @return void
     **/
    public function write($message, $class)
    {
        $this->messages[] = $message;
    }

    /**
     * Getter for messages
     *
     * @return array
     **/
    public function getMessages()
    {
        return $this->messages;
    }
}
