<?php
include_once 'phing/Task.php';
class DirsReadable extends Task {

    private $_strBasedir = null;
    private $_strUser = null;
    private $_strReturnName;

    public function setBaseDir($str) {
        $this->_strBasedir = $str;
    }
    
    public function setUser($strUser) {
        $this->_strUser = $strUser;
    }

    public function setReturnName($str) {
        $this->_strReturnName = $str;
    }    

    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}  

		if (!$this->_strUser) {
		    throw new BuildException("You must specify the user attribute", $this->getLocation());
		}  
		
		if(!is_dir($this->_strBasedir)) {
			throw new BuildException("$this->_strBasedir is not a valid directory", $this->getLocation());		
		}

		$arrScanDir = scandir($this->_strBasedir);

		$arrUser = posix_getpwnam($this->_strUser);		

		foreach ($arrScanDir as $strDir) {
			
			if ($strDir=='.' or $strDir=='..' or $strDir=='CVS') continue;
			
			$strDir = $this->_strBasedir.'/'.$strDir;
			
			if(is_dir($strDir)) {
			
				$intPerms = fileperms($strDir);
				$intGroup = filegroup($strDir);
				$intOwner = fileowner($strDir);
			
				//check permissions
				if($intPerms & 0x0004) {
					//world readable
					continue;
				}
		    
				if(($intPerms & 0x0020) && ($intGroup ==  $arrUser['gid'])) {
					//group readable
					continue;
				}
		    
				if(($intPerms & 0x0004) && ($intOwner ==  $arrUser['uid'])) {
					//owner readable
					continue;
				}
							
				//no match, dir not readable
				$this->project->setProperty($this->_strReturnName, "$strDir is not readable for user $this->_strUser");
			}
		}
    }
}