<?php

use Plusnet\C2mApiClient\C2MClient as C2MClient;
use Plusnet\C2mApiClient\Entity\PriceIncreaseData;
/**
 * Class AccountChange_C2mPriceHelper
 *
 * Enables to fetch pricing data  from C2MAPI
 *
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_C2mPriceHelper
{
    public static function getPriceIncreasePercent ($sectorName, $year)
    {
        if (empty($sectorName) || empty($year) )
        {
            error_log(__CLASS__ . '->' . __METHOD__ . "Invalid Arguments");
            return false;
        }
        try
        {
            $c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
            $priceIncreaseData = $c2mClient->getPriceIncreaseData($sectorName, $year);
            if ($priceIncreaseData){
                return $priceIncreaseData->getPriceIncreasePercent();
            }
            else
            {
                return false;
            }
        }
        catch (Exception $e)
        {
            error_log(__CLASS__ . '->' . __METHOD__ . $e->getMessage());
            return false;
        }
    }
}