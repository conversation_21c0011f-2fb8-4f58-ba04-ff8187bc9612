<?php

use Plusnet\BillingApiClient\Service\ServiceManager as BillingServiceManager;
use Plusnet\HouseMoves\Services\ServiceManager as HouseMoveServiceManager;

/**
 * AccountChange ValidationChecks Test
 *
 * @category AccountChange
 * @package  AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 Plusnet
 * @since     File available since 2010-05-26
 */

use Plusnet\BillingApiClient\Service\ServiceManager as ServiceManager;

/**
 * AccountChange ValidationChecks Test
 *
 * Test class for AccountChange_ValidationCheck
 *
 * @category AccountChange
 * @package  AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 Plusnet
 */
class AccountChange_ValidationCheck_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for Auth_BusinessActor
     *
     * @var Auth_BusinessActor
     */
    protected $actor;

    /**
     * PHPUnit setup functionality
     *
     * @return void
     */
    public function setup()
    {
        $this->actor = new Auth_BusinessActor(null);
        $this->actor->setExternalUserId(1);
    }

    /**
     * Tear down functionality
     *
     * (non-PHPdoc)
     *
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @throws Exception
     * @return void
     */
    public function testProductChangeIsAllowedWithNoFailures()
    {
        $billingApiFacade = Mockery::mock('BillingApiFacade');
        $billingApiFacade->shouldReceive('isBillingAccountSuspended')->once()->andReturnFalse();
        BillingServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $houseMoveService = Mockery::mock();
        $houseMoveService->shouldReceive('houseMoveIsOpenForUser')->once()->andReturnFalse();
        HouseMoveServiceManager::setService('HouseMoveService', $houseMoveService);

        $businessActor = Mockery::mock('Auth_BusinessActor');
        $businessActor->shouldReceive('getExternalUserId');

        $validationPolicies = [
            AccountChange_HouseMovePolicy::class,
            AccountChange_BillingAccountSuspendedPolicy::class
        ];

        $validationCheck = new AccountChange_ValidationCheck($businessActor, $validationPolicies);

        $this->assertTrue($validationCheck->isAccountChangeAllowed());
        $this->assertFalse($validationCheck->getReasonForBlockage());
    }

    /**
     * @throws Exception
     * @return void
     */
    public function testProductChangeIsNotAllowedWithOnePassThenOneFail()
    {
        $houseMoveService = Mockery::mock();
        $houseMoveService->shouldReceive('houseMoveIsOpenForUser')->once()->andReturnFalse();
        HouseMoveServiceManager::setService('HouseMoveService', $houseMoveService);

        $billingApiFacade = Mockery::mock('BillingApiFacade');
        $billingApiFacade->shouldReceive('isBillingAccountSuspended')->once()->andReturnTrue();
        BillingServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $businessActor = Mockery::mock('Auth_BusinessActor');
        $businessActor->shouldReceive('getExternalUserId');
        $businessActor->shouldReceive('getUserType')->andReturn('PLUSNET_ENDUSER');

        $validationPolicies = [
            AccountChange_HouseMovePolicy::class,
            AccountChange_BillingAccountSuspendedPolicy::class
        ];

        $validationCheck = new AccountChange_ValidationCheck($businessActor, $validationPolicies);

        $this->assertFalse($validationCheck->isAccountChangeAllowed());
        $this->assertNotNull($validationCheck->getReasonForBlockage());
    }

    /**
     * @throws Exception
     * @return void
     */
    public function testProductChangeIsNotAllowedWithOneFailThenOnePass()
    {
        $houseMoveService = Mockery::mock();
        $houseMoveService->shouldReceive('houseMoveIsOpenForUser')->once()->andReturnTrue();
        HouseMoveServiceManager::setService('HouseMoveService', $houseMoveService);

        $billingApiFacade = Mockery::mock('BillingApiFacade');
        $billingApiFacade->shouldReceive('isBillingAccountSuspended')->andReturnFalse();
        BillingServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $businessActor = Mockery::mock('Auth_BusinessActor');
        $businessActor->shouldReceive('getExternalUserId');
        $businessActor->shouldReceive('getUserType')->andReturn('PLUSNET_ENDUSER');

        $validationPolicies = [
            AccountChange_HouseMovePolicy::class,
            AccountChange_BillingAccountSuspendedPolicy::class
        ];

        $validationCheck = new AccountChange_ValidationCheck($businessActor, $validationPolicies);

        $this->assertFalse($validationCheck->isAccountChangeAllowed());
        $this->assertNotNull($validationCheck->getReasonForBlockage());
    }

    /**
     * @throws Exception
     * @return void
     */
    public function testExceptionIsThrownWhenInvalidValidatorSupplied()
    {
        $this->setExpectedException(AccountChange_InvalidValidatorException::class);

        $businessActor = Mockery::mock('Auth_BusinessActor');
        $businessActor->shouldReceive('getExternalUserId');

        $validationPolicies = [
            DateTime::class
        ];

        $validationCheck = new AccountChange_ValidationCheck($businessActor, $validationPolicies);

        $validationCheck->isAccountChangeAllowed();
    }
}
