<?php
/**
 * Broadband product filter for portal
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Broadband product filter for portal
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Product_BroadbandProductFilter_KeepCurrentProductOnly extends
    AccountChange_Product_BroadbandProductFilter_Common
{

    /**
     * Filter the broadband product variants
     *
     * @param LineCheck_Result $lineCheck         Line check result
     * @param array            $availableProducts Available products
     * @param array            $currentProduct    Current product
     * @param string           $campaignCode      Campaign code indicating which landing page this journey originated from, if any
     *
     * @return array Filtered products
     */
    public function filter(LineCheck_Result $lineCheck, array $availableProducts, array $currentProduct, $campaignCode = '')
    {
        $currentProductSdi = isset($currentProduct['intSdi']) ? $currentProduct['intSdi'] : null;

        $currentProductVariants = $this->getServiceDefinitionVariants($currentProductSdi);

        if (empty($currentProductVariants)) {
            $currentProductVariants[] = $currentProductSdi;
        }

        if (!empty($currentProductSdi)) {
            foreach($availableProducts as $key => $product) {
                if (!in_array($product['intSdi'], $currentProductVariants)) {
                    unset($availableProducts[$key]);
                }
            }
        }


        return $availableProducts;
    }
}
