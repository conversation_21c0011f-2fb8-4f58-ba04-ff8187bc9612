<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_WaivePostageHelper
{
    const ELIGIBLE_ROUTER_HANDLES = [
        HardwareClient_Client::HANDLE_HUB_ONE_RES,
        HardwareClient_Client::HANDLE_HUB_TWO_DSL_RES,
        HardwareClient_Client::HANDLE_HUB_TWO_DSL_BIZ,
        HardwareClient_Client::HANDLE_HUB_TWO_DSL_JL,
    ];

    /* @var boolean $isHousemove */
    private $isHousemove;

    /* @var boolean $isRecontract */
    private $isRecontract;

    /* @var string $routerHandle */
    private $routerHandle;

    /* @var boolean $isWorkplace */
    private $isWorkplace;

    /**
     * @param boolean $isHousemove  is Housemove flag
     * @param boolean $isRecontract is Recontract flag
     * @param String  $routerHandle handle for router being offered
     * @param boolean $isWorkplace  is Workplace flag
     */
    public function __construct($isHousemove, $isRecontract, $routerHandle, $isWorkplace)
    {
        $this->isHousemove = $isHousemove;
        $this->isRecontract = $isRecontract;
        $this->routerHandle = $routerHandle;
        $this->isWorkplace = $isWorkplace;
    }

    /**
     * @return bool
     */
    public function shouldShowWaivePostageCheckbox()
    {
        if ($this->isWorkplace && $this->isOfferingEligibleRouter()) {
            if ($this->isHousemove) {
                return $this->isRecontract;
            } else {
                return true;
            }
        }

        return false;
    }

    /**
     * @return bool
     */
    private function isOfferingEligibleRouter()
    {
        return in_array($this->routerHandle, static::ELIGIBLE_ROUTER_HANDLES);
    }
}
