<?php
/**
 * Abstract class for all Greenbee/Waitrose Product Set Changes to <PERSON> action
 *
 * A simple abstract class for actions specific to the GB/WR -> JL change set
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @since      File available since 2011-09-06
 */
/**
 * Greenbee/Waitrose Product Set Change to <PERSON> action class
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_Action_AbstractGbWrChangeToJl extends AccountChange_Action
{
    /**
     * Stores details for the account
     *
     * @var Core_Service
     */
    protected $_accountDetails;

    /**
     * Old Service Definition ID
     *
     * @var integer
     */
    protected $_oldServiceDefinitionId;

    /**
     * New Service Definition ID
     *
     * @var integer
     */
    protected $_newServiceDefinitionId;

    /**
     * Is account change scheduled?
     *
     * @var boolean
     */
    protected $_isAccountChangeScheduled;

    /**
     * Execute the action
     *
     * If
     * - the account change actioned before the billing date for the current month.
     * - the product set change is from GB/WR to JL
     * Then
     * - Send email to inform them of their full CBC usage until the product change
     *
     * @return void
     */
    public function execute()
    {
        $this->gatherRequiredData();
    }

    /**
     * Gather the data required to perform this action
     *
     * @return void
     */
    protected function gatherRequiredData()
    {
        $this->_accountDetails = $this->getCoreService();
        $this->_oldServiceDefinitionId = AccountChange_Registry::instance()->getEntry('intOldServiceDefinitionId');
        $this->_newServiceDefinitionId = AccountChange_Registry::instance()->getEntry('intNewServiceDefinitionId');
        $this->_isAccountChangeScheduled = AccountChange_Registry::instance()->getEntry('bolSchedule');
    }

    /**
     * Returns a new Core_Service object for the Service ID of the action.
     *
     * @return Core_Service
     */
    protected function getCoreService()
    {
        return new Core_Service($this->intServiceId);
    }

    /**
     * Returns if account change is switching from a Greenbee/Waitrose product to a John Lewis product.
     *
     * @return boolean
     */
    protected function isCustomerSwitchingFromGbWrToJlp()
    {
        return (AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJlp(
            $this->_oldServiceDefinitionId,
            $this->_newServiceDefinitionId)
        );
    }
}
