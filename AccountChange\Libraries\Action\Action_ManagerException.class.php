<?php
/**
 * Account Change Action Manager Exception
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_ManagerException.class.php,v 1.2 2009-01-27 07:07:45 bselby Exp $
 * @since     File available since 2008-09-01
 */
/**
 * Account Change Action Manager exception class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Action_ManagerException extends Exception
{
    /**
     * Invalid action name was sent
     *
     */
    const ERR_INVALID_ACTION_NAME   = 1;

    /**
     * Invalid action object was created
     *
     */
    const ERR_INVALID_ACTION_OBJECT = 2;

    /**
     * The hardware action was not correctly initialised
     *
     */
    const ERR_INVALID_HARDWARE_INITIALISATION = 3;

    /**
     * Service Id not in a valid format (basically int)
     *
     */
    const ERR_INVALID_SERVICE_ID_TYPE         = 4;
}
