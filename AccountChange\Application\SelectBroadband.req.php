<?php

use Plusnet\BTSportApp\Services\ServiceManager;
use Plusnet\Feature\FeatureToggleManager;

require_once(__DIR__ . '/../Libraries/C2mPromotionsHelper.php');
require_once(__DIR__ . '/../Libraries/ServiceDefinitionProductOfferingNameMapper.php');
require_once(__DIR__ . '/../Libraries/C2mSalesChannels.class.php');
require_once(__DIR__ . '/../Libraries/SalesJourneyViewHelper.class.php');
require_once(__DIR__ . '/../Libraries/EcommerceAdapter.class.php');
require_once(__DIR__ . '/../Libraries/BizProductsHelper.class.php');

/**
 * Select Broadband Requirement
 *
 * Collecting the data for the broadband selection requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: SelectBroadband.req.php,v 1.3.2.11 2009/07/14 10:56:38 mstarbuck Exp $
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-09-29
 */

/**
 * AccountChange_SelectBroadband class
 *
 * @package   AccountChange
 * <AUTHOR> Marek <<EMAIL>>
 * <AUTHOR> Selby <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_SelectBroadband extends AccountChange_SelectBroadbandRequirement
{
    /**
     * Keys used multiple times to retrieve and add values from/to arrays
     *
     * @var string
     */
    const C2M_DISCOUNT_CONTRACT_LENGTH_KEY    = 'c2mDiscountRequiredContractLength';
    const CAN_CHANGE_PHONE_PACKAGE            = 'canChangeHomePhonePackage';
    const CURRENT_BASE_PRICE_IN_CONTRACT_KEY  = 'currentBasePriceInContract';
    const CURRENT_BASE_PRICE_KEY              = 'currentBasePrice';
    const CURRENT_BASE_PRICE_NO_DISCOUNT_KEY  = 'currentBasePriceNoDiscount';
    const DEC_VALUE_KEY                       = 'decValue';
    const INT_DISCOUNT_LENGTH_KEY             = 'intDiscountLength';
    const IS_DUAL_KEY                         = 'isDual';
    const LINE_RENTAL_COST_KEY                = 'intLineRentCost';
    const LINE_RENTAL_DISCOUNT_VALUE_KEY      = 'lineRentalDiscountValue';
    const LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY = 'lineRentalPromoDiscountType';
    const LINE_RENT_COST_KEY                  = 'intLineRentCost';
    const PRESET_DISCOUNT_KEY                 = 'presetDiscount';
    const PRICE_WITHOUT_LINE_RENTAL_KEY       = 'priceWithoutLineRental';
    const PRICE_WITH_LINE_RENTAL_KEY          = 'priceWithLineRental';
    const PRODUCT_NAME_KEY                    = 'strProductName';
    const SELECTED_BROADBAND_KEY              = 'arrSelectedBroadband';
    const SELECTED_CONTRACT_DURATION_KEY      = 'selectedContractDuration';
    const SERVICE_DEFINITION_ID_KEY           = 'intSdi';
    const WLR_CHANGE_ALLOWED                  = 'bolWlrChangeAllowed';
    const DISCOUNT_AMOUNT_BROADBAND_KEY       = 'discountAmountBroadband';
    const PROMOTION_KEY                       = 'promotion';
    const PRODUCTS_LIST                       = 'arrProducts';
    const CAMPAIGN_KEY                        = 'campaign';

    /**
     * Inputs for this requirement.
     *
     * @var array
     */
    protected $arrInputs = [
        'intNewSdi'                => 'external:Custom',
        'bolBtException'           => 'external:Bool',
        'bolOptOut'                => 'external:Custom:conditional',
        'strProvisionOn'           => 'external:Custom:conditional',
        'promoCode'                => 'external:Val_ReferralPromoCode->promoCode:optional',
        'selectedContractDuration' => 'external:custom:optional',
        'callerDisplay'            => 'external:custom:optional'
    ];

    /**
     * Order to call custom validators.
     *
     * @var array
     */
    protected $arrCustomValidatorOrder = ['intNewSdi', 'valOptOut'];

    /** @var AccountChange_ValidationCheck */
    protected $validator = null;

    /**
     * Move product with desired value of given property to front
     * of returned array
     *
     * @param array $arrSource Source products array
     * @param string $key By which key seeks for given value
     * @param mixed $value Desired value
     *
     * @return array
     */
    public function moveProductToFront(array $arrSource, $key, $value)
    {
        $arrResult = [];
        $toMove = null;

        if (!isset($arrSource) || !isset($key) || !isset($value)) {
            $arrResult = $arrSource;
        } else {
            foreach ($arrSource as $element) {
                if (array_key_exists(
                        $key,
                        $element
                    ) && $element[$key] == $value) {
                    $toMove = $element;
                } else {
                    array_push($arrResult, $element);
                }
            }

            if (isset($toMove)) {
                array_unshift($arrResult, $toMove);
            }
        }

        return $arrResult;
    }

    /**
     * Get an instance of AccountChange_Account for the current service
     *
     * @param integer $serviceId Service id
     *
     * @return AccountChange_Account
     */
    protected function getAccountInstance($serviceId)
    {
        return AccountChange_Account::instance(new Int($serviceId));
    }

    /**
     * Describe.
     *
     * Pull all the information needed for the view of this requirement
     *
     * @param array &$arrValidatedApplicationData Validated data
     *
     * @return array
     * @throws AccountChange_InvalidValidatorException
     * @throws Exception
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $arrReturn = [];
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $campaign = $arrValidatedApplicationData[self::CAMPAIGN_KEY];
        $intOldSdi = $this->getApplicationStateVariable('intOldSdi');
        $account = $this->getAccountInstance($objCoreService->getServiceId());
        $arrWlrDetails = $account->getWlrInformation();
        $isBusiness = $this->isBusiness();

        $objLineCheckResult = $this->getApplicationStateVariable(
            'objLineCheckResult'
        );
        $objAdslService = $this->getCoreADSLService(
            $objCoreService->getServiceId()
        );

        if ($this->hasPromotionDiscountLength($arrValidatedApplicationData)) {
            $intDiscountLength = $arrValidatedApplicationData[self::INT_DISCOUNT_LENGTH_KEY];
        } else {
            $intDiscountLength = $this->getApplicationStateVariable(
                self::INT_DISCOUNT_LENGTH_KEY
            );
        }

        $objLineCheckMarket = $this->getLineCheckMarket(
            $objLineCheckResult->getExchangeCode()
        );
        $intMarketId = $objLineCheckMarket->getMarketId();
        $productFamily = $this->getProductFamily($intOldSdi);

        $hasHomePhone = (isset($arrWlrDetails['bolWlrAddAllowed'])
            && ($arrWlrDetails['bolWlrAddAllowed'] == false)) ? true : false;
        $isCurrentProductFibre = $this->isOldProductFibre();

        // $promotion (i.e not promoCode) signifies a c2m promotion code.
        // Note getApplicationStateVariable returns false (not null) if it's not present
        $promotion = $this->getApplicationStateVariable(self::PROMOTION_KEY);
        $arrProductsAndError = $this->getBroadbandProducts(
            $objCoreService->getServiceId(),
            $intMarketId,
            $promotion,
            $campaign
        );

        if (isset($arrProductsAndError[self::PRODUCTS_LIST])) {
            // residential case (member centre)
            $arrProducts = $arrProductsAndError[self::PRODUCTS_LIST];
        } else {
            // business / john lewis case
            $arrProducts = $arrProductsAndError;
        }

        $this->setUpValidator($objCoreService->getServiceId());

        sort($arrProducts);
        $currentProduct = $arrProducts[array_search($intOldSdi, array_column($arrProducts, self::SERVICE_DEFINITION_ID_KEY))];
        $currentProduct['productFamily'] = $productFamily;
        $currentProduct['hasPhone'] = $hasHomePhone;
        $currentProduct['wlr'] = $arrValidatedApplicationData['arrWlrProduct'];
        $currentProduct[self::IS_DUAL_KEY] = $currentProduct['wlr'][self::PRODUCT_NAME_KEY]!=='';
        $currentProduct['intTariffID'] = $arrValidatedApplicationData['intBroadbandTariff'];
        $currentProduct[self::PRICE_WITH_LINE_RENTAL_KEY] = '&pound;'.$this->addLineRentalBasePrice(
            $currentProduct,
            $this->getBroadbandBasePriceNoDiscount($currentProduct)
        );

        $arrReturn[self::WLR_CHANGE_ALLOWED] = $this->checkIfAllowedToMakeChange();

        $arrReturn['makeChangeErrorMessage'] = $this->makeChangeErrorMessage();
        $arrReturn['makeChangeErrorCode'] = empty($this->makeChangeErrorCode()) ? $arrProductsAndError['errorCode'] : $this->makeChangeErrorCode();
        $arrReturn['currentProduct'] = $currentProduct;
        AccountChange_Registry::instance()->setEntry(
            'currentProduct',
            $currentProduct
        );
        $milestoneDatesConfigPath = '/local/codebase2005/modules/Framework/milestoneDates.json';
        if (file_exists($milestoneDatesConfigPath))
        {
            $pricingDates = json_decode(file_get_contents($milestoneDatesConfigPath), true);
            $drop2DateTime = new DateTime($pricingDates['residential.pricing.drop2']);
            $drop2PricingDate = $drop2DateTime->format('j<\s\up>S</\s\up> F Y');
        }
        $arrReturn['resPricingDrop2Date'] = $drop2PricingDate;
        $arrReturn['showResPricing'] = true;

        $arrProducts = AccountChange_Product_BroadbandProductFilter::filter(
            $objLineCheckResult,
            $arrProducts,
            $currentProduct,
            AccountChange_Product_BroadbandProductFilter::FILTER_TYPE_PORTAL,
            $this->getApplicationStateVariable('campaignCode')
        );

        // There are multiple flags indicating a line check failure. Consider all of them.
        $lineCheckDown = false;
        if ($this->getApplicationStateVariable('bolBtException') === true
            || $arrValidatedApplicationData['bolCampaignLineCheckFailed']) {
            $lineCheckDown = true;
        }

        if ($lineCheckDown) {
            $arrProducts = AccountChange_Product_BroadbandProductFilter::filter(
                $objLineCheckResult,
                $arrProducts,
                $currentProduct,
                AccountChange_Product_BroadbandProductFilter::FILTER_TYPE_KEEP_CURRENT_PRODUCT_ONLY
            );
        }

        // As there are both solus and dualplay variants of products we need to get both variants and
        // pass them to the template so that it can correctly display both variants as the current
        // broadband product
        $broadbandProductVariants = $this->getServiceDefinitionVariants(
            $intOldSdi
        );

        // If the product is not solus/dualplay then the above function will not return any ids so
        // we also add the current sdi as a variant to make sure it is displayed as the current product
        $broadbandProductVariants['current'] = $intOldSdi;

        $arrReturn['broadbandProductVariants'] = $broadbandProductVariants;

        // Sort the products into ascending price order
        $arrProducts = AccountChange_Product_BroadbandProductPriceSort::sort(
            $arrProducts
        );

        // And move the current product variants to the end of the list.
        $arrProducts = AccountChange_Product_BroadbandProductPriceSort::moveCurrentProductVariantsToEndOfList(
            $arrProducts,
            $broadbandProductVariants
        );

        if (!$hasHomePhone) {
            $arrReturn['bolBBPhoneChoice'] = true;
        }

        if ($arrValidatedApplicationData['productIsp'] == 'greenbee' ||
            $arrValidatedApplicationData['productIsp'] == 'waitrose' ||
            $arrValidatedApplicationData['productIsp'] == 'johnlewis'
        ) {
            $arrProducts = $this->moveProductToFront(
                $arrProducts,
                self::PRODUCT_NAME_KEY,
                'Unlimited'
            );
        }

        $arrDecoratedProducts = $this->decorateProducts(
            $arrProducts,
            $objCoreService->getServiceId()
        );

        $arrDecoratedProducts = $this->overrideProductContractLengthWithPromotion(
            $arrDecoratedProducts
        );

        $arrReturn['bolProvisioned'] = 0;
        $arrReturn['arrNewProducts'] = $arrDecoratedProducts;

        $arrReturn[self::INT_DISCOUNT_LENGTH_KEY] = $intDiscountLength;

        $arrReturn['objLinecheckResult'] = $objLineCheckResult;
        $arrReturn['strTelephoneNo'] = $objCoreService->getCliNumber();
        $bolProvisionedOnAdsl2 = $this->isProvisionedOnAdsl2(
            $objCoreService->getServiceId()
        );
        $arrReturn['strProvisionOn'] = $this->getProvisionedOn(
            $objCoreService->getServiceId(),
            $bolProvisionedOnAdsl2
        );

        if ($objAdslService->isProvisionedAsADSLOrMaxDSL()) {
            $arrReturn['bolProvisioned'] = 1;
        }

        $arrReturn['strAccountType'] = $objAdslService->getSupplierPlatform();
        $arrReturn['bolHaveHomePhone'] = $this->getApplicationStateVariable(
                'wlrStatus'
            ) == 'active';
        $arrReturn['oldProductFibre'] = $isCurrentProductFibre;
        $arrReturn['currentProductIsSolusDualplay'] = $productFamily->isDualPlay();

        // Collect the current product speed ranges
        $currentProductSpeedRanges = $this->getMinAndMaxSpeedRanges();
        $arrReturn['currentProductDownloadSpeedRangeMin'] = $currentProductSpeedRanges['downloadSpeedRangeMin'];
        $arrReturn['currentProductDownloadSpeedRangeMax'] = $currentProductSpeedRanges['downloadSpeedRangeMax'];

        $fixedPriceContractStatus = $this->getFpcStatusService();
        $arrReturn['isCurrentlyPriceProtected'] = $fixedPriceContractStatus->isServicePriceProtected(
            $objCoreService->getServiceId()
        );

        $objBusinessActor = $this->getCurrentBusinessActor();

        if ($this->isC2fToggleSet() &&
            !$isBusiness &&
            $objCoreService->getIsp() === 'plus.net' &&
            $objCoreService->getAdslDetails()['type'] == 'residential' &&
            $objBusinessActor->getUserType() != 'PLUSNET_STAFF') {
            $arrReturn = $this->setupCopperToFibreJourney(
                $arrReturn,
                $intOldSdi,
                $arrWlrDetails,
                $objCoreService,
                $account
            );
        }

        $arrReturn['displayCallerDisplay'] = $this->getApplicationStateVariable('bolChangeBroadbandOnly') &&
            !empty($arrWlrDetails[self::PRODUCT_NAME_KEY]) ? true : false;

        $arrReturn['filterBroadbandProducts'] = !$this->isBGLCallPlansToggleSet();
        return $arrReturn;
    }

    /**
     * @param $serviceId int Service Id
     *
     * @return void
     */
    protected function setUpValidator($serviceId)
    {
        $promoCode = $this->getSubmittedC2mPromotionCodeForProductChange();

        $additionalInformation = array();
        if ($promoCode !== false) {
            $additionalInformation['C2MPromotionCode'] = $promoCode;
        }

        $campaign = $this->getApplicationStateVariable(self::CAMPAIGN_KEY);
        $salesChannel = AccountChange_C2mSalesChannels::getC2MSalesChannel($campaign);
        $additionalInformation[AccountChange_PromotionCodeAccountCompatibilityPolicy::SALES_CHANNEL] = $salesChannel;

        $actor = Auth_BusinessActor::getActorByExternalUserId($serviceId);
        $this->validator = new AccountChange_ValidationCheck($actor, AccountChange_ValidationCheck::getDefaultPolicies(), false, false, $additionalInformation);
    }

    /**
     * @return bool
     * @throws AccountChange_InvalidValidatorException
     */
    protected function checkIfAllowedToMakeChange()
    {
        return $this->validator->isAccountChangeAllowed();
    }

    /**
     * @return string|bool
     */
    protected function makeChangeErrorMessage()
    {
        return $this->validator->getReasonForBlockage();
    }

    /**
     * @return string|bool
     */
    protected function makeChangeErrorCode()
    {
        return $this->validator->getErrorCode();
    }


    /**
     * Will return coreDB adaptor
     *
     * @return void
     */
    protected function getDbAdaptor()
    {
        return Db_Manager::getAdaptor('Core');
    }

    /**
     * Will get the service definition details, using the application state variable method of objCoreService
     *
     * @return array
     */
    protected function getServiceDefinitionDetails()
    {
        $objDatabase = $this->getDbAdaptor();
        return $objDatabase->getServiceDefinitionDetailsForService(
            $this->getApplicationStateVariable('objCoreService')->getServiceId()
        );
    }

    /**
     * Tells us whether we are dealing with a business account or not
     *
     * @return boolean
     */
    protected function isBusiness()
    {
        $serviceDefinitionDetails = $this->getServiceDefinitionDetails();
        return (Core_ServiceDefinition::BUSINESS_ACCOUNT == $serviceDefinitionDetails['type']);
    }

    /**
     * Tells us whether we are dealing with a John Lewis account or not
     *
     * @return boolean
     */
    protected function isJohnLewis()
    {
        $arrServiceDefinitionDetails = $this->getServiceDefinitionDetails();
        return (Core_ServiceDefinition::FAMILY_JOHNLEWIS == $arrServiceDefinitionDetails['type']);
    }


    /**
     * Vaidator for callerDisplay
     * We shouldn't be showing on John Lewis of business, so will always return false for these journeys
     *
     * @return array
     */
    public function valCallerDisplay($callerDisplay)
    {
        if ($this->isBusiness() || $this->isJohnLewis()){
            return array('callerDisplay' => false);
        }

        return array('callerDisplay' =>  !empty($callerDisplay) );
    }

    /**
     * Returns a Auth_BusinessActor object for current logged in user.
     *
     * @return Auth_BusinessActor
     */
    protected function getCurrentBusinessActor()
    {
        $objLogin = Auth_Auth::getCurrentLogin();

        if ($objLogin instanceof Auth_Login) {
            return $objLogin->getBusinessActor();
        }

        return false;
    }

    /**
     * Attach the line rental price to each Broadband product, checking if it is a dual play product.
     *
     * Line Rental added at market rate from C2M.
     *
     * @param $products
     * @param $arrWlrDetails
     *
     * @return array
     */
    private function attachLineRentalPriceToProducts($products, $arrWlrDetails)
    {
        $arrProducts = array();

        $lineRentalPrice = 0;
        if (empty($arrWlrDetails[self::LINE_RENTAL_COST_KEY])) {
            $lineRentalPrice = $this->getLineRentalPriceFromC2mApi();
        } else {
            $lineRentalPrice = $arrWlrDetails[self::LINE_RENTAL_COST_KEY]->toDecimal();
        }

        $isLRS = empty($arrWlrDetails[self::LINE_RENTAL_COST_KEY]);

        foreach ($products as $product) {
            if ($product[self::IS_DUAL_KEY] && !$isLRS) {
                $product['wlr'][self::LINE_RENTAL_COST_KEY] = AccountChange_Controller::CreateCurrencyObject($lineRentalPrice);
            } else {
                $product['wlr'][self::LINE_RENTAL_COST_KEY] = AccountChange_Controller::CreateCurrencyObject(0);
            }

            $arrProducts[] = $product;
        }

        return $arrProducts;
    }

    /**
     * Attach the price without line rental to each Broadband product.
     *
     * @param $products
     * @return array
     */
    private function attachPriceWithoutLineRentalToProducts($products)
    {
        $settingSingleProduct = isset($products['name']);

        if (!$settingSingleProduct) {
            return array_map([$this, 'getBroadbandPriceWithoutLineRental'], $products);
        }

        return $this->getBroadbandPriceWithoutLineRental($products);
    }

    /**
     *
     * @return bool
     */
    protected function isC2fToggleSet()
    {
        return AccountChange_C2mSalesChannels::isC2fToggleSet();
    }

    /**
     * @return bool
     */
    protected function isBGLCallPlansToggleSet()
    {
        return FeatureToggleManager::isOn(FeatureToggleManager::BGL_CALL_PLANS_GO_LIVE);
    }

    /**
     * Uses ProductChangePlanClient to get the solus version of a dual play
     * product or vice versa.
     *
     * @param integer $sdi Service definition id
     *
     * @return array
     */
    protected function getServiceDefinitionVariants($sdi)
    {
        $client = \BusTier_BusTier::getClient('productChangePlan');

        return $client->getServiceDefinitionVariants($sdi);
    }

    /**
     * Decorate the products with 21cn information.
     *
     * @param array $products The products
     * @param integer $serviceId The Service Id
     *
     * @return array
     */
    public function decorateProducts(array $products, $serviceId)
    {
        $return = [];
        $bolProvisionedOnAdsl2 = $this->isProvisionedOnAdsl2($serviceId);
        $lineCheckResult = $this->getApplicationStateVariable(
            'objLineCheckResult'
        );
        $rules = AccountChange_ProductRules::instance();

        $isBusiness = $this->isBusiness();

        foreach ($products as $product) {
            $strProvisionedOn = $this->getProvisionedOn(
                $serviceId,
                $bolProvisionedOnAdsl2
            );

            if ($strProvisionedOn === 'Adsl') {
                $arrProductProvDetails = $rules->getProductProvisionForService(
                    $product[self::SERVICE_DEFINITION_ID_KEY],
                    $lineCheckResult
                );
                $product['bolWbcProduct'] = $arrProductProvDetails['bolWbcProduct'];
                $product['strProvisionOn'] = $arrProductProvDetails['strProvisionOn'];

            } else {
                $product['strProvisionOn'] = $strProvisionedOn;
                $product['bolWbcProduct'] = true;
            }

            /**
             * DBIZ-209
             * Business still has a few capped products. We need to identify these
             * in the template so we can clearly state to the customer that they're capped.
             */
            if ($isBusiness) {
                $product['isBizADSLCapped'] =
                    AccountChange_BizProductsHelper::isCappedADSLProduct($product[self::SERVICE_DEFINITION_ID_KEY]);
                $product['isBizFibreCapped'] =
                    AccountChange_BizProductsHelper::isCappedFibreProduct($product[self::SERVICE_DEFINITION_ID_KEY]);
            }

            $return[(int)$product[self::SERVICE_DEFINITION_ID_KEY]] = $product;
        }

        return $return;
    }


    /**
     * selectedContractDuration is usually set via POST in workplace
     * We want to re-purpose it to allow contract lengths to be
     * set via a promotion.
     *
     * So we turn off the valSelectedContractDuration
     * function (it sets the POST value to zero) and manually
     * pass the intDiscountLength in instead to the final wizard complete()
     * method.
     *
     * @return array
     */
    public function getDataForComplete()
    {
        $arrData = parent::getDataForComplete();

        if (
            isset($arrData[self::SELECTED_BROADBAND_KEY][self::PRESET_DISCOUNT_KEY][self::C2M_DISCOUNT_CONTRACT_LENGTH_KEY]) &&
            $arrData[self::SELECTED_BROADBAND_KEY][self::PRESET_DISCOUNT_KEY][self::C2M_DISCOUNT_CONTRACT_LENGTH_KEY] !== null
        ) {
            $arrData[self::SELECTED_CONTRACT_DURATION_KEY] = $arrData[self::SELECTED_BROADBAND_KEY][self::PRESET_DISCOUNT_KEY][self::C2M_DISCOUNT_CONTRACT_LENGTH_KEY];
        } else {

            $arrData[self::SELECTED_CONTRACT_DURATION_KEY] = $arrData[self::INT_DISCOUNT_LENGTH_KEY];
        }

        return $arrData;
    }

    public function valToken($strToken)
    {
        $passedCSRF = Security_CSRF::getInstance()->checkToken($strToken);
        if (!$passedCSRF){
            throw new Exception("I'm sorry, we can't proceed with your request.");
        }
        return ['strToken'=> $strToken];
    }

    /**
     * Validation for the new service definition id.
     *
     * @param integer $intNewSdi The service definition id to validate
     *
     * @return array
     * @throws NumberFormatException
     * @throws WrongTypeException
     */
    public function valNewSdi($intNewSdi)
    {
        $arrValidatedReturn = [];

        if (empty($intNewSdi)) {
            $this->addValidationError('intNewSdi', 'MISSING');
            $arrValidatedReturn['intNewSdi'] = '';

            return $arrValidatedReturn;
        }

        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $objLineCheckResult = $this->getApplicationStateVariable(
            'objLineCheckResult'
        );
        $objLineCheckMarket = $this->getLineCheckMarket(
            $objLineCheckResult->getExchangeCode()
        );

        $arrValidatedReturn = $this->createNewSdi(
            $intNewSdi,
            $objCoreService,
            $objLineCheckResult,
            $objLineCheckMarket
        );

        return $arrValidatedReturn;
    }

    /**
     * @param $arrSdiDetails
     *
     * @return string
     */
    private function getTariffID($arrSdiDetails)
    {
        $isValidtariffID = isset($arrSdiDetails[1]) && is_numeric(
                $arrSdiDetails[1]
            );

        return $isValidtariffID ? $arrSdiDetails[1] : '';
    }

    /**
     * @param $intNewSdi
     * @param $objCoreService
     * @param $objLineCheckMarket
     *
     * @return array
     */
    private function getSelectedProductDetails(
        $intNewSdi,
        $objCoreService,
        $objLineCheckMarket,
        $campaign = 'NoCampaign'
    )
    {
        $arrValidSdis = [];
        $arrProductDetails = [];
        $intMarketId = $objLineCheckMarket->getMarketId();
        $promoCode = $this->getSubmittedPromoCodeForProductChange($intNewSdi);
        $arrProducts = $this->getBroadbandProducts(
            $objCoreService->getServiceId(),
            $intMarketId,
            $promoCode,
            $campaign
        );

        if (isset($arrProducts[self::PRODUCTS_LIST])) {
            // residential case (member centre)
            $arrBroadbandProducts = $arrProducts[self::PRODUCTS_LIST];
        } else {
            // business / john lewis case
            $arrBroadbandProducts = $arrProducts;
        }

        //set return data promo validation error code
        foreach ($arrBroadbandProducts as $arrProduct) {
            $arrValidSdis[] = $arrProduct[self::SERVICE_DEFINITION_ID_KEY];
            // If we don't have a contract set in the product, then default it to monthly.
            $arrProduct['strContract'] = (!isset($arrProduct['strContract'])) ? "MONTHLY" : $arrProduct['strContract'];
            $arrProductDetails[$arrProduct[self::SERVICE_DEFINITION_ID_KEY]][$arrProduct['intTariffID']] = $arrProduct;
        }

        return [$arrValidSdis, $arrProductDetails];
    }


    protected function getWlrDetailsFromCoreService()
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $account = $this->getAccountInstance($objCoreService->getServiceId());
        return $account->getWlrInformation();
    }

    /**
     * @param $intNewSdi
     * @param $intOldSdi
     * @param $intTariffId
     * @param $arrProductDetails
     *
     * @return mixed
     */
    private function populateValidatedProduct(
        $intNewSdi,
        $intOldSdi,
        $intTariffId,
        $arrProductDetails
    )
    {
        $arrValidatedReturn['intNewSdi'] = $intNewSdi;

        $currentBasePriceBroadband = 0;
        $lineRentalDiscountValue = 0;
        $lineRentalPromoDiscountType = null;

        $arrWlrDetails = $this->getWlrDetailsFromCoreService();

        if (is_numeric($intNewSdi) && is_numeric($intOldSdi)
            && ((int)$intNewSdi === (int)$intOldSdi)) {
            $intNewCost = $arrProductDetails[$intNewSdi][$intTariffId][self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY];
        } else {
            $intNewCost = $arrProductDetails[$intNewSdi][$intTariffId][self::CURRENT_BASE_PRICE_KEY];
            $currentBasePriceBroadband = $arrProductDetails[$intNewSdi][$intTariffId]['currentBasePriceBroadband'];
            $lineRentalPrice = $arrWlrDetails[self::LINE_RENT_COST_KEY];

            if (empty($lineRentalPrice)) {
                if ($this->isBusiness()) {
                    $lineRentalPrice = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $this->getBusinessLineRentalPriceFromC2mApi());
                } else {
                    $lineRentalPrice = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $this->getLineRentalPriceFromC2mApi());
                }
            }

            if (isset($arrProductDetails[$intNewSdi][$intTariffId][self::LINE_RENTAL_DISCOUNT_VALUE_KEY]) &&
                $arrProductDetails[$intNewSdi][$intTariffId][self::LINE_RENTAL_DISCOUNT_VALUE_KEY] > 0
            ) {
                $lineRentalPromoDiscountType = $arrProductDetails[$intNewSdi][$intTariffId][self::LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY];

                $lineRentalDiscountValue = AccountChange_DiscountHelper::calculateDiscountAmount(
                    $lineRentalPrice,
                    $lineRentalPromoDiscountType,
                    $arrProductDetails[$intNewSdi][$intTariffId][self::LINE_RENTAL_DISCOUNT_VALUE_KEY]
                )->toDecimal();
            }
        }

        $arrValidatedReturn[self::SELECTED_BROADBAND_KEY] = [
            self::SERVICE_DEFINITION_ID_KEY  => $intNewSdi,
            'strNewProduct'                  => $arrProductDetails[$intNewSdi][$intTariffId][self::PRODUCT_NAME_KEY],
            'intNewCost'                     => $intNewCost,
            'currentBasePriceBroadband'      => $currentBasePriceBroadband,
            'lineRentalDiscountValue'        => $lineRentalDiscountValue,
            'lineRentalDiscountAmount'       => $lineRentalDiscountValue,
            'lineRentalPromoDiscountType'    => $lineRentalPromoDiscountType,
            'strContractHandle'              => $arrProductDetails[$intNewSdi][$intTariffId]['strContract'],
            'intSelectedTariffID'            => $arrProductDetails[$intNewSdi][$intTariffId]['intTariffID'],
            'provisioningProfile'            => $arrProductDetails[$intNewSdi][$intTariffId]['provisioningProfile'],
            'maxDownstreamSpeed'             => $arrProductDetails[$intNewSdi][$intTariffId]['maxDownloadSpeed'],
            'maxUpstreamSpeed'               => $arrProductDetails[$intNewSdi][$intTariffId]['maxUploadSpeed'],
            'downloadSpeedRangeMin'          => $arrProductDetails[$intNewSdi][$intTariffId]['downloadSpeedRangeMin'],
            'downloadSpeedRangeMax'          => $arrProductDetails[$intNewSdi][$intTariffId]['downloadSpeedRangeMax'],
            'uploadSpeedRangeMin'            => $arrProductDetails[$intNewSdi][$intTariffId]['uploadSpeedRangeMin'],
            'uploadSpeedRangeMax'            => $arrProductDetails[$intNewSdi][$intTariffId]['uploadSpeedRangeMax'],
            'intMGALSInMB'                   => $arrProductDetails[$intNewSdi][$intTariffId]['intMGALSInMB'],
            'intMGSInMB'                     => $arrProductDetails[$intNewSdi][$intTariffId]['intMGSInMB'],
            'uploadSpeedRangeMgsFormatted'   => $arrProductDetails[$intNewSdi][$intTariffId]['uploadSpeedRangeMgsFormatted'],
            'downloadSpeedRangeMgsFormatted' => $arrProductDetails[$intNewSdi][$intTariffId]['downloadSpeedRangeMgsFormatted'],
            'intImpactedMGALS'               => $arrProductDetails[$intNewSdi][$intTariffId]['intImpactedMGALS'],
            'strBroadbandType'               => $arrProductDetails[$intNewSdi][$intTariffId]['strBroadbandType'],
            'intContractLengthMonths'        => $arrProductDetails[$intNewSdi][$intTariffId]['intContractLengthMonths'],
            self::IS_DUAL_KEY                => $arrProductDetails[$intNewSdi][$intTariffId][self::IS_DUAL_KEY]
        ];

        if (!empty($arrProductDetails[$intNewSdi][$intTariffId][self::PRESET_DISCOUNT_KEY])) {

            $arrValidatedReturn[self::SELECTED_BROADBAND_KEY][self::PRESET_DISCOUNT_KEY] =
                $arrProductDetails[$intNewSdi][$intTariffId][self::PRESET_DISCOUNT_KEY];

            $arrValidatedReturn[self::SELECTED_BROADBAND_KEY]['discountAmount'] =
                $arrProductDetails[$intNewSdi][$intTariffId]['discountAmount'];

            if (isset($arrProductDetails[$intNewSdi][$intTariffId][static::DISCOUNT_AMOUNT_BROADBAND_KEY])) {
                $arrValidatedReturn[static::SELECTED_BROADBAND_KEY][static::DISCOUNT_AMOUNT_BROADBAND_KEY] =
                    $arrProductDetails[$intNewSdi][$intTariffId][static::DISCOUNT_AMOUNT_BROADBAND_KEY];
            }
        }

        return $arrValidatedReturn;
    }

    /**
     * Gets the active promoCode
     *
     * A promoCode could have manually been applied by a user in which case it
     * can be retrieved from the applicationState.
     *
     * Alternatively the a promoCode could have been applied by way of a
     * C2M offer in 'AccountChange_Controller::getProductsWithC2mDiscount()'
     * being active and automatically assigned to the item.
     *
     * @param string $sdi
     *
     * @return mixed|null
     */
    private function getSubmittedPromoCodeForProductChange($sdi)
    {
        $c2mPromoCode = AccountChange_Registry::instance()->getEntry(
            'c2mPromoCode'
        );
        if (!empty($c2mPromoCode)) {
            return $c2mPromoCode[$sdi];
        }
        //if not applied via c2m promotion then check for user supplied code.
        $promoCode = (true === $this->isApplicationStateVariable('promoCode')) ?
            $this->getApplicationStateVariable('promoCode') : null;

        return $promoCode;
    }


    /**
     * Gets the c2m promotion code if it's come from the user (as part of the url)
     * Note: c2m promo codes are submitted to the application using the 'promotion' parameter, whilst
     * legacy ones use 'promoCode', hence checking for 'promotion' here is looking at a c2m code.
     *
     * @return string
     **/
    private function getSubmittedC2mPromotionCodeForProductChange()
    {
        $promoCode = (true === $this->isApplicationStateVariable(self::PROMOTION_KEY)) ?
            $this->getApplicationStateVariable(self::PROMOTION_KEY) : null;

        if ($promoCode !== null) {
            return $promoCode;
        }
        return false;
    }

    /**
     * @param $intNewSdi
     * @param $objCoreService
     * @param $objLineCheckResult
     * @param $objLineCheckMarket
     *
     * @return array|mixed
     * @throws \NumberFormatException
     * @throws \WrongTypeException
     */
    private function createNewSdi(
        $intNewSdi,
        $objCoreService,
        $objLineCheckResult,
        $objLineCheckMarket
    ) {
        $arrValidatedReturn = [];
        $arrSdiDetails = explode("_", key($intNewSdi));
        $intNewSdi = $arrSdiDetails[0];
        $oldSdi = $this->getApplicationStateVariable('intOldSdi');
        $campaign = $this->getApplicationStateVariable(self::CAMPAIGN_KEY);
        $intTariffId = $this->getTariffID($arrSdiDetails);
        list($arrValidSdis, $arrProductDetails) = $this->getSelectedProductDetails(
            $intNewSdi,
            $objCoreService,
            $objLineCheckMarket,
            $campaign
        );

        $arrFinancialErrors = $this->getFinancialErrors(
            $objCoreService->getServiceId(),
            $intNewSdi,
            $oldSdi
        );

        if (!empty($arrFinancialErrors)) {
            $this->addValidationError(
                'intNewSdi',
                'FINANCIAL_ERROR',
                ["strError" => $arrFinancialErrors['error']]
            );

            return $arrValidatedReturn;
        }

        $isNewSdiInvalid = !is_numeric($intNewSdi) || !in_array(
                $intNewSdi,
                $arrValidSdis
            );
        if ($isNewSdiInvalid) {
            $this->addValidationError('intNewSdi', 'INVALID');

            return $arrValidatedReturn;
        }

        $arrValidatedReturn = $this->populateValidatedProduct(
            $intNewSdi,
            $oldSdi,
            $intTariffId,
            $arrProductDetails
        );

        $arrValidatedReturn = $this->setDiscountLength($arrValidatedReturn);

        if (AccountChange_Controller::isBundle($intNewSdi)) {
            $intNewSdi = (int)$intNewSdi;
            $arrValidatedReturn['intNewWlrId'] = AccountChange_Controller::getWlrIdBySdi(
                $intNewSdi
            );

            $arrValidatedReturn['arrSelectedWlr'] = [
                'strNewProduct' => AccountChange_Controller::getProductNameBySdi(
                    $intNewSdi
                ),
                'intNewCost'    => new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT, 0
                ),
            ];

            $arrValidatedReturn['bolSelectedBundle'] = true;
        } else {
            $arrValidatedReturn['bolSelectedBundle'] = false;
        }

        $rules = AccountChange_ProductRules::instance();
        $arrProductProvDetails = $rules->getProductProvisionForService(
            $intNewSdi,
            $objLineCheckResult
        );
        $arrValidatedReturn['bolWbcProduct'] = $arrProductProvDetails['bolWbcProduct'];
        $arrValidatedReturn[self::SELECTED_BROADBAND_KEY]['strProvisionOn']
            = $arrProductProvDetails['strProvisionOn'];

        // If there's compulsory hardware associated to new sdi,we need to submit an immediate hardware request
        $client = $this->getHardwareClient();
        $arrCompusloryHardware = $client->getCompulsoryHardwareForServiceDefinition(
            new Int($intNewSdi)
        );

        if (!empty($arrCompusloryHardware['strHandle'])) {
            $arrValidatedReturn['hardwareOption'] = $arrCompusloryHardware['strHandle'];
        }

        $arrValidatedReturn['selectedProductFamily'] = $this->getProductFamily(
            $intNewSdi
        );


        return $arrValidatedReturn;
    }

    private function setDiscountLength($arrValidatedReturn)
    {
        if (isset($arrValidatedReturn[self::SELECTED_BROADBAND_KEY][self::PRESET_DISCOUNT_KEY])
            && is_array($arrValidatedReturn[self::SELECTED_BROADBAND_KEY][self::PRESET_DISCOUNT_KEY])) {

            $presetDiscount = $arrValidatedReturn[self::SELECTED_BROADBAND_KEY][self::PRESET_DISCOUNT_KEY];
            if (array_key_exists(self::INT_DISCOUNT_LENGTH_KEY, $presetDiscount)) {

                $arrValidatedReturn[self::INT_DISCOUNT_LENGTH_KEY] = $presetDiscount[self::INT_DISCOUNT_LENGTH_KEY];
            }
        }

        return $arrValidatedReturn;
    }

    /**
     * Wrapper method to create a ProductFamily_ProductFamily instance based on
     * the service definition Id.
     *
     * @param integer $serviceDefinitionId Represents the product
     *
     * @return ProductFamily_ProductFamily
     */
    protected function getProductFamily($serviceDefinitionId)
    {
        $this->includeLegacyFiles();

        return \ProductFamily_Factory::getFamily($serviceDefinitionId);
    }


    /**
     * selectedContractDuration is a variable that comes from workplace
     * We want promotions to override contract lengths, so we are repurposing
     * this variable to also serve this functionality.
     *
     * We set it to zero here, as we don't, however, want a portal user to
     * set the variable with a POST request. Without this val function,
     * Framework doesn't allow us to pass the contract length that we take from
     * the promotion onto the final wizard complete() method.
     *
     * @param $selectedContractDuration
     *
     * @return array
     */
    public function valSelectedContractDuration($selectedContractDuration)
    {
        return [self::SELECTED_CONTRACT_DURATION_KEY => 0];
    }


    /**
     * Wrapper to get business tier hardware client.
     *
     * @return BusTier_Client
     */
    protected function getHardwareClient()
    {
        return BusTier_BusTier::getClient('hardware');
    }

    /**
     * Wrapper for legacy file inclusion.
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
        require_once '/local/data/mis/database/class_libraries/Customers/C_Core_ADSLService.php';
    }

    /**
     * Function to get the object of C_Core_ADSLService.
     *
     * @param integer $intServiceId Service id
     *
     * @return C_Core_ADSLService
     */
    protected function getCoreADSLService($intServiceId)
    {
        $this->includeLegacyFiles();

        return new C_Core_ADSLService(null, null, $intServiceId);
    }

    /**
     * Function to get the broadband products.
     *
     * @param integer $intServiceId Service id
     * @param integer $intMarketId Market id
     * @param null $promoCode Promotion code
     * @param String $campaign
     *
     * @return array
     */
    protected function getBroadbandProducts(
        $intServiceId,
        $intMarketId,
        $promoCode = null,
        $campaign = 'NoCampaign'
    ) {
        $lineCheckResult = $this->getApplicationStateVariable(
            'objLineCheckResult'
        );

        if ($promoCode === null) {

            $promoCode = (true === $this->isApplicationStateVariable('promoCode')) ?
                $this->getApplicationStateVariable('promoCode') : null;
        }

        return AccountChange_Controller::getBroadbandProducts(
            $intServiceId,
            $intMarketId,
            false,
            $lineCheckResult,
            $promoCode,
            false,
            false,
            AccountChange_Controller::SOURCE_MEMBER_CENTRE_KEY,
            $campaign
        );
    }

    /**
     * Function to get the line check market object.
     *
     * @param string $exchangeCode Exchange code
     *
     * @return LineCheck_Market
     */
    protected function getLineCheckMarket($exchangeCode)
    {
        return LineCheck_Market::getMarketFromExchange($exchangeCode);
    }

    /**
     * Unit test wrapper for AccountChange_Controller::getMinAndMaxSpeedRanges.
     *
     * @return array
     */
    public function getMinAndMaxSpeedRanges()
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $objLineCheckResult = $this->getApplicationStateVariable(
            'objLineCheckResult'
        );

        return AccountChange_Controller::getMinAndMaxSpeedRanges(
            $objCoreService,
            $objLineCheckResult
        );
    }

    /**
     * Get the discount required contract length from preset discount array
     *
     * @param array $presetDiscount Array of preset discount details
     *
     * @return int
     **/
    protected function getC2mDiscountRequiredContractLengthFromDiscount(array $presetDiscount)
    {
        if (isset($presetDiscount[self::C2M_DISCOUNT_CONTRACT_LENGTH_KEY]) && $presetDiscount[self::C2M_DISCOUNT_CONTRACT_LENGTH_KEY] !== null) {
            return $presetDiscount[self::C2M_DISCOUNT_CONTRACT_LENGTH_KEY];
        }
        return false;
    }

    /**
     * If we have a promotion, then the promotion's contract length should
     * override the product's default contract length
     *
     * @param array $arrProducts
     *
     * @return array
     * @throws Exception
     */
    public function overrideProductContractLengthWithPromotion(
        array $arrProducts
    ) {
        foreach ($arrProducts as &$product) {
            if (array_key_exists(self::PRESET_DISCOUNT_KEY, $product)
                && $this->presetDiscountHasContractLength(
                    $product[self::PRESET_DISCOUNT_KEY]
                )
            ) {

                $c2mDiscountContractLength = $this->getC2mDiscountRequiredContractLengthFromDiscount($product[self::PRESET_DISCOUNT_KEY]);

                if ($c2mDiscountContractLength !== false) {
                    $product['defaultContractLength'] = $c2mDiscountContractLength . "MONTH";
                    $product['intContractLengthMonths'] = $c2mDiscountContractLength;
                } else {
                    $product['defaultContractLength'] = $product[self::PRESET_DISCOUNT_KEY][self::INT_DISCOUNT_LENGTH_KEY] . "MONTH";
                    $product['intContractLengthMonths'] = $product[self::PRESET_DISCOUNT_KEY][self::INT_DISCOUNT_LENGTH_KEY];
                }
            } elseif (array_key_exists(self::PRESET_DISCOUNT_KEY, $product)
                && !$this->presetDiscountHasContractLength(
                    $product[self::PRESET_DISCOUNT_KEY]
                )) {
                throw new Exception(
                    "A preset discount exists, but it has no contract length. "
                );
            }
        }

        return $arrProducts;
    }

    /**
     * @param $presetDiscount
     *
     * @return bool
     */
    protected function presetDiscountHasContractLength(array $presetDiscount)
    {
        if ($this->getC2mDiscountRequiredContractLengthFromDiscount($presetDiscount) !== false) {
            return true;
        }

        return
            is_array($presetDiscount)
            && array_key_exists(self::INT_DISCOUNT_LENGTH_KEY, $presetDiscount)
            && !empty($presetDiscount[self::INT_DISCOUNT_LENGTH_KEY]);
    }

    /**
     * @param array $arrValidatedApplicationData
     *
     * @return bool
     */
    protected function hasPromotionDiscountLength(
        array &$arrValidatedApplicationData
    ) {
        return array_key_exists(
            self::INT_DISCOUNT_LENGTH_KEY,
            $arrValidatedApplicationData
        )
        && !empty($arrValidatedApplicationData[self::INT_DISCOUNT_LENGTH_KEY]);
    }

    /**
     * Get a sales journey view helper object
     *
     * @return AccountChange_SalesJourneyViewHelper
     */
    public function getSalesJourneyViewHelper()
    {
        return new AccountChange_SalesJourneyViewHelper();
    }

    /**
     * Adding the accounts Discount end date and
     * post discount product price to supplementaryDiscountData
     *
     * @param $newProduct
     * @param $objCoreService
     *
     * @return array
     * @throws Exception
     */
    public function addPromotionEndDateAndEndPrice(
        $newProduct,
        $objCoreService
    ) {
        if($newProduct[self::PRICE_WITH_LINE_RENTAL_KEY] && $newProduct[self::PRESET_DISCOUNT_KEY]) {

            $totalDiscount = 0;

            if (isset($newProduct[self::PRESET_DISCOUNT_KEY][self::DEC_VALUE_KEY]) && $newProduct[self::PRESET_DISCOUNT_KEY][self::DEC_VALUE_KEY] > 0) {
                $totalDiscount += $newProduct[self::PRESET_DISCOUNT_KEY][self::DEC_VALUE_KEY];
            }

            if (isset($newProduct[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY]) && $newProduct[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY] > 0) {

                $lineRentalPrice = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $newProduct[self::PRICE_WITH_LINE_RENTAL_KEY]->toDecimal() - $newProduct[self::PRICE_WITHOUT_LINE_RENTAL_KEY]->toDecimal()
                );

                $lineRentalDiscountValue = AccountChange_DiscountHelper::calculateDiscountAmount(
                    $lineRentalPrice,
                    $newProduct[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY],
                    $newProduct[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY]
                );
                $totalDiscount += $lineRentalDiscountValue->toDecimal();
            }

            $priceSansDiscount = $newProduct[self::PRICE_WITH_LINE_RENTAL_KEY]->toDecimal() + $totalDiscount;
            $intDiscountLength = $newProduct[self::PRESET_DISCOUNT_KEY][self::INT_DISCOUNT_LENGTH_KEY];
            $objNextInvoiceDate = $objCoreService->getNextInvoiceDate();
            $strNextInvoiceDate = $objNextInvoiceDate->toI18nStrHere('STANDARD');
            $startDiscountDate = DateTime::createFromFormat('d/m/Y', $strNextInvoiceDate);
            $endDiscountDate = $this->addMonth($startDiscountDate, $intDiscountLength)->format('F jS Y');
            $newProduct['supplementaryDiscountData']['priceSansDiscount'] = $priceSansDiscount;
            $newProduct['supplementaryDiscountData']['endDiscountDate'] = $endDiscountDate;

            return $newProduct;
        } else {
            return $newProduct;
        }
    }

    /**
     * Setup the variables needed by the new copper to fibre templates
     *
     * - Extract the current broadband product into it's own field.
     * - Call 'setBroadbandPriceInclusiveOfLineRental' which will create a
     *   field on the product that contains the price of product inclusive of
     *   line rental (not Line rental saver) and after any promo codes have been
     *   deducted.
     * - Set the completed views so the progress indicator template knows
     *   whether it is safe to link back to the previous pages.
     *
     * @param $arrReturn
     * @param $intOldSdi
     * @param $objCoreService
     * @param $account
     *
     *
     * @return mixed
     * @throws Exception
     */
    private function setupCopperToFibreJourney(
        $arrReturn,
        $intOldSdi,
        $arrWlrDetails,
        $objCoreService,
        $account
    ) {
        $salesJourneyViewHelper = $this->getSalesJourneyViewHelper();
        $serviceId = $objCoreService->getServiceId();

        $currentProductIsInNewProductsArray = isset($arrReturn['arrNewProducts'][intval($intOldSdi)]);
        if ($currentProductIsInNewProductsArray) {
            unset($arrReturn['arrNewProducts'][intval($intOldSdi)]);
        }

        $arrReturn['arrNewProducts'] = $this->attachLineRentalPriceToProducts(
            $arrReturn['arrNewProducts'],
            $arrWlrDetails);
        $arrReturn['arrNewProducts'] = $this->attachPriceWithoutLineRentalToProducts($arrReturn['arrNewProducts']);

        $arrReturn['arrNewProducts'] = $this->setBroadbandPriceInclusiveOfLineRental(
            $arrReturn['arrNewProducts'],
            $arrWlrDetails);

        foreach ($arrReturn['arrNewProducts'] as $key => $newProduct) {
            if(isset($newProduct[self::PRESET_DISCOUNT_KEY])) {
                $newProduct = $this->addPromotionEndDateAndEndPrice($newProduct, $objCoreService);
                $arrReturn['arrNewProducts'][$key] = $newProduct;
            }
            $arrReturn['arrNewProducts'][$key]['hasActiveLineRentalSaver'] = AccountChange_Manager::hasActiveLineRentalSaver($objCoreService->getServiceId());
        }

        $_SESSION[self::CAN_CHANGE_PHONE_PACKAGE] = isset($arrWlrDetails[self::WLR_CHANGE_ALLOWED]) && $arrWlrDetails[self::WLR_CHANGE_ALLOWED];
        $arrReturn['wizardInstanceID'] = $salesJourneyViewHelper::getWizardInstanceID(
            'AccountChange'
        );
        $arrReturn['currentProduct'][self::CAN_CHANGE_PHONE_PACKAGE] = $_SESSION[self::CAN_CHANGE_PHONE_PACKAGE];
        $arrReturn['currentProduct']['hasActiveLineRentalSaver'] = AccountChange_Manager::hasActiveLineRentalSaver($objCoreService->getServiceId());
        $salesJourneyViewHelper::setCompletedViews([]);
        $arrReturn['completedViews'] = $salesJourneyViewHelper::getCompletedViews();
        $arrReturn['contractDetails'] = $account->getContractDetails($serviceId);

        return $arrReturn;
    }

    /**
     * Identify if the current product is SOLUS and if not add line rental charge to a single or multiple products.
     *
     * @param array $products
     * @param array $arrWlrDetails
     *
     * @return array
     */
    private function setBroadbandPriceInclusiveOfLineRental($products, $arrWlrDetails)
    {
        $settingSingleProduct = isset($products['name']);

        if (!$settingSingleProduct) {
            return array_map(function($product) use ($arrWlrDetails) {
                return $this->addLineRentalToBroadbandPrice($product, $arrWlrDetails);
            }, $products);
        }

        return $this->addLineRentalToBroadbandPrice($products, $arrWlrDetails);
    }

    /**
     * Get the price of a Broadband product without line rental.
     *
     * @param array $product
     *
     * @return array mixed
     */
    private function getBroadbandPriceWithoutLineRental($product)
    {
        $broadbandPrice = $product[self::CURRENT_BASE_PRICE_KEY]->toDecimal();
        $product['priceWithoutLineRental'] = AccountChange_Controller::createCurrencyObject($broadbandPrice);

        return $product;
    }

    /**
     * Add the current line rental price to the base broadband product cost.
     *
     * @param array $product
     * @param array $arrWlrDetails
     *
     * @return array
     */
    private function addLineRentalToBroadbandPrice($product, $arrWlrDetails)
    {
        $lineRentalPrice = 0;
        if (isset($arrWlrDetails[self::LINE_RENTAL_COST_KEY])
            && $arrWlrDetails[self::LINE_RENTAL_COST_KEY] instanceof I18n_Currency) {

            $lineRentalPrice = $arrWlrDetails[self::LINE_RENTAL_COST_KEY]->toDecimal();
        } else {
            $lineRentalPrice = $this->getLineRentalPriceFromC2mApi();
        }

        $broadbandPrice = $product[self::CURRENT_BASE_PRICE_KEY]->toDecimal();

        // If there's a line rental discount, take it off the line rental price..
        if (isset($product[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY]) && $product[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY] > 0) {

            $lineRentalDiscountValue = AccountChange_DiscountHelper::calculateDiscountAmount(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $lineRentalPrice),
                $product[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY],
                $product[self::PRESET_DISCOUNT_KEY][self::LINE_RENTAL_DISCOUNT_VALUE_KEY]
            );
            $lineRentalPrice -= $lineRentalDiscountValue->toDecimal();
        }

        $priceWithLineRental = $this->addLineRentalBasePrice($product, $broadbandPrice, $lineRentalPrice);
        $product[self::PRICE_WITHOUT_LINE_RENTAL_KEY] = AccountChange_Controller::createCurrencyObject($broadbandPrice);
        $product[self::PRICE_WITH_LINE_RENTAL_KEY] = AccountChange_Controller::createCurrencyObject($priceWithLineRental);

        return $product;
    }

    /**
     * Add the line rental base price that was retrieved from Billing to the broadband price.
     * Get the broadband and line rental prices from the product or override by passing in prices.
     *
     * @param      $product         array           The product to get the broadband base price from
     * @param null $broadbandPrice  I18n_Currency   The broadband base price to add the line rental base price to
     * @param null $lineRentalPrice I18n_Currency   The line rental base price to add to the broadband base price to
     *
     * @return int The total price
     */
    private function addLineRentalBasePrice($product, $broadbandPrice = null, $lineRentalPrice = null)
    {
        if (is_null($broadbandPrice)) {
            $broadbandPrice = $this->getBroadbandBasePrice($product);
        }

        if (is_null($lineRentalPrice)) {
            $lineRentalPrice = $this->getLineRentalBasePrice($product);
        }

        $priceWithLineRental = $broadbandPrice + $lineRentalPrice;
        return $priceWithLineRental;
    }

    /**
     * Get the broadband base price from the supplied product
     *
     * @param $product array The product to get the broadband base price from
     *
     * @return int The broadband base price
     */
    private function getBroadbandBasePrice($product)
    {
        $broadbandBasePrice = 0;
        if (isset($product[self::CURRENT_BASE_PRICE_KEY]) && $product[self::CURRENT_BASE_PRICE_KEY] instanceof I18n_Currency) {
            $broadbandBasePrice = $product[self::CURRENT_BASE_PRICE_KEY]->toDecimal();
        }

        return $broadbandBasePrice;
    }

    /**
     * Get the broadband base price with no discount applied from the supplied product
     *
     * @param $product array The product to get the broadband base price from
     *
     * @return int The broadband base price with no discount applied
     */
    private function getBroadbandBasePriceNoDiscount($product)
    {
        $broadbandBasePriceNoDiscount = 0;
        if (isset($product[self::CURRENT_BASE_PRICE_NO_DISCOUNT_KEY]) && $product[self::CURRENT_BASE_PRICE_NO_DISCOUNT_KEY] instanceof I18n_Currency) {
            $broadbandBasePriceNoDiscount = $product[self::CURRENT_BASE_PRICE_NO_DISCOUNT_KEY]->toDecimal();
        }

        return $broadbandBasePriceNoDiscount;
    }

    /**
     * Get the line rental price from the supplied product
     *
     * @param $product array The product to get the line rental price from
     *
     * @return int The line rental price
     */
    private function getLineRentalBasePrice($product)
    {
        $lineRentalPrice = 0;
        if (isset($product['wlr'])
            && isset($product['wlr'][self::LINE_RENTAL_COST_KEY])
            && $product['wlr'][self::LINE_RENTAL_COST_KEY] instanceof I18n_Currency) {

            $lineRentalPrice = $product['wlr'][self::LINE_RENTAL_COST_KEY]->toDecimal();
        } else {
            $lineRentalPrice = $this->getLineRentalPriceFromC2mApi();
        }

        return $lineRentalPrice;
    }

    /**
     * Get line rental price from C2M
     *
     * @return string
     */
    private function getLineRentalPriceFromC2mApi () {
        $c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
        return $c2mClient->getPrice('LineRental')['amount'];
    }

    /**
     * Get business line rental price from C2M
     *
     * @return string
     */
    private function getBusinessLineRentalPriceFromC2mApi () {
        $c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
        return $c2mClient->getPrice('BusinessLineRental')['amount'];
    }

    /**
     * @param DateTime $date
     * @param int $add
     *
     * @return DateTime
     * @throws Exception
     */
    private function addMonth($date, $add = 0)
    {
        $newDate = clone $date;
        $newDate->add(new DateInterval('P'.$add.'M'));
        return $newDate;
    }
}
