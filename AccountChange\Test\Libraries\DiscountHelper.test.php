<?php
/**
 * Tests for DiscountHelper
 * 
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Tests for DiscountHelper
 * 
 * @package AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_DiscountHelper_Test extends PHPUnit_Framework_TestCase
{
 
    /**
     * Tests DiscountHelper correctly calculates discounts
     *
     * @dataProvider provideDataForTestCalculateDiscountedAmount
     * @covers AccountChange_DiscountHelper
     * 
     * @param  float $startingAmount [description]
     * @param  float $discountAmount [description]
     * @param  int $discountType   1 = Fixed discount, 2 = percentage discount
     * @param  float $expected       Result we expect
     * 
     * @return void
     */
    public function testCalculateDiscountAmount(
        I18n_Currency $startingAmount,
        $discountType,
        $discountAmount,
        I18n_Currency $expected,
        $causesException
    ) {

        if ($causesException) {
            $this->setExpectedException('AccountChange_DiscountHelperException');
        }

        $actual = AccountChange_DiscountHelper::calculateDiscountAmount($startingAmount, $discountType, $discountAmount);

        $this->assertEquals($expected, $actual);

    }

    /**
     * Data provider for testCalculateDiscountAmount
     * 
     * @return mixed test parameters
     */
    public function provideDataForTestCalculateDiscountedAmount()
    {

        return array(
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 9.99),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 5.00),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 8.75),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 9.99),
                AccountChange_DiscountHelper::FIXED_DISCOUNT,
                2.50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 2.50),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                100,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                0,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0.0),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                120,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 120.00),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                AccountChange_DiscountHelper::FIXED_DISCOUNT,
                200,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 200.00),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                3,
                50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                true
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                25,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 25.00),
                false
            )
        );

    }


    /**
     * Tests that leading Price is calculated correctly
     *
     * @dataProvider provideDataForTestCalculateLeadingPrice
     * @covers AccountChange_DiscountHelper
     * 
     * @param  I18n_Currency $startingAmount  Amount to discount
     * @param  int           $discountType    Type of discount to apply (AccountChange_DiscountHelper::FIXED_DISCOUNT or PERCENTAGE_DISCOUNT)
     * @param  [type]        $discountAmount  Amount of discount to apply (either a fixed value or a percentage of $startingAmount)
     * @param  I18n_Currency $expected        Expected result
     * @param  boolean       $causesException True if the calculation is invalid (e.g $discountType is invalid)
     * 
     * @return void
     *
     */
    public function testCalculateLeadingPrice(I18n_Currency $startingAmount, $discountType, $discountAmount, I18n_Currency $expected, $causesException)
    {

        if ($causesException) {
            $this->setExpectedException('AccountChange_DiscountHelperException');
        }

        $actual = AccountChange_DiscountHelper::calculateLeadingPrice($startingAmount, $discountType, $discountAmount);

        $this->assertEquals($expected, $actual);

    }

    /**
     * Data provider for testCalculateLeadingPrice
     * 
     * @return mixed test parameters
     */
    public function provideDataForTestCalculateLeadingPrice()
    {

        return array(
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 9.99),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 4.99),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 8.74),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 9.99),
                AccountChange_DiscountHelper::FIXED_DISCOUNT,
                2.50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 7.49),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                100,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0.00),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                0,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 17.49),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                120,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, -20.00),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                AccountChange_DiscountHelper::FIXED_DISCOUNT,
                200,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, -100.00),
                false
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                3,
                50,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                true
            ),
            array(
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 100.00),
                AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                25,
                new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 75.00),
                false
            )
        );

    }
}
