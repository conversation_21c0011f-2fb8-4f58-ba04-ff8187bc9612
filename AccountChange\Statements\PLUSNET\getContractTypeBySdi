server: coredb
role: slave
rows: single
statement:
    SELECT CASE
      WHEN cd.bolFixedPrice = 1 THEN 'F'
      ELSE 'V'
      END AS ContractType
      FROM products.tblProductContractDefinition AS pcd
INNER JOIN products.tblContractDefinition AS cd
        ON pcd.intContractDefinitionID = cd.intContractDefinitionID
     WHERE pcd.intServiceDefinitionID = :serviceDefinitionId
       AND (cd.dteEffectiveTo IS NULL OR cd.dteEffectiveTo > NOW())
