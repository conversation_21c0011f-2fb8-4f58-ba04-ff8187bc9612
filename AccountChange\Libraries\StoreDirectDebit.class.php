<?php

/**
 * <AUTHOR>
 */
class AccountChange_StoreDirectDebit
{
    private $dbAdaptor;

    const DD_ACCT_NUM = 'intDirectDebitAccountNumber';
    const DD_NAME = 'strDirectDebitName';
    const DD_SORT_CODE_1 = 'intDirectDebitSortCode1';
    const DD_SORT_CODE_2 = 'intDirectDebitSortCode2';
    const DD_SORT_CODE_3 = 'intDirectDebitSortCode2';

    /**
     * @param Db_Adaptor $dbAdaptor the datasource
     */
    public function __construct(Db_Adaptor $dbAdaptor)
    {
        $this->dbAdaptor = $dbAdaptor;
    }

    /**
     * Executes the storing of Direct Debit details
     *
     * @param int    $serviceId   the account service ID
     * @param string $isp         the ISP of the customer
     * @param array  $directDebit the array containing Direct Debit details
     *
     * @return void
     */
    public function execute($serviceId, $isp, $directDebit)
    {
        if (!$this->validateDirectDebitDetails($directDebit)) {
            return;
        }

        Dbg_Dbg::write('Storing direct debit data', 'AccountChange.complete');
        Dbg_Dbg::write('ISP no longer used', "{$isp}");

        list($accountNumber, $sortCode, $name) = $this->getDirectDebitDetails($directDebit);

        $this->storeDirectDebitDetailsRbm($serviceId, $accountNumber, $sortCode, $name);
    }


    /**
     * Send request to store Direct debit details via the billing API
     *
     * @param int    $serviceId     the account service ID
     * @param string $accountNumber the bank account number
     * @param string $sortCode      the bank sort code
     * @param string $name          the account holder name
     *
     * @return void
     */
    private function storeDirectDebitDetailsRbm($serviceId, $accountNumber, $sortCode, $name)
    {
        $gimpDirectDebitData = array(
            'directDebitFirstName' => $name,
            'directDebitSurname' => '',
            'directDebitSortCode' => $sortCode,
            'directDebitAccountNumber' => $accountNumber
        );

        $helper = $this->getDirectDebitPaymentHelper();
        $helper->registerDirectDebitInBillingEngine($serviceId, $gimpDirectDebitData);
    }

    /**
     * Store direct debit details in the Legacy coredb
     *
     * @param int    $serviceId     the account service ID
     * @param int    $accountId     the account ID
     * @param string $isp           the ISP of the customer
     * @param string $accountNumber the bank account number
     * @param string $sortCode      the bank sort code
     * @param string $name          the account holder name
     *
     * @return void
     */
    protected function storeDirectDebitDetailsLegacy($serviceId, $accountId, $isp, $accountNumber, $sortCode, $name)
    {
        require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';

        direct_debit_instruction_add(
            $serviceId,
            $accountId,
            $sortCode,
            $accountNumber,
            $name,
            '',
            null,
            'inserted at account change',
            'no',
            true,
            $isp,
            Db_Manager::DEFAULT_TRANSACTION
        );
    }

    /**
     * Gets the Direct Debit values from the array
     *
     * @param array $directDebit the Direct Debit details
     *
     * @return array
     */
    private function getDirectDebitDetails($directDebit)
    {
        return [
            is_object( $directDebit[self::DD_ACCT_NUM]) ?
                (string) $directDebit[self::DD_ACCT_NUM] :
                $directDebit[self::DD_ACCT_NUM],
            $directDebit[self::DD_SORT_CODE_1] .
            $directDebit[self::DD_SORT_CODE_2] .
            $directDebit[self::DD_SORT_CODE_3],
            is_object($directDebit[self::DD_NAME]) ?
                (string) $directDebit[self::DD_NAME] :
                $directDebit[self::DD_NAME]
        ];
    }

    /**
     * Validates the Direct Debit details are set
     *
     * @param array $directDebit the Direct Debit details
     *
     * @return bool
     */
    private function validateDirectDebitDetails($directDebit)
    {
        return (
            !empty($directDebit[self::DD_ACCT_NUM]) &&
            !empty($directDebit[self::DD_SORT_CODE_1]) &&
            !empty($directDebit[self::DD_SORT_CODE_2]) &&
            !empty($directDebit[self::DD_SORT_CODE_3]) &&
            !empty($directDebit[self::DD_NAME])
        );
    }

    /**
     * Returns the Direct Debit payment helper
     *
     * @return GenericImmediatePaymentApplication_DirectDebitPaymentHelper
     */
    protected function getDirectDebitPaymentHelper()
    {
        return new GenericImmediatePaymentApplication_DirectDebitPaymentHelper();
    }
}
