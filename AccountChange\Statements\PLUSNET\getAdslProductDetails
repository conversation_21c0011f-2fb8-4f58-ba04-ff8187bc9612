server: coredb
role: slave
rows: single
statement:

SELECT
	sd.service_definition_id,
	sd.name,
	sd.isp,
	sd.minimum_charge,
	sd.initial_charge,
	sd.type,
	sd.password_visible_to_support,
	sd.requires,
	sd.date_created,
	sd.end_date,
	sd.signup_via_portal,
	sd.blurb,
    ap.intMaximumSpeed,
    ap.intMaxUploadSpeed
FROM products.service_definitions AS sd
INNER JOIN products.adsl_product AS ap
	ON sd.service_definition_id = ap.service_definition_id
WHERE sd.service_definition_id = :intSdi