<?php
include_once 'phing/Task.php';
class LoadableApps extends Task {

    private $_strFile;
    private $_strReturnName;
        
    public function setFile($str) {
        $this->_strFile = $str;
    }
    
    public function setReturnName($strName) {
        $this->_strReturnName = $strName;
    }    
    
    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {

		if (!$this->_strFile) {
		    throw new BuildException("You must specify the file attribute", $this->getLocation());
		}      
		
		if (!$this->_strReturnName) {
		    throw new BuildException('You must specify the returnName attribute', $this->getLocation());
		}     
		
		$strFrameworkDir = $this->project->getProperty('frameworkDir');
		
		if(!@include_once($strFrameworkDir.'/Libraries/bootstrap.inc.php')) {
			throw new BuildException('Missing ClassLoader', $this->getLocation());
		}		
		
		if(!@include($this->_strFile)) throw new BuildException('Site configuration file missing');
		
		$failed = false;
		//exit if any apps is not loadable
		foreach ($arrApplications as $strApp) {
			if(!class_exists("{$strApp}_Controller")) {
				$this->log("$strApp not loadable by framework autoloader");
				$failed = true;
			}
		}
		if(!$failed) $this->project->setProperty($this->_strReturnName, 'All application are loadable');
    }
}
