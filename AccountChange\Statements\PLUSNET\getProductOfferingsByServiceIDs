server: coredb
role: slave
rows: multiple
statement:

SELECT
  sd.service_definition_id as intSdi, c2mpo.vchProductOfferingName as productOfferingName
FROM
    products.service_definitions sd
INNER JOIN
    products.service_component_config scc
        ON scc.service_definition_id = sd.service_definition_id
INNER JOIN
    products.service_components sc
        ON sc.service_component_id = scc.service_component_id
INNER JOIN
    products.tblServiceComponentProduct scp
        ON scp.intServiceComponentID = sc.service_component_id
INNER JOIN
    products.tblServiceComponentProductType scpt
        ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
INNER JOIN
    products.tblC2mProductOfferings c2mpo
        ON sc.service_component_id = c2mpo.intServiceComponentID
WHERE
    scpt.vchHandle = 'INTERNET_CONNECTION'
    AND sd.service_definition_id in (:serviceDefinitionID)
GROUP BY
    scc.service_definition_id
ORDER BY
    c2mpo.intC2mProductOfferingID

