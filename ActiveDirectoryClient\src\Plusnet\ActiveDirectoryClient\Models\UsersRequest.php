<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\ActiveDirectoryClient\Models;

use Guzzle\Http\Client;
use Guzzle\Http\Message\RequestInterface;
use Guzzle\Http\Message\Response;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;
use Plusnet\ActiveDirectoryClient\Exceptions\ActiveDirectoryClientException;
use Plusnet\ActiveDirectoryClient\Factory\UserDetailsFactory;
use Plusnet\ActiveDirectoryClient\Helpers\LoggerHelper;

/**
 * Class UsersRequest
 *
 * @package Plusnet\ActiveDirectoryClient
 */
class UsersRequest
{
    /**
     * The AD service URL
     */
    const AD_SERVICE_URL = 'https://ldapapi.plus.net/v1/ldap/user/%s';
    /**
     * REGEX to check UserName is in a valid format.
     */
    const IS_USER_NAME_VALID_REGEX = '/^[a-z_\-\d]+$/i';
    /**
     * Log Context to track what class has generated an error to allow reporting.
     */
    const LOG_CONTEXT = 'ActiveDirectoryClient';

    /**
     * @var LoggerHelper
     */
    private $loggerHelper;
    /**
     * @var Client
     */
    private $client;
    /**
     * @var UserDetailsFactory
     */
    private $factory;

    /**
     * UsersRequest constructor.
     *
     * @param UserDetailsFactory|null $factory      Sets instance of UserDetailsFactory
     * @param Client|null             $client       Sets instance of Guzzle Client
     * @param LoggerHelper|null       $loggerHelper Sets instance of LoggerHelper
     */
    public function __construct(
        UserDetailsFactory $factory = null,
        Client $client = null,
        LoggerHelper $loggerHelper = null
    ) {
        $this->factory = $factory ?: new UserDetailsFactory();
        $this->client = $client ?: new Client();
        $this->loggerHelper = $loggerHelper ?: new LoggerHelper(LoggerHelper::SERVICES_LOG_FILE_PATH);
    }

    /**
     * Query active directory using the current agents username.
     * This will return a UserDetails object containing the related agents details.
     *
     * If an error occurs during this process the details will be logged and a UserDetails object will be returned with invalid details.
     * This can easily be tested for using:
     *
     * - UserDetails::hasValidEin
     * - UserDetails::hasValidIsPartner
     *
     * @param string $userName Agents Username
     *
     * @return UserDetails
     */
    public function requestUserDetails($userName)
    {
        try {
            $this->assertUsernameIsValid($userName);

            $userData = json_decode($this->makeRequest($userName), true);

            $this->assertJsonDecodedCorrectly($userData);
            $this->assertNoErrorsReturned($userData);

            return $this->factory->createFromArray($userData);
        } catch (\Exception $e) {
            $this->loggerHelper->error(
                sprintf(
                    "%s - Exception occurred while requesting user details: error='%s' username='%s'",
                    self::LOG_CONTEXT,
                    $e->getMessage(),
                    $userName
                )
            );

            return $this->factory->createInvalidUserDetails();
        }
    }

    /**
     * Assert that the returned Active Directory JSON has been decoded successfully.
     *
     * @param array $userDetailsArray Array from Microservice
     */
    private function assertJsonDecodedCorrectly($userDetailsArray)
    {
        if (null !== $userDetailsArray) {
            return;
        }

        throw new ActiveDirectoryClientException(
            sprintf(
                "Invalid JSON returned: '%s'",
                json_last_error_msg()
            )
        );
    }

    /**
     * Assert that no errors were returned.
     *
     * @param array $userDetailsArray Array of user details returned in response.
     */
    private function assertNoErrorsReturned($userDetailsArray)
    {
        if (! array_key_exists("message", $userDetailsArray)) {
            return;
        }

        $error = isset($userDetailsArray['error']) ? $userDetailsArray['error'] : '';
        $message = isset($userDetailsArray['message']) ? $userDetailsArray['message'] : '';

        throw new ActiveDirectoryClientException(
            "Error returned in response: $error - $message"
        );
    }

    /**
     * Assert the username is valid.
     *
     * Valid characters are alphabet, numerals and hyphen
     *
     * @param string $userName Workplace Username
     */
    private function assertUsernameIsValid($userName)
    {
        if (1 === preg_match(self::IS_USER_NAME_VALID_REGEX, $userName)) {
            return;
        }

        throw new ActiveDirectoryClientException(
            "Invalid username provided: '$userName'"
        );
    }

    /**
     * Call Active Directory client to retrieve the EIN and Partner status
     *
     * @param string $userName Workplace Username
     *
     * @return string
     *
     * @throws \Guzzle\Common\Exception\ExceptionCollection
     */
    private function makeRequest($userName)
    {
        if (PHP_MAJOR_VERSION < 8) {
            $request =  $this->guzzleRequest($userName);
        } else {
            $request = $this->guzzleRequestPhp8($userName);
        }

        $response = $this->client->send(array($request));

        if ($response instanceof Response) {
            return $response->getBody();
        }

        if (is_array($response) && 1 === count($response) && $response[0] instanceof Response) {
            return $response[0]->getBody();
        }

        $type = $this->getType($response);
        $count = is_array($response) ? count($response) : 'N/A';

        throw new ActiveDirectoryClientException(
            "Invalid response object returned with type=$type and count=$count"
        );
    }

    /**
     * Get type information for the subject.
     *
     * @param mixed $subject The subject you wish the get the type of.
     *
     * @return string
     */
    private function getType($subject)
    {
        if (is_object($subject)) {
            return get_class($subject);
        }

        if (is_array($subject) && 1 === count($subject)) {
            // This is so we get useful log info when the response
            // is an array and has the expected count of 1.
            $type = is_object($subject[0]) ? get_class($subject[0]) : gettype($subject[0]);

            return "array($type)";
        }

        return gettype($subject);
    }

    /**
     * Get the request data
     *
     * @param string    $userName Agents workplace username
     *
     * @return RequestInterface
     */
    private function guzzleRequest($userName)
    {
        return $this->client->get(
            sprintf(static::AD_SERVICE_URL, $userName)
        );
    }

    /**
     * Get the request data
     *
     * @param string    $userName Agents workplace username
     *
     * @return RequestInterface
     */
    private function guzzleRequestPhp8($userName)
    {
        return $this->client->request(
            sprintf(static::AD_SERVICE_URL, $userName)
        );
    }
}