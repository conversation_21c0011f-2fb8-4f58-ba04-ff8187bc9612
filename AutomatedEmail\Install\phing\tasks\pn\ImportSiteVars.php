<?php
include_once 'phing/Task.php';
class ImportSiteVars extends Task {

    private $_strFile;
    private $_strVars;
    
    public function setFile($str) {
        $this->_strFile = $str;
    }
    
    public function setVars($str) {
        $this->_strVars = $str;
    }

    /**
     * The init method: Do init steps.
     */
    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {

		if (!$this->_strFile) {
		    throw new BuildException("You must specify the file attribute", $this->getLocation());
		}       
		
		if(!@include($this->_strFile)) throw new BuildException('Site configuration file missing');

 		$arrVars = explode(',',$this->_strVars);
		foreach ($arrVars as $index) {
			if(!isset($$index)) throw new BuildException("Missing variable '$index' site.vars.php");
			
			$this->project->setProperty('site.'.$index, $$index);
		}
    }
}