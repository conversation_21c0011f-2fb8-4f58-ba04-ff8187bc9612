<?php
/**
 * Action Appointment
 *
 * Testing class for the AccountChange_Action_Appointment class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
/**
 * Action Appointment Test Class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
class AccountChange_Action_Appointment_Test extends PHPUnit_Framework_TestCase
{

    /**
     * Use a null log handler for testing
     *
     * @return void
     */
    public function setUp()
    {
        $logHandler = new Log_Logger();
        $logHandler->registerLogHandler(new Log_NullLogHandler());
        Log_AuditLog::registerLogger($logHandler);
    }

    /**
     * Reset the account change registry when we're done
     *
     * @return void
     */
    public function tearDown()
    {
        AccountChange_Registry::instance()->reset();
    }

    /**
     * @covers AccountChange_Action_Appointment::execute
     * @covers AccountChange_Action_Appointment::__construct
     *
     * @return void
     */
    public function testExecuteCallsBookAppointment()
    {
        AccountChange_Registry::instance()->setEntry('appointing' , array('appointment' => 'true'));
        $mock = $this->getMock('AccountChange_Action_Appointment', array('bookLiveAppointment'),array(1, array()));

        $mock->expects($this->once())
             ->method('bookLiveAppointment');

        $mock->execute();
    }

    /**
     * @covers AccountChange_Action_Appointment::execute
     *
     * @return void
     */
    public function testExecuteCallsStoreAppointment()
    {
        AccountChange_Registry::instance()->setEntry('appointing' , array('appointmentdate1' => 'true'));
        $mock = $this->getMock('AccountChange_Action_Appointment', array('storeAppointmentSlots'),array(1, array()));

        $mock->expects($this->once())
             ->method('storeAppointmentSlots');

        $mock->execute();
    }

    /**
     * @covers AccountChange_Action_Appointment::getEngineerAppointmentClient
     *
     * @return void
     */
    public function testGetEngineerAppointmentClientReturnsTheRightObject()
    {
        $serviceContainer = $this->getMock(
            'EngineerAppointmentClient_Service_Container',
            array('log'),
            array()
        );

        $mock = $this->getMock(
            'AccountChange_Action_Appointment',
            array(
                'storeAppointmentSlots',
                'getServiceContainer'
            ),
            array(
                1,
                array()
            )
        );

        $mock->expects($this->once())
            ->method('getServiceContainer')
            ->will($this->returnValue($serviceContainer));

        $service = EngineerAppointmentClient_Service::FTTC;
        $data = array();
        $result = $mock->getEngineerAppointmentClient($service, $data);

        $this->assertInstanceOf('EngineerAppointmentClient_Client', $result);
    }

    /**
     * @covers AccountChange_Action_Appointment::bookLiveAppointment
     * @dataProvider liveAppointmentDataProvider
     *
     * @param int   $serviceId
     * @param array $dataIn
     * @param array $expectedData
     *
     * @return void
     */
    public function testBookLiveAppointmentStoresAppointment($serviceId, $dataIn, $expectedData)
    {

        AccountChange_Registry::instance()->setEntry('appointing', $dataIn);
        $mock = $this->getMock(
            'AccountChange_Action_Appointment',
            array('getEngineerAppointmentClient'),
            array($serviceId, array())
        );
        $client = $this->getMockBuilder('EngineerAppointmentClient_Client')
            ->setMethods(array('bookAppointment'))
            ->disableOriginalConstructor()
            ->getMock();
        $client->expects($this->once())
            ->method('bookAppointment')
            ->with(new Int($serviceId));

        $mock->expects($this->once())
            ->method('getEngineerAppointmentClient')
            ->with($dataIn['appointingType']['service'], $expectedData)
            ->will($this->returnValue($client));


        $mock->bookLiveAppointment();
    }

    /**
     * @return array
     */
    public function liveAppointmentDataProvider()
    {
        return array(
            // Data set 0
            array(
                12345,
                array(
                    'appointingType' => array(
                        'service'       => EngineerAppointmentClient_Service::FTTC,
                        'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    ),
                    'addressRef'       => 'A12345678901:SS:GOLD',
                    'intPhoneNumber'   => '***********',
                    'appointment'      => '20/10/2011AM',
                    'extensionKitId'   => 2,
                    'engineeringNotes' => 'Beware of the Raptors'
                ),
                array(
                    'addressRef'          => 'A12345678901',
                    'cssDatabaseCode'     => 'SS',
                    'cli'                  => '***********',
                    'engineerAppointment' => '20/10/2011AM',
                    'extensionKitId'      => 2,
                    'engineerAppointmentConfirmed' => false,
                    'engineerNotes'       => 'Beware of the Raptors'
                ),
            ),
        );
    }

    /**
     * @covers AccountChange_Action_Appointment::bookLiveAppointment
     * @dataProvider liveAppointmentDataProvider
     *
     * @param int   $serviceId
     * @param array $dataIn
     * @param array $expectedData
     *
     * @return void
     */
    public function testBookLiveAppointmentCatchesExceptionAndRaisesTicket($serviceId, $dataIn, $expectedData)
    {

        AccountChange_Registry::instance()->setEntry('appointing', $dataIn);
        $mock = $this->getMock(
            'AccountChange_Action_Appointment',
            array('getEngineerAppointmentClient', 'raiseTicket'),
            array($serviceId, array())
        );
        $client = $this->getMockBuilder('EngineerAppointmentClient_Client')
            ->setMethods(array('bookAppointment'))
            ->disableOriginalConstructor()
            ->getMock();
        $client->expects($this->once())
               ->method('bookAppointment')
               ->with(new Int($serviceId))
               ->will($this->throwException(new Exception('Appointment Booking Failed')));

        $mock->expects($this->once())
             ->method('getEngineerAppointmentClient')
             ->with($dataIn['appointingType']['service'], $expectedData)
             ->will($this->returnValue($client));

        $mock->expects($this->once())
             ->method('raiseTicket')
             ->will($this->returnValue($client));

        $mock->bookLiveAppointment();
    }

    /**
     * @covers AccountChange_Action_Appointment::storeAppointmentSlots
     * @dataProvider appointmentDataProvider
     *
     * @param int   $serviceId
     * @param array $dataIn
     * @param array $expectedData
     *
     * @return void
     */
    public function testStoreAppointment($serviceId, $dataIn, $expectedData)
    {
        AccountChange_Registry::instance()->setEntry('appointing', $dataIn);

        $mock = $this->getMock(
            'AccountChange_Action_Appointment',
            array('getEngineerAppointmentClient', 'raiseTicket'),
            array($serviceId, array())
        );

        $client = $this->getMockBuilder('EngineerAppointmentClient_Client')
            ->setMethods(array('storeAppointment'))
            ->disableOriginalConstructor()
            ->getMock();

        $client->expects($this->once())
            ->method('storeAppointment')
            ->with(new Int($serviceId));

        $mock->expects($this->once())
             ->method('getEngineerAppointmentClient')
             ->with($dataIn['appointingType']['service'], $expectedData)
             ->will($this->returnValue($client));

        $mock->expects($this->once())
             ->method('raiseTicket')
             ->will($this->returnValue($client));

        $mock->storeAppointmentSlots();
    }

    /**
     * @return array
     */
    public function appointmentDataProvider()
    {
        return array(
            // Data set 0
            array(
                12345,
                array(
                    'appointingType' => array(
                        'service'       => EngineerAppointmentClient_Service::FTTC,
                        'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
                    ),
                    'intPhoneNumber'   => '***********',
                    'extensionKitId'   => 2,
                    'engineeringNotes' => 'Beware of the Raptors',
                    'appointmentdate1' => strtotime('2011-06-20'),
                    'appointmentdate2' => strtotime('2011-06-21'),
                    'appointmentdate3' => strtotime('2011-06-22'),
                    'appointmenttime1' => 'AM',
                    'appointmenttime2' => 'PM',
                    'appointmenttime3' => 'AM',
                ),
                array(
                    'cli'            => '***********',
                    'extensionKitId' => 2,
                    'engineerNotes'  => 'Beware of the Raptors',
                    'engineerAppointmentConfirmed' => false,
                    'appointments'   => array(
                        array(
                            'priority' => 1,
                            'date'     => '2011/06/20',
                            'slot'       => 'AM',
                        ),
                        array(
                            'priority' => 2,
                            'date'     => '2011/06/21',
                            'slot'       => 'PM',
                        ),
                        array(
                            'priority' => 3,
                            'date'     => '2011/06/22',
                            'slot'       => 'AM',
                        ),
                    ),
                ),
            ),
        );
    }
}
