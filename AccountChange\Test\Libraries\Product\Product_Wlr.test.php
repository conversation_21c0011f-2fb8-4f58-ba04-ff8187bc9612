<?php
/**
 * Wlr Product Configuration
 *
 * Testing class for the AccountChange_Product_ServiceComponent class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008-2012 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */

/**
 * Wlr Configuration Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008-2012 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Product_Wlr_Test extends PHPUnit_Framework_TestCase
{
    /**
     * PHPUnit function
     *
     * All the test needs to mock getServiceDefinitionDao
     *
     * @return void
     */
    protected function setup()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);
    }

    /**
     * PHPUnit tearDown
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::commit();
        Db_Manager::restoreAdaptor('AccountChange');
        Db_Manager::restoreAdaptor('Core');

        Mockery::close();
    }

    /**
     * Verify that matching product configuration is set when adding a new product
     *
     * @covers AccountChange_Product_Wlr::setMatchingProductConfiguration
     *
     * @return void
     * @throws AccountChange_AccountConfigurationException
     */
    public function testSetMatchingProductConfigurationWhenAddingNewProduct()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );

        // Create the Wlr products we want to test
        $objProduct1 = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_ADD
        );

        // Setup the Account configurations
        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition)
        );
        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct1)
        );

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }

    /**
     * Function to test 'setMatchingProductConfiguration' while removing the product
     *
     * @covers AccountChange_Product_Wlr::setMatchingProductConfiguration
     *
     * @return void
     * @throws AccountChange_AccountConfigurationException
     */
    public function testSetMatchingProductConfigurationWhenRemovingAProduct()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );

        // Create the Wlr products we want to test
        $objProduct1 = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_REMOVE
        );

        // Setup the Account configurations
        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct1)
        );
        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition)
        );

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }

    /**
     * Function to test 'setMatchingProductConfiguration' while changing the product
     *
     * @covers AccountChange_Product_Wlr::setMatchingProductConfiguration
     *
     * @return void
     * @throws AccountChange_AccountConfigurationException
     */
    public function testSetMatchingProductConfigurationWhenChangingAProduct()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );

        // Create the Wlr products we want to test
        $objProduct1 = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE
        );
        $objProduct2 = new AccountChange_Product_Wlr(
            2,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct1)
        );
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct2)
        );

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertEquals($objProduct2, $objProduct1->getNewProductConfiguration());
    }

    /**
     * Function to test 'setMatchingProductConfiguration' manually throw exception
     *
     * @covers AccountChange_Product_Wlr::setMatchingProductConfigurationManually
     *
     * @return void
     * @throws AccountChange_Product_Exception
     */
    public function testSetMatchingProductConfigurationManuallyThrowsExceptionIfTheConfigurationTypesAreDifferent()
    {
        $this->setExpectedException(
            'AccountChange_Product_Exception',
            'Matching product is not the same product type',
            AccountChange_Product_Exception::ERR_MATCHING_PRODUCT_NOT_VALID
        );

        $objProduct1 = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE
        );
        $objProduct2 = new AccountChange_Product_ServiceDefinition(
            2,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $objProduct1->setMatchingProductConfigurationManually($objProduct2);
    }

    /**
     * Function to test 'setMatchingProductConfiguration' manually
     *
     * @covers AccountChange_Product_Wlr::setMatchingProductConfigurationManually
     *
     * @return void
     */
    public function testSetMatchingProductConfigurationManuallyCallsSetAccountChangeIfProductIsCorrect()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_Wlr',
            array('setAccountChange'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct->expects($this->once())
            ->method('setAccountChange');

        $objProductConfiguration = new AccountChange_Product_Wlr(
            515,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $objProduct->setMatchingProductConfigurationManually($objProductConfiguration);
    }

    /**
     * Testing initialise function
     *
     * @covers AccountChange_Product_Wlr::initialise
     * @covers AccountChange_Product_ServiceComponent::initialise
     *
     * @return void
     */
    public function testInitialiseSetsUpTheObjectCorrectly()
    {
        $strCliNumber = '01111 111111';
        $bolScheduleChange = true;
        $bolEssentialProduct = true;
        $bolTakePayment = true;
        $uxtScheduledDate = **********;
        $manuallyAddPhone = true;
        $oldSdi = 1;
        $newSdi = 2;
        $oldWlrScid = 3;
        $newWlrScid = 4;
        $oldAdslComponentId = 5;
        $oldWlrComponentId = 6;
        $newAdslScid = 7;

        $objComponent = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_NONE,
            array(
                'strCliNumber' => $strCliNumber,
                'bolSchedule' => $bolScheduleChange,
                'bolEssentialProduct' => $bolEssentialProduct,
                'bolTakePayment' => $bolTakePayment,
                'uxtScheduledDate' => $uxtScheduledDate,
                'manuallyAddPhone' => $manuallyAddPhone,
                'oldSdi' => $oldSdi,
                'newSdi' => $newSdi,
                'oldWlrScid' => $oldWlrScid,
                'newWlrScid' => $newWlrScid,
                'oldAdslComponentId' => $oldAdslComponentId,
                'oldWlrComponentId' => $oldWlrComponentId,
                'newAdslScid' => $newAdslScid
            )
        );

        $this->assertAttributeEquals($strCliNumber, 'strCliNumber', $objComponent);
        $this->assertAttributeEquals($bolScheduleChange, 'bolScheduleChange', $objComponent);
        $this->assertAttributeEquals($bolEssentialProduct, 'bolEssentialProduct', $objComponent);
        $this->assertAttributeEquals($bolTakePayment, 'bolTakePayment', $objComponent);
        $this->assertAttributeEquals(I18n_Date::fromTimestamp($uxtScheduledDate), 'uxtScheduledDate', $objComponent);
        $this->assertAttributeEquals($manuallyAddPhone, 'manuallyAddPhone', $objComponent);
        $this->assertAttributeEquals($oldSdi, 'oldSdi', $objComponent);
        $this->assertAttributeEquals($newSdi, 'newSdi', $objComponent);
        $this->assertAttributeEquals($oldWlrScid, 'oldWlrScid', $objComponent);
        $this->assertAttributeEquals($newWlrScid, 'newWlrScid', $objComponent);
        $this->assertAttributeEquals($oldAdslComponentId, 'oldAdslComponentId', $objComponent);
        $this->assertAttributeEquals($oldWlrComponentId, 'oldWlrComponentId', $objComponent);
        $this->assertAttributeEquals($newAdslScid, 'newAdslScid', $objComponent);

    }

    /**
     * Testing function execute
     *
     * @covers AccountChange_Product_Wlr::execute
     *
     * @return void
     */
    public function testExecuteCallsChange()
    {
        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('change'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objServiceComponent->expects($this->once())
            ->method('change');

        $objServiceComponent->execute();
    }

    /**
     * AccountChange_Product_Wlr::change
     *
     * @return void
     * @throws AccountChange_AccountConfigurationException
     * @throws AccountChange_Product_ManagerException
     */
    public function testChangeThrowsExceptionIfNoServiceComponentContractIsSet()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Service Component
        $objServiceComponent = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );
        $objAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objServiceComponent)
        );

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);

        $this->setExpectedException(
            'AccountChange_Product_ManagerException',
            'Wlr product changes need a service component contract - one was not specified',
            AccountChange_Product_ManagerException::ERR_MISSING_SERVICE_COMPONENT_CONTRACT
        );

        $objServiceComponent->change();
    }

    /**
     * Test for the function 'change' when upgrading
     *
     * @covers AccountChange_Product_Wlr::change
     *
     * @return void
     */
    public function testChangeCallsChangeAccountWhenUpgrading()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getContract', 'changeAccount', 'getProductCost'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE,
            array('intOldComponentId' => 1, 'intNewComponentId' => 2))
        );

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('fetchTariffId'),
            array(),
            '',
            false
        );

        $objContract->expects($this->any())
            ->method('fetchTariffId')
            ->will($this->returnValue(100));

        $objServiceComponent->setContract($objContract);

        $objServiceComponent->expects($this->once())
            ->method('changeAccount');

        $objServiceComponent->expects($this->at(0))
            ->method('getProductCost')
            ->will($this->returnValue(50));

        $objServiceComponent->expects($this->at(1))
            ->method('getProductCost')
            ->will($this->returnValue(100));

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );
        $objAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objServiceComponent)
        );

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);

        $objServiceComponent->change();
    }

    /**
     * Test that an exception is thrown if no contract defined
     *
     * @covers AccountChange_Product_Wlr::change
     * @covers AccountChange_Product_ManagerException
     *
     * @return void
     * @throws AccountChange_Product_ManagerException
     */
    public function testChangeThrowsExceptionIfNoContractIsDefined()
    {
        $this->setExpectedException(
            'AccountChange_Product_ManagerException',
            'Wlr product changes need a service component contract - one was not specified',
            AccountChange_Product_ManagerException::ERR_MISSING_SERVICE_COMPONENT_CONTRACT
        );

        $product = new AccountChange_Product_Wlr(1, AccountChange_Product_Manager::ACTION_NONE);
        $product->change();
    }

    /**
     * Test for the function 'change' when downgrading
     *
     * @covers AccountChange_Product_Wlr::change
     *
     * @return void
     */
    public function testChangeCallsScheduleChangeWhenDowngrading()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getContract', 'scheduleChange', 'getProductCost'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE,
            array('intOldComponentId' => 1, 'intNewComponentId' => 2))
        );

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('fetchTariffId'),
            array(),
            '',
            false
        );

        $objContract->expects($this->any())
            ->method('fetchTariffId')
            ->will($this->returnValue(100));

        $objServiceComponent->setContract($objContract);

        $objServiceComponent->expects($this->once())
            ->method('scheduleChange');

        $objServiceComponent->expects($this->at(0))
            ->method('getProductCost')
            ->will($this->returnValue(100));

        $objServiceComponent->expects($this->at(1))
            ->method('getProductCost')
            ->will($this->returnValue(50));

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );
        $objAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objServiceComponent)
        );

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);

        $objServiceComponent->change();
    }

    /**
     * Test for the function 'change'
     *
     * @covers AccountChange_Product_Wlr::change
     *
     * @return void
     */
    public function testChangeCallsScheduleChangeWhenTheScheduleChangeFlagHasBeenSetNoMatterWhetherUpgradingOrNot()
    {
        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('scheduleChange'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE,
            array('bolSchedule' => true))
        );

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('fetchTariffId'),
            array(),
            '',
            false
        );

        $objServiceComponent->setContract($objContract);

        $objServiceComponent->expects($this->once())
            ->method('scheduleChange');

        $objServiceComponent->change();
    }

    /**
     * Test for the function 'change' when product is essential and opt schedule account change
     *
     * @covers AccountChange_Product_Wlr::change
     *
     * @return void
     */
    public function testChangeCallsScheduleEssentialProductChangeWhenTheProductIsEssentialAndTheChangeIsScheduled()
    {
        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('scheduleEssentialProductChange'),
            array(
                1,
                AccountChange_Product_Manager::ACTION_CHANGE,
                array(
                    'bolSchedule' => true,
                    'bolEssentialProduct' => true
                )
            )
        );

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('fetchTariffId'),
            array(),
            '',
            false
        );

        $objServiceComponent->setContract($objContract);

        $objServiceComponent->expects($this->once())
            ->method('scheduleEssentialProductChange');

        $objServiceComponent->change();
    }

    /**
     * Test for the function 'execute' while call remove
     *
     * @covers AccountChange_Product_Wlr::execute
     *
     * @return void
     */
    public function testExecuteCallsRemove()
    {
        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('remove'),
            array(1, AccountChange_Product_Manager::ACTION_REMOVE)
        );

        $objServiceComponent->expects($this->once())
            ->method('remove');

        $objServiceComponent->execute();
    }

    /**
     * Test for the function 'remove'
     *
     * @covers AccountChange_Product_Wlr::remove
     *
     * @return void
     */
    public function testRemoveCallsRemoveWlrProductLegacyCall()
    {
        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('removeWlrProduct'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objServiceComponent->expects($this->once())
            ->method('removeWlrProduct');

        $objServiceComponent->remove();
    }

    /**
     * Test for the function 'create' when called without CLI
     *
     * @covers AccountChange_Product_Wlr::create
     *
     * @return void
     */
    public function testCreateThrowsCliErrorExceptionWhenCliNotProvided()
    {
        $objServiceComponentMock = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(
                1,
                AccountChange_Product_Manager::ACTION_CHANGE
            )
        );

        $objServiceComponentMock->expects($this->never())
            ->method('getProductId');

        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('doSignup', 'getContract'),
            array(1,AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objAccountConfigurationMock = $this->getMock(
            'AccountChange_AccountConfiguration',
            array('getProductConfigurationByType'),
            array(),
            '',
            false
        );

        $objAccountConfigurationMock->expects($this->once())
            ->method('getProductConfigurationByType')
            ->will($this->returnValue($objServiceComponentMock));

        $objServiceComponent->expects($this->never())
            ->method('doSignup');
        $objServiceComponent->expects($this->never())
            ->method('getContract');

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfigurationMock);

        $this->setExpectedException(
            'AccountChange_Product_ManagerException',
            'A valid Cli number is needed to perform signup on Wlr',
            AccountChange_Product_ManagerException::ERR_CLI_ERROR
        );

        $objServiceComponent->create();
    }

    /**
     * @test
     * @dataProvider createWlrCliTestProvider
     * @covers AccountChange_Product_Wlr::create
     * @covers AccountChange_Product_Wlr::isBBOToDslMove
     *
     * @return void
     */
    public function createShouldAllowNoCliWhereAppropriate($oldSdiIsBBO, $newSdiIsBBO, $manuallyAddPhone, $shouldCreateComponent, $cli)
    {
        $options = array(
            'oldSdi' => 123,
            'newSdi' => 321,
            'manuallyAddPhone' => $manuallyAddPhone,
            'strCliNumber' => $cli
        );

        $bboToDslChange = $oldSdiIsBBO && !$newSdiIsBBO;

        $objServiceComponentMock = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(
                1,
                AccountChange_Product_Manager::ACTION_CHANGE
            )
        );

        $objServiceComponentMock->expects($this->never())
            ->method('getProductId');

        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array(
                'doSignup',
                'getContract',
                'getBBOHelper',
                'includeLegacyFiles',
                'getServiceComponentDetails',
                'buildActionManager',
                'raiseManaullyAddPhoneRequestTicket'
            ),
            array(1,AccountChange_Product_Manager::ACTION_CHANGE,$options)
        );

        $bboHelperMock = $this->getMock(
            AccountChange_BroadbandOnlyHelper::class,
            array('isBroadbandOnlyProduct'),
            array()
        );

        $actionManagerMock = $this->getMock(
            AccountChange_Action_Manager::class,
            array('execute'),
            array(),
            '',
            false
        );

        $bboHelperMock
            ->method('isBroadbandOnlyProduct')
            ->withConsecutive([123], [321])
            ->willReturnOnConsecutiveCalls($oldSdiIsBBO, $newSdiIsBBO);


        $objServiceComponent
            ->expects($this->once())
            ->method('getBBOHelper')
            ->will($this->returnValue($bboHelperMock));

        if ($manuallyAddPhone && !empty($cli) && !$bboToDslChange) {
            $objServiceComponent
                ->expects($this->once())
                ->method('buildActionManager')
                ->will($this->returnValue($actionManagerMock));

            $actionManagerMock
                ->expects($this->once())
                ->method('execute');

            $objServiceComponent
                ->expects($this->once())
                ->method('raiseManaullyAddPhoneRequestTicket');
        }


        $objServiceComponent
            ->expects($this->once())
            ->method('getBBOHelper')
            ->will($this->returnValue($bboHelperMock));


        $objAccountConfigurationMock = $this->getMock(
            'AccountChange_AccountConfiguration',
            array('getProductConfigurationByType'),
            array(),
            '',
            false
        );

        $contract = $this->getMock('AccountChange_Product_ServiceComponentContract', array(), array(), '', false);

        $objAccountConfigurationMock->expects($this->once())
            ->method('getProductConfigurationByType')
            ->will($this->returnValue($objServiceComponentMock));

        if ($shouldCreateComponent) {
            $objServiceComponent->expects($this->once())
                ->method('doSignup');
            $objServiceComponent->expects($this->once())
                ->method('getContract')
                ->willReturn($contract);
        } elseif(empty($cli)) {
            $this->setExpectedException(
                'AccountChange_Product_ManagerException',
                'A valid Cli number is needed to perform signup on Wlr',
                AccountChange_Product_ManagerException::ERR_CLI_ERROR
            );
        }

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfigurationMock);

        $this->assertNull($objServiceComponent->create());
    }

    public function createWlrCliTestProvider()
    {
        return [
            'BBO -> ADSL' => [
                'oldSdiIsBBO' => true,
                'newSdiIsBBO' => false,
                'manuallyAddPhone' => false,
                'shouldCreateComponent' => true,
                'strCli' => null
            ],
            'BBO -> FTTC' => [
                'oldSdiIsBBO' => true,
                'newSdiIsBBO' => false,
                'manuallyAddPhone' => true,
                'shouldCreateComponent' => true,
                'strCli' => null
            ],
            'DSL -> DSL' => [
                'oldSdiIsBBO' => false,
                'newSdiIsBBO' => false,
                'manuallyAddPhone' => false,
                'shouldCreateComponent' => false,
                'strCli' => null
            ],
            'DSL -> FTTC' => [
                'oldSdiIsBBO' => false,
                'newSdiIsBBO' => false,
                'manuallyAddPhone' => true,
                'shouldCreateComponent' => false,
                'strCli' => '**********'
            ],
            'DSL -> BBO' => [
                'oldSdiIsBBO' => false,
                'newSdiIsBBO' => true,
                'manuallyAddPhone' => false,
                'shouldCreateComponent' => false,
                'strCli' => null
            ]
        ];
    }

    /**
     * Test for the function 'isScheduled'
     *
     * @covers AccountChange_Product_Wlr::isScheduled
     *
     * @return void
     */
    public function testIsScheduledReturnsTrueIfTheBolScheduleFlagIsSet()
    {
        $arrOptions = array(
            'bolSchedule' => true,
        );

        $objProduct = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE,
            $arrOptions
        );

        $bolResult = $objProduct->isScheduled();

        $this->assertTrue($bolResult);
    }

    /**
     * Test for the function 'isScheduled' when product is downgrading
     *
     * @covers AccountChange_Product_Wlr::isScheduled
     *
     * @return void
     */
    public function testIsScheduledReturnsTrueIfProductIsDowngrading()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objProduct = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getContract', 'scheduleChange', 'getProductCost'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE,
            array('intOldComponentId' => 1, 'intNewComponentId' => 2))
        );

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('fetchTariffId'),
            array(),
            '',
            false
        );

        $objContract->expects($this->any())
            ->method('fetchTariffId')
            ->will($this->returnValue(100));

        $objProduct->setContract($objContract);

        $objProduct->expects($this->at(0))
            ->method('getProductCost')
            ->will($this->returnValue(100));

        $objProduct->expects($this->at(1))
            ->method('getProductCost')
            ->will($this->returnValue(50));

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );
        $objAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct)
        );

        $objProduct->setMatchingProductConfiguration($objAccountConfiguration);
        $objProduct->setAccountChangeOperation($objAccountConfiguration);

        $bolResult = $objProduct->isScheduled();

        $this->assertTrue($bolResult);
    }

    /**
     * Test for the function 'isScheduled' when product is not downgrading and nor Scheduled
     *
     * @covers AccountChange_Product_Wlr::isScheduled
     *
     * @return void
     */
    public function testIsScheduledReturnsFalseIfNotBolScheduledNorProductDowngrading()
    {
        $objProduct = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE,
            array()
        );

        $bolResult = $objProduct->isScheduled();

        $this->assertFalse($bolResult);
    }

    /**
     * Test for the function 'isTakingPayment'
     *
     * @covers AccountChange_Product_Wlr::isTakingPayment
     *
     * @return void
     */
    public function testIsTakingPaymentReturnsCorrectValue()
    {
        $objProduct = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_NONE,
            array('bolTakePayment' => true)
        );

        $this->assertTrue($objProduct->isTakingPayment());
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getServiceDaoData()
    {
        return array(
            'service_id'=>1234,
            'user_id'=>'12345',
            'isp' => 'plusnet',
            'username' => 'testuser',
            'password' => 'password',
            'cli_number' => ************,
            'type' => '1',
            'status' => 'active',
            'startdate' => '2008-12-10',
            'enddate' => '',
            'next_invoice' => '2009-10-10',
            'invoice_period' => '2',
            'db_src' => 'test',
            'timestamp' => time(),
            'next_invoice_warned' => '',
            'invoice_day' => 3,
            'authorised_switch_payment' => '',
            'bolMailOptOut' => ''
        );
    }

    /**
     * Wrapper for data generation
     *
     * @param string $customerType customer type (res/biz)
     *
     * @return array
     */
    private function getServiceDefinitionDaoData($customerType = 'residential')
    {
        return array(
            'service_definition_id' => 1,
            'intProductVariantId' => 12,
            'name' => 'Test',
            'isp' => 'plusnet',
            'minimum_charge' => '',
            'date_created' => '',
            'requires' => '',
            'initial_charge' => '',
            'type' => $customerType,
            'password_visible_to_support' => '',
            'end_date' => '',
            'signup_via_portal' => '',
            'bt_product_id' => '',
            'strProductVariant' => '' ,
            'strProductFamily' => '',
            'bolAdsl' => true,
            'signup_via_portal' => 1
        );
    }

    /**
     * Verifies that correct email template is used when sending a confirmation email
     *
     * @param int    $action                Action type (change, add, remove, etc).
     *                                      See {@link AccountChange_Product_Manager} for full list
     * @param bool   $switchingFromGbWr     Flag whether customer is mvoing from GB/WR brand
     * @param int    $newServiceComponentId New service component id (phone)
     * @param bool   $scheduledChange       Flag to indicate whether it is a scheduled change
     * @param int    $uxtScheduledDate      Date the scheduled change should occur
     * @param string $expectedEmailTemplate Expected email template to be sent
     * @param string $customerType          Customer type
     *
     * @covers AccountChange_Product_Wlr::sendConfirmationEmail
     * @dataProvider provideChangeDetailsForSendingEmails
     *
     * @return void
     */
    public function testCorrectTemplateIsUsedWhenSendingConfirmationEmail(
        $action,
        $switchingFromGbWr,
        $newServiceComponentId,
        $scheduledChange,
        $uxtScheduledDate,
        $expectedEmailTemplate,
        $customerType = 'residential'
    ) {

        $serviceId = 267854;
        $oldSdi = 6718;
        $newSdi = 6719;
        $oldProductName = 'Plusnet Value';
        $newProductName = 'Plusnet Unlimited';
        $cli       = '***********';
        $emailData = array(
            'intOldSdi' => $oldSdi,
            'intNewSdi' => $newSdi,
            'arrWlrProduct' => array(
                'strProductName' => $oldProductName
            ),
            'arrSelectedWlr' => array(
                'strNewProduct' => $newProductName
            ),
            'uxtScheduledDate' => $uxtScheduledDate
        );

        $serviceComponentContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getProductCost'),
            array('MONTHLY', 'MONTHLY')
        );

        $options   = array(
            'strCliNumber' => $cli,
            'bolSchedule' => $scheduledChange
        );
        $newWlrProduct = new AccountChange_Product_Wlr($oldSdi, AccountChange_Product_Manager::ACTION_NONE);
        $newWlrProduct->setContract($serviceComponentContract);

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceDao',
                'getServiceDefinitionDao'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDao')
            ->will($this->returnValue($this->getServiceDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($this->getServiceDefinitionDaoData($customerType)));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $oldProductWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array('includeLegacyFiles', 'checkCustomerSwitchingFromGbWrToJlp', 'sendEmail'),
            array($newServiceComponentId, $action, $options)
        );
        $oldProductWlr->expects($this->once())
            ->method('checkCustomerSwitchingFromGbWrToJlp')
            ->will($this->returnValue($switchingFromGbWr));

        $oldProductWlr->expects($this->once())
            ->method('sendEmail')
            ->with(
                $this->equalTo($serviceId),
                $this->equalTo($expectedEmailTemplate)
            );

        $oldProductWlr->setServiceId($serviceId);
        $oldProductWlr->setContract($serviceComponentContract);
        $oldProductWlr->setMatchingProductConfigurationManually($newWlrProduct);

        $oldProductWlr->sendConfirmationEmail($emailData);
    }

    /**
     * Verifies that wlr prices for phone change email are retrieved from billing api when the newContract flag is set.
     *
     * @covers AccountChange_Product_Wlr::sendConfirmationEmail
     */
    public function testPricesAreRetrievedFromBillingApiWhenNewContractFlagIsSet()
    {
        $serviceId = 267854;
        $scheduledDate = new DateTime('2020-01-01');

        $emailData = array(
            'intOldSdi' => 1,
            'intNewSdi' => 3,
            'arrWlrProduct' => array(
                'strProductName' => 'Unlimited'
            ),
            'arrSelectedWlr' => array(
                'strNewProduct' => 'Unlimited Fibre Extra'
            ),
            'uxtScheduledDate' => $scheduledDate->getTimestamp()
        );

        $serviceComponentContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getProductCost'),
            array('MONTHLY', 'MONTHLY')
        );

        $options = array(
            'strCliNumber' => '***********',
            'bolSchedule'  => true,
            'newContract'  => true
        );

        $newWlrProduct = $this->getMock(
            'AccountChange_Product_Wlr',
            array(
                'getCallPlanPricePointId',
                'getLineRentalPricePointId',
                'hasActiveLineRentalSaver',
                'getBasePrices'),
            array(1, AccountChange_Product_Manager::ACTION_NONE, $options)
        );

        $newWlrProduct->setContract($serviceComponentContract);

        $newWlrProduct->expects($this->once())
            ->method('getCallPlanPricePointId')
            ->will($this->returnValue('1'));

        $newWlrProduct->expects($this->once())
            ->method('getLineRentalPricePointId')
            ->will($this->returnValue('2'));

        $newWlrProduct->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));

        $newWlrProduct->expects($this->once())
            ->method('getBasePrices')
            ->will($this->returnValue(
                array(
                    '0' => array(
                        'currentBasePrice' => 5
                    ),
                    '1' => array(
                        'currentBasePrice' => 10
                    )
                )
            ));

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceDao',
                'getServiceDefinitionDao'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDao')
            ->will($this->returnValue($this->getServiceDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($this->getServiceDefinitionDaoData()));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $oldProductWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array(
                'includeLegacyFiles',
                'checkCustomerSwitchingFromGbWrToJlp',
                'sendEmail'),
            array(3, AccountChange_Product_Manager::ACTION_CHANGE, $options)
        );

        $oldProductWlr->expects($this->once())
            ->method('checkCustomerSwitchingFromGbWrToJlp')
            ->will($this->returnValue(false));

        $oldProductWlr->expects($this->once())
            ->method('sendEmail')
            ->with(
                $this->equalTo($serviceId),
                $this->equalTo('wlr_changing_sub_to_sub'),
                $this->equalTo(array(
                    'strOldProduct' => 'Unlimited',
                    'strProduct' => 'Unlimited Fibre Extra',
                    'strContractLength' => 'Monthly',
                    'monthlyCost' => 15,
                    'uxtStartDate' => I18n_Date::fromTimestamp($scheduledDate->getTimestamp())
                ))
            );

        $oldProductWlr->setServiceId($serviceId);
        $oldProductWlr->setContract($serviceComponentContract);
        $oldProductWlr->setMatchingProductConfigurationManually($newWlrProduct);
        $oldProductWlr->sendConfirmationEmail($emailData);
    }

    /**
     * Provides:
     * 1. action type (change, add, remove, etc). See {@link AccountChange_Product_Manager} for full list
     * 2. Flag whether customer is mvoing from GB/WR brand
     * 3. new service component id (phone)
     * 4. is this a scheduled change?
     * 5. Date the scheduled change should occur
     * 6. Expected email template to be sent
     *
     * @return array
     */
    public function provideChangeDetailsForSendingEmails()
    {
        $scheduledDate = new DateTime('2012-02-27');
        return array(
            // GB/WR user changing WLR product (scheduled)
            array(
                AccountChange_Product_Manager::ACTION_CHANGE,
                true,
                669,
                true,
                $scheduledDate->getTimestamp(),
                'wlr_changing_sub_to_sub_from_gbwr',
                'residential'
            ),
            // PN user changing WLR product (scheduled)
            array(
                AccountChange_Product_Manager::ACTION_CHANGE,
                false,
                669,
                true,
                $scheduledDate->getTimestamp(),
                'wlr_changing_sub_to_sub',
            ),
            // GB/WR user changing WLR product (instant)
            array(
                AccountChange_Product_Manager::ACTION_CHANGE,
                true,
                669,
                false,
                null,
                'wlr_changing_sub_to_sub_from_gbwr',
            ),
            // PN user changing WLR product (instant)
            array(
                AccountChange_Product_Manager::ACTION_CHANGE,
                false,
                669,
                false,
                null,
                'wlr_changing_sub_to_sub',
            ),
            // Business product change
            array(
                AccountChange_Product_Manager::ACTION_CHANGE,
                false,
                669,
                false,
                null,
                'wlr_changing_sub_to_sub_business',
                'business',
            ),
            // PN user adding specific WLR product (not scheduled)
            array(AccountChange_Product_Manager::ACTION_ADD, false, 554, false, null, 'welcome_email_talk_eb003'),
            array(AccountChange_Product_Manager::ACTION_ADD, false, 555, false, null, 'welcome_email_talk_eb003'),
            array(
                AccountChange_Product_Manager::ACTION_ADD,
                false,
                213,
                false,
                null,
                'welcome_email_business_phone',
                'business',
            ),
        );
    }

    /**
     * Function to test create function when phone need to be added manually
     *
     * @covers AccountChange_Product_Wlr::create
     * @covers AccountChange_Product_Wlr::raiseManaullyAddPhoneRequestTicket
     * @return void
     */
    public function testCreateWhenManuallyProcessAddPhone()
    {
        $options = array(
            'strCliNumber' => **********,
            'manuallyAddPhone' => true
        );
        $serviceComponentMock = $this->getMock(
            'AccountChange_Product_Wlr',
            array('includeLegacyFiles', 'getServiceComponentDetails', 'getActionManager'),
            array(1, AccountChange_Product_Manager::ACTION_ADD, $options)
        );

        $serviceComponentDetails = array(
            'strName' => 'testComponent'
        );

        $actionManagerMock = $this->getMock('AccountChange_Action_Manager', array(), array(), '', false);

        $serviceComponentMock->expects($this->once())
            ->method('includeLegacyFiles');
        $serviceComponentMock->expects($this->once())
            ->method('getServiceComponentDetails')
            ->will($this->returnValue($serviceComponentDetails));
        $serviceComponentMock->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($actionManagerMock));

        $this->assertTrue($serviceComponentMock->create());
    }

    /**
     * Function to test create function when phone need to be added manually
     *
     * @covers AccountChange_Product_Wlr::create
     * @covers AccountChange_Product_Wlr::raiseManaullyAddPhoneRequestTicket
     * @return void
     */
    public function testCreateWhenManuallyProcessAddPhoneWithCallerDisplay()
    {
        $options = array(
            'strCliNumber' => **********,
            'manuallyAddPhone' => true
        );
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_Wlr $serviceComponentMock */
        $serviceComponentMock = $this->getMock(
            'AccountChange_Product_Wlr',
            array('includeLegacyFiles', 'getServiceComponentDetails', 'getActionManager'),
            array(1, AccountChange_Product_Manager::ACTION_ADD, $options)
        );

        $serviceComponentDetails = array(
            'strName' => 'testComponent'
        );

        $actionManagerMock = $this->getMock('AccountChange_Action_Manager', array(), array(), '', false);

        $serviceComponentMock->expects($this->once())
            ->method('includeLegacyFiles');
        $serviceComponentMock->expects($this->once())
            ->method('getServiceComponentDetails')
            ->will($this->returnValue($serviceComponentDetails));
        $serviceComponentMock->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($actionManagerMock));

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('callerDisplay', true);

        $this->assertTrue($serviceComponentMock->create());
        /** @var AccountChange_Ticket[] $tickets */
        $tickets = $serviceComponentMock->getTickets();
        $ticketText = $tickets[sizeof($tickets)-1]->getComment();
        $this->assertContains('Caller display', $ticketText, '', true);
    }

    /**
     * Function to test create function when phone need to be process automatically
     *
     * @covers AccountChange_Product_Wlr::create
     *
     * @return void
     */
    public function testCreate()
    {
        $componentId = 55;
        $options = array(
            'strCliNumber' => **********,
            'manuallyAddPhone' => false
        );
        $contract = $this->getMock('AccountChange_Product_ServiceComponentContract', array(), array(), '', false);
        $serviceComponentMock = $this->getMock(
            'AccountChange_Product_Wlr',
            array('includeLegacyFiles', 'getServiceComponentDetails', 'doSignup', 'getContract'),
            array(1, AccountChange_Product_Manager::ACTION_ADD, $options)
        );
        $serviceComponentMock->expects($this->any())
            ->method('doSignup')
            ->will($this->returnValue($componentId));

        $serviceComponentDetails = array(
            'strName' => 'testComponent'
        );

        $serviceComponentMock->expects($this->once())
            ->method('includeLegacyFiles');

        $serviceComponentMock->expects($this->once())
            ->method('getServiceComponentDetails')
            ->will($this->returnValue($serviceComponentDetails));
        $serviceComponentMock->expects($this->once())
            ->method('getContract')
            ->will($this->returnValue($contract));

        $test = $serviceComponentMock->create();

        $this->assertEquals($componentId, $test);
    }

    /**
     * Test action manager is created correctly with the necessary values in the registry when
     * NewProductConfiguration is set
     *
     * @covers AccountChange_Product_Wlr::getActionManager
     * @covers AccountChange_Product_Wlr::buildActionManager
     *
     * @return void
     */
    public function testBuildActionManager()
    {
        $serviceId = 999;
        $oldSdi = 1;
        $newSdi = 2;
        $oldWlrScid = 3;
        $newWlrScid = 4;
        $oldAdslComponentId = 5;
        $oldWlrComponentId = 6;
        $newAdslScid = 7;

        // Create a service definition product
        $serviceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $product = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE,
            array(
                'oldSdi' => $oldSdi,
                'newSdi' => $newSdi,
                'oldWlrScid' => $oldWlrScid,
                'newWlrScid' => $newWlrScid,
                'oldAdslComponentId' => $oldAdslComponentId,
                'oldWlrComponentId' => $oldWlrComponentId,
                'newAdslScid' => $newAdslScid
            )
        );

        // Setup the Account configurations
        $configuration = new AccountChange_AccountConfiguration(
            array($serviceDefinition, $product)
        );

        // Match the product up
        $product->setMatchingProductConfiguration($configuration);
        $product->setServiceId($serviceId);

        $registry = AccountChange_Registry::instance();
        $registry->reset();

        $actionManager = $product->buildActionManager(array());

        $this->assertInstanceOf('AccountChange_Action_Manager', $actionManager);
        $this->assertAttributeEquals($serviceId, '_intServiceId', $actionManager);

        $this->assertEquals($oldSdi, $registry->getEntry('intOldServiceDefinitionId'));
        $this->assertEquals($newSdi, $registry->getEntry('intNewServiceDefinitionId'));
        $this->assertEquals($oldWlrScid, $registry->getEntry('oldWlrServiceComponentId'));
        $this->assertEquals($newWlrScid, $registry->getEntry('newWlrServiceComponentId'));
        $this->assertEquals($oldAdslComponentId, $registry->getEntry('oldAdslComponentId'));
        $this->assertEquals($oldWlrComponentId, $registry->getEntry('oldWlrComponentId'));
        $this->assertEquals($newAdslScid, $registry->getEntry('newAdslServiceComponentId'));
    }

    /**
     * Test action manager is created correctly with the necessary values in the registry when
     * NewProductConfiguration is not set
     *
     * @covers AccountChange_Product_Wlr::getActionManager
     * @covers AccountChange_Product_Wlr::buildActionManager
     *
     * @return void
     */
    public function testBuildActionManagerWithoutNewProductConfiguration()
    {
        $serviceId = 999;
        $oldSdi = 1;
        $newSdi = 2;
        $oldWlrScid = 3;
        $newWlrScid = 4;
        $oldAdslComponentId = 5;
        $oldWlrComponentId = 6;
        $newAdslScid = 7;
        $newWlrComponentId = *********;

        $product = new AccountChange_Product_Wlr(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE,
            array(
                'oldSdi' => $oldSdi,
                'newSdi' => $newSdi,
                'oldWlrScid' => $oldWlrScid,
                'newWlrScid' => $newWlrScid,
                'oldAdslComponentId' => $oldAdslComponentId,
                'oldWlrComponentId' => $oldWlrComponentId,
                'newAdslScid' => $newAdslScid
            )
        );

        $product->setServiceId($serviceId);
        $product->setComponentId($newWlrComponentId);

        $registry = AccountChange_Registry::instance();
        $registry->reset();

        $actionManager = $product->buildActionManager(array());

        $this->assertInstanceOf('AccountChange_Action_Manager', $actionManager);
        $this->assertAttributeEquals($serviceId, '_intServiceId', $actionManager);

        $this->assertEquals($oldSdi, $registry->getEntry('intOldServiceDefinitionId'));
        $this->assertEquals($newSdi, $registry->getEntry('intNewServiceDefinitionId'));
        $this->assertEquals($oldWlrScid, $registry->getEntry('oldWlrServiceComponentId'));
        $this->assertEquals($newWlrScid, $registry->getEntry('newWlrServiceComponentId'));
        $this->assertEquals($oldAdslComponentId, $registry->getEntry('oldAdslComponentId'));
        $this->assertEquals($oldWlrComponentId, $registry->getEntry('oldWlrComponentId'));
        $this->assertEquals($newAdslScid, $registry->getEntry('newAdslServiceComponentId'));
        $this->assertEquals($newWlrComponentId, $registry->getEntry('newWlrComponentId'));
    }

    /**
     * Tests the suppress confirmation email mechanism
     * @return void
     *
     * @covers AccountChange_Product_Wlr::shouldSendConfirmationEmail
     * @covers AccountChange_PerformChangeApi::suppressWlrConfirmationEmail
     */
    public function testShouldSendConfirmationEmail()
    {

        $product = new AccountChange_Product_Wlr(1, AccountChange_Product_Manager::ACTION_CHANGE);

        $api = new AccountChange_PerformChangeApi();

        $this->assertTrue($product->shouldSendConfirmationEmail());

        $api->suppressWlrConfirmationEmail();

        $this->assertFalse($product->shouldSendConfirmationEmail());

    }

    /**
     * Test that adding or removing caller display affects the optional product component list as expected
     *
     * @param string[] $arrOptionalProductComponents Optional product components
     * @param boolean $userWantsCallerDisplay Whether we want caller display to end up in the result
     * @param integer $expectedPCCount The expected number of product components after running
     * @param boolean $accountChange Whether accountchange is schedule or instant
     *
     * @return void
     * @throws AccountChange_CallFeature_ExistingCallFeatureException
     * @throws AccountChange_CallFeature_InactiveWlrComponentException
     * @dataProvider dataProviderAddOrRemoveCallerDisplay
     * @group arg
     */
    public function testAddOrRemoveCallerDisplay(
        $arrOptionalProductComponents,
        $userWantsCallerDisplay,
        $expectedPCCount,
        $accountChange
    ) {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_Wlr $sut */
        $sut = $this->getMock(
            'AccountChange_Product_Wlr',
            ['getOptionalProductComponents','getCallFeatureApi'],
            [1, AccountChange_Product_Manager::ACTION_CHANGE]
        );
        $sut->expects($this->any())->method('getOptionalProductComponents')
            ->willReturn($arrOptionalProductComponents);
	    $mockCallFeature = $this->getMock(
            'AccountChange_CallFeature_Api',
            ['removeCallFeature', 'addCallFeature'],
            [1, AccountChange_Product_Manager::ACTION_CHANGE]
        );
	    $mockCallFeature->expects($this->any())->method('removeCallFeature')
            ->willReturn($this->returnValue(true));
        $mockCallFeature->expects($this->any())->method('addCallFeature')
            ->willReturn($this->returnValue(true));
        $sut->expects($this->any())->method('getCallFeatureApi')
            ->willReturn($mockCallFeature);

        $registry = new AccountChange_Registry();
        $registry->setEntry('callerDisplay', $userWantsCallerDisplay);
        AccountChange_Registry::setInstance($registry);

        $sut->addOrRemoveCallerDisplayForNewWlrComponent($arrOptionalProductComponents, $accountChange);

        if ($userWantsCallerDisplay) {
            $this->assertContains(
                AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                $arrOptionalProductComponents
            );
        } else {
            $this->assertNotContains(
                AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                $arrOptionalProductComponents
            );
        }
        $this->assertCount($expectedPCCount, $arrOptionalProductComponents);
    }

    public function dataProviderAddOrRemoveCallerDisplay()
    {
        return array(
            array([], false, 0, false),
            array([], true, 1, false),
            array([AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE], false, 0, false),
            array([AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE], true, 1, false),
            array(['WlrOtherFeature'], false, 1, false),
            array(['WlrOtherFeature'], true, 2, false),
            array([AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE, 'WlrCallFeature'], false, 1, false),
            array([AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE, 'WlrCallFeature'], true, 2, false),
            array(['WlrSomeFeature', AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE], false, 1, false),
            array(['WlrSomeFeature', AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE], true, 2, false),
            array(['WlrOtherFeature'], true, 2, true),
        );
    }


    /**
     * Test that Caller Display Feature Sending WLR Order
     *
     */
    public function testCallerDisplaySendWlrOrder() {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Product_Wlr $sut */
        $sut = $this->getMock(
            'AccountChange_Product_Wlr',
            ['getCallFeatureApi'],
            [1, AccountChange_Product_Manager::ACTION_CHANGE]
        );
        $mockCallFeature = $this->getMock(
            'AccountChange_CallFeature_Api',
            ['provisionCallFeature'],
            [1, AccountChange_Product_Manager::ACTION_CHANGE]
        );
        $mockCallFeature->expects($this->any())->method('provisionCallFeature')
            ->willReturn($this->returnValue(true));

        $sut->expects($this->any())->method('getCallFeatureApi')
            ->willReturn($mockCallFeature);

        $intNewComponentId = 1;

        $result = $sut->callerDisplaySendWlrOrder($intNewComponentId);

        $this->assertNull($result);
    }

    /**
     * Test the protected method doSignup
     * Testing protected methods is more brittle because changes to the non-public API will break the test
     * But this avoids a major refactoring
     *
     * @param $userWantsCallerDisplay
     * @param $sendConfirmationEmail
     * @param $isHouseMove
     *
     * @return void
     * @dataProvider dataProviderDoSignup
     */
    public function testDoSignup($userWantsCallerDisplay, $sendConfirmationEmail, $isHouseMove)
    {
        $serviceId = 2334243;
        $serviceComponentId = 3238720;
        $cliNumber = '***********';
        $productName = 'My test WLR product';
        $newWlrComponentId = ********;

        /** @var PHPUnit_Framework_MockObject_MockObject| AccountChange_Product_Wlr $sut */
        $sut = $this->getMock(
            'AccountChange_Product_Wlr',
            ['includeLegacyFiles', 'makeNewWlrProduct', 'buildActionManager', 'shouldSendConfirmationEmail', 'sendConfirmationEmail'],
            [],
            '',
            false
        );

        $contract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            [
                'getContractLengthHandle',
                'getPaymentFrequencyHandle',
                'shouldSendConfirmationEmail',
                'sendConfirmationEmail'
            ],
            [],
            '',
            false
        );

        $product = $this->getMock(
            'CWlrProduct',
            ['doSignup', 'getFullName', 'getComponentID', 'getError'],
            [],
            '',
            false
        );

        $actionManager = $this->getMock('AccountChange_Action_Manager', ['execute'], [], '', false);

        $sut->expects($this->any())->method('includeLegacyFiles');
        $sut->expects($this->any())->method('makeNewWlrProduct')->willReturn($product);
        $sut->expects($this->any())->method('shouldSendConfirmationEmail')->willReturn($sendConfirmationEmail);

        $sut->expects($this->exactly((int)$sendConfirmationEmail))
            ->method('sendConfirmationEmail')
            ->willReturnCallback(function ($emailData) use ($productName) {
                $this->assertArrayHasKey('arrSelectedWlr', $emailData);
                $this->assertArrayHasKey('strNewProduct', $emailData['arrSelectedWlr']);
                $this->assertEquals($productName, $emailData['arrSelectedWlr']['strNewProduct']);
            });

        $sut->expects($this->any())
            ->method('buildActionManager')
            ->willReturnCallback(function ($actions) use ($isHouseMove, $actionManager) {
                $this->assertContains(AccountChange_Action_Manager::ACTION_LINE_RENTAL_SAVER_ADD, $actions);
                $this->assertContains(AccountChange_Action_Manager::ACTION_CONSENT, $actions);
                if (!$isHouseMove) {
                    $this->assertContains(AccountChange_Action_Manager::ACTION_LTC_CONTRACTS, $actions);
                } else {
                    $this->assertNotContains(AccountChange_Action_Manager::ACTION_LTC_CONTRACTS, $actions);
                }

                return $actionManager;
            });

        $contract->expects($this->atLeastOnce())
            ->method('getContractLengthHandle')
            ->willReturn('MONTHLY');

        $contract->expects($this->atLeastOnce())
            ->method('getPaymentFrequencyHandle')
            ->willReturn('MONTHLY');

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Registry $registry */
        $registry = $this->getMock('AccountChange_Registry', ['getEntry']);
        $registry->expects($this->any())
            ->method('getEntry')
            ->willReturnMap([
                ['callerDisplay', $userWantsCallerDisplay]
            ]);
        AccountChange_Registry::setInstance($registry);

        $product->expects($this->once())
            ->method('doSignup')
            ->with(
                $this->equalTo($serviceId),
                $this->equalTo($serviceComponentId),
                $this->equalTo($cliNumber),
                $this->anything() // Evaluate in callback
            )
            ->willReturnCallback(
                function($serviceId, $serviceComponentId, $cliNumber, $options, $params)
                use ($product, $userWantsCallerDisplay) {
                    if ($userWantsCallerDisplay) {
                        $this->assertContains(
                            AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                            $options
                        );
                    } else {
                        $this->assertNotContains(
                            AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                            $options
                        );
                    }

                    return $product; // Real version creates a new object of same type
                }
            );

        $product->expects($this->any())->method('getFullName')->willReturn($productName);
        $product->expects($this->any())->method('getComponentID')->willReturn($newWlrComponentId);
        $product->expects($this->any())->method('getError')->willReturn(false);

        $actionManager->expects($this->once())->method('execute');

        // Make the protected method accessible
        // This makes the test more brittle because any refactoring of the non-public API will make the test fail
        // But avoids a major refactoring job
        $reflector = new ReflectionObject($sut);
        $method = $reflector->getMethod('doSignup');
        $method->setAccessible(true);
        $houseMoveField = $reflector->getProperty('bolHousemove');
        $houseMoveField->setAccessible(true);
        $houseMoveField->setValue($sut, $isHouseMove);

        $this->assertEquals(
            $newWlrComponentId,
            $method->invoke($sut, $serviceId, $serviceComponentId, $cliNumber, $contract)
        );
    }

    function dataProviderDoSignup()
    {
        return [
            [false, false, false],
            [false, false, true ],
            [false, true,  true ],
            [true,  true,  true ],
            [false, true,  false],
            [true,  true,  false],
            [true,  false, false],
            [true,  false, true ]
        ];
    }

    public function testChangeAccountDoesNotTriggerLtcContractActionsForCallPlanOnlyChange()
    {
        $componentId = 12345;
        $action = AccountChange_Product_Manager::ACTION_CHANGE;
        $productId = 54321;
        $tariffId = 1111;
        $contractEndDate = strtotime('today');
        $callerDisplay = false;

        $productOptions = array(
            "option1" => 1
        );

        $options = array(
            "oldSdi" =>         "0000",
            "newSdi" =>         "0000",
            "oldWlrScid" =>     "1111",
            "newWlrScid" =>     "2222",
            "bolHousemove" =>   false,
            "bolSchedule" =>    false
        );

        $serviceComponentContract = Mockery::mock("AccountChange_Product_ServiceComponentContract");

        $wlrComponent = Mockery::mock();
        $wlrComponent->shouldReceive("getComponentID")->andReturn($componentId);

        $wlrProduct = Mockery::mock();
        $wlrProduct->shouldReceive("create")->andReturn($wlrComponent);
        $wlrProduct->shouldReceive("changeAccountType");

        $contract = Mockery::mock("AccountChange_Product_ServiceComponentContract");
        $contract->shouldReceive("fetchTariffId")->andReturn($tariffId);
        $contract->shouldReceive("getContractEndDate")->andReturn($contractEndDate);

        $productConfiguration = Mockery::mock("AccountChange_ProductConfiguration");
        $productConfiguration->shouldReceive("getContract")->andReturn($contract);
        $productConfiguration->shouldReceive("getProductId")->andReturn($productId);
        $productConfiguration->shouldReceive("setComponentId");
        $productConfiguration->shouldReceive("getComponentId")->andReturn($componentId);

        $accountConfig = Mockery::mock("AccountChange_AccountConfiguration");
        $accountConfig->shouldReceive("getProductConfigurationByType")->andReturn($productConfiguration);

        $actionManager = Mockery::mock("AccountChange_Action_Manager");
        $actionManager->shouldReceive("execute");

        $productChange = Mockery::mock("AccountChange_Product_Wlr");
        $productChange->makePartial();
        $productChange->shouldAllowMockingProtectedMethods();
        $productChange->shouldReceive("setMatchingProductConfiguration")->with($accountConfig)->passthru();
        $productChange->shouldReceive("includeLegacyFiles");
        $productChange->shouldReceive("makeNewWlrProduct")->andReturn($wlrProduct);
        $productChange->shouldReceive("getOptionalProductComponents")->andReturn($productOptions);
        $productChange->shouldReceive("addOrRemoveCallerDisplayForNewWlrComponent")->andReturn($callerDisplay);
        $productChange->shouldReceive("buildActionManager")->with(
            Mockery::on(function ($actions) {
                foreach ($actions as $action) {
                    $this->assertNotEquals(AccountChange_Action_Manager::ACTION_LTC_CONTRACTS, $action);
                }
                return true;
            })
        )->andReturn($actionManager);

        $productChange->initialise($componentId, $action, $options);
        $productChange->setContract($serviceComponentContract);
        $productChange->setMatchingProductConfiguration($accountConfig);
        $productChange->change();
    }

    public function testScheduleChangeDoesNotTriggerLtcContractActionsForCallPlanOnlyChange()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('phoneContractChange', false);

        $componentId = 12345;
        $newComponentId = 23456;
        $action = AccountChange_Product_Manager::ACTION_CHANGE;
        $productId = 54321;
        $tariffId = 1111;
        $contractStartDate = strtotime('today');
        $contractEndDate = strtotime('+1 year', $contractStartDate);
        $invoiceDate = strtotime('+1 month', $contractStartDate);
        $newComponentName = "testComponent";
        $existingComponentName = "anotherTestComponent";
        $recontract = false;

        $options = array(
            "oldSdi" =>                    "0000",
            "newSdi" =>                    "0000",
            "oldWlrScid" =>                "1111",
            "newWlrScid" =>                "2222",
            "bolHousemove" =>              false,
            "bolSchedule" =>               true,
            "bolEssentialProduct" =>       false,
            "intAccountChangeOperation" => 0
        );

        $productOptions = array(
            "option1" => 1
        );

        $wlrComponent = Mockery::mock();
        $wlrComponent->shouldReceive("getComponentID")->andReturn($newComponentId);
        $wlrComponent->shouldReceive("getComponentName")->andReturn($newComponentName);

        $wlrProduct = Mockery::mock();
        $wlrProduct->shouldReceive("create")->andReturn($wlrComponent);

        $staticProduct = Mockery::mock("CProduct");
        $staticProduct->shouldReceive("cancelAllOutstandingProductScheduledActions");

        $staticChange = Mockery::mock("CProductChange");
        $staticChange->shouldReceive("create");

        $serviceComponentContract = Mockery::mock("AccountChange_Product_ServiceComponentContract");

        $contract = Mockery::mock("AccountChange_Product_ServiceComponentContract");
        $contract->shouldReceive("fetchTariffId")->andReturn($tariffId);

        $productConfiguration = Mockery::mock("AccountChange_ProductConfiguration");
        $productConfiguration->shouldReceive("getContract")->andReturn($contract);
        $productConfiguration->shouldReceive("getProductId")->andReturn($productId);
        $productConfiguration->shouldReceive("setComponentId");

        $accountConfig = Mockery::mock("AccountChange_AccountConfiguration");
        $accountConfig->shouldReceive("getProductConfigurationByType")->andReturn($productConfiguration);

        $nextInvoiceDate = Mockery::mock();
        $nextInvoiceDate->shouldReceive("fShort")->andReturn($invoiceDate);

        $coreService = Mockery::mock("Core_Service");
        $coreService->shouldReceive("getNextInvoiceDate")->andReturn($nextInvoiceDate);

        $registry = Mockery::mock("AccountChange_Registry");
        $registry->shouldReceive("getEntry")->andReturn($recontract);
        $registry->shouldReceive("setEntry");

        $contractStatus = Mockery::mock();
        $contractStatus->shouldReceive("isServicePriceProtected");

        $actionManager = Mockery::mock();
        $actionManager->shouldReceive("execute");

        $existingComponent = Mockery::mock("CComponent");
        $existingComponent->shouldReceive("getComponentName")->andReturn($existingComponentName);

        $mockSchedulingHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('calculateScheduledChangeDate', 'getOveriddenChangeDate'),
            array()
        );

        $schedulingHelper = Mockery::mock('AccountChange_SchedulingHelper');
        $schedulingHelper->shouldReceive("calculateScheduledChangeDate")->andReturn($nextInvoiceDate);

        $productChange = Mockery::mock("AccountChange_Product_Wlr");
        $productChange->makePartial();
        $productChange->shouldAllowMockingProtectedMethods();
        $productChange->shouldReceive("setMatchingProductConfiguration")->with($accountConfig)->passthru();
        $productChange->shouldReceive("includeLegacyFiles");
        $productChange->shouldReceive("makeNewWlrProduct")->andReturn($wlrProduct);
        $productChange->shouldReceive("makeNewStaticProduct")->andReturn($staticProduct);
        $productChange->shouldReceive("makeNewStaticProductChange")->andReturn($staticChange);
        $productChange->shouldReceive("getCoreService")->andReturn($coreService);
        $productChange->shouldReceive("getContractStatus")->andReturn($contractStatus);
        $productChange->shouldReceive("getOptionalProductComponents")->andReturn($productOptions);
        $productChange->shouldReceive("addOrRemoveCallerDisplayForNewWlrComponent");
        $productChange->shouldReceive("createServiceNotice");
        $productChange->shouldReceive("makeNewComponent")->andReturn($existingComponent);
        $productChange->shouldReceive("getSchedulingHelper")->andReturn($schedulingHelper);
        $productChange->shouldReceive("shouldSendConfirmationEmail")->andReturnFalse();
        $productChange->shouldReceive("buildActionManager")->with(
            Mockery::on(function ($actions) {
                foreach ($actions as $action) {
                    $this->assertNotEquals(AccountChange_Action_Manager::ACTION_LTC_CONTRACTS, $action);
                }
                return true;
            })
        )->andReturn($actionManager);

        $productChange->initialise($componentId, $action, $options);
        $productChange->setContract($serviceComponentContract);
        $productChange->setMatchingProductConfiguration($accountConfig);
        $productChange->change();
    }

    /**
     * Test for the function 'change' when there is a backdated change
     *
     * @covers AccountChange_Product_Wlr::change
     *
     * @return void
     */
    public function testChangeCallsChangeAccountWhenBackDatedChange()
    {
        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isServiceComponentAllowedOnServiceDefinition')
            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objServiceComponent = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getContract', 'changeAccount', 'getProductCost', 'getBackdatedDate'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE,
                array('intOldComponentId' => 1, 'intNewComponentId' => 2, 'bolSchedule' => 1))
        );

        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('fetchTariffId'),
            array(),
            '',
            false
        );

        $objContract->expects($this->any())
            ->method('fetchTariffId')
            ->will($this->returnValue(100));

        $objServiceComponent->setContract($objContract);

        $objServiceComponent->expects($this->once())
            ->method('changeAccount');

        $objServiceComponent
            ->expects($this->once())
            ->method('getBackdatedDate')
            ->will($this->returnValue('2020-01-01'));

        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );
        $objAccountConfiguration = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objServiceComponent)
        );

        $objServiceComponent->setMatchingProductConfiguration($objAccountConfiguration);
        $objServiceComponent->setAccountChangeOperation($objAccountConfiguration);
        $objServiceComponent->change();
    }
}
