<?php
/**
 * Scheduled Account Change Performer test
 *
 * Testing class for the AccountChange_PerformScheduledAccountChange
 *
 * @package   AccountChange
 * <AUTHOR> <kprz<PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2009-03-05
 */

use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamWrapper;
use org\bovigo\vfs\vfsStreamDirectory;
use org\bovigo\vfs\vfsStreamFile;
use org\bovigo\vfs\vfsStreamContent;

/**
 * Scheduled Account Change Class Test
 *
 * @package   AccountChange
 * <AUTHOR> <kprz<PERSON>bysz<PERSON><EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_PerformScheduledAccountChangeTest extends PHPUnit_Framework_TestCase
{
    const SCRIPT_NAME = 'AccountChange_PerformScheduledAccountChange';

    /**
     * @return void
     */
    protected function tearDown()
    {
        Mockery::close();
    }

    /**
     * Create the constructor
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testContructorLoadsBuildsObject()
    {
        $script = new AccountChange_PerformScheduledAccountChange();
        $this->assertInstanceOf('AccountChange_PerformScheduledAccountChange', $script);
    }

    /**
     * Create the constructor by calling 'singleton'
     *
     * @covers AccountChange_PerformScheduledAccountChange::instance
     *
     * @return void
     */
    public function testInstanceLoadsBuildsObject()
    {
        $script = AccountChange_PerformScheduledAccountChange::instance();
        $this->assertInstanceOf('AccountChange_PerformScheduledAccountChange', $script);
    }

    /**
     * Make sure that by default there is no restriction on service ids
     *
     * @covers AccountChange_PerformScheduledAccountChange::getRestrictTo
     *
     * @return void
     */
    public function testThatByDefaultThereIsNoRestrictionOnServiceIds()
    {
        $script = new AccountChange_PerformScheduledAccountChange();
        $actual = $script->getRestrictTo();

        $expected = array();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test isAccountValidForProcessing
     *
     * @covers AccountChange_PerformScheduledAccountChange::isAccountValidForProcessing
     *
     * @return void
     */
    public function testIsAccountValidForProcessingOldSdiSameAsCurrent()
    {
        $expected = true;
        $changer = new AccountChange_PerformScheduledAccountChange();
        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '',
            'strOldProductName' => '',
            'intOldSdi' => '1',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );
        $actual = $changer->isAccountValidForProcessing($account);
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test isAccountValidForProcessing
     *
     * @covers AccountChange_PerformScheduledAccountChange::isAccountValidForProcessing
     *
     * @return void
     */
    public function testIsAccountValidForProcessingNewSdiSameAsCurrent()
    {
        $expected = false;
        $changer = new AccountChange_PerformScheduledAccountChange();
        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '1',
            'strOldProductName' => '',
            'intOldSdi' => '',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );
        $actual = $changer->isAccountValidForProcessing($account);
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test isAccountValidForProcessing
     *
     * @covers AccountChange_PerformScheduledAccountChange::isAccountValidForProcessing
     *
     * @return void
     */
    public function testIsAccountValidForProcessingOldSdiNotEqualToCurrent()
    {
        $expected = false;
        $changer = new AccountChange_PerformScheduledAccountChange();
        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '1',
            'strOldProductName' => '',
            'intOldSdi' => '3',
            'intServiceId' => '12323',
            'intCurrentSdi' => '2',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );
        $actual = $changer->isAccountValidForProcessing($account);
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test isAccountValidForProcessing
     *
     * @covers AccountChange_PerformScheduledAccountChange::isAccountValidForProcessing
     *
     * @return void
     */

    public function testIsAccountValidForProcessingNewSdiNotSameAsCurrent()
    {
        $expected = true;
        $changer = new AccountChange_PerformScheduledAccountChange();
        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '3',
            'strOldProductName' => '',
            'intOldSdi' => '1',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );
        $actual = $changer->isAccountValidForProcessing($account);
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test isScheduledToBeChangedToday
     *
     * @covers AccountChange_PerformScheduledAccountChange::isScheduledToBeChangedToday
     * @covers AccountChange_PerformScheduledAccountChange::getScheduledAccountChanges
     * @return void
     */
    public function testIsScheduledToBeChangedToday()
    {
        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '3',
            'strOldProductName' => '',
            'intOldSdi' => '1',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'promoCode' => 'PROMOCODE',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getAccountsScheduledToChangeTodayUnrestricted',
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getAccountsScheduledToChangeTodayUnrestricted')
            ->will($this->returnValue(array($account)));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $actual = AccountChange_PerformScheduledAccountChange::isScheduledToBeChangedToday(12323);
        $this->assertTrue($actual);
    }

    /**
     * Test getScheduledAccountChanges
     *
     * @covers AccountChange_PerformScheduledAccountChange::getScheduledAccountChanges
     *
     * @return void
     */
    public function testgetScheduledAccountChangesCallsCorrectQueriesAndCombinesResults()
    {
        $account1 = array(
            'strNewProductName' => '',
            'intNewSdi' => '3',
            'strOldProductName' => '',
            'intOldSdi' => '1',
            'intServiceId' => '1234',
            'intCurrentSdi' => '1',
            'promoCode' => 'PROMOCODE',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );

        $account2 = array(
            'strNewProductName' => '',
            'intNewSdi' => '3',
            'strOldProductName' => '',
            'intOldSdi' => '1',
            'intServiceId' => '4321',
            'intCurrentSdi' => '1',
            'promoCode' => 'PROMOCODE',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getAccountsScheduledToChangeByServiceId',
                'getAccountsScheduledToChangeByAppointmentAndServiceId',
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getAccountsScheduledToChangeByServiceId')
            ->with(array(1234, 4321))
            ->will($this->returnValue(array($account1)));

        $objMockDbAdaptor->expects($this->any())
            ->method('getAccountsScheduledToChangeByAppointmentAndServiceId')
            ->with(array(1234, 4321))
            ->will($this->returnValue(array($account2)));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $result = AccountChange_PerformScheduledAccountChange::getScheduledAccountChanges(array(1234, 4321));
        $this->assertEquals(array($account1, $account2), $result);
    }

    /**
     * Test isScheduledToBeChangedToday
     *
     * @covers AccountChange_PerformScheduledAccountChange::isScheduledToBeChangedToday
     *
     * @return void
     */
    public function testIsNotScheduledToBeChangedToday()
    {
        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '3',
            'strOldProductName' => '',
            'intOldSdi' => '1',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'promoCode' => 'PROMOCODE',
            'dteChangeComplete' => '',
            'dteChangeDate' => ''
        );
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getAccountsScheduledToChangeToday'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getAccountsScheduledToChangeToday')
            ->will($this->returnValue(array($account)));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $actual = AccountChange_PerformScheduledAccountChange::isScheduledToBeChangedToday(12324);
        $this->assertFalse($actual);
    }

    /**
     * Test restoreAccountManager
     *
     * @covers AccountChange_PerformScheduledAccountChange::restoreAccountManager
     *
     * @return void
     */
    public function testRestoreAccountManager()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDao',
                'getC2MPromotionCode'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will(
            $this->returnValue(
                array(
                    'service_definition_id' => 1,
                    'name' => 'test',
                    'isp' => 'plus.net',
                    'minimum_charge' => '17.25',
                    'intTariffID' => 492,
                    'initial_charge' => 30,
                    'type' => 'Business',
                    'password_visible_to_support' => 'Yes',
                    'requires' => 'Yes',
                    'date_created' => '2009-08-07 15:38:40',
                    'end_date' => '2010-08-07 15:38:40',
                    'signup_via_portal' => 'yes',
                    'blurb' => 'yes',
                    'vchContract' => 'ANNUAL',
                    'strComponentName' => 'Test Product'
                )
            )
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'VALUE')));

        $objMockDbAdaptor->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockAccountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getTariffId',
                'getC2MPromotionCode'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getTariffId')
            ->will($this->returnValue(1));

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('AccountChange', $objMockAccountChangeDbAdaptor);

        $this->setMockHouseMovesDbAdaptor();

        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '3',
            'strOldProductName' => '',
            'intOldSdi' => '1',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'promoCode' => 'PROMOCODE',
            'dteChangeComplete' => '',
            'strContractLengthHandle' => 'ANNUAL',
            'dteChangeDate' => ''
        );

        //mock changer object
        $this->setUpAccountChangePerformScheduledAccountChangeUnitTestProxy();
        $changer = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_UnitTestProxy',
            array(
                'getBusinessActorId',
                'hasWlr',
                'getComponentIdForInternetConnection',
                'getMarketIdFromLineCheckResult',
                'getInternetConnectionId',
                'isScheduledHouseMove'
            ),
            array()
        );

        $changer->expects($this->any())
            ->method('getBusinessActorId')
            ->will($this->returnValue(5));

        $changer->expects($this->any())
            ->method('hasWlr')
            ->with(12323)
            ->will($this->returnValue(true));

        $changer
            ->expects($this->any())
            ->method('getComponentIdForInternetConnection')
            ->will($this->returnValue(array(355655)));

        $changer
            ->expects($this->any())
            ->method('getMarketIdFromLineCheckResult')
            ->will($this->returnValue(1));

        $changer
            ->expects($this->any())
            ->method('getInternetConnectionId')
            ->with(3, 1);

        $changer
            ->expects($this->any())
            ->method('isScheduledHouseMove')
            ->will($this->returnValue(false));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getMinimumCharge'),
            array()
        );
        $objCoreService->expects($this->any())
            ->method('getMinimumCharge')
            ->will($this->returnValue(12));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);
        $objCoreService->setType(6543);
        $changer->protected_setCoreService($objCoreService);

        $this->setUpCorrectLinecheckResult();
        $expected = 'AccountChange_Manager';

        $actual = get_class($changer->restoreAccountManager($account));

        $this->assertEquals($expected, $actual);
        $this->assertEquals(true, AccountChange_Registry::instance()->getEntry('bolActivationContract'));
        $this->assertEquals('PROMOCODE', AccountChange_Registry::instance()->getEntry('promoCode'));
        $this->assertNull(AccountChange_Registry::instance()->getEntry('retainDiscounts'));

        // set promoCode to empty string for the next execution and run again
        $account['promoCode'] = '';
        $changer->restoreAccountManager($account);
        $this->assertNull(AccountChange_Registry::instance()->getEntry('promoCode'));

        $this->tearDownLinecheckResult();
    }

    /**
     * Test restoreAccountManager
     *
     * @covers AccountChange_PerformScheduledAccountChange::restoreAccountManager
     * @covers AccountChange_PerformChangeBase::isReContractingOnSameProduct
     *
     * @return void
     */
    public function testRestoreAccountManagerSetsRetainDiscountsRegistryVariable()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        $oldWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(),
            '',
            false
        );

        $oldWlr->expects($this->once())
            ->method('getProductId')
            ->will($this->returnValue(1340));

        $newWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(),
            '',
            false
        );

        $newWlr->expects($this->once())
            ->method('getProductId')
            ->will($this->returnValue(1340));

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDao',
                'getC2MPromotionCode'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will(
            $this->returnValue(
                array(
                    'service_definition_id' => 1,
                    'name' => 'test',
                    'isp' => 'plus.net',
                    'minimum_charge' => '17.25',
                    'intTariffID' => 492,
                    'initial_charge' => 30,
                    'type' => 'Business',
                    'password_visible_to_support' => 'Yes',
                    'requires' => 'Yes',
                    'date_created' => '2009-08-07 15:38:40',
                    'end_date' => '2010-08-07 15:38:40',
                    'signup_via_portal' => 'yes',
                    'blurb' => 'yes',
                    'vchContract' => 'ANNUAL',
                    'strComponentName' => 'Test Product'
                )
            )
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'VALUE')));

        $objMockDbAdaptor->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockAccountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getTariffId',
                'getC2MPromotionCode'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getTariffId')
            ->will($this->returnValue(1));

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('AccountChange', $objMockAccountChangeDbAdaptor);

        $this->setMockHouseMovesDbAdaptor();

        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '6791',
            'strOldProductName' => '',
            'intOldSdi' => '6791',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'dteChangeComplete' => '',
            'strContractLengthHandle' => 'ANNUAL',
            'dteChangeDate' => ''
        );
        //mock changer object
        $this->setUpAccountChangePerformScheduledAccountChangeUnitTestProxy();
        $changer = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_UnitTestProxy',
            array(
                'getBusinessActorId',
                'hasWlr',
                'getComponentIdForInternetConnection',
                'getMarketIdFromLineCheckResult',
                'getInternetConnectionId',
                'isScheduledHouseMove'
            ),
            array()
        );

        $changer->expects($this->any())
            ->method('getBusinessActorId')
            ->will($this->returnValue(5));

        $changer->expects($this->any())
            ->method('hasWlr')
            ->with(12323)
            ->will($this->returnValue(true));

        $changer
            ->expects($this->any())
            ->method('getComponentIdForInternetConnection')
            ->will($this->returnValue(array()));

        $changer
            ->expects($this->any())
            ->method('getMarketIdFromLineCheckResult')
            ->will($this->returnValue(1));

        $changer
            ->expects($this->any())
            ->method('getInternetConnectionId')
            ->with(6791, 1);

        $changer
            ->expects($this->any())
            ->method('isScheduledHouseMove')
            ->will($this->returnValue(false));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getMinimumCharge', 'getServiceId', 'getCliNumber', 'getBroadbandContract'),
            array()
        );
        $objCoreService->expects($this->any())
            ->method('getMinimumCharge')
            ->will($this->returnValue(12));

        $objCoreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1110));

        $objCoreService->expects($this->any())
            ->method('getCliNumber')
            ->will($this->returnValue('***********'));

        $objCoreService->expects($this->any())
            ->method('getBroadbandContract')
            ->will($this->returnValue('CONTRACT'));

        $changer->protected_setCoreService($objCoreService);

        $this->setUpCorrectLinecheckResult();

        $wlrConfiguration = array(
            'objOldWlr' => $oldWlr,
            'objNewWlr' => $newWlr
        );

        $actual = $changer->restoreAccountManager($account, $wlrConfiguration);
        $this->assertInstanceOf('AccountChange_Manager', $actual);
        $this->assertNull(AccountChange_Registry::instance()->getEntry('promoCode'));
        $this->assertEquals(true, AccountChange_Registry::instance()->getEntry('retainDiscounts'));

        $this->tearDownLinecheckResult();
    }

    /**
     * Test restoreAccountManager
     *
     * @covers AccountChange_PerformScheduledAccountChange::restoreAccountManager
     * @covers AccountChange_PerformChangeBase::isReContractingOnSameProduct
     *
     * @return void
     */
    public function testRestoreAccountManagerSetsLineCheckResultRegistryVariable()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        $oldWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(),
            '',
            false
        );

        $oldWlr->expects($this->once())
            ->method('getProductId')
            ->will($this->returnValue(1340));

        $newWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(),
            '',
            false
        );

        $newWlr->expects($this->once())
            ->method('getProductId')
            ->will($this->returnValue(1340));

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDao'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will(
            $this->returnValue(
                array(
                    'service_definition_id' => 1,
                    'name' => 'test',
                    'isp' => 'plus.net',
                    'minimum_charge' => '17.25',
                    'intTariffID' => 492,
                    'initial_charge' => 30,
                    'type' => 'Business',
                    'password_visible_to_support' => 'Yes',
                    'requires' => 'Yes',
                    'date_created' => '2009-08-07 15:38:40',
                    'end_date' => '2010-08-07 15:38:40',
                    'signup_via_portal' => 'yes',
                    'blurb' => 'yes',
                    'vchContract' => 'ANNUAL',
                    'strComponentName' => 'Test Product'
                )
            )
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'VALUE')));

        $objMockDbAdaptor->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockAccountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getTariffId',
                'getC2MPromotionCode'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getTariffId')
            ->will($this->returnValue(1));

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('AccountChange', $objMockAccountChangeDbAdaptor);

        $this->setMockHouseMovesDbAdaptor();

        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '6791',
            'strOldProductName' => '',
            'intOldSdi' => '6791',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'dteChangeComplete' => '',
            'strContractLengthHandle' => 'ANNUAL',
            'dteChangeDate' => ''
        );
        //mock changer object
        $this->setUpAccountChangePerformScheduledAccountChangeUnitTestProxy();
        $changer = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_UnitTestProxy',
            array(
                'getBusinessActorId',
                'hasWlr',
                'getComponentIdForInternetConnection',
                'getMarketIdFromLineCheckResult',
                'getInternetConnectionId',
                'isScheduledHouseMove'
            ),
            array()
        );

        $changer->expects($this->any())
            ->method('getBusinessActorId')
            ->will($this->returnValue(5));

        $changer->expects($this->any())
            ->method('hasWlr')
            ->with(12323)
            ->will($this->returnValue(true));

        $changer
            ->expects($this->any())
            ->method('getComponentIdForInternetConnection')
            ->will($this->returnValue(355655));

        $changer
            ->expects($this->any())
            ->method('getMarketIdFromLineCheckResult')
            ->will($this->returnValue(1));

        $changer
            ->expects($this->any())
            ->method('getInternetConnectionId')
            ->with(6791, 1);

        $changer
            ->expects($this->any())
            ->method('isScheduledHouseMove')
            ->will($this->returnValue(false));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getMinimumCharge'),
            array()
        );
        $objCoreService->expects($this->any())
            ->method('getMinimumCharge')
            ->will($this->returnValue(12));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);
        $objCoreService->setType(6543);
        $changer->protected_setCoreService($objCoreService);

        $this->setUpCorrectLinecheckResult();

        $wlrConfiguration = array(
            'objOldWlr' => $oldWlr,
            'objNewWlr' => $newWlr
        );

        $actual = $changer->restoreAccountManager($account, $wlrConfiguration);
        $this->assertInstanceOf('AccountChange_Manager', $actual);
        $this->assertInstanceOf('LineCheck_Result', AccountChange_Registry::instance()->getEntry('lineCheckResult'));

        $this->tearDownLinecheckResult();
    }

    /**
     * Test testRestoreAccountManagerSetsC2MPromotionCodeRegistryVariable
     *
     * @covers AccountChange_PerformScheduledAccountChange::restoreAccountManager
     * @covers AccountChange_PerformChangeBase::isReContractingOnSameProduct
     *
     * @return void
     */
    public function testRestoreAccountManagerSetsC2MPromotionCodeRegistryVariable()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        $oldWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(),
            '',
            false
        );

        $oldWlr->expects($this->once())
            ->method('getProductId')
            ->will($this->returnValue(1340));

        $newWlr = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductId'),
            array(),
            '',
            false
        );

        $newWlr->expects($this->once())
            ->method('getProductId')
            ->will($this->returnValue(1340));

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDao'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will(
                $this->returnValue(
                    array(
                        'service_definition_id' => 1,
                        'name' => 'test',
                        'isp' => 'plus.net',
                        'minimum_charge' => '17.25',
                        'intTariffID' => 492,
                        'initial_charge' => 30,
                        'type' => 'Business',
                        'password_visible_to_support' => 'Yes',
                        'requires' => 'Yes',
                        'date_created' => '2009-08-07 15:38:40',
                        'end_date' => '2010-08-07 15:38:40',
                        'signup_via_portal' => 'yes',
                        'blurb' => 'yes',
                        'vchContract' => 'ANNUAL',
                        'strComponentName' => 'Test Product'
                    )
                )
            );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objMockAccountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getTariffId',
                'getC2MPromotionCode'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getTariffId')
            ->will($this->returnValue(1));

        $c2mData = array(
            "promotionCode" => "c2mPromoCode1",
            "contractLengthMonths" => "12"
        );

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue($c2mData));

        Db_Manager::setAdaptor('AccountChange', $objMockAccountChangeDbAdaptor);

        $this->setMockHouseMovesDbAdaptor();

        $account = array(
            'strNewProductName' => '',
            'intNewSdi' => '6791',
            'strOldProductName' => '',
            'intOldSdi' => '6791',
            'intServiceId' => '12323',
            'intCurrentSdi' => '1',
            'dteChangeComplete' => '',
            'strContractLengthHandle' => 'ANNUAL',
            'dteChangeDate' => ''
        );
        //mock changer object
        $this->setUpAccountChangePerformScheduledAccountChangeUnitTestProxy();
        $changer = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_UnitTestProxy',
            array(
                'getBusinessActorId',
                'hasWlr',
                'getComponentIdForInternetConnection',
                'getMarketIdFromLineCheckResult',
                'getInternetConnectionId',
                'isScheduledHouseMove'
            ),
            array()
        );

        $changer->expects($this->any())
            ->method('getBusinessActorId')
            ->will($this->returnValue(5));

        $changer->expects($this->any())
            ->method('hasWlr')
            ->with(12323)
            ->will($this->returnValue(true));

        $changer
            ->expects($this->any())
            ->method('getComponentIdForInternetConnection')
            ->will($this->returnValue(355655));

        $changer
            ->expects($this->any())
            ->method('getMarketIdFromLineCheckResult')
            ->will($this->returnValue(1));

        $changer
            ->expects($this->any())
            ->method('getInternetConnectionId')
            ->with(6791, 1);

        $changer
            ->expects($this->any())
            ->method('isScheduledHouseMove')
            ->will($this->returnValue(false));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getMinimumCharge'),
            array()
        );
        $objCoreService->expects($this->any())
            ->method('getMinimumCharge')
            ->will($this->returnValue(12));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);
        $objCoreService->setType(6543);
        $changer->protected_setCoreService($objCoreService);

        $this->setUpCorrectLinecheckResult();

        $wlrConfiguration = array(
            'objOldWlr' => $oldWlr,
            'objNewWlr' => $newWlr
        );

        $actual = $changer->restoreAccountManager($account, $wlrConfiguration);
        $this->assertInstanceOf('AccountChange_Manager', $actual);
        $this->assertEquals('c2mPromoCode1', AccountChange_Registry::instance()->getEntry('promoCode'));
        $this->assertEquals(true, AccountChange_Registry::instance()->getEntry('c2mPromotion'));
        $this->assertEquals('12', AccountChange_Registry::instance()->getEntry('contractLengthMonths'));

        $this->tearDownLinecheckResult();
    }

    /**
     * Test setCoreService
     *
     * @covers AccountChange_PerformScheduledAccountChange::setCoreService
     * @covers AccountChange_PerformScheduledAccountChange::getCoreService
     *
     * @return void
     */
    public function testSetCoreService()
    {
        $changer = TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array());
        $coreService = new Core_Service();
        $changer->protected_setCoreService($coreService);
        $coreServiceSet = $changer->protected_getCoreService();
        $this->assertEquals($coreService, $coreServiceSet);
    }

    /**
     * Test checkAndAddSid
     *
     * @covers AccountChange_PerformScheduledAccountChange::checkAndAddSid
     *
     * @return void
     */
    public function testCheckAndAddSidValid()
    {
        $changer = TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array());
        $expected = true;
        $actual = $changer->protected_checkAndAddSid(199);

        $this->assertEquals($expected, $actual);
        $expected = array(199 => 199);
        $actual = $changer->getRestrictTo();
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test checkAndAddSid
     *
     * @covers AccountChange_PerformScheduledAccountChange::checkAndAddSid
     *
     * @return void
     */
    public function testCheckAndAddSidInValid()
    {
        $changer = TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array());
        $expected = false;
        $actual = $changer->protected_checkAndAddSid('ss');

        $this->assertEquals($expected, $actual);

        //empty array
        $expected = array();
        $actual = $changer->getRestrictTo();
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test processCommandLineArgs
     *
     * @covers AccountChange_PerformScheduledAccountChange::processCommandLineArgs
     *
     * @return void
     */
    public function testProcessCommandLineArgsEmpty()
    {
        $changer = new AccountChange_PerformScheduledAccountChange();

        $changer->processCommandLineArgs(array());
        $actual = $changer->getRestrictTo();
        $expected = array();
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test processCommandLineArgs
     *
     * @covers AccountChange_PerformScheduledAccountChange::processCommandLineArgs
     *
     * @return void
     */
    public function testProcessCommandLineArgsSingleVal()
    {
        $changer = new AccountChange_PerformScheduledAccountChange();

        $changer->processCommandLineArgs(array('s' => '1'));
        $actual = $changer->getRestrictTo();
        $expected = array('1' => '1');
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test processCommandLineArgs
     *
     * @covers AccountChange_PerformScheduledAccountChange::processCommandLineArgs
     *
     * @return void
     */
    public function testProcessCommandLineArgsMultiVals()
    {
        $changer = new AccountChange_PerformScheduledAccountChange();

        $changer->processCommandLineArgs(array('s' => array('1', '123')));
        $actual = $changer->getRestrictTo();
        $expected = array('1' => '1', '123' => '123');
        $this->assertEquals($expected, $actual);
    }

    /**
     * Test getLinecheckData
     *
     * @covers AccountChange_PerformScheduledAccountChange::getLinecheckData
     *
     * @return void
     */
    public function testGetLinecheckDataLinecheckOk()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        //mock linecheck

        $this->setUpCorrectLinecheckResult();
        $changer = new AccountChange_PerformScheduledAccountChange();
        $actual = $changer->getLinecheckData(1);
        $this->assertInternalType('array', $actual);
        $this->assertInstanceOf('LineCheck_Result', $actual['objLineCheckResult']);
        $this->assertInternalType('boolean', $actual['bolLinecheckOptOut']);
        $this->assertFalse($actual['bolLinecheckOptOut']);
    }

    /**
     * Test getLinecheckData
     *
     * @covers AccountChange_PerformScheduledAccountChange::getLinecheckData
     *
     * @return void
     */
    public function testGetLinecheckDataLinecheckFail()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');

        // Mock Db_Adaptor
        $result = array(
            'intLineCheckId' => 2,
            'intLineCheckStatusId' => 1,
            'intServiceId' => 2,
            'intServiceDefinitionId' => 1,
            'intErrorId' => 1,
            'vchLineCheckInput' => '***********',
            'vchLineCheckType' => 'TELNO',
            'ch.dtmLineCheckDate' => '2009-03-26 18:42:43',
            'chrReasonCode' => 'L',
            'vchExchangeName' => 'LSSID',
            'vchExchangeCode' => 'LSSID',
            'chrFixedRateRag' => 'G',
            'dteFixedRateReadyDate' => '',
            'chrFixedRateExchState' => 'E',
            'chrRateAdaptiveRag' => 'G',
            'dteRateAdaptiveReadyDate' => '',
            'chrRateAdaptiveExchState' => 'E',
            'chrMaxRag' => 'G',
            'intMaxSpeed' => 4500,
            'dteMaxReadyDate' => '',
            'chrMaxExchState' => 'E',
            'chrWbcRag' => 'G',
            'intWbcSpeed' => 6000,
            'dteWbcReadyDate' => '',
            'chrWbcExchState' => 'E',
            'vchPostCode' => 'DA5 3EQ'
        );

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getPendingLineCheckResult', 'getMaxLineCheckDetailsMGALS', 'getWbcLineCheckDetailsMGALS', 'getFttcLineCheckDetailsMGALS'),
            array('LineCheck', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getPendingLineCheckResult')
            ->will($this->returnValue($result));

        $objMockDbAdaptor->expects($this->any())
            ->method('getMaxLineCheckDetailsMGALS')
            ->will($this->returnValue(null));

        $objMockDbAdaptor->expects($this->any())
            ->method('getWbcLineCheckDetailsMGALS')
            ->will($this->returnValue(null));

        $objMockDbAdaptor->expects($this->any())
            ->method('getFttcLineCheckDetailsMGALS')
            ->will($this->returnValue(null));


        Db_Manager::setAdaptor('LineCheck', $objMockDbAdaptor);

        $changer = new AccountChange_PerformScheduledAccountChange();
        $actual = $changer->getLinecheckData(2);

        $this->assertInternalType('array', $actual);
        $this->assertInstanceOf('LineCheck_Result', $actual['objLineCheckResult']);
        $this->assertInternalType('boolean', $actual['bolLinecheckOptOut']);
        $this->assertTrue($actual['bolLinecheckOptOut']);
        $this->tearDownLinecheckResult();
    }

    /**
     * AccountChange_PerformScheduledAccountChange::restoreEmailData
     *
     * @medium
     * @return void
     */
    public function testRestoreEmailDataValue()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        //mock changer object
        $this->setUpAccountChangePerformScheduledAccountChangeUnitTestProxy();
        $changer = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_UnitTestProxy',
            array('getUserdataGetPrices'),
            array()
        );

        $changer->expects($this->any())
            ->method('getUserdataGetPrices')
            ->will($this->returnValue(array('subscription' => array('ongoing' => 1, 'lead' => 2))));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getMinimumCharge'),
            array()
        );

        $objCoreService->expects($this->any())
            ->method('getMinimumCharge')
            ->will($this->returnValue(12));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);
        $objCoreService->setType(6543);
        $changer->protected_setCoreService($objCoreService);

        //mock getServiceDefinitionDao
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $this->setUpCorrectLinecheckResult();

        $actual = $changer->protected_restoreEmailData('test product name', 1);

        $this->assertEquals(1, $actual['intDiscountLength']);
        $this->assertEquals(2, $actual['objDiscountedProductCost']->toDecimal());
        $this->assertEquals(1, $actual['objOngoingProductCost']->toDecimal());
        $this->assertEquals('test product name', $actual['arrSelectedBroadband']['strNewProduct']);

        $this->tearDownLinecheckResult();
    }

    /**
     * AccountChange_PerformScheduledAccountChange::restoreEmailData
     *
     * @return void
     */
    public function testRestoreEmailDataNoValue()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        //mock changer object
        $this->setUpAccountChangePerformScheduledAccountChangeUnitTestProxy();
        $changer = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_UnitTestProxy',
            array('getUserdataGetPrices'),
            array()
        );

        $changer->expects($this->any())
            ->method('getUserdataGetPrices')
            ->will($this->returnValue(array('subscription' => array('ongoing' => 1, 'lead' => 2))));

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getMinimumCharge'),
            array()
        );
        $objCoreService->expects($this->any())
            ->method('getMinimumCharge')
            ->will($this->returnValue(12));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1111);
        $objCoreService->setType(6542);
        $changer->protected_setCoreService($objCoreService);

        //mock getServiceDefinitionDao
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'BBYW')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $this->setUpCorrectLinecheckResult();

        $actual = $changer->protected_restoreEmailData('test product name', 0);

        $this->assertEquals(0, $actual['intDiscountLength']);
        $this->assertNull($actual['objDiscountedProductCost']);
        $this->assertEquals(12, $actual['objOngoingProductCost']->toDecimal());
        $this->assertEquals('test product name', $actual['arrSelectedBroadband']['strNewProduct']);

        $this->tearDownLinecheckResult();
    }
    /**
     * Set up a correct linecheck result
     *
     * @return void
     */
    private function setUpCorrectLinecheckResult()
    {
        $result = array(
            'intLineCheckId' => 1,
            'intLineCheckStatusId' => 1,
            'intServiceId' => 1,
            'intServiceDefinitionId' => 1,
            'intErrorId' => 0,
            'vchLineCheckInput' => '***********',
            'vchLineCheckType' => 'TELNO',
            'ch.dtmLineCheckDate' => '2009-03-26 18:42:43',
            'chrReasonCode' => 'L',
            'vchExchangeName' => 'LSSID',
            'vchExchangeCode' => 'LSSID',
            'chrFixedRateRag' => 'G',
            'dteFixedRateReadyDate' => '',
            'chrFixedRateExchState' => 'E',
            'chrRateAdaptiveRag' => 'G',
            'dteRateAdaptiveReadyDate' => '',
            'chrRateAdaptiveExchState' => 'E',
            'chrMaxRag' => 'G',
            'intMaxSpeed' => 4500,
            'dteMaxReadyDate' => '',
            'chrMaxExchState' => 'E',
            'chrWbcRag' => 'G',
            'intWbcSpeed' => 6000,
            'dteWbcReadyDate' => '',
            'chrWbcExchState' => 'E',
            'vchPostCode' => 'DA5 3EQ'
        );
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getPendingLineCheckResult', 'getMaxLineCheckDetailsMGALS', 'getWbcLineCheckDetailsMGALS', 'getFttcLineCheckDetailsMGALS'),
            array('LineCheck', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getPendingLineCheckResult')
            ->will($this->returnValue($result));

        $objMockDbAdaptor->expects($this->any())
            ->method('getMaxLineCheckDetailsMGALS')
            ->will($this->returnValue(null));

        $objMockDbAdaptor->expects($this->any())
            ->method('getWbcLineCheckDetailsMGALS')
            ->will($this->returnValue(null));

        $objMockDbAdaptor->expects($this->any())
            ->method('getFttcLineCheckDetailsMGALS')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('LineCheck', $objMockDbAdaptor);
    }

    /**
     * Tear down linecheck result
     *
     * @return void
     */
    private function tearDownLinecheckResult()
    {
        Db_Manager::setAdaptor('LineCheck', new Db_Adaptor('test', 'test', false));
    }

    /**
     * A method to set-up account changer test unit proxy
     *
     * @return void
     */
    private function setUpAccountChangePerformScheduledAccountChangeUnitTestProxy()
    {
        if (!class_exists('AccountChange_PerformScheduledAccountChange_UnitTestProxy', false)) {
            $strClassDefinition = <<<CLASS
                class AccountChange_PerformScheduledAccountChange_UnitTestProxy
                    extends AccountChange_PerformScheduledAccountChange
                {
                    public function __call(\$strFunctionName, \$arrParams)
                    {
                        \$strFunctionName = str_replace('protected_', '', \$strFunctionName);

                        return call_user_func_array(array(&\$this, \$strFunctionName), \$arrParams);
                    }
                }
CLASS;

            eval($strClassDefinition);
        }
    }

    /**
     * Test accountChangeStartForumPosting
     *
     * @covers AccountChange_PerformScheduledAccountChange::accountChangeStartForumPosting
     *
     * @return void
     */
    public function testAccountChangeStartForumPosting()
    {

        TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array(), '_Proxy');
        $objMockPerformScheduledAccountChange = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_Proxy',
            array('getForumPosterId', 'createForumTopicAndPostMessage')
        );

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getForumPosterId')
            ->will($this->returnValue("********************************"));

        $strHostname = gethostname();
        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('createForumTopicAndPostMessage')
            ->will($this->returnValue(12))
            ->with(
            $this->equalTo('Account Change Script Run'),
            $this->equalTo(
                "Account change has started on host $strHostname.\n\nDate: 2010-01-06\n\n123 records to process"
            ),
            $this->equalTo('********************************')
        );

        $intTopicId = $objMockPerformScheduledAccountChange->protected_accountChangeStartForumPosting(
            "2010-01-06",
            123
        );

        $this->assertEquals($intTopicId, 12);
    }

    /**
     * Test accountChangeEndForumPosting
     *
     * @covers AccountChange_PerformScheduledAccountChange::accountChangeEndForumPosting
     *
     * @return void
     */
    public function testAccountChangeEndForumPostingFailureAboveThreshold()
    {
        TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array(), '_Proxy');
        $objMockPerformScheduledAccountChange = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_Proxy',
            array('getForumPosterId', 'getFailuresCounter', 'createForumMessage')
        );

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getForumPosterId')
            ->will($this->returnValue("********************************"));

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getFailuresCounter')
            ->will($this->returnValue(20));

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('createForumMessage')
            ->will($this->returnValue(true))
            ->with(
            $this->equalTo('Account Change processing run failed to complete'),
            $this->stringStartsWith(
                'The Account Change process has finished but failed to complete' .
                "\n\nFAILURE THRESHOLD (10) WAS MET\n** SCRIPT DID NOT COMPLETE **\n\n"
            ),
            $this->equalTo(123),
            $this->equalTo('********************************')
        );

        $intForumMessageId = $objMockPerformScheduledAccountChange->protected_accountChangeEndForumPosting(
            "2010-01-06",
            123,
            123,
            1234
        );

        $this->assertEquals(true, $intForumMessageId);
    }

    /**
     * Test accountChangeEndForumPosting
     *
     * @covers AccountChange_PerformScheduledAccountChange::accountChangeEndForumPosting
     *
     * @return void
     */
    public function testAccountChangeEndForumPostingFailureBelowThreshold()
    {
        TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array(), '_Proxy');
        $objMockPerformScheduledAccountChange = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_Proxy',
            array('getForumPosterId', 'getFailuresCounter', 'createForumMessage')
        );

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getForumPosterId')
            ->will($this->returnValue("********************************"));

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getFailuresCounter')
            ->will($this->returnValue(1));

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('createForumMessage')
            ->will($this->returnValue(true))
            ->with(
            $this->equalTo('Account Change processing run complete'),
            $this->stringStartsWith(
                "The Account Change process has finished\n" .
                "Date: 2010-01-06\n" .
                "Total number of accounts processed: 123\n" .
                "Accounts successfully processed: 122\n" .
                "Accounts which were not successfully processed: 1\n\n" .
                "List of failures: \n\n"
            ),
            $this->equalTo(123),
            $this->equalTo('********************************')
        );

        $intForumMessageId = $objMockPerformScheduledAccountChange->protected_accountChangeEndForumPosting(
            "2010-01-06",
            123,
            123,
            1234
        );

        $this->assertEquals(true, $intForumMessageId);
    }

    /**
     * Test accountChangeEndForumPosting
     *
     * @covers AccountChange_PerformScheduledAccountChange::accountChangeEndForumPosting
     *
     * @return void
     */
    public function testAccountChangeEndForumPostingNoFailures()
    {
        TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array(), '_Proxy');
        $objMockPerformScheduledAccountChange = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_Proxy',
            array('getForumPosterId', 'getFailuresCounter', 'createForumMessage')
        );

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getForumPosterId')
            ->will($this->returnValue("********************************"));

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getFailuresCounter')
            ->will($this->returnValue(0));

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('createForumMessage')
            ->will($this->returnValue(true))
            ->with(
            $this->equalTo('Account Change processing run complete'),
            $this->equalTo(
                "The Account Change process has finished\n" .
                "Date: 2010-01-06\n" .
                "Total number of accounts processed: 123\n" .
                "Accounts successfully processed: 123\n" .
                "Accounts which were not successfully processed: 0\n\n"
            ),
            $this->equalTo(123),
            $this->equalTo('********************************')
        );

        $intForumMessageId = $objMockPerformScheduledAccountChange->protected_accountChangeEndForumPosting(
            "2010-01-06",
            123,
            123,
            1234
        );

        $this->assertEquals(true, $intForumMessageId);
    }

    /**
     * Test createForumMessage
     *
     * @covers AccountChange_PerformScheduledAccountChange::createForumMessage
     *
     * @return void
     */
    public function testCreateForumMessage()
    {
        TestCaseWithProxy::getPHPUnitProxy('AccountChange_PerformScheduledAccountChange', array(), '_Proxy');
        $objMockPerformScheduledAccountChange = $this->getMock(
            'AccountChange_PerformScheduledAccountChange_Proxy',
            array('getActorByExternalUserId')
        );

        $mockActor = $this->getMock('Auth_BusinessActor', array(), array(), '', false);
        $mockKirin = $this->getMock('ForumsClient_KirinTopic', array(), array(), '', false);

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue($mockActor));

        $objMessage = new ForumsClient_KirinMessage();

        $client = $this->getMock(
            'ForumsClient_DbForumsClient',
            array('getNewMessage', 'getTopic', 'postReply'),
            array()
        );

        $client->expects($this->once())
            ->method('getNewMessage')
            ->will($this->returnValue($objMessage));

        $client->expects($this->once())
            ->method('getTopic')
            ->will($this->returnValue($mockKirin));

        $client->expects($this->once())
            ->method('postReply');

        BusTier_BusTier::setClient('forums', $client);

        $result = $objMockPerformScheduledAccountChange->createForumMessage(
            "announcements",
            "Hello All",
            12,
            '********************************'
        );

        $this->assertEquals($result, true);
    }

    /**
     * Test createForumTopicAndpostMessage
     *
     * @covers AccountChange_PerformScheduledAccountChange::createForumTopicAndpostMessage
     *
     * @return void
     */
    public function testCreateForumTopicAndPostMessage()
    {
        $objMockPerformScheduledAccountChange = $this->getMock(
            'AccountChange_PerformScheduledAccountChange',
            array('getActorByExternalUserId'),
            array(),
            '',
            false
        );
        $mockActor = $this->getMock('Auth_BusinessActor', array(), array(), '', false);
        $mockKirin = $this->getMock(
            'ForumsClient_KirinTopic',
            array('getTopicId'),
            array(),
            '',
            false
        );

        $objMockPerformScheduledAccountChange->expects($this->once())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue($mockActor));

        $objMessage = new ForumsClient_KirinMessage();

        $client = $this->getMock(
            'ForumsClient_DbForumsClient',
            array('getNewMessage', 'postMessage'),
            array()
        );

        $mockKirin->expects($this->once())
            ->method('getTopicId')
            ->will($this->returnValue(12));

        $client->expects($this->once())
            ->method('getNewMessage')
            ->will($this->returnValue($objMessage));

        $client->expects($this->once())
            ->method('postMessage')
            ->will($this->returnValue($mockKirin));

        BusTier_BusTier::setClient('forums', $client);

        $result = $objMockPerformScheduledAccountChange->createForumTopicAndPostMessage(
            "announcements",
            "Hello All",
            '********************************'
        );
        $this->assertEquals($result, 12);
    }

    /**
     * Test maintainTimestampFile
     *
     * @covers AccountChange_PerformScheduledAccountChange::maintainTimestampFile
     *
     * @return void
     */
    public function testMaintainTimestampFile()
    {
        $this->setUpVfsStream();
        $strLockFile = 'timestamp_file';
        $vfsRoot = vfsStreamWrapper::getRoot();
        $utxNow = strtotime('now');
        $vfsFileLock = vfsStream::newFile($strLockFile);
        $vfsRoot->addChild($vfsFileLock);
        $this->assertTrue($vfsRoot->hasChild($strLockFile));

        $vfsFileLock->lastModified($utxNow);
        $this->assertEquals($utxNow, $vfsFileLock->filemtime());
    }

    /**
     * Sets up vfsStream. It will skipp a test if vfsStream is not available.
     *
     * @return bool
     */
    protected function setUpVfsStream()
    {
        vfsStreamWrapper::register();
        vfsStreamWrapper::setRoot(new vfsStreamDirectory('tmp'));
    }

    /**
     * Check that the correct value is returned by getShortOpts
     *
     * @covers AccountChange_PerformScheduledAccountChange::getShortOpts
     *
     * @return void
     */
    public function testGetShortOpts()
    {
        $expected = 'qs:';
        $actual = AccountChange_PerformScheduledAccountChange::getShortOpts();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Check that the correct value is returned by getLongOpts
     *
     * @covers AccountChange_PerformScheduledAccountChange::getLongOpts
     *
     * @return void
     */
    public function testGetLongOpts()
    {
        $expected = array('runcbc', 'quiet', 'debug');
        $actual = AccountChange_PerformScheduledAccountChange::getLongOpts();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Tests a controlled run of the whole script
     *
     * @param int   $serviceId                        Service Id to be processed
     * @param array $opts                             The options to be passed to the script
     * @param bool  $runcbc                           Indicates that CBC should be ran
     * @param bool  $retentionDiscountsApplied        Indicates whether retention discounts were applied correctly
     * @param bool  $retentionDiscountsThrowException Indicates whether applying retention discounts throws exception
     *
     * @covers AccountChange_PerformScheduledAccountChange::run
     * @covers AccountChange_PerformScheduledAccountChange::processCommandLineArgs
     * @covers AccountChange_PerformScheduledAccountChange::getScheduledAccountChanges
     * @covers AccountChange_PerformScheduledAccountChange::generateCbcServiceBill
     * @covers AccountChange_PerformScheduledAccountChange::getCbcScriptToRunForService
     * @covers AccountChange_PerformScheduledAccountChange::isAccountValidForProcessing
     * @covers AccountChange_PerformScheduledAccountChange::getDiscountLength
     * @covers AccountChange_PerformScheduledAccountChange::restoreEmailData
     * @covers AccountChange_PerformScheduledAccountChange::completeServiceChangeSchedule
     *
     * @dataProvider provideDataForTestRun
     * @medium
     *
     * @return void
     */
    public function testRun(
        $serviceId,
        $opts,
        $runcbc,
        $retentionDiscountsApplied,
        $changeAccountThrowException,
        $retentionDiscountsThrowException,
        $coreServiceForServiceIdThrowException,
        $accountHasOpenMopOrders,
        $accountHasHouseMove,
        $storedContractType,
        $sdis
    ) {
        $mockChanger = $this->getMock(
            'AccountChange_PerformScheduledAccountChange',
            array(
                'output',
                'includeLegacyFiles',
                'accountChangeStartForumPosting',
                'getCoreServiceForServiceId',
                'getCoreServiceDefinition',
                'getTechnologyTypeFromSdi',
                'restoreEmailData',
                'accountChangeEndForumPosting',
                'maintainTimestampFile',
                'getCustomerExistingContractStartDate',
                'getBlockedAccountChangeEmailProductNames',
                'executeAndReturnCode',
                'restoreAccountManager',
                'applyPendingRetentionDiscounts',
                'addToBillingExclusion',
                'isExcludedFromBilling',
                'hasOpenMopOrder',
                'isScheduledHouseMove',
                'recreateEngineerChargingScheduledPayments',
                'errorLog',
                'flushOutputBuffer',
                'getC2MPromoCode',
                'determineContractType',
                'isReContractingOnSameProduct',
                'isFttpProduct',
                'isSogeaProduct'
            )
        );

        $mockMGALSHelper = $this->getMock(
            'AccountChange_MGALSHelper',
            ['makeScheduledMGALSLiveForServiceId'],
            [],
            '',
            false
        );
        AccountChange_ServiceManager::setService('MgalsHelper', $mockMGALSHelper);

        $mockChanger
            ->expects($this->any())
            ->method('getMGALSHelper')
            ->willReturn($mockMGALSHelper);

        $mockMGALSHelper
            ->expects($this->any())
            ->method('makeScheduledMGALSLiveForServiceId');

        $mockChanger
            ->expects($this->once())
            ->method('flushOutputBuffer');

        $mockChanger
            ->expects($this->any())
            ->method('getC2MPromoCode')
            ->will($this->returnValue(null));

        $mockChanger
            ->expects($this->any())
            ->method('isReContractingOnSameProduct')
            ->will($this->returnValue(false));

        $mockIES = $this->getMock(
            '\Plusnet\InventoryEventClient\Service\EventService',
            array(
                'addSubscriber',
                'takePreChangeSnapshot',
                'takePostChangeSnapshot'
            ),
            array(),
            '',
            true
        );

        BusTier_BusTier::setClient('inventoryEventService', $mockIES);

        $mockChanger->setDoDebugOutput(false);

        $mockDb = $this->getMock(
            'Db_Adaptor',
            array(
                'getCbcBillingPeriodForService',
                'getAccountsScheduledToChangeByServiceId',
                'getAccountsScheduledToChangeByAppointmentAndServiceId',
                'completeServiceChangeSchedule',
                'getC2MPromotionCode',
                'isAdslToFttcAccountChangeByScheduleId'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockCoreService = $this->getMock(
            'Core_Service',
            array(
                'getServiceId',
                'getType'
            )
        );

        $mockContractDto = $this->getMock(
            '\Plusnet\ContractsClient\Entity\ContractTypeDto',
            array('getContractType','getContractSubType'),
            array(),
            '',
            false
        );

        $mockCoreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        $mockCoreService->expects($this->any())
            ->method('getType');

        $mockCoreServiceDefintion = $this->getMock(
            'Core_ServiceDefinition',
            array('isBusiness')
        );

        $mockCoreServiceDefintion->expects($this->any())
            ->method('isBusiness')
            ->will($this->returnValue(false));

        $mockAccountConfig = $this->getMock(
            'AccountChange_AccountConfiguration',
            array(),
            array(array())
        );

        $mockManager = $this->getMock(
            'AccountChange_Manager',
            array('changeAccount', 'sendConfirmationEmails'),
            array($serviceId, $mockAccountConfig, $mockAccountConfig)
        );

        $mockChanger->expects($this->any())
            ->method('output');

        $mockChanger->expects($this->once())
            ->method('hasOpenMopOrder')
            ->will($this->returnValue($accountHasOpenMopOrders));

        $mockChanger->expects($this->any())
             ->method('isScheduledHouseMove')
             ->will($this->returnValue($accountHasHouseMove));

        $mockChanger->expects($this->once())
            ->method('includeLegacyFiles');

        $mockChanger->expects($this->once())
            ->method('accountChangeStartForumPosting');

        $mockChanger->expects($this->once())
            ->method('accountChangeEndForumPosting');

        $mockChanger->expects($this->once())
            ->method('maintainTimestampFile');

        $mockChanger->expects($this->any())
            ->method('recreateEngineerChargingScheduledPayments');

        if ($accountHasOpenMopOrders) {
            // mop orders dont get processed so dont expect stuff...
            $mockIES->expects($this->never())
                ->method('addSubscriber');

            $mockIES->expects($this->never())
                ->method('takePreChangeSnapshot');

            $mockIES->expects($this->never())
                ->method('takePostChangeSnapshot');
        } elseif ($accountHasHouseMove) {
             // house move orders dont get processed
             $mockIES->expects($this->never())
                ->method('takePreChangeSnapshot');

             $mockIES->expects($this->never())
                ->method('takePostChangeSnapshot');
        } elseif ($changeAccountThrowException ||
            $retentionDiscountsThrowException ||
            $coreServiceForServiceIdThrowException) {
            $mockIES->expects($this->once())
                ->method('takePreChangeSnapshot')
                ->with($serviceId);

            $mockIES->expects($this->never())
                ->method('takePostChangeSnapshot')
                ->with($serviceId);

            if ($coreServiceForServiceIdThrowException) {
                $mockChanger->expects($this->any())
                    ->method('getCoreServiceForServiceId')
                    ->will($this->throwException(new Exception("Exception generated for unit testing in getCoreServiceForServiceId")));
            } else {
                $mockChanger->expects($this->once())
                    ->method('getCoreServiceForServiceId')
                    ->with($serviceId)
                    ->will($this->returnValue($mockCoreService));
                $mockChanger->expects($this->any())
                    ->method('getCoreServiceDefinition')
                    ->will($this->returnValue($mockCoreServiceDefintion));
            }

            if ($changeAccountThrowException) {
                $mockManager->expects($this->any())
                    ->method('changeAccount')
                    ->will($this->throwException(new Exception("Exception generated for unit testing in changeAccount")));
                $mockChanger->expects($this->once())
                    ->method('errorLog');

                $mockChanger
                    ->expects($this->exactly(2))
                    ->method('getTechnologyTypeFromSdi')
                    ->willReturnOnConsecutiveCalls($sdis['oldSdi'], $sdis['newSdi']);

                $shouldPopulateSdis = true;
            }

            $mockChanger->expects($this->once())
                ->method('addToBillingExclusion');
        } else {
            $mockIES->expects($this->once())
                ->method('takePreChangeSnapshot')
                ->with($serviceId);

            $mockIES->expects($this->once())
                ->method('takePostChangeSnapshot')
                ->with($serviceId);

            $mockChanger->expects($this->exactly(2))
                ->method('getCoreServiceForServiceId')
                ->with($serviceId)
                ->will($this->returnValue($mockCoreService));

            $mockChanger->expects($this->any())
                ->method('getCoreServiceDefinition')
                ->will($this->returnValue($mockCoreServiceDefintion));

            $mockChanger->expects($this->once())
                ->method('getCustomerExistingContractStartDate')
                ->with($serviceId, 'INTERNET_CONNECTION')
                ->will($this->returnValue(false));

            $mockChanger->expects($this->once())
                ->method('getBlockedAccountChangeEmailProductNames')
                ->will($this->returnValue(array()));

            $mockChanger->expects($this->once())
                ->method('restoreEmailData')
                ->will($this->returnValue(array()));

            $mockManager->expects($this->once())
                ->method('changeAccount');

            $mockManager->expects($this->once())
                ->method('sendConfirmationEmails');

            $mockChanger
                ->expects($this->exactly(2))
                ->method('getTechnologyTypeFromSdi')
                ->willReturnOnConsecutiveCalls($sdis['oldSdi'], $sdis['newSdi']);

            $shouldPopulateSdis = true;
        }

        $mockChanger->expects($this->once())
            ->method('isExcludedFromBilling')
            ->will($this->returnValue(false));

        if (!$accountHasOpenMopOrders) {

            $mockChanger->expects($this->once())
                ->method('isScheduledHouseMove');

            if (!$accountHasHouseMove) {

                if ($runcbc == true) {
                    $mockChanger->expects($this->once())
                        ->method('executeAndReturnCode')
                        ->will($this->returnValue(0));

                    $mockDb->expects($this->once())
                        ->method('getCbcBillingPeriodForService')
                        ->with($serviceId)
                        ->will($this->returnValue("SERVICE"));
                 } else {
                    $mockChanger->expects($this->never())
                         ->method('executeAndReturnCode');

                    $mockDb->expects($this->never())
                        ->method('getCbcBillingPeriodForService');
                 }

                 $mockChanger->expects($this->once())
                     ->method('restoreAccountManager')
                     ->will($this->returnValue($mockManager));
            }
        }

        if ($retentionDiscountsThrowException) {
            $mockChanger->expects($this->any())
                ->method('applyPendingRetentionDiscounts')
                ->will($this->throwException(new Exception()));
        } else {
            $mockChanger->expects($this->any())
                ->method('applyPendingRetentionDiscounts')
                ->will($this->returnValue($retentionDiscountsApplied));
        }

        $mockDb->expects($this->once())
            ->method('getAccountsScheduledToChangeByServiceId')
            ->with(array($serviceId => $serviceId))
            ->will(
            $this->returnValue(
                array(array(
                    'intServiceChangeScheduleId' => 1,
                    'strNewProductName' => 'Extra',
                    'intNewSdi' => 6754,
                    'strOldProductName' => 'PlusNet Staff Broadband Up to 8Mb (Monthly Contract, No Modem)',
                    'intOldSdi' => 6359,
                    'intServiceId' => $serviceId,
                    'intCurrentSdi' => 6359,
                    'dteChangeComplete' => null,
                    'dteChangeDate' => I18n_Date::fromTimestamp(**********),
                    'strContractLengthHandle' => 'MONTHLY',
                    'requiresBroadbandOrder' => 1,
                    'contractType' => $storedContractType
                ))
            )
        );

        $mockDb->expects($this->once())
            ->method('getAccountsScheduledToChangeByAppointmentAndServiceId')
            ->will($this->returnValue(array()));

        $mockDb->expects($this->any())
            ->method('completeServiceChangeSchedule')
            ->will($this->returnValue(true));

        $mockDb->expects($this->any())
            ->method('getC2MPromotionCode')
            ->will($this->returnValue(null));

        $mockDb
            ->method('isAdslToFttcAccountChangeByScheduleId')
            ->will($this->returnValue(false));

        $mockChanger->expects($this->any())
            ->method('determineContractType')
            ->willReturn($mockContractDto);

        $expectedContractType = 'A contract type';
        $expectedContractSubType = 'A contract sub type';

        if (!is_null($storedContractType)) {
            if (!$changeAccountThrowException ||
                !$retentionDiscountsThrowException ||
                !$coreServiceForServiceIdThrowException ||
                !$accountHasOpenMopOrders ||
                !$accountHasHouseMove
            ) {
                $mockContractDto->expects($this->once())
                    ->method('getContractType')
                    ->willReturn($expectedContractType);

                $mockContractDto->expects($this->once())
                    ->method('getContractSubType')
                    ->willReturn($expectedContractSubType);
            }
        } else {
            $mockContractDto->expects($this->any())
                ->method('getContractType');

            $mockContractDto->expects($this->any())
                ->method('getContractSubType');
        }


        Db_Manager::setAdaptor('AccountChange', $mockDb);

        $mockChanger->run(array(), $opts);

        if (isset($shouldPopulateSdis)) {
            $this->assertEquals($mockChanger->getNewAccessTechnology(), $sdis['newSdi']);
            $this->assertEquals($mockChanger->getCurrentAccessTechnology(), $sdis['oldSdi']);
        } else {
            $this->assertEquals($mockChanger->getNewAccessTechnology(), null);
            $this->assertEquals($mockChanger->getCurrentAccessTechnology(), null);
        }

        if (!is_null($storedContractType)) {
            $contractType = AccountChange_Registry::instance()->getEntry(
                'contractType'
            );

            $contractSubType = AccountChange_Registry::instance()->getEntry(
                'contractSubType'
            );

            $this->assertEquals($contractType, $expectedContractType);
            $this->assertEquals($contractSubType, $expectedContractSubType);

        }
    }

    /**
     * Data provider method for testRun
     *
     * @return array
     */
    public function provideDataForTestRun()
    {
        // $serviceId, $opts, $runcbc, $retentionDiscountsApplied, $retentionDiscountsThrowException
        $serviceId = 1111;
        $optsWithCbc = array(
            's' => $serviceId,
            'runcbc' => true
        );

        $optsWithoutCbc = array(
            's' => $serviceId
        );

        $storedContractType = 'F';

        //keys: serviceId, opts, runcbc, retentionDiscountsApplied, changeAccountThrowException, retentionDiscountsThrowException, coreServiceForServiceIdThrowException, accountHasOpenMopOrders, accountHasHouseMove, storedContractType, sdis
        return array(
            array($serviceId, $optsWithCbc,     true,   true,   false,  false, false, false, false, $storedContractType, self::provideSdis()),
            array($serviceId, $optsWithCbc,     true,   false,  false,  false, false, false, false, null, self::provideSdis()),
            array($serviceId, $optsWithCbc,     true,   true,   true,   false, false, false, false, null, self::provideSdis()),
            array($serviceId, $optsWithoutCbc,  false,  true,   false,  false, false, false, false, null, self::provideSdis()),
            array($serviceId, $optsWithoutCbc,  false,  true,   false,  false, false, false, false, null, self::provideSdis()),
            array($serviceId, $optsWithCbc,     true,   true,   false,  false, false, true, false, null, self::provideSdis()),
            array($serviceId, $optsWithCbc,     true,   false,  false,  false, false, true, false, null, self::provideSdis()),
            array($serviceId, $optsWithCbc,     true,   true,   true,   false, false, true, false, null, self::provideSdis()),
            array($serviceId, $optsWithoutCbc,  false,  true,   false,  false, false, true, false, null, self::provideSdis()),
            array($serviceId, $optsWithoutCbc,  false,  true,   false,  false, false, true, false, null, self::provideSdis()),
            array($serviceId, $optsWithoutCbc,  true,   true,   false,  false, false, false, true, null, self::provideSdis())
        );
    }

    public function provideSdis()
    {
        $technologies = ['ADSL', 'FTTP', 'SoGEA', 'tech1', 'tech2', 'tech3', null];

        return array(
            'oldSdi' => $technologies[rand(0, 5)],
            'newSdi' => $technologies[rand(0, 6)],
        );
    }

    /**
     * Test for 'applyRetentionsDiscount'
     *
     * @covers AccountChange_PerformScheduledAccountChange::applyPendingRetentionDiscounts
     *
     * @return void
     */
    public function testApplyPendingRetentionDiscounts()
    {
        $serviceId = 1111;
        $reasonId = 3;
        $resultId = 4;
        $adslScheduleId = 99;
        $wlrScheduleId = 100;

        //Mock AccountChange db access
        $mockAccountChangeDb = $this->getMock(
            'Db_Adaptor',
            array(
                'getComponentIdForTypeAndStatus',
                'getSubscriptionProductComponentInstanceIdByComponentId'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockAccountChangeDb->expects($this->once())
            ->method('getComponentIdForTypeAndStatus')
            ->will($this->returnValue(array("2222")));
        $mockAccountChangeDb
            ->method('getSubscriptionProductComponentInstanceIdByComponentId')
            ->willReturn(null);

        Db_Manager::setAdaptor('AccountChange', $mockAccountChangeDb);

        //Mock Auth db access
        $mockAutheDb = $this->getMock(
            'Db_Adaptor',
            array(
                'getActorByExternalUserId',
                'getBusinessActor'
            ),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockAutheDb->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(2222));

        $mockAutheDb->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue(array(array('intActorId' => 999, 'strUsername' => 'TestUser'))));

        Db_Manager::setAdaptor('Auth', $mockAutheDb);

        //Create mock RetentionTool_OfferItem object
        $pendingAdslOffer = $this->getMock(
            'RetentionsTool_OfferItem',
            array('apply'),
            array(new RetentionsTool_Offers_ChargableComponent())
        );

        $pendingAdslOffer->setRetentionOfferId(1);
        $pendingAdslOffer->setDiscountValueIncVat(100);
        $pendingAdslOffer->setDiscountDurationMonths(4);
        $pendingAdslOffer->setComponentId(null);
        $pendingAdslOffer->setAdslScheduleId($adslScheduleId);

        $pendingAdslOffer->expects($this->once())
            ->method('apply');

        //Create mock RetentionTool_OfferItem object
        $pendingWlrOffer = $this->getMock(
            'RetentionsTool_OfferItem',
            array('apply'),
            array(new RetentionsTool_Offers_ChargableComponent())
        );

        $pendingWlrOffer->setRetentionOfferId(1);
        $pendingWlrOffer->setDiscountValueIncVat(100);
        $pendingWlrOffer->setDiscountDurationMonths(4);
        $pendingWlrOffer->setComponentId(null);
        $pendingWlrOffer->setWlrScheduleId($wlrScheduleId);

        $pendingWlrOffer->expects($this->never())
            ->method('apply');

        $retentionManager = new RetentionsTool_Manager(
            new RetentionsTool_OfferManager(new Auth_BusinessActor()),
            $reasonId,
            $resultId
        );

        $offeringActor = new Auth_BusinessActor();
        $offeringActor->setActorId(99);

        $retentionManager->setOfferingActor($offeringActor);

        $retentionManager->getOfferManager()->addOffer($pendingAdslOffer);
        $retentionManager->getOfferManager()->addOffer($pendingWlrOffer);

        $accountChanger = $this->getMock(
            'AccountChange_PerformScheduledAccountChange',
            array('getRetentionManagerByAdslScheduleId')
        );

        $accountChanger->expects($this->once())
            ->method('getRetentionManagerByAdslScheduleId')
            ->will($this->returnValue($retentionManager));

        $discountApplied = $accountChanger->applyPendingRetentionDiscounts($serviceId, $adslScheduleId);

        $this->assertTrue($discountApplied);
    }

    /**
     * Set mock house moves db adaptor
     *
     * @return void
     */
    private function setMockHouseMovesDbAdaptor()
    {
        $objMockHouseMovesDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isScheduledHouseMove'),
            array('HouseMoves', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockHouseMovesDbAdaptor->expects($this->any())
            ->method('isScheduledHouseMove')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('HouseMoves', $objMockHouseMovesDbAdaptor);
    }

    /**
     * @return void
     */
    public function testAdslToFttcAccountChangeIsProcessedOnlyInRadius()
    {
        $serviceId = 12345;
        $changeId = 54321;

        $accountChanges = [
            [
                'intServiceId' => $serviceId,
                'intServiceChangeScheduleId' => $changeId
            ]
        ];

        $radiusConnection = Mockery::mock();
        $radiusConnection->shouldReceive('activate')->with($serviceId, true)->once();

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getAccountsScheduledToChangeTodayUnrestricted')->andReturn($accountChanges);
        $dbAdaptor->shouldReceive('isAdslToFttcAccountChangeByScheduleId')->with($changeId)->andReturnTrue();

        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $accountChange = Mockery::mock('AccountChange_PerformScheduledAccountChange');
        $accountChange->makePartial();
        $accountChange->shouldAllowMockingProtectedMethods();

        $accountChange->shouldReceive('output');
        $accountChange->shouldReceive('includeLegacyFiles');
        $accountChange->shouldReceive('isExcludedFromBilling')->andReturnFalse();
        $accountChange->shouldReceive('hasOpenMopOrder')->andReturnFalse();
        $accountChange->shouldReceive('isScheduledHouseMove')->andReturnFalse();
        $accountChange->shouldReceive('isReContractingOnSameProduct')->andReturnFalse();
        $accountChange->shouldReceive('accountChangeStartForumPosting');
        $accountChange->shouldReceive('accountChangeEndForumPosting');
        $accountChange->shouldReceive('getRadiusConnection')->andReturn($radiusConnection);
        $accountChange->shouldReceive('flushOutputBuffer');
        $accountChange->shouldReceive('getC2MPromoCode');

        $accountChange->run([], []);
    }

    /**
     * @return void
     */
    public function testAccountChangePendingFibreBroadbandOrderIsProcessedOnlyInRadius()
    {
        $serviceId = 12345;
        $changeId = 54321;
        $newServiceDefinition = 432;

        $accountChanges = [
            [
                'intServiceId' => $serviceId,
                'intServiceChangeScheduleId' => $changeId,
                'requiresBroadbandOrder' => 1,
                'intNewSdi' => $newServiceDefinition,
            ]
        ];

        $radiusConnection = Mockery::mock();
        $radiusConnection->shouldReceive('activate')->with($serviceId, true)->once();

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getAccountsScheduledToChangeTodayUnrestricted')->andReturn($accountChanges);
        $dbAdaptor->shouldReceive('isAdslToFttcAccountChangeByScheduleId')->with($changeId)->andReturnFalse();
        $dbAdaptor->shouldReceive('isFibreProduct')->with($newServiceDefinition)->andReturnTrue();

        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $accountChange = Mockery::mock('AccountChange_PerformScheduledAccountChange');
        $accountChange->makePartial();
        $accountChange->shouldAllowMockingProtectedMethods();

        $accountChange->shouldReceive('output');
        $accountChange->shouldReceive('includeLegacyFiles');
        $accountChange->shouldReceive('isExcludedFromBilling')->andReturnFalse();
        $accountChange->shouldReceive('hasOpenMopOrder')->andReturnFalse();
        $accountChange->shouldReceive('isScheduledHouseMove')->andReturnFalse();
        $accountChange->shouldReceive('isReContractingOnSameProduct')->andReturnFalse();
        $accountChange->shouldReceive('isFttpProduct')->andReturnFalse();
        $accountChange->shouldReceive('isSogeaProduct')->andReturnFalse();
        $accountChange->shouldReceive('accountChangeStartForumPosting');
        $accountChange->shouldReceive('accountChangeEndForumPosting');
        $accountChange->shouldReceive('getRadiusConnection')->andReturn($radiusConnection);
        $accountChange->shouldReceive('flushOutputBuffer');
        $accountChange->shouldReceive('getC2MPromoCode');


        $accountChange->run([], []);
    }

    /**
     * @return void
     */
    public function testAccountChangePendingFTTPOrderIsNotProcessedInRadius()
    {
        $serviceId = 12345;
        $changeId = 54321;
        $newServiceDefinition = 432;

        $accountChanges = [
            [
                'intServiceId' => $serviceId,
                'intServiceChangeScheduleId' => $changeId,
                'requiresBroadbandOrder' => 1,
                'intNewSdi' => $newServiceDefinition,
            ]
        ];

        $radiusConnection = Mockery::mock();
        $radiusConnection->shouldNotReceive('activate');

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getAccountsScheduledToChangeTodayUnrestricted')->andReturn($accountChanges);
        $dbAdaptor->shouldReceive('isAdslToFttcAccountChangeByScheduleId')->with($changeId)->andReturnFalse();
        $dbAdaptor->shouldReceive('isFibreProduct')->with($newServiceDefinition)->andReturnFalse();
        $dbAdaptor->shouldReceive('isProductType')->with(['FTTP'], $newServiceDefinition)->andReturnTrue();

        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $accountChange = Mockery::mock('AccountChange_PerformScheduledAccountChange');
        $accountChange->makePartial();
        $accountChange->shouldAllowMockingProtectedMethods();

        $accountChange->shouldReceive('output');
        $accountChange->shouldReceive('includeLegacyFiles');
        $accountChange->shouldReceive('isExcludedFromBilling')->andReturnFalse();
        $accountChange->shouldReceive('hasOpenMopOrder')->andReturnFalse();
        $accountChange->shouldReceive('isScheduledHouseMove')->andReturnFalse();
        $accountChange->shouldReceive('isReContractingOnSameProduct')->andReturnFalse();
        $accountChange->shouldReceive('accountChangeStartForumPosting');
        $accountChange->shouldReceive('accountChangeEndForumPosting');
        $accountChange->shouldNotReceive('getRadiusConnection');
        $accountChange->shouldReceive('flushOutputBuffer');
        $accountChange->shouldReceive('getC2MPromoCode');

        $accountChange->run([], []);
    }

    public function testAccountChangePendingSoGEAOrderIsNotProcessedInRadius()
    {
        $serviceId = 12345;
        $changeId = 54321;
        $newServiceDefinition = 432;

        $accountChanges = [
            [
                'intServiceId' => $serviceId,
                'intServiceChangeScheduleId' => $changeId,
                'requiresBroadbandOrder' => 1,
                'intNewSdi' => $newServiceDefinition,
            ]
        ];

        $radiusConnection = Mockery::mock();
        $radiusConnection->shouldNotReceive('activate');

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getAccountsScheduledToChangeTodayUnrestricted')->andReturn($accountChanges);
        $dbAdaptor->shouldReceive('isAdslToFttcAccountChangeByScheduleId')->with($changeId)->andReturnFalse();
        $dbAdaptor->shouldReceive('isFibreProduct')->with($newServiceDefinition)->andReturnFalse();
        $dbAdaptor->shouldReceive('isProductType')->with(['FTTP'], $newServiceDefinition)->andReturnFalse();
        $dbAdaptor->shouldReceive('isProductType')->with(['SoGEA'], $newServiceDefinition)->andReturnTrue();

        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $accountChange = Mockery::mock('AccountChange_PerformScheduledAccountChange');
        $accountChange->makePartial();
        $accountChange->shouldAllowMockingProtectedMethods();

        $accountChange->shouldReceive('output');
        $accountChange->shouldReceive('includeLegacyFiles');
        $accountChange->shouldReceive('isExcludedFromBilling')->andReturnFalse();
        $accountChange->shouldReceive('hasOpenMopOrder')->andReturnFalse();
        $accountChange->shouldReceive('isScheduledHouseMove')->andReturnFalse();
        $accountChange->shouldReceive('isReContractingOnSameProduct')->andReturnFalse();
        $accountChange->shouldReceive('accountChangeStartForumPosting');
        $accountChange->shouldReceive('accountChangeEndForumPosting');
        $accountChange->shouldNotReceive('getRadiusConnection');
        $accountChange->shouldReceive('flushOutputBuffer');
        $accountChange->shouldReceive('getC2MPromoCode');

        $accountChange->run([], []);
    }

    /**
     * @return void
     */
    public function testRecontractOnSameProductProcessesPendingRetentionDiscounts() {
        $serviceId = 12345;
        $changeId = 54321;

        $accountChanges = [
            [
                'intServiceId' => $serviceId,
                'intServiceChangeScheduleId' => $changeId
            ]
        ];

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getAccountsScheduledToChangeTodayUnrestricted')->andReturn($accountChanges);
        $dbAdaptor->shouldReceive('isAdslToFttcAccountChangeByScheduleId')->with($changeId)->andReturnTrue();
        $dbAdaptor->shouldReceive('completeServiceChangeSchedule');

        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $mockIEC = Mockery::mock('InventoryEventClient');
        $mockIEC->shouldReceive('takePreChangeSnapshot');
        $mockIEC->shouldReceive('takePostChangeSnapshot');

        BusTier_BusTier::setClient('inventoryEventService', $mockIEC);

        $accountChange = Mockery::mock('AccountChange_PerformScheduledAccountChange');
        $accountChange->makePartial();
        $accountChange->shouldAllowMockingProtectedMethods();

        $accountChange->shouldReceive('output');
        $accountChange->shouldReceive('includeLegacyFiles');
        $accountChange->shouldReceive('isExcludedFromBilling')->andReturnFalse();
        $accountChange->shouldReceive('hasOpenMopOrder')->andReturnFalse();
        $accountChange->shouldReceive('isScheduledHouseMove')->andReturnFalse();
        $accountChange->shouldReceive('isReContractingOnSameProduct')->andReturnTrue();
        $accountChange->shouldReceive('applyPendingRetentionDiscounts')->andReturnTrue();
        $accountChange->shouldReceive('accountChangeStartForumPosting');
        $accountChange->shouldReceive('accountChangeEndForumPosting');
        $accountChange->shouldReceive('flushOutputBuffer');
        $accountChange->shouldReceive('getC2MPromoCode');

        $accountChange->run([],[]);
    }
}
