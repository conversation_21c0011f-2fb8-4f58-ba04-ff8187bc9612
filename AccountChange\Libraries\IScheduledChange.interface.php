<?php
/**
 * Scheduled Change Interface File
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      link
 */
/**
 * Interface for each product that can have a change scheduled
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      link
 */
interface AccountChange_IScheduledChange
{
    /**
     * Constructor for the scheduled change
     *
     * @param int $serviceId The customers service id
     *
     * @return AccountChange_IScheduledChange
     */
    public function __construct($serviceId);

    /**
     * Is there a change scheduled in the database for this product
     *
     * @return boolean
     */
    public function hasScheduledChange();

    /**
     * Cancel the change
     *
     * @param Auth_BusinessActor $actioner Business actor performing the action
     *
     * @throws Exception
     *
     * @return void
     */
    public function cancelChange(Auth_BusinessActor $actioner);

    /**
     * Get the date of the sheduled change
     *
     * @return I18n_Date|null
     */
    public function getChangeDate();

    /**
     * Get the name of the new product
     *
     * @return string|null
     */
    public function getNewProductName();

    /**
     * Get the customer friendly name of the product type
     *
     * @return string
     */
    public function getProductType();

    /**
     * Getter for the interal bag of data
     *
     * @return array
     */
    public function getData();
}
