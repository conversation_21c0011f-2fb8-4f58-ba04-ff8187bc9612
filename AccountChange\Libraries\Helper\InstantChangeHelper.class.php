<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\InventoryEventClient\Context\AccountChangeContext;
use Plusnet\InventoryEventClient\Service\EventService;

class AccountChange_InstantChangeHelper
{
    /**
     * @var Db_Adaptor
     */
    private $db;

    /**
     * @var bool
     */
    private $broadbandProductsChanging;

    /**
     * @var EventService
     */
    private $inventoryEventService;

    /**
     * @var DateTime
     */
    private $backDatedDate;

    /**
     * @var int
     */
    private $serviceId;

    /**
     * @param int      $serviceId                 Customer service id
     * @param bool     $broadbandProductsChanging Is the broadband product changing
     * @param DateTime $backDatedDate             Account change context
     * @throws Db_TransactionException
     * @throws Exception
     */
    public function __construct(
        $serviceId,
        $broadbandProductsChanging,
        DateTime $backDatedDate = null
    ) {
        $this->serviceId = $serviceId;
        $this->broadbandProductsChanging = $broadbandProductsChanging;
        $this->backDatedDate = $backDatedDate;
        $this->db = Db_Manager::getAdaptor('AccountChange');
        $this->inventoryEventService = \BusTier_BusTier::getClient('inventoryEventService');
    }

    /**
     * @return void
     */
    public function takePreChangeSnapshot()
    {
        $context = $this->setContext();

        $this->inventoryEventService->takePreChangeSnapshot(
            $this->serviceId,
            $context
        );
    }

    /**
     * @throws BillingApiClientBillingServiceException
     * @return void
     */
    public function takePostChangeSnapshot()
    {
        $this->inventoryEventService->takePostChangeSnapshot(
            $this->serviceId
        );
    }

    /**
     * @return AccountChangeContext
     */
    private function setContext()
    {
        if (!empty($this->backDatedDate)) {
            $context = new AccountChangeContext($this->backDatedDate);
            $context->setIsPredate(true);
        } else {
            $context = new AccountChangeContext();
        }

        if (!$this->broadbandProductsChanging) {
            $curBBStartDate = $this->db->getCurrentBroadbandStartDate(
                $this->serviceId
            );

            if (!empty($curBBStartDate)) {
                $curBroadbandStartDateObj = new DateTime($curBBStartDate);
                $context->setBroadbandStartDate($curBroadbandStartDateObj);
            }
        } else {
            $context->setIsBBCRCease(true);
        }

        return $context;
    }
}
