<?php
/**
 * InternetConnection Product Configuration
 *
 * Testing class for the AccountChange_Product_InternetConnection class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Product_InternetConnection.test.php,v 1.3 2009-02-17 04:41:28 rmerewood Exp $
 * @since      File available since 2008-08-28
 */
/**
 * InternetConnection Product Configuration Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Product_InternetConnection_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for the constructor of a plustalk object: array
     * of options to initialise the configuration
     *
     * @var array
     */
    private $arrOptions;

    /**
     * Fixture for the constructor: intServiceComponentId
     *
     * @var unknown_type
     */
    private $intComponentId;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        $this->arrOptions   = array(
                                    'intNewServiceDefinitionId' => 12,
                                    'intLineCheckId' => 101);
        $this->intComponentId = 9999;
    }

    /**
     * @covers AccountChange_Product_InternetConnection::isKeyProduct
     */
    public function testKeyProductIsFalse()
    {
        $objInternetConnection = new AccountChange_Product_InternetConnection(
            $this->intComponentId,
            AccountChange_Product_Manager::ACTION_NONE,
            $this->arrOptions
        );

        $this->assertFalse($objInternetConnection->isKeyProduct());
    }

    /**
     * @covers AccountChange_Product_InternetConnection::execute
     */
    public function testExecuteCallsLegacyWrapperFunctionWhenAddingCommponent()
    {
        $objInternetConnection = $this->getMock(
            'AccountChange_Product_InternetConnection',
            array('create'),
            array(
                $this->intComponentId,
                AccountChange_Product_Manager::ACTION_ADD,
                $this->arrOptions
            )
        );

        $objInternetConnection->expects($this->once())
                              ->method('create');

        $objInternetConnection->execute();
    }

    /**
     * @covers AccountChange_Product_InternetConnection::execute
     */
    public function testExecuteCallsLegacyWrapperFunctionWhenChangingComponent()
    {
        $objInternetConnection = $this->getMock(
            'AccountChange_Product_InternetConnection',
            array('change'),
            array(
                $this->intComponentId,
                AccountChange_Product_Manager::ACTION_CHANGE,
                $this->arrOptions
            )
        );

        $objInternetConnection->expects($this->once())
                              ->method('change');

        $objInternetConnection->execute();
    }

    /**
     * @covers AccountChange_Product_InternetConnection::initialise
     *
     */
    public function testInitialiseThrowsExceptionIfNewServiceDefinitionIdIsNotPresent()
    {
        $this->setExpectedException(
            'AccountChange_Product_ManagerException',
            'The new service definition id is needed for this product configuration to be actioned',
            AccountChange_Product_ManagerException::ERR_NEW_SERVICE_DEFINITION_ID_NOT_PRESENT
        );

        $objProduct = new AccountChange_Product_InternetConnection(
            $this->intComponentId, AccountChange_Product_Manager::ACTION_NONE
        );
    }

    /**
     * @covers AccountChange_Product_InternetConnection::initialise
     *
     */
    public function testInitialiseSetsUpTheOptionsCorrectly()
    {
        $objProduct = new AccountChange_Product_InternetConnection(
            $this->intComponentId,
            AccountChange_Product_Manager::ACTION_NONE,
            $this->arrOptions
        );

        $this->assertAttributeEquals(
            $this->arrOptions['intNewServiceDefinitionId'],
            'intNewServiceDefinitionId',
            $objProduct
        );
        $this->assertAttributeEquals(
            $this->arrOptions['intLineCheckId'],
            'intLineCheckId',
            $objProduct
        );
    }

    /**
     * @covers AccountChange_Product_InternetConnection::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will(
                             $this->returnValue(
                                 array('strProductFamily'=>'VALUE')
                             )
                         );

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                            ->method(
                                'isServiceComponentAllowedOnServiceDefinition'
                            )
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1, AccountChange_Product_Manager::ACTION_NONE
        );

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_InternetConnection(
            1, AccountChange_Product_Manager::ACTION_CHANGE, $this->arrOptions
        );
        $objProduct2 = new AccountChange_Product_InternetConnection(
            2, AccountChange_Product_Manager::ACTION_CHANGE, $this->arrOptions
        );

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct1)
        );
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct2)
        );

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertEquals(
            $objProduct2, $objProduct1->getNewProductConfiguration()
        );

        Db_Manager::restoreAdaptor('Core');
    }

    /**
     * @covers AccountChange_Product_InternetConnection::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenNotChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will(
                             $this->returnValue(
                                 array('strProductFamily'=>'VALUE')
                             )
                         );

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                            ->method(
                                'isServiceComponentAllowedOnServiceDefinition'
                            )
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(
            1, AccountChange_Product_Manager::ACTION_NONE
        );

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_InternetConnection(
            1, AccountChange_Product_Manager::ACTION_NONE, $this->arrOptions
        );
        $objProduct2 = new AccountChange_Product_InternetConnection(
            2, AccountChange_Product_Manager::ACTION_NONE, $this->arrOptions
        );

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct1)
        );
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(
            array($objProductServiceDefinition, $objProduct2)
        );

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }

    /**
     * Test method for AccountChange_Product_InternetConnection::change
     * with CurrentContract
     *
     * @covers AccountChange_Product_InternetConnection::change
     *
     * @return void
     */
    public function testChangeWithCurrentContract()
    {
        TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Product_InternetConnection',
            array(
                $this->intComponentId,
                AccountChange_Product_Manager::ACTION_ADD,
                $this->arrOptions
            ),
            '_Proxy'
        );

        $objInternetConnection = $this->getMock(
            'AccountChange_Product_InternetConnection_Proxy',
            array(
                'includeLegacyFiles',
                'getMarketId',
                'getAccountChangeOperation',
                'accountTypeChangeHandler',
                'getTariffID',
                'getCoreService',
                'output',
            ),
            array(
                $this->intComponentId,
                AccountChange_Product_Manager::ACTION_ADD,
                $this->arrOptions
            )
        );
        $objInternetConnection->expects($this->once())
                ->method('includeLegacyFiles');
        $objInternetConnection->expects($this->once())
                ->method('getMarketId')
                ->will($this->returnValue(1));
        $objInternetConnection->expects($this->once())
                ->method('getAccountChangeOperation')
                ->will($this->returnValue(1));
        $objInternetConnection->expects($this->once())
                ->method('accountTypeChangeHandler')
                ->will($this->returnValue(true))
                ->with(
                    $this->equalTo('MONTHLY'),
                    $this->equalTo(true),
                    $this->equalTo(1)
                );
        $objInternetConnection->expects($this->once())
                ->method('getTariffID')
                ->will($this->returnValue(822));
        $objInternetConnection->expects($this->any())
                ->method('output');

         $core = $this->getMock('Core_Service', array('getNextInvoiceDate'));
         $core->expects($this->once())
              ->method('getNextInvoiceDate')
              ->will($this->returnValue(I18n_Date::fromTimestamp(strtotime('+1 week'))));

        $objInternetConnection->expects($this->once())
                              ->method('getCoreService')
                              ->will($this->returnValue($core));
        $bolresult = $objInternetConnection->protected_change();
        $this->assertEquals(true, $bolresult);
    }

    /**
     * Test method for AccountChange_Product_InternetConnection::change
     * with out CurrentContract
     *
     * @covers AccountChange_Product_InternetConnection::change
     *
     * @return void
     */
    public function testChangeWithOutCurrentContract()
    {
        TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Product_InternetConnection',
            array(
                $this->intComponentId,
                AccountChange_Product_Manager::ACTION_ADD,
                $this->arrOptions
            ),
            '_Proxy'
        );
        $objInternetConnection = $this->getMock(
            'AccountChange_Product_InternetConnection_Proxy',
            array(
                'includeLegacyFiles',
                'getMarketId',
                'getAccountChangeOperation',
                'accountTypeChangeHandler',
                'getTariffID',
                'getCoreService',
                'output',
            ),
            array(
                $this->intComponentId,
                AccountChange_Product_Manager::ACTION_ADD,
                $this->arrOptions
            )
        );
        $objInternetConnection->expects($this->once())
                ->method('includeLegacyFiles');
        $objInternetConnection->expects($this->once())
                ->method('getMarketId')
                ->will($this->returnValue(1));
        $objInternetConnection->expects($this->once())
                ->method('getAccountChangeOperation')
                ->will($this->returnValue(2));
        $objInternetConnection->expects($this->once())
                ->method('accountTypeChangeHandler')
                ->will($this->returnValue(true))
                ->with(
                    $this->equalTo('MONTHLY'),
                    $this->equalTo(false),
                    $this->equalTo(1)
                );

         $core = $this->getMock('Core_Service', array('getNextInvoiceDate'));
         $core->expects($this->once())
              ->method('getNextInvoiceDate')
              ->will($this->returnValue(I18n_Date::fromTimestamp(strtotime('+1 week'))));

         $objInternetConnection->expects($this->once())
                               ->method('getCoreService')
                               ->will($this->returnValue($core));

        $objInternetConnection->expects($this->once())
                ->method('getTariffID')
                ->will($this->returnValue(822));

        $objInternetConnection->expects($this->any())
                ->method('output');

        $bolresult = $objInternetConnection->protected_change();
        $this->assertEquals(true, $bolresult);
    }
}
