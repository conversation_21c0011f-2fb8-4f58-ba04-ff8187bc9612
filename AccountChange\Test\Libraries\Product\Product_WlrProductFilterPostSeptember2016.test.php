<?php

/**
 * File Product_WlrProductFilter.test.php
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */

/**
 * Class AccountChange_Product_WlrProductFilterPostSeptember2016Test
 *
 * Unit tests for AccountChange_Product_WlrProductFilter post September 2016
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */

require_once('Test/Libraries/Product/Product_WlrProductFilterBase.php');

class AccountChange_Product_WlrProductFilterPostSeptember2016Test extends AccountChange_Product_WlrProductFilterBase
{
    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014EveningAndWeekendsFreeWithMobileForFibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014EveningsAndWeekendsFreeMobileSelected,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014EveningAndWeekendsFreeForFibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014EveningsAndWeekendsFree,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForJune2014EveningAndWeekendsFreeForFibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014EveningsAndWeekendsFree,
            $this->June2014EveningsAndWeekendsFreeWithMobile,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }



    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForADSLLegacyProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->legacyMobileProducts
        );


        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = 919,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForADSLLegacyProductTalkEveningsAndWeekends()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->legacyMobileProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            AccountChange_Product_WlrProductFilter::FALCON_LEGACY_EVENINGS_AND_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForADSLLegacyProductTalkAnytime()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->legacyMobileProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            AccountChange_Product_WlrProductFilter::FALCON_LEGACY_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForLegacyFibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->legacyMobileProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = 919,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForLegacyFibreTalkEveningsAndWeekendsProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->legacyMobileProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            AccountChange_Product_WlrProductFilter::FALCON_LEGACY_EVENINGS_AND_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForLegacyFibreAnytimeProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->legacyProducts,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->legacyMobileProducts
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            AccountChange_Product_WlrProductFilter::FALCON_LEGACY_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForPenguinAdslProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->penguinMlrOnlyProducts,
            $this->penguinMlrOnlyProductsMobileVersion,
            $this->penguinProducts,
            $this->penguinProductsMobileVersions,
            $this->penguinMobileProduct,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForPenguinAdslWeekendsProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->penguinMlrOnlyProducts,
            $this->penguinMlrOnlyProductsMobileVersion,
            $this->penguinProducts,
            $this->penguinProductsMobileVersions,
            $this->penguinMobileProduct,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForPenguinFibreEveningsAndWeekendsCharged()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->penguinMlrOnlyProducts,
            $this->penguinMlrOnlyProductsMobileVersion,
            $this->penguinProducts,
            $this->penguinProductsMobileVersions,
            $this->penguinMobileProduct,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_EVENINGS_AND_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForPenguinFibreAnytime()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->penguinMlrOnlyProducts,
            $this->penguinMlrOnlyProductsMobileVersion,
            $this->penguinProducts,
            $this->penguinProductsMobileVersions,
            $this->penguinMobileProduct,
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014AdslProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    public function testWorkplaceFilteringForJune2014AdslWeekendsProduct()
    {

        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForJune2014AdslProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_INCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014AdslProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForJune2014AdslProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014FibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForJune2014FibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }


    public function testWorkplaceFilteringForJune2014FibreProductAnytimeWithMobile()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014AnytimeWithMobile,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    public function testWorkplaceFilteringForJune2014FibreProductAnytime()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014FibreProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForJune2014FibreProductOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014Anytime,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForNoPhoneAdslProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, $includeNoHomePhone = true);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->noHomePhoneProduct
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = null,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForJune2014NoHomePhone()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, $includeNoHomePhone = true);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile,
            $this->noHomePhoneProduct
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = null,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014WeekendsWithMobile()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014WeekendsMobileSelected,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014Weekends()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014Weekends,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testWorkplaceFilteringForJune2014Weekends()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014Weekends,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014EveningsAndWeekendsWithMobileOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014EveningsAndWeekendsMobileSelected,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014WeekendsWithMobileForFibreProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     * @covers AccountChange_Product_WlrProductFilter::familiesMatch
     * @covers AccountChange_Product_WlrProductFilter::mlrOnly
     * @covers AccountChange_Product_WlrProductFilter::lrsOnly
     * @covers AccountChange_Product_WlrProductFilter::fibreOnly
     * @covers AccountChange_Product_WlrProductFilter::alwaysAvailableOnLRS
     * @covers AccountChange_Product_WlrProductFilter::adslOnly
     * @covers AccountChange_Product_WlrProductFilter::isLineOnlyProduct
     * @covers AccountChange_Product_WlrProductFilter::wlrProductIdRecognised
     * @covers AccountChange_Product_WlrProductFilter::availableInPortal
     * @covers AccountChange_Product_WlrProductFilter::getProductFamily
     * @covers AccountChange_Product_WlrProductFilter::findProductForBoltOnVersion
     */
    public function testPortalFilteringForJune2014LineOnlyOnLRS()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::ADSL_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForLineOnlyProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );

    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForUnlimitedUKAndMobilesProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2016_UNLIMITED_UK_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForEveningAndWeekendUKAndMobilesProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     */
    public function testPortalFilteringForUnlimitedUKAndMobilesProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2016_UNLIMITED_UK_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     */
    public function testPortalFilteringForEveningAndWeekendUKAndMobilesProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }


    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForPortal
     */
    public function testPortalFilteringForEveningAndWeekendsProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnly,
            $this->June2014EveningsAndWeekends,
            $this->June2014RemainingProductsPostSept16,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_PORTAL,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForEveningAndWeekendsProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014EveningsAndWeekends,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_ENABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }

    /**
     * @covers AccountChange_Product_WlrProductFilter::filterProducts
     * @covers AccountChange_Product_WlrProductFilter::filterForWorkplace
     */
    public function testWorkplaceFilteringForEveningAndWeekendsWithMobileProduct()
    {
        $availableProducts = $this->getAllProducts(self::LEGACY_MOBILE_EXCLUDED, self::NO_HOME_PHONE_EXCLUDED);

        $expectedResult = array_merge(
            $this->June2014LineOnlyResult,
            $this->June2014LineOnlyWithMobileResult,
            $this->June2014EveningsAndWeekendsWithMobile,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );

        $this->filterProducts(
            $expectedResult,
            $availableProducts,
            $currentProductId = AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
            AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE,
            self::DO_NOT_THROW_EXCEPTION,
            self::LRS_DISABLED,
            self::POST_SEPT_2016_DATE,
            self::FIBRE_PRODUCT
        );
    }
}
