<?php
/**
 * Product Configuration for Firewall component
 *
 * Holds information about a service definition product than an account may have
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_Firewall.class.php,v 1.2 2009-01-27 07:07:24 bselby Exp $
 * @since     File available since 2008-08-19
 */

/**
 * Product Firewall class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_Firewall extends AccountChange_Product_ServiceComponent
{
    /**
     * Is the product a key product
     *
     * @var boolean
     */
    protected $bolKeyProduct = false;

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration Account products' configurations
     *
     * @return void
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {

            $this->objNewProductConfiguration = $objAccountConfiguration->getProductConfigurationByType(
                AccountChange_Product_Manager::PRODUCT_TYPE_FIREWALL
            );
        }
    }

    /**
     * Refresh the firewall component
     *
     * @return void
     */
    protected function refresh()
    {
        // No code coverage for this code as it is calling legacy functions which we cannot mock
        $this->includeLegacyFiles();

        $objFirewall = new CFirewallComponentController($this->getComponentId());
        $objFirewall->setTicketText("The customer's firewall settings have been updated ");
        $objFirewall->setTicketSource('Script');
        $objFirewall->setSelectedOption('FW-Off');
        $objFirewall->updateCurrentOption();
    }

    /**
     * Need to include the legacy files, but we need to be able to mock it for the unit tests
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/common_library_functions/class_libraries/Firewall/CFirewallComponentController.php';
    }
}
