<?php
/**
 * This class contains the AccountChange_Address class
 *
 * @package    AccountChange
 * @subpackage Application
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
/**
 * Display the list of addresses obtained from the address matcher
 *
 * @package    AccountChange
 * @subpackage Application
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_Address extends Mvc_WizardRequirement
{
    /**
     * @var mixed  Defaults to array().
     */
    protected $arrInputs = array(
        'addressRef' => 'external:custom'
    );

    /**
     * Decorate the address matcher results for the template
     *
     * @param array &$arrValidatedApplicationData the validated app data
     *
     * @return array
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $addresses = array();

        foreach ($arrValidatedApplicationData['addresses'] as $address) {
            $addresses[$address->getIdentifyingString()] = $address;
        }
        return array(
            'addresses' => $addresses,
            'selectedBroadband' => $this->getSelectedBroadband()
        );
    }

    /**
     * Get the selected broadband product from an application state variable
     *
     * @return array
     **/
    public function getSelectedBroadband()
    {
        return $this->getApplicationStateVariable('arrSelectedBroadband');
    }

    /**
     * Custom validator for address ref
     * Regex taken from PlusnetPreSignup / Linecheck_AddressMatchController
     *
     * @param string $addressRef the address reference
     *
     * @return array
     */
    public function valAddressRef($addressRef)
    {
        if (!is_string($addressRef)
            || !preg_match('/^[A-Z]{1}[0-9]{11}:[A-Z]{2}:[a-zA-Z0-9]+$/', $addressRef)
        ) {
            $this->addValidationError('addressRef', 'INVALID');
            return array('addressRef' => '');
        }

        return array('addressRef' => $addressRef);

    }
}
