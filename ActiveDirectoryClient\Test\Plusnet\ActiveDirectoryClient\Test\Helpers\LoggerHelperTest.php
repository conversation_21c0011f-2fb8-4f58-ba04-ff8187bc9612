<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\ActiveDirectoryClient\Helpers\Test;

use Plusnet\ActiveDirectoryClient\Helpers\LoggerHelper;
use Plusnet\PrimitiveConverter\ConvertToPrimitiveValue;

/**
 * Class LoggerHelperTest
 *
 * @package Plusnet\ActiveDirectoryClient\Helpers\Test
 */
class LoggerHelperTest extends \PHPUnit_Framework_TestCase
{
    /**
     * @var LoggerHelper
     */
    private $loggerHelper;
    /**
     * @var \Log_Logger
     */
    private $mockLogger;

    /**
     * Setup the test.
     */
    public function setUp()
    {
        $this->mockLogger = $this->getMock('Log_Logger');

        $this->loggerHelper = new LoggerHelper(
            LoggerHelper::SERVICES_LOG_FILE_PATH,
            $this->mockLogger
        );
    }

    /**
     * @test
     *
     * @param string $message         Log message
     * @param string $expectedMessage Expected log message
     *
     * @dataProvider logDataProvider
     */
    public function iCanLogInfoMessages(
        $message,
        $expectedMessage
    ) {
        $this->mockLogger
            ->expects($this->once())
            ->method('log')
            ->with($this->createLogDataAssertion(
                $expectedMessage,
                LoggerHelper::LOG_LEVEL_INFO
            ));

        $this->loggerHelper->info($message);
    }

    /**
     * @test
     *
     * @param string $message         Expected log message
     * @param string $expectedMessage Logging level (info/error)
     *
     * @dataProvider logDataProvider
     */
    public function iCanLogErrorMessages(
        $message,
        $expectedMessage
    ) {
        $this->mockLogger
            ->expects($this->once())
            ->method('log')
            ->with($this->createLogDataAssertion(
                $expectedMessage,
                LoggerHelper::LOG_LEVEL_ERROR
            ));

        $this->loggerHelper->error($message);
    }

    /**
     * Provide data for test.
     *
     * @return array
     */
    public function logDataProvider()
    {
        return array(
            'single line' => array(
                'message'  => 'This is a single line message',
                'expected' => 'This is a single line message'
            ),
            'multi line'  => array(
                'message'  => "This is a multi\nline\nmessage",
                'expected' => 'This is a multi | line | message'
            )
        );
    }

    /**
     * Creates a log data assertion.
     *
     * @param string $expectedMessage Expected log message
     * @param string $logLevel        Logging level (info/error)
     *
     * @return \PHPUnit_Framework_Constraint_Callback
     */
    private function createLogDataAssertion(
        $expectedMessage,
        $logLevel
    ) {
        return $this->callback(function (\Log_LogData $logData) use ($expectedMessage, $logLevel) {
            $correctMessage = $expectedMessage === ConvertToPrimitiveValue::convert($logData->getMessage());
            $correctLogLevel = $logLevel === $logData->getLogLevel();
            $correctContext = LoggerHelper::CONTEXT === ConvertToPrimitiveValue::convert($logData->getContext());
            
            return $correctMessage && $correctLogLevel && $correctContext;
        });
    }
}
