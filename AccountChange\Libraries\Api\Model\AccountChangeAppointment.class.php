<?php
/***
 * @package   AccountChange
 * <AUTHOR> <PERSON>hatia <<EMAIL>>
 */

class AccountChange_AccountChangeAppointment
{
    /**
     * @var string
     */
    private $notes;

    /**
     * @var array
     */
    private $live;

    /**
     * @var array
     */
    private $manual;

    /**
     * AccountChange_AccountChangeAppointment constructor.
     * @param array $appointmentData appointment array
     * @return void
     */
    public function __construct($appointmentData)
    {
        $this->setNotes($appointmentData['notes']);
        if (!empty($appointmentData['live']) && count($appointmentData['live']) > 0) {
            $this->setLiveAppointment($appointmentData['live']);
        }
        if (!empty($appointmentData['manual']) && count($appointmentData['manual']) > 0) {
            $this->setManualAppointment($appointmentData['manual']);
        }
    }

    /**
     * @return string
     */
    public function getNotes()
    {
        return $this->notes;
    }

    /**
     * @param string $notes note string
     * @return void
     */
    private function setNotes($notes)
    {
        $this->notes = $notes;
    }

    /**
     * @return array
     */
    public function getLiveAppointment()
    {
        return $this->live;
    }

    /**
     * @param array $live live appointment array
     * @return void
     */
    private function setLiveAppointment($live)
    {
        $this->live = $live;
    }

    /**
     * @return array
     */
    public function getManualAppointment()
    {
        return $this->manual;
    }

    /**
     * @param array $manual appointment array
     * @return void
     */
    private function setManualAppointment($manual)
    {
        $this->manual = $manual;
    }

    /**
     * @return array
     */
    public function getAppointmentArray()
    {
        if (!empty($this->getManualAppointment())) {
            return array(
                "notes" => $this->getNotes(),
                "manual" => $this->getManualAppointment()
            );
        } else {
            return array(
                "notes" => $this->getNotes(),
                "live" => $this->getLiveAppointment()
            );
        }
    }
}
