<?php

use Plusnet\BillingApiClient\Entity\AvailableProduct;
use Plusnet\BillingApiClient\Entity\Request\ProductsQueryRequest;
use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;
use AccountChange_BillingApi_ProductOfferingPricePointIdHelper as ProductOfferingPricePointIdHelper;

class AccountChange_BillingApi_BasePriceHelper
{
    /**
     * Some known legacy service definitions are not being migrated to RBM so we can't query for a price
     *
     * @var array
     */
    const SERVICE_DEFINITIONS_NOT_IN_RBM = array(304, 2314, 4705, 6630, 6680, 6684, 6856, 6859);

    /**
     * Keys for base price and base price in contract values returned from Billing API getCustomerAvailableProducts()
     *
     * @var string
     */
    const CURRENT_BASE_PRICE_KEY             = 'currentBasePrice';
    const CURRENT_BASE_PRICE_IN_CONTRACT_KEY = 'currentBasePriceInContract';

    /**
     * SUBSCRIPTION intProductComponentID from dbProductComponents.tblProductComponent
     *
     * @var integer
     */
    const SUBSCRIPTION_PRODUCT_COMPONENT_ID = 1;

    /**
     * The price point id to use for a legacy broadband product that does not have tariffs
     *
     * @var integer
     */
    const LEGACY_PRODUCT_PRICE_POINT_ID = 1;

    /**
     * Get base prices from Billing for the supplied service id and product offering price point pairs.
     * This will call getCustomerAvailableProducts on BillingApiClient and transform the returned AvailableProducts
     * into an array of base prices keyed by unique combination of product offering and price point id.
     *
     * @param $serviceId                        int     Service ID of customer
     * @param $productOfferingPricePointPairs   array   Array of product offering : price point pairs
     *
     * @return array An array of base prices keyed by the product offering id/price point id combination
     */
    public static function getBasePrices($serviceId, $productOfferingPricePointPairs)
    {
        if (empty($productOfferingPricePointPairs)) {

            return array();
        }

        $availableProducts = static::getAvailableProducts($serviceId, $productOfferingPricePointPairs);

        $basePrices = array();
        foreach ($availableProducts as $availableProduct) {

            $basePricesForProduct = array();
            $basePricesForProduct[self::CURRENT_BASE_PRICE_KEY] = $availableProduct->getCurrentBasePriceInPounds();
            $basePricesForProduct[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] =
                !empty($availableProduct->getCurrentBasePriceInContractInPounds()) ?
                    $availableProduct->getCurrentBasePriceInContractInPounds() :
                    $availableProduct->getCurrentBasePriceInPounds();

            $id = ProductOfferingPricePointIdHelper::generateIdFromAvailableProduct($availableProduct);
            if (isset($basePrices[$id])) {

                $basePrices[$id] = static::mergeBasePricesForProduct($basePrices[$id], $basePricesForProduct);
            } else {

                $basePrices[$id] = $basePricesForProduct;
            }
        }

        return $basePrices;
    }

    /**
     * Generates the correct ProductOfferingPricePointPair to use in a request to BillingApi to get the price for a
     * given product. Takes a service definition id and optionally a service component id and tariff id.
     *
     * @param      integer|string $serviceDefinitionId The service definition id of the product
     * @param null|integer|string $serviceComponentId  Optional, the service component id of the product
     * @param null|integer|string $tariffId            Optional, the tariff id of the product
     *
     * @return null|ProductOfferingPricePointPair null if not enough information or a product offering price point pair
     */
    public static function createProductOfferingPricePointPairFromIds(
        $serviceDefinitionId,
        $serviceComponentId = null,
        $tariffId           = null)
    {
        $productOfferingId = static::getProductOfferingIdFromIds($serviceDefinitionId, $serviceComponentId);
        $pricePointId      = !empty($tariffId) ? $tariffId : self::LEGACY_PRODUCT_PRICE_POINT_ID;

        if ($productOfferingId === null) {
            return null;
        } else {
            return new ProductOfferingPricePointPair($productOfferingId, $pricePointId);
        }
    }

    /**
     * Helper method to call getCustomerAvailableProducts on BillingApiClient
     *
     * @param $serviceId                        int     Service ID of customer
     * @param $productOfferingPricePointPairs   array   Array of product offering : price point pairs
     *
     * @return array An array of AvailableProducts
     */
    private static function getAvailableProducts($serviceId, $productOfferingPricePointPairs)
    {
        $productsQueryRequest = new ProductsQueryRequest();
        $productsQueryRequest->setServiceId($serviceId);
        $productsQueryRequest->setProductOfferingPricePointPairs($productOfferingPricePointPairs);

        return static::getBillingApiClient()->getCustomerAvailableProducts($productsQueryRequest);
    }

    /**
     * For certain products e.g. call plans that include a mobile bolt-on, we get multiple sets of base prices from BillingAPI.
     * In this case they will share the same product offering id and price point id; we should add these prices together.
     *
     * @param $existingBasePricesForProduct array The base prices for the product we have already stored against the id
     * @param $newBasePricesForProduct      array The base prices we have found that share the same id
     *
     * @return array The merged array of current base price and current base price in contract
     */
    private static function mergeBasePricesForProduct($existingBasePricesForProduct, $newBasePricesForProduct)
    {
        $mergedBasePricesForProduct = array();

        $mergedBasePricesForProduct[self::CURRENT_BASE_PRICE_KEY] =
            $existingBasePricesForProduct[self::CURRENT_BASE_PRICE_KEY] +
            $newBasePricesForProduct[self::CURRENT_BASE_PRICE_KEY];

        $mergedBasePricesForProduct[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] =
            $existingBasePricesForProduct[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY] +
            $newBasePricesForProduct[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY];

        return $mergedBasePricesForProduct;
    }

    /**
     * Get an instance of the Billing Api Client
     *
     * @return \Plusnet\BillingApiClient\Facade\BillingApiFacade
     */
    private static function getBillingApiClient()
    {
        return \Plusnet\BillingApiClient\Service\ServiceManager::getService('BillingApiFacade');
    }

    /**
     * Calls into ProductComponent_ProductOfferingService to get the correct product offering id to use for a request
     * to BillingApi to get the price for a given product. Returns null if the service component id is null and the
     * service definition id is not a recognised legacy service definition
     *
     * @param      integer|string $serviceDefinitionId The service definition id of the product
     * @param null|integer|string $serviceComponentId  Optional, the service component id of the product
     *
     * @return null|integer null if there is not enough information to create the product offering id or an integer
     */
    private static function getProductOfferingIdFromIds($serviceDefinitionId, $serviceComponentId)
    {
        $productOfferingService = new \ProductComponent_ProductOfferingService();

        $serviceDefinitionId = !empty($serviceDefinitionId) ? (int) $serviceDefinitionId               : null;
        $serviceComponentId  = !empty($serviceComponentId)  ? (int) $serviceComponentId                : null;
        $productComponentId  = !empty($serviceComponentId)  ? self::SUBSCRIPTION_PRODUCT_COMPONENT_ID  : null;

        if ($serviceComponentId === null
            && (static::isServiceDefinitionNotInRBM($serviceDefinitionId) ||
                !$productOfferingService->isKnownLegacyInternetAccountType($serviceDefinitionId))) {

            // Not enough information to get product offering id
            return null;
        }

        return $productOfferingService->getProductOfferingFromIds(
            $serviceDefinitionId,
            $serviceComponentId,
            $productComponentId);
    }

    private static function isServiceDefinitionNotInRBM($serviceDefinitionId)
    {
        return in_array($serviceDefinitionId, self::SERVICE_DEFINITIONS_NOT_IN_RBM);
    }
}