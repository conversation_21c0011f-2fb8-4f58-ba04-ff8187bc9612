<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\BillingApiClient\Exceptions\BillingApiClientQueueException;
use Plusnet\BillingApiClient\Service\ServiceManager;

class AccountChange_BillingPendingReratingPolicyTest extends PHPUnit_Framework_TestCase
{
    /**
     * tearDown
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @test
     * @dataProvider pendingReratingProvider
     * @param array $billingApiResponse billing api response
     */
    public function shouldValidateCorrectly($billingApiResponse, $willPassValidation)
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(123345);

        $billingApiMock = Mockery::mock('BillingApiFacade');
        $billingApiMock->shouldR<PERSON>eive('hasPendingReratingRequests')
            ->once()
            ->with(123345)
            ->andReturn($billingApiResponse);

        ServiceManager::setService('BillingApiFacade', $billingApiMock);

        $test = new AccountChange_BillingPendingReratingPolicy($actor, true, false, []);
        if ($willPassValidation) {
            $this->assertTrue($test->validate());
        } else {
            $this->assertFalse($test->validate());
            $this->assertEquals(AccountChange_BillingPendingReratingPolicy::FAILURE_REASON, $test->getFailure());
            $this->assertEquals(AccountChange_BillingPendingReratingPolicy::ERROR_CODE, $test->getErrorCode());
        }
    }

    /**
     * @return array
     */
    public function pendingReratingProvider()
    {
        return [
            'Valid - request count 0' => [
                'billingApiResponse' => ['requestCount' => 0],
                'willPassValidation' => true
            ],
            'Valid - empty array response' => [
                'billingApiResponse' => [],
                'willPassValidation' => true
            ],
            'Valid - null response' => [
                'billingApiResponse' => null,
                'willPassValidation' => true
            ],
            'Invalid - request count > 0' => [
                'billingApiResponse' => ['requestCount' => 1],
                'willPassValidation' => false
            ]
        ];
    }

    /**
     * @test
     * @dataProvider exceptionProvider
     * @param string $expectedException Exception
     */
    public function shouldHandleBillingApiClientExceptions($expectedException)
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(123345);

        $billingApiMock = Mockery::mock('BillingApiFacade');
        $billingApiMock->shouldReceive('hasPendingReratingRequests')
            ->once()
            ->andThrow($expectedException);

        ServiceManager::setService('BillingApiFacade', $billingApiMock);

        $test = new AccountChange_BillingPendingReratingPolicy($actor, true, false, []);
        $this->assertFalse($test->validate());
        $this->assertEquals(AccountChange_BillingPendingReratingPolicy::RBM_FAIL_REASON, $test->getFailure());
        $this->assertEquals(AccountChange_BillingPendingReratingPolicy::ERROR_CODE, $test->getErrorCode());
    }

    /**
     * @return array
     */
    public function exceptionProvider()
    {
        return [
            'BillingApiClientQueueException' => [BillingApiClientQueueException::class],
            'BillingApiClientBillingServiceException' => [BillingApiClientBillingServiceException::class],
        ];
    }
}
