<?php
/**
 * <AUTHOR> <Reecequa<PERSON><EMAIL>>
 */

use Plusnet\ContractsClient\Entity\CreationReason;
use Plusnet\ContractsClient\Instance\InstanceInterface as Contract;

class AccountChange_CompleteContractHelper
{
    /**
     * @var string $serviceId
     */
    private $serviceId;

    /**
     * @var ProductFamily_Generic $productFamily
     */
    private $productFamily;

    /**
     * AccountChange_CompleteContractHelper constructor.
     * @param string                $serviceId     service id
     * @param ProductFamily_Generic $productFamily product family
     */
    public function __construct($serviceId, $productFamily)
    {
        $this->serviceId = $serviceId;
        $this->productFamily = $productFamily;
    }

    /**
     * @return array
     */
    public function getContractDetailsForEmail()
    {
        $arrContractDetails = array();
        $contractsClient = BusTier_BusTier::getClient('contracts');
        $contractsClient->setServiceId($this->serviceId);

        $contractsCriteria = array(
            'status' => Contract::STATUS_ACTIVE,
            'serviceId' => $this->serviceId,
        );

        $contract = $contractsClient->getContract($contractsCriteria);

        if ($contract && $contract->getStatus() == Contract::STATUS_ACTIVE) {
            $isContractRetained = true;
            $currentDate = $this->getTodaysDate();
            $contractStartDate = I18n_Date::fromString($contract->getStartDate());

            if ($currentDate->getTimestamp() == $contractStartDate->getTimestamp() &&
                $contract->getCreationReason() == CreationReason::ACCOUNT_CHANGE
            ) {
                $isContractRetained = false;
            }

            $arrContractDetails['isContractRetained'] = $isContractRetained;
            $duration = $contract->getDuration();
            $arrContractDetails['intContractLengthInMonths'] = $duration['value'];
            $arrContractDetails['strContractEndDate'] = $contract->getEndDate();
        }

        $arrContractDetails['isNewProductDualPlay'] = $this->productFamily->isDualPlay();
        return $arrContractDetails;
    }

    /**
     * @return I18n_Date
     */
    protected function getTodaysDate()
    {
        return I18n_Date::fromString(date('Y-m-d'));
    }
}
