<?php

use Plusnet\C2mApiClient\C2MClient as C2MClient;
use Plusnet\C2mApiClient\Entity\Promotion;

/**
 * Class AccountChange_C2mPromotionsHelper
 *
 * Enables consumption of promotions from C2MAPI
 *
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_C2mPromotionsHelper
{
    const PLUSNET_PROTECT_CHARGEABLE_ID = 'PlusnetProtectChargeable';

    /**
     * Names of the sales channels we want to consume.
     *
     * @var string
     */
    private $salesChannel;

    /**
     * @var \Plusnet\C2mApiClient\C2MClient
     */
    private $c2mClient;

    /**
     * A collection of Promotion objects on the salesChannels
     *
     * @var array<Promotion>
     */
    private $promotions = [];

    /**
     * A collection of rules to apply to the promotions.
     *
     * @var \PromotionRules
     */
    private $promotionRules;

    /**
     * Return the Error code of the Policy
     *
     * @var string
     */
    private $promoCode;

    /**
     * C2mSalesChannelPromotions constructor.
     *
     * @param \Plusnet\C2mApiClient\C2MClient $c2mClient
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotionRules
     * @param string $salesChannel
     * @param string $promoCode
     *
     * @throws PromotionNotFoundException
     */
    public function __construct(
        C2MClient $c2mClient,
        PromotionRules $promotionRules,
        $salesChannel,
        $promoCode
    )
    {
        $this->salesChannel = $salesChannel;
        $this->c2mClient = $c2mClient;
        $this->promotionRules = $promotionRules;
        $this->promoCode = $promoCode;
        $this->setPromotionsForPromoCodeOrChannel();
    }

    /**
     * Named constructor
     *
     * @param \Plusnet\C2mApiClient\C2MClient $c2mClient
     * @param string $salesChannel
     *
     * @return \C2MPromotionsHelper
     *
     * @throws PromotionNotFoundException
     */
    public static function getFromGivenSalesChannels(
        C2MClient $c2mClient,
        PromotionRules $rules,
        $salesChannel,
        $promoCode
    )
    {
        return new self(
            $c2mClient,
            $rules,
            $salesChannel,
            $promoCode
        );
    }

    /**
     *
     * Gets an active promotion with a given promocode, else returns null
     *
     * @param $promoCode
     * @return mixed (Promotion or boolean if not found)
     */
    public static function getActivePromotionWithPromocode($promoCode)
    {
        try {
            $c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
            $promotion = $c2mClient->getPromotion($promoCode);
            if ($promotion)
            {
                self::removePlusnetProtectDiscountIfPresent($promotion);
            }
        } catch (Exception $e) {
            error_log(__CLASS__ . '->' . __METHOD__ . $e->getMessage());
            return false;
        }

        return $promotion;
    }

    /**
     * @return array<Promotion>
     */
    public function getPromotions()
    {
        return $this->promotions;
    }

    /**
     * @param array<Promotion>
     */
    private function setPromotions(array $promotions, $overwriteAll = false)
    {
        if (!$overwriteAll) {
            $this->promotions = array_merge(
                $this->promotions,
                $promotions
            );
            return;
        }
        $this->promotions = $promotions;
    }

    /**
     *
     */
    public function applyRules()
    {
        $overwriteCurrentPromotionList = true;
        $promotions = $this->getPromotions();
        $promotions = $this->promotionRules->handle($promotions);
        $this->setPromotions($promotions, $overwriteCurrentPromotionList);
    }

    /**
     * Returns SalesChannel
     * @return string
     */
    public function getSalesChannel()
    {
        return $this->salesChannel;
    }

    /**
     * Set the promotions by loading them from the Sales Channel provided to
     * the constructor.
     */
    private function setPromotionsFromSalesChannel()
    {
        $promotions = $this->c2mClient->getPromotionsForSalesChannelAndCharacteristics(
            $this->salesChannel
        );
        foreach ($promotions as $promotion)
        {
            AccountChange_C2mPromotionsHelper::removePlusnetProtectDiscountIfPresent($promotion);
        }
        $this->setPromotions($promotions, true);
    }

    /**
     * Set the promotions by loading them from the Sales Channel provided to
     * the constructor.
     *
     * @throws PromotionNotFoundException
     */
    private function setPromotionsFromPromocode()
    {
        $promotion = $this->c2mClient->getPromotion(
            $this->promoCode
        );
        $promotions = array();
        if ($promotion && in_array($this->salesChannel, $promotion->getSalesChannels())) {
            AccountChange_C2mPromotionsHelper::removePlusnetProtectDiscountIfPresent($promotion);
            array_push($promotions, $promotion);
        }
        $this->setPromotions($promotions, true);
    }

    /**
     * Set Promotion from given Promocode or channel.
     *
     * @throws PromotionNotFoundException
     */
    private function setPromotionsForPromoCodeOrChannel()
    {
        if ($this->promoCode) {
            $this->setPromotionsFromPromocode();
        } else {
            $this->setPromotionsFromSalesChannel();
        }
    }

    /**
     * Returns true if the given promotion includes a line rental discount
     *
     * @param Plusnet\C2mApiClient\Entity\Promotion $promotion A promotion object
     *
     * @return bool
     **/
    public static function promotionHasLineRentalDiscount($promotion)
    {
        $discounts = $promotion->getDiscounts();

        if (!is_array($discounts) || count($discounts) < 1) {
            return false;
        }

        foreach ($discounts as $discount) {
            $paymentInformationList = $discount->getProductOfferingPaymentInformations();
            if (is_array($paymentInformationList) && count($paymentInformationList) > 0) {
                foreach ($paymentInformationList as $paymentInformation) {
                    if ($paymentInformation->getName() == 'LineRental') {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Most of this system cannot handle plusnet protect discount and the business has decided that it shouldn't be
     * applied.
     * @param Promotion\ $promotion The promotion to modify
     */
    private static function removePlusnetProtectDiscountIfPresent($promotion)
    {
        $discounts = $promotion->getDiscounts();
        foreach ($discounts as $discountKey => $discount) {
            $productInformationList = $discount->getProductOfferingPaymentInformations();
            if (is_array($productInformationList) && count($productInformationList) > 0) {
                foreach ($productInformationList as $paymentInformation) {
                    if ($paymentInformation->getName() == self::PLUSNET_PROTECT_CHARGEABLE_ID) {
                        unset($discounts[$discountKey]);
                    }
                }
            }
        }
        $promotion->setDiscounts($discounts);
    }
}
