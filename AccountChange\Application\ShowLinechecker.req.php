<?php
/**
 * Line Checker Requirement
 *
 * Collecting the data for the linke checker requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @since     File available since 2008-09-29
 */
/**
 * AccountChange_ShowLinechecker class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_ShowLinechecker extends Mvc_WizardRequirement
{
    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'intPhoneNumber' => 'external:Custom',
        'strPostCode'    => 'external:Custom',
        'bolHousemove'   => 'external:Bool:optional',
        'strAddressReference' => 'external:String:optional',
        'strCssDatabaseCode' => 'external:String:optional',
        'strThoroughFareNumber' => 'external:String:optional'
    );

    /**
     * Validate the phone number as being a UK Landline
     * Validate the postcode
     *
     * If {@link LineCheck_BtRequestException} was thrown during linecheck
     * an additional element will be added to returned array containing the exception object
     * An array is returned as follows:
     * <pre>
     * array (
     *  'LineCheckPhoneNumber' => Val_UKLandlinePhoneNumber or '',
     *  'bolBtException' => [BOOL] (if {@link LineCheck_BtRequestException} thrown)
     * )
     * </pre>
     *
     * @param int     $intPhoneNumber Phone number
     * @param string  $strPostCode    Post code
     * @param boolean $bolHousemove   House Move flag
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses Val_UKLandlinePhoneNumber::getValidated()
     * @uses Val_Postcode::getValidated()
     * @uses AccountChange_ShowLinechecker::performLineCheck()
     *
     * @return array
     */
    public function valLineCheckInputs($intPhoneNumber, $strPostCode, $strAddressReference = '',  $strCssDatabaseCode = '', $strThoroughFareNumber = '', $bolHousemove = false)
    {
        $arrValidatedReturn = array();

        if (empty($intPhoneNumber) && !$bolHousemove) {

            $arrValidatedReturn['intPhoneNumber'] = '';
            $this->addValidationError('intPhoneNumber', 'MISSING');
        } elseif (empty($strPostCode)) {

            $arrValidatedReturn['strPostCode'] = '';
            $this->addValidationError('strPostCode', 'MISSING');
        } else {

            $objCliNumber = Val_UKLandlinePhoneNumber::getValidated($intPhoneNumber);
            $objPostCode = Val_Postcode::getValidated($strPostCode);

            if (!(isset($objCliNumber) && $objCliNumber instanceof Val_UKLandlinePhoneNumber) && !$bolHousemove) {

                $arrValidatedReturn['intPhoneNumber'] = '';
                $this->addValidationError('intPhoneNumber', 'INVALID');
            } elseif (!(isset($objPostCode) && $objPostCode instanceof Val_Postcode)) {

                $arrValidatedReturn['strPostCode'] = '';
                $this->addValidationError('strPostCode', 'INVALID');
            } else {
                $arrResult = $this->performLineCheck(
                    $objCliNumber,
                    $objPostCode,
                    $strAddressReference,
                    $strCssDatabaseCode,
                    $strThoroughFareNumber,
                    $bolHousemove
                );
                $arrValidatedReturn['intPhoneNumber'] = $objCliNumber;
                $arrValidatedReturn['strPostCode'] = $objPostCode;

                if (!empty($arrResult['LineCheckPhoneNumber'])
                    && $arrResult['LineCheckPhoneNumber'] instanceof LineCheck_BtRequestException
                ) {

                    $arrValidatedReturn['bolBtException'] = true;
                }
            }
        }
        return $arrValidatedReturn;
    }

    /**
     * Performs linecheck, fetches linecheck results and does application error handling.
     * Currently it only runs linecheck for CLI (postcode linechecks are not available for account change process)
     *
     * When linecheck results are retrieved successfuly the following array is returned:
     * <pre>
     * array (
     *  'LineCheckPhoneNumber'=> Val_UKLandlinePhoneNumber,
     *  'objLinecheckResult'  => LineCheck_Result
     * )
     * </pre>
     * If BT Linechecker is not available validation is marked as successful but customer has to agree
     * to progress with account change without valid linecheck results. An appropriate option
     * (and requirement {@link AccountChange_SelectBroadband} is gathered on Select Broadband product page)
     * The following array is returned in that case:
     * <pre>
     * array (
     *  'LineCheckPhoneNumber'=> LineCheck_BtRequestException
     * )
     * </pre>
     * In case of any other error an appropriate validation error is added and the following array is returned:
     * <pre>
     * array (
     *  'LineCheckPhoneNumber'=> ''
     * )
     * </pre>
     *
     * @param Val_UKLandlinePhoneNumber $objCliNumber Val_UKLandlinePhoneNumber
     * @param Val_Postcode              $objPostCode  Val_Postcode
     * @param boolean                   $bolHouseMove House Move flag
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses AccountChange_ShowLinechecker::getLineCheckResult()
     * @uses insertPendingServiceLineData
     *
     * @see LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO
     * @see LineCheck_Result
     *
     * @return array
     */
    public function performLineCheck(Val_UKLandlinePhoneNumber $objCliNumber, Val_Postcode $objPostCode, $strAddressReference = '',  $strCssDatabaseCode = '', $strThoroughFareNumber = '', $bolHouseMove = false)
    {
        try {
            $objLineCheckResult = $this->lineChecker()->performLineCheck(
                $objCliNumber,
                $objPostCode,
                $strAddressReference,
                $strCssDatabaseCode,
                $strThoroughFareNumber,
                $bolHouseMove,
                $this->getApplicationStateVariable('objCoreService'),
                $this->getApplicationStateVariable('objLineCheckResult'));

        } catch (AccountChange_LineCheckResultException $lineCheckResultException) {

            switch ($lineCheckResultException->getLineCheckErrorId()) {
                case LineCheck_Result::ERR_ID_INVALID_TELNO:
                    $this->addValidationError('intPhoneNumber', 'LINE_CHECK_INVALID_TELNO');
                    return array('LineCheckPhoneNumber'=> '');
                    break;
                case LineCheck_Result::ERR_ID_NOT_BT_LINE:
                case LineCheck_Result::ERR_ID_TELNO_CEASED:
                case LineCheck_Result::ERR_ID_TELNO_NOT_FOUND:
                    $this->addValidationError('intPhoneNumber', 'LINE_CHECK_TELNO_NOT_FOUND');
                    return array('LineCheckPhoneNumber'=> '');
                    break;
                case LineCheck_Result::ERR_ID_POST_CODE_NOT_FOUND:
                    $this->addValidationError('strPostCode', 'LINE_CHECK_POST_CODE_NOT_FOUND');
                    return array('LineCheckPostCode'=> '');
                    break;
                case LineCheck_Result::ERR_ID_INVALID_POST_CODE:
                    $this->addValidationError('strPostCode', 'LINE_CHECK_INVALID_POST_CODE');
                    return array('LineCheckPostCode'=> '');
                    break;
                default:
                    $this->addValidationError('intPhoneNumber', 'LINE_CHECK_FAILED_PN');
                    return array('LineCheckPhoneNumber'=> '');
                    break;
            }
        } catch (LineCheck_BtRequestException $btre) {
            error_log($btre->getMessage().'\n'.$btre->getTraceAsString());
            //BT is down ? Offer OptOut for customer to continue without LineCheck results
            Dbg_Dbg::write('Exception : '. $btre->getMessage(), 'AccountChange.ShowLinechecker');

            $this->lineChecker()->insertPendingServiceLineData($this->getApplicationStateVariable('objCoreService'));

            return array('LineCheckPhoneNumber'=> $btre);

        } catch (Exception $e) {
            error_log($e->getMessage().'\n'.$e->getTraceAsString());
            $this->addValidationError('intPhoneNumber', 'LINE_CHECK_FAILED_PN');
            Dbg_Dbg::write('Exception : '. $e->getMessage(), 'AccountChange.ShowLinechecker');

            return array('LineCheckPhoneNumber'=> '');
        }

        return array(
            'LineCheckPhoneNumber'=> $objCliNumber,
            'LineCheckPostCode' => $objPostCode,
            'objLinecheckResult' => $objLineCheckResult
        );
    }

    protected function lineChecker()
    {
        return new AccountChange_LineChecker();
    }
}
