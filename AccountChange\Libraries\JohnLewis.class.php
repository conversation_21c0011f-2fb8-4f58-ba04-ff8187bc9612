<?php
/**
 * <PERSON>
 *
 * A set of common account change functions for <PERSON>
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-11-24
 */
/**
 * <PERSON>
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class AccountChange_JohnLewis
{
    /**
    * Returns true if customer is switching from a Greenbee/Waitrose product to a John Lewis product.
    *
    * @param integer $intOldServiceDefinitionId Old Service Definition ID
    * @param integer $intNewServiceDefinitionId New Service Definition ID
    *
    * @return boolean
    */
    public static function isCustomerSwitchingFromGbWrToJlp($intOldServiceDefinitionId, $intNewServiceDefinitionId)
    {
        $oldProductDetails = new Core_ServiceDefinition($intOldServiceDefinitionId);
        $newProductDetails = new Core_ServiceDefinition($intNewServiceDefinitionId);

        $isOldProductGreenbeeOrWaitrose = in_array($oldProductDetails->getIsp(), array('greenbee', 'waitrose'));

        return ($isOldProductGreenbeeOrWaitrose && $newProductDetails->getIsp() == 'johnlewis');
    }
}
