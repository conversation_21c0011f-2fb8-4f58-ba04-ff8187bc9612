<?php
/**
 * Appointment Booking / Storing Action
 *
 * Books an appointment for an engineer to upgrade a line
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 PlusNet
 */

/**
 * Appointment Booking Action class
 *
 * Books an appointment for an engineer to upgrade a line
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
class AccountChange_Action_Appointment extends AccountChange_Action
{
    /**
     * Holder for data passed from the Action Manager
     *
     * @var array
     */
    protected $data;

    /**
     * Overrides default constructor to add obtaining the relevent data from the registry.
     *
     * @param int   $intServiceId service Id
     * @param array $arrOptions   array of options
     *
     * @see AccountChange_Action::_construct
     * @return void
     */
    public function __construct($intServiceId, $arrOptions)
    {
        parent::__construct($intServiceId, $arrOptions);
        $this->data = AccountChange_Registry::instance()->getEntry('appointing');
    }

    /**
     * Execute the action
     * If we have a single appointment, book it.
     * If we have 3 appointment dates, store them and raise a ticket
     * If we have neither, do nothing.
     *
     * @return void
     */
    public function execute()
    {
        if (isset($this->data['appointment'])) {

            $this->bookLiveAppointment();

        } elseif (isset($this->data['appointmentdate1'])) {

            $this->storeAppointmentSlots();
        }
    }

    /**
     * Obtain all the necessary information and book an Engineer Appointment
     *
     * @return void
     */
    public function bookLiveAppointment()
    {

        $serviceId      = new Int($this->intServiceId);
        $service        = $this->data['appointingType']['service'];

        $addressRefSplit = explode(':', $this->data['addressRef']);

        $data = array(
            'addressRef'                   => $addressRefSplit[0],
            'cssDatabaseCode'              => $addressRefSplit[1],
            'cli'                          => $this->data['intPhoneNumber'],
            'engineerAppointment'          => $this->data['appointment'],
            'extensionKitId'               => isset($this->data['extensionKitId']) ? $this->data['extensionKitId'] :
                                              EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED,
            'engineerAppointmentConfirmed' => false,
            'engineerNotes'                => $this->data['engineeringNotes'],
            );

        $client = $this->getEngineerAppointmentClient($service, $data);

        try {

            $client->bookAppointment($serviceId);

        } catch (Exception $e) {
            $text = '<strong>Live Appointing failed</strong> <br />'
                  . 'An engineer appointment failed to book correctly for an account change.<br />'
                  . 'The appointment requested was: <strong>' . $this->data['appointment'];

            $this->raiseTicket($text, 'FTTC_APPOINTMENTS');
        }
    }

    /**
     * Store the appointment dates for booking manually.  Raise a ticket with all the necessary info.
     *
     * @return void
     */
    public function storeAppointmentSlots()
    {
        $serviceId      = new Int($this->intServiceId);

        $appointmentdate1 = $this->data['appointmentdate1'];
        $appointmentdate2 = $this->data['appointmentdate2'];
        $appointmentdate3 = $this->data['appointmentdate3'];

        $appointmenttime1 = $this->data['appointmenttime1'];
        $appointmenttime2 = $this->data['appointmenttime2'];
        $appointmenttime3 = $this->data['appointmenttime3'];

        $service        = $this->data['appointingType']['service'];

        $data = array(
            'cli'                          => $this->data['intPhoneNumber'],
            'extensionKitId'               => isset($this->data['extensionKitId'])
                                              ? $this->data['extensionKitId']
                                              : EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED,
            'engineerAppointmentConfirmed' => false,
            'engineerNotes'                => $this->data['engineeringNotes'],
            'appointments' => array(
                array(
                    'priority' => 1,
                    'date' => date('Y/m/d', $appointmentdate1),
                    'slot' => $appointmenttime1,
                ),
                array(
                    'priority' => 2,
                    'date' => date('Y/m/d', $appointmentdate2),
                    'slot' => $appointmenttime2,
                ),
                array(
                    'priority' => 3,
                    'date' => date('Y/m/d', $appointmentdate3),
                    'slot' => $appointmenttime3,
                ),
            ),
        );

        $client = $this->getEngineerAppointmentClient($service, $data);
        $client->storeAppointment($serviceId);

        $text = '<strong>Engineer Appointment Required</strong> <br />'
              . 'Manual appointment booking is required following an account change.<br />'
              . 'The following dates were selected:<br />'
              . '<strong>1) ' . date('d/m/Y', $appointmentdate1) . ' ' . $appointmenttime1 . ' <br />'
              . '<strong>2) ' . date('d/m/Y', $appointmentdate2) . ' ' . $appointmenttime2 . ' <br />'
              . '<strong>3) ' . date('d/m/Y', $appointmentdate3) . ' ' . $appointmenttime3 . ' <br />';

        $this->raiseTicket($text, 'FTTC_APPOINTMENTS');
    }

    /**
     * Get the EngineerAppointmentClient client
     *
     * @param string $service the service
     * @param array  $data    array of data
     *
     * @return EngineerAppointmentClient_Client
     */
    public function getEngineerAppointmentClient($service, $data)
    {
        $container = $this->getServiceContainer();
        $container->add($service, $data);
        return new EngineerAppointmentClient_Client($container);
    }

    /**
     * Return a new service container
     *
     * @return EngineerAppointmentClient_Service_Container
     */
    protected function getServiceContainer()
    {
        return new EngineerAppointmentClient_Service_Container();
    }
}
