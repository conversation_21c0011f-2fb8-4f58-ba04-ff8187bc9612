<?php
/**
 * Home Phone Workplace Requirement
 *
 * Testing class for the AccountChange_SelectHomephoneWorkplace requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: SelectHomephoneWorkplace.test.php,v 1.3 2009-05-11 04:19:04 fzaki Exp $
 * @link      ed in
 * @since     File available since 2008-12-17
 */
/**
 * Home Phone Workplace Requirement Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      ed in
 */
class AccountChange_SelectHomephoneWorkplace_Test extends PHPUnit_Framework_TestCase
{

    /**
     * test setup
     *
     * @return void
     */
    public function setUp()
    {
        // Toggle on
        $_SERVER[\Plusnet\Feature\Toggle::KEY] = 'wlr-pricing-2013';
        parent::setUp();
    }

    /**
     * test teardown
     *
     * @return void
     */
    public function tearDown()
    {
        // Toggle reset
        unset($_SERVER[\Plusnet\Feature\Toggle::KEY]);
        parent::tearDown();
    }

    /**
     * Checks for the arrInput variables for the requirement
     *
     * @return void
     */
    public function testArrInputIsSetupCorrectly()
    {
        $arrInputs = array(
            'intNewWlrId' => 'external:Custom',
            'bolContractResetWlr' => 'external:Custom:optional',
            'callerDisplay' => 'external:custom:optional',
            'selectedContractDuration' => 'external:custom:optional'
        );

        $objReq = new AccountChange_SelectHomephoneWorkplace();

        $this->assertAttributeEquals($arrInputs, 'arrInputs', $objReq);
    }

    /**
     * Checks for the arrCustomValidatorOrder is setup correctly for the requirement
     *
     * @return void
     */
    public function testArrCustomValidatorOrderIsSetupCorrectly()
    {
        $arrInputs = array(
            'valNewWlrId',
            'valSelectedContractDuration',
            'valContractResetWlr',
            'valTakeContractChargesWlr'
        );

        $objReq = new AccountChange_SelectHomephoneWorkplace();

        $this->assertAttributeEquals($arrInputs, 'arrCustomValidatorOrder', $objReq);
    }

    /**
     * Dispatch to the describe method with some helpful data setup
     *
     * @param array $setup parameters to setup the state of the test
     *
     * @return array View data passed to template
     */
    public function doDescribe(array $setup)
    {
        $objReq = $this->getMock(
            'AccountChange_SelectHomephoneWorkplace',
            array(
                'getWlrProducts',
                'encryptVar',
                'hasActiveLineRentalSaver',
                'isFibreProduct',
                'includeLegacyFiles',
                'getWlrProductSorter',
                'getWlrProductFilter',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges'
            )
        );
        // Sorted list
        $arrProducts = array(
            array(
                'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
                'strProductName' => 'Plusnet Anytime', 'strContract' => 'MONTHLY',
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
            ),
            array(
                'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_WITH_MOBILE,
                'strProductName' => 'Plusnet Anytime with Mobile', 'strContract' => 'MONTHLY',
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
            ),
            array(
                'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
                'strProductName' => 'Plusnet No Calls', 'strContract' => 'MONTHLY',
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'isDefaultLrsProduct' => true
            ),
            array(
                'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY_WITH_MOBILE,
                'strProductName' => 'Plusnet No Calls with Mobile', 'strContract' => 'MONTHLY',
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 15.95),
                'isDefaultLrsProduct' => true
            ),
            array(
                'intNewWlrId' => AccountChange_Product_WlrProductFilter::FALCON_LEGACY_EVENINGS_AND_WEEKENDS,
                'strProductName' => 'Talk Evenings & Weekends', 'strContract' => 'MONTHLY',
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 14.99),
            ),
            array(
                'intNewWlrId' => AccountChange_Product_WlrProductFilter::FALCON_LEGACY_INTERNATIONAL,
                'strProductName' => 'Talk Anytime International 300', 'strContract' => 'MONTHLY',
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 14.99),
            ),
            array(
                'intNewWlrId' => AccountChange_Product_WlrProductFilter::FALCON_LEGACY_ANYTIME,
                'strProductName' => 'Talk Anytime', 'strContract' => 'MONTHLY',
                'intLineRentCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 14.99),
            )
        );
        $objReq->expects($this->once())
            ->method('getWlrProducts')
            ->will($this->returnValue($arrProducts));
        $objReq->expects($this->any())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue($setup['hasLrs']));
        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));
        $objReq->expects($this->any())
            ->method('encryptVar')
            ->will($this->returnArgument(0));
        $state = $this->getMock(
            'Mvc_ApplicationStateCallback',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );
        $objReq->expects($this->any())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(array()));
        $objReq->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will($this->returnValue(array('downloadSpeedRangeMin' => null, 'downloadSpeedRangeMax' => null)));
        $objReq->setAppStateCallback($state);
        $coreService = $this->getMock('Core_Service');
        $coreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(123456));
        $map = array(
            'intNewSdi' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            'objCoreService' => $coreService,
            'arrWlrProduct' => array(
                'intOldWlrId' => $setup['oldWlrProductId'],
                'strContractHandle' => 'MONTHLY'
            )
        );
        $state->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function ($_, $arg) use ($map) {
                        return $map[$arg];
                    }
                )
            );

        $input = array();

        $mockFilter =  $this->getFilterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductFilter')
            ->will($this->returnValue($mockFilter));

        $mockSorter =  $this->getSorterMock();

        $objReq->expects($this->any())
            ->method('getWlrProductSorter')
            ->will($this->returnValue($mockSorter));

        return $objReq->describe($input);
    }


    public function provideDataForGetMappedMobileProductTest()
    {
        return array(
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_WITH_MOBILE
            )
        );
    }

    private function getFilterMock()
    {
        $filterMock = $this->getMock(
            'AccountChange_Product_WlrProductFilter',
            array('getPhoneHelper'),
            array(AccountChange_Product_WlrProductFilter::FILTER_TYPE_WORKPLACE)
        );


        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );


        $phoneHelperMock->expects($this->any())
            ->method('getServiceComponentBoltOnMappings')
            ->will(
                $this->returnValue(
                    $this->provideDataForGetMappedMobileProductTest()
                )
            );

        $filterMock
            ->expects($this->any())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        return $filterMock;
    }


    private function getSorterMock()
    {
        $sorterMock = $this->getMock(
            'AccountChange_Product_WlrProductSorter',
            array('getServiceComponentBoltOnMappings')
        );


        $sorterMock->expects($this->any())
            ->method('getServiceComponentBoltOnMappings')
            ->will(
                $this->returnValue(
                    $this->provideDataForGetMappedMobileProductTest()
                )
            );
        return $sorterMock;
    }

    /**
     * Test the case described by the method name
     *
     * @covers AccountChange_SelectHomephoneWorkplace::valContractResetWlr
     *
     * @return void
     */
    public function testContractResetWlrValidationReturnsTrueIfContractIsSet()
    {
        $objReq = new AccountChange_SelectHomephoneWorkplace();

        $arrResult = $objReq->valContractResetWlr(1);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('bolContractResetWlr', $arrResult);
        $this->assertTrue($arrResult['bolContractResetWlr']);
    }

    /**
     * Test the case described by the method name
     *
     * @covers AccountChange_SelectHomephoneWorkplace::valContractResetWlr
     *
     * @return void
     */
    public function testContractResetWlrValidationReturnsFalsIfContractIsNotSet()
    {
        $objReq = new AccountChange_SelectHomephoneWorkplace();

        $arrResult = $objReq->valContractResetWlr(0);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('bolContractResetWlr', $arrResult);
        $this->assertFalse($arrResult['bolContractResetWlr']);
    }

    /**
     * Test the case described by the method name
     *
     * @covers AccountChange_SelectHomephoneWorkplace::valNewWlrId
     *
     * @return void
     */
    public function testNewWlrIdValidatorReturnsAnInvalidErrorIfNewWlrIdIsNotNumeric()
    {
        $arrProducts = array(array(
            'intNewWlrId'       => 1111,
            'strContractHandle' => 'ANNUAL',
            'strProductName'    => 'Test Wlr Product',
            'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10)
        ));

        $strInvalidWlrId = 'aBadString';

        $objReq = $this->getMock(
            'AccountChange_SelectHomephoneWorkplace',
            array('getWlrProducts')
        );

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('getProductFamilyHandle'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->never())
            ->method('getProductFamilyHandle');

        $objReq->expects($this->once())
            ->method('getWlrProducts')
            ->will($this->returnValue($arrProducts));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->will($this->returnValue(123));

        $objMockController->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->will($this->returnValue(array()));

        $objReq->setAppStateCallback($objMockController);

        $arrResult = $objReq->valNewWlrId($strInvalidWlrId);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayNotHasKey('intNewWlrId', $arrResult);
        $this->assertArrayNotHasKey('arrSelectedWlr', $arrResult);

        $arrErrors = $objReq->getValidationErrors();
        $this->assertArrayHasKey('intNewWlrId', $arrErrors);
        $this->assertArrayHasKey('INVALID', $arrErrors['intNewWlrId']);
    }

    /**
     * @covers AccountChange_SelectHomePhoneWorkplace::valNewWlrId
     *
     */
    public function testNewWlrIdValidatorLetsCustomerStayOnNoPhone()
    {
        $arrProducts = array(
            array(
                'intNewWlrId' => 0,
                'strContract' => 'MONTHLY',
                'strProductName' => 'No Home Phone',
                'bolSplitPrice'     => false,
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                'intLineRentCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                'intCallPlanCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
            ),
            array(
                'intNewWlrId'       => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
                'strContractHandle' => 'MONTHLY',
                'strProductName'    => 'Test Wlr Product',
                'bolSplitPrice'     => true,
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10),
                'intLineRentCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
                'intCallPlanCost'   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 30),
            ));

        // Selected ID => co-ordinate clicked on button image
        $formInput = '0';

        $objReq = $this->getMock(
            'AccountChange_SelectHomephoneWorkplace',
            array(
                'getWlrProducts',
                'getApplicationStateVariable',
                'hasActiveLineRentalSaver',
                'isFibreProduct'
            )
        );

        $objReq->expects($this->once())
               ->method('getWlrProducts')
               ->with(123, null, true)
               ->will($this->returnValue($arrProducts));

        $objReq->expects($this->any())
               ->method('hasActiveLineRentalSaver')
               ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $productFamily = $this->getMock('Object', array('isDualPlay'));
        $productFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        $map = array(
            'intNewSdi' => 123,
            'arrWlrProduct' => array(),
            'selectedProductFamily' => $productFamily
        );
        $objMockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function ($_, $arg) use ($map) {
                        return $map[$arg];
                    }
                )
            );
        $objMockController->expects($this->any())
            ->method('isApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function ($_, $arg) use ($map) {
                        return isset($map[$arg]);
                    }
                )
            );
        $objReq->setAppStateCallback($objMockController);

        $arrResult = $objReq->valNewWlrId($formInput);

        $arrErrors = $objReq->getValidationErrors();
        $this->assertArrayNotHasKey('intNewWlrId', $arrErrors);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('intNewWlrId', $arrResult);
        $this->assertArrayHasKey('arrSelectedWlr', $arrResult);
        $this->assertEmpty($arrResult['callerDisplay']);
        $this->assertInternalType('array', $arrResult['arrSelectedWlr']);
        $this->assertEquals('No Home Phone', $arrResult['arrSelectedWlr']['strNewProduct']);
        $this->assertEquals(0, $arrResult['arrSelectedWlr']['intNewCost']->toDecimal());
        $this->assertEquals(false, $arrResult['arrSelectedWlr']['bolSplitPrice']);
        $this->assertEquals(0, $arrResult['arrSelectedWlr']['intNewLineRentCost']->toDecimal());
        $this->assertEquals(0, $arrResult['arrSelectedWlr']['intNewCallPlanCost']->toDecimal());
    }

    /**
     * Test new wlr id validator returns a valid wlr id and name and cost
     *
     * @covers AccountChange_SelectHomephoneWorkplace::valNewWlrId
     *
     * @return void
     */
    public function testNewWlrIdValidatorReturnsAValidWlrIdAndNameAndCost()
    {
        $intValidWlrId  = AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME;
        $strValidName   = 'Test Wlr Product';
        $intProductCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10);
        $intLineRentCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20);
        $intCallPlanCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 30);
        $bolSplitPrice   = 1;
        $arrCallerDisplay = ['callerDisplay' => 'on'];

        $arrProducts = array(array(
            'intNewWlrId'       => $intValidWlrId,
            'strContractHandle' => 'ANNUAL',
            'strProductName'    => $strValidName,
            'intProductCost'    => $intProductCost,
            'intLineRentCost'   => $intLineRentCost,
            'intCallPlanCost'   => $intCallPlanCost,
            'bolSplitPrice'     => $bolSplitPrice
        ));

        $objReq = $this->getMock(
            'AccountChange_SelectHomephoneWorkplace',
            array('getWlrProducts', 'hasActiveLineRentalSaver', 'isFibreProduct')
        );

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('getProductFamilyHandle'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->never())
            ->method('getProductFamilyHandle');

        $objReq->expects($this->once())
            ->method('getWlrProducts')
            ->will($this->returnValue($arrProducts));

        $objReq->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));

        $objReq->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->will($this->returnValue(123));

        $objMockController->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->will($this->returnValue(array()));

        $objReq->setAppStateCallback($objMockController);

        $arrResult = $objReq->valNewWlrId($intValidWlrId, $arrCallerDisplay);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('intNewWlrId', $arrResult);
        $this->assertArrayHasKey('arrSelectedWlr', $arrResult);
        $this->assertArrayHasKey('callerDisplay', $arrResult);
        $this->assertEquals($arrCallerDisplay, $arrResult['callerDisplay']);
        $this->assertInternalType('array', $arrResult['arrSelectedWlr']);
        $this->assertEquals($strValidName, $arrResult['arrSelectedWlr']['strNewProduct']);
        $this->assertEquals($intProductCost, $arrResult['arrSelectedWlr']['intNewCost']);
        $this->assertEquals($bolSplitPrice, $arrResult['arrSelectedWlr']['bolSplitPrice']);
        $this->assertEquals($intLineRentCost, $arrResult['arrSelectedWlr']['intNewLineRentCost']);
        $this->assertEquals($intCallPlanCost, $arrResult['arrSelectedWlr']['intNewCallPlanCost']);

        $arrErrors = $objReq->getValidationErrors();
        $this->assertArrayNotHasKey('intNewWlrId', $arrErrors);
    }
}
