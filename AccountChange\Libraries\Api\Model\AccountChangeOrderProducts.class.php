<?php
class AccountChange_AccountChangeOrderProducts
{
    /**
     * @var string
     */
    const PHONE_COMPONENT_ID = 'phoneComponentId';

    /**
     * @var string
     */
    const HARDWARE_COMPONENT_ID = 'hardwareComponentId';

    const PRODUCT_TYPE_BROADBAND = 'BROADBAND';
    const PRODUCT_TYPE_CALL_PLAN = 'CALL_PLAN';
    const PRODUCT_TYPE_MOBILE_BOLT_ON = 'MobileBoltOn';
    const PRODUCT_TYPE_CALLER_DISPLAY = 'CallerDisplay';
    const PRODUCT_TYPE_ROUTER = 'ROUTER';

    /**
     * @var int
     */
    private $serviceDefinitionId;

    /**
     * In this context, serviceComponentId is only referring the the broadband
     * product, in some uses this is set instead of a service definition id.
     *
     * For other service components (e.g. phone product, hardware etc) we set into
     * serviceComponentIds below..
     *
     * @var int
     */
    private $serviceComponentId;

    /**
     * Array of other (non broadband product) service component ids..
     *
     * @var array
     */
    private $serviceComponentIds;

    /**
     * Boolean indicating whether the user wants caller display. If left as null it default to not set in the registry.
     *
     * @var bool
     */
    private $wantCallerDisplay;

    /**
     * @var boolean
     */
    private $removePhone = false;

    /**
     * An alternative way of sending products, in a form like:
     *
     *
     * "products": {
     * "products": [
     * {
     * "type": "ROUTER",
     * "serviceComponentId": 2222
     * },
     * {
     * "type": "CALL_PLAN",
     * "serviceComponentId": 1638
     * },
     * {
     * "type": "MobileBoltOn",
     * "selected": true
     * },
     * {
     * "type": "BROADBAND",
     * "serviceComponentId": 1631
     * },
     * {
     * "type": "PostageAndPackaging",
     * "selected": true
     * },
     * {
     * "type": "LINE_RENTAL",
     * "selected": true
     * },
     * {
     * "type": "CallerDisplay",
     * "option": "STANDARD",
     * "productComponentId": 11
     * }
     * ]
     * },
     *
     *
     * @var array
     *
     */
    private $products;

    /**
     * @param int $serviceDefinitionId
     */
    public function setServiceDefinitionId($serviceDefinitionId)
    {
        $this->serviceDefinitionId = $serviceDefinitionId;
    }

    /**
     * @return int
     */
    public function getServiceDefinitionId()
    {
        return $this->serviceDefinitionId;
    }

    /**
     * Set service component id
     * @param int $serviceComponentId service component id
     * @return void
     */
    public function setServiceComponentId($serviceComponentId)
    {
        $this->serviceComponentId = $serviceComponentId;
    }

    /**
     * @return bool
     */
    public function doesUserWantCallerDisplay()
    {
        return $this->wantCallerDisplay;
    }

    /**
     * @return bool
     */
    public function isKeepingPhone()
    {
        return !$this->removePhone;
    }

    /**
     * Populates the broadband service component id and products from a passed in array
     *
     * @param $products
     */
    public function buildProductsFromArray($products)
    {
        $callPlanScid = null;
        $mobileBoltOn = false;

        foreach ($products as $product) {
            switch ($product['type']) {
                case self::PRODUCT_TYPE_BROADBAND:
                    if (!empty($product['serviceComponentId'])) {
                        $this->setServiceComponentId($product['serviceComponentId']);
                    }
                    break;
                case self::PRODUCT_TYPE_CALL_PLAN:
                    if (!empty($product['serviceComponentId'])) {
                        $callPlanScid = $product['serviceComponentId'];
                    }
                    if ($product['option'] === 'NONE') {
                        $this->removePhone = true;
                    }
                    break;
                case self::PRODUCT_TYPE_MOBILE_BOLT_ON:
                    if ($product['selected'] == 'true') {
                        $mobileBoltOn = true;
                    }
                    break;
                case self::PRODUCT_TYPE_CALLER_DISPLAY:
                    if ($product['option'] === 'STANDARD') {
                        $this->wantCallerDisplay = true;
                    } elseif ($product['option'] === 'NONE') {
                        $this->wantCallerDisplay = false;
                    }
                    break;
                case self::PRODUCT_TYPE_ROUTER:
                    if (!empty($product['serviceComponentId'])) {
                        $this->addServiceComponentId(self::HARDWARE_COMPONENT_ID, $product['serviceComponentId']);
                    }
                    break;
                default:
                    break;
            }
        }

        if (!is_null($callPlanScid)) {
            if ($mobileBoltOn) {
                // We're adding mobile bolt on
                $planWithBoltOnScid = $this->findMobileVariantOfCallPlan($callPlanScid);
                if (!is_null($planWithBoltOnScid)) {
                    $callPlanScid = $planWithBoltOnScid;
                }
            } elseif ($mobileBoltOn === false){
                // We're removing mobile bolt-on
                $planWithoutBoltOnScid = $this->findNonMobileVariantOfCallPlan($callPlanScid);
                if (!is_null($planWithoutBoltOnScid)) {
                    $callPlanScid = $planWithoutBoltOnScid;
                }
            }
            $this->addServiceComponentId(self::PHONE_COMPONENT_ID, $callPlanScid);
        }
    }

    /**
     * Given a call plan without mobile bolt on, returns either the service component id
     * of the equivalent plan with bolt on or null if no matching plan is found.
     *
     * @param $nonMobileServiceComponentId
     * @return int
     */
    private function findMobileVariantOfCallPlan($nonMobileServiceComponentId)
    {
        $phoneHelper = $this->getPhoneHelper();
        return $phoneHelper->getMappedMobileProduct($nonMobileServiceComponentId);
    }

    /**
     * Given a call plan with mobile bolt on, returns either the service component id
     * of the equivalent plan without bolt on or null if no matching plan is found.
     *
     * @param $mobileServiceComponentId
     * @return int
     */
    private function findNonMobileVariantOfCallPlan($mobileServiceComponentId)
    {
        $phoneHelper = $this->getPhoneHelper();
        return $phoneHelper->getMappedNonMobileProduct($mobileServiceComponentId);
    }

    /**
     * @return AccountChange_PhoneProductHelper
     */
    protected function getPhoneHelper()
    {
        return new AccountChange_PhoneProductHelper();
    }

    /**
     * @return int
     */
    public function getServiceComponentId()
    {
        return $this->serviceComponentId;
    }

    /**
     * @return int
     */
    public function getPhoneComponentId()
    {
        if ($this->serviceComponentIds != null
            && is_array($this->serviceComponentIds)
            && array_key_exists(self::PHONE_COMPONENT_ID, $this->serviceComponentIds)) {

            return $this->serviceComponentIds[self::PHONE_COMPONENT_ID];
        }

        return null;
    }

    /**
     * @return int
     */
    public function getHardwareComponentId()
    {
        if ($this->serviceComponentIds != null
            && is_array($this->serviceComponentIds)
            && array_key_exists(self::HARDWARE_COMPONENT_ID, $this->serviceComponentIds)) {

            return $this->serviceComponentIds[self::HARDWARE_COMPONENT_ID];
        }

        return null;
    }

    /**
     * @param string $componentType component type
     * @param int    $componentId   component type id
     * @return void
     */
    public function addServiceComponentId($componentType, $componentId)
    {
        if ($this->serviceComponentIds === null) {
            $this->serviceComponentIds = array();
        }
        $this->serviceComponentIds[$componentType] = $componentId;
    }
}
