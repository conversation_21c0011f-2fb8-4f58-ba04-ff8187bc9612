<?php
/**
 * UpdateSupplierProduct Action
 *
 * Updates the Supplier Product for the new account type
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 PlusNet
 */

/**
 * UpdateSupplierProduct Booking Action class
 *
 * Updates the Supplier Product and Connection Profile for the new account type
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
use \Plusnet\InventoryEventClient\Service\ActiveContextService;
use \Plusnet\InventoryEventClient\Context\ScheduledAccountChangeContext;

class AccountChange_Action_UpdateSupplierProduct extends AccountChange_Action_BroadbandProvisioning
{
    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $activeContext = ActiveContextService::getActiveContext();

        if (!$this->isScheduledAccountChange() || $this->requiresOrderPlacement()) {
            $this->includeLegacyFiles();
            $this->updateSupplierProduct(true);
            $this->setDefaultConnectionProfile();
        } elseif ($this->isScheduledAccountChange()) {
            $activeContext->setIsInternetSupplierOrder(!empty($this->arrOptions['bolHousemove']));
        }
    }

    /**
     * Set the default connection profile on the account for the supplier product
     *
     * @return void
     */
    protected function setDefaultConnectionProfile()
    {
        $service = new C_Core_ADSLService(null, null, $this->intServiceId);
        $service->setConnectionProfile();
    }
}
