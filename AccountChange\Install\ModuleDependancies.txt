#                         'Framework' Module Dependancies
#
# A comma seperated, whitespace padded list of dependacies on other Plusnet Framework
# modules the 'Framework' Module has.
#
# a Namespace is a functional divisions within a module identified by a unique
# prefix (classes conform to <Namespace>_<Prefix>). Modules are collections of
# namespaces sharing a single CVS module.  A module is the smallest deployable
# code grouping. For multiple namespaces to share a # module there must be inherent
# interdependancies between those namespaces.
#
# Each application is a single module. Library functions shared between several
# applictions also form a disinct modules. Often a namespace will begin as an
# intrisic part of one application and then migrate into its own module when
# another appliction becomes dependent upon it.
#
# Dependancies can be specified to either an entire module or a namespace within
# that module. Specifying individual namespaces is preferred.
#
#
#    Local Namespace  |                Dependent Upon                     |
#       Name          |  module / namespace   |            Name           |
#==========================================================================
#  SignupApplication  ,     namespace         ,           BuyNet
#
AccountChange         ,     module            ,           ActivityLog
AccountChange         ,     module            ,           AutomatedEmail
AccountChange         ,     module            ,           AutoProblem
AccountChange         ,     module            ,           BoltOn
AccountChange         ,     module            ,           Buynet
AccountChange         ,     module            ,           CmdTools
AccountChange         ,     module            ,           Components
AccountChange         ,     module            ,           ContractsClient
AccountChange         ,     module            ,           Core
AccountChange         ,     module            ,           CustomerDetails
AccountChange         ,     module            ,           DslOrdering
AccountChange         ,     module            ,           Email
AccountChange         ,     module            ,           EngineerAppointmentClient
AccountChange         ,     module            ,           FeatureSwitch
AccountChange         ,     module            ,           Financial
AccountChange         ,     module            ,           ForumsClient
AccountChange         ,     module            ,           Framework
AccountChange         ,     module            ,           GenericImmediatePaymentApplication
AccountChange         ,     module            ,           HardwareClient
AccountChange         ,     module            ,           InstallDiary
AccountChange         ,     module            ,           IronPort
AccountChange         ,     module            ,           IspAutomatedEmail
AccountChange         ,     module            ,           IspPayments
AccountChange         ,     module            ,           IspPrimitives
AccountChange         ,     module            ,           LdapClient
AccountChange         ,     legacy            ,           LegacyCodebase
AccountChange         ,     module            ,           LineCheck
AccountChange         ,     module            ,           PomsClient
AccountChange         ,     module            ,           Postini
AccountChange         ,     module            ,           PrepaidContract
AccountChange         ,     module            ,           Product
AccountChange         ,     module            ,           ProductChangePlanClient
AccountChange         ,     module            ,           ProductComponent
AccountChange         ,     module            ,           ProductFamily
AccountChange         ,     module            ,           Products
AccountChange         ,     module            ,           PromotionTools
AccountChange         ,     module            ,           Reseller
AccountChange         ,     module            ,           ResellerTag
AccountChange         ,     module            ,           RetentionsTool
AccountChange         ,     module            ,           ServiceNoticeClient
AccountChange         ,     module            ,           SignupApplication
AccountChange         ,     module            ,           SoapSession
AccountChange         ,     module            ,           TicketClient
AccountChange         ,     module            ,           Tr069
AccountChange         ,     module            ,           WebService
AccountChange         ,     module            ,           Wlr3
AccountChange         ,     namespace         ,           InventoryEventClient
AccountChange         ,     module            ,           GenerateCbcServiceBills
AccountChange         ,     module            ,           IspTemplateData
AccountChange         ,     module            ,           FailedBilling
AccountChange         ,     module            ,           CustomerEmail
AccountChange         ,     module            ,           BrandMigration
