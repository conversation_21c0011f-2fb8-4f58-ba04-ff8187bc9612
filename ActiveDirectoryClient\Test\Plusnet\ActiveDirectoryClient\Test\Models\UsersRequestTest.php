<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\ActiveDirectoryClient\Models\Test;

use Guzzle\Http\Client;
use \Guzzle\Http\Message\Response;
use Plusnet\ActiveDirectoryClient\Helpers\LoggerHelper;
use Plusnet\ActiveDirectoryClient\Models\UserDetails;
use Plusnet\ActiveDirectoryClient\Models\UsersRequest;

/**
 * Class UserRequestTest
 *
 * @package Test\Plusnet\ActiveDirectoryClient
 */
class UsersRequestTest extends \PHPUnit_Framework_TestCase
{
    const USER_NAME = 'TestUser';

    /**
     * @var UsersRequest
     */
    private $userRequest;
    /**
     * @var Client
     */
    private $mockGuzzleClient;
    /**
     * @var Response
     */
    private $mockGuzzleResponse;
    /**
     * @var LoggerHelper
     */
    private $mockLoggerHelper;

    public function setUp()
    {
        parent::setUp();

        $this->mockLoggerHelper = $this->getMockBuilder(LoggerHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(array(
                             'error'
                         ))
            ->getMock();

        $this->mockGuzzleClient = $this->getMockBuilder(Client::class)
            ->disableOriginalConstructor()
            ->setMethods(array(
                             'get',
                             'send'
                         ))
            ->getMock();

        $this->mockGuzzleResponse = $this->getMockBuilder(Response::class)
            ->disableOriginalConstructor()
            ->setMethods(array(
                             'getBody'
                         ))
            ->getMock();

        $this->userRequest = new UsersRequest(
            null,
            $this->mockGuzzleClient,
            $this->mockLoggerHelper
        );
    }

    /**
     * Test that we can process a valid response.
     *
     * We've found that in some cases Guzzle can return an array rather then just a Response
     * object. This can happen even when cURL multi isn't being used. Along with confirming that
     * the Response is correctly processed, this test also checks this edge case is handled properly.
     *
     * @param callable $prepareResponse Function to prepare the response.
     *
     * @covers       \Plusnet\ActiveDirectoryClient\Factory\UserDetailsFactory
     * @covers       \Plusnet\ActiveDirectoryClient\Models\UsersRequest
     *
     * @test
     *
     * @dataProvider checkCanProcessAValidResponseDataProvider
     */
    public function checkCanProcessAValidResponse(
        $prepareResponse
    ) {
        $ein = '123456789';
        $isPartner = true;

        $json = json_encode(array(
                                UserDetails::KEY_EIN        => $ein,
                                UserDetails::KEY_IS_PARTNER => $isPartner
                            ));

        $this->mockGuzzleClient->expects($this->once())
            ->method('get');

        $this->mockGuzzleResponse->expects($this->once())
            ->method('getBody')
            ->willReturn($json);

        $this->mockGuzzleClient->expects($this->once())
            ->method('send')
            ->willReturn($prepareResponse($this->mockGuzzleResponse));

        $this->mockLoggerHelper->expects($this->never())
            ->method('error');

        $userDetails = $this->userRequest->requestUserDetails(self::USER_NAME);

        $this->assertSame($ein, $userDetails->getEin());
        $this->assertSame($isPartner, $userDetails->getIsPartner());
    }

    /**
     * Provide test data.
     *
     * @return array
     */
    public function checkCanProcessAValidResponseDataProvider()
    {
        return array(
            'single response object not in array' => array(
                'prepare_response' => function ($response) {
                    return $response;
                }
            ),
            'single response object in array'     => array(
                'prepare_response' => function ($response) {
                    return array($response);
                }
            ),
        );
    }

    /**
     * Check if invalid JSON is returned in response an error is logged and invalid user details are returned.
     *
     * @covers \Plusnet\ActiveDirectoryClient\Factory\UserDetailsFactory
     * @covers \Plusnet\ActiveDirectoryClient\Models\UsersRequest
     *
     * @test
     */
    public function checkInvalidJsonIsHandledCorrectly()
    {
        $testJSON = '"}';

        $this->mockGuzzleClient->expects($this->once())
            ->method('get');

        $this->mockGuzzleClient->expects($this->once())
            ->method('send')
            ->willReturn($this->mockGuzzleResponse);

        $this->mockLoggerHelper->expects($this->once())
            ->method('error');

        $this->mockGuzzleResponse->expects($this->once())
            ->method('getBody')
            ->willReturn($testJSON);

        $userDetails = $this->userRequest->requestUserDetails(self::USER_NAME);

        $this->assertUserDetailsAreInvalid($userDetails);
    }

    /**
     * When an error is returned by the microservice I should:
     *   - return an invalid UserDetails
     *   - Log the issue.
     *
     * @covers \Plusnet\ActiveDirectoryClient\Factory\UserDetailsFactory
     * @covers \Plusnet\ActiveDirectoryClient\Models\UsersRequest
     *
     * @test
     */
    public function iCorrectlyHandleErrorReturnedByMicroservice()
    {
        $testJSON = '{"error": "Bad Request", "exception": "An Example Error", "message": "An Example Message"}';

        $this->mockGuzzleClient->expects($this->once())
            ->method('get');

        $this->mockGuzzleClient->expects($this->once())
            ->method('send')
            ->willReturn($this->mockGuzzleResponse);

        $this->mockGuzzleResponse->expects($this->once())
            ->method('getBody')
            ->willReturn($testJSON);

        $this->assertLoggerHelperLogs(
            "ActiveDirectoryClient - Exception occurred while requesting user details: "
            . "error='Error returned in response: Bad Request - An Example Message' username='TestUser'"
        );

        $userDetails = $this->userRequest->requestUserDetails(self::USER_NAME);

        $this->assertUserDetailsAreInvalid($userDetails);
    }

    /**
     * Check if username is invalid format that an error is logged and an invalid UserDetails is returned.
     *
     * @covers \Plusnet\ActiveDirectoryClient\Factory\UserDetailsFactory
     * @covers \Plusnet\ActiveDirectoryClient\Models\UsersRequest
     *
     * @test
     */
    public function checkInvalidUsername()
    {
        $username = 'invalid!name';

        $this->assertLoggerHelperLogs(
            "ActiveDirectoryClient - Exception occurred while requesting user details: "
            . "error='Invalid username provided: 'invalid!name'' username='invalid!name'"
        );

        $userDetails = $this->userRequest->requestUserDetails($username);

        $this->assertUserDetailsAreInvalid($userDetails);
    }

    /**
     * @test
     *
     * We need to make sure that we catch the client send exception and return an invalid
     * UserDetails so this can be set on the cookie.
     */
    public function iCorrectlyHandleAClientSendException()
    {
        $exceptionMessage = 'client_send_exception';

        $this->mockGuzzleClient
            ->method('send')
            ->willThrowException(new \Exception($exceptionMessage));

        $this->assertLoggerHelperLogs(
            "ActiveDirectoryClient - Exception occurred while requesting user details: "
            . "error='$exceptionMessage' username='username'"
        );

        $userDetails = $this->userRequest->requestUserDetails('username');

        $this->assertUserDetailsAreInvalid($userDetails);
    }

    /**
     * @test
     *
     * @param string $exceptionMessage Exception message.
     * @param mixed  $response         Response.
     *
     * @dataProvider iCorrectlyHandleInvalidResponseTypesDataProvider
     */
    public function iCorrectlyHandleInvalidResponseTypes(
        $exceptionMessage,
        $response
    ) {
        $this->mockGuzzleClient
            ->method('send')
            ->willReturn($response);

        $this->assertLoggerHelperLogs(
            "ActiveDirectoryClient - Exception occurred while requesting user details: "
            . "error='$exceptionMessage' username='username'"
        );

        $userDetails = $this->userRequest->requestUserDetails('username');

        $this->assertUserDetailsAreInvalid($userDetails);
    }

    /**
     * Provide test data.
     *
     * @return array
     */
    public function iCorrectlyHandleInvalidResponseTypesDataProvider()
    {
        return array(
            'multiple response objects in an array'                  => array(
                'exception_message' => 'Invalid response object returned with type=array and count=2',
                'response'          => array(
                    Response::fromMessage('TEST-1'),
                    Response::fromMessage('TEST-2')
                )
            ),
            'boolean returned in place of response object'           => array(
                'exception_message' => 'Invalid response object returned with type=boolean and count=N/A',
                'response'          => false
            ),
            'string returned in place of response object'            => array(
                'exception_message' => 'Invalid response object returned with type=string and count=N/A',
                'response'          => 'response'
            ),
            'stdClass returned in place of response object'          => array(
                'exception_message' => 'Invalid response object returned with type=stdClass and count=N/A',
                'response'          => new \stdClass()
            ),
            'stdClass returned in array in place of response object' => array(
                'exception_message' => 'Invalid response object returned with type=array(stdClass) and count=1',
                'response'          => array(
                    new \stdClass()
                )
            )
        );
    }

    /**
     * Assert that the provided user details are invalid.
     *
     * @param UserDetails $userDetails The user details to check
     */
    private function assertUserDetailsAreInvalid($userDetails)
    {
        $this->assertInstanceOf(UserDetails::class, $userDetails);
        /** @var $userDetails UserDetails */
        $this->assertSame(UserDetails::INVALID_EIN, $userDetails->getEin());
        $this->assertSame(UserDetails::INVALID_IS_PARTNER, $userDetails->getIsPartner());
    }

    /**
     * Setup the logger helper to expect a particular message.
     *
     * @param string $expectedMessage The expected message
     */
    private function assertLoggerHelperLogs($expectedMessage)
    {
        $this->mockLoggerHelper
            ->expects($this->once())
            ->method('error')
            ->with($expectedMessage);
    }
}
