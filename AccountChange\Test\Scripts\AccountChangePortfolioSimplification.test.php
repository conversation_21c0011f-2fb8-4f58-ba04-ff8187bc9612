<?php

/**
 * AccountChange_AccountChangePortfolioSimplification scrip tests
 *
 * Testing class for the AccountChange_AccountChangePortfolioSimplification
 *
 * @package    AccountChange
 * <AUTHOR> <d<PERSON><PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright  2022 PlusNet
 * @since      File available since 2022-04-29
 */

use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;

/**
 * Testing class for the Test_AccountChange_AccountChangePortfolioSimplification
 *
 * @package    AccountChange
 * <AUTHOR> <d<PERSON><PERSON><PERSON><EMAIL>>
 *
 * @copyright  2022 PlusNet
 */


class Test_AccountChange_AccountChangePortfolioSimplification extends PHPUnit_Framework_TestCase
{
    const SCRIPT_NAME = 'AccountChange_AccountChangePortfolioSimplification';

    /** @var org\bovigo\vfs\vfsStreamDirectory $vfsroot */
    private static $vfsroot;

    /** @var ConvertCsvToMappingSql $object */
    private $object;

    /**
     * @return vfsStreamDirectory
     */
    public static function getRoot()
    {
        if (!self::$vfsroot) {
            self::$vfsroot = vfsStream::setup('tmp');
        }
        return self::$vfsroot;
    }

    public function setUp()
    {
        $objBusTierMock = $this->getMock(
            'BusTier_BusTier',
            array('getClient'),
            array(), '', false
        );

        $objBusTierMock->expects($this->never())
            ->method('getClient');

        $this->mockDb = Mockery::mock('Db_Adaptor');
        Db_Manager::setAdaptor('AccountChange', $this->mockDb);

        $this->mockIec = Mockery::mock('inventoryEventService');
        BusTier_BusTier::setClient('inventoryEventService', $this->mockIec);
        $this->mockIec
            ->shouldReceive('takePreChangeSnapshot')
            ->once();
        $this->mockIec
            ->shouldReceive('takePostChangeSnapshot')
            ->once();

        Mockery::mock('\Plusnet\InventoryEventClient\Context\AccountChangeContext');
       // $billingContext->shouldReceive('setIsInternetSupplierOrder')->with(false)->once();

        $mockCoreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockCoreDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will(
                $this->returnValue(
                    array(
                        'service_definition_id' => '********',
                        'provisioningProfile' => null
                    )
                )
            );

        Db_Manager::setAdaptor('AccountChange', $mockCoreDbAdaptor);
    }

    private function getCsv(array $lines)
    {
        $csvFileName = 'csvFileName.csv';
        $csvFileData = "serviceId,currentSdi,newSdi\n";
        $csvFileData .= implode("\n", $lines);
        $csvFileData .= "\n";
        return $this->getFakeFile($csvFileName, $csvFileData);
    }

    private function checkLogFile($filename, $expectedFragment)
    {
        $file = file($filename);
        foreach($file as $line) {
            if (strpos($line, $expectedFragment) !== false) {
                return true;
            }
        }
        return false;
    }


    /**
     * Checks the scripts default values when a valid csv is passed in
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testPerformBroadbandChangePortfolioSimplificationDefaultsToScheduledAndTmpDir()
    {

        $serviceId = "1";
        $fromSdid = "2";
        $toSdid = "3";
        $csvFileHand = $this->getCsv(array("$serviceId,$fromSdid,$toSdid"));

        $inputParams = array(
            'input-file' => $csvFileHand,
            'suppress-output' => true
        );

        $expectedNewProducts = new \AccountChange_AccountChangeOrderProducts();
        $expectedNewProducts->setServiceDefinitionId($toSdid);

        $script = $this->getMockBuilder(AccountChange_AccountChangePortfolioSimplification)
            ->setMethods(array('__construct','login', 'getAccountChangeApi','importLegacy'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->once())
            ->method('login');


        $accountChangeApi = $this->getMock(
            '\AccountChange_AccountChangeApi',
            array(
                'processAccountChangeRequest')
        );

        $script
            ->expects($this->once())
            ->method('getAccountChangeApi')
            ->will($this->returnValue($accountChangeApi));

        $accountChangeApi
            ->expects($this->once())
            ->method('processAccountChangeRequest');

        $script->run( array(),$inputParams);
        $this->assertTrue($script->getAccountChangeOrder()->getIsScheduledChange());
        $this->assertEquals($script->getAccountChangeOrder()->getServiceId(), $serviceId);
        $this->assertEquals($script->getAccountChangeOrder()->getProducts(), $expectedNewProducts);
        $this->assertEquals($script->getLogDirectory(), '/tmp');
    }

    /**
     * Checks that passing in the instant flog as false results in a scheduled change
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testPerformBroadbandChangePortfolioSimplificationCanBeSetToScheduled()
    {
        $serviceId = "1";
        $fromSdid = "2";
        $toSdid = "3";
        $csvFileHand = $this->getCsv(array("$serviceId,$fromSdid,$toSdid"));

        $inputParams = array(
            'input-file' => $csvFileHand,
            'instant' => false,
            'suppress-output' => true
        );

        $expectedNewProducts = new \AccountChange_AccountChangeOrderProducts();
        $expectedNewProducts->setServiceDefinitionId($toSdid);

        $script = $this->getMockBuilder(AccountChange_AccountChangePortfolioSimplification)
            ->setMethods(array('__construct','login', 'getAccountChangeApi','importLegacy'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->once())
            ->method('login');

        $accountChangeApi = $this->getMock(
            '\AccountChange_AccountChangeApi',
            array(
                'processAccountChangeRequest')
        );

        $script
            ->expects($this->once())
            ->method('getAccountChangeApi')
            ->will($this->returnValue($accountChangeApi));

        $accountChangeApi
            ->expects($this->once())
            ->method('processAccountChangeRequest');

        $script->run( array(),$inputParams);
        $this->assertTrue($script->getAccountChangeOrder()->getIsScheduledChange());
        $this->assertEquals($script->getAccountChangeOrder()->getServiceId(), $serviceId);
        $this->assertEquals($script->getAccountChangeOrder()->getProducts(), $expectedNewProducts);
        $this->assertEquals($script->getLogDirectory(), '/tmp');
    }

    /**
     * Checks that passing in a custom directory for the output results in that directory being setup
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testPerformBroadbandChangePortfolioSimplificationCustomLogDirectory()
    {
        $serviceId = "1";
        $fromSdid = "2";
        $toSdid = "3";
        $logDir = '/tmp/testScriptUnitTestPortfolioSimplification/';
        $csvFileHand = $this->getCsv(array("$serviceId,$fromSdid,$toSdid"));

        $inputParams = array(
            'input-file' => $csvFileHand,
            'instant' => false,
            'log-dir' => $logDir,
            'suppress-output' => true
        );

        $expectedNewProducts = new \AccountChange_AccountChangeOrderProducts();
        $expectedNewProducts->setServiceDefinitionId($toSdid);

        $script = $this->getMockBuilder(AccountChange_AccountChangePortfolioSimplification)
            ->setMethods(array('__construct','login', 'getAccountChangeApi','importLegacy'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->once())
            ->method('login');

        $accountChangeApi = $this->getMock(
            '\AccountChange_AccountChangeApi',
            array(
                'processAccountChangeRequest')
        );

        $script
            ->expects($this->once())
            ->method('getAccountChangeApi')
            ->will($this->returnValue($accountChangeApi));

        $accountChangeApi
            ->expects($this->once())
            ->method('processAccountChangeRequest');

        $script->run( array(),$inputParams);
        $this->assertTrue($script->getAccountChangeOrder()->getIsScheduledChange());
        $this->assertEquals($script->getAccountChangeOrder()->getServiceId(), $serviceId);
        $this->assertEquals($script->getAccountChangeOrder()->getProducts(), $expectedNewProducts);
        $this->assertEquals($script->getLogDirectory(), $logDir);
        $this->assertTrue(is_dir($logDir));
    }

    /**
     * Check order not valid error type
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testPerformBroadbandChangePortfolioSimplificationLogsErrorWhenAccountChangeOrderInvalid()
    {
        $serviceId = "1";
        $fromSdid = "2";
        $toSdid = "3";
        $csvFileHand = $this->getCsv(array("$serviceId,$fromSdid,$toSdid"));

        $inputParams = array(
            'input-file' => $csvFileHand,
            'instant' => false,
            'suppress-output' => true
        );

        $expectedNewProducts = new \AccountChange_AccountChangeOrderProducts();
        $expectedNewProducts->setServiceDefinitionId($toSdid);

        $expectedLogName = '/tmp/'.AccountChange_AccountChangePortfolioSimplification::INVALID_ORDER_LOG_FILE
            .date(AccountChange_AccountChangePortfolioSimplification::DATE_FORMAT) . '.log';

        if (file_exists($expectedLogName)) {
            unlink($expectedLogName);
        }

        $script = $this->getMockBuilder(AccountChange_AccountChangePortfolioSimplification)
            ->setMethods(array('__construct','login', 'getAccountChangeApi','importLegacy'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->once())
            ->method('login');

        $accountChangeApi = $this->getMock(
            '\AccountChange_AccountChangeApi',
            array('processAccountChangeRequest')
        );

        $script
            ->expects($this->once())
            ->method('getAccountChangeApi')
            ->will($this->returnValue($accountChangeApi));

        $accountChangeApi
            ->expects($this->once())
            ->method('processAccountChangeRequest')
            ->will($this->throwException(new \AccountChange_InvalidAccountChangeOrderException('invalid account') ));

        $script->run( array(),$inputParams);
        $this->assertTrue($script->getAccountChangeOrder()->getIsScheduledChange());
        $this->assertEquals($script->getAccountChangeOrder()->getServiceId(), $serviceId);
        $this->assertEquals($script->getAccountChangeOrder()->getProducts(), $expectedNewProducts);
        $this->assertEquals($script->getLogDirectory(), '/tmp');

        $this->assertTrue($this->checkLogFile($expectedLogName, "The order was invalid for service ID: $serviceId"));
    }


    /**
     * Check account change not allowed error
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testPerformBroadbandChangePortfolioSimplificationLogsErrorWhenAccountChangeAccountInvalid()
    {
        $serviceId = "1";
        $fromSdid = "2";
        $toSdid = "3";
        $csvFileHand = $this->getCsv(array("$serviceId,$fromSdid,$toSdid"));

        $inputParams = array(
            'input-file' => $csvFileHand,
            'instant' => false,
            'suppress-output' => true
        );

        $expectedNewProducts = new \AccountChange_AccountChangeOrderProducts();
        $expectedNewProducts->setServiceDefinitionId($toSdid);

        $expectedLogName = '/tmp/'.AccountChange_AccountChangePortfolioSimplification::ACCOUNT_CHANGE_NOT_ALLOWED_LOG_FILE
            .date(AccountChange_AccountChangePortfolioSimplification::DATE_FORMAT) . '.log';

        if(file_exists($expectedLogName)) {
            unlink($expectedLogName);
        }

        $script = $this->getMockBuilder(AccountChange_AccountChangePortfolioSimplification)
            ->setMethods(array('__construct','login', 'getAccountChangeApi','importLegacy'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->once())
            ->method('login');

        $accountChangeApi = $this->getMock(
            '\AccountChange_AccountChangeApi',
            array('processAccountChangeRequest')
        );

        $script
            ->expects($this->once())
            ->method('getAccountChangeApi')
            ->will($this->returnValue($accountChangeApi));

        $accountChangeApi
            ->expects($this->once())
            ->method('processAccountChangeRequest')
            ->will($this->throwException(new \AccountChange_AccountChangeNotAllowedException('invalid account') ));

        $script->run( array(),$inputParams);
        $this->assertTrue($script->getAccountChangeOrder()->getIsScheduledChange());
        $this->assertEquals($script->getAccountChangeOrder()->getServiceId(), $serviceId);
        $this->assertEquals($script->getAccountChangeOrder()->getProducts(), $expectedNewProducts);
        $this->assertEquals($script->getLogDirectory(), '/tmp');

        $this->assertTrue($this->checkLogFile($expectedLogName, "The account change was not allowed for service ID: $serviceId"));
    }

    /**
     * Check account change api failure error log
     *
     * @covers AccountChange_PerformScheduledAccountChange
     *
     * @return void
     */
    public function testPerformBroadbandChangePortfolioSimplificationLogsErrorWhenApiFailure()
    {
        $serviceId = "1";
        $fromSdid = "2";
        $toSdid = "3";
        $csvFileHand = $this->getCsv(array("$serviceId,$fromSdid,$toSdid"));

        $inputParams = array(
            'input-file' => $csvFileHand,
            'instant' => false,
            'suppress-output' => true
        );

        $expectedNewProducts = new \AccountChange_AccountChangeOrderProducts();
        $expectedNewProducts->setServiceDefinitionId($toSdid);

        $expectedLogName = '/tmp/'.AccountChange_AccountChangePortfolioSimplification::ACCOUNT_CHANGE_API_FAILURE_LOG_FILE
            .date(AccountChange_AccountChangePortfolioSimplification::DATE_FORMAT) . '.log';

        if(file_exists($expectedLogName)) {
            unlink($expectedLogName);
        }

        $script = $this->getMockBuilder(AccountChange_AccountChangePortfolioSimplification)
            ->setMethods(array('__construct','login', 'getAccountChangeApi','importLegacy'))
            ->setConstructorArgs(array())
            ->disableOriginalConstructor()
            ->getMock();

        $script
            ->expects($this->once())
            ->method('login');

        $accountChangeApi = $this->getMock(
            '\AccountChange_AccountChangeApi',
            array('processAccountChangeRequest')
        );

        $script
            ->expects($this->once())
            ->method('getAccountChangeApi')
            ->will($this->returnValue($accountChangeApi));

        $accountChangeApi
            ->expects($this->once())
            ->method('processAccountChangeRequest')
            ->will($this->throwException(new \AccountChange_AccountChangeApiFailureException('invalid account') ));

        $script->run( array(),$inputParams);
        $this->assertTrue($script->getAccountChangeOrder()->getIsScheduledChange());
        $this->assertEquals($script->getAccountChangeOrder()->getServiceId(), $serviceId);
        $this->assertEquals($script->getAccountChangeOrder()->getProducts(), $expectedNewProducts);
        $this->assertEquals($script->getLogDirectory(), '/tmp');

        $this->assertTrue($this->checkLogFile($expectedLogName, "The account change api failed for service ID: $serviceId"));
    }



    private function getFakeFile($filename, $data)
    {
        $filehandle = vfsStream::url('tmp/' . $filename);
        vfsStream::newFile($filename)->at(self::getRoot())->setContent($data);
        return $filehandle;
    }






}
