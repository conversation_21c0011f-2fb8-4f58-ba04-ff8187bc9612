<?php

/**
 * <AUTHOR>
 */

class AccountChange_ShouldBlockLineRateChangeDueToAccountChange
{
    /**
     * Checks whether we should accept a line rate change while an account change is in progress
     *
     * @param int $serviceId the service ID
     *
     * @return bool
     */
    public function query($serviceId)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');

        return $dbAdaptor->shouldBlockLineRateChangeDueToAccountChangeByServiceId($serviceId);
    }
}
