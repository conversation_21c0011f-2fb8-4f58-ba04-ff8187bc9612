{"name": "plusnet/active-directory-client", "description": "Active directory Client", "homepage": "https://bitbucket.int.plus.net/projects/SERVICES/repos/activedirectoryclient/browse", "license": "proprietary", "type": "plusnet-framework", "keywords": ["plusnet", "php5.6"], "require": {"php": "~5.6"}, "require-dev": {"composer/installers": "~1.0", "elendev/nexus-composer-push": "0.1.7", "oomphinc/composer-installers-extender": "1.1.2"}, "autoload": {"psr-0": {"Plusnet\\ActiveDirectoryClient": "src/"}}, "autoload-dev": {"psr-4": {"Plusnet\\ActiveDirectoryClient\\Test\\": "Test/"}}}