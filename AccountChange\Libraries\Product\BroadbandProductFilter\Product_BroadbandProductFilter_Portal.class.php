<?php
/**
 * Broadband product filter for portal
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */

use Plusnet\Feature\FeatureToggleManager;

/**
 * Broadband product filter for portal
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Product_BroadbandProductFilter_Portal extends
    AccountChange_Product_BroadbandProductFilter_Common
{

    /**
     * Filter the broadband product variants
     *
     * @param LineCheck_Result $lineCheck         Line check result
     * @param array            $availableProducts Available products
     * @param array            $currentProduct    Current product
     * @param string           $campaignCode      Campaign code indicating which landing page this journey originated from, if any
     *
     * @return array Filtered products
     */
    public function filter(LineCheck_Result $lineCheck, array $availableProducts, array $currentProduct, $campaignCode = '')
    {
        $availableProducts = $this->commonFilter($lineCheck, $availableProducts, $currentProduct);
        $currentProductSdi = isset($currentProduct['intSdi']) ? $currentProduct['intSdi'] : null;

        $currentProductVariants = static::getServiceDefinitionVariants($currentProductSdi);

        if (empty($currentProductVariants)) {
            $currentProductVariants[] = $currentProductSdi;
        }

        $availableProducts = static::removeSolusProducts($availableProducts, $currentProduct['intSdi']);

        if ($campaignCode == AccountChange_CampaignCodes::ADSL_TO_FIBRE) {
            $availableProducts = static::removeUnlimitedFibreExtraProducts($availableProducts);
        }

        return $availableProducts;
    }
}
