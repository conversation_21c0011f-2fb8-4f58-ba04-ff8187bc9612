<?php
/**
 * AccountChange ProductRules Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-22
 */
/**
 * AccountChange ProductRules Test
 *
 * Test class for AccountChange_ProductRules
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_ProductRules_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Tear down functionality
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::restoreAdaptor('AccountChange');
    }

    /**
     * Test that the instance methods returns the object originally set
     *
     * @covers AccountChange_ProductRules::instance
     * @covers AccountChange_ProductRules::setInstance
     *
     * @return void
     */
    public function testInstanceReturnsObjectAlreadySetIfThereIsOne()
    {
        $rulesSet = new AccountChange_ProductRules();

        AccountChange_ProductRules::setInstance($rulesSet);

        $rules = AccountChange_ProductRules::instance();

        $this->assertEquals($rulesSet, $rules);
    }

    /**
     * @group getLineCheckResult
     * @test
     */
    public function getLineCheckResultWillThrowExceptionIfNotAnInstanceOfLineCheckResult()
    {
        $this->setExpectedException(Exception::class);

        $registry = $this->getMockBuilder('AccountChange_Registry')
            ->disableOriginalConstructor()
            ->setMethods(array('getEntry'))
            ->getMock();

        $registry->expects($this->once())
            ->method('getEntry')
            ->will($this->returnValue(array()));

        $accountChangeAction = $this->getMockBuilder('AccountChange_Action')
            ->disableOriginalConstructor()
            ->getMockForAbstractClass();

        $accountChangeAction->getLineCheckResult($registry);
    }

    /**
     * @group getLineCheckResult
     * @test
     */
    public function getLineCheckResultWillReturnLineCheckResultIfItIsAnInstanceOfALineCheckResult()
    {
        $registry = $this->getMockBuilder('AccountChange_Registry')
            ->disableOriginalConstructor()
            ->setMethods(array('getEntry'))
            ->getMock();

        $lineCheckResult = $this->getMockBuilder('LineCheck_Result')
            ->disableOriginalConstructor()
            ->getMock();

        $registry->expects($this->once())
            ->method('getEntry')
            ->will($this->returnValue($lineCheckResult));

        $accountChangeAction = $this->getMockBuilder('AccountChange_Action')
            ->disableOriginalConstructor()
            ->getMockForAbstractClass();

        $lineCheckResult = $accountChangeAction->getLineCheckResult($registry);

        $this->assertTrue($lineCheckResult instanceof LineCheck_Result);
    }

    /**
     * Test that getProductProvisionForService fetchs and returns the correct arraty the first time
     * and returns from cache when accessed the second time.
     *
     * @cover AccountChange_ProductRules::getProductProvisionForService
     *
     * @return void
     */
    public function testGetProductProvisionForService()
    {
        $supplierProduct = new Product_SupplierProduct(
            new Int(1234),
            new Int(12),
            new String('name'),
            new String('code'),
            new Int(4),
            new Int(8),
            new Int(6),
            new String('STANDARD'),
            new String('Auto'),
            new String(448),
            new String(''),
            new String('')
        );

        $supplier = new Product_Supplier(
            new Int(1),
            new String('handle'),
            new String('displayName')
        );

        $supplierPlatform = new Product_SupplierPlatform(
            new Int(1),
            $supplier,
            new String('handle'),
            new String('displayName')
        );

        $supplierProductRules = $this->getMock(
            'Product_SupplierProductRules',
            array(
                'getSupplierProduct',
                'getSupplierPlatform',
                'isWbc',
                'getProvisionOn'
            ),
            array(),
            '',
            false
        );
        $supplierProductRules->expects($this->once())
            ->method('getSupplierProduct')
            ->will($this->returnValue($supplierProduct));
        $supplierProductRules->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue($supplierPlatform));
        $supplierProductRules->expects($this->once())
            ->method('isWbc')
            ->will($this->returnValue(new Bool(true)));
        $supplierProductRules->expects($this->once())
            ->method('getProvisionOn')
            ->will($this->returnValue(new String('Adsl2')));

        $rules = $this->getMock(
            'AccountChange_ProductRules',
            array('getSupplierProductRules'),
            array()
        );
        $rules->expects($this->once())
            ->method('getSupplierProductRules')
            ->will($this->returnValue($supplierProductRules));

        AccountChange_ProductRules::setInstance($rules);

        $arrProductProvDetails = $rules->getProductProvisionForService(1234, new LineCheck_Result());
        $this->assertEquals(1234, $arrProductProvDetails['intSupplierProductId']);
        $this->assertEquals(1, $arrProductProvDetails['intSupplierPlatformID']);
        $this->assertEquals(true, $arrProductProvDetails['bolWbcProduct']);
        $this->assertEquals('Adsl2', $arrProductProvDetails['strProvisionOn']);

        // Check if the cached result is returned the second time.
        $arrCachedProductProvDetails = $rules->getProductProvisionForService(1234, new LineCheck_Result());
        $this->assertEquals(1234, $arrCachedProductProvDetails['intSupplierProductId']);
        $this->assertEquals(1, $arrCachedProductProvDetails['intSupplierPlatformID']);
        $this->assertEquals(true, $arrCachedProductProvDetails['bolWbcProduct']);
        $this->assertEquals('Adsl2', $arrCachedProductProvDetails['strProvisionOn']);
    }

    /**
     * Test that getLineCheckSpeedCaps returns proper max download and max
     * ulopad speeds for given product
     *
     * @param int   $serviceDefinitionId Service definition id
     * @param array $adslProductDetails  Adsl product details
     * @param array $expectedResult      Expected caps
     *
     * @covers AccountChange_ProductRules::getLinecheckSpeedCaps
     *
     * @dataProvider provideDataToTestGetLineCheckSpeedCapsReturnProperMaxDownAndUpSpeeds
     *
     * @return void
     */
    public function testGetLinecheckSpeedCapsReturnProperMaxDownAndUpSpeeds(
        $serviceDefinitionId,
        $adslProductDetails,
        $expectedResult
    ) {
        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getAdslProductDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockDbAdaptor->expects($this->once())
            ->method('getAdslProductDetails')
            ->with($this->equalTo($serviceDefinitionId))
            ->will($this->returnValue($adslProductDetails));

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $rules = new AccountChange_ProductRules();

        $speedCaps = $rules->getLinecheckSpeedCaps($serviceDefinitionId);

        $this->assertEquals($expectedResult, $speedCaps);
    }

    /**
     * Provide data to test that getLineCheckSpeedCap returns proper max download
     * and upload speeds
     *
     * @return array
     */
    public function provideDataToTestGetLineCheckSpeedCapsReturnProperMaxDownAndUpSpeeds()
    {
        return array(
            array(
                'serviceDefinitionId'   => null,
                'adslProductDetails'    => array(),
                'expectedResult'        => array(
                    'downCap' => null,
                    'upCap'   => null
                ),
            ),
            array(
                'serviceDefinitionId'   => 6754,
                'adslProductDetails'    => array(
                    'intMaximumSpeed'       => 24000,
                    'intMaxUploadSpeed'     => null
                ),
                'expectedResult'        => array(
                    'downCap' => 24000,
                    'upCap'   => null
                ),
            ),
            array(
                'serviceDefinitionId'   => 6768,
                'adslProductDetails'    => array(
                    'intMaximumSpeed'       => 40000,
                    'intMaxUploadSpeed'     => null
                ),
                'expectedResult'        => array(
                    'downCap' => 40000,
                    'upCap'   => null
                ),
            ),
            array(
                'serviceDefinitionId'   => 6784,
                'adslProductDetails'    => array(
                    'intMaximumSpeed'       => 80000,
                    'intMaxUploadSpeed'     => 20000
                ),
                'expectedResult'        => array(
                    'downCap' => 80000,
                    'upCap'   => 20000
                ),
            )
        );
    }
}
