<?php
/**
 * Select Broadband Requirement
 *
 * Collecting the data for the broadband selection requirement
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-09-29
 */

/**
 * AccountChange_SelectBroadband class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */

use Plusnet\Feature\FeatureToggleManager;
use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;

require_once(__DIR__ . '/../Libraries/BizProductsHelper.class.php');

class AccountChange_SelectBroadbandWorkplace extends AccountChange_SelectBroadbandRequirement
{
    /**
     * Keys for base price and base price in contract values returned from Billing API getCustomerAvailableProducts()
     *
     * @var string
     */
    const CURRENT_BASE_PRICE_KEY              = 'currentBasePrice';
    const CURRENT_BASE_PRICE_IN_CONTRACT_KEY  = 'currentBasePriceInContract';
    const PRODUCT_COST_KEY                    = 'intProductCost';
    const PROMOTION_KEY                       = 'promotion';
    const ACCOUNT_CHANGE_ADAPTOR              = 'AccountChange';
    const CAMPAIGN_KEY                        = 'campaign';
    const PROMO_CODE_VALIDATION_ERRORS = array(
        'ERROR_PROMO_PERSONALISED_CODE' => 'Promo code is personalised but is not available for this customer',
        'ERROR_PROMO_RISK_PROFILE_CODE' => 'Promo code does not match the profile for this customer',
        'ERROR_LRS_PROMO_CODE'          => 'Promo code is not available as this customer has line rental saver',
        'ERROR_PROMO_MISSING_PRODUCT_OFFERING_MAPPING' => 'Promo code is unavailable due to an internal error',
        'ERROR_PROMO_WORKPLACE_PROMO_WITH_EXTERNAL_CHANNEL' => 'Promo code is for an external channel so cannot be added in Workplace account change',
        'ERROR_PROMO_CODE_NOT_APPLICABLE_TO_CHANNEL' => 'Promo code is not applicable to this channel.'
    );

    /**
     * Inputs for this requirement.
     *
     * @var array
     */
    protected $arrInputs = array(
        'intNewSdi'                => 'external:custom',
        'bolBtException'           => 'external:Bool',
        'bolOptOut'                => 'external:Custom:conditional',
        'arrComponentsNotToKeep'   => 'external:custom:optional',
        'strProvisionOn'           => 'external:Custom:conditional',
        'promoCode'                => 'external:Val_PromoCodeOrC2mPromoCode->promoCode:optional',
        'selectedContractDuration' => 'external:custom:optional',
        'filterProductFamily'      => 'external:string:optional',
        'backDatedDate'            => 'external:custom:optional'
    );

    /**
     * constants declared for the response from coredb
     */
    const PRODUCT_CHANGE_IN_PROGRESS = -1;

    /**
     * constants declared for the response from new billing engine RBM
     */
    const RBM_INVOICE_DATE_IN_PAST = 2;
    const RBM_FAILED_PAYMENT = 1;
    const RBM_ERROR_RESPONSE = 3;
    const RBM_VALID_SERVICE = 0;


    /**
     * Describe. Pull all the information needed for the view of this requirement.
     *
     * @param array &$arrValidatedApplicationData Validated application data
     *
     * @return array
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $arrReturn = array();
        $intOldSdi = $this->getApplicationStateVariable('intOldSdi');
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $objLineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');
        $arrWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');
        $isCurrentProductFibre = $this->isOldProductFibre();
        $bolHousemove = $this->getApplicationStateVariable('bolHousemove');
        $hasHomephone = $this->hasHomephone($arrWlrProduct, $bolHousemove);
        $isBusiness = false;
        $campaign = $arrValidatedApplicationData[self::CAMPAIGN_KEY];

        if ($this->isApplicationStateVariable('bolIsBusiness')) {
            $isBusiness = $this->getApplicationStateVariable('bolIsBusiness');
        }

        $oldProductFamily = $this->getProductFamily($intOldSdi);
        $intServiceId = $objCoreService->getServiceId();
        $bolShowComponents = false;

        $arrReturn['availableContractDurations'] = null;
        $arrReturn['fromSelectBroadbandWorkplace'] = true;

        // $promotion (i.e not promoCode) signifies a c2m promotion code.
        $promotion = $this->getApplicationStateVariable('promoCode');
        $c2mPromoCode = null;
        if (!empty($promotion) && $promotion->isPromoCodeC2mPromoCode($promotion->getPromotionCode())) {
            $c2mPromoCode = $promotion->getPromotionCode();
        }

        // If we have posted back for more information and the new service definition is not the same as the old one
        if (isset($arrValidatedApplicationData['intNewSdi']) &&
            $arrValidatedApplicationData['intNewSdi'] !== $intOldSdi
        ) {
            $arrSdiDetails = explode('_', $arrValidatedApplicationData['intNewSdi']);

            if (array_key_exists('bolSchedule', $arrValidatedApplicationData) &&
                        !$arrValidatedApplicationData['bolSchedule']) {
                $this->isBackDatedAllowed($arrReturn, $intServiceId, $objCoreService);
            }

            $intNewSdi = $arrSdiDetails[0];
            $intTariffId = '';

            if (isset($arrSdiDetails[1]) && is_numeric($arrSdiDetails[1])) {
                $intTariffId = $arrSdiDetails[1];
            }

            $arrReturn['arrCurrentComponents'] = self::getCurrentComponents($intServiceId);
            $arrReturn['arrComponentsToDelete']
                = self::getComponentsToDelete($intNewSdi, $arrReturn['arrCurrentComponents']);

            // Get a list of component IDs that will be deleted
            $arrComponentIDsToDelete = array();

            foreach ($arrReturn['arrComponentsToDelete'] as $arrComponent) {
                $arrComponentIDsToDelete[] = $arrComponent['component_id'];
            }

            // Remove to to-be-deleted from the list of current components.
            $arrComponentsAfterDeletion = $arrReturn['arrCurrentComponents'];

            foreach ($arrComponentsAfterDeletion as $intKey => $arrComponent) {
                if (in_array($arrComponent['component_id'], $arrComponentIDsToDelete)) {
                    unset($arrComponentsAfterDeletion[$intKey]);
                }
            }

            $arrReturn['arrComponentsToAdd'] = self::getComponentsToAdd($intNewSdi, $arrComponentsAfterDeletion);
            $arrReturn['arrComponentsToKeep'] = self::getComponentsToKeep($intNewSdi, $arrComponentsAfterDeletion);

            $bolShowComponents = true;

            $objDatabase = Db_Manager::getAdaptor(self::ACCOUNT_CHANGE_ADAPTOR, Db_Manager::DEFAULT_TRANSACTION);

            $arrReturn['intSelectedSdi'] = $intNewSdi;
            $arrReturn['intSelectedTariffId'] = $intTariffId;
            $rules = AccountChange_ProductRules::instance();
            $arrProductProvDetails = $rules->getProductProvisionForService(
                $intNewSdi,
                $objLineCheckResult
            );
            $arrReturn['bolWbcProduct'] = $arrProductProvDetails['bolWbcProduct'];

            if ($this->isApplicationStateVariable('arrSelectedBroadband')) {
                $arrReturn['selectedBroadband'] = $this->getApplicationStateVariable('arrSelectedBroadband');
                $arrReturn['intSelectedTariffId'] = $arrReturn['selectedBroadband']['intSelectedTariffID'];
            }

            // Get available contract durations
            $productFamily = ProductFamily_Factory::getFamily($intNewSdi);
            $oldProductFamily = ProductFamily_Factory::getFamily($intOldSdi);
            if ((true === $productFamily->hasAutoContracts()) &&
                (true === $productFamily->isAutoContractedProduct())
            ) {
                $minimumContractDuration = $this->getMinimumContractDuration();

                $overrideDefaultContractLengthDuration = 0;
                if (!empty($c2mPromoCode) && isset($arrValidatedApplicationData['intDiscountLength'])) {
                    $overrideDefaultContractLengthDuration = $arrValidatedApplicationData['intDiscountLength'];
                }

                $arrReturn = (new AccountChange_ContractDurationHelper($objDatabase))->getContractDurationOptions(
                    $arrReturn,
                    $intServiceId,
                    $intNewSdi,
                    $minimumContractDuration,
                    $overrideDefaultContractLengthDuration
                );
            }

            //Remove the option to keep contract if on an old product and moving to the new product
            if ($isBusiness) {
                $arrReturn['bolShowNoRecontractOption'] = $oldProductFamily::PRODUCT_FAMILY_HANDLE === $productFamily::PRODUCT_FAMILY_HANDLE;
            }
        }

        // Setup which product should be selected in the drop down (back button reasons)
        if (!isset($arrReturn['intSelectedSdi'])) {
            $arrReturn['intSelectedSdi'] = $intOldSdi;
            $arrReturn['intSelectedTariffId'] = '';
        }

        $objLineCheckMarket = $this->getLineCheckMarket($objLineCheckResult->getExchangeCode());
        $intMarketId = $objLineCheckMarket->getMarketId();


        $arrNewProducts = $this->getBroadbandProducts(
            $intServiceId,
            $intMarketId,
            true,
            $objLineCheckResult,
            $bolHousemove,
            $hasHomephone,
            $c2mPromoCode,
            $campaign
        );

        $currentProductSpeedRanges = $this->getMinAndMaxSpeedRanges();
        $arrReturn['currentProductDownloadSpeedRangeMin'] = $currentProductSpeedRanges['downloadSpeedRangeMin'];
        $arrReturn['currentProductDownloadSpeedRangeMax'] = $currentProductSpeedRanges['downloadSpeedRangeMax'];

        $currentProduct = array();
        $currentProduct['intSdi'] = $intOldSdi;
        $currentProduct['isFibre'] = $isCurrentProductFibre;
        $currentProduct['productFamily'] = $oldProductFamily;
        $currentProduct['hasPhone'] = $hasHomephone;
        $currentProduct['isp'] = $objCoreService->getIsp();

        // This filtering is only required for Residential products
        if (!$isBusiness) {
            $filter = null;
            if ($bolHousemove) {
                $filter = new AccountChange_Product_BroadbandProductFilter_Housemove();
                if ($arrWlrProduct['bolWlrAddAllowed']) {
                    // We require dual play
                    $filter->setForceAllowDualPlayProducts(true);
                    $filter->setForceAllowSolusProducts(false);
                    $arrReturn['addHomephone'] = true;
                } else {
                    // We can't have dual play
                    $filter->setForceAllowDualPlayProducts(false);
                    $filter->setForceAllowSolusProducts(true);
                    $arrReturn['addHomephone'] = false;
                }
            } else {
                $filter = new AccountChange_Product_BroadbandProductFilter_Workplace();
            }
            $arrGroupedProducts = $filter->filter(
                $objLineCheckResult,
                $arrNewProducts,
                $currentProduct
            );

        } else {
            $availableBglProducts =
                AccountChange_Product_BroadbandProductFilter_Common::getAvailableBizGoLargeProducts($arrNewProducts);
            $filter = new AccountChange_Product_BroadbandProductFilter_Workplace();

            $arrGroupedProducts = $filter->filter(
                $objLineCheckResult,
                $availableBglProducts,
                $currentProduct
            );

            if (isset($arrGroupedProducts['Current Product'])) {
                foreach ($arrGroupedProducts['DualPlay'] as $key => $arrProducts) {
                    if ($arrProducts['intSdi'] == $arrGroupedProducts['Current Product'][0]['intSdi']) {
                        unset($arrGroupedProducts['DualPlay'][$key]);
                    }
                }
            }
        }

        $arrReturn['arrGroupedProducts'] = $arrGroupedProducts;

        $arrReturn['filterProductFamily'] = !empty($arrValidatedApplicationData['filterProductFamily'])
            ? $arrValidatedApplicationData['filterProductFamily'] : null;
        $arrReturn['hasHomephone'] = $hasHomephone;
        $arrReturn['bolShowDialupLink'] = $this->isServiceDefinitionDialup($intOldSdi);
        $arrReturn['bolShowComponents'] = $bolShowComponents;
        $arrReturn['objLinecheckResult'] = $objLineCheckResult;
        $arrReturn['strTelephoneNo'] = $objCoreService->getCliNumber();
        $bolProvisionedOnAdsl2 = $this->isProvisionedOnAdsl2($objCoreService->getServiceId());
        $arrReturn['strProvisionOn'] = $this->getProvisionedOn($objCoreService->getServiceId(), $bolProvisionedOnAdsl2);
        $arrReturn['oldProductFibre'] = $isCurrentProductFibre;
        $arrReturn['promoCode']
            = (!empty($arrValidatedApplicationData['promoCode'])) ? $arrValidatedApplicationData['promoCode'] : '';
        $arrReturn['bolHousemove'] = $bolHousemove;

        if (isset($arrNewProducts['errorCode'])) {
            $arrReturn['promoCodeValidationError'] = $arrNewProducts['errorCode'];
        }

        if ($bolHousemove && !$hasHomephone && !$isBusiness) {
            $arrReturn['isResidentialSolusDualPlayProduct'] = false;
        } else {
            $arrReturn['isResidentialSolusDualPlayProduct'] = (!$isBusiness) && ($oldProductFamily->isSolus() ||
                    $oldProductFamily->isDualPlay());
        }

        $arrReturn['promoCodeValidationErrors'] = self::PROMO_CODE_VALIDATION_ERRORS;

        if (!empty($this->hardwareHelper) && $this->hardwareHelper->shouldUseHelper()) {
            $arrReturn['displayHardwareMessage'] = $this->hardwareHelper->shouldDisplayHardwareMessage();
        }

        return $arrReturn;
    }

    /**
     * Wrapper function to legacy to check if a core service is a dial-up account.
     *
     * @param integer $sdi Service definition id
     *
     * @return boolean
     */
    protected function isServiceDefinitionDialup($sdi)
    {
        return !Core_ServiceDefinition::instance($sdi)->isAdsl();
    }

    /**
     * Wrapper for AccountChange_Controller::getBroadbandProducts.
     *
     * @param integer          $serviceId       Service id
     * @param integer          $marketId        Market id
     * @param boolean          $includeAll      Include all legacy products as well as current ones
     * @param LineCheck_Result $lineCheckResult Line check result
     * @param boolean          $bolHousemove    Does the account in house move
     * @param boolean          $hasPhone        Does the account currently have wlr
     *
     * @return array
     */
    protected function getBroadbandProducts(
        $serviceId,
        $marketId,
        $includeAll,
        $lineCheckResult,
        $bolHousemove = false,
        $hasPhone = false,
        $promoCode = null,
        $campaign = null
    ) {
        // If we have an application state variable of promoCode, it means that we've submmitted it
        // through the input box on SelectBroadbandWorkplace, in this case override the function parameter
        $agentSubmittedPromoCode = false;

        if ($this->isApplicationStateVariable('promoCode') === true) {
            $promoCode = $this->getApplicationStateVariable('promoCode');
            if (!empty($promoCode)) {
                $agentSubmittedPromoCode = true;
            }
        }

        if (is_object($promoCode) && get_class($promoCode) == 'Val_PromoCodeOrC2mPromoCode') {
            $promoCode = $promoCode->getPromotionCode();
        }

        return AccountChange_Controller::getBroadbandProducts(
            $serviceId,
            $marketId,
            $includeAll,
            $lineCheckResult,
            $promoCode,
            $bolHousemove,
            $hasPhone,
            AccountChange_Controller::SOURCE_WORKPLACE_KEY,
            $campaign,
            $agentSubmittedPromoCode
        );
    }

    /**
     * Getter for components that should be kept.
     *
     * @param integer $intNewDefinitionId   New service definition id
     * @param array   $arrCurrentComponents Current components
     *
     * @return array
     */
    private static function getComponentsToKeep($intNewDefinitionId, $arrCurrentComponents)
    {
        $arrDefaultComponentTypes = product_get_default_components_for_service($intNewDefinitionId);
        $arrAllowedComponentTypes = self::normalizeComponentTypesArray(
            product_get_components_for_service($intNewDefinitionId),
            'service_component_id'
        );
        $arrCurrentComponentTypes = self::normalizeComponentTypesArray($arrCurrentComponents, 'component_type_id');

        // Remove all default
        $arrTypes = array_diff($arrCurrentComponentTypes, $arrDefaultComponentTypes);

        // Allow only allowed
        $arrTypes = array_intersect($arrTypes, $arrAllowedComponentTypes);

        $arrReturn = array();

        // Remove all not on the $arrTypes list
        foreach ($arrCurrentComponents as $arrComponent) {
            if (in_array($arrComponent['component_type_id'], $arrTypes)) {
                $arrReturn[] = self::decorateComponent($arrComponent, $arrComponent['component_type_id']);
            }
        }

        return $arrReturn;
    }

    /**
     * Retrieve the piece of information we need from the component data.
     *
     * @param array  $arrIn  Component data
     * @param string $strKey Key
     *
     * @return array
     */
    private static function normalizeComponentTypesArray($arrIn, $strKey)
    {
        $arrOut = array();

        foreach ($arrIn as $arrComponent) {
            $arrOut[] = (int)$arrComponent[$strKey];
        }

        return $arrOut;
    }

    /**
     * Getter for components to add.
     *
     * @param integer $intNewDefinitionId   New service definition id
     * @param array   $arrCurrentComponents Current components
     *
     * @return array
     */
    private static function getComponentsToAdd($intNewDefinitionId, $arrCurrentComponents)
    {
        $arrRet = array();
        $arrComponents = userdata_service_type_change_get_components_to_add($intNewDefinitionId, $arrCurrentComponents);

        foreach ($arrComponents as $intTypeId) {
            $arrRet[] = self::decorateComponent(array(), $intTypeId);
        }

        return $arrRet;
    }

    /**
     * Getter for components to be removed.
     *
     * @param integer $intNewDefinitionId   New service definition id
     * @param array   $arrCurrentComponents Current components
     *
     * @return array
     */
    private static function getComponentsToDelete($intNewDefinitionId, $arrCurrentComponents)
    {
        return userdata_service_type_change_get_components_to_remove(
            $intNewDefinitionId,
            $arrCurrentComponents
        );
    }

    /**
     * Getter for the current components.
     *
     * @param integer $intServiceId Service id
     *
     * @return array
     */
    private static function getCurrentComponents($intServiceId)
    {
        $arrTmp = userdata_component_get_by_service($intServiceId);
        $arrCurrentComponents = array();

        foreach ($arrTmp as $arrCurrentComponent) {
            if (in_array($arrCurrentComponent['status'], array('queued-destroy', 'destroyed'))) {
                continue;
            }

            $arrCurrentComponent
                = self::decorateComponent($arrCurrentComponent, $arrCurrentComponent['component_type_id']);
            $arrCurrentComponent['strStatus'] = $arrCurrentComponent['status'];
            $arrCurrentComponents[] = $arrCurrentComponent;
        }

        return $arrCurrentComponents;
    }

    /**
     * Decorator to populate information for the view.
     *
     * @param array   $arrComponent Component
     * @param integer $intTypeId    Tariff type id
     *
     * @return array
     */
    private static function decorateComponent($arrComponent, $intTypeId)
    {
        if (empty($arrComponent['strName'])) {
            $arrDetails = product_get_component($intTypeId);
            $arrComponent['strName'] = $arrDetails['name'];
        }

        $arrComponent['intComponentTypeId'] = (integer) $intTypeId;

        return $arrComponent;
    }

    /**
     * Validator for components not to keep.
     *
     * @param array $arrComponentsNotToKeep Components to keep
     *
     * @return array
     */
    public function valComponentsNotToKeep($arrComponentsNotToKeep)
    {
        if (empty($arrComponentsNotToKeep)) {
            return array('arrComponentsNotToKeep' => array());
        }

        foreach ($arrComponentsNotToKeep as $intComponentId) {
            if (!ctype_digit($intComponentId)) {
                $this->addValidationError('arrComponentsNotToKeep', 'INCORRECT_ID');

                return array('arrComponentsNotToKeep'=> array());
            }
        }

        return array('arrComponentsNotToKeep' => $arrComponentsNotToKeep);
    }

    /**
     * Is legacy promo code valid for product?
     *
     * @param integer $intNewSdi New product service definition id
     * @param string  $promoCode Promo code
     *
     * @return boolean
     */
    protected function isPromoCodeValidForProduct($intNewSdi, $promoCode)
    {
        $request = new Mvc_HttpRequest();

        // Return an error to the UI if promo code is not valid for the product, so have to do this check also!
        return Val_ReferralPromoCode::isPromoCodeValidForProductAndIp(
            new Val_ReferralPromoCode((string) $promoCode),
            new Val_ConnectivityProduct((int) $intNewSdi),
            $request->getSourceIpAddress()
        );
    }

    /**
     * Gets products with discount.
     *
     * @param array  $selectedBroadband Selected broadband
     * @param string $promoCode         Promotion code
     *
     * @return array
     */
    protected function getProductsWithDiscount($selectedBroadband, $promoCode)
    {
        return AccountChange_Controller::getProductsWithDiscount($selectedBroadband, $promoCode);
    }

    /**
     * Validation for the new service definition id.
     *
     * @param integer $intNewSdi                New service definition id
     * @param string  $promoCode                Promo code
     * @param string  $selectedContractDuration selected contract duration
     * @return array
     */
    public function valNewSdi($intNewSdi, $promoCode = null, $selectedContractDuration = null)
    {
        $arrValidatedReturn = array();

        if (empty($intNewSdi)) {
            $this->addValidationError('intNewSdi', 'MISSING');
            $arrValidatedReturn['intNewSdi'] = '';
        } else {
            $arrSdiDetails = explode('_', $intNewSdi);
            $objCoreService = $this->getApplicationStateVariable('objCoreService');
            $bolHousemove = $this->getApplicationStateVariable('bolHousemove');
            $arrWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');
            $intNewSdi = $arrSdiDetails[0];
            $intTariffId = '';

            if (isset($arrSdiDetails[1]) && is_numeric($arrSdiDetails[1])) {
                $intTariffId = $arrSdiDetails[1];
            }

            $hasHomephone = $this->hasHomephone($arrWlrProduct, $bolHousemove);

            $arrValidSdis = array();
            $arrProductDetails = array();
            $objLineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');
            $objLineCheckMarket = $this->getLineCheckMarket($objLineCheckResult->getExchangeCode());
            $intMarketId = $objLineCheckMarket->getMarketId();

            // $promotion (i.e not promoCode) signifies a c2m promotion code.
            $promotion = $this->getApplicationStateVariable('promoCode');
            if (!empty($promotion) && $promotion->isPromoCodeC2mPromoCode($promotion->getPromotionCode())) {
                $c2mPromoCode = $promotion->getPromotionCode();
            }

            $campaign = $this->getApplicationStateVariable(self::CAMPAIGN_KEY);

            $arrAvailableProducts = $this->getBroadbandProducts(
                $objCoreService->getServiceId(),
                $intMarketId,
                true,
                $objLineCheckResult,
                $bolHousemove,
                $hasHomephone,
                $c2mPromoCode,
                $campaign
            );

            if (isset($arrAvailableProducts['arrProducts']) && is_array(($arrAvailableProducts['arrProducts']))) {
                $arrAvailableProducts = $arrAvailableProducts['arrProducts'];
            }

            foreach ($arrAvailableProducts as $arrProduct) {
                $arrValidSdis[] = $arrProduct['intSdi'];
                $arrProductDetails[$arrProduct['intSdi']][$arrProduct['intTariffID']] = $arrProduct;
            }

            $arrFinancialErrors = $this->getFinancialErrors(
                $objCoreService->getServiceId(),
                $intNewSdi,
                $this->getApplicationStateVariable('intOldSdi')
            );

            if (!empty($arrFinancialErrors)) {
                $this->addValidationError(
                    'intNewSdi',
                    'FINANCIAL_ERROR',
                    array("strError" => $arrFinancialErrors['error'])
                );
            } elseif (!is_numeric($intNewSdi) || !in_array($intNewSdi, $arrValidSdis)) {
                $this->addValidationError('intNewSdi', 'INVALID');
            } else {
                $arrValidatedReturn['intNewSdi'] = (int) $intNewSdi;

                $arrSelectedBroadband = $arrProductDetails[$intNewSdi][$intTariffId];

                if (empty($promoCode) && $arrSelectedBroadband['presetDiscount'] !== null) {
                    $promotionCode = $arrSelectedBroadband['presetDiscount']['promoCode'];
                    $isC2mPromo = Val_PromoCodeOrC2mPromoCode::isPromoCodeC2mPromoCode($promoCode);
                    $promoCode = new Val_PromoCodeOrC2mPromoCode($promotionCode, $isC2mPromo);
                }

                if (is_null($selectedContractDuration) || $selectedContractDuration === '0') {
                    $arrSelectedBroadband[self::PRODUCT_COST_KEY] = $arrSelectedBroadband[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY];
                } else {
                    $arrSelectedBroadband[self::PRODUCT_COST_KEY] = $arrSelectedBroadband[self::CURRENT_BASE_PRICE_KEY];
                }

                $arrValidatedReturn['arrSelectedBroadband'] = array(
                    'intSdi'                         => $intNewSdi,
                    'strNewProduct'                  => $arrSelectedBroadband['strProductName'],
                    'intNewCost'                     => $arrSelectedBroadband[self::PRODUCT_COST_KEY],
                    'intNewLeadingCost'              => $arrSelectedBroadband['objProductDiscountedCost'],
                    'intSelectedTariffID'            => $arrSelectedBroadband['intTariffID'],
                    'strContractHandle'              => $arrSelectedBroadband['strContract'],
                    'provisioningProfile'            => $arrSelectedBroadband['provisioningProfile'],
                    'strProvisionOn'                 => $arrSelectedBroadband['provisioningProfile'],
                    'maxDownstreamSpeed'             => $arrSelectedBroadband['maxDownloadSpeed'],
                    'maxUpstreamSpeed'               => $arrSelectedBroadband['maxUploadSpeed'],
                    'downloadSpeedRangeMin'          => $arrSelectedBroadband['downloadSpeedRangeMin'],
                    'downloadSpeedRangeMax'          => $arrSelectedBroadband['downloadSpeedRangeMax'],
                    'uploadSpeedRangeMin'            => $arrSelectedBroadband['uploadSpeedRangeMin'],
                    'uploadSpeedRangeMax'            => $arrSelectedBroadband['uploadSpeedRangeMax'],
                    'uploadSpeedRangeMgsFormatted'   => $arrSelectedBroadband['uploadSpeedRangeMgsFormatted'],
                    'downloadSpeedRangeMgsFormatted' => $arrSelectedBroadband['downloadSpeedRangeMgsFormatted'],
                    'intMGSInMB'                     => $arrSelectedBroadband['intMGSInMB'],
                    'intMGALSInMB'                   => $arrSelectedBroadband['intMGALSInMB'],
                    'intImpactedMGALS'               => $arrSelectedBroadband['intImpactedMGALS'],
                    'strBroadbandType'               => $arrSelectedBroadband['strBroadbandType'],
                    'isPartner'                      => $arrSelectedBroadband['isPartner'],
                    'intContractLengthMonths'        => $arrSelectedBroadband['intContractLengthMonths'],
                    'selectedContractDuration'       => $selectedContractDuration,
                    'intInContractMonthlyPrice'      => $arrSelectedBroadband[self::CURRENT_BASE_PRICE_IN_CONTRACT_KEY],
                    'intMonthlyPrice'                => $arrSelectedBroadband[self::CURRENT_BASE_PRICE_KEY],
                );

                $fixedPriceContractStatus = $this->getFpcStatusService();
                $arrReturn['isCurrentlyPriceProtected'] =
                $fixedPriceContractStatus->isServicePriceProtected($objCoreService->getServiceId());

                if (!empty($promoCode)) {
                    $arrValidatedReturn['promoCode'] = $promoCode;

                    // Return an error to the UI if the promo code is invalid for product, so have to check also!
                    if (!$promoCode instanceof Val_PromoCodeOrC2mPromoCode && !$this->isPromoCodeValidForProduct($intNewSdi, $promoCode)) {
                        $this->addValidationError('promoCode', 'INVALIDPRODUCT');
                    } elseif (!$promoCode instanceof Val_PromoCodeOrC2mPromoCode) {
                        // This is for Legacy promo codes..
                        // Get the product with discount this automatically checks if promoCode is valid for the product

                        $selectedProductsWithDiscount = $this->getProductsWithDiscount(
                            array($arrValidatedReturn['arrSelectedBroadband']),
                            $promoCode
                        );

                        if (!empty($selectedProductsWithDiscount) && is_array($selectedProductsWithDiscount)) {
                            // Selected product is the first element on the list
                            $arrValidatedReturn['arrSelectedBroadband'] = array_shift($selectedProductsWithDiscount);

                            if (is_array($arrValidatedReturn['arrSelectedBroadband']) &&
                                array_key_exists('presetDiscount', $arrValidatedReturn['arrSelectedBroadband']) &&
                                is_array($arrValidatedReturn['arrSelectedBroadband']['presetDiscount'])
                            ) {
                                $presetDiscount = $arrValidatedReturn['arrSelectedBroadband']['presetDiscount'];

                                if (array_key_exists('intDiscountLength', $presetDiscount)) {
                                    $arrValidatedReturn['intDiscountLength'] = $presetDiscount['intDiscountLength'];
                                }
                            }
                        }
                    } elseif ($promoCode instanceof Val_PromoCodeOrC2mPromoCode) {
                        // We've got a c2m promo code, at this point we've already worked out the discounts so we just need to
                        // add them to the return array with a few traslations to match the expected basket format....
                        $arrValidatedReturn['arrSelectedBroadband']['presetDiscount'] = $arrSelectedBroadband['presetDiscount'];

                        if (!empty($arrSelectedBroadband['presetDiscount']['decValue'])) {
                            $arrValidatedReturn['arrSelectedBroadband']['discountAmount'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $arrSelectedBroadband['presetDiscount']['decValue']);
                            $arrValidatedReturn['discountAmount'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $arrSelectedBroadband['presetDiscount']['decValue']);
                        }
                        if (!empty($arrSelectedBroadband['lineRentalDiscountValue'])) {
                            $arrValidatedReturn['arrSelectedBroadband']['lineRentalDiscountAmount'] = $arrSelectedBroadband['lineRentalDiscountValue'];
                            $arrValidatedReturn['arrSelectedBroadband']['lineRentalPromoDiscountType'] = $arrSelectedBroadband['lineRentalPromoDiscountType'];
                            $arrValidatedReturn['arrSelectedBroadband']['lineRentalDiscountValue'] = $arrSelectedBroadband['lineRentalDiscountValue'];
                        }

                        $arrValidatedReturn['intDiscountLength'] = $arrSelectedBroadband['presetDiscount']['intDiscountLength'];
                    }
                }

                if (AccountChange_Controller::isBundle($intNewSdi)) {
                    $intNewSdi = (int)$intNewSdi;
                    $arrValidatedReturn['intNewWlrId'] = AccountChange_Controller::getWlrIdBySdi($intNewSdi);
                    $arrValidatedReturn['arrSelectedWlr'] = array(
                        'strNewProduct' => AccountChange_Controller::getProductNameBySdi($intNewSdi),
                        'intNewCost' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                    );

                    $arrValidatedReturn['bolSelectedBundle'] = true;
                } else {
                    $arrValidatedReturn['bolSelectedBundle'] = false;
                }

                $lineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');
                $rules = AccountChange_ProductRules::instance();
                $arrProductProvDetails = $rules->getProductProvisionForService($intNewSdi, $lineCheckResult);
                $arrValidatedReturn['bolWbcProduct'] = $arrProductProvDetails['bolWbcProduct'];

                // If there is compulsory hardware associated to new sdi, we need to submit immediate hardware request
                if (!empty($this->arrData['strCompulsoryHardware'])) {
                    $arrValidatedReturn['hardwareOption'] = $this->arrData['strCompulsoryHardware'];
                }

                $arrValidatedReturn['selectedProductFamily'] = $this->getProductFamily($intNewSdi);

                if ($intNewSdi == $this->getApplicationStateVariable('intOldSdi')
                    && !is_null($selectedContractDuration)
                    && $selectedContractDuration !== '0') {
                    $arrValidatedReturn['bolContractResetBroadband'] = true;
                }
            }
        }
        return $arrValidatedReturn;
    }

    /**
     * Validator for selected contract duration.
     *
     * There is a distinction between a contract length of 0 and a null contract length:
     * 0 is something explicitly chosen by the agent, meaning do not recontract.
     * Null indicates that no contract length has been selected.
     *
     * You should only be able to get past SelectBroadbandWorkplace without selecting a contract length
     * if the product doesn't allow the contract length to be selected. In that case, the
     * TermsAndConditions requirement will set the default contract length for that product.
     *
     * @param integer $selectedContractDuration Selected contract duration
     *
     * @return array
     */
    public function valSelectedContractDuration($selectedContractDuration)
    {
        $validatedReturn = array('selectedContractDuration' => null);

        if (isset($selectedContractDuration)) {
            $validatedReturn['selectedContractDuration'] = $selectedContractDuration;
        }

        return $validatedReturn;
    }

    /**
     * You can't recontract to a shorter contract than the time remaining on the existing contract
     *
     * @return int
     */
    public function getMinimumContractDuration()
    {
        // the new product has auto contracts and is contracted
        $minimumContractDuration = 0;

        if ($this->isApplicationStateVariable('existingBroadband')) {
            $existingBroadband = $this->getApplicationStateVariable('existingBroadband');

            if (!empty($existingBroadband) && !empty($existingBroadband['remainingTime'])) {
                // if contracted already can't recontract to less than remaining duration
                $minimumContractDuration = $existingBroadband['remainingTime'];
            }
        }

        return $minimumContractDuration;
    }

    /**
     * Validator for back dated date which is optional field for the Billing Operation team
     * Validation: if the date format is wrong, set class error.
     *             if the date is greater than today or beyond past 90 days, set class error.
     *
     * @param string $backDatedDate input value
     *
     * @return array valid date or null if error has occurred
     */
    public function valBackDatedDate($backDatedDate)
    {
        $validatedReturn = array('backDatedDate' => '');

        if (!empty($backDatedDate)) {
            // Valid date validation
            $date = DateTime::createFromFormat('d/m/Y', $backDatedDate);
            if (!($date && $date->format('d/m/Y') === $backDatedDate)) {
                $this->addValidationError('backDatedDate', 'INVALID');
            }
            $validatedReturn['backDatedDate'] = $backDatedDate;
        }

        return $validatedReturn;
    }

    /**
     * Wrapper method to create a ProductFamily_ProductFamily instance based on the service definition id.
     *
     * @param integer $serviceDefinitionId Represents the product
     *
     * @return ProductFamily_ProductFamily
     */
    protected function getProductFamily($serviceDefinitionId)
    {
        $this->includeLegacyFiles();

        return \ProductFamily_Factory::getFamily($serviceDefinitionId);
    }

    /**
     * Function to get the line check market object.
     *
     * @param string $exchangeCode Exchange code
     *
     * @return LineCheck_Market object
     */
    protected function getLineCheckMarket($exchangeCode)
    {
        return LineCheck_Market::getMarketFromExchange($exchangeCode);
    }

    /**
     * Inclusion of legacy files so we can mock them.
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        class_exists('Smarty');
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/class-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWifiComponent.inc';
    }

    /**
     * Unit test wrapper for AccountChange_Controller::getMinAndMaxSpeedRanges.
     *
     * @return array
     */
    public function getMinAndMaxSpeedRanges()
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $objLineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');

        return AccountChange_Controller::getMinAndMaxSpeedRanges($objCoreService, $objLineCheckResult);
    }

    /**
     * Returns a Auth_BusinessActor object for current logged in user.
     *
     * @return Auth_BusinessActor|bool
     */
    protected function getCurrentBusinessActor()
    {
        $objLogin = Auth_Auth::getCurrentLogin();

        if ($objLogin instanceof Auth_Login) {
            return $objLogin->getBusinessActor();
        }

        return false;
    }

    /**
     * The method to check,whether loggin user can able to do backdated product change or not
     *
     * @param array        &$arrData       view object
     * @param int          $intServiceId   service id of the customer
     * @param Core_Service $objCoreService Core_service object
     *
     * @return void
     */
    public function isBackDatedAllowed(&$arrData, $intServiceId, $objCoreService)
    {
        $hasStatus = false;
        $strMsg = null;
        $objBusinessActor = $this->getCurrentBusinessActor();

        if ($objBusinessActor instanceof Auth_BusinessActor && $objBusinessActor->getUserType()=='PLUSNET_STAFF') {
            $userId = $objBusinessActor->getExternalUserId();

            $objDatabase = Db_Manager::getAdaptor(self::ACCOUNT_CHANGE_ADAPTOR, Db_Manager::DEFAULT_TRANSACTION);
            $intStatusFromDB = $objDatabase->isUserAllowedBackDated($userId);
            $hasStatus = ($intStatusFromDB && $intStatusFromDB>0) ? true: false;

            if ($hasStatus) {
                $intDBValue = $this->checkProductChangeInProgress($objDatabase, $intServiceId);

                switch ($intDBValue) {
                    case self::PRODUCT_CHANGE_IN_PROGRESS:
                        $strMsg = "a product change is already in progress.";
                        break;
                    default:
                        $arrData['lstChangeInDays'] = $intDBValue;
                        break;
                }

                if ($strMsg == null) {
                    $rbmResponse = $this->checkBillingDate($intServiceId, $objCoreService);

                    switch ($rbmResponse) {
                        case self::RBM_FAILED_PAYMENT:
                            $strMsg = "the customer has failed to make one or more payments.";
                            break;
                        case self::RBM_INVOICE_DATE_IN_PAST:
                            $strMsg = "the next invoice date is either today or in the past.";
                            break;
                        case self::RBM_ERROR_RESPONSE:
                            $strMsg = "technical error occured. Please try again later.";
                            break;
                        default:
                            $hasPendingRequest = $this->hasPendingReratingRequest($intServiceId);

                            if ($hasPendingRequest) {
                                $strMsg = "there is already a backdated product change in progress.";
                            }
                            break;
                    }
                }
            }
        }

        $arrData['isAgentAllowBackDated'] = $hasStatus;
        if ($strMsg !== null) {
            $arrData['backDatedProductMsg'] = "Cannot back date product change because ".$strMsg;
        }
    }

    /**
     * Method to check whether any product change is in progress
     * for the customer
     *
     * @param object $objDatabase  object source
     * @param int    $intServiceId service id of the customer
     *
     * @return int -1=>for product change in progress
     *             +value=>number of days.
    */
    public function checkProductChangeInProgress($objDatabase, $intServiceId)
    {
        //check whether any product change is in progress
        $intCount = $objDatabase->getProductChangeInProgress($intServiceId);
        if ($intCount >= 1) {
            $intChangeInProgress = self::PRODUCT_CHANGE_IN_PROGRESS;
        } else {
            //check any product change happened within 90 days
            $intDays = $objDatabase->getDaysDiffOfPreviousProductChange($intServiceId);

            if (!isset($intDays)) {
                $intChangeInProgress = 0;
            } else {
                $intChangeInProgress = $intDays;
            }
        }
        return $intChangeInProgress;
    }

    /**
     * The method to check the billing date is future
     *
     * @param int          $intServiceId   serviceId
     * @param Core_Service $objCoreService Core_service object
     *
     * @return int 1=>failed payment status
     *             2=>invoice date is in past or current day
     *             3=>error occured during BillingApi
     *             0=>invoice is in future - valid scenario.
     */
    public function checkBillingDate($intServiceId, $objCoreService)
    {
        AccountChange_AuditLogger::functionEntry(__METHOD__);

        try {
            $billingApiFacade = \Plusnet\BillingApiClient\Service\ServiceManager::getService('BillingApiFacade');
            $arrRBMResponse = $billingApiFacade->getBillingAccount($intServiceId);

            if (isset($arrRBMResponse)) {
                if (array_key_exists('failedPaymentDetails', $arrRBMResponse) &&
                        array_key_exists('status', $arrRBMResponse['failedPaymentDetails']) &&
                                 $arrRBMResponse['failedPaymentDetails']['status']) {
                    AccountChange_AuditLogger::functionExit(__METHOD__);
                    return self::RBM_FAILED_PAYMENT;
                } elseif (array_key_exists('nextInvoiceDate', $arrRBMResponse)) {
                    $invoicedate = $arrRBMResponse['nextInvoiceDate'];
                    if (!empty($invoicedate) && date('Y-m-d', strtotime($invoicedate)) > date('Y-m-d')) {
                        if (!Core_Service::checkDateToday($invoicedate, $objCoreService->getInvoicePeriod())) {
                            AccountChange_AuditLogger::functionExit(__METHOD__);
                            return self::RBM_VALID_SERVICE;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log("error occurred during RBM call with message".$e->getMessage());
            AccountChange_AuditLogger::functionExit(__METHOD__);
            return self::RBM_ERROR_RESPONSE;
        }

        AccountChange_AuditLogger::functionExit(__METHOD__);

        return self::RBM_INVOICE_DATE_IN_PAST;
    }

     /**
     * Validates any Rerating Request is pending or not.
     *
     * @param int $intServiceId ServiceDefinitionId
     *
     * @return bool Pending Rerating Request Status
     */
    public function hasPendingReratingRequest($intServiceId)
    {
        AccountChange_AuditLogger::functionEntry(__METHOD__);

        $bolPendingRerating = true;

        try {
            $billingApiFacade = \Plusnet\BillingApiClient\Service\ServiceManager::getService('BillingApiFacade');
            $hasPendingReratingRequests = $billingApiFacade->hasPendingReratingRequests($intServiceId);

            if (isset($hasPendingReratingRequests) && is_array($hasPendingReratingRequests)) {
                if ($hasPendingReratingRequests['requestCount'] == 0) {
                    $bolPendingRerating = false;
                }
            }
        } catch (Exception $e) {
            error_log("error occurred during RBM call with message".$e->getMessage());
        }

        AccountChange_AuditLogger::functionExit(__METHOD__);

        return $bolPendingRerating;
    }

    /**
     * Checks whether customer already has phone (non-house move) or is adding it (house move)
     *
     * TODO: Fix ugly hack
     * $hasHomephone originally meant that the customer ALREADY has phone,
     * and therefore should only be shown dual play products (http://jira.internal.plus.net/browse/PRI-453)
     * In these cases, $arrWlrProduct would exist but bolWlrAddAllowed would be false.
     *
     * During early house moves builds, bolWlrAddAllowed was used to indicate that a solus customer was adding
     * phone during the house move, but it was incorrectly set to false when they were adding phone.
     *
     * The overloading of this variable has made things very complicated, and there's some more logic that needs
     * untangling. It seems to all work correctly, but there are some ugly bits and near-duplicate code around.
     *
     * @param array $arrWlrProduct Wlr Product details
     * @param bool  $bolHousemove  Housemove boolean
     *
     * @return bool hasHomephone
     */
    private function hasHomephone($arrWlrProduct, $bolHousemove)
    {
        if (!empty($arrWlrProduct) && (
                (
                    $bolHousemove &&
                    !empty($arrWlrProduct['bolWlrAddAllowed'])
                ) || (
                    !$bolHousemove &&
                    empty($arrWlrProduct['bolWlrAddAllowed'])
                )
            )
        ) {
            return true;
        }

        return false;
    }
}
