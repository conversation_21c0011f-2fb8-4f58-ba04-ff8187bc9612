<?php


interface PromotionRulesComposite
{

    /**
     * Add a rule to the list that will be applied to the promotions.
     * @param \PromotionRule $rule
     *
     * @return mixed
     */
    public function addRule(PromotionRule $rule);

    /**
     * Remove a rule from the list that will be applied the the promotions
     * @param \PromotionRule $rule
     *
     * @return mixed
     */
    public function removeRule(PromotionRule $rule);

}