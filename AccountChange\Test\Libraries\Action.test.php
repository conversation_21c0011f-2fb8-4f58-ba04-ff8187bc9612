<?php
/**
 * Action
 *
 * Testing class for the AccountChange_Action abstract class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Action.test.php,v 1.3 2009-03-04 10:33:44 smarek Exp $
 * @since      File available since 2008-09-04
 */
/**
 * Action Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Action_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for the constructor: intServiceId
     *
     * @var int
     */
    private $intServiceId;

    /**
     * Fixture for the constructor: arrOptions
     *
     * @var array
     */
    private $arrOptions;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        $this->intServiceId = 919191;
        $this->arrOptions   = array('intId' => 1, 'strString' => 'aString');
    }

    public function tearDown()
    {
        Db_Manager::restoreAdaptor('Auth');
        Db_Manager::restoreAdaptor('TicketClient');
    }

    /**
     * @covers AccountChange_Action::__construct
     * @covers AccountChange_Action::initialise
     */
    public function testInitialiseSetsUpTheObjectCorrect()
    {
        $objActionMock = $this->getMock(
            'AccountChange_Action',
            array('execute'),
            array(111), '', false
        );

        $objActionMock->expects($this->any())
                      ->method('execute');

        $objActionMock->initialise($this->intServiceId, $this->arrOptions);

        $this->assertAttributeEquals($this->intServiceId, 'intServiceId', $objActionMock);
        $this->assertAttributeEquals($this->arrOptions, 'arrOptions', $objActionMock);
    }

    /**
     * @covers AccountChange_Action::initialise
     * @covers AccountChange_Action_ManagerException
     */
    public function testInitialiseThrowsExceptionIfServiceIdIsInvalid()
    {
        $objActionMock = $this->getMock(
            'AccountChange_Action',
            array('execute'),
            array(111), '', false
        );

        $objActionMock->expects($this->any())
                      ->method('execute');

        $this->setExpectedException(
            'AccountChange_Action_ManagerException', 'Non numeric service id',
            AccountChange_Action_ManagerException::ERR_INVALID_SERVICE_ID_TYPE
        );

        $objActionMock->initialise('Invalid Service Id', $this->arrOptions);
    }

    /**
     * @covers AccountChange_Action::getServiceId
     */
    public function testGetServiceId()
    {
        $objAction = $this->getMock(
            'AccountChange_Action', array('execute'), array($this->intServiceId, $this->arrOptions)
        );

        $intServiceId = $objAction->getServiceId();

        $this->assertEquals($this->intServiceId, $intServiceId);
    }

    /**
     * @covers AccountChange_Action::raiseTicket
     */
    public function testRaiseTicketDoesNotRaiseATicketIfTheBodyOrPoolAreNotSet()
    {
        $objActionMock = $this->getMock(
            'AccountChange_Action',
            array('execute'),
            array(111), '', false
        );

        $objActionMock->expects($this->any())
                      ->method('execute');

        $objBusTierMock = $this->getMock(
            'BusTier_BusTier',
            array('getClient'),
            array(), '', false
        );

        $objBusTierMock->expects($this->never())
                       ->method('getClient');

        $unkReturn = $objActionMock->raiseTicket('', '');
        $this->assertEquals(0, $unkReturn);
    }

    /**
     * @covers AccountChange_Action::raiseServiceEvent
     *
     */
    public function testRaiseServiceEventDoesNotRaiseAServiceEventIfTheBodyIsNotSet()
    {
        $objActionMock = $this->getMock(
            'AccountChange_Action',
            array('execute'),
            array(111), '', false
        );

        $objActionMock->expects($this->any())
                      ->method('execute');

        $objBusTierMock = $this->getMock(
            'BusTier_BusTier',
            array('getClient'),
            array(), '', false
        );

        $objBusTierMock->expects($this->never())
                       ->method('getClient');

        $objActionMock->raiseServiceEvent('');
    }

    /**
     * @covers AccountChange_Action::raiseTicket
     */
    public function testRaiseTicket()
    {
        $strTicketComment = 'A test comment';
        $strPoolHandle = 'TEST_POOL';
        $intActorId = 4567;
        $intTeamId = 9678;
        $intTicketId = 666;
        $arrBusinessActor = array (
            'intActorId'        => $intActorId,
            'strUsername'       => 'myUsername',
            'strRealm'          => 'myRealm',
            'strUserType'       => 'myUserType',
            'strExternalUserId' => $this->intServiceId
        );
        $arrDbTeam = array (
            'intTeamId'          => $intTeamId,
            'strTeamName'        => $strPoolHandle,
            'strLeaderId'        => 1,
            'strNomineeId'       => 1,
            'strDescription'     => '',
            'strTicketingStatus' => '',
            'intSlgMins'         => 1
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getActorByExternalUserId', 'getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objAuthAdaptor->expects($this->once())
                       ->method('getActorByExternalUserId')
                       ->with($this->equalTo($this->intServiceId))
                       ->will($this->returnValue(array('intActorId' => $intActorId)));
        $objAuthAdaptor->expects($this->once())
                       ->method('getBusinessActor')
                       ->will($this->returnValue(array($arrBusinessActor)));

        $objTicketClientAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getTeamIdByHandle', 'getDbTeam'),
            array('TicketClient', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objTicketClientAdaptor->expects($this->once())
                               ->method('getTeamIdByHandle')
                               ->with($this->equalTo($strPoolHandle))
                               ->will($this->returnValue(array('intTeamId' => $intTeamId)));
        $objTicketClientAdaptor->expects($this->once())
                               ->method('getDbTeam')
                               ->will($this->returnValue(array($arrDbTeam)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);
        Db_Manager::setAdaptor('TicketClient', $objTicketClientAdaptor);

        //Intentionally changed the TicketClient_Ticket name to TicketClient_Ticket_mock so that it will not
        //Call the orginal class. This is done because the original class is including some legacy files and
        //was causings some problems
        $objTicket = $this->getMock('TicketClient_Ticket_mock', array('comment', 'getId', 'attachTicketCause', 'getTicketId'), array(), '', false);
        $objTicket->expects($this->once())
            ->method('comment');
        $objTicket->expects($this->once())
                  ->method('getId')
                  ->will($this->returnValue($intTicketId));
        $objTicket->expects($this->once())
                  ->method('attachTicketCause');

        $objTicketClient = $this->getMock('TicketClient_TicketClient', array('createTicket'), array(), '', false);
        $objTicketClient->expects($this->once())
                        ->method('createTicket')
                        ->will($this->returnValue($objTicket));

        BusTier_BusTier::setClient('tickets', $objTicketClient);

        $objAction = $this->getMock('AccountChange_Action', array('execute'), array($this->intServiceId));
        $intReturn = $objAction->raiseTicket($strTicketComment, $strPoolHandle);

        $this->assertEquals($intTicketId, $intReturn);
    }

    /**
     * @covers AccountChange_Action::raiseServiceEvent
     */
    public function testRaiseServiceEvent()
    {
        $strBody = 'myBody';
        $intServiceNoticeTypeId = 10;
        $intActorId = 4567;
        $arrBusinessActor = array (
            'intActorId'        => $intActorId,
            'strUsername'       => 'myUsername',
            'strRealm'          => 'myRealm',
            'strUserType'       => 'myUserType',
            'strExternalUserId' => $this->intServiceId
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getActorByExternalUserId', 'getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objAuthAdaptor->expects($this->once())
                       ->method('getActorByExternalUserId')
                       ->with($this->equalTo($this->intServiceId))
                       ->will($this->returnValue(array('intActorId' => $intActorId)));
        $objAuthAdaptor->expects($this->once())
                       ->method('getBusinessActor')
                       ->will($this->returnValue(array($arrBusinessActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objServiceNotice = $this->getMock(
            'ServiceNoticeClient_ServiceNotice',
            array(
                'setBody',
                  'setServiceNoticeTypeId',
                  'setActionerId',
                  'write'
            )
        );
        $objServiceNotice->expects($this->once())
                         ->method('setBody')
                         ->with($this->equalTo($strBody));
        $objServiceNotice->expects($this->once())
                         ->method('setServiceNoticeTypeId')
                         ->with($this->equalTo($intServiceNoticeTypeId));
        $objServiceNotice->expects($this->once())
                         ->method('setActionerId')
                         ->with($this->equalTo(0));
        $objServiceNotice->expects($this->once())
                         ->method('write');

        $objClient = $this->getMock(
            'ServiceNoticeClient_ServiceNoticeClient',
            array('createServiceNotice'), array(), '', false
        );
        $objClient->expects($this->once())
                  ->method('createServiceNotice')
                  ->will($this->returnValue($objServiceNotice));

        BusTier_BusTier::setClient('serviceNotices', $objClient);

        $objAction = $this->getMock('AccountChange_Action', array('execute'), array($this->intServiceId));
        $objAction->raiseServiceEvent($strBody, $intServiceNoticeTypeId);
    }
}
