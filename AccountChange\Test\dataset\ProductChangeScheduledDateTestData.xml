<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE dataset SYSTEM "dataset.dtd">
<dataset>
    <table name="userdata.service_change_schedule">
        <column>schedule_id</column>
        <column>service_id</column>
        <column>owner_id</column>
        <column>old_type</column>
        <column>new_type</column>
        <column>change_complete_date</column>
        <column>active</column>
        <column>db_src</column>
        <column>intFromCBCFlexID</column>
        <column>intToCBCFlexID</column>
        <column>timestamp</column>
        <column>change_date</column>
        <column>change_complete</column>
        <!--
            START: UpdateProductChangeScheduledDateTest::testICorrectlyUpdateChangeDateWhenMoreThanOneRecordExistsForAServiceId
        -->
        <!-- Target record has active "yes", change_complete "no" -->
        <row>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>yes</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>no</value>
        </row>
        <row>
            <value>2</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>no</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>yes</value>
        </row>
        <!-- Target record has active "yes", change_complete "yes" -->
        <row>
            <value>3</value>
            <value>2</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>yes</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>yes</value>
        </row>
        <row>
            <value>4</value>
            <value>2</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>no</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>yes</value>
        </row>
        <!-- Target record has active "no", change_complete "yes" -->
        <row>
            <value>5</value>
            <value>3</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>no</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>yes</value>
        </row>
        <row>
            <value>6</value>
            <value>3</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>no</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>yes</value>
        </row>
        <!-- Target record has active "no", change_complete "no" -->
        <row>
            <value>7</value>
            <value>4</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>no</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>no</value>
        </row>
        <row>
            <value>8</value>
            <value>4</value>
            <value>1</value>
            <value>1</value>
            <value>1</value>
            <value>2001-01-01</value>
            <value>no</value>
            <value>db_src</value>
            <value>intFromCBCFlexID</value>
            <value>intToCBCFlexID</value>
            <value>timestamp</value>
            <value>2001-01-01</value>
            <value>yes</value>
        </row>
        <!--
            END: UpdateProductChangeScheduledDateTest::testICorrectlyUpdateChangeDateWhenMoreThanOneRecordExistsForAServiceId
        -->
    </table>
</dataset>