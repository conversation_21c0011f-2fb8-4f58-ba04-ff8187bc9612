<?php

use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;
use AccountChange_BillingApi_BasePriceHelper as BasePriceHelper;
use AccountChange_BillingApi_ProductOfferingPricePointIdHelper as ProductOfferingPricePointIdHelper;

/**
 * Home Phone Requirement
 *
 * A super class for Portal and Workplace HP reqs
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <kp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright 2008 Plusnet
 * @link      is a character from the legend of zelda
 * @since     File available since 2009-02-01
 */
/**
 * AccountChange_SelectHomephoneRequirement class
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright 2009 Plusnet
 * @link      is a character from the legend of zelda
 */
class AccountChange_SelectHomephoneRequirement extends Mvc_WizardRequirement
{
    const MOBILE_BOLTON_FEATURE_READY_TO_ROLL = true;

    /**
     * Keys used multiple times to retrieve and add values from/to arrays
     *
     * @var string
     */
    const SERVICE_COMPONENT_ID_KEY = 'intServiceComponentID';
    const TARIFF_ID_KEY            = 'intTariffID';
    const SPLIT_PRICE_KEY          = 'bolSplitPrice';
    const LINE_RENTAL_COST_KEY     = 'intLineRentCost';
    const CALL_PLAN_COST_KEY       = 'intCallPlanCost';
    const CONTRACT_HANDLE_KEY             = 'strContractHandle';
    const LINE_RENTAL_DISCOUNT_AMOUNT_KEY = 'lineRentalDiscountAmount';
    const LINE_RENTAL_DISCOUNTED_PRICE_KEY = 'lineRentalDiscountedPrice';

    /**
     * Get list of service component IDs belonging to WLR products that
     * shouldn't be visible to the outside world, specifically the portal
     * or anywhere customer-facing.
     *
     * This is predominantly for the two new Mobile Anytime trial products
     * that are being created. They will be complete products in their own
     * right and will be seen by the system as "available" products for
     * use.
     *
     * They need to be hidden from customers, so by default we list their
     * IDs here. Workplace can override this functionality because it should
     * be the only frontend that can see them.
     *
     * @return array
     **/
    protected function getExcludedWlrProducts()
    {
        return array(
            COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL1,
            COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL2
        );
    }

    /**
     * Wrapping the call AccountChange_Manager::hasActiveLineRentalSaver() for
     * the ease of unit testing
     *
     * @return bool
     */
    protected function hasActiveLineRentalSaver()
    {
        $intServiceId = $this->getApplicationStateVariable('objCoreService')->getServiceId();
        return AccountChange_Manager::hasActiveLineRentalSaver($intServiceId);
    }

    /**
     * Wrapper that uses account change libraries to check if a certain SDI belongs
     * to a fibre product.
     *
     * @param int $sdi The SDI of the product to check.
     *
     * @return bool True If the SDI belongs to a Fibre product.
     */
    protected function isFibreProduct($sdi)
    {
        $fibreHelper = new AccountChange_FibreHelper();
        return $fibreHelper->isFibreProduct($sdi);
    }

    /**
     * For business 2012 (BRP-570) the WLR price varies by contract length, with
     * a different product for each length.
     *
     * This function filters all the wlr products down to a list that had the same
     * contract length as the selected BB product, defaulting to uncontracted if we've selected a
     * legacy product.
     *
     * @param array                                  $products            An array of all wlr products
     * @param int                                    $serviceDefinitionId The sdid of the new bb product
     * @param AccountChange_Product_WlrProductFilter $filter              The WLR product filter
     * @return array
     */
    public function filterWlrProductsBasedOnContractLength(
        $products,
        $serviceDefinitionId,
        AccountChange_Product_WlrProductFilter $filter
    ) {
        $contractLengthLookup = array(
            'No Contract'       => -1,
            '12 Month Contract' => 12,
            '24 Month Contract' => 24
        );

        $broadbandContractLength = $this->getProductDefaultContractLength($serviceDefinitionId);

        // If we're moving onto a legacy product or an uncontracted product, only
        // show the uncontracted wlr products.
        if (empty($broadbandContractLength)) {
            $broadbandContractLength = -1;
        }

        foreach ($products as $key => $product) {
            preg_match('/\(([^\)]*)\)/', $product['ProductName'], $matches);
            if (isset($matches[1])) {
                $wlrContractLength = $contractLengthLookup[$matches[1]];
            } else {
                // Default to no contract if no BB contract length found
                $wlrContractLength = -1;
            }
            if ($broadbandContractLength != $wlrContractLength && !$this->isBglProduct($filter, $product)) {
                unset($products[$key]);
            }
        }

        return $products;
    }


    /**
     * Gets the length of the broadband contract based on service definition id.
     *
     * It's only valid for product families that use the new contracting system
     * i.e. Falcon onwards.
     *
     * @param int $serviceDefinitionId The id of the customers service definition
     *
     * @return int
     **/
    public function getProductDefaultContractLength($serviceDefinitionId)
    {
        $database = Db_Manager::getAdaptor('AccountChange');
        return $database->getContractDefinitionLengthFromSdi($serviceDefinitionId);
    }

    /**
     * Get the Wlr products that we can display
     *
     * @param integer $intServiceDefinitionId  New Service Definition ID
     * @param string  $strContractHandle       Contract Handle
     * @param boolean $bolIncludeNoWlr         Should the "No Home Phone" option be shown?
     * @param boolean $keepingExistingContract Are they keeping their existing contract?
     *
     * @return array
     */
    protected function getWlrProducts($intServiceDefinitionId,
                                      $strContractHandle = null,
                                      $bolIncludeNoWlr = false,
                                      $keepingExistingContract = false)
    {
        $this->includeLegacyFiles();

        $arrNewProducts = array();
        $product = new Core_ServiceDefinition($intServiceDefinitionId);

        $strReportSectorHandle = $this->getReportSector($intServiceDefinitionId);

        if (Core_ServiceDefinition::BUSINESS_ACCOUNT == $strReportSectorHandle) {
            $strCustomerSectorHandle = 'BUSINESS';
        } else {
            $strCustomerSectorHandle = 'CONSUMER';
        }

        // Pull all legacy wlr products that can be taken with lrs
        $filter = new AccountChange_Product_WlrProductFilter();
        $lrsServiceComponentList = $filter->getLegacyWlrLrsProducts();


        $arrProducts = $this->getAllUpgradeProducts(
            $strCustomerSectorHandle,
            $intServiceDefinitionId,
            $lrsServiceComponentList
        );

        // For BRP12, we need to only show wlr products with the same length contract as the
        // selected broadband product.
        if ($strCustomerSectorHandle == 'BUSINESS') {
            $arrProducts = $this->filterWlrProductsBasedOnContractLength($arrProducts, $intServiceDefinitionId, $filter);
        }

        $oldWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');
        $oldWlrId = $oldWlrProduct['intOldWlrId'];

        $oldServiceDefinitionId = $this->getApplicationStateVariable('intOldSdi');
        $oldProduct = new Core_ServiceDefinition($oldServiceDefinitionId);

        $objBusinessActor = $this->getApplicationStateVariable('objBusinessActor');

        $forceWlrProductChange = false;

        if ('PLUSNET_STAFF' != $objBusinessActor->getUserType()) {
            $forceWlrProductChange = ($product->getIsp() == 'johnlewis'
                && in_array($oldProduct->getIsp(), array('greenbee', 'waitrose')));
        }

        // Include the current WLR product the customer has if:
        // 1) The current product is not an essential product
        // 2) The customer is moving from GB/WR to JL based products
        if (!(AccountChange_Controller::isEssential($oldServiceDefinitionId)
            && AccountChange_Controller::isWlrEssential($oldWlrId))
            && !$forceWlrProductChange
        ) {
            $arrProducts = $this->applyProductImCurrentlyOn($arrProducts);
        }

        $objProduct = $this->getProductComponent();

        // Get call plan prices from Billing API
        $callPlanPricePointPairs = array();
        foreach ($arrProducts as $key => $arrProduct) {

            $arrCallPlan = $this->getCallPlanDetailsByWlrServiceComponentId($arrProduct[self::SERVICE_COMPONENT_ID_KEY]);

            $productOfferingId = $arrCallPlan[self::SERVICE_COMPONENT_ID_KEY];
            $pricePointId      = $arrCallPlan[self::TARIFF_ID_KEY];
            $productOfferingPricePointPair = new ProductOfferingPricePointPair($productOfferingId, $pricePointId);

            $callPlanProductOfferingPricePointId =
                $this->generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);
            $callPlanPricePointPairs[] = $productOfferingPricePointPair;

            $arrProducts[$key]['callPlanProductOfferingPricePointId'] = $callPlanProductOfferingPricePointId;
            $arrProducts[$key][self::SPLIT_PRICE_KEY]                 = $arrCallPlan['bolDisplaySplitCharge'];
        }

        $serviceId = $this->getApplicationStateVariable('objCoreService')->getServiceId();
        $callPlanBasePrices = $this->getBasePrices($serviceId, $callPlanPricePointPairs);

        foreach ($arrProducts as $arrProduct) {

            $callPlanProductOfferingPricePointId = $arrProduct['callPlanProductOfferingPricePointId'];
            $callPlanBasePrice = AccountChange_Controller::createCurrencyObject(
                $callPlanBasePrices[$callPlanProductOfferingPricePointId]['currentBasePrice']
            );

            $bolSplitPrice = $arrProduct[self::SPLIT_PRICE_KEY];

            $arrProductContractOptions = $objProduct->getProductContractOptions(
                $arrProduct[self::SERVICE_COMPONENT_ID_KEY],
                'SUBSCRIPTION',
                true
            );

            if (sizeof($arrProductContractOptions) != 1) {

                throw new AccountChange_MultipleLineRentalTariffsException(
                    "Expected exactly one line rental tariff for call plan but found: " .
                    print_r($arrProductContractOptions, true));
            }

            // Get Line Rental price from BillingAPI
            $productContractOption = $arrProductContractOptions[0];
            $productOfferingId     = $arrProduct[self::SERVICE_COMPONENT_ID_KEY];
            $pricePointId          = $productContractOption[self::TARIFF_ID_KEY];
            $productOfferingPricePointPair = new ProductOfferingPricePointPair($productOfferingId, $pricePointId);

            $lineRentalProductOfferingPricePointId =
                $this->generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

            $lineRentalBasePrices = $this->getBasePrices($serviceId, array($productOfferingPricePointPair));
            $lineRentalBasePrice  = $lineRentalBasePrices[$lineRentalProductOfferingPricePointId];

            // Check if there is an ACTIVE LRS contract in the account, set the line rental price to zero if there is
            if ($this->hasActiveLineRentalSaver()) {

                $lineRentalPrice = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
                $intProductCost = $callPlanBasePrice->toDecimal();
            } else {

                if ($keepingExistingContract) {
                    $lineRentalPrice = AccountChange_Controller::createCurrencyObject(
                        $lineRentalBasePrice['currentBasePriceInContract']
                    );
                } else {
                    $lineRentalPrice = AccountChange_Controller::createCurrencyObject(
                        $lineRentalBasePrice['currentBasePrice']
                    );
                }

                $intProductCost = $callPlanBasePrice->toDecimal() + $lineRentalPrice->toDecimal();
            }

            if (is_null($strContractHandle)
                || $strContractHandle == $productContractOption[self::CONTRACT_HANDLE_KEY]
                || $strCustomerSectorHandle == 'BUSINESS') {

                $lineRentalDiscounts = $this->getLineRentalDiscounts($lineRentalPrice);

                if (!is_null($lineRentalDiscounts['discountAmountInPounds']) && $lineRentalDiscounts['discountAmountInPounds']->toDecimal() > 0) {
                    $intProductCost -= $lineRentalDiscounts['discountAmountInPounds']->toDecimal();
                }

                $arrNewProducts[] = array(
                    'intNewWlrId'                  => $arrProduct[self::SERVICE_COMPONENT_ID_KEY],
                    'intServiceComponentId'        => $arrProduct[self::SERVICE_COMPONENT_ID_KEY],
                    'strContract'                  => $productContractOption[self::CONTRACT_HANDLE_KEY],
                    'strProductName'               => $arrProduct['ProductName'],
                    self::SPLIT_PRICE_KEY          => $bolSplitPrice,
                    'intProductCost'               => new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $intProductCost
                    ),
                    self::LINE_RENTAL_COST_KEY             => $lineRentalPrice,
                    self::CALL_PLAN_COST_KEY               => $callPlanBasePrice,
                    self::LINE_RENTAL_DISCOUNT_AMOUNT_KEY  => $lineRentalDiscounts['discountAmountInPounds'],
                    self::LINE_RENTAL_DISCOUNTED_PRICE_KEY => $lineRentalDiscounts['lineRentalDiscountedPrice']
                );
            }
        }

        // sort the products
        $arrNewProducts = $this->sortWlrProducts($arrNewProducts, $oldWlrId);

        // Don't include 'No Home Phone' option if the customer is switching to John Lewis
        if ($bolIncludeNoWlr && !$forceWlrProductChange) {

            $arrNewProducts[] = array(
                'intNewWlrId'              => 0,
                'strContract'              => 'MONTHLY',
                'strProductName'           => 'No Home Phone',
                self::SPLIT_PRICE_KEY      => false,
                'intProductCost'           => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                self::LINE_RENTAL_COST_KEY => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0),
                self::CALL_PLAN_COST_KEY   => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0)
            );
        }

        return $arrNewProducts;
    }

    /**
     *  Calculates line rental discounts
     *
     * @param I18n_Currency $lineRentalPrice Line rental price
     *
     * @return array
     */
    protected function getLineRentalDiscounts($lineRentalPrice)
    {
        $lineRentalDiscountedPrice = null;
        $discountAmountInPounds = null;

        if ($this->isApplicationStateVariable('intNewSdi') && $this->isApplicationStateVariable('arrSelectedBroadband')) {
            $selectedBroabdband = $this->getApplicationStateVariable('arrSelectedBroadband');
            if (!empty($selectedBroabdband['lineRentalPromoDiscountType']) && !empty($selectedBroabdband['lineRentalDiscountAmount'])) {
                if ($selectedBroabdband['lineRentalPromoDiscountType'] == AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT) {
                    $discountAmountInPounds = $lineRentalPrice->toDecimal() * ($selectedBroabdband['lineRentalDiscountAmount'] / 100);
                    $discountAmountInPounds = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $discountAmountInPounds);
                } else {
                    $discountAmountInPounds = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $selectedBroabdband['lineRentalDiscountAmount']);
                }
                $lineRentalDiscountedPrice = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $lineRentalPrice->toDecimal() - $discountAmountInPounds->toDecimal());
            }
        }

        return array('lineRentalDiscountedPrice' => $lineRentalDiscountedPrice, 'discountAmountInPounds' => $discountAmountInPounds);
    }


    /**
     * Sort array of products using the sorter class
     *
     * @param array   $arrProducts products that need to be sorted
     * @param integer $intOldId    ID of the currently selected product
     *
     * @return array
     */
    protected function sortWlrProducts(array $arrProducts, $intOldId)
    {
        $sorter = new AccountChange_Product_WlrProductSorter();
        return $sorter->sort($arrProducts, $intOldId);
    }

    /**
     * Get the available WLR upgrade product list.
     *
     * @param string  $customerSectorHandle    Customer Sector Handle
     * @param integer $serviceDefinitionId     Service Definition ID
     * @param array   $lrsServiceComponentList List of otherwise expired service component ids that should be shown
     *                                         because they are still available to customers on their version of LRS
     *
     * @return array available WLR products
     */
    protected function getAllUpgradeProducts(
        $customerSectorHandle,
        $serviceDefinitionId,
        $lrsServiceComponentList = array()
    ) {
        $objDatabase = Db_Manager::getAdaptor('AccountChange');

        // The "NOT IN" clause in the SQL cannot handle an empty array so if we have one use an array with
        // '0' for ServiceComponentID as that does not exist anyway.
        $excludedWlrProducts = $this->getExcludedWlrProducts();
        $excludedWlrProducts = empty($excludedWlrProducts)?array(0):$excludedWlrProducts;

        return $objDatabase->getAvailableWlrServiceComponentProducts(
            'WLR',
            $customerSectorHandle,
            $excludedWlrProducts,
            $lrsServiceComponentList,
            $serviceDefinitionId
        );
    }

    /**
     * If customer is on a legacy product, the product is not listed by getAllUpgradeProducts
     * If we recontract (only) - we need to add legacy product he is currently on to the list
     *
     * @param array $arrProducts product list
     *
     * @return $arrProducts
     */
    public function applyProductImCurrentlyOn($arrProducts)
    {
        $arrWlrDetails = $this->getApplicationStateVariable('arrWlrProduct');

        //check if not on WLR
        if (empty($arrWlrDetails['intOldWlrId'])) {

            //if not on WLR ATM
            return $arrProducts;
        }

        //check if component already on the list
        foreach ($arrProducts as $arrProduct) {

            if ($arrWlrDetails['intOldWlrId'] == $arrProduct[self::SERVICE_COMPONENT_ID_KEY]) {

                //component you are currently on is on the list already
                return $arrProducts;
            }
        }

        //it must be a legacy component - let's add it
        $arrOldWlr = array (
            self::SERVICE_COMPONENT_ID_KEY => $arrWlrDetails['intOldWlrId'],
            self::CONTRACT_HANDLE_KEY      => $arrWlrDetails[self::CONTRACT_HANDLE_KEY],
            'ProductName'                  => $arrWlrDetails['strProductName']
        );
        $arrProducts[] = $arrOldWlr;
        return $arrProducts;
    }

    /**
     * Function to get new product component object
     * Added to make the code testable
     *
     * @return CProduct
     */
    protected function getProductComponent()
    {
        return new CProduct();
    }

    /**
     * Function to get call plan details for given service component Id
     * Added to make the code testable
     *
     * @param int $serviceComponentId the service component id
     *
     * @return array
     */
    protected function getCallPlanDetailsByWlrServiceComponentId($serviceComponentId)
    {
        return CWlrProduct::getCallPlanDetailsFromServiceComponentId($serviceComponentId);
    }

    /**
     * Fetches the Report Sector (business/residential) based on service definition id
     *
     * @param int $intServiceDefinitionId the service definition id
     *
     * @return string
     */
    protected function getReportSector($intServiceDefinitionId)
    {
        $objDatabase = Db_Manager::getAdaptor('AccountChange');

        return $objDatabase->getReportSectorByServiceDefinitionId($intServiceDefinitionId);
    }

    /**
     * Fetches a list of Call Features for the WLR component
     *
     * @param int $intWlrComponentId WLR component to get call features for
     *
     * @return array of call features for the WLR component
     */
    protected function getWlrCallFeatures($intWlrComponentId)
    {
        $this->includeLegacyFiles();

        $arrCallFeatures = array();

        if (!empty($intWlrComponentId)) {
            // return call features for the old phone component!

            // create the WLR component
            $objWlrComponent = new CWlrProduct($intWlrComponentId);

            // Obtain the call features
            if (!empty($objWlrComponent)) {
                $arrCallFeatures = $objWlrComponent->getCurrentCallFeatures();
            }
        }

        return $arrCallFeatures;
    }

    /**
     * Validate caller display input
     *
     * @param boolean $callerDisplay Caller Display
     *
     * @return array of call display flag
     */
    public function valCallerDisplay($callerDisplay)
    {
        return array('callerDisplay' => !empty($callerDisplay));
    }

    /**
     * Wrapper function used for unit testing
     *
     * @param $serviceId               int     Service ID of customer
     * @param $callPlanPricePointPairs array   Array of product offering : price point pairs
     *
     * @return array An array of base prices keyed by the product offering id/price point id combination
     */
    public function getBasePrices($serviceId, $callPlanPricePointPairs)
    {
        return BasePriceHelper::getBasePrices($serviceId, $callPlanPricePointPairs);
    }

    /**
     * Wrapper function used for unit testing
     *
     * @param Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair $productOfferingPricePointPair
     *
     * @return string
     */
    public function generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair)
    {
        return ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);
    }

    /**
     * @param AccountChange_Product_WlrProductFilter $filter  filter
     * @param array                                  $product product
     * @return bool
     */
    private function isBglProduct($filter, $product)
    {
        return AccountChange_Product_WlrProductFilter::FAMILY_BGL ==
            $filter->getProductFamily($product['intServiceComponentID']);
    }
}
