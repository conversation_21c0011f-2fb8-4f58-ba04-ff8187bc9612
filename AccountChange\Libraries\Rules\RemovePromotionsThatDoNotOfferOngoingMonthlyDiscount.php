<?php
require_once(__DIR__.'/PromotionRule.php');
require_once(__DIR__.'/PromotionRuleBase.php');


use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\C2mApiClient\Entity\PaymentFrequency;
use Plusnet\C2mApiClient\Entity\DiscountValue;
use Plusnet\C2mApiClient\Entity\DiscountType;

class RemovePromotionsThatDoNotOfferOngoingMonthlyDiscount extends PromotionRuleBase implements PromotionRule
{
    /**
     * @param array<Promotion> $promotions
     *
     * @return array
     */
    public function handle(array $promotions)
    {
        return array_filter(
          $promotions,
          [$this, 'doesPromotionHaveAMonthlyOnGoingDiscount']
        );
    }

    /**
     * Now that we can have promotions such as cash back, this rule should only be removing ongoing
     * discounts that aren't monthly.
     *
     *
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     */
    private function doesPromotionHaveAMonthlyOnGoingDiscount(Promotion $promotion)
    {
        foreach ($promotion->getDiscounts() as $discount) {

            $discountValue = $discount->getDiscountValues();

            // If we have an on_going discount as part of the promotion with a monthly
            if (
                !empty($discountValue[0]) &&
                $discount->getType() === DiscountType::ON_GOING &&
                $discountValue[0]->getDurationType() === "MONTHS"
            ) {
                return true;
            }

        }
        return false;

    }
}
