<?php

class AccountChange_Action_CallerDisplay_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Make sure the registry is cleared between tests
     *
     * @return void
     */
    protected function setUp()
    {
        AccountChange_Registry::instance()->reset();
        // Make sure exceptions are re-thrown
        $registry = AccountChange_Registry::instance();
        //$registry->setEntry('disableContractsErrorHandle', true);
        //
    }

    protected function tearDown()
    {
        AccountChange_Registry::instance()->reset();
    }


    public function testExecuteDoesNotDoAnythingIfAccountDoesNotHaveExistingPhone()
    {
        $serviceId = 1234;

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('hasWlr', 'getCallFeatureApi', 'hasCallerDisplay'),
            array($serviceId, array())
        );

        $action
            ->expects($this->once())
            ->method('hasWlr')
            ->will($this->returnValue(false));

        $action
            ->expects($this->never())
            ->method('getCallFeatureApi');

        $action
            ->expects($this->never())
            ->method('hasCallerDisplay');

        $action->execute();
    }

    public function testExecuteDoesNotDoAnythingIfCallerDisplayIsNotSetInRegistry()
    {
        $serviceId = 1234;

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('getCallFeatureApi', 'hasCallerDisplay', 'hasCallPlanAlreadyAndCallPlanIsNotChanging'),
            array($serviceId, array())
        );

        $action
            ->expects($this->once())
            ->method('hasCallPlanAlreadyAndCallPlanIsNotChanging')
            ->will($this->returnValue(true));

        $action
            ->expects($this->never())
            ->method('getCallFeatureApi');

        $action
            ->expects($this->never())
            ->method('hasCallerDisplay');

        $action->execute();
    }


    public function testExecuteDoesNotDoAnythingIfBolHousemoveIsSetInRegistry()
    {
        $serviceId = 1234;

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('getCallFeatureApi', 'hasCallerDisplay', 'hasCallPlanAlreadyAndCallPlanIsNotChanging', 'callerDisplayOptionSetInRegistry'),
            array($serviceId, array())
        );

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('bolHousemove', true);


        $action
            ->expects($this->once())
            ->method('hasCallPlanAlreadyAndCallPlanIsNotChanging')
            ->will($this->returnValue(true));

        $action
            ->expects($this->once())
            ->method('callerDisplayOptionSetInRegistry')
            ->will($this->returnValue(true));

        $action
            ->expects($this->never())
            ->method('getCallFeatureApi');

        $action
            ->expects($this->never())
            ->method('hasCallerDisplay');

        $action->execute();
    }


    public function testExecuteDoesNotDoAnythingIfAccountDoesHaveExistingPhoneButIsChangingCallPlan()
    {
        $serviceId = 1234;

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('hasWlr', 'getCallFeatureApi', 'hasCallerDisplay'),
            array($serviceId, array())
        );

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('newWlrServiceComponentId', 123);
        $registry->setEntry('oldWlrServiceComponentId', 456);

        $action
            ->expects($this->once())
            ->method('hasWlr')
            ->will($this->returnValue(true));

        $action
            ->expects($this->never())
            ->method('getCallFeatureApi');

        $action
            ->expects($this->never())
            ->method('hasCallerDisplay');

        $action->execute();
    }

    public function testExecuteDoesNotDoAnythingIfAccountDoesNotHaveExistingPhoneAndIsChangingCallPlan()
    {
        $serviceId = 1234;

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('hasWlr', 'getCallFeatureApi', 'hasCallerDisplay'),
            array($serviceId, array())
        );

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('newWlrServiceComponentId', 123);
        $registry->setEntry('oldWlrServiceComponentId', 456);

        $action
            ->expects($this->once())
            ->method('hasWlr')
            ->will($this->returnValue(false));

        $action
            ->expects($this->never())
            ->method('getCallFeatureApi');

        $action
            ->expects($this->never())
            ->method('hasCallerDisplay');

        $action->execute();
    }

    public function testExecuteDoesNotOrderIfCustomerHasCallerDisplayAndStillWantsIt()
    {
        $serviceId = 1234;

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('hasWlr', 'getCallFeatureApi', 'hasCallerDisplay'),
            array($serviceId, array())
        );

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('newWlrServiceComponentId', 123);
        $registry->setEntry('oldWlrServiceComponentId', 123);
        $registry->setEntry('callerDisplay', true);

        $action
            ->expects($this->once())
            ->method('hasWlr')
            ->will($this->returnValue(true));

        $action
            ->expects($this->never())
            ->method('getCallFeatureApi');

        $action
            ->expects($this->once())
            ->method('hasCallerDisplay')
            ->will($this->returnValue(true));

        $action->execute();
    }

    public function testExecuteRemovesAndProvisionsCallerDisplayIfCustomerHasItAndDoesNotWantIt()
    {
        $serviceId = 1234;

        $mockCallFeatureApi = $this->getMock(
            'AccountChange_CallFeature_Api',
            array('removeCallFeature'),
            array()
        );

        $mockCallFeatureApi
            ->expects($this->once())
            ->method('removeCallFeature');

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('hasWlr', 'getCallFeatureApi', 'hasCallerDisplay'),
            array($serviceId, array())
        );

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('newWlrServiceComponentId', 123);
        $registry->setEntry('oldWlrServiceComponentId', 123);
        $registry->setEntry('callerDisplay', false);

        $action
            ->expects($this->once())
            ->method('hasWlr')
            ->will($this->returnValue(true));

        $action
            ->expects($this->once())
            ->method('getCallFeatureApi')
            ->will($this->returnValue($mockCallFeatureApi));

        $action
            ->expects($this->once())
            ->method('hasCallerDisplay')
            ->will($this->returnValue(true));

        $action->execute();
    }

    public function testExecuteAddsAndProvisionsCallerDisplayIfCustomerHasItAndDoesWantIt()
    {
        $serviceId = 1234;

        $mockCallFeatureApi = $this->getMock(
            'AccountChange_CallFeature_Api',
            array('addCallFeature'),
            array()
        );

        $mockCallFeatureApi
            ->expects($this->once())
            ->method('addCallFeature');

        $action = $this->getMock(
            'AccountChange_Action_CallerDisplay',
            array('hasWlr', 'getCallFeatureApi', 'hasCallerDisplay'),
            array($serviceId, array())
        );

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('newWlrServiceComponentId', 123);
        $registry->setEntry('oldWlrServiceComponentId', 123);
        $registry->setEntry('callerDisplay', true);

        $action
            ->expects($this->once())
            ->method('hasWlr')
            ->will($this->returnValue(true));

        $action
            ->expects($this->once())
            ->method('getCallFeatureApi')
            ->will($this->returnValue($mockCallFeatureApi));

        $action
            ->expects($this->once())
            ->method('hasCallerDisplay')
            ->will($this->returnValue(false));

        $action->execute();
    }
}

