<?php

use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;

class AccountChange_EmailHandler_PriceHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that prices are retrieved from BillingAPI to be displayed in the confirmation email
     *
     * @covers AccountChange_EmailHandler_PriceHelper::getNewPackagePrices()
     */
    public function testThatPricesAreRetrievedCorrectlyFromBillingAPI()
    {
        $priceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array(
                'getServiceComponentIdFromServiceDefinitionIdAndMarketId',
                'getTariffIdFromMarketIdAndServiceDefinitionId',
                'getBasePrices',
                'getProductComponentTariffIdForWlrServiceComponent',
                'getCallPlanDetailsByWlrServiceComponentId',
                'createComponentInstance',
                'createProductComponentInstance',
                'getNoBundleableCallFeatures',
                'getPromotionHelper'
            ),
            array(1, 2, 3, 4, 'promoCode', 5)
        );

        $priceHelper
            ->expects($this->once())
            ->method('getServiceComponentIdFromServiceDefinitionIdAndMarketId')
            ->willReturn('123');

        $priceHelper
            ->expects($this->once())
            ->method('getTariffIdFromMarketIdAndServiceDefinitionId')
            ->willReturn('456');

        $priceHelper
            ->expects($this->once())
            ->method('getProductComponentTariffIdForWlrServiceComponent')
            ->willReturn('222');

        $priceHelper
            ->expects($this->once())
            ->method('getCallPlanDetailsByWlrServiceComponentId')
            ->willReturn(
                array(
                    'intServiceComponentID' => '888',
                    'intTariffID'           => '999'
                )
            );

        $wlrComponentInstance = $this->getMock(
            'CComponent',
            array(
                'getActiveCallFeaturesBundle',
                'getSelectedProductComponentInstanceIDs',
                'getComponentTypeID')
        );

        $callFeatureBundle = $this->getMock(
            'CWlrCallFeaturesBundle',
            array(
                'getServiceComponentID',
                'getTariffID',
            )
        );

        $callFeatureBundle
            ->expects($this->once())
            ->method('getServiceComponentID')
            ->willReturn('444');

        $callFeatureBundle
            ->expects($this->once())
            ->method('getTariffID')
            ->willReturn('555');

        $wlrComponentInstance
            ->expects($this->once())
            ->method('getActiveCallFeaturesBundle')
            ->willReturn($callFeatureBundle);

        $wlrComponentInstance
            ->expects($this->once())
            ->method('getSelectedProductComponentInstanceIDs')
            ->willReturn(array(1));

        $wlrComponentInstance
            ->expects($this->once())
            ->method('getComponentTypeID')
            ->willReturn(1);

        $priceHelper
            ->expects($this->once())
            ->method('createComponentInstance')
            ->willReturn($wlrComponentInstance);

        $individualCallFeatureInstance = $this->getMock(
            'CProductComponent',
            array(
                'getServiceComponentID',
                'getTariffID',
            )
        );

        $individualCallFeatureInstance
            ->expects($this->once())
            ->method('getServiceComponentID')
            ->willReturn('666');

        $individualCallFeatureInstance
            ->expects($this->once())
            ->method('getTariffID')
            ->willReturn('777');

        $priceHelper
            ->expects($this->once())
            ->method('createProductComponentInstance')
            ->willReturn($individualCallFeatureInstance);

        $promotionHelper = $this->getMock(
            'AccountChange_EmailHandler_PromotionHelper',
            array('getDiscountData'),
            array(null, null, null, null)
        );

        $promotionHelper
            ->expects($this->once())
            ->method('getDiscountData')
            ->willReturn(
                array(
                    'discountAmount'   => '10',
                    'discountDuration' => '18'
                )
            );

        $priceHelper
            ->expects($this->once())
            ->method('getPromotionHelper')
            ->willReturn($promotionHelper);

        $priceHelper
            ->expects($this->any())
            ->method('getBasePrices')
            ->withConsecutive(
                [1, array(new ProductOfferingPricePointPair('123', '456'))],
                [1, array(new ProductOfferingPricePointPair('4', '222'))],
                [1, array(new ProductOfferingPricePointPair('888', '999'))],
                [1, array(
                    new ProductOfferingPricePointPair('444', '555'),
                    new ProductOfferingPricePointPair('666', '777'))])
            ->willReturnOnConsecutiveCalls(
                array(
                    '123:456' => array(
                        'currentBasePrice' => 10
                    )
                ),
                array(
                    '4:222' => array(
                        'currentBasePrice' => 20
                    )
                ),
                array(
                    '888:999' => array(
                        'currentBasePrice' => 30
                    )
                ),
                array(
                    '444:555' => array(
                        'currentBasePrice' => 40
                    ),
                    '666:777' => array(
                        'currentBasePrice' => 50
                    )
                )
            );

        $expected = array(
            'broadband'        => 10,
            'lineRental'       => 20,
            'callPlan'         => 30,
            'callFeatures'     => 90,
            'total'            => 150,
            'discountDuration' => '18',
            'discountAmount'   => '10'
        );

        $actual = $priceHelper->getNewPackagePrices();

        $this->assertEquals($expected, $actual);
    }
}