<?php

class AccountChange_AccountChangeOrderContract_Test extends PHPUnit_Framework_TestCase
{
    const CONTRACT_TYPE = 'A contract type';
    const CONTRACT_SUB_TYPE = 'A contract sub type';
    const AGREEMENT_DATE = 'Agreement date';
    const CONTRACT_LENGTH = 12;

    public function testContractObjectGetsAndSetsData()
    {
        $contractObject = new AccountChange_AccountChangeOrderContract();
        $contractObject->setAgreementDate(static::AGREEMENT_DATE);
        $contractObject->setType(static::CONTRACT_TYPE);
        $contractObject->setSubType(static::CONTRACT_SUB_TYPE);
        $contractObject->setLength(static::CONTRACT_LENGTH);

        $this->assertEquals($contractObject->getAgreementDate(), static::AGREEMENT_DATE);
        $this->assertEquals($contractObject->getType(), static::CONTRACT_TYPE);
        $this->assertEquals($contractObject->getSubType(), static::CONTRACT_SUB_TYPE);
        $this->assertEquals($contractObject->getLength(), static::CONTRACT_LENGTH);
    }
}
