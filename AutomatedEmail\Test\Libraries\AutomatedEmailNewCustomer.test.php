<?php
/**
 * AutomatedEmailNewCustomerTest class
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
class AutomatedEmailNewCustomerTest extends \PHPUnit_Framework_TestCase
{
    /**
     * Share Path
     */
    const SHARE_PATH  = 'PLUSNET/WORKPLACE_ISP_ADMIN/WORKPLACE_ROOT/PLUSNET_PARTNER_ROOT/';

    /**
     * Email
     *
     * @var  object
     */
    private $email;

    /**
     * File Template Path
     *
     * @var string
     */
    private $path;

    /**
     * Setup before each test
     *
     * @return void
     */
    public function setUp()
    {
        $dir = dirname(__FILE__);

        if (!file_exists($dir . '/testData/compile')) {
            mkdir($dir . '/testData/compile');
        }

        $this->email  = new Email_Email();

        AutomatedEmail_TemplateConfig::setCompilePath(new String($dir . '/testData/compile'));
        AutomatedEmail_TemplateConfig::setContentBasePath(new String($dir . '/testData/template'));
        $strContentBaseDir    = AutomatedEmail_TemplateConfig::getContentBasePath()->getValue();
        $this->path = $strContentBaseDir.'/'.self::SHARE_PATH;
    }

    /**
     * Reset after each test.
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::restoreAdaptor('Auth', Db_Manager::DEFAULT_TRANSACTION);

        AutomatedEmail_TemplateConfig::reset();
    }

    /**
     * Test for Automated Email With no Temapletes
     *
     * @return void
     */
    public function testPrepareAutomatedEmailNoTemplates()
    {
        $automatedEmail = new AutomatedEmail_AutomatedEmailNewCustomer($this->path);

        $this->setExpectedException(
            'AutomatedEmail_Exception',
            'Unable to locate email templates for handle \'NoTemplates\' within the signup agent working from home context',
            AutomatedEmail_Exception::NO_TEMPLATE_FOR_HANDLE
        );
        $automatedEmail->prepareAutomatedEmail('NoTemplates');
    }

    /**
     * Test for Prepare method for Sending Email
     *
     * @covers AutomatedEmail_AutomatedEmailNewCustomer::prepareAutomatedEmail
     * @covers AutomatedEmail_AutomatedEmailNewCustomer::prepareStandardAutomatedEmail
     * @covers AutomatedEmail_AutomatedEmailNewCustomer::getTextBody
     * @covers AutomatedEmail_AutomatedEmailNewCustomer::getHtmlBody
     * @covers AutomatedEmail_AutomatedEmailNewCustomer::getSubject
     *
     * @return void
     */
    public function testPrepareAutomatedEmail()
    {
        $automatedEmail = new AutomatedEmail_AutomatedEmailNewCustomer($this->path);

        $strTestText = 'Test Email

---
Internal Reference: Test';

        $strTestHtml = '<html>
  <head>
  </head>
  <body>
    <h1>Test Html Email</h1>
    This is a test HTML Email.
  </body>
</html>';

        $this->assertThat(
            $automatedEmail->prepareAutomatedEmail('testTemplateOne', array('strEmailReference' => 'Test')),
            $this->isInstanceOf('Email_Email')
        );
        $this->assertEquals('Test Email', $automatedEmail->getSubject());
        $this->assertEquals($strTestText, trim($automatedEmail->getTextBody()));
        $this->assertEquals($strTestHtml, trim($automatedEmail->getHtmlBody()));
    }

     /**
     * Prepare method for Sending Email with No Subject
     *
     * @covers AutomatedEmail_AutomatedEmail::prepareAutomatedEmail
     * @covers AutomatedEmail_AutomatedEmail::prepareStandardAutomatedEmail
     *
     * @return void
     */
    public function testPrepareAutomatedEmailNoSubject()
    {
        $strTestText = 'Test Email

---
Internal Reference: Test';

        $objAutomatedEmail = new AutomatedEmail_AutomatedEmailNewCustomer($this->path);
        $this->assertThat(
            $objAutomatedEmail->prepareAutomatedEmail('testTemplateNoSubject', array('strEmailReference' => 'Test')),
            $this->isInstanceOf('Email_Email')
        );
        $this->assertEquals('Link for the Order Payment', $objAutomatedEmail->getSubject());
        $this->assertEquals($strTestText, trim($objAutomatedEmail->getTextBody()));
    }


    /**
     * Test for Prepare Email with invalid Extension
     *
     * @return void
     */
    public function testPrepareAutomatedEmailInvalidExtension()
    {
        $automatedEmail = new AutomatedEmail_AutomatedEmailNewCustomer($this->path);

        $this->setExpectedException(
            'AutomatedEmail_Exception',
            'Templates located with the context do not appear to be valid email templates',
            AutomatedEmail_Exception::INVALID_TEMPLATE_FOR_HANDLE
        );
        $automatedEmail->prepareAutomatedEmail('invalidExtension');
    }
}
