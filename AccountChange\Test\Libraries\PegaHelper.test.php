<?php
/**
 * AccountChange Scheduling Helper test
 *
 * Test class for AccountChange_PegaHelper
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2021 Plusnet
 */
class AccountChange_PegaHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Tear down functionality
     *
     * @return void
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     */
    public function tearDown()
    {
        $registry = AccountChange_Registry::instance();
        $registry->reset();
    }

    /**
     * Tests that register interaction calls the next best action client in the expected way
     */
    public function testRegisterInteractionCallsNextBestActionWithCorrectParameters()
    {
        $impressionOfferId = 123;
        $interaction = 'Rejected';
        $actorId = 434;
        $externalCustomer = true;

        $mockNextBestAction = $this->getMockBuilder('Plusnet\NextBestActionServiceClient\V1\Client')
            ->disableOriginalConstructor()
            ->setMethods(array('registerImpressionInteraction'))
            ->getMock();

        $mockNextBestAction
            ->expects($this->once())
            ->method('registerImpressionInteraction')
            ->with($impressionOfferId, $interaction, $actorId, $externalCustomer);


        $mockHelper = $this->getMock(
            'AccountChange_PegaHelper',
            array('getNextBestActionClient'),
            array()
        );

        $mockHelper
            ->expects($this->once())
            ->method('getNextBestActionClient')
            ->will($this->returnValue($mockNextBestAction));

        $mockHelper->registerInteraction($impressionOfferId, $interaction, $actorId, $externalCustomer);
    }

    /**
     * Tests that register interaction logs an error and continues when an exception is
     * thrown by the client
     */
    public function testRegisterInteractionLogsErrorWhenClientThrowsAnException()
    {
        $impressionOfferId = 123;
        $interaction = 'Rejected';
        $actorId = 434;
        $externalCustomer = true;

        $expectedLogMessage  = "[AccountChange][PEGA] There was a problem registering a PEGA Interaction (impressionOfferId = $impressionOfferId, ";
        $expectedLogMessage .= "interaction = $interaction, actorId = $actorId) Error: Exception message";

        $mockNextBestAction = $this->getMockBuilder('Plusnet\NextBestActionServiceClient\V1\Client')
            ->disableOriginalConstructor()
            ->setMethods(array('registerImpressionInteraction'))
            ->getMock();

        $mockNextBestAction
            ->expects($this->once())
            ->method('registerImpressionInteraction')
            ->with($impressionOfferId, $interaction, $actorId, $externalCustomer)
            ->will($this->throwException(new Exception('Exception message')));

        $mockHelper = $this->getMock(
            'AccountChange_PegaHelper',
            array('getNextBestActionClient', 'logError'),
            array()
        );

        $mockHelper
            ->expects($this->once())
            ->method('getNextBestActionClient')
            ->will($this->returnValue($mockNextBestAction));

        $mockHelper
            ->expects($this->once())
            ->method('logError')
            ->with($expectedLogMessage);

        $mockHelper->registerInteraction($impressionOfferId, $interaction, $actorId, $externalCustomer);
    }
}

