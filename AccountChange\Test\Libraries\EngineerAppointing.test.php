<?php
/**
 * AccountChange_EngineerAppointing
 *
 * @package    AccountChange
 * @subpackage test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @since      File available since 2011-05-31
 */
/**
 * AccountChange_EngineerAppointing Test Class
 *
 * @package    AccountChange
 * @subpackage test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_EngineerAppointing_Test extends PHPUnit_Framework_TestCase
{
   /**
     * Test that the decoration of appointment data happens correctly
     *
     * @covers       AccountChange_EngineerAppointing::decorateAppointments
     * @dataProvider appointmentProvider
     *
     * @param array $input
     * @param array $expected
     */
    public function testDecorateAppointments($input, $expected)
    {
        $helper = new AccountChange_EngineerAppointing();
        $result = $helper->decorateAppointments($input);

        $this->assertEquals($expected, $result);
    }

    /**
     * Provider for testDecorateAppointments
     *
     * @return array
     */
    public function appointmentProvider()
    {
        return array(
            array(
                array(
                    '31/05/2011' => array('AM', 'PM'),
                    '01/06/2011' => array('AM'),
                    '02/06/2011' => array('1400', 'PM'),
                    '04/06/2011' => array('AM', '1800'),
                    '05/06/2011' => array('AM', 'PM', '1000', '2000'),
                ),
                array(
                    '01/06/2011' => array(
                        'AM' => 'AM',
                        'PM' => false,
                    ),
                    '02/06/2011' => array(
                            'AM' => false,
                            'PM' => 1400,
                    ),
                    '04/06/2011' => array(
                            'AM' => 'AM',
                            'PM' => 1800,
                    ),
                    '05/06/2011' => array(
                            'AM' => 1000,
                            'PM' => 2000,
                    ),
                    '31/05/2011' => array(
                            'AM' => 'AM',
                            'PM' => 'PM',
                    ),
                ),
            ),
        );
    }

    /**
     * @covers       AccountChange_EngineerAppointing::getLiveAppointments
     * @dataProvider provideDataForLiveAppointments
     *
     * @param array  $vars
     * @param string $service
     * @param array  $data
     * @param array  $appointments
     * @param array  $expected
     *
     * @return void
     */
    public function testGetLiveAppointmentsCallsEngineerAppointingClientAndReturnsResults(
        $vars, $service, $data, $appointments, $expected
    )
    {
        $helper = $this->getMock('AccountChange_EngineerAppointing', array('getEngineerAppointingClient'));
        $client = $this->getMockBuilder('EngineerAppointmentClient_Client')
            ->setMethods(array('getAppointments'))
            ->disableOriginalConstructor()
            ->getMock();
        $helper->expects($this->once())
               ->method('getEngineerAppointingClient')
               ->with($service, $data)
               ->will($this->returnValue($client));

        $client->expects($this->once())
               ->method('getAppointments')
               ->will($this->returnValue($appointments));

        $result = $helper->getLiveAppointments($vars);
        $this->assertEquals($expected, $result);
    }

    public function provideDataForLiveAppointments()
    {
        return array(
            //Data set 0
            array(
                array(
                    'addressRef'      => 'A12345678901',
                    'addressCategory' => 'GOLD',
                    'databaseCode'    => 'SL',
                    'journeyType'     => 'A',
                    'sparePairs'      => '60',
                    'cli'             => '01142000000',
                    'extensionKitId'  => '1',
                    'locationType'    => 'RESIDENTIAL',
                    'service'          => EngineerAppointmentClient_Service::FTTC,
                    'serviceHandle'   => EngineerAppointmentClient_Service::FTTC_HANDLE,
                ),
                EngineerAppointmentClient_Service::FTTC,
                array(
                    'addressRef'      => 'A12345678901',
                    'addressCategory' => 'GOLD',
                    'cssDatabaseCode' => 'SL',
                    'journeyType'     => 'A',
                    'noSparePairs'    => 60,
                    'cli'             => '01142000000',
                    'extensionKitId'  => 1,
                    'locationType'    => 'RESIDENTIAL'
                ),
                array(
                    'FTTC' => array(
                        '12/07/2011' => array(
                          0 => '1000',
                          1 => '2000',
                        ),
                        '13/07/2011' => array(
                          0 => 'AM',
                          1 => '1200',
                        ),
                        '14/07/2011' => array(
                          0 => '1600',
                          1 => 'AM',
                        ),
                    ),
                ),
                array(
                    'appointments' => array(
                        '12/07/2011' => array(
                          0 => '1000',
                          1 => '2000',
                        ),
                        '13/07/2011' => array(
                          0 => 'AM',
                          1 => '1200',
                        ),
                        '14/07/2011' => array(
                          0 => '1600',
                          1 => 'AM',
                        ),
                    ),
                   'offsetDate' => '14/07/2011',
                )
            )
        );
    }
}