<?php
/**
 * Account Change Mode
 *
 * @package    AccountChange
 * @subpackage Application
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 Plusnet
 * @since      File available since 2010-05-27
 */
/**
 * AccountChange_Mode
 *
 * This class represents what mode the account change application can be run in
 *
 * @package    AccountChange
 * @subpackage Application
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 Plusnet
 */
class AccountChange_Mode
{
    /**
     * The modes the account change can run in
     *
     * Note: CHANGE_BROADBAND_ONLY is implemented to remove the select call plan step requirement in the account change journey.
     *       This is not a specific Solus mode, it can be used on dual play. Implemented in DLIFE-118.
     *
     * @var string
     */
    const WLR_ONLY = 'wlr-only';
    const ADSL_ONLY = 'adsl-only'; // Not implemented
    const CHANGE_BROADBAND_ONLY = 'broadband-only'; // Change broadband product only mode.
    const BOTH = 'both'; // Usual mode
    const NONE = 'none'; // Nothing seems to be right

    /**
     * Factory Instsance, represents overview of account
     *
     * @var AccountChange_Account
     */
    private static $_instance;

    /**
     * Change mode
     *
     * @var string
     */
    private $_changeMode;

    /**
     * Business Actor Id
     *
     * @var Auth_BusinessActor
     */
    protected $_actor;

    /**
     * Service Id
     *
     * @var int
     */
    protected $_serviceId;

    /**
     * Constructor
     *
     * This is a pseudo singleton object, which isn't great.
     * I've left the constructor public so we can mock off
     * the object in the Controller unit tests
     *
     * @param Auth_BusinessActor $actor The business actor
     *
     * @return AccountChange_ValidationChecks
     */
    public function __construct(Auth_BusinessActor $actor)
    {
        $this->_actor = $actor;
        $this->_serviceId = $actor->getExternalUserId();
    }

    /**
     * Factory instance getter
     *
     * @param Auth_BusinessActor $actor Business Actor of account
     *
     * @return AccountChange_Mode
     */
    public static function instance(Auth_BusinessActor $actor)
    {
        if (!isset(self::$_instance)) {

            self::$_instance = new AccountChange_Mode($actor);
        }

        return self::$_instance;
    }

    /**
     * Setter for the factory
     *
     * Set a bespoke mode instance
     *
     * @param AccountChange_Mode $instance The mode instance
     *
     * @return void
     */
    public static function setInstance(AccountChange_Mode $instance)
    {
        self::$_instance = $instance;
    }

    /**
     * Decide if we should be running in the wlr only mode
     *
     * @return string
     */
    public function getChangeMode()
    {
        if (!$this->_changeMode) {

            $adslOk = $this->isAdslActive();
            $wlrOk = $this->isWlrChangeAllowed();

            $johnLewisPhoneOnly = $this->isOnNonAdslJohnLewisProduct();

            if ((!$adslOk && $wlrOk) || $johnLewisPhoneOnly) {
                $this->setChangeMode(self::WLR_ONLY);          // Adsl is not ok but wlr is = WLR ONLY
            } elseif ($adslOk && !$wlrOk) {
                $this->setChangeMode(self::ADSL_ONLY);         // Adsl ok but wlr isn't = ADSL ONLY
            } elseif (!$adslOk && !$wlrOk) {
                $this->setChangeMode(self::NONE);              // Adsl not ok nor is wlr = NONE
            } else {
                $this->setChangeMode(self::BOTH);              // Everything is OK = BOTH
            }
        }

        return $this->_changeMode;
    }

    /**
     * Set the changeMode for the journey, such as for WLR only journey
     *
     * @param string
     *
     * @return void
     */
    public function setChangeMode($changeMode)
    {
        $this->_changeMode = $changeMode;
    }

    /**
     * Wrapper for retrieving the adsl install diary status
     *
     * @return boolean
     */
    public function isAdslActive()
    {
        $diaryStatus = $this->getInstallDiaryStatus();

        if ('active' != $diaryStatus) {
            return $this->isAdslSubscriptionComponentActive();
        } else {
            return true;
        }
    }

    /**
     * Check to see if the customer is on a non adsl product for john lewis
     * therefore force wlr only mode
     *
     * @return boolean
     */
    public function isOnNonAdslJohnLewisProduct()
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $product = $db->getServiceDefinitionDetailsForService($this->_serviceId);

        if ($product['name'] == 'Basic' && $product['isp'] == 'johnlewis') {
            return true;
        }

        return false;
    }

    /**
     * Wrapper to retrieve the InstallDiary status
     *
     * @return String
     */
    protected function getInstallDiaryStatus()
    {
        try {
            $diary = new InstallDiary_InstallDiary($this->_actor);
            return $diary->getStatus();
        } catch (Exception $e) {
            // If they do not have an install diary, then we can only
            // return false.. It may mean they are gen services!
            return false;
        }
    }

    /**
     * Wrapper for checking if the customer can change/add wlr
     *
     * @return boolean
     */
    public function isWlrChangeAllowed()
    {
        $account = AccountChange_Account::instance(new Int($this->_serviceId));
        $wlrInfo = $account->getWlrInformation();

        if ($wlrInfo['bolWlrChangeAllowed'] || $wlrInfo['bolWlrAddAllowed']) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Check to see if the internetn connection subscription component
     * is active if it is that kind of product
     *
     * @return boolean
     */
    protected function isAdslSubscriptionComponentActive()
    {
        $valid = true;
        $this->includeLegacyFiles();

        $connection = CInternetConnectionProduct::getInternetConnectionProductFromServiceId($this->_serviceId);
        if ($connection) {
            $subscription = CProductComponent::createInstanceFromComponentID(
                $connection->getComponentID(),
                'SUBSCRIPTION',
                array('ACTIVE')
            );

            if ($subscription) {
                if ('ACTIVE' != $subscription->getStatus()) {
                    $valid = false;
                }
            }
        }

        // Be lenient here in the sense that we really need to rely on
        // Install Diary, but if that isn't correct we need to check
        // The internet subscription component.
        // If that doesn't exist it is a legacy product (assumption)
        // and therefore with regards to wlr first, we can let them
        // change their account
        return $valid;
    }

    /**
     * Wrapper for legacy includes
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProduct.inc';
    }
}
