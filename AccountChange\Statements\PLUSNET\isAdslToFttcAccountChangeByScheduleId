server: coredb
role: slave
rows: single
statement:

SELECT EXISTS(
  SELECT cs.service_id
  FROM userdata.service_change_schedule cs
  JOIN products.adsl_product apOld
    ON cs.old_type = apOld.service_definition_id
  JOIN products.adsl_product apNew
    ON cs.new_type = apNew.service_definition_id
  JOIN products.tblProvisioningProfile ppOld
    ON ppOld.intProvisioningProfileID = apOld.intProvisioningProfileID
  JOIN products.tblProvisioningProfile ppNew
    ON ppNew.intProvisioningProfileID = apNew.intProvisioningProfileID
  JOIN userdata.tblProvisionedService new_ps
    ON new_ps.intServiceID = cs.service_id
  JOIN products.tblSupplierProduct new_sp
    ON new_sp.intSupplierProductID = new_ps.intSupplierProductID
  JOIN userdata.tblProvisionedService old_ps
    ON old_ps.intServiceID = cs.service_id
  JOIN products.tblSupplierProduct old_sp
    ON old_sp.intSupplierProductID = old_ps.intSupplierProductID
  WHERE
    ppNew.vchName = 'FTTC' AND
    ppOld.vchName != 'FTTC' AND
    cs.change_complete = 'no' AND
    cs.active = 'yes' AND
    cs.change_date IS NOT NULL AND
    cs.change_date <= date(now()) AND
    new_ps.dtmStart IS NULL AND
    new_ps.dtmEnd IS NULL AND
    new_ps.intProvisionedServiceEventTypeID = 4 AND
    new_sp.vchProductCode = 'FTTC' AND
    old_ps.dtmStart IS NOT NULL AND
    old_ps.dtmEnd IS NULL AND
    old_sp.vchProductCode != 'FTTC' AND
    cs.schedule_id = :scheduleId
);