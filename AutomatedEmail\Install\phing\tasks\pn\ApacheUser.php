<?php
include_once 'phing/Task.php';
class ApacheUser extends Task {

    private $_strApacheConfFile = null;
    private $_strReturnName;
    
    public function setConfFile($strApacheConf) {
        $this->_strApacheConfFile = $strApacheConf;
    }
    
    public function setReturnName($strName) {
        $this->_strReturnName = $strName;
    }
    
    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {

		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}      

		if (!$arrFile = file($this->_strApacheConfFile)) {
			throw new Exception("Cannot load $this->_strApacheConfFile");      	
		}

		$pattern = '/^User\s+(\S+)$/';
		
		foreach ($arrFile as $strLine) {

    	  	if(preg_match($pattern, $strLine, $arrMatches)) {
      			
      			$strUser = $arrMatches[1];
      			$this->project->setProperty($this->_strReturnName, $strUser);
      			return;
    	  	}
		}
    }
}