server: coredb
role: slave
rows: multiple
statement:

SELECT DISTINCT
    scp.intServiceComponentID AS intServiceComponentID,
    sc.name AS ProductName,
    sc.description as strDescription
FROM
    products.service_components sc
INNER JOIN products.tblServiceComponentProduct scp
    ON sc.service_component_id = scp.intServiceComponentID
INNER JOIN products.tblServiceComponentProductType scpt
    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
    AND scpt.vchHandle = :productTypeHandle
INNER JOIN products.tblCustomerSector cs
    ON scp.intCustomerSectorID = cs.intCustomerSectorID
    AND cs.vchHandle = :customerSectorHandle
INNER JOIN products.service_component_config scc
    ON scc.service_component_id = sc.service_component_id
WHERE
    scp.intServiceComponentID NOT IN (:excludedServiceComponentIDs)
    AND (sc.available = 'Yes' OR sc.service_component_id IN (:unavailableServiceComponentList))
    AND scc.service_definition_id = :serviceDefinitionId
    AND dteAvailableFrom <= CURDATE()
    AND (dteAvailableTo IS NULL OR dteAvailableTo >= CURDATE());
