CREATE TABLE `dbSystemEvents`.`tblScheduledEvent` (
  `intScheduledEventID` int(10) unsigned NOT NULL auto_increment,
  `intEventTypeID` int(10) unsigned NOT NULL default '0',
  `dtmCreated` datetime NOT NULL default '0000-00-00 00:00:00',
  `dteDue` date NOT NULL default '0000-00-00',
  `bolOnBilling` tinyint(1) NOT NULL default '1',
  `bolOnContractEnd` tinyint(1) NOT NULL default '1',
  `dtmCompleted` datetime default NULL,
  `dtmCancelled` datetime default NULL,
  `stmLastUpdate` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  `bolAdjustDate` tinyint(1) NOT NULL default '0',
  PRIMARY KEY  (`intScheduledEventID`),
  KEY `idxEventTypeID` (`intEventTypeID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1