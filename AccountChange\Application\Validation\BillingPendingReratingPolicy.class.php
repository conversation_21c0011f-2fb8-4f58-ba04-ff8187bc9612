<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\BillingApiClient\Exceptions\BillingApiClientQueueException;
use Plusnet\BillingApiClient\Service\ServiceManager;

class AccountChange_BillingPendingReratingPolicy extends AccountChange_AbstractValidationPolicy
{
    const FAILURE_REASON = 'There is already a backdated product change in progress.';
    const RBM_FAIL_REASON = 'Error occurred during RBM call to determine pending rating requests';
    const ERROR_CODE = 'ERROR_BACKDATED_CHANGE';

    /**
     * @var string
     */
    private $failureReason;

    /**
     * @return bool
     */
    public function validate()
    {
        try {
            $billingApiFacade = ServiceManager::getService('BillingApiFacade');
            $hasPendingReratingRequests = $billingApiFacade->hasPendingReratingRequests(
                $this->actor->getExternalUserId()
            );

            if (isset($hasPendingReratingRequests) && is_array($hasPendingReratingRequests)) {
                if ($hasPendingReratingRequests['requestCount'] != 0) {
                    $this->failureReason = static::FAILURE_REASON;
                }
            }
        } catch (BillingApiClientQueueException $e) {
             $this->failureReason = static::RBM_FAIL_REASON;
        } catch (BillingApiClientBillingServiceException $e) {
            $this->failureReason = static::RBM_FAIL_REASON;
        }

        return empty($this->failureReason);
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return $this->failureReason;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return static::ERROR_CODE;
    }
}
