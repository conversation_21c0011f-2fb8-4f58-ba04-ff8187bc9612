CREATE TABLE products.service_components (
  service_component_id int(8) unsigned zerofill NOT NULL auto_increment,
  name varchar(50) NOT NULL default '',
  description varchar(50) NOT NULL default '',
  available enum('Yes','No') NOT NULL default 'Yes',
  date_created date NOT NULL default '0000-00-00',
  dteAvailableFrom date NOT NULL default '0000-00-00',
  dteAvailableTo date default NULL,
  bolSignupBoltOn tinyint(4) NOT NULL default '0',
  bolInLifeBoltOn tinyint(4) NOT NULL default '0',
  bolScheduledCancellation tinyint(4) NOT NULL default '0',
  db_src varchar(4) NOT NULL default '',
  time_stamp timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  isp varchar(10) default NULL,
  PRIMARY KEY  (service_component_id),
  KEY name (name),
  KEY idxAvailableFrom (dteAvailableFrom)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 PACK_KEYS=1