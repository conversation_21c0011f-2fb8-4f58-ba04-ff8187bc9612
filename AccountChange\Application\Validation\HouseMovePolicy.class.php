<?php

/**
 * <AUTHOR>
 */

use \Plusnet\HouseMoves\Services\ServiceManager;

class AccountChange_HouseMovePolicy extends AccountChange_AbstractValidationPolicy
{
    const MC_ERROR_MESSAGE =
        'You can\'t change your products for the moment, you already have a product change in progress.';

    const WP_ERROR_MESSAGE =
        'Unable to change product at this time as a House Moves process is in progress. Please advise the customer we
          can action this once the House Move has been completed. Please add a ticket to the account with details of the
         change needed. If a product change is needed before the House Move completes, cancel the House Move and start
          the product change. Add a ticket to re-instate the House Move once the product change has completed.';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_ACCOUNT_CHANGE_IN_PROGRESS';
    
    /**
     * @return bool
     */
    public function validate()
    {
        $houseMoveService = ServiceManager::getService("HouseMoveService");
        return !$houseMoveService->houseMoveIsOpenForUser($this->actor->getExternalUserId());
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return ($this->isWorkplace || $this->actor->getUserType() === static::STAFF_USER_TYPE)
            ? self::WP_ERROR_MESSAGE
            : self::MC_ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
