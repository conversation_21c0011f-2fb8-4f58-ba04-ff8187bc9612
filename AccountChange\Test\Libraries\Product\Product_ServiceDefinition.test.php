<?php
/**
 * Service Definition Product Configuration - file docblock
 *
 * Testing class for the AccountChange_Product_ServiceDefinition class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-08-19
 */

require_once '/local/data/mis/database/database_libraries/product-access.inc';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/database_libraries/financial-access.inc';

use Plusnet\Feature\Test\Helpers\FeatureToggleDbAdaptorTestHelper;

/**
 * Service Definition Product Configuration test Class
 *
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-08-19
 */
class AccountChange_Product_ServiceDefinition_Test extends TestCaseWithProxy
{
    /**
     * Current tariffs being tested
     *
     * @var array
     */
    private $_arrCurrentTariffs;

    /**
     * Current data being tested with
     *
     * @var array
     */
    private $_arrCurrentData;

    /**
     * PHPUnit function
     *
     * All the test needs to mock getServiceDefinitionDao
     *
     * @return void
     */
    protected function setup()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(array('strProductFamily' => 'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Reset registries
        $registry = AccountChange_Registry::instance();
        $registry->reset();
    }

    /**
     * PHPUnit tearDown function
     *
     * @return void
     */
    protected function tearDown()
    {
        Db_Manager::commit();
        Db_Manager::restoreAdaptor('AccountChange');
        Db_Manager::restoreAdaptor('Core');
        Lib_Product::resetInstance();
        Mockery::close();
    }

    /**
     * Test initialise throws exception if serviceDefintionId is non numeric
     *
     * @covers AccountChange_Product_ServiceDefinition::initialise
     *
     * @return void
     */
    public function testInitialiseThrowsExceptionIfServiceDefintionIdIsNonNumeric()
    {
        $this->setExpectedException(
            'AccountChange_Product_ManagerException',
            'Non numeric service definition id',
            AccountChange_Product_ManagerException::ERR_INVALID_COMPONENT_ID_TYPE
        );

        $objServiceDefinition = new AccountChange_Product_ServiceDefinition(
            'strInvalidIntServiceDefintiontId',
            AccountChange_Product_Manager::ACTION_NONE
        );
    }

    /**
     * Test initialise correctly sets up additional options
     *
     * @covers AccountChange_Product_ServiceDefinition::initialise
     *
     * @return void
     */
    public function testInitialiseCorrectlySetsUpAdditionalOptions()
    {
        $bolContractReset = true;
        $bolSchedule = true;
        $bolScheduleDowngrade = true;
        $bolTakePayment = true;
        $strContract = 'Monthly';
        $oldWlrScid = 1;
        $newWlrScid = 2;
        $oldAdslComponentId = 3;
        $defunctAdslComponentIds = array(6, 7);
        $oldWlrComponentId = 4;
        $newAdslScid = 5;

        $arrOptions = array(
            'bolContractReset' => $bolContractReset,
            'bolSchedule' => $bolSchedule,
            'bolScheduleDowngrade' => $bolScheduleDowngrade,
            'bolTakePayment' => $bolTakePayment,
            'strContract' => $strContract,
            'oldWlrScid' => $oldWlrScid,
            'newWlrScid' => $newWlrScid,
            'oldAdslComponentId' => $oldAdslComponentId,
            'defunctAdslComponentIds' => $defunctAdslComponentIds,
            'oldWlrComponentId' => $oldWlrComponentId,
            'newAdslScid' => $newAdslScid
        );

        $objComponent = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE,
            $arrOptions
        );

        $this->assertAttributeEquals($bolContractReset, '_bolContractReset', $objComponent);
        $this->assertAttributeEquals($strContract, '_strContract', $objComponent);
        $this->assertAttributeEquals($bolSchedule, 'bolScheduleChange', $objComponent);
        $this->assertAttributeEquals($bolScheduleDowngrade, 'bolScheduleDowngrade', $objComponent);
        $this->assertAttributeEquals($bolTakePayment, '_bolTakePayment', $objComponent);
        $this->assertAttributeEquals($oldWlrScid, '_oldWlrScid', $objComponent);
        $this->assertAttributeEquals($newWlrScid, '_newWlrScid', $objComponent);
        $this->assertAttributeEquals($oldAdslComponentId, '_oldAdslComponentId', $objComponent);
        $this->assertAttributeEquals($defunctAdslComponentIds, '_defunctAdslComponentIds', $objComponent);
        $this->assertAttributeEquals($oldWlrComponentId, '_oldWlrComponentId', $objComponent);
        $this->assertAttributeEquals($newAdslScid, '_newAdslScid', $objComponent);
    }

    /**
     * Test some getters that are left over
     *
     * @covers AccountChange_Product_ServiceDefinition::getTickets
     * @covers AccountChange_Product_ServiceDefinition::getServiceNotices
     *
     * @return void
     */
    public function testGetters()
    {
        $id = 500;

        $product = new AccountChange_Product_ServiceDefinition($id, true);

        $this->assertAttributeEquals($product->getTickets(), '_tickets', $product);
        $this->assertAttributeEquals($product->getServiceNotices(), '_serviceNotices', $product);
    }

    /**
     * Test getProductId returns correct id
     *
     * @covers AccountChange_Product_ServiceComponent::getProductId
     *
     * @return void
     */

    public function testGetProductIdReturnsCorrectId()
    {
        $intProductId = 500;

        $objProductConfiguration = new AccountChange_Product_ServiceDefinition($intProductId, true);

        $this->assertEquals($intProductId, $objProductConfiguration->getProductId());
    }

    /**
     * Test isKeyProduct returns true if the product is a key product
     *
     * @covers AccountChange_Product_ServiceComponent::isKeyProduct
     *
     * @return void
     */
    public function testIsKeyProductReturnstrueIfTheProductIsAKeyProduct()
    {
        $objProductConfiguration = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_ADD
        );

        $this->asserttrue($objProductConfiguration->isKeyProduct());
    }

    /**
     * Test getProductCost throws an exception when retrieved no data
     *
     * @covers AccountChange_Product_ServiceDefinition::getProductCost
     *
     * @return void
     */
    public function testGetProductCostThrowsAnExceptionWhenRetrievedNoData()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objProduct = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_ADD
        );

        $this->setExpectedException(
            'AccountChange_Product_Exception',
            'Cannot get product details for service definition ID',
            AccountChange_Product_Exception::ERR_CANNOT_RETRIEVE_PRODUCT_DATA
        );

        $objProduct->getProductCost();
    }

    /**
     * Test getProductCost
     *
     * @covers AccountChange_Product_ServiceDefinition::getProductCost
     *
     * @return void
     */
    public function testGetProductCost()
    {
        $intCost = 999;

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('minimum_charge' => $intCost)));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objProduct = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_ADD
        );

        $intResult = $objProduct->getProductCost();

        $this->assertEquals($intCost, $intResult);
    }

    /**
     * Test setMatchingProductConfiguration when adding new product
     *
     * @covers AccountChange_Product_ServiceDefinition::setMatchingProductConfiguration
     *
     * @return void
     */
    public function testSetMatchingProductConfigurationWhenAddingNewProduct()
    {
        $objProduct1 = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_ADD
        );
        $objProduct2 = new AccountChange_Product_ServiceDefinition(
            2,
            AccountChange_Product_Manager::ACTION_NONE
        );

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct2));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }

    /**
     * Test setMatchingProductConfiguration when removing a product
     *
     * @covers AccountChange_Product_ServiceDefinition::setMatchingProductConfiguration
     *
     * @return void
     */
    public function testSetMatchingProductConfigurationWhenRemovingAProduct()
    {
        $objProduct1 = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_REMOVE
        );
        $objProduct2 = new AccountChange_Product_ServiceDefinition(
            2,
            AccountChange_Product_Manager::ACTION_NONE
        );

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct2));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }

    /**
     * Test setMatchingProductConfiguration when changing a product
     *
     * @covers AccountChange_Product_ServiceDefinition::setMatchingProductConfiguration
     *
     * @return void
     */
    public function testSetMatchingProductConfigurationWhenChangingAProduct()
    {
        $objProduct1 = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE
        );
        $objProduct2 = new AccountChange_Product_ServiceDefinition(
            2,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct2));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $this->assertEquals($objProduct2, $objProduct1->getNewProductConfiguration());
    }

    /**
     * Test setMatchingProductConfigurationManually throws exception if the
     * configuration types are different
     *
     * @covers AccountChange_Product_Wlr::setMatchingProductConfigurationManually
     *
     * @return void
     */
    public function testSetMatchingProductConfigurationManuallyThrowsExceptionIfTheConfigurationTypesAreDifferent()
    {
        $this->setExpectedException(
            'AccountChange_Product_Exception',
            'Matching product is not the same product type',
            AccountChange_Product_Exception::ERR_MATCHING_PRODUCT_NOT_VALID
        );

        $objProduct1 = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_CHANGE
        );
        $objProduct2 = new AccountChange_Product_Wlr(
            2,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $objProduct1->setMatchingProductConfigurationManually($objProduct2);
    }

    /**
     * Test setAccountChangeOperation when upgrading a product
     * NOTE: this test is timing out: the @medium keyword below extends the test timings
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChangeOperation
     *
     * @medium
     * @group medium
     *
     * @return void
     */
    public function testSetAccountChangeOperationWhenUpgradingAProduct()
    {
        // Service Definition
        $objProduct1 = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getContract', 'getProductCost'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct1->expects($this->at(0))
            ->method('getProductCost')
            ->will($this->returnValue(10));

        $objProduct1->expects($this->at(1))
            ->method('getProductCost')
            ->will($this->returnValue(50));

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $objProduct1->setAccountChangeOperation($objNewAccountConfiguration);

        $this->assertAttributeEquals(
            AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE,
            'intAccountChangeOperation',
            $objProduct1
        );
    }

    /**
     * Test setAccountChangeOperation when downgrading a product
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChangeOperation
     *
     * @return void
     */
    public function testSetAccountChangeOperationWhenDowngradingAProduct()
    {
        // Service Definition
        $objProduct1 = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getContract', 'getProductCost'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct1->expects($this->at(0))
            ->method('getProductCost')
            ->will($this->returnValue(50));

        $objProduct1->expects($this->at(1))
            ->method('getProductCost')
            ->will($this->returnValue(10));

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $objProduct1->setAccountChangeOperation($objNewAccountConfiguration);

        $this->assertAttributeEquals(
            AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
            'intAccountChangeOperation',
            $objProduct1
        );
    }

    /**
     * Test setAccountChangeOperation when keeping the product the same
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChangeOperation
     *
     * @return void
     */
    public function testSetAccountChangeOperationWhenKeepingTheProductTheSame()
    {
        $objProduct1 = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE
        );
        $objProduct2 = new AccountChange_Product_ServiceDefinition(
            2,
            AccountChange_Product_Manager::ACTION_NONE
        );

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct2));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);

        $objProduct1->setAccountChangeOperation($objNewAccountConfiguration);

        $this->assertAttributeEquals(
            AccountChange_Product_Manager::ACCOUNT_CHANGE_SAME,
            'intAccountChangeOperation',
            $objProduct1
        );
    }

    /**
     * Test setAccountChangeOperation calls setAccountChange
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChangeOperation
     *
     * @return void
     */
    public function testSetAccountChangeOperationCallsSetAccountChange()
    {
        // Service Definition
        $objProduct1 = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getContract', 'setAccountChange'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct1->expects($this->once())
            ->method('setAccountChange')
            ->will($this->returnValue(50));

        $objOldAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objNewAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct1));

        $objProduct1->setMatchingProductConfiguration($objNewAccountConfiguration);
        $objProduct1->setAccountChangeOperation($objNewAccountConfiguration);
    }

    /**
     * Test setAccountChange throws exception if there is no new product
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChange
     *
     * @return void
     */
    public function testSetAccountChangeThrowsExceptionIfThereIsNoNewProduct()
    {
        $this->setExpectedException(
            'AccountChange_Product_Exception',
            'There is no matching product set',
            AccountChange_Product_Exception::ERR_NEW_PRODUCT_NOT_SET
        );

        $objProduct = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Product_ServiceDefinition',
            array(1, 1)
        );

        $objProduct->protected_setAccountChange();
    }

    /**
     * Test setAccountChange sets operation to upgrade if new product cost is
     * greater
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChange
     *
     * @return void
     */
    public function testSetAccountChangeSetsOperationToUpgradeIfNewProductCostIsGreater()
    {
        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objOldProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(10));

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(20));

        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $this->assertAttributeEquals(
            AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE,
            'intAccountChangeOperation',
            $objOldProduct
        );
    }

    /**
     * Test setAccountChange sets operation to downgrade if new product cost is
     * less
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChange
     *
     * @return void
     */
    public function testSetAccountChangeSetsOperationToDowngradeIfNewProductCostIsLess()
    {
        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objOldProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(40));

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(20));

        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $this->assertAttributeEquals(
            AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
            'intAccountChangeOperation',
            $objOldProduct
        );
    }

    /**
     * Test setAccountChange sets operation to upgrade if new product cost is
     * the same
     *
     * @covers AccountChange_Product_ServiceDefinition::setAccountChange
     *
     * @return void
     */
    public function testSetAccountChangeSetsOperationToUpgradeIfNewProductCostIsTheSame()
    {
        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objOldProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(20));

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(20));

        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $this->assertAttributeEquals(
            AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE,
            'intAccountChangeOperation',
            $objOldProduct
        );
    }

    /**
     * Test getAction
     *
     * @covers AccountChange_Product_ServiceDefinition::getAction
     *
     * @return void
     */
    public function testGetAction()
    {
        $intAction = AccountChange_Product_Manager::ACTION_NONE;

        $objProductConfiguration = new AccountChange_Product_ServiceDefinition(1, $intAction);

        $this->assertEquals($intAction, $objProductConfiguration->getAction());
    }

    /**
     * Test change calls changeAccount if change is not scheduled
     *
     * @covers AccountChange_Product_ServiceDefinition::change
     *
     * @return void
     */
    public function testChangeCallsChangeAccountIfChangeIsNotScheduled()
    {
        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $intServiceId = 9999;

        $objProductConfiguration = new AccountChange_Product_ServiceDefinition(
            1234,
            AccountChange_Product_Manager::ACTION_CHANGE,
            array('bolContractReset' => true)
        );

        $objMockProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'setAccountChange', 'changeAccount', 'resetUsage', 'isBvUser', 'isPartnerEnduser'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true))
        );

        $objMockProduct->expects($this->once())
            ->method('scheduleChange');

        $objMockProduct->expects($this->once())
            ->method('setAccountChange');

        $objMockProduct->expects($this->once())
            ->method('changeAccount');

        $objMockProduct->expects($this->once())
            ->method('resetUsage');

        $objMockProduct->expects($this->once())
            ->method('isBvUser');

        $objMockProduct->expects($this->once())
            ->method('isPartnerEnduser');

        $objMockProduct->setServiceId($intServiceId);

        $objMockProduct->setMatchingProductConfigurationManually($objProductConfiguration);

        $objMockProduct->execute();
    }

    /**
     * Test change calls scheduleChange if change is scheduled
     *
     * @covers AccountChange_Product_ServiceDefinition::change
     *
     * @return void
     */
    public function testChangeCallsScheduleChangeIfChangeIsScheduled()
    {
        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $intServiceId = 9999;

        $objProductConfiguration = new AccountChange_Product_ServiceDefinition(
            1234,
            AccountChange_Product_Manager::ACTION_CHANGE,
            array(
                'bolContractReset' => true,
                'bolSchedule' => true
            )
        );

        $objMockProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'setAccountChange', 'changeAccount'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array(
                    'bolContractReset' => true,
                    'bolSchedule' => true
                )
            )
        );

        $objMockProduct->expects($this->once())
            ->method('scheduleChange');

        $objMockProduct->expects($this->once())
            ->method('setAccountChange');

        $objMockProduct->expects($this->never())
            ->method('changeAccount');

        $objMockProduct->setServiceId($intServiceId);

        $objMockProduct->setMatchingProductConfigurationManually($objProductConfiguration);

        $objMockProduct->execute();
    }

    /**
     * Test change calls scheduleChange if change is a downgrade and scheduled
     *
     * @covers AccountChange_Product_ServiceDefinition::change
     *
     * @return void
     */
    public function testChangeCallsScheduleChangeIfChangeIsADowngradeAndScheduled()
    {
        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $intServiceId = 9999;

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'changeAccount', 'getProductCost'),
            array(
                1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array(
                    'bolContractReset' => true,
                    'bolScheduleDowngrade' => true
                )
            )
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'changeAccount', 'getProductCost'),
            array(
                1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array(
                    'bolContractReset' => true,
                    'bolScheduleDowngrade' => true
                )
            )
        );

        $objOldProduct->expects($this->once())
            ->method('scheduleChange');

        $objOldProduct->expects($this->never())
            ->method('changeAccount');

        $objOldProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(20));

        $objNewProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(10));

        $objOldProduct->setServiceId($intServiceId);
        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $objOldProduct->execute();
    }

    /**
     * Test execute calls change if the action is set to change
     *
     * @covers AccountChange_Product_ServiceDefinition::execute
     *
     * @return void
     */
    public function testExecuteCallsChangeIfTheActionIsSetToChange()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('remove', 'create', 'refresh', 'change'),
            array(1, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct->expects($this->never())
            ->method('remove');

        $objProduct->expects($this->never())
            ->method('create');

        $objProduct->expects($this->never())
            ->method('refresh');

        $objProduct->expects($this->once())
            ->method('change');

        $objProduct->execute();
    }

    /**
     * Test execute calls create if the action is set to add
     *
     * @covers AccountChange_Product_ServiceDefinition::execute
     *
     * @return void
     */
    public function testExecuteCallsCreateIfTheActionIsSetToAdd()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('remove', 'create', 'refresh', 'change'),
            array(1, AccountChange_Product_Manager::ACTION_ADD)
        );

        $objProduct->expects($this->never())
            ->method('remove');

        $objProduct->expects($this->once())
            ->method('create');

        $objProduct->expects($this->never())
            ->method('refresh');

        $objProduct->expects($this->never())
            ->method('change');

        $objProduct->execute();
    }

    /**
     * Test execute calls remove if the action is set to remove
     *
     * @covers AccountChange_Product_ServiceDefinition::execute
     *
     * @return void
     */
    public function testExecuteCallsRemoveIfTheActionIsSetToRemove()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('remove', 'create', 'refresh', 'change'),
            array(1, AccountChange_Product_Manager::ACTION_REMOVE)
        );

        $objProduct->expects($this->once())
            ->method('remove');

        $objProduct->expects($this->never())
            ->method('create');

        $objProduct->expects($this->never())
            ->method('refresh');

        $objProduct->expects($this->never())
            ->method('change');

        $objProduct->execute();
    }

    /**
     * Test execute calls refresh if the action is set to refresh
     *
     * @covers AccountChange_Product_ServiceDefinition::execute
     *
     * @return void
     */
    public function testExecuteCallsRefreshIfTheActionIsSetToRefresh()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('remove', 'create', 'refresh', 'change'),
            array(1, AccountChange_Product_Manager::ACTION_REFRESH)
        );

        $objProduct->expects($this->never())
            ->method('remove');

        $objProduct->expects($this->never())
            ->method('create');

        $objProduct->expects($this->once())
            ->method('refresh');

        $objProduct->expects($this->never())
            ->method('change');

        $objProduct->execute();
    }

    /**
     * Test execute calls nothing if the action is none or none of the
     * valid actions
     *
     * @covers AccountChange_Product_ServiceDefinition::execute
     *
     * @return void
     */
    public function testExecuteCallsNothingIfTheActionIsNoneOrNoneOfTheValidActions()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('remove', 'create', 'refresh', 'change'),
            array(1, AccountChange_Product_Manager::ACTION_NONE)
        );

        $objProduct->expects($this->never())
            ->method('remove');

        $objProduct->expects($this->never())
            ->method('create');

        $objProduct->expects($this->never())
            ->method('refresh');

        $objProduct->expects($this->never())
            ->method('change');

        $objProduct->execute();
    }

    /**
     * Test setLegacyComponent not to keep sets the correct attribute
     *
     * @covers AccountChange_Product_ServiceDefinition::setLegacyComponentNotToKeep
     *
     * @return void
     */
    public function testSetLegacyComponentNotToKeepSetsTheCorrectAttribute()
    {
        $arrNotKeep = array(992, 12, 123, 3221);

        $objProduct = new AccountChange_Product_ServiceDefinition(
            12,
            AccountChange_Product_Manager::ACTION_NONE
        );

        $objProduct->setLegacyComponentNotToKeep($arrNotKeep);

        $this->assertAttributeEquals($arrNotKeep, '_arrLegacyComponentNotToKeep', $objProduct);
    }

    /**
     * Test getContractEndDate returns correct end date
     *
     * @covers AccountChange_Product_ServiceDefinition::getContractEndDate
     *
     * @return void
     */
    public function testGetContractEndDateReturnsCorrectEndDate()
    {
        $dteEvent = mktime(0, 0, 0, 11, 05, 2008); //'2008-11-05';

        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('includeLegacyFiles'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        // Userdata Mock
        $objUserdataSplitter = $this->getMock(
            'Lib_Userdata',
            array('userdata_service_events_find'),
            array(),
            '',
            false
        );

        $objUserdataSplitter->expects($this->once())
            ->method('userdata_service_events_find')
            ->will($this->returnValue(array(array('event_date' => date('Y-m-d', $dteEvent)))));

        Lib_Userdata::setInstance($objUserdataSplitter);

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Annual')));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $uxtExpected = mktime(0, 0, 0, date('m', $dteEvent), date('d', $dteEvent), date('Y', $dteEvent) + 1);
        $uxtContractEndDate = $objProduct->getContractEndDate();

        $this->assertEquals($uxtExpected, $uxtContractEndDate);
    }

    /**
     * Test calculateDaysRemainingOnContract returns zero if the contract end
     * date is zero
     *
     * @covers AccountChange_Product_ServiceDefinition::calculateDaysRemainingOnContract
     *
     * @return void
     */
    public function testCalculateDaysRemainingOnContractReturnsZeroIfTheContractEndDateIsZero()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getContractEndDate'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct->expects($this->once())
            ->method('getContractEndDate')
            ->will($this->returnValue(0)); // 05-11-2008

        $intDaysRemaining = $objProduct->calculateDaysRemainingOnContract();

        $this->assertEquals(0, $intDaysRemaining);
    }

    /**
     * Test calculateDaysRemainingOnContract returns none zero if the
     * contract end date is zero
     *
     * @covers AccountChange_Product_ServiceDefinition::calculateDaysRemainingOnContract
     *
     * @return void
     */
    public function testCalculateDaysRemainingOnContractReturnsNoneZeroIfTheContractEndDateIsZero()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getContractEndDate'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct->expects($this->once())
            ->method('getContractEndDate')
            ->will($this->returnValue(mktime(0, 0, 0, date("m"), date("d") + 2, date("Y")))); // 05-11-2008

        $intDaysRemaining = $objProduct->calculateDaysRemainingOnContract();

        $this->assertEquals(2, $intDaysRemaining);
    }

    /**
     * Test is takingPayment returns correct value
     *
     * @covers AccountChange_Product_ServiceDefinition::isTakingPayment
     *
     * @return void
     */
    public function testIsTakingPaymentReturnsCorrectValue()
    {
        $objProduct = new AccountChange_Product_ServiceDefinition(
            1,
            AccountChange_Product_Manager::ACTION_NONE,
            array('bolTakePayment' => true)
        );

        $this->asserttrue($objProduct->isTakingPayment());
    }

    /**
     * Test setMatchingProductConfigurationManually throws exception if not
     * passed a serviceDefinitionProduct
     *
     * @covers AccountChange_Product_ServiceDefinition::setMatchingProductConfigurationManually
     *
     * @return void
     */
    public function testSetMatchingProductConfigurationManuallyThrowsExceptionIfNotPassedAServiceDefinitionProduct()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('setAccountChange'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct->expects($this->never())
            ->method('setAccountChange');

        $objProductConfiguration = new AccountChange_Product_Wlr(
            515,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $this->setExpectedException(
            'AccountChange_Product_Exception',
            'Matching product is not the same product type',
            AccountChange_Product_Exception::ERR_MATCHING_PRODUCT_NOT_VALID
        );

        $objProduct->setMatchingProductConfigurationManually($objProductConfiguration);
    }

    /**
     * Test setMatchingProductConfigurationManually calls setAccountChange
     * if product is correct
     *
     * @covers AccountChange_Product_ServiceDefinition::setMatchingProductConfigurationManually
     *
     * @return void
     */
    public function testSetMatchingProductConfigurationManuallyCallsSetAccountChangeIfProductIsCorrect()
    {
        $objProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('setAccountChange'),
            array(12, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objProduct->expects($this->once())
            ->method('setAccountChange');

        $objProductConfiguration = new AccountChange_Product_ServiceDefinition(
            6630,
            AccountChange_Product_Manager::ACTION_CHANGE
        );

        $objProduct->setMatchingProductConfigurationManually($objProductConfiguration);
    }

    /**
     * This function is just a holding place for calling all the other dumb functions
     * that do their little bit
     *
     * So this test just needs to make sure that those calls are made, after that the other
     * unit tests should cover that they work etc
     *
     * @covers       AccountChange_Product_ServiceDefinition::changeAccount
     *
     * @dataProvider provideIsPartnerAccountOrEndUserOrResidentiaL
     *
     * @return void
     */
    public function testChangeAccountCallsTheCorrectFunctionsNeededToPerformAccountChange(
        $isPartnerAccount,
        $isPartnerEndUser,
        $isResidential
    )
    {
        $intServiceId = 9999;

        $expectedActions = [
            'Products',
            'Discounts',
            'LegacyComponents',
            'Ellacoya',
            'Cbc',
            'Radius',
            'StaticIp',
            'Billing',
            'Contract',
            'Hardware',
            'UpdateSupplierProduct',
            'GbWrProductSetChangeJl',
            'GbWrBullGuardJl',
            'Consent',
            'RegisterCharges',
            'LtcContracts',
            'BroadbandOnlyCli'
        ];


        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'setServiceDefinitionForService',
                'getComponentIdForTypeAndStatus',
                'isFibreProduct',
                'isLimitedAccount'
            ),
            array(
                'AccountChange',
                Db_Manager::DEFAULT_TRANSACTION,
                false
            )
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('setServiceDefinitionForService');

        $objMockDbAdaptor->expects($this->once())
            ->method('getComponentIdForTypeAndStatus');

        $objMockDbAdaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objMockDbAdaptor->expects($this->any())
            ->method('isLimitedAccount')
            ->will($this->returnValue(true));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objActionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('execute'),
            array(),
            '',
            false
        );

        $objActionManager->expects($this->once())
            ->method('execute');

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'scheduleChange',
                'processInternetConnectionProduct',
                'getActionManager',
                'logChange',
                'raiseAccountChangeServiceNotice',
                'setAccountChange',
                'processIncludedBandwith',
                'processDataUsageLimit',
                'getCurrentFlexId',
                'resetUsage',
                'isBvUser',
                'isPartnerEnduser',
                'getExchangeHelper',
                'isLimitedAccount',
                'resetDtwRBM',
                'isPartnerAccount'
            ),
            array(
                1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true)
            )
        );

        $mockExchangeHelper = $this->getMock(
            'AccountChange_ExchangeHelper',
            array('getMarketAndExchangeIdFromRegistry', 'maintainCustomerExhangeData')
        );

        $marketId = 12334;
        $exchangeId = 242332;

        $exchangeAndMarketId = array(
            'marketId' => $marketId,
            'exchangeId' => $exchangeId
        );

        $mockExchangeHelper
            ->expects($this->once())
            ->method('getMarketAndExchangeIdFromRegistry')
            ->will($this->returnValue($exchangeAndMarketId));

        $mockExchangeHelper
            ->expects($this->once())
            ->method('maintainCustomerExhangeData')
            ->with($intServiceId, $marketId, $exchangeId)
            ->will($this->returnValue(true));

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'getProductCost'),
            array(1235, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true))
        );

        $objOldProduct
            ->expects($this->once())
            ->method('getExchangeHelper')
            ->will($this->returnValue($mockExchangeHelper));

        $objOldProduct->expects($this->once())
            ->method('scheduleChange');

        $objOldProduct->expects($this->once())
            ->method('processInternetConnectionProduct');

        $objOldProduct->expects($this->once())
            ->method('getActionManager')
            ->with($intServiceId, $expectedActions)
            ->will($this->returnValue($objActionManager));

        $objOldProduct->expects($this->once())
            ->method('logChange');

        $objOldProduct->expects($this->once())
            ->method('raiseAccountChangeServiceNotice');

        $objOldProduct->expects($this->once())
            ->method('setAccountChange');

        $objOldProduct->expects($this->once())
            ->method('processIncludedBandwith');

        $objOldProduct->expects($this->once())
            ->method('processDataUsageLimit');

        $objOldProduct->expects($this->once())
            ->method('getCurrentFlexId');

        if (!$isPartnerEndUser) {
            $objOldProduct->expects($this->once())
                ->method('resetUsage');
        }
        $objOldProduct->expects($this->once())
            ->method('isBvUser');

        if ($isPartnerEndUser) {
            $objOldProduct->expects($this->exactly(2))
                ->method('isPartnerEnduser')
                ->will($this->returnValue($isPartnerEndUser));
        }

        $objOldProduct->expects($this->once())
            ->method('isLimitedAccount')
            ->will($this->returnValue(true));

        if (!$isPartnerAccount && !$isPartnerEndUser) {
            $objOldProduct->expects($this->once())
                ->method('resetDtwRBM')
                ->will($this->returnValue("1"));
        }

        $objOldProduct->expects($this->once())
            ->method('isPartnerAccount')
            ->will($this->returnValue($isPartnerAccount));


        $objOldProduct->setServiceId($intServiceId);
        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $objOldProduct->execute();
        FeatureToggleDbAdaptorTestHelper::tearDownToggleMock();
    }

    /**
     * Confirm that the system handles invalid linecheck results correctly. I.e. when the linecheck object holds
     * "null" values, the system doesn't attempt to call $exchangeHelper->maintainCustomerExhangeData()
     * As per testChangeAccountCallsTheCorrectFunctionsNeededToPerformAccountChange(), this test therefore doesn't
     * perform any asserts, as it's checking the functional flow rather than the functional "output".
     * NOTE: the "positive" flow is covered in testChangeAccountCallsTheCorrectFunctionsNeededToPerformAccountChange()
     *
     * @covers AccountChange_Product_ServiceDefinition::changeAccount
     *
     * @return void
     */
    public function testChangeAccountHandlesInvalidLineCheckResults()
    {
        $intServiceId = 9999;

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'setServiceDefinitionForService',
                'getComponentIdForTypeAndStatus',
                'isFibreProduct'
            ),
            array(
                'AccountChange',
                Db_Manager::DEFAULT_TRANSACTION,
                false
            )
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('setServiceDefinitionForService');

        $objMockDbAdaptor->expects($this->once())
            ->method('getComponentIdForTypeAndStatus');

        $objMockDbAdaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objActionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('execute'),
            array(),
            '',
            false
        );

        $objActionManager->expects($this->once())
            ->method('execute');

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'scheduleChange',
                'processInternetConnectionProduct',
                'getActionManager',
                'logChange',
                'raiseAccountChangeServiceNotice',
                'setAccountChange',
                'processIncludedBandwith',
                'processDataUsageLimit',
                'getCurrentFlexId',
                'resetUsage',
                'isBvUser',
                'isPartnerEnduser',
                'getExchangeHelper',
                'isLimitedAccount'
            ),
            array(
                1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true)
            )
        );

        $mockExchangeHelper = $this->getMock(
            'AccountChange_ExchangeHelper',
            array('getMarketAndExchangeIdFromRegistry', 'maintainCustomerExhangeData')
        );

        $marketId = null;
        $exchangeId = null;

        $exchangeAndMarketId = array(
            'marketId' => $marketId,
            'exchangeId' => $exchangeId
        );

        $mockExchangeHelper
            ->expects($this->once())
            ->method('getMarketAndExchangeIdFromRegistry')
            ->will($this->returnValue($exchangeAndMarketId));

        $mockExchangeHelper
            ->expects($this->never())
            ->method('maintainCustomerExhangeData');

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'getProductCost'),
            array(1235, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true))
        );

        $objOldProduct
            ->expects($this->once())
            ->method('getExchangeHelper')
            ->will($this->returnValue($mockExchangeHelper));

        $objOldProduct
            ->expects($this->once())
            ->method('isLimitedAccount')
            ->will($this->returnValue(false));

        $objOldProduct->expects($this->once())
            ->method('scheduleChange');

        $objOldProduct->expects($this->once())
            ->method('processInternetConnectionProduct');

        $objOldProduct->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($objActionManager));

        $objOldProduct->expects($this->once())
            ->method('logChange');

        $objOldProduct->expects($this->once())
            ->method('raiseAccountChangeServiceNotice');

        $objOldProduct->expects($this->once())
            ->method('setAccountChange');

        $objOldProduct->expects($this->once())
            ->method('processIncludedBandwith');

        $objOldProduct->expects($this->once())
            ->method('processDataUsageLimit');

        $objOldProduct->expects($this->once())
            ->method('getCurrentFlexId');

        $objOldProduct->expects($this->once())
            ->method('resetUsage');

        $objOldProduct->expects($this->once())
            ->method('isBvUser');

        $objOldProduct->expects($this->once())
            ->method('isPartnerEnduser');

        $objOldProduct->setServiceId($intServiceId);
        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $objOldProduct->execute();
        FeatureToggleDbAdaptorTestHelper::tearDownToggleMock();
    }

    /**
     * Test change account does not call any functions if products are same and
     * contract needs setting
     *
     * @covers AccountChange_Product_ServiceDefinition::alterContractOnly
     *
     * @return void
     */
    public function testChangeAccountDoesNotCallAnyFunctionsIfProductsAreSameAndContractNeedsSetting()
    {
        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $intServiceId = 9999999;
        $objActionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('execute'),
            array(),
            '',
            false
        );

        $objActionManager->expects($this->once())
            ->method('execute');

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'scheduleChange',
                'processInternetConnectionProduct',
                'processLegacyComponents',
                'getActionManager',
                'logChange',
                'raiseAccountChangeServiceNotice',
                'setAccountChange',
                'resetUsage',
                'isBvUser',
                'isPartnerEnduser'
            ),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true)
            )
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'getProductCost'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true))
        );
        $objOldProduct->expects($this->never())
            ->method('processInternetConnectionProduct');

        $objOldProduct->expects($this->never())
            ->method('processLegacyComponents');

        $objOldProduct->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($objActionManager));

        $objOldProduct->expects($this->once())
            ->method('resetUsage');

        $objOldProduct->expects($this->once())
            ->method('isBvUser');

        $objOldProduct->expects($this->once())
            ->method('isPartnerEnduser');

        $objOldProduct->setServiceId($intServiceId);
        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);
        $objOldProduct->execute();
        FeatureToggleDbAdaptorTestHelper::tearDownToggleMock();
    }

    /**
     * Test that the getContract calls the database
     *
     * @covers AccountChange_Product_ServiceDefinition::getContract
     *
     * @return void
     */
    public function testGetContractReturnsContract()
    {
        $aContract = 'Monthly';
        $db = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->once())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => $aContract)));

        Db_Manager::setAdaptor('AccountChange', $db);

        $product = new AccountChange_Product_ServiceDefinition(
            1234,
            AccountChange_Product_Manager::ACTION_NONE,
            array()
        );

        $output = $product->getContract();

        $this->assertEquals($aContract, $output);
    }

    /**
     * Test getDesiredContract returns the contract passed into constructor
     *
     * @covers AccountChange_Product_ServiceDefinition::getDesiredContract
     *
     * @return void
     */
    public function testGetDesiredContractReturnsCorrectContract()
    {
        $aContract = 'aContract';
        $product = new AccountChange_Product_ServiceDefinition(
            1234,
            AccountChange_Product_Manager::ACTION_NONE,
            array('strContract' => $aContract)
        );

        $output = $product->getDesiredContract();

        $this->assertEquals($aContract, $output);
    }

    /**
     * Test isKeyProduct returns true
     *
     * @covers AccountChange_Product_ServiceDefinition::isKeyProduct
     *
     * @return void
     */
    public function testIsKeyProductReturnstrue()
    {
        $product = new AccountChange_Product_ServiceDefinition(
            1234,
            AccountChange_Product_Manager::ACTION_NONE,
            array()
        );

        $output = $product->isKeyProduct();

        $this->asserttrue($output);
    }

    /**
     * Test the setter for contract
     *
     * @covers AccountChange_Product_ServiceDefinition::setContract
     *
     * @return void
     */
    public function testSetContractCorrectlySetsAttribute()
    {
        $contract = new AccountChange_Product_ServiceComponentContract('Monthly', 'Monthly');

        $product = new AccountChange_Product_ServiceDefinition(
            1234,
            AccountChange_Product_Manager::ACTION_NONE,
            array()
        );

        $product->setContract($contract);

        $this->assertAttributeEquals($contract, 'objServiceDefinitionContract', $product);
    }

    /**
     * Test for isScheduled
     *
     * @param bool $bolSchedule Wether is scheduled account change
     * @param bool $bolScheduleDowngrade Whether is scheduled downgrade
     * @param int $intType Account change operation
     * @param bool $output Expected qoutput
     *
     * @covers       AccountChange_Product_ServiceDefinition::isScheduled
     *
     * @dataProvider provideIsScheduledData
     *
     * @return void
     */
    public function testIsScheduledWithData($bolSchedule, $bolScheduleDowngrade, $intType, $output)
    {
        $options = array(
            'bolSchedule' => $bolSchedule,
            'bolScheduleDowngrade' => $bolScheduleDowngrade
        );

        $product = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getAccountChangeOperation'),
            array(1234, AccountChange_Product_Manager::ACTION_NONE, $options)
        );

        $product->expects($this->any())
            ->method('getAccountChangeOperation')
            ->will($this->returnValue($intType));

        $actual = $product->isScheduled();

        $this->assertEquals($output, $actual);
    }

    /**
     * Test for buildActionManager
     *
     * @covers AccountChange_Product_ServiceDefinition::buildActionManager
     *
     * @return void
     */
    public function testBuildActionManager()
    {
        $objActionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('execute'),
            array(),
            '',
            false
        );

        $options = array(
            'hardwareOption' => 'hardwareOption',
            'strProvisionOn' => 'Adsl2',
            'bolWbcProduct' => true,
            'objLineCheckResult' => null
        );

        $product = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getActionManager', 'setAccountChange', 'isScheduled'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE, $options)
        );

        $product->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($objActionManager));

        $product->expects($this->once())
            ->method('setAccountChange');

        $product->expects($this->exactly(2))
            ->method('isScheduled')
            ->will($this->returnValue(true));

        $productMock = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getActionManager'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE, $options)
        );

        $product->setMatchingProductConfigurationManually($productMock);

        $return = $product->buildActionManager(array());
        $this->assertEquals($objActionManager, $return);

        $registry = AccountChange_Registry::instance();
        $intNewServiceDefinitionId = $registry->getEntry('intNewServiceDefinitionId');
        $this->assertEquals(1234, $intNewServiceDefinitionId);

        $arrLegacyComponentNotToKeep = $registry->getEntry('arrLegacyComponentNotToKeep');
        $this->assertEquals(array(), $arrLegacyComponentNotToKeep);

        $arrLegacyComponentTypesToIgnore = $registry->getEntry(
            'arrLegacyComponentTypesToIgnore'
        );
        $this->assertEquals(array(), $arrLegacyComponentTypesToIgnore);

        $hardwareOption = $registry->getEntry('hardwareOption');
        $this->assertEquals('hardwareOption', $hardwareOption);

        $strProvisionOn = $registry->getEntry('strProvisionOn');
        $this->assertEquals('Adsl2', $strProvisionOn);

        $bolWbcProduct = $registry->getEntry('bolWbcProduct');
        $this->assertEquals(true, $bolWbcProduct);

        $objLineCheckResult = $registry->getEntry('objLineCheckResult');
        $this->assertEquals(null, $objLineCheckResult);

        $isScheduled = $registry->getEntry('bolSchedule');
        $this->assertEquals(true, $isScheduled);
    }

    /**
     * Test that newAdslComponentId / newWlrComponentId are set when isScheduled() is false
     *
     * @covers AccountChange_Product_ServiceDefinition::buildActionManager
     *
     * @return void
     */
    public function testBuildActionManagerWhenIsScheduledIsFalse()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getComponentIdForTypeAndStatus'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objMockDbAdaptor->expects($this->exactly(2))
            ->method('getComponentIdForTypeAndStatus')
            ->will($this->returnValue(array("99")));
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objActionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('execute'),
            array(),
            '',
            false
        );

        $options = array(
            'hardwareOption' => 'hardwareOption',
            'strProvisionOn' => 'Adsl2',
            'bolWbcProduct' => true,
            'objLineCheckResult' => null,
            'newWlrScid' => 123
        );

        $product = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getActionManager', 'setAccountChange', 'isScheduled'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE, $options)
        );

        $product->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($objActionManager));

        $product->expects($this->once())
            ->method('setAccountChange');

        $product->expects($this->exactly(2))
            ->method('isScheduled')
            ->will($this->returnValue(false));

        $productMock = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getActionManager'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE, $options)
        );

        $product->setMatchingProductConfigurationManually($productMock);

        $return = $product->buildActionManager(array());
        $this->assertEquals($objActionManager, $return);

        $registry = AccountChange_Registry::instance();

        $this->assertEquals(99, $registry->getEntry('newAdslComponentId'));
        $this->assertEquals(99, $registry->getEntry('newWlrComponentId'));
    }

    /**
     * Data provider for testIsScheduledWithData
     *
     * @return void
     */
    public static function provideIsScheduledData()
    {
        return array(
            array(
                true,
                true,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                true
            ),
            array(
                true,
                false,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                true
            ),
            array(
                false,
                false,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                false
            ),
            array(
                false,
                true,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE,
                true
            ),
        );
    }

    /**
     * Data provider for testIsPartnerAccount or PartnerEndUser or any other (Residential)
     *
     * @return void
     */
    public static function provideIsPartnerAccountOrEndUserOrResidentiaL()
    {
        return array(
            array(
                true,
                false,
                false
            ),
            array(
                false,
                true,
                false
            ),
            array(
                false,
                false,
                true
            ),
        );
    }

    /**
     * Test for sendConfirmationEmail
     * providerForFetchCbcUsageDetails
     *
     * @covers       AccountChange_Product_ServiceDefinition::sendConfirmationEmail
     * @covers       AccountChange_Product_ServiceDefinition::fetchContractDetails
     *
     * @dataProvider providerForSendConfirmationEmail
     *
     * @return void
     */
    public function testSendConfirmationEmail($data, $hasProfitForgoneContract, $mailData, $contractDetails)
    {
        $intMaxSpeedKbps = 7000;
        $strEstimatedSpeed = '9000 Kbps';
        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array(
                'getHighestAvailableSpeed',
                'getOfcomCompliantSpeedEstimate',
                'amendResultWithNewCapping'
            ),
            array(),
            '',
            false
        );

        $lineCheckResult->expects($this->once())
            ->method('getHighestAvailableSpeed')
            ->will($this->returnValue($intMaxSpeedKbps));

        $lineCheckResult->expects($this->once())
            ->method('getOfcomCompliantSpeedEstimate')
            ->will($this->returnValue($strEstimatedSpeed));

        $lineCheckResult
            ->expects($this->once())
            ->method('amendResultWithNewCapping');

        $data['objLineCheckResult'] = $lineCheckResult;

        $supplierPlatform = $this->getMock(
            'Product_SupplierPlatform',
            array('getSupplierPlatformId'),
            array(),
            '',
            false
        );

        $supplierPlatform->expects($this->once())
            ->method('getSupplierPlatformId')
            ->will($this->returnValue(new Int(123)));

        $supplierProduct = $this->getMock(
            'Product_SupplierProduct',
            array('getSupplierProductId'),
            array(),
            '',
            false
        );

        $supplierProduct->expects($this->once())
            ->method('getSupplierProductId')
            ->will($this->returnValue(new Int(456)));

        $supplierProductRules = $this->getMock(
            'Product_SupplierProductRules',
            array('getSupplierPlatform', 'getSupplierProduct'),
            array(),
            '',
            false
        );

        $supplierProductRules->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue($supplierPlatform));

        $supplierProductRules->expects($this->once())
            ->method('getSupplierProduct')
            ->will($this->returnValue($supplierProduct));

        $arrContractDetails = array(
            'intContractLengthInMonths' => 12,
            'objContractEndDate' => '2010-05-06'
        );

        $arrCbcDetails = array(
            'bolDisplayUsageInfo' => true,
            'intCappedBandwidthLimit' => 1200
        );

        $intServiceId = 1234;
        $productFamily = $this->getMock(
            'ProductFamily_Generic',
            array('hasAutoContracts', 'isDualPlay'),
            array(),
            '',
            false
        );
        $productFamily->expects($this->once())
            ->method('hasAutoContracts')
            ->will($this->returnValue($hasProfitForgoneContract));

        $productFamily->expects($this->once())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $productServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'getProductFamily',
                'fetchContractDetails',
                'fetchCbcUsageDetails',
                'adslGetUserRealm',
                'sendEmail',
                'getSupplierProductRules',
                'getNewServiceDefinitionId',
                'getSpeedCaps',
                'isFibreProduct',
                'getMinAndMaxSpeedRanges'
            ),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true))
        );

        $productServiceDefinition->setServiceId($intServiceId);

        if ($hasProfitForgoneContract) {
            $contract = new \Plusnet\ContractsClient\Entity\Contract();
            $contract->setId(1);
            $contract->setStatus($contractDetails['status']);
            // 12 months duration
            $contract->setDuration(12, Plusnet\ContractsClient\Entity\DurationUnit::MONTH);
            // endDate in 11 months 29 day in future
            $contractEndDate = new DateTime();
            $contractEndDate->add(new DateInterval('P12M1D'));
            $contract->setEndDate($contractDetails['endDate']);
            $contract->setStartDate($contractDetails['startDate']);
            $contract->setCreationReason($contractDetails['creationReason']);

            $contractsClient = $this->getMock(
                '\Plusnet\ContractsClient\Client',
                array(
                    'setServiceId',
                    'getContract'
                )
            );
            $contractsClient->expects($this->once())
                ->method('setServiceId')
                ->will($this->returnValue($contractsClient));

            $contractsClient->expects($this->once())
                ->method('getContract')
                ->will($this->returnValue($contract));
            BusTier_BusTier::setClient('contracts', $contractsClient);
        } else {
            $productServiceDefinition->expects($this->once())
                ->method('fetchContractDetails')
                ->will($this->returnValue($arrContractDetails));
        }

        $productServiceDefinition->expects($this->once())
            ->method('fetchCbcUsageDetails')
            ->will($this->returnValue($arrCbcDetails));

        $productServiceDefinition->expects($this->any())
            ->method('getProductFamily')
            ->will($this->returnValue($productFamily));

        $productServiceDefinition->expects($this->once())
            ->method('adslGetUserRealm')
            ->will($this->returnValue('plus.net'));

        $productServiceDefinition->expects($this->once())
            ->method('getSupplierProductRules')
            ->will($this->returnValue($supplierProductRules));

        $productServiceDefinition->expects($this->once())
            ->method('sendEmail')
            ->with($intServiceId, 'adsl2adsl', $mailData);

        $productServiceDefinition
            ->expects($this->exactly(2))
            ->method('getNewServiceDefinitionId')
            ->will($this->returnValue(6577));

        $productServiceDefinition
            ->expects($this->once())
            ->method('getSpeedCaps')
            ->will($this->returnValue(array('downCap' => 2000, 'upCap' => 1000)));

        $productServiceDefinition
            ->expects($this->exactly(2))
            ->method('isFibreProduct')
            ->will($this->returnValue(true));

        $productServiceDefinition
            ->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->will($this->returnValue(array('downloadSpeedRangeMin' => null, 'downloadSpeedRangeMax' => null)));

        $accountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $accountChangeDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('type' => 'residential')));

        Db_Manager::setAdaptor('AccountChange', $accountChangeDbAdaptor);

        $productServiceDefinition->sendConfirmationEmail($data);
    }

    /**
     * Data provider for sendConfirmationEmail
     */
    public function providerForSendConfirmationEmail()
    {
        $data1 = array(
            'arrSelectedBroadband' => array('strNewProduct' => 'Value'),
            'objOngoingProductCost' => 22.50,
            'objDiscountedProductCost' => 11.50,
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'strUserRealm' => 'plus.net',
            'bolLinecheckOptOut' => true,
            'intOldSdi' => 1234,
            'intNewSdi' => 1235,
        );

        $contractEndDate = new DateTime();
        $contractEndDate->add(new DateInterval('P12M1D'));
        $profitContractDetails1 = array(
            'status' => 'ACTIVE',
            'startDate' => date('Y-m-d'),
            'endDate' => $contractEndDate->format('d-m-Y'),
            'creationReason' => \Plusnet\ContractsClient\Entity\CreationReason::ACCOUNT_CHANGE
        );

        $data2 = array(
            'arrSelectedBroadband' => array('strNewProduct' => 'Unlimited'),
            'objOngoingProductCost' => 22.50,
            'objDiscountedProductCost' => 11.50,
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'strUserRealm' => 'plus.net',
            'bolLinecheckOptOut' => true,
            'intOldSdi' => 1235,
            'intNewSdi' => 4567,
        );

        $profitContractDetails2 = array(
            'status' => 'ACTIVE',
            'startDate' => date('Y-m-d', mktime(0, 0, 0, date('m') - 2, 1, date('Y'))),
            'endDate' => $contractEndDate->format('d-m-Y'),
            'creationReason' => \Plusnet\ContractsClient\Entity\CreationReason::ACCOUNT_CHANGE
        );

        $profitContractDetails3 = array(
            'status' => 'PENDING',
            'startDate' => null,
            'endDate' => null,
            'creationReason' => \Plusnet\ContractsClient\Entity\CreationReason::ACCOUNT_CHANGE
        );

        $data3 = array(
            'arrSelectedBroadband' => array('strNewProduct' => 'Unlimited'),
            'objOngoingProductCost' => 22.50,
            'objDiscountedProductCost' => 11.50,
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'strUserRealm' => 'plus.net',
            'bolLinecheckOptOut' => true,
            'intOldSdi' => 1235,
            'intNewSdi' => 4777,
        );

        $mailVariablesData1 = array(
            'bolSpeedChecked' => true,
            'strEstimatedSpeed' => '9000 Kbps',
            'strProduct' => 'Value',
            'objOngoingProductCost' => 22.50,
            'objDiscountedProductCost' => 11.50,
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'intContractLengthInMonths' => 12,
            'objContractEndDate' => '2010-05-06',
            'bolDisplayUsageInfo' => true,
            'intCappedBandwidthLimit' => 1200,
            'strUserRealm' => 'plus.net',
            'bolFibreToFibre' => true,
            'estimatedSpeedRangeLow' => null,
            'estimatedSpeedRangeHigh' => null,
            'isNewProductDualPlay' => true
        );

        $mailVariablesData2 = array(
            'bolSpeedChecked' => true,
            'strEstimatedSpeed' => '9000 Kbps',
            'strProduct' => 'Unlimited',
            'objOngoingProductCost' => 22.50,
            'objDiscountedProductCost' => 11.50,
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'intContractLengthInMonths' => 12,
            'objContractEndDate' => $contractEndDate->format('d-m-Y'),
            'bolDisplayUsageInfo' => true,
            'intCappedBandwidthLimit' => 1200,
            'strUserRealm' => 'plus.net',
            'bolFibreToFibre' => true,
            'isContractRetained' => false,
            'estimatedSpeedRangeLow' => null,
            'estimatedSpeedRangeHigh' => null,
            'isNewProductDualPlay' => true
        );

        $mailVariablesData3 = array(
            'bolSpeedChecked' => true,
            'strEstimatedSpeed' => '9000 Kbps',
            'strProduct' => 'Unlimited',
            'objOngoingProductCost' => 22.50,
            'objDiscountedProductCost' => 11.50,
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'intContractLengthInMonths' => 12,
            'objContractEndDate' => $contractEndDate->format('d-m-Y'),
            'bolDisplayUsageInfo' => true,
            'intCappedBandwidthLimit' => 1200,
            'strUserRealm' => 'plus.net',
            'bolFibreToFibre' => true,
            'isContractRetained' => true,
            'estimatedSpeedRangeLow' => null,
            'estimatedSpeedRangeHigh' => null,
            'isNewProductDualPlay' => true
        );

        $mailVariablesData4 = array(
            'bolSpeedChecked' => true,
            'strEstimatedSpeed' => '9000 Kbps',
            'strProduct' => 'Unlimited',
            'objOngoingProductCost' => 22.50,
            'objDiscountedProductCost' => 11.50,
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'bolDisplayUsageInfo' => true,
            'intCappedBandwidthLimit' => 1200,
            'strUserRealm' => 'plus.net',
            'bolFibreToFibre' => true,
            'estimatedSpeedRangeLow' => null,
            'estimatedSpeedRangeHigh' => null,
            'isNewProductDualPlay' => true
        );

        return array(
            array($data1, false, $mailVariablesData1, array()),
            array($data2, true, $mailVariablesData2, $profitContractDetails1),
            array($data3, true, $mailVariablesData3, $profitContractDetails2),
            array($data3, true, $mailVariablesData4, $profitContractDetails3)
        );
    }

    /**
     * Data provider for testGetSetupFeeGetsFeeFromDb
     *
     * @return array
     */
    public function providerForFetchCbcUsageDetails()
    {
        $gigabyte = **********;

        $cappedQuotaGB = 10;
        $flexQuotaGB = 20;

        return array(
            // Test 1: customer is on a quota'd product (STANDARD)
            // Usage info should be generated; the "flex" calls should *NOT* be triggered
            array(
                array('strVariantHandle' => 'STANDARD'),
                true,
                $cappedQuotaGB * $gigabyte,
                null,
                null,
                array(
                    'bolDisplayUsageInfo' => true,
                    'intCappedBandwidthLimit' => $cappedQuotaGB
                )
            ),
            // Test 2: customer is on a quota'd product (JLSTANDARD)
            // Usage info should be generated; the "flex" calls should *NOT* be triggered
            array(
                array('strVariantHandle' => 'JLSTANDARD', 'strFamilyHandle' => 'JOHNLEWIS2011', 'intSDI' => 6770),
                true,
                $cappedQuotaGB * $gigabyte,
                null,
                null,
                array(
                    'bolDisplayUsageInfo' => true,
                    'intCappedBandwidthLimit' => $cappedQuotaGB
                )
            ),
            // Test 3: customer is on a quota'd product (PRO) where a quota has not been set.
            // Usage info should be generated; the "flex" calls should be triggered
            array(
                array('strVariantHandle' => 'PRO'),
                true,
                0,
                123,
                array(
                    'intIncludedBandwidthBytes' => $flexQuotaGB * $gigabyte
                ),
                array(
                    'bolDisplayUsageInfo' => true,
                    'intCappedBandwidthLimit' => $flexQuotaGB
                )
            ),
            // Test 4: customer is on a quota'd product (EXTRA) where no quotas exist
            // Usage info should be generated; the "flex" calls should be triggered; a cap of "0" will be returned
            array(
                array('strVariantHandle' => 'EXTRA'),
                true,
                0,
                123,
                array(
                    'intIncludedBandwidthBytes' => 0
                ),
                array(
                    'bolDisplayUsageInfo' => true,
                    'intCappedBandwidthLimit' => 0
                )
            ),
            // Test 5: customer is on a non-quota'd product (BASIC)
            // Usage info should not be generated; bandwidth-cap and flex calls should *NOT* be triggered
            array(
                array('strVariantHandle' => 'BASIC'),
                false,
                null,
                null,
                null,
                array(
                    'bolDisplayUsageInfo' => false,
                    'intCappedBandwidthLimit' => 0
                )
            ),
            // Test 6: customer is on a quota'd product (JLFIBRE)
            // Usage info should not be generated; bandwidth-cap and flex calls should *NOT* be triggered
            array(
                array('strVariantHandle' => 'JLFIBRE', 'strFamilyHandle' => 'JOHNLEWIS2011', 'intSDI' => 6772),
                false,
                null,
                null,
                null,
                array(
                    'bolDisplayUsageInfo' => false,
                    'intCappedBandwidthLimit' => 0
                )
            ),
            // Test 7: customer is on a quota'd product (JLFIBREEXTRA)
            // Usage info should not be generated; bandwidth-cap and flex calls should *NOT* be triggered
            array(
                array('strVariantHandle' => 'JLFIBREEXTRA', 'strFamilyHandle' => 'JOHNLEWIS2011', 'intSDI' => 6871),
                false,
                null,
                null,
                null,
                array(
                    'bolDisplayUsageInfo' => false,
                    'intCappedBandwidthLimit' => 0
                )
            ),
        );
    }

    /**
     * Test that fetchCbcUsageDetails() returns the correct usage/quota details
     *
     * @param array $arrContract the contract details for the customer
     * @param boolean $bolHasCap Indicates that the customer's product is capped
     * @param int $intBandwidthBytes the capped bandwidth limit for the customer - may be null
     * @param int $intFlexId the flex id - may be null
     * @param array $arrFlexDetails the flex details for the given id - may be null
     * @param array $arrExpectedData the data which fetchCbcUsageDetails() is expected to return
     *
     * @covers       AccountChange_Product_ServiceDefinition::fetchCbcUsageDetails
     * @dataProvider providerForFetchCbcUsageDetails
     *
     * @return void
     */
    public function testFetchCbcUsageDetails(
        $arrContract,
        $bolHasCap,
        $intBandwidthBytes,
        $intFlexId,
        $arrFlexDetails,
        $arrExpectedData
    )
    {

        $intServiceDefinitionId = 1234;
        $intServiceId = '5678';

        $productFamily = $this->getMock(
            'ProductFamily_Generic',
            array('isCbcProduct'),
            array(),
            '',
            false
        );
        $productFamily->expects($this->any())
            ->method('isCbcProduct')
            ->will($this->returnValue($arrExpectedData['bolDisplayUsageInfo']));

        $productServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'fetchUserdataServiceProductGet',
                'GetCBCUserCappedBandwidthLimit',
                'getCurrentFlexId',
                'GetCBCFlexDetails',
                'getProductFamily'
            ),
            array($intServiceDefinitionId, AccountChange_Product_Manager::ACTION_CHANGE,
                array('appointing' => array()))
        );

        $productServiceDefinition->expects($this->once())
            ->method('fetchUserdataServiceProductGet')
            ->with($intServiceId)
            ->will($this->returnValue($arrContract));

        if ($bolHasCap) {
            $productServiceDefinition->expects($this->once())
                ->method('GetCBCUserCappedBandwidthLimit')
                ->with($intServiceId)
                ->will($this->returnValue($intBandwidthBytes));
        } else {
            $productServiceDefinition->expects($this->never())
                ->method('GetCBCUserCappedBandwidthLimit');
        }

        if (empty($intFlexId) == false) {
            $productServiceDefinition->expects($this->once())
                ->method('getCurrentFlexId')
                ->will($this->returnValue($intFlexId));

            $productServiceDefinition->expects($this->once())
                ->method('GetCBCFlexDetails')
                ->with($intFlexId)
                ->will($this->returnValue($arrFlexDetails));
        } else {
            $productServiceDefinition->expects($this->never())
                ->method('getCurrentFlexId');
            $productServiceDefinition->expects($this->never())
                ->method('GetCBCFlexDetails');
        }

        $productServiceDefinition->expects($this->any())
            ->method('getProductFamily')
            ->will($this->returnValue($productFamily));

        $productServiceDefinition->setServiceId($intServiceId);

        $arrReturnData = $productServiceDefinition->fetchCbcUsageDetails();
        $this->assertEquals($arrExpectedData, $arrReturnData);
    }

    /**
     * Test for sendAppointmentEmail
     *
     * @covers AccountChange_Product_ServiceDefinition::sendAppointmentEmail
     *
     * @return void
     */
    public function testSendAppointmentEmail()
    {
        // live appointing test data
        $arrData = array(
            'appointingType' => array(
                'serviceHandle' => 'FTTC'
            ),
            'liveAppointing' => 1,
            'appointment' => '01/02/2012PM'
        );
        // fallback test data
        $arrData2 = array(
            'appointingType' => array(
                'serviceHandle' => 'FTTC'
            ),
            'appointmentdate1' => '**********', // 01/02/2012
            'appointmenttime1' => 'AM',
            'appointmentdate2' => '**********', // 02/02/2012
            'appointmenttime2' => 'PM',
            'appointmentdate3' => '**********', // 03/02/2012
            'appointmenttime3' => 'AM'
        );
        // mail variables resulting from live appointing data
        $arrMailVariablesTemplate = array(
            'fibreAppointments' => array(
                'liveAppointing' => true,
                'Date1' => '01/02/2012 PM'
            )
        );
        // mail variables resulting from fallback data
        $arrMailVariablesTemplate2 = array(
            'fibreAppointments' => array(
                'Date1' => '01/02/2012 AM',
                'Date2' => '02/02/2012 PM',
                'Date3' => '03/02/2012 AM'
            )
        );

        $intServiceId = 1234;

        // test for live appointing data
        $productServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'sendEmail'
            ),
            array(
                $intServiceId,
                AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true)
            )
        );
        $productServiceDefinition->setServiceId($intServiceId);

        $productServiceDefinition->expects($this->once())
            ->method('sendEmail')
            ->with($intServiceId, 'fibre_install_dates', $arrMailVariablesTemplate);

        $productServiceDefinition->sendAppointmentEmail($arrData);

        // test for fallback data
        $productServiceDefinition2 = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'sendEmail'
            ),
            array(
                $intServiceId,
                AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true)
            )
        );
        $productServiceDefinition2->setServiceId($intServiceId);

        $productServiceDefinition2->expects($this->once())
            ->method('sendEmail')
            ->with($intServiceId, 'fibre_install_dates', $arrMailVariablesTemplate2);

        $productServiceDefinition2->sendAppointmentEmail($arrData2);
    }

    /**
     * Test getSetupFee returns false if no appointing was used
     *
     * @covers AccountChange_Product_ServiceDefinition::getSetupFee
     *
     * @return void
     */
    public function testGetSetupFeeReturnsfalseIfNoAppointingWasUsed()
    {
        $productServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('fetchContractDetails'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('appointing' => array()))
        );

        $result = $productServiceDefinition->getSetupFee();
        $this->assertfalse($result);
    }

    /**
     * Test getSetupFee gets fee from Db
     *
     * @param int $initialCharge Initial charge
     * @param array|bool $expected Expected result
     *
     * @covers       AccountChange_Product_ServiceDefinition::getSetupFee
     *
     * @dataProvider providerForTestGetSetupFee
     *
     * @return array
     */
    public function testGetSetupFeeGetsFeeFromDb($initialCharge, $expected)
    {
        $productServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('fetchContractDetails'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('appointing' => array('appointingType' => 'FTTC')))
        );
        $accountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getSetupFee'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $accountChangeDbAdaptor->expects($this->once())
            ->method('getSetupFee')
            ->will($this->returnValue($initialCharge));

        Db_Manager::setAdaptor('AccountChange', $accountChangeDbAdaptor);

        $result = $productServiceDefinition->getSetupFee();
        $this->assertEquals($expected, $result);
    }

    /**
     * Data provider for testGetSetupFeeGetsFeeFromDb
     *
     * @return array
     */
    public function providerForTestGetSetupFee()
    {
        return array(
            // data set 0
            array(
                25,
                array(
                    'charge' => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 25),
                    'description' => 'Broadband Activation'
                ),
            ),
            array(
                0,
                false,
            ),
            array(
                null,
                false,
            )
        );
    }


    /**
     * Check that AccountChange_Product_ServiceDefinition::isJohnLewisProduct gets details of
     * product from database and uses the isp result
     *
     * @param array $productDetails Product details
     * @param bool $expectedResult Expected result
     *
     * @covers       AccountChange_Product_ServiceDefinition::isJohnLewisProduct
     *
     * @dataProvider provideDataForTestingJohnLewisProductChecks*
     *
     * @return void
     */
    public function testThatTheJohnLewisProductCheckValidatesAgainstDatabase(
        $productDetails,
        $expectedResult
    )
    {
        $serviceDefinitionId = 1234;

        // add new product to registry
        $registry = AccountChange_Registry::instance();
        $registry->setEntry("intNewServiceDefinitionId", $serviceDefinitionId);

        // set up database mock
        $databaseMock = $this->getMock(
            "Db_Adaptor",
            array(
                "getServiceDefinitionDetails",
            ),
            array(
                "AccountChange",
                Db_Manager::DEFAULT_TRANSACTION,
                true
            )
        );

        $databaseMock
            ->expects($this->once())
            ->method("getServiceDefinitionDetails")
            ->with($serviceDefinitionId)
            ->will($this->returnValue($productDetails));

        Db_Manager::setAdaptor(
            "AccountChange",
            $databaseMock,
            Db_Manager::DEFAULT_TRANSACTION
        );

        $service = TestCaseWithProxy::getPHPUnitProxy(
            "AccountChange_Product_ServiceDefinition",
            array(
                $serviceDefinitionId,
                AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE,
                array()
            )
        );

        $this->assertEquals(
            $expectedResult,
            $service->protected_isJohnLewisProduct()
        );
    }

    /**
     * Data provider for testing AccountChange_Product_ServiceDefinition::isJohnLewisProduct
     *
     * @return array
     */
    public function provideDataForTestingJohnLewisProductChecks()
    {
        return array(

            // not John Lewis product
            array(
                array(
                    "requires" => "adsl",
                    "isp" => "plus.net"
                ),
                false
            ),

            // John Lewis product
            array(
                array(
                    "requires" => "adsl",
                    "isp" => "johnlewis"
                ),
                true
            )
        );
    }

    /**
     * Test that the correct template is used when sending confirmation email
     *
     * @param bool $switchingToJlpFromGbWr Flag whether customer is
     *                                       switchig from GB/WR to JLP or not
     * @param string $serviceType Service type
     * @param string $expectedEmailTemplate Expected email template to be sent
     *
     * @covers       AccountChange_Product_ServiceDefinition::sendConfirmationEmail
     *
     * @dataProvider provideChangeDetailsForSendingConfirmationEmails
     *
     * @return void
     */
    public function testCorrectTemplateIsUsedWhenSendingConfirmationEmail(
        $switchingToJlpFromGbWr,
        $serviceType,
        $expectedEmailTemplate
    )
    {
        $serviceId = 1987521;
        $oldSdi = 6718;
        $newSdi = 6719;
        $serviceDefinition = 82321;
        $action = AccountChange_Product_Manager::ACTION_CHANGE;
        $options = array();
        $newProductName = 'Plusnet Unlimited';
        $newProductId = 198;

        $contractDetails = array(
            'intContractLengthInMonths' => 12,
            'objContractEndDate' => I18n_Date::fromString('2010-05-06')
        );

        $cbcDetails = array(
            'bolDisplayUsageInfo' => true,
            'intCappedBandwidthLimit' => 1200
        );

        $emailData = array(
            'intOldSdi' => $oldSdi,
            'intNewSdi' => $newSdi,
            'arrSelectedBroadband' => array(
                'strNewProduct' => $newProductName
            ),
            'objOngoingProductCost' => new I18n_Currency('gbp', '22.50'),
            'objDiscountedProductCost' => new I18n_Currency('gbp', '11.50'),
            'floOngoingProductCost' => 22.50,
            'floDiscountedProductCost' => 11.50,
            'intDiscountLength' => 12,
            'bolLinecheckOptOut' => true,
        );

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('amendResultWithNewCapping'),
            array()
        );

        $lineCheckResult->expects($this->once())
            ->method('amendResultWithNewCapping');

        $emailData['objLineCheckResult'] = $lineCheckResult;

        $accountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $accountChangeDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('type' => $serviceType)));

        Db_Manager::setAdaptor('AccountChange', $accountChangeDbAdaptor);

        $productSupplier = $this->getMock(
            'Product_Supplier',
            array(),
            array(
                new Int(1),
                new String('BT'),
                new String('BT')
            )
        );

        $supplierPlatform = $this->getMock(
            'Product_SupplierPlatform',
            array('getSupplierPlatformId'),
            array(
                new Int(6),
                $productSupplier,
                new String('BT21CN'),
                new String('BT 21CN/WBC/ADSL2+')
            )
        );

        $supplierPlatform->expects($this->once())
            ->method('getSupplierPlatformId')
            ->will($this->returnValue(new Int($newProductId)));

        $supplierProduct = $this->getMock(
            'Product_SupplierProduct',
            array('getSupplierProductId'),
            array(
                new Int(121),
                new Int(11),
                new String('WBC MAX24 mb/s (ADSL2+ Annex M) Stable, Auto Interleaving, Elevated'),
                new String('WBC End User Access (EUA)'),
                new Int(250),
                new Int(24000),
                new Int(21000),
                new String('Stable'),
                new String('Auto'),
                new String('AnnexM'),
                new String('Elevated'),
                new String('Maintenance Category 5')
            )
        );

        $supplierProduct->expects($this->once())
            ->method('getSupplierProductId')
            ->will($this->returnValue(new Int($newProductId)));

        $supplierProductRules = $this->getMock(
            'Product_SupplierProductRules',
            array('getSupplierPlatform', 'getSupplierProduct'),
            array($lineCheckResult, new Int(0))
        );

        $supplierProductRules->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue($supplierPlatform));

        $supplierProductRules->expects($this->once())
            ->method('getSupplierProduct')
            ->will($this->returnValue($supplierProduct));
        $productFamily = $this->getMock(
            'ProductFamily_Generic',
            array('hasAutoContracts'),
            array(),
            '',
            false
        );
        $productFamily->expects($this->once())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));
        $productServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'includeLegacyFiles', 'fetchContractDetails',
                'fetchCbcUsageDetails', 'getSupplierProductRules',
                'adslGetUserRealm', 'sendEmail',
                'checkCustomerSwitchingFromGbWrToJlp',
                'getNewServiceDefinitionId',
                'getSpeedCaps',
                'isFibreProduct',
                'getProductFamily',
                'getMinAndMaxSpeedRanges'
            ),
            array($serviceDefinition, $action, $options)
        );

        $productServiceDefinition->setServiceId($serviceId);

        $productServiceDefinition->expects($this->once())
            ->method('getSupplierProductRules')
            ->will($this->returnValue($supplierProductRules));

        $productServiceDefinition->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($productFamily));

        $productServiceDefinition->expects($this->once())
            ->method('fetchContractDetails')
            ->will($this->returnValue($contractDetails));

        $productServiceDefinition->expects($this->once())
            ->method('fetchCbcUsageDetails')
            ->will($this->returnValue($cbcDetails));

        $productServiceDefinition->expects($this->any())
            ->method('checkCustomerSwitchingFromGbWrToJlp')
            ->will($this->returnValue($switchingToJlpFromGbWr));

        $productServiceDefinition->expects($this->once())
            ->method('sendEmail')
            ->with(
                $this->equalTo($serviceId),
                $this->equalTo($expectedEmailTemplate)
            );

        $productServiceDefinition
            ->expects($this->exactly(2))
            ->method('getNewServiceDefinitionId')
            ->will($this->returnValue(6777));

        $productServiceDefinition
            ->expects($this->once())
            ->method('getSpeedCaps')
            ->will($this->returnValue(array('downCap' => 2000, 'upCap' => 1000)));

        $productServiceDefinition
            ->expects($this->exactly(2))
            ->method('isFibreProduct')
            ->will($this->returnValue(true));

        $productServiceDefinition
            ->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->will($this->returnValue(array('downloadSpeedRangeMin' => null, 'downloadSpeedRangeMax' => null)));

        $productServiceDefinition->sendConfirmationEmail($emailData);
    }

    /**
     * Data provider for testCorrectTemplateIsUsedWhenSendingConfirmationEmail
     * providing change details for sending confirmation email
     *
     * Provides:
     * 1. Flag whether customer is switchig from GB/WR to JLP or not
     * 2. Service type
     * 3. Expected email template to be sent
     *
     * @return array
     */
    public function provideChangeDetailsForSendingConfirmationEmails()
    {
        return array(
            //residential customer switching (from GB/WR to JLP)
            array(
                'switchingToJlpFromGbWr' => true,
                'serviceType' => 'residential',
                'expectedEmailTemplate' => 'adsl2adsl_from_gbwr'
            ),
            //residential customer switching (not from GB/WR to JLP)
            array(
                'switchingToJlpFromGbWr' => false,
                'serviceType' => 'residential',
                'expectedEmailTemplate' => 'adsl2adsl'
            ),
            //business customer switching
            array(
                'switchingToJlpFromGbWr' => false,
                'serviceType' => 'business',
                'expectedEmailTemplate' => 'adsl2adsl_business'
            )
        );
    }

    /**
     * Test scheduledChange will set product details to registry
     *
     * @covers AccountChange_Product_ServiceDefinition::scheduleChange
     *
     * @return void
     */
    public function testScheduledChangeWillSetProductDetailsToRegistry()
    {
        $oldSdi = 6687;
        $newSdi = 6771;
        $action = AccountChange_Product_Manager::ACTION_CHANGE;
        $oldServiceDefinition = array(
            'name' => 'Greenbee Phone and Broadband'
        );

        $newServiceDefinition = array(
            'name' => 'Unlimited'
        );

        $serviceId = 1987352;
        $nextInvoiceDate = I18n_Date::fromString('10-03-2012');

        $productSet = array(
            array(
                'intSdi' => $newSdi,
                'strProductName' => 'Unlimited',
                'intProductCost' => 18.0
            )
        );

        $productsAndError = $productSet;

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getHighestAvailableSpeed')
        );

        $options = array(
            'bolSchedule' => true,
            'hardwareOption' => 'Thomson 4-port Wireless Router',
            'strProvisionOn' => true,
            'bolWbcProduct' => true,
            'objLineCheckResult' => $lineCheckResult,
            'appointing' => false,
            'arrSelectedWlr' => array(
                'strHomePhoneProduct' => 'Talking Anytime International',
                'floOngoingHomePhoneProductCost' => 20.5
            )
        );

        $userdataSplitter = $this->getMock(
            'Lib_Userdata',
            array('userdata_service_schedule_add'),
            array(),
            '',
            false
        );

        Lib_Userdata::setInstance($userdataSplitter);

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->at(0))
            ->method('isFibreProduct')
            ->with($this->equalTo($oldSdi))
            ->will($this->returnValue(false));

        $adaptor->expects($this->at(1))
            ->method('isFibreProduct')
            ->with($this->equalTo($newSdi))
            ->will($this->returnValue(false));

        $adaptor->expects($this->at(2))
            ->method('getServiceDefinitionDetails')
            ->with($this->equalTo($oldSdi))
            ->will($this->returnValue($oldServiceDefinition));

        $adaptor->expects($this->at(3))
            ->method('getServiceDefinitionDetails')
            ->with($this->equalTo($newSdi))
            ->will($this->returnValue($newServiceDefinition));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $actionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array(),
            array($serviceId, array('GbWrProductSetChangeJl'))
        );

        $newProductServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('includeLegacyFiles', 'getProductCost', 'getDesiredContract'),
            array($newSdi, $action, $options)
        );

        $newProductServiceDefinition->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(18.0));

        $mockSchedulingHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('calculateScheduledChangeDate', 'getOveriddenChangeDate'),
            array()
        );

        $mockSchedulingHelper
            ->expects($this->once())
            ->method('calculateScheduledChangeDate')
            ->with($serviceId, false)
            ->will($this->returnValue($nextInvoiceDate));

        $mockSchedulingHelper
            ->expects($this->once())
            ->method('getOveriddenChangeDate')
            ->will($this->returnValue(null));

        $oldProductServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('includeLegacyFiles', 'getCoreService',
                'getProductCost', 'getBusinessActorId',
                'controllerGetBroadbandProducts', 'getActionManager', 'getSchedulingHelper'),
            array($oldSdi, $action, $options)
        );

        $oldProductServiceDefinition
            ->expects($this->exactly(2))
            ->method('getSchedulingHelper')
            ->will($this->returnValue($mockSchedulingHelper));

        $oldProductServiceDefinition->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(16.0));

        $oldProductServiceDefinition->expects($this->once())
            ->method('controllerGetBroadbandProducts')
            ->will($this->returnValue($productsAndError));

        $expectedActions = [
            'Appointment',
            'Regrade',
            'Hardware',
            'GbWrProductSetChangeJl',
            'Consent',
            'RegisterCharges',
            'CallerDisplay',
            'LineRentalSaverAdd',
            'LtcContracts'
        ];
        $oldProductServiceDefinition->expects($this->once())
            ->method('getActionManager')
            ->with(1987352,$expectedActions)
            ->will($this->returnValue($actionManager));

        $oldProductServiceDefinition->setServiceId($serviceId);
        $oldProductServiceDefinition->setMatchingProductConfigurationManually(
            $newProductServiceDefinition
        );

        $oldProductServiceDefinition->execute();

        $this->assertEquals(
            $options['hardwareOption'],
            AccountChange_Registry::instance()->getEntry('hardwareOption')
        );
        $this->assertEquals(
            $oldSdi,
            AccountChange_Registry::instance()->getEntry('intOldServiceDefinitionId')
        );
        $this->assertEquals(
            $newSdi,
            AccountChange_Registry::instance()->getEntry('intNewServiceDefinitionId')
        );
        $this->assertEquals(
            $options['strProvisionOn'],
            AccountChange_Registry::instance()->getEntry('strProvisionOn')
        );
        $this->assertEquals(
            $options['bolWbcProduct'],
            AccountChange_Registry::instance()->getEntry('bolWbcProduct')
        );
        $this->AssertInstanceOf(
            'LineCheck_Result',
            AccountChange_Registry::instance()->getEntry('objLineCheckResult')
        );
        $this->assertEquals(
            $lineCheckResult,
            AccountChange_Registry::instance()->getEntry('objLineCheckResult')
        );
        $this->assertEquals(
            $options['appointing'],
            AccountChange_Registry::instance()->getEntry('appointing')
        );
        $this->assertEquals(
            $options['bolSchedule'],
            AccountChange_Registry::instance()->getEntry('bolSchedule')
        );
        $this->assertEquals(
            $options['arrSelectedWlr'],
            AccountChange_Registry::instance()->getEntry('arrSelectedWlr')
        );
        $this->assertEquals(
            $productSet[0]['strProductName'],
            AccountChange_Registry::instance()->getEntry('newProductName')
        );
        $this->assertEquals(
            $productSet[0]['intProductCost'],
            AccountChange_Registry::instance()->getEntry('newProductCost')
        );
    }

    /**
     * Test scheduledChange will set product details to registry
     *
     * @covers AccountChange_Product_ServiceDefinition::scheduleChange
     *
     * @return void
     */
    public function testScheduledChangeWillAddC2MPromotionInformationIfCodeSupplied()
    {
        $oldSdi = 6687;
        $newSdi = 6771;
        $action = AccountChange_Product_Manager::ACTION_CHANGE;
        $oldServiceDefinition = array(
            'name' => 'Greenbee Phone and Broadband'
        );

        $newServiceDefinition = array(
            'name' => 'Unlimited'
        );

        $serviceId = 1987352;
        $nextInvoiceDate = I18n_Date::fromString('10-03-2012');

        $productSet = array(
            array(
                'intSdi' => $newSdi,
                'strProductName' => 'Unlimited',
                'intProductCost' => 18.0
            )
        );

        $productsAndError = $productSet;

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getHighestAvailableSpeed')
        );

        $options = array(
            'bolSchedule' => true,
            'hardwareOption' => 'Thomson 4-port Wireless Router',
            'strProvisionOn' => true,
            'bolWbcProduct' => true,
            'objLineCheckResult' => $lineCheckResult,
            'appointing' => false,
            'arrSelectedWlr' => array(
                'strHomePhoneProduct' => 'Talking Anytime International',
                'floOngoingHomePhoneProductCost' => 20.5
            )
        );

        $userdataSplitter = $this->getMock(
            'Lib_Userdata',
            array('userdata_service_schedule_add'),
            array(),
            '',
            false
        );

        Lib_Userdata::setInstance($userdataSplitter);

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->at(0))
            ->method('isFibreProduct')
            ->with($this->equalTo($oldSdi))
            ->will($this->returnValue(false));

        $adaptor->expects($this->at(1))
            ->method('isFibreProduct')
            ->with($this->equalTo($newSdi))
            ->will($this->returnValue(false));

        $adaptor->expects($this->at(2))
            ->method('getServiceDefinitionDetails')
            ->with($this->equalTo($oldSdi))
            ->will($this->returnValue($oldServiceDefinition));

        $adaptor->expects($this->at(3))
            ->method('getServiceDefinitionDetails')
            ->with($this->equalTo($newSdi))
            ->will($this->returnValue($newServiceDefinition));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $actionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array(),
            array($serviceId, array('GbWrProductSetChangeJl'))
        );

        $newProductServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('includeLegacyFiles', 'getProductCost', 'getDesiredContract'),
            array($newSdi, $action, $options)
        );

        $newProductServiceDefinition->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(18.0));

        $mockSchedulingHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('calculateScheduledChangeDate', 'getOveriddenChangeDate'),
            array()
        );

        $mockSchedulingHelper
            ->expects($this->once())
            ->method('calculateScheduledChangeDate')
            ->with($serviceId, false)
            ->will($this->returnValue($nextInvoiceDate));

        $mockSchedulingHelper
            ->expects($this->once())
            ->method('getOveriddenChangeDate')
            ->will($this->returnValue(null));

        $oldProductServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('includeLegacyFiles', 'getCoreService',
                'getProductCost', 'getBusinessActorId',
                'controllerGetBroadbandProducts', 'getActionManager', 'getSchedulingHelper'),
            array($oldSdi, $action, $options)
        );

        $oldProductServiceDefinition
            ->expects($this->exactly(2))
            ->method('getSchedulingHelper')
            ->will($this->returnValue($mockSchedulingHelper));

        $oldProductServiceDefinition->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(16.0));

        $oldProductServiceDefinition->expects($this->once())
            ->method('controllerGetBroadbandProducts')
            ->will($this->returnValue($productsAndError));

        $oldProductServiceDefinition->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($actionManager));

        $oldProductServiceDefinition->setServiceId($serviceId);
        $oldProductServiceDefinition->setMatchingProductConfigurationManually(
            $newProductServiceDefinition
        );

        $oldProductServiceDefinition->execute();

        $this->assertEquals(
            $options['hardwareOption'],
            AccountChange_Registry::instance()->getEntry('hardwareOption')
        );
        $this->assertEquals(
            $oldSdi,
            AccountChange_Registry::instance()->getEntry('intOldServiceDefinitionId')
        );
        $this->assertEquals(
            $newSdi,
            AccountChange_Registry::instance()->getEntry('intNewServiceDefinitionId')
        );
        $this->assertEquals(
            $options['strProvisionOn'],
            AccountChange_Registry::instance()->getEntry('strProvisionOn')
        );
        $this->assertEquals(
            $options['bolWbcProduct'],
            AccountChange_Registry::instance()->getEntry('bolWbcProduct')
        );
        $this->AssertInstanceOf(
            'LineCheck_Result',
            AccountChange_Registry::instance()->getEntry('objLineCheckResult')
        );
        $this->assertEquals(
            $lineCheckResult,
            AccountChange_Registry::instance()->getEntry('objLineCheckResult')
        );
        $this->assertEquals(
            $options['appointing'],
            AccountChange_Registry::instance()->getEntry('appointing')
        );
        $this->assertEquals(
            $options['bolSchedule'],
            AccountChange_Registry::instance()->getEntry('bolSchedule')
        );
        $this->assertEquals(
            $options['arrSelectedWlr'],
            AccountChange_Registry::instance()->getEntry('arrSelectedWlr')
        );
        $this->assertEquals(
            $productSet[0]['strProductName'],
            AccountChange_Registry::instance()->getEntry('newProductName')
        );
        $this->assertEquals(
            $productSet[0]['intProductCost'],
            AccountChange_Registry::instance()->getEntry('newProductCost')
        );
    }

    /**
     * Tests the Fibre Account Change Estimate now displayed on the terms and conditions page
     *
     * @param array $arrData Validated application data
     * @param array $arrTariffs Tariffs used by the calculation
     * @param array $arrExpected Expected result
     *
     * @covers       AccountChange_Product_ServiceDefinition::getProRataEstimate
     * @covers       AccountChange_Product_ServiceDefinition::_getProRataBillingDays
     * @covers       AccountChange_Product_ServiceDefinition::_calculateProductCostPerDay
     * @covers       AccountChange_Product_ServiceDefinition::getTariffDetails
     *
     * @dataProvider provideDataForTestGetProRataEstimate
     *
     * @return null
     */
    public function testGetProRataEstimate($arrData, $arrTariffs, $arrExpected)
    {
        $this->_arrCurrentData = $arrData;
        $this->_arrCurrentTariffs = $arrTariffs;

        $objMock = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'getTariffDetails', 'getCoreService'
            ),
            array(
                $arrData['intSdi'],
                AccountChange_Product_Manager::ACTION_CHANGE,
                $arrData['arrOptions'],
            )
        );

        // Set the Db_Adaptor for the AccountChange module.
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $intOldTariffId = $arrTariffs['arrOldTariff']['intTariffID'];
        $intNewTariffId = $arrTariffs['arrNewTariff']['intTariffID'];

        $objMock
            ->expects($this->any())
            ->method('getTariffDetails')
            ->will($this->returnCallback(array($this, 'returnGetTariffDetails')));

        $objMock
            ->expects($this->any())
            ->method('getCoreService')
            ->will($this->returnValue($arrData['objCoreService']));

        $arrResult = $objMock->getProRataEstimate(
            $arrData['intServiceId'],
            $arrData['dteAppointmentDate'],
            $intOldTariffId,
            $intNewTariffId
        );

        return $this->assertEquals($arrExpected, $arrResult);
    }

    /**
     * Test AccountChange_Product_ServiceDefinition::processInternetConnectionProduct
     *
     * @covers AccountChange_Product_ServiceDefinition::processInternetConnectionProduct
     *
     * @return void
     */
    public function testProcessInternetConnectionProduct()
    {
        $intServiceId = 9999;
        $intDefaultServiceComponentIDForServiceDefinitionID = 5;

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'setServiceDefinitionForService',
                'isFibreProduct'
            ),
            array(
                'AccountChange',
                Db_Manager::DEFAULT_TRANSACTION,
                false
            )
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('setServiceDefinitionForService');

        $objMockDbAdaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'scheduleChange',
                'setAccountChange',
                'getCurrentFlexId',
                'resetUsage',
                'getProductFamily',
                'getServiceDefinitionDetails',
                'getDefaultServiceComponentIDForServiceDefinitionID',
                'getInternetConnectionProductFromServiceId',
                'includeLegacyFiles',
                'isBvUser',
                'isPartnerEnduser',
                'isLimitedAccount'
            ),
            array(
                1234, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true)
            )
        );

        $objFamily = $this->getMock(
            'ProductFamily_Generic',
            array('getMarketFromLineCheckId', 'getInternetConnectionComponent'),
            array(),
            '',
            false
        );

        $market = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('scheduleChange', 'getProductCost'),
            array(1235, AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true, 'bolRegrade' => true))
        );
        $objOldProduct->expects($this->once())
            ->method('scheduleChange');

        $objOldProduct->expects($this->once())
            ->method('setAccountChange');

        $objOldProduct->expects($this->once())
            ->method('getCurrentFlexId');

        $objOldProduct->expects($this->once())
            ->method('resetUsage');

        $objOldProduct->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($objFamily));

        $objOldProduct->expects($this->once())
            ->method('getServiceDefinitionDetails');

        $objOldProduct->expects($this->once())
            ->method('getDefaultServiceComponentIDForServiceDefinitionID')
            ->will($this->returnValue($intDefaultServiceComponentIDForServiceDefinitionID));

        $objOldProduct->expects($this->once())
            ->method('getInternetConnectionProductFromServiceId');

        $objOldProduct->expects($this->once())
            ->method('includeLegacyFiles');

        $objOldProduct->expects($this->once())
            ->method('isBvUser');

        $objOldProduct->expects($this->once())
            ->method('isPartnerEnduser');

        $objOldProduct
            ->expects($this->once())
            ->method('isLimitedAccount')
            ->will($this->returnValue(false));

        $market->expects($this->once())
            ->method('getMarketId');

        $objFamily->expects($this->once())
            ->method('getMarketFromLineCheckId')
            ->will($this->returnValue($market));

        $objFamily->expects($this->once())
            ->method('getInternetConnectionComponent');

        $this->setExpectedException(
            'AccountChange_Product_ManagerException',
            'Non numeric service component id',
            AccountChange_Product_ManagerException::ERR_INVALID_COMPONENT_ID_TYPE
        );

        $objOldProduct->setServiceId($intServiceId);
        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $objOldProduct->execute();
        FeatureToggleDbAdaptorTestHelper::tearDownToggleMock();
    }

    /**
     * Data provider for testGetProRataEstimate
     *
     * @return array
     */
    public function provideDataForTestGetProRataEstimate()
    {
        $objCoreService = $this->getMock(
            'Core_Service',
            array(
                'getNextInvoiceDate',
                'getInvoicePeriod',
                'getInvoiceDay',
                'getServiceId',
            ),
            array()
        );

        $objCoreService
            ->expects($this->any())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue('2012-07-28'));

        $objCoreService
            ->expects($this->any())
            ->method('getInvoicePeriod')
            ->will($this->returnValue('monthly'));

        $objCoreService
            ->expects($this->any())
            ->method('getInvoiceDay')
            ->will($this->returnValue(28));

        $objCoreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(12345));

        $arrOptions = array('strContract' => 'MONTHLY');

        return array(

            // Test 1 - Old Monthly cost = £30, New Monthly cost = £60 - customer owes us £6.00
            array(
                array(
                    'intServiceId' => 12345,
                    'intSdi' => 1234,
                    'arrOptions' => $arrOptions,
                    'dteAppointmentDate' => '2012-07-22',
                    'objCoreService' => $objCoreService
                ),

                array(
                    'arrOldTariff' => $this->_getTestTariffData(100, 3000),
                    'arrNewTariff' => $this->_getTestTariffData(101, 6000),
                ),

                array(
                    'intDaysPassed' => 24,
                    'intDaysRemaining' => 6,
                    'intFttcEstimateInPence' => 600,
                    'dteBillingDateAfterAppointment' => '2012-07-27',
                    'dteBillingDateBeforeAppointment' => '2012-06-28',
                )
            ),

            // Test 2 - Old Monthly cost = £15.99, New Monthly cost = £29.99 - customer owes us £2.80
            array(
                array(
                    'intServiceId' => 12345,
                    'intSdi' => 1234,
                    'arrOptions' => $arrOptions,
                    'dteAppointmentDate' => '2012-07-22',
                    'objCoreService' => $objCoreService
                ),

                array(
                    'arrOldTariff' => $this->_getTestTariffData(100, 1599),
                    'arrNewTariff' => $this->_getTestTariffData(101, 2999),
                ),

                array(
                    'intDaysPassed' => 24,
                    'intDaysRemaining' => 6,
                    'intFttcEstimateInPence' => 280,
                    'dteBillingDateAfterAppointment' => '2012-07-27',
                    'dteBillingDateBeforeAppointment' => '2012-06-28',
                )
            ),

            // Test 3 - Old Monthly cost = £30, New Monthly cost = £60 - account changing on last day
            array(
                array(
                    'intServiceId' => 12345,
                    'intSdi' => 1234,
                    'arrOptions' => $arrOptions,
                    'dteAppointmentDate' => '2012-07-27',
                    'objCoreService' => $objCoreService
                ),

                array(
                    'arrOldTariff' => $this->_getTestTariffData(100, 3000),
                    'arrNewTariff' => $this->_getTestTariffData(101, 6000),
                ),

                array(
                    'intDaysPassed' => 29,
                    'intDaysRemaining' => 1,
                    'intFttcEstimateInPence' => 100,
                    'dteBillingDateAfterAppointment' => '2012-07-27',
                    'dteBillingDateBeforeAppointment' => '2012-06-28',
                )
            ),

            // Test 4 - Old Monthly cost = £30, New Monthly cost = £60 - account changing next to last day
            array(
                array(
                    'intServiceId' => 12345,
                    'intSdi' => 1234,
                    'arrOptions' => $arrOptions,
                    'dteAppointmentDate' => '2012-07-26',
                    'objCoreService' => $objCoreService
                ),

                array(
                    'arrOldTariff' => $this->_getTestTariffData(100, 3000),
                    'arrNewTariff' => $this->_getTestTariffData(101, 6000),
                ),

                array(
                    'intDaysPassed' => 28,
                    'intDaysRemaining' => 2,
                    'intFttcEstimateInPence' => 200,
                    'dteBillingDateAfterAppointment' => '2012-07-27',
                    'dteBillingDateBeforeAppointment' => '2012-06-28',
                )
            ),
        );
    }

    /**
     * Call back function so that we can pass back different values for the test for getTariffDetails
     *
     * @return array
     */
    public function returnGetTariffDetails()
    {
        $arrArgs = func_get_args();

        $arrMatchedTariff = null;

        foreach ($this->_arrCurrentTariffs as $arrTariff) {
            if ($arrTariff['intTariffID'] === $arrArgs[0]) {
                $arrMatchedTariff = $arrTariff;
                break;
            }
        }

        return $arrMatchedTariff;
    }

    /**
     * Returns test tariff data
     *
     * @param int $intId Id for the tariff data
     * @param int $intCostInPence Cost in Pence
     *
     * @return array
     */
    private function _getTestTariffData($intId, $intCostInPence)
    {
        $arrTariffData = array(
            "intTariffID" => $intId,
            "intContractLengthID" => 3,
            "strContractLengthHandle" => 'MONTHLY',
            "strContractLengthDisplayName" => 'Monthly',
            "intPaymentFrequencyID" => 3,
            "strPaymentFrequencyHandle" => 'MONTHLY',
            "intQuantityFrom" => 1,
            "intQuantityTo" => 1,
            "intCostIncVatPence" => $intCostInPence,
            "bolAutoRenew" => 1,
            "intNextTariffID" => 522,
            "intNoticePeriodDays" => 10,
            "uxtStart" => **********,
            "uxtEnd" => 0,
        );

        return $arrTariffData;
    }

    /**
     * Test if ChangeAction properly prepares actions when switching between product variants during adding/removing Wlr
     *
     * @covers AccountChange_Product_ServiceDefinition::execute
     * @covers AccountChange_Product_ServiceDefinition::change
     * @covers AccountChange_Product_ServiceDefinition::changeAccount
     *
     * @return void
     */
    public function testIfChangeAccountProperlyPreparesActionsWhenSwitchingBetweenProductVariants()
    {
        AccountChange_Registry::instance()->setEntry('bolVariantSwitchForWlrAddOrRemove', true);
        $nextInvoiceDate = I18n_Date::fromString('10-03-2012');

        $productSet = array(
            array(
                'intSdi' => 1234,
                'strProductName' => 'Unlimited',
                'intProductCost' => 18.0
            )
        );

        $expectedActionsForSchedule = array(
            'Hardware',
            'Appointment',
            'Regrade',
            'GbWrProductSetChangeJl',
            'Consent',
            'LineRentalSaverAdd',
            'LtcContracts',
            'RegisterCharges'
        );

        $expectedActionsForChangeAccount = array(
            'Products',
            'Ellacoya',
            'Contract',
            'Consent',
            'LtcContracts'
        );

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'isFibreProduct',
                'getServiceDefinitionDetails',
                'setServiceDefinitionForService',
                'isLimitedAccount'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $adaptor->expects($this->any())
            ->method('isLimitedAccount')
            ->will($this->returnValue(true));

        $adaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('minimum_charge' => 5.99)));

        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $intServiceId = 2239821;

        $objProductConfiguration = new AccountChange_Product_ServiceDefinition(
            1234,
            AccountChange_Product_Manager::ACTION_CHANGE,
            array('bolContractReset' => true)
        );

        $actionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('execute'),
            array(),
            '',
            false
        );

        $mockSchedulingHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('calculateScheduledChangeDate', 'getOveriddenChangeDate', 'buildProductChangeServiceNotice'),
            array()
        );

        $mockSchedulingHelper
            ->expects($this->any())
            ->method('calculateScheduledChangeDate')
            ->with($intServiceId, false)
            ->will($this->returnValue($nextInvoiceDate));

        $mockSchedulingHelper
            ->expects($this->once())
            ->method('getOveriddenChangeDate')
            ->will($this->returnValue(null));

        $serviceDefinitionProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array(
                'isFibreProduct',
                'getNewServiceDefinitionId',
                'alterContractOnly',
                'getCurrentFlexId',
                'resetUsage',
                'processInternetConnectionProduct',
                'buildActionManager',
                'logChange',
                'processIncludedBandwith',
                'processDataUsageLimit',
                'raiseAccountChangeServiceNotice',
                'isBvUser',
                'includeLegacyFiles',
                'isPartnerEnduser',
                'resetDtwRBM',
                'getSchedulingHelper',
                'controllerGetBroadbandProducts',
            ),
            array(
                1234,
                AccountChange_Product_Manager::ACTION_CHANGE,
                array('bolContractReset' => true)
            )
        );

        $serviceDefinitionProduct
            ->expects($this->any())
            ->method('getSchedulingHelper')
            ->will($this->returnValue($mockSchedulingHelper));

        $serviceDefinitionProduct->expects($this->any())
            ->method('resetDtwRBM')
            ->will($this->returnValue("1"));

        $serviceDefinitionProduct->expects($this->once())
            ->method('buildActionManager')
            ->with($expectedActionsForChangeAccount)
            ->willReturnOnConsecutiveCalls($actionManager);

        $serviceDefinitionProduct->setServiceId($intServiceId);

        $serviceDefinitionProduct->setMatchingProductConfigurationManually($objProductConfiguration);

        $serviceDefinitionProduct->execute();
    }


    /**
     * Test data for testServiceNoticeRaisedOnlyWhenBBProductChanged description]
     *
     * @return mixed pararameters to test with
     */
    public function provideDataForTestServiceNoticeRaisedOnlyWhenBBProductChanged()
    {

        return array(
            array(6687, 6771),
            array(6687, 6687)
        );
    }

    /**
     * Tests if a service notice is raised only when the BB product changes
     *
     * @param  int $oldSdi current service definition id
     * @param  int $newSdi new service definition id
     *
     * @return void
     *
     * @dataProvider provideDataForTestServiceNoticeRaisedOnlyWhenBBProductChanged
     * @covers       AccountChange_Product_ServiceDefinition::scheduleChange
     */
    public function testServiceNoticeRaisedOnlyWhenBBProductChanged($oldSdi, $newSdi)
    {

        $action = AccountChange_Product_Manager::ACTION_CHANGE;
        $oldServiceDefinition = array(
            'name' => 'Greenbee Phone and Broadband'
        );

        $newServiceDefinition = array(
            'name' => 'Unlimited'
        );

        $serviceId = '*********';
        $nextInvoiceDate = I18n_Date::fromString('10-03-2012');

        $productSet = array(
            array(
                'intSdi' => $newSdi,
                'strProductName' => 'Unlimited',
                'intProductCost' => 18.0
            )
        );

        $productsAndError = $productSet;

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getHighestAvailableSpeed')
        );

        $options = array(
            'bolSchedule' => true,
            'hardwareOption' => 'Thomson 4-port Wireless Router',
            'strProvisionOn' => true,
            'bolWbcProduct' => true,
            'objLineCheckResult' => $lineCheckResult,
            'appointing' => false,
            'arrSelectedWlr' => array(
                'strHomePhoneProduct' => 'Talking Anytime International',
                'floOngoingHomePhoneProductCost' => 20.5
            )
        );

        $userdataSplitter = $this->getMock(
            'Lib_Userdata',
            array('userdata_service_schedule_add'),
            array(),
            '',
            false
        );

        Lib_Userdata::setInstance($userdataSplitter);

        $adaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $adaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));
        Db_Manager::setAdaptor('AccountChange', $adaptor);

        $actionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array(),
            array($serviceId, array('GbWrProductSetChangeJl'))
        );

        $newProductServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('includeLegacyFiles', 'getProductCost', 'getDesiredContract'),
            array($newSdi, $action, $options)
        );

        $newProductServiceDefinition->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(18.0));

        $mockSchedulingHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('calculateScheduledChangeDate', 'getOveriddenChangeDate', 'buildProductChangeServiceNotice'),
            array()
        );

        $mockSchedulingHelper
            ->expects($this->any())
            ->method('calculateScheduledChangeDate')
            ->with($serviceId, false)
            ->will($this->returnValue($nextInvoiceDate));

        $mockSchedulingHelper
            ->expects($this->once())
            ->method('getOveriddenChangeDate')
            ->will($this->returnValue(null));

        if ($oldSdi != $newSdi) {
            $mockSchedulingHelper
                ->expects($this->once())
                ->method('buildProductChangeServiceNotice');
        } else {
            $mockSchedulingHelper
                ->expects($this->never())
                ->method('buildProductChangeServiceNotice');
        }
        $oldProductServiceDefinition = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('includeLegacyFiles', 'getCoreService',
                'getProductCost', 'getBusinessActorId',
                'controllerGetBroadbandProducts', 'getActionManager', 'getSchedulingHelper'),
            array($oldSdi, $action, $options)
        );

        $oldProductServiceDefinition
            ->expects($this->exactly(2))
            ->method('getSchedulingHelper')
            ->will($this->returnValue($mockSchedulingHelper));

        $oldProductServiceDefinition->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(16.0));

        $oldProductServiceDefinition->expects($this->once())
            ->method('controllerGetBroadbandProducts')
            ->will($this->returnValue($productsAndError));

        $oldProductServiceDefinition->expects($this->once())
            ->method('getActionManager')
            ->will($this->returnValue($actionManager));

        $oldProductServiceDefinition->setServiceId($serviceId);
        $oldProductServiceDefinition->setMatchingProductConfigurationManually(
            $newProductServiceDefinition
        );

        $oldProductServiceDefinition->execute();
    }

    /**
     * @return array
     */
    public function testInstantRecontractUsesSnapshotBehaviourDataProvider()
    {
        return [
            [true, 'once', 'never'],
            [false, 'never', 'once']
        ];
    }


    /**
     * Tests that updateScheduledChangeWithLoggedInActorId calls the expected queries with the
     * expected parameters to perform the update.
     *
     * @return void
     */
    public function testUpdateScheduledChangeWithLoggedInActorIdCallsUpdateWithCorrectId()
    {
        $actorId = 4444;
        $scheduleId = 116;

        $mockBusinessActor = Mockery::mock('Auth_BusinessActor');
        $mockBusinessActor->makePartial();
        $mockBusinessActor->shouldReceive('getActorId')->andReturn($actorId);

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getScheduleIdForBroadband')->andReturn($scheduleId);

        $dbAdaptor
            ->shouldReceive('updateServiceChangeScheduleWithActorId')
            ->with($actorId, $scheduleId);
        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $serviceDefinition = Mockery::mock('AccountChange_Product_ServiceDefinition');
        $serviceDefinition->makePartial();
        $serviceDefinition->shouldAllowMockingProtectedMethods();
        $serviceDefinition->shouldReceive('getLoggedInOrScriptActor')->andReturn($mockBusinessActor);

        $serviceDefinition->updateScheduledChangeWithLoggedInActorId();
    }

}
