<?php

/**
 * <AUTHOR>
 */

class AccountChange_PerformChangeBaseTest extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function tearDown()
    {
        Mockery::close();
        AccountChange_Registry::instance()->reset();
    }

    /**
     * @throws Exception
     * @return void
     */
    public function testDiscountsAreCancelledWhenAddingNewDiscountAsPartOfRecontract()
    {
        $serviceId = 12345;
        $scheduleId = 54321;
        $componentId = 24234;
        $componentInstanceId = 43242;
        $ticketId = 21312;
        $offeringActor = 542221;
        $actor = new Auth_BusinessActor();

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getComponentIdForTypeAndStatus')->andReturn([$componentId]);
        $dbAdaptor
            ->shouldReceive('getSubscriptionProductComponentInstanceIdByComponentId')
            ->andReturn($componentInstanceId);
        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $pendingOffer = Mockery::mock();
        $pendingOffer->shouldReceive('getAdslScheduleId')->andReturn($scheduleId);
        $pendingOffer->shouldReceive('setComponentId');
        $pendingOffer->shouldReceive('getProductComponentInstanceId')->andReturnNull();
        $pendingOffer->shouldReceive('setProductComponentInstanceId')->with($componentInstanceId);
        $pendingOffer->shouldReceive('apply')->with($actor, $offeringActor, $ticketId);
        $pendingOffer->shouldReceive('getDiscountValueIncVat');
        $pendingOffer->shouldReceive('getDiscountDurationMonths');

        $offerManager = Mockery::mock();
        $offerManager->shouldReceive('getPendingOffers')->andReturn([$pendingOffer]);

        $retentionManager = \Mockery::mock('RetentionsTool_Manager');
        $retentionManager->shouldReceive('getByScheduledEvent')->andReturn($retentionManager);
        $retentionManager->shouldReceive('getOfferManager')->andReturn($offerManager);
        $retentionManager->shouldReceive('getOfferingActor')->andReturn($offeringActor);
        $retentionManager->shouldReceive('getTicketId')->andReturn($ticketId);

        $pendingDiscount = Mockery::mock();
        $pendingDiscount->shouldReceive('getState')->andReturn(Mockery::mock('Retention_DiscountPendingState'));
        $pendingDiscount->shouldReceive('cancel')->once();

        $redeemedDiscount = Mockery::mock();
        $redeemedDiscount->shouldReceive('getState')->andReturn(Mockery::mock('Retention_DiscountRedeemedState'));
        $redeemedDiscount->shouldReceive('cancel')->never();

        $discountManager = Mockery::mock();
        $discountManager->shouldReceive('getServiceDiscounts')->andReturn([$redeemedDiscount, $pendingDiscount]);
        $discountManager->shouldReceive('saveDiscountArray');

        $performChangeBase = Mockery::mock('AccountChange_PerformChangeBase');
        $performChangeBase->shouldAllowMockingProtectedMethods();
        $performChangeBase->makePartial();

        $performChangeBase->shouldReceive('getActorByExternalUserId')->andReturn($actor);
        $performChangeBase->shouldReceive('getRetentionManagerByAdslScheduleId')->andReturn($retentionManager);
        $performChangeBase->shouldReceive('getDiscountManager')->andReturn($discountManager);
        $performChangeBase->shouldReceive('output');

        $performChangeBase->applyPendingRetentionDiscounts($serviceId, $scheduleId, true);
    }

    /**
     * @throws Exception
     * @return void
     */
    public function testDiscountsAreNotCancelledWhenNoNewDiscountAsPartOfRecontract()
    {
        $serviceId = 12345;
        $scheduleId = 54321;
        $componentId = 24234;
        $componentInstanceId = 43242;
        $ticketId = 21312;
        $offeringActor = 542221;
        $actor = new Auth_BusinessActor();

        $dbAdaptor = Mockery::mock('Db_Adaptor');
        $dbAdaptor->shouldReceive('getComponentIdForTypeAndStatus')->andReturn([$componentId]);
        $dbAdaptor
            ->shouldReceive('getSubscriptionProductComponentInstanceIdByComponentId')
            ->andReturn($componentInstanceId);
        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $offerManager = Mockery::mock();
        $offerManager->shouldReceive('getPendingOffers')->andReturn([]);

        $retentionManager = \Mockery::mock('RetentionsTool_Manager');
        $retentionManager->shouldReceive('getByScheduledEvent')->andReturn($retentionManager);
        $retentionManager->shouldReceive('getOfferManager')->andReturn($offerManager);
        $retentionManager->shouldReceive('getOfferingActor')->andReturn($offeringActor);
        $retentionManager->shouldReceive('getTicketId')->andReturn($ticketId);

        $discountManager = Mockery::mock();
        $discountManager->shouldReceive('getServiceDiscounts')->never();
        $discountManager->shouldReceive('saveDiscountArray')->never();

        $performChangeBase = Mockery::mock('AccountChange_PerformChangeBase');
        $performChangeBase->shouldAllowMockingProtectedMethods();
        $performChangeBase->makePartial();

        $performChangeBase->shouldReceive('getActorByExternalUserId')->andReturn($actor);
        $performChangeBase->shouldReceive('getRetentionManagerByAdslScheduleId')->andReturn($retentionManager);
        $performChangeBase->shouldReceive('getDiscountManager')->andReturn($discountManager);
        $performChangeBase->shouldReceive('output');

        $performChangeBase->applyPendingRetentionDiscounts($serviceId, $scheduleId, true);
    }

    /**
     * @test
     * @dataProvider restoreAccountManagerHouseMoveProvider
     * @param bool $isScheduledHouseMove         return value from house move service
     * @param bool $houseMoveSetInOptions        should house move be set in options
     * @param bool $expectedHouseMoveOptionValue value that should be set in options array
     * @throws Db_TransactionException
     */
    public function testRestoreAccountManagerSetsHouseMoveCorrectly(
        $isScheduledHouseMove,
        $houseMoveSetInOptions,
        $expectedHouseMoveOptionValue
    ) {
        $serviceId = 12345;
        $marketId = 3;
        $oldSdi = 1111;
        $newSdi = 2222;
        $newAdslScid = 333;
        $adslComponentId = 4444;
        $defunctIds = [5555,6666,7777];
        $tariffId = 999;
        $actorId = '123xyz';
        $promoCode = 'promoCode';
        $contractHandle = 'CONTRACT';
        $hasWlr = false;
        $recontractingOnSameProduct = false;
        $backDatedDate = '27/09/2021';

        $accountChangeArray = [
            'promoCode'                  => $promoCode,
            'intOldSdi'                  => $oldSdi,
            'intNewSdi'                  => $newSdi,
            'intServiceId'               => $serviceId,
            'strContractLengthHandle'    => $contractHandle,
            'backDatedDate'              => $backDatedDate
        ];

        if ($houseMoveSetInOptions) {
            $accountChangeArray['bolHouseMove'] = $houseMoveSetInOptions;
        }

        $expectedOptions = [
            'bolScheduleDowngrade'    => false,
            'bolSchedule'             => false,
            'bolContractReset'        => false,
            'bolTakePayment'          => false,
            'bolRegrade'              => false,
            'objLineCheckResult'      => null,
            'newAdslScid'             => $newAdslScid,
            'oldAdslComponentId'      => $adslComponentId,
            'defunctAdslComponentIds' => $defunctIds,
            'bolHousemove'            => $expectedHouseMoveOptionValue,
            'backDatedDate'           => $backDatedDate,
            'strContract'             => $contractHandle
        ];

        $dbAdaptor = Mockery::mock(Db_Adaptor::class);

        $dbAdaptor
            ->shouldReceive('getComponentIdForTypeAndStatus')
            ->with('active', 'INTERNET_CONNECTION', $serviceId)
            ->andReturn([$adslComponentId]);

        $dbAdaptor
            ->shouldReceive('getComponentIdForTypeAndStatus')
            ->with('destroyed', 'INTERNET_CONNECTION', $serviceId)
            ->andReturn($defunctIds);

        $dbAdaptor
            ->shouldReceive('getTariffId')
            ->with($marketId, $newSdi)
            ->andReturn($tariffId);

        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $productConfigOld = Mockery::mock(AccountChange_Product_Configuration::class);
        $productConfigNew = Mockery::mock(AccountChange_Product_Configuration::class);
        $coreServiceMock = Mockery::mock(Core_Service::class);
        $performChangeBase = Mockery::mock(AccountChange_PerformChangeBase::class)
            ->shouldAllowMockingProtectedMethods()
            ->makePartial();

        $performChangeBase->__construct();

        $performChangeBase->setCoreService($coreServiceMock);

        $performChangeBase
            ->shouldReceive('getLineCheckFromDatabase')
            ->with($coreServiceMock)
            ->andReturnNull();

        $performChangeBase
            ->shouldReceive('getInternetConnectionId')
            ->with($newSdi, $marketId)
            ->andReturn($newAdslScid);

        $performChangeBase
            ->shouldReceive('isScheduledHouseMove')
            ->with($serviceId)
            ->andReturn($isScheduledHouseMove);

        $performChangeBase
            ->shouldReceive('hasWlr')
            ->once()
            ->with($serviceId)
            ->andReturn($hasWlr);

        $performChangeBase
            ->shouldReceive('getBusinessActorId')
            ->andReturn($actorId);

        $coreServiceMock
            ->shouldReceive('getBroadbandContract')
            ->andReturn($contractHandle);

        $performChangeBase
            ->shouldReceive('getProductConfiguration')
            ->with($oldSdi, \Mockery::on(function ($argument) use ($expectedOptions) {
                $this->assertSame($argument, $expectedOptions);
                return true;
            }))
            ->andReturn($productConfigOld);

        $performChangeBase
            ->shouldReceive('getProductConfiguration')
            ->with($newSdi, $expectedOptions)
            ->andReturn($productConfigNew);

        $productConfigOld
            ->shouldReceive('setBusinessActorId')
            ->with($actorId);

        $productConfigOld
            ->shouldReceive('setServiceId')
            ->with($serviceId);

        $productConfigNew
            ->shouldReceive('setBusinessActorId')
            ->with($actorId);

        $productConfigNew
            ->shouldReceive('setServiceId')
            ->with($serviceId);

        $performChangeBase
            ->shouldReceive('isReContractingOnSameProduct')
            ->andReturn($recontractingOnSameProduct);

        $result = $performChangeBase->restoreAccountManager($accountChangeArray);
        $registry = AccountChange_Registry::instance();
        $this->assertInstanceOf('AccountChange_Manager', $result);
        $this->assertEquals($tariffId, $registry->getEntry('intSelectedTariffID'));
        $this->assertEquals($hasWlr, $registry->getEntry('bolActivationContract'));
        $this->assertEquals(false, $registry->getEntry('runFromPerformScheduledAccountChange'));
        $this->assertEquals(null, $registry->getEntry('lineCheckResult'));
        $this->assertEquals($promoCode, $registry->getEntry('promoCode'));
        $this->assertEquals(false, $registry->getEntry('c2mPromotion'));
        $this->assertEquals($backDatedDate, $registry->getEntry('backDatedDate'));
        $this->assertEquals($expectedHouseMoveOptionValue, $registry->getEntry('bolHousemove'));
    }

    /**
     * @return array
     */
    public function restoreAccountManagerHouseMoveProvider()
    {
        return [
            'scheduled House Move' => [
                'is scheduled house move' => true,
                'housemove set in options' => false,
                'expectedHouseMoveFlag' => true
            ],
            'House move set in options' => [
                'is scheduled house move' => false,
                'housemove set in options' => true,
                'expectedHouseMoveFlag' => true
            ],
            'No house move' => [
                'is scheduled house move' => false,
                'housemove set in options' => false,
                'expectedHouseMoveFlag' => false
            ]
        ];
    }

    /**
     * Test getMarketId market if linecheck found in DB
     *
     * @covers AccountChange_PerformChangeApi::getLineCheckFromDatabase
     */
    public function testsetMarketWhenLineCheckIdDB()
    {
        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'doLineCheck',
                'writeLineCheckResultToDb',
                'isCustomerUser',
                'getLineCheckByServiceId'
            ),
            array()
        );

        $mockLineCheckResults = $this->getMock(
            'LineCheck_Result',
            array('isValidResult')
        );

        $mockCoreService = $this->getMock(
            'Core_Service',
            array()
        );

        $mockLineCheckResults->expects($this->once())
            ->method('isValidResult')
            ->will($this->returnValue(true));

        $mockApi->expects($this->once())
            ->method('getLineCheckByServiceId')
            ->will($this->returnValue($mockLineCheckResults));

        $mockApi->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $mockApi->expects($this->never())
            ->method('doLineCheck');

        $result = $mockApi->getLineCheckFromDatabase($mockCoreService);

        $this->assertEquals($mockLineCheckResults, $result);
    }

    /**
     * Test getMarketId market if linecheck not found in DB and islineCheckNeededIfNotFound is false
     *
     * @covers AccountChange_PerformChangeApi::getLineCheckFromDatabase
     */
    public function testsetMarketWhenLineCheckIdNotInDB()
    {
        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'doLineCheck',
                'getLineCheckByServiceId',
                'writeLineCheckResultToDb',
                'icCustomerUser'
            ),
            array()
        );

        $mockCoreService = $this->getMock(
            'Core_Service',
            array()
        );

        $mockApi->expects($this->once())
            ->method('getLineCheckByServiceId')
            ->will($this->returnValue(null));

        $mockApi->expects($this->never())
            ->method('doLineCheck');

        $mockApi->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $result = $mockApi->getLineCheckFromDatabase($mockCoreService);

        $this->assertEquals($mockLineCheckResults, $result);
    }

    /**
     * Test getMarketId market if linecheck not found in DB and islineCheckNeededIfNotFound is true
     *
     * @covers AccountChange_PerformChangeApi::getLineCheckFromDatabase
     */
    public function testsetMarketWhenLineCheckIdNotInDBButRetryLineCheck()
    {
        $mockApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'doLineCheck',
                'getLineCheckByServiceId',
                'writeLineCheckResultToDb',
                'isCustomerUser'
            ),
            array()
        );

        $mockLineCheckResults = $this->getMock(
            'LineCheck_Result',
            array('isValidResult')
        );

        $mockCoreService = $this->getMock(
            'Core_Service',
            array()
        );

        $mockLineCheckResults->expects($this->at(0))
            ->method('isValidResult')
            ->will($this->returnValue(false));


        $mockLineCheckResults->expects($this->at(1))
            ->method('isValidResult')
            ->will($this->returnValue(true));


        $mockApi->expects($this->once())
            ->method('getLineCheckByServiceId')
            ->will($this->returnValue($mockLineCheckResults));

        $mockApi->expects($this->once())
            ->method('doLineCheck')
            ->will($this->returnValue($mockLineCheckResults));

        $mockApi->expects($this->once())
            ->method('writeLineCheckResultToDb');

        $mockApi->expects($this->any())
            ->method('isCustomerUser')
            ->will($this->returnValue(true));

        $mockApi->setLineCheckNeededIfNotFound(true);

        $result = $mockApi->getLineCheckFromDatabase($mockCoreService);
        $this->assertEquals($mockLineCheckResults, $result);
    }
}
