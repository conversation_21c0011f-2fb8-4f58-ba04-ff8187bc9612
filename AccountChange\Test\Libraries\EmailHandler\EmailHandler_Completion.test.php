<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_EmailHandler_CompletionTest extends PHPUnit_Framework_TestCase
{
    const BROADBAND_PRODUCT = 'Full Fibre 40 (PN)';
    const USER_REALM = "<EMAIL>";

    public function invokeMethod(&$object, $methodName, array $parameters = array())
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }

    /**
     * @param int $newWlrCompId comp id
     * @param array $arrSpeedData speed data
     * @param array $arrPriceData price data
     * @param array $arrContractData contract data
     * @param array $expectedArray expected details
     * @param boolean $optOut opt out
     * @dataProvider getDataForPopulateEmailVars
     */
    public function testPopulateEmailVars(
        $newWlrCompId,
        $arrSpeedData,
        $arrPriceData,
        $arrContractData,
        $expectedArray,
        $optOut = false
    ) {
        $mockPerformAccountChangeApi = $this->getPerformAccountChangeApiMock($newWlrCompId);
        $mockSpeed = $this->getSpeedHelperMock($arrSpeedData, $optOut);
        $mockPriceHelper = $this->getPriceHelperMock($arrPriceData);
        $mockContractHelper = $this->getContractHelper($arrContractData);
        $mockOrder = $this->getMockOrder($optOut);

        $mockEmailHandler = $this->getMock(
            'AccountChange_EmailHandler_Completion',
            array('adslGetUserRealm'),
            array(),
            '',
            false
        );

        $mockEmailHandler->expects($this->once())
            ->method('adslGetUserRealm')
            ->will($this->returnValue(static::USER_REALM));

        $mockEmailHandler->__construct(
            $mockPerformAccountChangeApi,
            $mockOrder,
            $mockSpeed,
            $mockPriceHelper,
            $mockContractHelper
        );

        $returnedArray = $this->invokeMethod($mockEmailHandler, "populateEmailVariables");

        $this->assertEquals($expectedArray, $returnedArray);
    }

    public function getDataForPopulateEmailVars()
    {
        return [
            [
                null,
                [
                    "minimumEstimatedDownloadSpeedMbs" =>  "1Mbps",
                    "maximumEstimatedDownloadSpeedMbs" => "20Mbps"
                ],
                [
                    "intDiscountLength" => null,
                    "discountAmount" => null,
                    "total" => "35.5"
                ],
                [
                    "intContractLengthInMonths" => 24,
                    "isContractRetained" => false,
                    "strContractEndDate" => "01-02-2023",
                    "isNewProductDualPlay" => true
                ],
                [
                    "strProduct" => static::BROADBAND_PRODUCT,
                    "estimatedSpeedRangeLow" => "1Mbps",
                    "estimatedSpeedRangeHigh" => "20Mbps",
                    "bolDisplayUsageInfo" => 0,
                    "strUserRealm" => static::USER_REALM,
                    "intContractLengthInMonths" => 24,
                    "isContractRetained" => 0,
                    "objContractEndDate" => "01-02-2023",
                    "isNewProductDualPlay" => 1,
                    "floDiscountedProductCost" => 0,
                    "floOngoingProductCost" => 35.5,
                    "intDiscountLength" => 0,
                    "bolOptoutMessage" => 0,
                    "bolHomePhone" => 0,
                    "strAuthenticationRealm" => static::USER_REALM,
                    "bolSpeedChecked" => 1,
                    "installationTypeFTTP" => 0,
                ]

            ],
            [
                1,
                [
                    "minimumEstimatedDownloadSpeedMbs" =>  "5Mbps",
                    "maximumEstimatedDownloadSpeedMbs" => "10Mbps"
                ],
                [
                    "intDiscountLength" => 9,
                    "discountAmount" => 10,
                    "total" => 35.5
                ],
                [
                    "intContractLengthInMonths" => 14,
                    "isContractRetained" => true,
                    "strContractEndDate" => "01-02-2023",
                    "isNewProductDualPlay" => true
                ],
                [
                    "strProduct" => static::BROADBAND_PRODUCT,
                    "estimatedSpeedRangeLow" => "5Mbps",
                    "estimatedSpeedRangeHigh" => "10Mbps",
                    "bolDisplayUsageInfo" => 0,
                    "strUserRealm" => static::USER_REALM,
                    "intContractLengthInMonths" => 14,
                    "isContractRetained" => 1,
                    "objContractEndDate" => "01-02-2023",
                    "isNewProductDualPlay" => 1,
                    "floDiscountedProductCost" => 25.5,
                    "floOngoingProductCost" => 35.5,
                    "intDiscountLength" => 9,
                    "bolOptoutMessage" => 1,
                    "bolHomePhone" => 1,
                    "strAuthenticationRealm" => static::USER_REALM,
                    "bolSpeedChecked" => 0,
                    "installationTypeFTTP" => 0
                ],
                true

            ]
        ];
    }

    /**
     * @param array $contractArray contract array
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getContractHelper($contractArray)
    {
        $mockContracthelper = $this->getMock(
            'AccountChange_CompleteContractHelper',
            array('getContractDetailsForEmail'),
            array(),
            '',
            false
        );


        $mockContracthelper->expects($this->once())
            ->method('getContractDetailsForEmail')
            ->will($this->returnValue($contractArray));

        return $mockContracthelper;
    }

    /**
     * @param boolean $optOut opt out
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getMockOrder($optOut)
    {
        $mockOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getServiceId', 'getLineCheckId'),
            array(),
            '',
            false
        );

        $mockOrder->expects($this->once())
            ->method('getServiceId')
            ->will($this->returnValue("1234"));

        $mockOrder->expects($this->once())
            ->method('getLineCheckId')
            ->will($this->returnValue("678912"));

        return $mockOrder;
    }

    /**
     * @param array $pricesArray prices array
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getPriceHelperMock($pricesArray)
    {
        $mockPriceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(),
            '',
            false
        );

        $mockPriceHelper->expects($this->once())
            ->method('getNewPackagePrices')
            ->will($this->returnValue($pricesArray));

        return $mockPriceHelper;
    }

    /**
     * @param array   $speedDataArray speed data
     * @param boolean $optOut         opt out
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getSpeedHelperMock($speedDataArray, $optOut)
    {
        $mockSpeedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array(
                'getSpeedData',
                'getOptOut'
                ),
            array(),
            '',
            false
        );

        $mockSpeedHelper->expects($this->once())
            ->method('getOptOut')
            ->will($this->returnValue($optOut));

        $mockSpeedHelper->expects($this->once())
            ->method('getSpeedData')
            ->will($this->returnValue($speedDataArray));

        return $mockSpeedHelper;
    }

    /**
     * @param int    $wlrCompId   wlr comp id
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function getPerformAccountChangeApiMock($wlrCompId)
    {
        $mockPerformAccountChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getNewProductName','getNewWlrComponentId', 'getProductDetailsForAccountChange', 'getTechnologyType'),
            array(),
            '',
            false
        );

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('getNewProductName')
            ->will($this->returnValue(static::BROADBAND_PRODUCT));

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('getNewWlrComponentId')
            ->will($this->returnValue($wlrCompId));

        return $mockPerformAccountChangeApi;
    }

    public function testGetEmailName()
    {
        $expectedEmailName = 'CUSTOMER_PRODUCT_CHANGE_COMPLETE';

        $mockEmailHandler = $this->getMock(
            'AccountChange_EmailHandler_Completion',
            array('adslGetUserRealm'),
            array(),
            '',
            false
        );

        $returnedEmailName = $mockEmailHandler->getEmailName();

        $this->assertEquals($expectedEmailName, $returnedEmailName);
    }

    public function testInstallationTypeIssetToFTTPInstallProcessWhenTechnologyTypeIsFTTP()
    {

        $mockPerformAccountChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getTechnologyType', 'getNewProductName', 'getNewWlrComponentId', 'getLineCheckResults' ),
            array(),
            '',
            false
        );

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('getTechnologyType')
            ->will($this->returnValue('FTTP'));

        $mockLineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getFttpInstallProcess'),
            array(),
            '',
            false
        );

        $mockLineCheckResult->expects($this->once())
            ->method('getFttpInstallProcess')
            ->will($this->returnValue('0'));

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('getLineCheckResults')
            ->will($this->returnValue($mockLineCheckResult));

        $mockEmailHandler = $this->getMock(
            'AccountChange_EmailHandler_Completion',
            array('adslGetUserRealm'),
            array(),
            '',
            false
        );

        $mockSpeedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array(
                'getSpeedData',
                'getOptOut'
            ),
            array(),
            '',
            false
        );

        $mockEmailHandler->__construct(
            $mockPerformAccountChangeApi,
            $this->getMockOrder($optOut),
            $mockSpeedHelper,
            $this->getPriceHelperMock([]),
            $this->getContractHelper([])
        );

    }
}
