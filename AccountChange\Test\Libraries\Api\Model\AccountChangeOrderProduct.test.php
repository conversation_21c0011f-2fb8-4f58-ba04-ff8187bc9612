<?php

class AccountChange_AccountChangeOrderProduct_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @test
     */
    public function shouldConvertDataCorrectly()
    {
        $products = [
            [
                'type' => 'POSTAGE_AND_PACKAGING',
                'selected' => true
            ],
            [
                'type' => 'BROADBAND',
                'serviceComponentId' => 1631
            ],
            [
                'type' => 'LINE_RENTAL',
                'selected' => true
            ],
            [
                'type' => 'CALL_PLAN',
                'serviceComponentId' => 1651
            ],
            [
                'type' => 'ROUTER',
                'serviceComponentId' => 1952
            ],
        ];


        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->buildProductsFromArray($products);

        $this->assertEquals(1631, $accountChangeOrderProducts->getServiceComponentId());
        $this->assertEquals(1651, $accountChangeOrderProducts->getPhoneComponentId());
        $this->assertEquals(1952, $accountChangeOrderProducts->getHardwareComponentId());
    }

    /**
     * @test
     */
    public function shouldOverrideCallPlanWithMobileVariantWhenMobileBoltOnSelectedTrue()
    {
        $boltOnMappings = array(
            array(
                'intServiceComponentID' => 1651,
                'intBoltOnServiceComponentID' => 1652
            )
        );

        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );

        $phoneHelperMock
            ->expects($this->once())
            ->method('getServiceComponentBoltOnMappings')
            ->will($this->returnValue($boltOnMappings));

        $accountChangeOrderProducts = $this->getMock(
            'AccountChange_AccountChangeOrderProducts',
            array('getPhoneHelper')
        );

        $accountChangeOrderProducts
            ->expects($this->once())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        $products = [
            [
                'type' => 'POSTAGE_AND_PACKAGING',
                'selected' => true
            ],
            [
                'type' => 'BROADBAND',
                'serviceComponentId' => 1631
            ],
            [
                'type' => 'LINE_RENTAL',
                'selected' => true
            ],
            [
                'type' => 'CALL_PLAN',
                'serviceComponentId' => 1651
            ],
            [
                'type' => 'ROUTER',
                'serviceComponentId' => 1952
            ],
            [
                'type' => 'MobileBoltOn',
                'selected' => true
            ]
        ];

        $accountChangeOrderProducts->buildProductsFromArray($products);

        $this->assertEquals(1652, $accountChangeOrderProducts->getPhoneComponentId());
    }

    /**
     * @test
     */
    public function shouldNotOverrideCallPlanWithMobileVariantWhenMobileBoltOnSelectedFalseAndCurrentProductIsNotMobile()
    {
        $boltOnMappings = array(
            array(
                'intServiceComponentID' => 1651,
                'intBoltOnServiceComponentID' => 1652
            )
        );

        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );

        $phoneHelperMock
            ->expects($this->once())
            ->method('getServiceComponentBoltOnMappings')
            ->will($this->returnValue($boltOnMappings));

        $accountChangeOrderProducts = $this->getMock(
            'AccountChange_AccountChangeOrderProducts',
            array('getPhoneHelper')
        );

        $accountChangeOrderProducts
            ->expects($this->once())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        $products = [
            [
                'type' => 'POSTAGE_AND_PACKAGING',
                'selected' => true
            ],
            [
                'type' => 'BROADBAND',
                'serviceComponentId' => 1631
            ],
            [
                'type' => 'LINE_RENTAL',
                'selected' => true
            ],
            [
                'type' => 'CALL_PLAN',
                'serviceComponentId' => 1651
            ],
            [
                'type' => 'ROUTER',
                'serviceComponentId' => 1952
            ],
            [
                'type' => 'MobileBoltOn',
                'selected' => false
            ]
        ];

        $accountChangeOrderProducts->buildProductsFromArray($products);

        $this->assertEquals(1651, $accountChangeOrderProducts->getPhoneComponentId());
    }

    /**
     * @test
     */
    public function shouldOverrideCallPlanWithNonMobileVariantWhenMobileBoltOnSelectedFalseAndCurrentProductIsMobile()
    {
        $boltOnMappings = array(
            array(
                'intServiceComponentID' => 1651,
                'intBoltOnServiceComponentID' => 1652
            )
        );

        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );

        $phoneHelperMock
            ->expects($this->once())
            ->method('getServiceComponentBoltOnMappings')
            ->will($this->returnValue($boltOnMappings));

        $accountChangeOrderProducts = $this->getMock(
            'AccountChange_AccountChangeOrderProducts',
            array('getPhoneHelper')
        );

        $accountChangeOrderProducts
            ->expects($this->once())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        $products = [
            [
                'type' => 'POSTAGE_AND_PACKAGING',
                'selected' => true
            ],
            [
                'type' => 'BROADBAND',
                'serviceComponentId' => 1631
            ],
            [
                'type' => 'LINE_RENTAL',
                'selected' => true
            ],
            [
                'type' => 'CALL_PLAN',
                'serviceComponentId' => 1652
            ],
            [
                'type' => 'ROUTER',
                'serviceComponentId' => 1952
            ],
            [
                'type' => 'MobileBoltOn',
                'selected' => false
            ]
        ];

        $accountChangeOrderProducts->buildProductsFromArray($products);

        $this->assertEquals(1651, $accountChangeOrderProducts->getPhoneComponentId());
    }
}