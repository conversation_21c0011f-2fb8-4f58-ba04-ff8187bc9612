<?php
require_once(__DIR__.'/PromotionRule.php');
require_once(__DIR__.'/PromotionRulesComposite.php');

/**
 * Class PromotionRules
 */
class PromotionRules implements PromotionRule, PromotionRulesComposite
{

    /**
     * Contains the promotion rules to apply to promotions
     *
     * @var array<PromotionRule>
     */
    private $rules;

    /**
     * Service id (required with some rules)
     *
     * @var int
     */
    private $serviceId;

    /**
     * @return int
     */
    public function getServiceId()
    {
        return $this->serviceId;
    }

    /**
     * @param int $serviceId
     */
    public function setServiceId($serviceId)
    {
        $this->serviceId = $serviceId;
    }

    /**
     * @param \PromotionRule $rule
     *
     * @return $this
     */
    public function addRule(PromotionRule $rule)
    {
        if (!isset($this->rules[get_class($rule)])) {
            $this->rules[get_class($rule)] = $rule;
        }
        if($this->rules[get_class($rule)]->getServiceId() != $this->getServiceId()) {
            $this->rules[get_class($rule)]->setServiceId($this->getServiceId());
        }
        return $this;
    }

    /**
     * @param \PromotionRule $rule
     *
     * @return $this
     */
    public function removeRule(PromotionRule $rule)
    {
        if (!isset($this->rules[get_class($rule)])) {
            return $this;
        }
        unset($this->rules[get_class($rule)]);

        return $this;
    }

    /**
     * For testing purposes
     *
     * @return array
     */
    public function getRules()
    {
        return $this->rules;
    }


    /**
     * @param \AccountChange_C2mPromotionsHelper $promotionHelper
     *
     * @return mixed
     */
    public function handle(array $promotions)
    {
        foreach ($this->rules as $rule) {
            $promotions = $rule->handle($promotions);
        }

        return $promotions;
    }
}
