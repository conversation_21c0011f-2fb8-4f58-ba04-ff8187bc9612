<?php
/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Action_RegisterChargesTest extends PHPUnit_Framework_TestCase
{
    const MOCK_INVOICE_ITEMS_ARRAY = [['description' => 'Invoice Item']];
    const MOCK_PAYMENT_HANDOVER_ID = '12345';
    const MOCK_SERVICE_ID = ********;

    /**
     * tearDown
     */
    public function tearDown()
    {
        \Mockery::close();
    }

    /**
     * @test
     * @dataProvider registerChargeTestDataProvider
     * @param $paymentHandoverId
     */
    public function shouldCorrectlyMakeCallsToRegisterOneTimeCharge($paymentHandoverId)
    {
        $reg = AccountChange_Registry::instance();
        $reg->reset();
        $reg->setEntry('intServiceId', static::MOCK_SERVICE_ID);
        $reg->setEntry('invoiceItems', static::MOCK_INVOICE_ITEMS_ARRAY);
        $reg->setEntry('paymentHandoverId', $paymentHandoverId);

        $test = $this->getTest();
        $mockOtcHandler = \Mockery::mock(Products_OneTimeCharge::class);

        $test
            ->shouldReceive('getOneTimeChargeObject')
            ->once()
            ->andReturn($mockOtcHandler);

        $mockOtcHandler
            ->shouldReceive('sendChargeGroup')
            ->once();

        $test->execute();
    }

    /**
     * @return array
     */
    public function registerChargeTestDataProvider()
    {
        return [
            'with Payment Handover Id' => [static::MOCK_PAYMENT_HANDOVER_ID],
            'without PaymentHandover Id' => [null]
        ];
    }

    /**
     * @test
     */
    public function shouldNotMakeCallsToRegisterOneTimeChargeIfInvoiceItemsNotSet()
    {
        $reg = AccountChange_Registry::instance();
        $reg->reset();

        $test = $this->getTest();

        $test
            ->shouldReceive('getOneTimeChargeObject')
            ->never();

        $test->execute();
    }

    /**
     * @return AccountChange_Action_RegisterCharges|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private function getTest()
    {
        return \Mockery::mock('AccountChange_Action_RegisterCharges[getOneTimeChargeObject]', [static::MOCK_SERVICE_ID, []])
            ->shouldAllowMockingProtectedMethods();
    }
}