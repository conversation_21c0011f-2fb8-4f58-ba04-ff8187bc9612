Server roles are the Framework's abstraction of the differnet servers farms we
run.

The ServerRoles directory tree configures which scheduled activities (cron
jobs) take place on a server performing a particular server role. 
The .cron files contained in this structure contain a single line to be inserted
into the relevant crontab. .cron file Entries within this directory tree will 
automatically be installed in thestandard clusterwide crontab by the install script.

The directory structure is organised to make it clear where, as whom and when a
script is supposed to run. When a script is no longer required to run in cron 
it's .cron entry should be removed from the ServerRoles structure.

directory structure:

<ServerRoles>
     <RoleName>
	<Cron Username>
              <OncePerCluster>
                  + scriptname.cron
	      <OnEachServer>
                  + scriptname.cron

