<?php
/**
 * File Product_WlrProductFilter
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://confluence.internal.plus.net/display/PRI/Call+plan+visibility+by+page
 */

/**
 * Context:
 *
 * We are introducing a new feature named Mobile bolt-on, which enables customers to buy additional mobile minutes
 * on top of their basic call package.
 *
 * At the time of implementing this feature and writing this filter class, there is no way for a customer to
 * have two different call plans on their accounts other than having a permutation of WLR products in the database
 * and switch the customers to the desired products based on their selections from Sales Journey or Member Centre
 * Product Change.
 *
 * For example, we would have an 'Anytime' and 'Anytime With Mobile' in the database but on the front-end
 * the customer only sees 'Anytime' and an option to select Mobile bolt-on. If the customer chooses this option,
 * we switch them internally to 'Anytime With Mobile'; if not, 'Anytime'.
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://confluence.internal.plus.net/display/PRI/Call+plan+visibility+by+page
 */
class AccountChange_Product_WlrProductFilter extends AccountChange_Product_WlrProductBase
{
    const FILTER_TYPE_WORKPLACE = 'WORKPLACE';
    const FILTER_TYPE_PORTAL = 'PORTAL';

    /**
     * Array of available penguin LRS products
     *
     * @var array
     **/
    private $penguinWlrLrsProducts = array(
        self::PENGUIN_ANYTIME_INTERNATIONAL,
        self::PENGUIN_ANYTIME_INTERNATIONAL_WITH_MOBILE,
        self::PENGUIN_ANYTIME,
        self::PENGUIN_ANYTIME_WITH_MOBILE,
        self::PENGUIN_EVENINGS_AND_WEEKENDS,
        self::PENGUIN_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
        self::PENGUIN_MOBILE,
        self::PENGUIN_LINE_ONLY,
        self::PENGUIN_LINE_ONLY_WITH_MOBILE,
        self::PENGUIN_WEEKENDS,
        self::PENGUIN_WEEKENDS_WITH_MOBILE
    );

    const FALCON_LEGACY_EVENINGS_AND_WEEKENDS = 670;
    const FALCON_LEGACY_ANYTIME = 919;
    const FALCON_LEGACY_INTERNATIONAL = 669;
    const FALCON_LEGACY_MOBILE_1 = 921;

    /**
     * Array of available legacy LRS products
     *
     * @var array
     **/
    private $legacyWlrLrsProducts = array(
        self::FALCON_LEGACY_EVENINGS_AND_WEEKENDS,
        self::FALCON_LEGACY_ANYTIME,
        self::FALCON_LEGACY_INTERNATIONAL,
        self::FALCON_LEGACY_MOBILE_1,
    );

    /**
     * Array of new products for easy look-up
     *
     * @var array
     */
    private $newProducts = array(
        self::NO_HOME_PHONE,
        self::JUNE2014_ANYTIME_INTERNATIONAL,
        self::JUNE2014_ANYTIME_INTERNATIONAL_WITH_MOBILE,
        self::JUNE2014_ANYTIME,
        self::JUNE2014_ANYTIME_WITH_MOBILE,
        self::JUNE2014_EVENINGS_AND_WEEKENDS,
        self::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
        self::JUNE2014_EVENINGS_AND_WEEKENDS_FREE,
        self::JUNE2014_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE,
        self::JUNE2014_MOBILE,
        self::JUNE2014_LINE_ONLY,
        self::JUNE2014_LINE_ONLY_WITH_MOBILE,
        self::JUNE2014_WEEKENDS,
        self::JUNE2014_WEEKENDS_WITH_MOBILE,
        self::JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
        self::JUNE2016_UNLIMITED_UK_WITH_MOBILE
    );

    /**
     * Type of filter to be applied
     *
     * @var array
     */
    private $filterType;

    /**
     * Constructor
     *
     * @param string $filterType Type of filter to be applied
     */
    public function __construct($filterType = self::FILTER_TYPE_PORTAL)
    {
        $this->filterType = $filterType;
    }

    /**
     * Return a list of all LRS based products
     *
     * @return array
     **/
    public function getLegacyWlrLrsProducts()
    {
        return array_merge($this->penguinWlrLrsProducts, $this->legacyWlrLrsProducts);
    }

    /**
     * This methods handles the rules on what should and what shouldn't
     * be displayed on the home phone selection page
     * during Member Centre Product Change.
     *
     * @param array $availableWlrProducts All the available WLR products in the database
     * @param int   $currentWlrProductId  Current WLR product
     * @param bool  $hasLrs               Indicates whether the customer has active LRS
     * @param bool  $isFibreProduct       Indicates that the product being processed is a fibre product.
     *
     * @throws InvalidArgumentException Thrown if the filter type has not been set to a recognised value.
     *
     * @return array $filteredProducts An array of filtered products ready for display
     */
    public function filterProducts(
        array $availableWlrProducts,
        $currentWlrProductId,
        $hasLrs = false,
        $isFibreProduct = false
    ) {
        switch ($this->filterType) {
            case self::FILTER_TYPE_PORTAL:
                return $this->filterForPortal(
                    $availableWlrProducts,
                    $currentWlrProductId,
                    $hasLrs,
                    $isFibreProduct
                );
                break;
            case self::FILTER_TYPE_WORKPLACE:
                return $this->filterForWorkplace(
                    $availableWlrProducts,
                    $currentWlrProductId,
                    $hasLrs,
                    $isFibreProduct
                );
                break;
            default:
                throw new InvalidArgumentException("Invalid filter type provided to 'filterProducts'");
        }
    }

    /**
     * Filter WLR products for portal. The filtering will results in an array that contains
     * only products that are of the same family as the current WLR product ID. It will also
     * include the no home phone option but only if the customer current does not have a
     * WLR component linked to their account.
     *
     * @param array $availableWlrProducts Available WLR products
     * @param int   $currentWlrProductId  Current WLR product id
     * @param bool  $hasLrs               Indicates whether the customer has active LRS
     * @param bool  $isFibreProduct       Indicates that the product being processed is a fibre product.
     *
     * @link http://confluence.internal.plus.net/display/PRI/Call+plan+visibility+by+page
     *
     * @throws InvalidArgumentException Thrown if the data related to one of the possible
     * products passed into the function is not found.
     *
     * @return array The filtered array of products.
     */
    protected function filterForPortal (
        array $availableWlrProducts,
        $currentWlrProductId,
        $hasLrs,
        $isFibreProduct
    ) {

        $filteredProducts = array();
        $standardProductForBoltOnProduct = $this->findProductForBoltOnVersion($currentWlrProductId);

        foreach ($availableWlrProducts as $availableWlrProduct) {
            $availableWlrProductId = $availableWlrProduct['intNewWlrId'];
            if (($availableWlrProductId == self::NO_HOME_PHONE)
                    && ($currentWlrProductId == self::NO_HOME_PHONE)
                    && !$this->filterDeprecated(
                        $currentWlrProductId,
                        $availableWlrProductId
                    )) {
                $filteredProducts[] = $availableWlrProduct;
                continue;
            }

            if ($this->shouldFilterForPortal($availableWlrProductId, $currentWlrProductId, $hasLrs, $isFibreProduct)) {
                continue;
            }

            /**
             * If the customer is on a WLR product with mobile bolt-on, for display purposes
             * We need to filter that product out and send the mapped product (without mobile bolt-on)
             * to the template
             *
             * Eg: If the customer has 'Anytime With Mobile',
             * display 'Anytime' with the Mobile bolt-on checkbox checked.
             */
            if ($standardProductForBoltOnProduct && $standardProductForBoltOnProduct == $availableWlrProductId) {
                $availableWlrProduct['mobileSelected'] = true;
            }
            $filteredProducts[] = $availableWlrProduct;
        }
        return $filteredProducts;
    }


    /**
     * Portal filtering
     *
     * @param int  $availableWlrProductId Available WLR products
     * @param int  $currentWlrProductId   Current WLR product id
     * @param bool $hasLrs                Indicates whether the customer has active LRS
     * @param bool $isFibreProduct        Indicates that the product being processed is a fibre product.
     *
     * @return array The filtered array of products.
     */
    private function shouldFilterForPortal(
        $availableWlrProductId,
        $currentWlrProductId,
        $hasLrs,
        $isFibreProduct
    ) {

        // Only filter if the current WLR product is not the product determined to be
        // the non-bolt on version of the current. This has the effect of including the
        // current product in the filtered list.
        if (!$this->familiesMatch($availableWlrProductId, $this->getProductFamily($currentWlrProductId))
            || ($hasLrs && $this->mlrOnly($availableWlrProductId))
            || (!$hasLrs && $this->lrsOnly($availableWlrProductId))
            || ($this->fibreOnly($availableWlrProductId) && !$isFibreProduct)
            || ($this->adslOnly($availableWlrProductId) && $isFibreProduct)
            && !($this->alwaysAvailableOnLRS($availableWlrProductId) && $hasLrs)) {

            return true;
        }

        if (!$this->availableInPortal($availableWlrProductId)) {
            return true;
        }

        if ($this->filterDeprecated($currentWlrProductId, $availableWlrProductId)) {
            return true;
        }

        if ($this->shouldFilterSupersedingProduct(
            $currentWlrProductId,
            $availableWlrProductId,
            $hasLrs,
            $isFibreProduct,
            self::FILTER_TYPE_PORTAL
        )) {
            return true;
        }
    }

    /**
     * In the portal, only non mobile versions of product are displayed with a check
     * box that activates the mobile version. This method will attempt to find a non
     * mobile version of the product when the ID passed in represents a mobile version.
     * Where the ID passed in is for a non-mobile version, null will be returned.
     *
     * @param int $currentWlrProductId The product ID
     *
     * @return int The ID of a non-mobile version of mobile product.
     */
    private function findProductForBoltOnVersion($currentWlrProductId)
    {
        $phoneHelper = $this->getPhoneHelper();
        $mobileBoltOnMapping = $phoneHelper->getServiceComponentIdToToBoltOnMap();
        $currentProductHasMobileBoltOn = in_array($currentWlrProductId, $mobileBoltOnMapping);
        $currentMappedWlrProductId = null;
        if ($currentProductHasMobileBoltOn) {
            $mappingProducts = array_flip($mobileBoltOnMapping);
            $currentMappedWlrProductId = $mappingProducts[$currentWlrProductId];
        }
        return $currentMappedWlrProductId;
    }

    /**
     * Filter WLR products for Workplace.
     *
     * The rules for this filter are:
     *
     * 1 - MLR: If the customer is on MLR (monthly line rental), the workplace user is
     * given the options to change the customer to any call plan for their current product
     * family or any product from the default call plan family. Line only should not be
     * included in the results. Mobile versions should be included.
     *
     * 2 - LRS: If the customer has paid for line rental saver, they can only change to
     * call plan products that are in the same family as the call plan that they currently
     * have. Mobile versions should be included.
     *
     * @param array $availableWlrProducts Available WLR products
     * @param int   $currentWlrProductId  Current WLR product id
     * @param bool  $hasLrs               Indicates whether the customer has active LRS
     * @param bool  $isFibreProduct       Indicates that the product being processed is a fibre product.
     *
     * @throws InvalidArgumentException Thrown if the data related to one of the possible
     * products passed into the function is not found.
     *
     * @return array The filtered list of call plan products.
     */
    protected function filterForWorkplace(
        array $availableWlrProducts,
        $currentWlrProductId,
        $hasLrs,
        $isFibreProduct
    ) {
        $filteredProducts = array();


        foreach ($availableWlrProducts as $availableWlrProduct) {
            $availableWlrProductId = $availableWlrProduct['intNewWlrId'];

            if ($this->shouldFilterForWorkplace(
                $availableWlrProductId,
                $currentWlrProductId,
                $hasLrs,
                $isFibreProduct
            )) {
                continue;
            }

            if ($this->isLineOnlyProduct($availableWlrProductId)) {
                $availableWlrProduct['isDefaultLrsProduct'] = true;
            }

            $filteredProducts[] = $availableWlrProduct;
        }
        return $filteredProducts;
    }

    /**
     * Workplace filtering
     *
     * @param int  $availableWlrProductId Available WLR products
     * @param int  $currentWlrProductId   Current WLR product id
     * @param bool $hasLrs                Indicates whether the customer has active LRS
     * @param bool $isFibreProduct        Indicates that the product being processed is a fibre product.
     *
     * @return array The filtered array of products.
     */
    protected function shouldFilterForWorkplace(
        $availableWlrProductId,
        $currentWlrProductId,
        $hasLrs,
        $isFibreProduct
    ) {

        $currentWlrProductFamily = $this->getProductFamily($currentWlrProductId);

        if (!$this->familiesMatch($availableWlrProductId, $currentWlrProductFamily)
            && !$this->familiesMatch($availableWlrProductId, self::DEFAULT_PRODUCT_FAMILY)) {
            return true;
        }

        if ($hasLrs && !$this->familiesMatch($availableWlrProductId, $currentWlrProductFamily)) {
            return true;
        }

        if (($hasLrs && $this->mlrOnly($availableWlrProductId))
            || (!$hasLrs && $this->lrsOnly($availableWlrProductId))) {
            return true;
        }

        if (!($this->alwaysAvailableOnLRS($availableWlrProductId) && $hasLrs)) {
            if ($this->fibreOnly($availableWlrProductId) && !$isFibreProduct) {
                return true;
            }
            if ($this->adslOnly($availableWlrProductId) && $isFibreProduct) {
                return true;
            }
        }

        if ($this->filterDeprecated($currentWlrProductId, $availableWlrProductId)) {
            return true;
        }

        // if we're on a deprecated product, don't display
        // the products that supersede it
        if ($this->shouldFilterSupersedingProduct(
            $currentWlrProductId,
            $availableWlrProductId,
            $hasLrs,
            $isFibreProduct,
            self::FILTER_TYPE_WORKPLACE
        )) {
            return true;
        }
    }


    /**
     * Filter superseding products for workplace if we are on a deprecated product
     *
     * @param int    $currentWlrProductId   Product ID that the user is currently on
     * @param int    $availableProductWlrId Product ID that is being filtered
     * @param int    $hasLrs                Has lrs
     * @param int    $isFibreProduct        Is fibre product
     * @param string $filterType            Workplace or Portal
     *
     * @return bool true for filter, false leave in
     */
    private function shouldFilterSupersedingProduct(
        $currentWlrProductId,
        $availableProductWlrId,
        $hasLrs,
        $isFibreProduct,
        $filterType
    ) {

        if ($currentWlrProductId == $availableProductWlrId) {
            return false;
        }

        if (!empty($this->products[(int)$availableProductWlrId]['newVariantOfExistingProductFamily'])) {
            return false;
        }


        // if we're filtering (removing) the current product, then don't filter its superseding product (if it has one)
        // this can potentially happen when moving between broadband products
        // e.g. we move to adsl weekends to fibre and we still want to display line-only in this case.

        if ($filterType == self::FILTER_TYPE_WORKPLACE
            && $this->shouldFilterForWorkplace($currentWlrProductId, $currentWlrProductId, $hasLrs, $isFibreProduct)
        ) {
            return false;
        } elseif ($filterType == self::FILTER_TYPE_PORTAL) {

            $standardProductForBoltOnProduct = $this->findProductForBoltOnVersion($currentWlrProductId);
            $currentWlrProductId = $standardProductForBoltOnProduct ?
                $standardProductForBoltOnProduct : $currentWlrProductId;

            if ($this->shouldFilterForPortal($currentWlrProductId, $currentWlrProductId, $hasLrs, $isFibreProduct)) {
                return false;
            }

        }

        if ($this->isSuperseded($currentWlrProductId, $availableProductWlrId)) {
            return true;
        }
    }


    /**
     * Tests if the current product has been superseded by another product
     *
     * @param int $currentWlrProductId   The available ID of the product that is being checked.
     * @param int $availableWlrProductId The current ID of the product that is being checked.
     *
     * @return int The id of the product family.
     */
    private function isSuperseded($currentWlrProductId, $availableWlrProductId)
    {
        $deprecatedBy = $this->isDeprecated($currentWlrProductId);

        if (!empty($deprecatedBy)
            && (in_array($availableWlrProductId, $deprecatedBy))
        ) {
            return true;
        }
        return false;
    }

    /**
     * Utility function that uses the hard coded data to determine the product
     * family for the product ID passed in. If the product ID is not recognised,
     * default the family to the current newest.
     *
     * @param int $wlrProductId The ID of the product that is being checked.
     *
     * @return int The id of the product family.
     */
    public function getProductFamily($wlrProductId)
    {
        if ($wlrProductId == null) {
            $wlrProductFamily = self::DEFAULT_PRODUCT_FAMILY;
        } elseif (!$this->wlrProductIdRecognised($wlrProductId)) {
            $wlrProductFamily = self::FAMILY_UNKNOWN;
        } else {
            $wlrProductFamily = $this->products[(int)$wlrProductId]['family'];
        }
        return $wlrProductFamily;
    }

    /**
     * Filter deprecated products
     *
     * If we're not currently on this plan
     * or a mobile or non-mobile version is available for this plan
     * as we want to allow user to switch between mobile and non-mobile
     * even though the plans are deprecated
     *
     * @param int $currentWlrProductId   Product ID that the user is currently on
     * @param int $availableProductWlrId Product ID that is being filtered
     *
     * @return bool true for filter out, false to leave in
     */
    private function filterDeprecated($currentWlrProductId, $availableProductWlrId)
    {
        if ($this->isDeprecated($availableProductWlrId)
            && !($this->isCurrentProduct($currentWlrProductId, $availableProductWlrId))
            && !($this->getMobileVersion($currentWlrProductId) == $availableProductWlrId
                || $this->getNonMobileVersion($currentWlrProductId) == $availableProductWlrId
                )
        ) {
            return true;
        }
        return false;
    }

    /**
     * Check if product is deprecated
     *
     * @param int $wlrProductId Product ID
     *
     * @return bool|array
     */
    public function isDeprecated($wlrProductId)
    {
        if (array_key_exists((int)$wlrProductId, $this->products)
                && array_key_exists('deprecatedBy', $this->products[(int)$wlrProductId])
                && !empty($this->products[(int)$wlrProductId]['deprecatedBy'])
        ) {

            // Return false if product isn't deprecated yet based on date.
            if (array_key_exists('deprecatedFromDate', $this->products[(int)$wlrProductId])) {
                $depFromDate = new DateTime($this->products[(int)$wlrProductId]['deprecatedFromDate']);
                $dateNow = $this->timeNow();
                if ($dateNow < $depFromDate) {
                    return false;
                }
            }

            return $this->products[(int)$wlrProductId]['deprecatedBy'];
        }
        return false;
    }

    /**
     * Get the current time using a DateTime object
     * as a function for injection during unit testing
     *
     * @return DateTime
     **/
    public function timeNow()
    {
        return new DateTime();
    }


    /**
     * Check if product has a mobile version, e.g. Anytime's would be Anytime with Mobiles
     *
     * @param int $wlrProductId Product ID
     *
     * @return bool|string
     */
    private function getMobileVersion($wlrProductId)
    {
        $mobileVersionOfProduct = $this->getProductProperty($wlrProductId, 'mobileVersion');
        if ($mobileVersionOfProduct === false) return false;

        if (!is_array($mobileVersionOfProduct)) {
            return $mobileVersionOfProduct;
        } else {
            return $this->getValueFromArray($this->filterType, $mobileVersionOfProduct);
        }
    }

    /**
     * Check if product has a non-mobile version, e.g. Anytime with Mobile's would be Anytime
     *
     * @param int $wlrProductId Product ID
     *
     * @return bool|string
     */
    private function getNonMobileVersion($wlrProductId)
    {
        $nonMobileVersionOfProduct = $this->getProductProperty($wlrProductId, 'nonMobileVersion');
        if ($nonMobileVersionOfProduct === false) return false;

        if (!is_array($nonMobileVersionOfProduct)) {
            return $nonMobileVersionOfProduct;
        } else {
            return $this->getValueFromArray($this->filterType, $nonMobileVersionOfProduct);
        }
    }

    /**
     * Search the products array for a product and get its property
     *
     * @param int $productId Product ID
     *
     * @return bool|array
     */
    private function getProductProperty($productId, $property)
    {
        $product = $this->getValueFromArray((int)$productId, $this->products);
        if ($product === false) return false;
        return $this->getValueFromArray($property, $product);
    }

    /**
     * Get a sensible value from an array (even if array key does not exist)
     *
     * @param string $key   Needle
     * @param array  $array Haystack
     *
     * @return false|string
     */
    private function getValueFromArray($key, $array)
    {
        if (array_key_exists($key, $array)
        ) {
            return $array[$key];
        }
        return false;
    }

    /**
     * Check if product is free
     *
     * @param int $wlrProductId Product ID
     *
     * @return bool
     */
    public function isFreeProduct($wlrProductId)
    {
        if (array_key_exists((int)$wlrProductId, $this->products)
            && array_key_exists('isFreeProduct', $this->products[(int)$wlrProductId])) {
            return $this->products[(int)$wlrProductId]['isFreeProduct'];
        }
        return false;
    }

    /**
     * Checks to see if the product is available in portal based on the hard
     * coded meta data.
     *
     * @param int $wlrProductId The ID of the potential product.
     *
     * @return bool True if this product is allowed to be displayed in portal
     */
    private function availableInPortal($wlrProductId)
    {
        return !array_key_exists((int)$wlrProductId, $this->products)
        || $this->products[(int)$wlrProductId]['availableOnPortal'];
    }

    /**
     * Checks to see if the product ID passed in is only available in when
     * the customer is on monthly line rental.
     *
     * @param int $wlrProductId The product ID
     *
     * @return bool True if the product is monthly line rental only.
     */
    public function mlrOnly($wlrProductId)
    {
        return array_key_exists((int)$wlrProductId, $this->products)
        && $this->products[(int)$wlrProductId]['mlrOnly'];
    }

    /**
     * Checks to see if the product ID passed in is only available when
     * the customer is using LRS (line rental saver).
     *
     * @param int $wlrProductId The product ID
     *
     * @return bool True if the product is restricted to LRS.
     */
    public function lrsOnly($wlrProductId)
    {
        return array_key_exists((int)$wlrProductId, $this->products)
        && $this->products[(int)$wlrProductId]['lrsOnly'];
    }

    /**
     * Checks to see if the product ID passed in is only available when
     * the customer has selected a Fibre product.
     *
     * @param int $wlrProductId The WLR product ID
     *
     * @return bool True if the product is restricted to Fibre products.
     */
    private function fibreOnly($wlrProductId)
    {
        return array_key_exists((int)$wlrProductId, $this->products)
        && $this->products[(int)$wlrProductId]['fibreOnly'];
    }

    /**
     * Check to see if the flag is set that will force the product to
     * appear when LRS is selected.
     *
     * @param int $wlrProductId The WLR product ID
     *
     * @return bool True if this product should always appear when LRS is chosen
     */
    private function alwaysAvailableOnLRS($wlrProductId)
    {
        return array_key_exists((int)$wlrProductId, $this->products)
        && $this->products[(int)$wlrProductId]['alwaysShowOnLRS'];
    }

    /**
     * Checks to see if the product ID passed is only available when
     * the customer has selected ADSL.
     *
     * @param int $wlrProductId The WLR product ID
     *
     * @return bool True if the product is restricted to ADSL products.
     */
    private function adslOnly($wlrProductId)
    {
        return array_key_exists((int)$wlrProductId, $this->products)
        && $this->products[(int)$wlrProductId]['adslOnly'];
    }

    /**
     * Check to see if this product is a standard line only product i.e.
     * does not include a call plan.
     *
     * @param int $wlrProductId The product ID
     *
     * @return bool True is the product is a LRS line only product.
     */
    public function isLineOnlyProduct($wlrProductId)
    {
        return array_key_exists((int)$wlrProductId, $this->products)
        && $this->products[(int)$wlrProductId]['isLineOnlyProduct'];
    }

    /**
     * This is a test to see if a certain product ID is contained
     * in the hard-coded data for products.
     *
     * @param int $wlrProductId The product ID.
     *
     * @return bool True if the product ID is contained in data.
     */
    private function wlrProductIdRecognised($wlrProductId)
    {
        return array_key_exists((int)$wlrProductId, $this->products);
    }

    /**
     * Are we offering a new product when currently on an old one?
     *
     * @param array $products     List of products (which are also arrays)
     * @param int   $oldProductId ID of the old product
     *
     * @return boolean is this possibly a old->new change
     */
    public function isOldToNewList(array $products, $oldProductId)
    {
        foreach ($products as $product) {
            if ($this->isOldToNew($product, $oldProductId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Is this product new and the current one old?
     *
     * @param array $product      Product item
     * @param int   $oldProductId ID of the old product
     *
     * @return boolean is this an old->new
     */
    public function isOldToNew(array $product, $oldProductId)
    {
        $oldProductId = (int) $oldProductId;
        $newProductId = (int) $product['intNewWlrId'];
        if ($newProductId && in_array($newProductId, $this->newProducts)) {
            return $oldProductId &&
            !in_array($oldProductId, $this->newProducts);
        }
        return false;
    }

    /**
     * Return the mobile bolt-on mapping array
     *
     * @return array
     */
    public function getMobileBoltOnMapping()
    {
        $phoneHelper = $this->getPhoneHelper();
        return  $phoneHelper->getServiceComponentIdToToBoltOnMap();
    }

    /**
     * Return the "* With Mobile" equivalent of a WLR product
     *
     * @param int $wlrProductId Wlr product Id
     *
     * @return int Id of the mapped product, null otherwise
     */
    public function getMappedMobileProduct($wlrProductId)
    {
        $phoneHelper = $this->getPhoneHelper();
        return $phoneHelper->getMappedMobileProduct($wlrProductId);
    }

    /**
     * @return AccountChange_PhoneProductHelper
     */
    protected function getPhoneHelper()
    {
        return new AccountChange_PhoneProductHelper();
    }

    /**
     * @param $currentWlrProductId
     * @param $availableProductWlrId
     * @return bool
     */
    private function isCurrentProduct($currentWlrProductId, $availableProductWlrId)
    {
        return $currentWlrProductId == $availableProductWlrId;
    }
}
