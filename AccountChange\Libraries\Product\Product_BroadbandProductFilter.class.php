<?php

/**
 * File Product_BroadbandProductFilter
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://confluence.internal.plus.net/display/PRI/Solus+and+Dual+Play+Product+Combinations
 */

/**
 * Class to filter available broadband products for display in Workplace Account Change tool.
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://confluence.internal.plus.net/display/PRI/Solus+and+Dual+Play+Product+Combinations
 */
class AccountChange_Product_BroadbandProductFilter extends AccountChange_PerformChangeBase
{
    const FILTER_TYPE_WORKPLACE = 'WORKPLACE';
    const FILTER_TYPE_HOUSEMOVE = 'HOUSEMOVE';
    const FILTER_TYPE_PORTAL = 'PORTAL';
    const FILTER_TYPE_KEEP_CURRENT_PRODUCT_ONLY = 'KEEP_CURRENT_PRODUCT_ONLY';
    const APR_14_FIBRE_EXTRA_WITH_PHONE = 6867;
    const APR_14_FIBRE_EXTRA_NO_PHONE = 6863;


    /**
     * Method to filter products
     *
     * @param LineCheck_Result $lineCheck         object representing the line check
     * @param array            $availableProducts Array of available products
     * @param array            $currentProduct    Array of the current product
     * @param string           $filterType        Which filter to run of type AccountChange_Product_BroadbandProductFilter::FILTER_TYPE
     * @param string           $campaignCode      Campaign code indicating which landing page this journey originated from, if any
     *
     * @return array
     */
    public static function filter(
        LineCheck_Result $lineCheck,
        array $availableProducts,
        array $currentProduct,
        $filterType,
        $campaignCode = ''
    ) {
        /** @var AccountChange_Product_BroadbandProductFilter_Common $filter */
        $filter = null;
        switch ($filterType) {
            case self::FILTER_TYPE_WORKPLACE:
                $filter = new AccountChange_Product_BroadbandProductFilter_Workplace();
                break;
            case self::FILTER_TYPE_PORTAL:
                $filter = new AccountChange_Product_BroadbandProductFilter_Portal();
                break;
            case self::FILTER_TYPE_HOUSEMOVE:
                $filter = new AccountChange_Product_BroadbandProductFilter_Housemove();
                break;
            case self::FILTER_TYPE_KEEP_CURRENT_PRODUCT_ONLY:
                $filter = new AccountChange_Product_BroadbandProductFilter_KeepCurrentProductOnly();
                break;
            default:
                throw new InvalidArgumentException("Invalid filter type provided");
        }
        return $filter->filter($lineCheck, $availableProducts, $currentProduct, $campaignCode);
    }
}
