<?php
/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_BackDatedDatePolicyTest extends PHPUnit_Framework_TestCase
{
    /**
     * tearDown
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @test
     * @dataProvider backDatedDateProvider
     * @param bool   $isScheduled                        is it a scheduled change
     * @param string $backDatedDateModifier              modifier to create backdated datetime
     * @param int    $daysDifferenceFromLastServiceEvent db return value
     * @param bool   $expectedResult                     will the validation pass
     * @param string $expectedErrorMessage               expected error message
     * @param bool   $willQueryDb                        will test query the db
     * @throws Db_TransactionException
     */
    public function shouldValidateCorrectly(
        $isScheduled,
        $backDatedDateModifier,
        $daysDifferenceFromLastServiceEvent,
        $expectedResult,
        $expectedErrorMessage,
        $willQueryDb
    ) {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andR<PERSON>urn(123345);
        $dbMock = Mockery::mock('Db_Adaptor');

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        if ($willQueryDb) {
            $dbMock
                ->shouldReceive('getDaysDiffOfPreviousProductChange')
                ->once()
                ->andReturn($daysDifferenceFromLastServiceEvent);
        } else {
            $dbMock
                ->shouldReceive('getDaysDiffOfPreviousProductChange')
                ->never();
        }

        $backDatedDate = (new DateTime('today midnight'))->modify($backDatedDateModifier);

        $additionalInfo = [
            'backDatedDate' => $backDatedDate,
            'isScheduledChange' => $isScheduled
        ];

        $test = new AccountChange_BackDatedDatePolicy($actor, true, false, $additionalInfo);

        if ($expectedResult) {
            $this->assertTrue($test->validate());
        } else {
            $this->assertFalse($test->validate());
            $this->assertEquals($expectedErrorMessage, $test->getFailure());
            $this->assertEquals(AccountChange_BackDatedDatePolicy::ERROR_CODE, $test->getErrorCode());
        }
    }

    /**
     * @return array
     */
    public function backDatedDateProvider()
    {
        return [
            'Valid' => [
                'is scheduled' => false,
                'backdated date modifier' => '-10 days',
                'days diff from last event' => '20',
                'expectedResult' => true,
                'expectedError' => null,
                'willQueryDb' => true
            ],
            'Scheduled' => [
                'is scheduled' => true,
                'backdated date modifier' => '-10 days',
                'days diff from last event' => '20',
                'expectedResult' => false,
                'expectedError' => AccountChange_BackDatedDatePolicy::ERROR_MESSAGE_NOT_INSTANT,
                'willQueryDb' => false
            ],
            'Not in past' => [
                'is scheduled' => false,
                'backdated date modifier' => '-0 days',
                'days diff from last event' => '10',
                'expectedResult' => false,
                'expectedError' => AccountChange_BackDatedDatePolicy::ERROR_MESSAGE_NOT_IN_PAST,
                'willQueryDb' => false
            ],
            'Before most recent service event' => [
                'is scheduled' => false,
                'backdated date modifier' => '-20 days',
                'days diff from last event' => 10,
                'expectedResult' => false,
                'expectedError' => AccountChange_BackDatedDatePolicy::ERROR_MESSAGE_BEFORE_MOST_RECENT_SERVICE_EVENT,
                'willQueryDb' => true
            ]
        ];
    }

    /**
     * @test
     * @dataProvider missingInfoProvider
     */
    public function shouldReturnFalseIfAdditionalInfoIsNotSet(
        $additionalInfo,
        $expectedErrorMessage
    ) {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(123345);

        $test = new AccountChange_BackDatedDatePolicy($actor, true, false, $additionalInfo);

        $this->assertFalse($test->validate());
        $this->assertEquals($expectedErrorMessage, $test->getFailure());
        $this->assertEquals(AccountChange_BackDatedDatePolicy::ERROR_CODE, $test->getErrorCode());
    }

    /**
     * @return array
     */
    public function missingInfoProvider()
    {
        return [
            'no backdated date' => [
                ['isScheduledChange' => false],
                'expectedError' => sprintf(
                    AccountChange_BackDatedDatePolicy::ERROR_MESSAGE_INVALID_VALUE,
                    'backDatedDate'
                )
            ],
            'invalid backdated date' => [
                ['backDatedDate' => 'a string', 'isScheduledChange' => false],
                'expectedError' => sprintf(
                    AccountChange_BackDatedDatePolicy::ERROR_MESSAGE_INVALID_VALUE,
                    'backDatedDate'
                )
            ],
            'No scheduled change value' => [
                ['backDatedDate' => new DateTime()],
                'expectedError' => sprintf(
                    AccountChange_BackDatedDatePolicy::ERROR_MESSAGE_INVALID_VALUE,
                    'isScheduledChange'
                )
            ],
            'Invalid scheduled change value' => [
                ['backDatedDate' => new DateTime(), 'isScheduledChange' => 10],
                'expectedError' => sprintf(
                    AccountChange_BackDatedDatePolicy::ERROR_MESSAGE_INVALID_VALUE,
                    'isScheduledChange'
                )
            ],
        ];
    }
}
