<?php
require_once(__DIR__.'/../../../Libraries/Rules/PromotionRules.php');
require_once(__DIR__.'/../../../Libraries/Rules/PromotionRule.php');
require_once(__DIR__.'/../../../Libraries/Rules/RemoveExpiredPromotions.php');
require_once(__DIR__.'/../../../Libraries/Rules/RemoveNonThroughTheLinePromotions.php');

class PromotionRulesTest extends PHPUnit_Framework_TestCase
{

    public function testAddingPromotionRules()
    {
        $removeExpired = new RemoveExpiredPromotions();
        $removeNonThruLine = new RemoveNonThroughTheLinePromotions();
        $removeExpiredDuplicate = new RemoveExpiredPromotions();

        $promotionRules = new PromotionRules();
        $promotionRules
          ->addRule($removeExpired)
          ->addRule($removeNonThruLine)
          ->addRule($removeExpiredDuplicate);


        $this->assertEquals(2, count($promotionRules->getRules()));
        $this->assertArrayHasKey(
          'RemoveExpiredPromotions',
          $promotionRules->getRules()
        );
        $this->assertArrayHasKey(
          'RemoveNonThroughTheLinePromotions',
          $promotionRules->getRules()
        );

    }

    public function testRemovingPromotionRules()
    {
        $removeExpired = new RemoveExpiredPromotions();
        $removeNonThruLine = new RemoveNonThroughTheLinePromotions();


        $promotionRules = new PromotionRules();
        $promotionRules
          ->addRule($removeExpired)
          ->addRule($removeNonThruLine);

        $promotionRules->removeRule($removeExpired);

        $this->assertEquals(1, count($promotionRules->getRules()));
        $this->assertArrayHasKey(
          'RemoveNonThroughTheLinePromotions',
          $promotionRules->getRules()
        );

    }

    public function testRemovingPromotionRuleTwiceReturnsPromotionRulesObject()
    {
        $removeExpired = new RemoveExpiredPromotions();
        $removeNonThruLine = new RemoveNonThroughTheLinePromotions();

        $promotionRules = new PromotionRules();
        $promotionRules
          ->addRule($removeExpired)
          ->addRule($removeNonThruLine);

        $promotionRules->removeRule($removeExpired);
        $promotionRule = $promotionRules->removeRule($removeExpired);

        $this->assertEquals(1, count($promotionRules->getRules()));
        $this->assertEquals('PromotionRules', get_class($promotionRule));
        $this->assertArrayHasKey(
          'RemoveNonThroughTheLinePromotions',
          $promotionRules->getRules()
        );

    }

    public function testEachHandlerMethodIsCalled()
    {
        $mockRemoveExpiredPromotions = $this->getMockBuilder(
          RemoveExpiredPromotions::class
        )->setMethods(['handle'])
          ->getMock();

        $mockRemoveExpiredPromotions->expects($this->once())
          ->method('handle')
          ->willReturn([]);

        $mockRemoveNonThroughTheLinePromotions = $this->getMockBuilder(
          RemoveNonThroughTheLinePromotions::class
        )->setMethods(['handle'])
          ->getMock();

        $mockRemoveNonThroughTheLinePromotions->expects($this->once())
          ->method('handle')
          ->willReturn([]);


        $promotionRules = new PromotionRules();
        $promotionRules
          ->addRule($mockRemoveExpiredPromotions)
          ->addRule($mockRemoveNonThroughTheLinePromotions);

        $promotions = $promotionRules->handle([]);

        $this->assertEquals(0, count($promotions));

    }


}
