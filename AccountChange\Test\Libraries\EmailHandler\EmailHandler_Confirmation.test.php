<?php

/**
 * Class AccountChange_EmailHandler_ConfirmationTest
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_EmailHandler_ConfirmationTest extends PHPUnit_Framework_TestCase
{

    public function invokeMethod(&$object, $methodName, array $parameters = array())
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }

    /**
     * @throws Db_TransactionException
     * @dataProvider getDataForTestPopulateEmailVars
     */
    public function testPopulateEmailVars(
        $data,
        $expectedEmailVars
    ) {
        $techType = $data['techType'];
        $callPlanArray = array("strDisplayName" => $data['callPlan']);
        $componentArray = array("strName" => $data['hardware']);
        $pricesArray = array("total" => $data['price']);
        $wlrInformationArray = array();
        $speedDataArray = array(
            "minimumEstimatedDownloadSpeedMbs" => $data['estimatedDownloadSpeedRangeLower'],
            "maximumEstimatedDownloadSpeedMbs" => $data['estimatedDownloadSpeedRangeHigher'],
            "guaranteedSpeedValue" => $data['minimumGuaranteedSpeed'],
            "advertisedDownloadSpeedMbs" => $data['advertisedDownloadSpeed'],
            "maxDownSpeed" => $data['maximumDownloadSpeed'],
            "minUpSpeed" => $data['minimumUploadSpeed'],
            "maxUpSpeed" => $data['maximumUploadSpeed'],
            "minimumEstimatedUploadSpeedMbs" => $data['estimatedUploadSpeedRangeLower'],
            "maximumEstimatedUploadSpeedMbs" => $data['estimatedUploadSpeedRangeHigher'],
            "advertisedUploadSpeedMbs" => $data['advertisedUploadSpeed']
        );

        $mockPerformAccountChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getNewProductName',
                'isProductChangeForHouseMove',
                'getTechnologyType',
                'getLineCheckResults',
                'getAppointment',
                'getServiceId',
                'getScheduledChangeDate',
                'getNewAccessTechnology',
                'getCurrentAccessTechnology'
            ),
            array(),
            '',
            false
        );

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('getNewProductName')
            ->will($this->returnValue($data['broadbandProduct']));

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('getScheduledChangeDate')
            ->will($this->returnValue($data['activation']));

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('isProductChangeForHouseMove')
            ->will($this->returnValue($data['houseMove']));

        $mockPerformAccountChangeApi->expects($this->once())
            ->method('getTechnologyType')
            ->will($this->returnValue($techType));

        $mockPerformAccountChangeApi
            ->expects($this->once())
            ->method('getCurrentAccessTechnology')
            ->will($this->returnValue($data['currentAccessTechnology']));

        $mockPerformAccountChangeApi
            ->expects($this->exactly(2))
            ->method('getNewAccessTechnology')
            ->will($this->returnValue($data['newAccessTechnology']));

        $mockLineCheckResults = $this->getMock(
            'LineCheck_Result',
            array('getFttpInstallProcess'),
            array(),
            '',
            false
        );

        $mockLineCheckResults->expects($this->exactly($techType == 'FTTP' ? 1 : 0))
            ->method('getFttpInstallProcess')
            ->will($this->returnValue($data['installationTypeFTTP']));

        $mockPerformAccountChangeApi->expects($this->atMost($techType == 'FTTP' ? 2 : 0))
            ->method('getLineCheckResults')
            ->will($this->returnValue($mockLineCheckResults));

        $mockOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getContract', 'getProducts', 'isRetainCurrentContracts'),
            array(),
            '',
            false
        );

        $mockOrderContract = $this->getMock(
            'AccountChange_AccountChangeOrderContract',
            array('getLength'),
            array(),
            '',
            false
        );

        $mockOrderContract->expects($this->any())
            ->method('getLength')
            ->will($this->returnValue($data['contractLength']));

        $mockOrder->expects($this->any())
            ->method('getContract')
            ->will($this->returnValue($mockOrderContract));

        $mockOrder->expects($this->once())
            ->method('isRetainCurrentContracts')
            ->will($this->returnValue($data['isRetain']));

        $mockOrderProducts = $this->getMock(
            'AccountChange_AccountChangeOrderProducts',
            array('getPhoneComponentId', 'getHardwareComponentId'),
            array(),
            '',
            false
        );

        $mockOrderProducts->expects($this->once())
            ->method('getPhoneComponentId')
            ->will($this->returnValue($data['phoneComp']));

        $mockOrderProducts->expects($this->once())
            ->method('getHardwareComponentId')
            ->will($this->returnValue($data['hardwareCompId']));

        $mockOrder->expects($this->any())
            ->method('getProducts')
            ->will($this->returnValue($mockOrderProducts));

        $mockPriceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(),
            '',
            false
        );

        $mockPriceHelper->expects($this->once())
            ->method('getNewPackagePrices')
            ->will($this->returnValue($pricesArray));

        $mockAccount = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation','getNextInvoiceDate'),
            array(),
            '',
            false
        );

        $mockAccount->expects($this->once())
            ->method('getWlrInformation')
            ->will($this->returnValue($wlrInformationArray));

        $mockAccount->expects($this->once())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue($data['nextBillingDate']));

        $mockSpeedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array('getSpeedData'),
            array(),
            '',
            false
        );

        $mockSpeedHelper->expects($this->once())
            ->method('getSpeedData')
            ->will($this->returnValue($speedDataArray));

        $mockDataHelper = $this->getMock(
            'AccountChange_EmailHandler_DataHelper',
            array('getCallPlanDetailsByWlrServiceComponentId'),
            array(),
            '',
            false
        );

        $mockDataHelper->expects($this->exactly(is_null($data['phoneComp']) ? 0 : 1))
            ->method('getCallPlanDetailsByWlrServiceComponentId')
            ->with($data['phoneComp'])
            ->will($this->returnValue($callPlanArray));

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForComponentType'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockDbAdaptor->expects($this->exactly(1))
            ->method('getServiceComponentDetailsForComponentType')
            ->with($data['hardwareCompId'])
            ->will($this->returnValue($componentArray));

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $workplaceEmailHandler = new AccountChange_EmailHandler_Confirmation(
            $mockPerformAccountChangeApi,
            $mockOrder,
            $mockPriceHelper,
            $mockAccount,
            $mockSpeedHelper,
            $mockDataHelper
        );

        $returnedArray = $this->invokeMethod($workplaceEmailHandler, "populateEmailVariables");

        $this->assertEquals($expectedEmailVars, $returnedArray);
    }

    public function getDataForTestPopulateEmailVars()
    {
        $expectedMinExpectedSpeed = "3";
        $expectedMaxExpectedSpeed = "40";
        $expectedBroadbandSpeed = "38";
        $expectedGuaranteedSpeedValue = "30";
        $expectedAdvertisedDownloadSpeedMbs = "33";
        $expectedAdvertisedUploadSpeedMbs = "29";
        $minimumUploadSpeed = "2";
        $maximumUploadSpeed = "10";
        $maximumDownloadSpeed = "330";
        $estimatedUploadSpeedRangeHigher = "300";
        $estimatedUploadSpeedRangeLower = "41";
        $minimumGuaranteedSpeed = "26";

        return array(
            array(
                "data" => array(
                    'broadbandProduct' => 'Full Fibre 40',
                    'houseMove' => false,
                    'techType' => 'FTTP',
                    'installationTypeFTTP' => 'Stage 1',
                    'liveAppointment' => true,
                    'contractLength' => 12,
                    'isRetain' => false,
                    'hardwareCompId' => 2343,
                    'phoneComp' => null,
                    'nextBillingDate' => "2021-02-21",
                    'callPlan' => '',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => $expectedBroadbandSpeed,
                    "minimumGuaranteedSpeedAvailable" => $expectedGuaranteedSpeedValue,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "price" => '22.34',
                    "activation" => '2021-02-02',
                    "hardware" => "hub 2",
                    "currentAccessTechnology" => "ADSL",
                    "newAccessTechnology" => "FTTP"
                ),
                "expectedArray" => array(
                    "broadbandProduct" => 'Full Fibre 40',
                    "contractLength" => 12,
                    "phoneProduct" => 0,
                    "hardware" => "hub 2",
                    "houseMove" => 0,
                    "newMonthlyCost" => "22.34",
                    "installationTypeFTTP" => 'Stage 1',
                    "targetActivationDate" => '2021-02-02',
                    "nextBillingDate" => '2021-02-21',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => 1,
                    "minimumGuaranteedSpeedAvailable" => 1,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "accessTechnologyChanging" => 1,
                    "isSogea" => 0
                )
            ),
            array(
                "data" => array(
                    'broadbandProduct' => 'Unlimited',
                    'houseMove' => false,
                    'techType' => 'ADSL',
                    'installationTypeFTTP' => LineCheck_Result::STAGE_1,
                    'liveAppointment' => true,
                    'contractLength' => 12,
                    'isRetain' => false,
                    'hardwareCompId' => 2343,
                    'phoneComp' => 23,
                    'nextBillingDate' => "2021-02-21",
                    'callPlan' => 'Anytime',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => $expectedBroadbandSpeed,
                    "minimumGuaranteedSpeedAvailable" => $expectedGuaranteedSpeedValue,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "price" => '22.34',
                    "activation" => '2021-02-20',
                    "hardware" => "hub 1",
                    "currentAccessTechnology" => "ADSL",
                    "newAccessTechnology" => "ADSL"
                ),
                "expectedArray" => array(
                    "broadbandProduct" => 'Unlimited',
                    "contractLength" => 12,
                    "phoneProduct" => "Anytime",
                    "hardware" => "hub 1",
                    "houseMove" => 0,
                    "newMonthlyCost" => "22.34",
                    "installationTypeFTTP" => 0,
                    "targetActivationDate" => '2021-02-20',
                    "nextBillingDate" => '2021-02-21',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => 1,
                    "minimumGuaranteedSpeedAvailable" => 1,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "accessTechnologyChanging" => 0,
                    "isSogea" => 0
                )
            ),
            array(
                "data" => array(
                    'broadbandProduct' => 'Full Fibre 40',
                    'houseMove' => false,
                    'techType' => 'FTTP',
                    'installationTypeFTTP' => LineCheck_Result::REMOTE_ACTIVATION,
                    'workingDays' => 5,
                    'contractLength' => 12,
                    'isRetain' => false,
                    'hardwareCompId' => 2343,
                    'phoneComp' => null,
                    'nextBillingDate' => "2021-02-21",
                    'callPlan' => '',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => $expectedBroadbandSpeed,
                    "minimumGuaranteedSpeedAvailable" => $expectedGuaranteedSpeedValue,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "price" => '22.34',
                    "activation" => '2021-11-12',
                    "hardware" => "hub 2",
                    "currentAccessTechnology" => "FTTP",
                    "newAccessTechnology" => "FTTP"
                ),
                    "expectedArray" => array(
                    "broadbandProduct" => 'Full Fibre 40',
                    "contractLength" => 12,
                    "phoneProduct" => 0,
                    "hardware" => "hub 2",
                    "houseMove" => 0,
                    "newMonthlyCost" => "22.34",
                    "installationTypeFTTP" => LineCheck_Result::REMOTE_ACTIVATION,
                    "targetActivationDate" => '2021-11-12',
                    "nextBillingDate" => '2021-02-21',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => 1,
                    "minimumGuaranteedSpeedAvailable" => 1,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "accessTechnologyChanging" => 0,
                    "isSogea" => 0
                )
            ),
            array(
                "data" => array(
                    'broadbandProduct' => 'Full Fibre 40',
                    'houseMove' => false,
                    'techType' => 'FTTP',
                    'installationTypeFTTP' => LineCheck_Result::STAGE_1,
                    'contractLength' => 12,
                    'isRetain' => false,
                    'hardwareCompId' => 2343,
                    'phoneComp' => null,
                    'nextBillingDate' => "2021-02-21",
                    'callPlan' => '',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => $expectedBroadbandSpeed,
                    "minimumGuaranteedSpeedAvailable" => $expectedGuaranteedSpeedValue,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "price" => '22.34',
                    "activation" => '2021-02-02',
                    "hardware" => "hub 2",
                    "currentAccessTechnology" => "ADSL",
                    "newAccessTechnology" => "FTTP"
                ),
                "expectedArray" => array(
                    "broadbandProduct" => 'Full Fibre 40',
                    "contractLength" => 12,
                    "phoneProduct" => 0,
                    "hardware" => "hub 2",
                    "houseMove" => 0,
                    "newMonthlyCost" => "22.34",
                    "installationTypeFTTP" => LineCheck_Result::STAGE_1,
                    "targetActivationDate" => '2021-02-02',
                    "nextBillingDate" => '2021-02-21',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => 1,
                    "minimumGuaranteedSpeedAvailable" => 1,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "accessTechnologyChanging" => 1,
                    "isSogea" => 0
                )
            ),
            array(
                "data" => array(
                    'broadbandProduct' => 'Full Fibre 40',
                    'houseMove' => false,
                    'techType' => 'FTTP',
                    'installationTypeFTTP' => LineCheck_Result::STAGE_1,
                    'contractLength' => 0,
                    'isRetain' => true,
                    'hardwareCompId' => 2343,
                    'phoneComp' => null,
                    'nextBillingDate' => "2021-02-21",
                    'callPlan' => '',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => $expectedBroadbandSpeed,
                    "minimumGuaranteedSpeedAvailable" => $expectedGuaranteedSpeedValue,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "price" => '22.34',
                    "activation" => '2021-02-02',
                    "hardware" => "hub 2",
                    "currentAccessTechnology" => "ADSL",
                    "newAccessTechnology" => "FTTP"
                ),
                "expectedArray" => array(
                    "broadbandProduct" => 'Full Fibre 40',
                    "contractLength" => 0,
                    "phoneProduct" => 0,
                    "hardware" => "hub 2",
                    "houseMove" => 0,
                    "newMonthlyCost" => "22.34",
                    "installationTypeFTTP" => LineCheck_Result::STAGE_1,
                    "targetActivationDate" => '2021-02-02',
                    "nextBillingDate" => '2021-02-21',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => 1,
                    "minimumGuaranteedSpeedAvailable" => 1,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "accessTechnologyChanging" => 1,
                    "isSogea" => 0
                )
            ),
            array(
                "data" => array(
                    'broadbandProduct' => 'Fibre 74',
                    'houseMove' => false,
                    'techType' => 'SoGEA',
                    'installationTypeFTTP' => 0,
                    'contractLength' => 0,
                    'isRetain' => true,
                    'hardwareCompId' => 0,
                    'phoneComp' => null,
                    'nextBillingDate' => "2021-02-21",
                    'callPlan' => '',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => $expectedBroadbandSpeed,
                    "minimumGuaranteedSpeedAvailable" => $expectedGuaranteedSpeedValue,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "price" => '22.34',
                    "activation" => '2021-02-02',
                    "hardware" => 0,
                    "currentAccessTechnology" => "ADSL",
                    "newAccessTechnology" => "SOGEA"
                ),
                "expectedArray" => array(
                    "broadbandProduct" => 'Fibre 74',
                    "contractLength" => 0,
                    "phoneProduct" => 0,
                    "hardware" => 0,
                    "houseMove" => 0,
                    "newMonthlyCost" => "22.34",
                    "installationTypeFTTP" => 0,
                    "targetActivationDate" => '2021-02-02',
                    "nextBillingDate" => '2021-02-21',
                    "advertisedDownloadSpeed" => $expectedAdvertisedDownloadSpeedMbs,
                    "advertisedUploadSpeed" => $expectedAdvertisedUploadSpeedMbs,
                    "estimatedDownloadSpeedAvailable" => 1,
                    "minimumGuaranteedSpeedAvailable" => 1,
                    "estimatedDownloadSpeedRangeLower" => $expectedMinExpectedSpeed,
                    "estimatedDownloadSpeedRangeHigher" => $expectedMaxExpectedSpeed,
                    "minimumUploadSpeed" => $minimumUploadSpeed,
                    "maximumUploadSpeed" => $maximumUploadSpeed,
                    "maximumDownloadSpeed" => $maximumDownloadSpeed,
                    "estimatedUploadSpeedRangeHigher" => $estimatedUploadSpeedRangeHigher,
                    "estimatedUploadSpeedRangeLower" => $estimatedUploadSpeedRangeLower,
                    "minimumGuaranteedSpeed" => $minimumGuaranteedSpeed,
                    "accessTechnologyChanging" => 1,
                    "isSogea" => 1
                )
            )
        );
    }

    public function testGetEmailName()
    {
        $expectedEmailName = 'CUSTOMER_PRODUCT_CHANGE';

        $mockPerformAccountChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getNewProductName',
                'isProductChangeForHouseMove',
                'getTechnologyType',
                'getLineCheckResults',
                'getAppointment',
                'getServiceId',
                'calculateScheduledChangeDate'),
            array(),
            '',
            false
        );

        $mockOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getContract', 'getProducts'),
            array(),
            '',
            false
        );

        $mockOrderContract = $this->getMock(
            'AccountChange_AccountChangeOrderContract',
            array('getLength'),
            array(),
            '',
            false
        );

        $mockOrder->expects($this->once())
            ->method('getContract')
            ->will($this->returnValue($mockOrderContract));

        $mockOrderProducts = $this->getMock(
            'AccountChange_AccountChangeOrderProducts',
            array('getPhoneComponentId', 'getHardwareComponentId'),
            array(),
            '',
            false
        );

        $mockOrder->expects($this->any())
            ->method('getProducts')
            ->will($this->returnValue($mockOrderProducts));

        $mockPriceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(),
            '',
            false
        );

        $mockAccount = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation','getNextInvoiceDate'),
            array(),
            '',
            false
        );

        $mockSpeedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array('getSpeedData'),
            array(),
            '',
            false
        );

        $mockDataHelper = $this->getMock(
            'AccountChange_EmailHandler_DataHelper',
            array('getCallPlanDetailsByWlrServiceComponentId'),
            array(),
            '',
            false
        );

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForComponentType'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $workplaceEmailHandler = new AccountChange_EmailHandler_Confirmation(
            $mockPerformAccountChangeApi,
            $mockOrder,
            $mockPriceHelper,
            $mockAccount,
            $mockSpeedHelper,
            $mockDataHelper
        );

        $returnedEmailName = $workplaceEmailHandler->getEmailName();

        $this->assertEquals($expectedEmailName, $returnedEmailName);
    }
}
