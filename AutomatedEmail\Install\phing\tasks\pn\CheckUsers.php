<?php
include_once 'phing/Task.php';
class CheckUsers extends Task {

    private $_strReturnName = null;
    private $_strUsers = null;
    private $_strGroups = null;

    public function setUsers($str) {
        $this->_strUsers = $str;
    }    

    public function setGroups($str) {
        $this->_strGroups = $str;
    }    

    public function setReturnName($str) {
        $this->_strReturnName = $str;
    }   

	public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}  

		$failed = false;

		//check if groups exists		
		$arrMatches = array();
		$arrGroupsFound = array();
		$arrGrpFile = @file('/etc/group');
		if(!$arrGrpFile) throw new BuildException("Failed to access'/etc/group'", $this->getLocation());
		foreach ($arrGrpFile as $line) {
			preg_match('/(\w+):\w+:\d+/',$line,$arrMatches);
			$arrGroupsFound[] = $arrMatches[1];
		}

		if ($this->_strGroups) {
			$arrGroups = explode(',',$this->_strGroups);
			foreach ($arrGroups as $strGroup) {
				
				if(!in_array($strGroup, $arrGroupsFound)) {
					$this->log("group:$strGroup doesn't exists, run 'groupadd $strGroup' first");
					$failed = true;
				}		
			}
		}
		
		//check if users exists
		if ($this->_strUsers) {
			$arrUsers = explode(',',$this->_strUsers);
			foreach ($arrUsers as $strUser) {
				
				$arrUser = posix_getpwnam($strUser);
				
				if(!is_array($arrUser) or empty($arrUser)) {
					$this->log("user:$strUser doesn't exists, run 'useradd $strUser' first");
					$failed = true;
				}		
			}
		}
		
		//check if running under correct user
		$arrCurrUser = posix_getpwuid(posix_geteuid());
		if(!in_array($arrCurrUser['name'], $arrUsers)) {
			$this->log("You need to be looged in as one of the specified users: $this->_strUsers");
			$failed = true; 			
		}
		
		if(!$failed) $this->project->setProperty($this->_strReturnName, true);
    }
}