<?php

	/**
	 * phing task for checking to see if the specified directory is empty or not
	 * 
	 * @uses Task
	 * @package Framework
	 * @subpackage Install
	 * @version $Id: IsEmptyDirectory.php,v 1.2 2008-05-19 05:51:38 swestcott Exp $
	 * @copyright 2006 PlusNet plc
	 * <AUTHOR> <<EMAIL>>
	 * @since 08/08/2006
	 * @filesource 
	 */

	require_once('phing/Task.php');

	class IsEmptyDirectory extends Task
	{
		private $strDirectory = null;
		private $strProperty = null;

		/**
		 * Sets the directory member variable 
		 * 
		 * @param string $strDirectory 
		 * @access public
		 */
		public function setDirectory($strDirectory)
		{
			$this->strDirectory = $strDirectory;
		}
	    
		/**
		 * Sets the property member variable
		 * 
		 * @param string $strProperty 
		 * @access public
		 */
		public function setProperty($strProperty)
		{
			$this->strProperty = $strProperty;
		}    

		public function init()
		{
			// nothing to do here
		}

		/**
		 * The main entry point method.
		 * 
		 * @access public
		 */
		public function main()
		{
			if(!is_dir($this->strDirectory))
			{
				throw new BuildException($this->strDirectory . ' is not a valid directory', $this->getLocation());		
			}

			if($fph = opendir($this->strDirectory))
			{
				// read contents of directory
				while($strFileName = readdir($fph))
				{
					// if there is a valid subdirectory then the directory we're checking isn't empty ...
					if(is_dir($strFileName) && !in_array($strFileName, array('.', '..')))
					{
						$this->project->setProperty($this->strProperty, 1);
						break;
					}
				}
				closedir($fph);

			} // if($fph = opendir($this->strDirectory))

		} // public function main()

	} // class IsEmptyDirectory extends Task

?>
