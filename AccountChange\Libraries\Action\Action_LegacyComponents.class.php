<?php
/**
 * Account Change Legacy Components Action
 *
 * @category  AccountChnage_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-15
 */
/**
 * Account Change Legacy Components Action
 *
 * This class represents the logic surrounding processing of legacy components
 *
 * @category  AccountChange_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Action_LegacyComponents extends AccountChange_Action
{
    /**
     * Service Definition Id
     *
     * @var int
     */
    private $serviceDefinitionId = null;

    /**
     * Registry of information
     *
     * @var AccountChange_Registry
     */
    private $registry;

    /**
     * Legacy components that need to be added as part of the account change
     *
     * @var array
     */
    private $arrLegacyComponentsToAdd = array();

    /**
     * Legacy components that need to be removed as part of the account change
     *
     * @var array
     */
    private $arrLegacyComponentsToRemove = array();

    /**
     * Array of component types we simply want to ignore in the legacy change
     * These component types have been dealt with via this app,
     * and therefore do not need to be dealth with by legacy code
     *
     * @var array
     */
    private $arrLegacyComponentTypesToIgnore = array();

    /**
     * An array of components ID that we do not want to keep.
     * This array is populated from frontend
     *
     * @var array
     */
    private $arrLegacyComponentNotToKeep = array();

    /**
     *
     * Service Component id for Partner Enduser component.
     * @var Integer
     */
    const SERVICE_COMPONENT_ID_FOR_PARTNER_ENDUSER = 594;

    /**
     *
     * Service Component id for youview tv component.
     * @var Integer
     */
    const SERVICE_COMPONENT_ID_FOR_YOUVIEW_TV = 1835;
    /**
     *
     * Component Type id for Annex M.
     * @var Integer
     */
    const COMPONENT_TYPE_ID_FOR_ANNEX_M = 938;

    /**
     *
     * Service Component type id for Plus.net Mailboxes.
     * @var Integer
     */
    const SERVICE_COMPONENT_ID_FOR_PLUSNET_MAILBOXES= 5;

    /**
     *
     * Singal value for destroy
     * @var String
     */
    const AUTO_DESTROY_SIGNAL = 'auto_destroy';

    /**
     * Main execute of the action
     *
     * @return void
     */
    public function execute()
    {
        // Get registry information
        $this->registry = AccountChange_Registry::instance();
        $this->serviceDefinitionId =
            $this->registry->getEntry('intNewServiceDefinitionId');
        $this->arrLegacyComponentTypesToIgnore =
            $this->registry->getEntry('arrLegacyComponentTypesToIgnore');
        $this->arrLegacyComponentsToAdd =
            $this->registry->getEntry('arrLegacyComponentsToAdd');
        $this->arrLegacyComponentsToRemove =
            $this->registry->getEntry('arrLegacyComponentsToRemove');
        $this->arrLegacyComponentNotToKeep =
            $this->registry->getEntry('arrLegacyComponentNotToKeep');

        /*
         * Currently there are so many partner enduser with out
         * "Partner Enduser" component. Since its a default component,
         * during account change, system was trying to add this.
         * But due to current system issue, we can't configure it.
         * So currently we are retain the current situation.
         * That is avoiding the addition of this component
         * as part of account change, if it was not available.
         * Ref P: 65499
         */
        $this->arrLegacyComponentTypesToIgnore[] =
            AccountChange_Action_LegacyComponents::SERVICE_COMPONENT_ID_FOR_PARTNER_ENDUSER;

        $this->arrLegacyComponentTypesToIgnore[] = 
            AccountChange_Action_LegacyComponents::SERVICE_COMPONENT_ID_FOR_YOUVIEW_TV;

        $this->arrLegacyComponentTypesToIgnore[] =
            AccountChange_Action_LegacyComponents::SERVICE_COMPONENT_ID_FOR_PLUSNET_MAILBOXES;

        $this->processLegacyComponents();

        // Replace registry information
        $this->registry->setEntry(
            'arrLegacyComponentTypesToIgnore',
            $this->arrLegacyComponentTypesToIgnore
        );
    }

    /**
     * Makes the changes to the account with regards to the legacy components
     * Adds and removes components to the account, depending on what is needed,
     * and what should be ignored
     *
     * @param string $strSignal Signal to send to the component configurator
     *
     * @return void
     */
    public function processLegacyComponents($strSignal = 'auto_configure')
    {
        Dbg_Dbg::write(
            'AccountChange_Action_LegacyComponents::processLegacyComponents ignoring (' .
            implode(', ', $this->arrLegacyComponentTypesToIgnore) . ')',
            'AccountChange'
        );

        $this->populateLegacyComponentArrays();
        foreach ($this->arrLegacyComponentsToRemove as $arrComponentToRemove) {

            if (!empty($arrComponentToRemove['component_type_id']) &&
                !empty($arrComponentToRemove['component_id']) &&
                !in_array($arrComponentToRemove['component_type_id'], $this->arrLegacyComponentTypesToIgnore)) {

                // Legacy call to auto_destroy a component
                $this->setupGlobalConfigurator(
                    $arrComponentToRemove['component_type_id'],
                    $arrComponentToRemove['component_id'],
                    'auto_destroy'
                );
            }
        }

        foreach ($this->arrLegacyComponentsToAdd as $intComponentTypeId) {

            if (is_numeric($intComponentTypeId) &&
                !in_array($intComponentTypeId, $this->arrLegacyComponentTypesToIgnore)) {

                $this->includeLegacyFiles();

                // Legacy call to auto configure a component
                $intComponentId = userdata_component_add(
                    $this->intServiceId,
                    $intComponentTypeId,
                    -1,
                    '',
                    'unconfigured'
                );
                $this->setupGlobalConfigurator($intComponentTypeId, $intComponentId, $strSignal);
            }
        }

        foreach ($this->arrLegacyComponentNotToKeep as $intComponentId) {
            $arrComponentToRemove = userdata_component_get($intComponentId);
            $this->setupGlobalConfigurator(
                $arrComponentToRemove['component_type_id'],
                $arrComponentToRemove['component_id'],
                'auto_destroy'
            );
        }
    }

    /**
     * Populate the arrays of legacy components that we want to add and remove
     *
     * @return void
     */
    protected function populateLegacyComponentArrays()
    {
        $arrCurrentComponents = array();

        $this->includeLegacyFiles();

        $arrCurrentComponents = $this->userdataGetComponentsForService();

        if (empty($this->arrLegacyComponentsToRemove)) {

            $this->arrLegacyComponentsToRemove = $this->userdataGetComponentsToRemove($arrCurrentComponents);

            Dbg_Dbg::write(
                'AccountChange_Action_LegacyComponents::populateLegacyComponentArrays Retrieving components to remove: ' .
                print_r($this->arrLegacyComponentsToRemove, 1),
                'AccountChange'
            );
        }

        $arrComponentTypesIDsToDelete = array();

        //unmark some components marked for removal (if they are hardware components)
        if (!empty($this->arrLegacyComponentsToRemove)) {
            foreach ($this->arrLegacyComponentsToRemove as $arrComponent) {
                $arrComponentTypesIDsToDelete[] = $arrComponent['component_type_id'];
            }

            $arrComponentTypesIDsToDelete = $this->removeHardwareComponentsFromComponentIdsArray($arrComponentTypesIDsToDelete);

            if (is_array($arrComponentTypesIDsToDelete)) {
                foreach ($this->arrLegacyComponentsToRemove as $arrKey => $legacyComponentToRemove) {
                    if (!in_array($legacyComponentToRemove['component_type_id'], $arrComponentTypesIDsToDelete)) {
                        unset($this->arrLegacyComponentsToRemove[$arrKey]);
                    }
                }
            }
        }

        if (empty($this->arrLegacyComponentsToAdd)) {
            // Remove to-be-deleted components to present a view of what the
            // account will be like after deletion. This prevents mutually
            // exclusive components failing to be created
            $arrComponentsAfterDeletion = $arrCurrentComponents;
            foreach ($arrComponentsAfterDeletion as $intKey => $arrComponent) {
                if (in_array($arrComponent['component_type_id'], $arrComponentTypesIDsToDelete)) {
                    unset($arrComponentsAfterDeletion[$intKey]);
                }
            }

            $this->arrLegacyComponentsToAdd = $this->userdataGetLegacyComponentsToAdd($arrComponentsAfterDeletion);

            Dbg_Dbg::write(
                'AccountChange_Action_LegacyComponents::populateLegacyComponentArrays Retrieving components to add : ' .
                implode(', ', $this->arrLegacyComponentsToAdd),
                'AccountChange'
            );
        }
    }

    /**
     * Legacy wrapper function to setup the global configurator
     *
     * @param int    $intComponentTypeId Service component id
     * @param int    $intComponentId     Component id
     * @param string $strConfigurator    Configurator to use
     *
     * @return void
     */
    protected function setupGlobalConfigurator(
        $intComponentTypeId,
        $intComponentId,
        $strConfigurator
    ) {
        $this->includeLegacyFiles();

        // Need to pull in the global variables for this part of the account change process
        global $global_component_configurators;

        Dbg_Dbg::write(
            "AccountChange_Manager::setupGlobalConfigurator\n\n" .
            "Component Type: " . $intComponentTypeId . "\n" .
            "Component: " . $intComponentId . "\n".
            "Signal: " .  $strConfigurator,
            'AccountChange'
        );
        if (isset($global_component_configurators[$intComponentTypeId]) &&
            function_exists(
                $global_component_configurators[$intComponentTypeId]
            )) {

            $options = $this->getConfiguratorOptions($intComponentTypeId, $strConfigurator, $this->registry->getEntry('$adslToFttc'));

            $global_component_configurators[$intComponentTypeId]($intComponentId, $strConfigurator, $options);

            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        } else {
            error_log("AccountChange_Action_LegacyComponents::setupGlobalConfigurator : Unable to setup "
                . " global_component_configurators for Component Type [$intComponentTypeId], "
                . " Component [$intComponentId], Signal [$strConfigurator], SKIPPING."
            );
        }
    }

    /**
     *
     * When an ADSL to Fibre change occurs, the upstream speed is modified to the maximum speed of the product that
     * the line can support. This effectively removes Annex M as the upstream field is used to indicate whether they
     * have annex m. Using this knowledge the order to BT can be suppressed in that specific case.
     *
     * If the order isn't suppressed, it fails and then the Annex component is left in place in our systems.
     *
     * @param $intComponentTypeId
     * @param $strConfigurator
     * @param $adslToFttc
     *
     * @return array
     */
    public function getConfiguratorOptions($intComponentTypeId, $strConfigurator, $adslToFttc) {
        $options = array();
        if($intComponentTypeId == self::COMPONENT_TYPE_ID_FOR_ANNEX_M && $strConfigurator == self::AUTO_DESTROY_SIGNAL && $adslToFttc) {
            $options = array('placeOrderToBT' => false);
        }
        return $options;
    }

    /**
     * Filters components to be removed types ids and returns a list of all non-hardware components
     * @param $componentIdsArray
     * @return array || string
     * @throws Db_TransactionException
     */
    protected function removeHardwareComponentsFromComponentIdsArray($componentIdsArray)
    {
        $db = Db_Manager::getAdaptor('AccountChange');

        return $db->getComponentTypesIdsIgnoringHardwareComponents($componentIdsArray);
    }

    /**
     * Used for easy mocking
     *
     * @return array
     */
    protected function userdataGetComponentsForService()
    {
        return userdata_component_get_by_service($this->intServiceId);
    }

    /**
     * Used for easy mocking
     *
     * @return array
     */
    protected function userdataGetComponentsToRemove($arrCurrentComponents)
    {
        return userdata_service_type_change_get_components_to_remove(
            $this->serviceDefinitionId,
            $arrCurrentComponents
        );
    }

    protected function userdataGetLegacyComponentsToAdd($arrComponentsAfterDeletion)
    {
        return userdata_service_type_change_get_components_to_add(
            $this->serviceDefinitionId,
            $arrComponentsAfterDeletion
        );
    }

    /**
     * Include legacy files in a way that can be mocked
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/class-access.inc';
        require_once '/local/data/mis/database/database_libraries/config-access.inc';
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
        require_once '/local/data/mis/database/database_libraries/cbc-access.inc';
    }
}
