<?php

use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\CampaignNotificationClient\Exception\PersonalisedPromotionNotFoundException;

class AccountChange_PromotionCodeContractLengthPolicy extends \AccountChange_AbstractValidationPolicy
{

    const C2M_PROMOTION_CODE = 'C2MPromotionCode';

    const C2M_PROMOTION_PERSONALISED = 'PersonalisedOffer';

    const C2M_CLIENT = 'C2mClient';

    const CAMPAIGN_NOTIFICATION_CLIENT = 'CampaignNotificationClient';

    const ERROR_MESSAGE = 'Oops, that offer isn\'t available. Please call us on 0800 432 0080 to find out the latest deal we can give you.';

    const CONTRACT_LENGTH_MONTHS = 'contractLengthMonths';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_PROMO_CONTRACT_LENGTH_MISMATCH';

    private $campaignNotificationClient;
    private $c2mClient;
    private $currentDate;
    protected $actor;

    public function __construct(Auth_BusinessActor $actor, $isWorkplace = false, $isScript = false, $additionalInformation = array())
    {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);

        $this->actor = $actor;
        $this->campaignNotificationClient = \BusTier_BusTier::getClient('campaignNotificationClient');
        $this->c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
        $this->currentDate = (new \DateTime())->format('Y-m-d');
    }

    /**
     * @return bool
     */
    public function validate()
    {
        /*
         *  checks if promotion has personalisedoffer set to true in C2M,
         *  promotion is applicable to the channel(campaign) of the customer &
         *  the claim date is in between start and end date of promotion set by customer domain
         *  @return bool
         */
        $c2mPromotion       =   $this->getC2mPromotionFromAdditionalData();
        $additionalInfo     =   $this->getAdditionalInformation();
        $isPersonalised     =   false;

        if ($c2mPromotion instanceof Promotion) {
            $isPersonalised = $c2mPromotion->getIsPersonalisedOffer();
        }

        $promoOfferCode = isset($additionalInfo[self::C2M_PROMOTION_CODE])
                            && !is_null($additionalInfo[self::C2M_PROMOTION_CODE])
                            && !empty(trim($additionalInfo[self::C2M_PROMOTION_CODE]));

        if ($promoOfferCode && $isPersonalised && !empty($additionalInfo['impressionOfferId'])) {
            try {
                $impressionOfferDetails = $this->getImpressionOfferDetails($additionalInfo['impressionOfferId']);
            } catch (Exception $e) {
                $message = "[AccountChange][getImpressionOfferDetails] There was an unexpected error getting impression offer details for impression offer id ".
                           $additionalInfo['impressionOfferId']." The error was: ".$e->getMessage();

                error_log($message);
                return false;
            }

            if (!empty($impressionOfferDetails['OfferedContractDuration'])) {
                $offeredMonths = $this->convertContractLengthToMonths($impressionOfferDetails['OfferedContractDuration']);
                if ($offeredMonths == $additionalInfo['contractDuration']) {
                    return true;
                }
            }
            return false;
        }
        // No impression offer id = not Pega, so shold validate ok
        return true;
    }

    protected function getImpressionOfferDetails($impressionOfferId)
    {
        $nbaClient = $this->getNextBestActionClient();
        return $nbaClient->getImpressionOfferDetails($impressionOfferId);
    }

    public function getNextBestActionClient()
    {
        return BusTier_BusTier::getClient('nextBestActionServiceClient');
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Converts dates in the format of:
     *  X Months
     *  X Years
     *  X Years X Months
     * into a number of months.
     *
     * @param string $contractLength Contract length
     */
    public function convertContractLengthToMonths($contractLength)
    {
        $parts = explode(' ', $contractLength);
        $pairs = array();
        $pairCounter = 1;
        $number = 0;

        for ($i = 0; $i < count($parts); $i++) {

            if ($pairCounter == 1) {
                $number = $parts[$i];
                $pairCounter ++;
            } elseif ($pairCounter == 2) {
                $unit = $parts[$i];
                $pairs[] = array('number' => $number, 'unit' => strtoupper($unit));
                $pairCounter = 1;
            }
            // No units, assume months
            if (count($parts) == 1) {
                $pairs[] = array('number' => $number, 'unit' => 'MONTH');
            }
        }
        $months = 0;

        foreach($pairs as $pair) {
            if (strpos($pair['unit'], 'MONTH') !== false) {
                $months += $pair['number'];
            } elseif (strpos($pair['unit'], 'YEAR') !== false) {
                $months += (12 * $pair['number']);
            }
        }
        return $months;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
