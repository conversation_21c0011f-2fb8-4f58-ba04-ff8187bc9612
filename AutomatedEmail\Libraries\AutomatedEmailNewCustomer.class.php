<?php

/**
* AutomatedEmail_AutomatedEmailNewCustomer class
*
* <AUTHOR> <<EMAIL>>
*/
class AutomatedEmail_AutomatedEmailNewCustomer
{
    /**
    * Localized Smarty
    *
    * @var    object
    */
    private $localizedSmarty;

    /**
    * Email
    *
    * @var    object
    */
    private $email;

    /**
    * Template Email Handle
    *
    * @var    string
    */
    private $emailHandle;

    /**
    * Path for template
    *
    * @var    string
    */
    private $path;

    /**
    * File Template Extension
    *
    * @var    array
    */
    private $arrTemplateExtensionMap = array(
        'text' => array('.txt', '.tpl'),
        'html' => array('.html', '.ihtml'),
    );

    /**
    * class constructor
    *
    * @param  string $path Path
    *
    * @return void
    */
    public function __construct($path)
    {
        $this->path = $path;
        $strSmartyCompilePath = AutomatedEmail_TemplateConfig::getCompilePath()->getValue();

        $objLocale = new I18n_Locale('en_GB');
        $arrErrors = array();
        $this->email  = new Email_Email();
        $this->localizedSmarty = new AutomatedEmail_LocalizedSmarty($objLocale, $arrErrors, $strSmartyCompilePath);
    }

    /**
    * Prepare method for Sending Email
    *
    * @param  string $strEmailHandle Template handle
    * @param  array  $arrEmailData   Data for template
    *
    * @return bool
    */
    public function prepareAutomatedEmail($strEmailHandle, $arrEmailData = array())
    {
        $this->emailHandle = $strEmailHandle;

        // Assign in our data so it's available to Smarty
        if (!empty($arrEmailData)) {
            $this->localizedSmarty->assign($arrEmailData);
        }
        return $this->prepareStandardAutomatedEmail();
    }

    /**
    * Getting Files Templates
    *
    * @return array
    */
    private function getTemplates()
    {
        $strCheckPath = $this->path.'/'.$this->emailHandle;

        $arrTemplates = array();
        $strPattern = '/^' . preg_quote($this->emailHandle) . '\.[a-zA-Z0-9]{3,4}$/S';
        while ($strCheckPath = substr($strCheckPath, 0, strrpos($strCheckPath, '/'))) {
            if (is_dir($strCheckPath)) {
                $dphContent = dir($strCheckPath);

                while ($strEntry = $dphContent->read()) {
                    if (!in_array($strEntry, array('.', '..')) && !is_dir($strEntry)) {
                        if (preg_match($strPattern, $strEntry)) {
                            $arrTemplates[] = $strCheckPath . '/' . $strEntry;
                        }
                    }
                }
            }

            if (!empty($arrTemplates)) {
                return $arrTemplates;
            }
        }
    }

    /**
    * Method to prepare email content 
    *
    * @return void
    */
    private function prepareStandardAutomatedEmail()
    {
        $arrTemplates  =  $this->getTemplates();

        if (empty($arrTemplates)) {
            $strMessage = "Unable to locate email templates for handle '{$this->emailHandle}' within the signup agent working from home context";
            throw new AutomatedEmail_Exception($strMessage, AutomatedEmail_Exception::NO_TEMPLATE_FOR_HANDLE);
        }

        foreach ($arrTemplates as $strTemplate) {
            $arrFileExtension = array();
            foreach ($this->arrTemplateExtensionMap as $type) {
                $arrFileExtension[] = implode('|', $type);
            }
            $fileExtension = implode('|', $arrFileExtension);

            if (!preg_match("/^.*({$fileExtension})\$/i", $strTemplate, $arrMatch)) {
                $strMessage = 'Templates located with the context do not appear to be valid email templates';
                throw new AutomatedEmail_Exception($strMessage, AutomatedEmail_Exception::INVALID_TEMPLATE_FOR_HANDLE);
            }

            $matchedFileExtension     = $arrMatch[1];
            $strContent = $this->localizedSmarty->fetch($strTemplate);

            if (in_array($matchedFileExtension, $this->arrTemplateExtensionMap['text'])) {
                $this->email->setTextBody(html_entity_decode($strContent));
            }

            if (in_array($matchedFileExtension, $this->arrTemplateExtensionMap['html'])) {
                $this->email->setHtmlBody($strContent);
            }
        }

        $strSubject = trim($this->localizedSmarty->getSubject());

        if (!empty($strSubject)) {
            $this->email->setSubject($strSubject);
        } else {
            $this->email->setSubject('Link for the Order Payment');
        }

        return $this->email;
    }

    /**
    * Set Receiver Address
    *
    * @param  string $strRecipient Email recipient
    *
    * @return void
    */
    public function addRecipient($strRecipient)
    {
        return $this->email->addRecipient($strRecipient);
    }

    /**
    * Set Sender Address
    *
    * @param  string $strSender Email sender
    *
    * @return void
    */
    public function setSender($strSender)
    {
        return $this->email->setSender($strSender);
    }

    /**
    * Send Email
    *
    * @return void
    */
    public function send()
    {
        try {
            return $this->email->send();
        } catch (Email_Exception $emailException) {
            error_log('Error sending email: '. $emailException->getMessage());
            return false;
        }
    }

    /**
    * Get Email Subject
    *
    * @return void
    */
    public function getSubject()
    {
        return $this->email->getSubject();
    }

    /**
    * Get Email Html Body
    *
    * @return void
    */
    public function getHtmlBody()
    {
        return $this->email->getHtmlBody();
    }

    /**
    * Get Email Text Body
    *
    * @return void
    */
    public function getTextBody()
    {
        return $this->email->getTextBody();
    }
}
