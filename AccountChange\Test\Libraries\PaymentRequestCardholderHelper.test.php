<?php
/**
 * @category   AccountChange
 * @package    FrameworkWebApi_Test
 * @subpackage Libraries
 * <AUTHOR> <PERSON> <<EMAIL>>
 */

/**
 * Test class for AccountChange_PaymentRequestCardholderHelper
 */
class AccountChange_PaymentRequestCardholderHelperTest extends PHPUnit_Framework_TestCase
{
    /**
     * Reset database after each test
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::restoreAdaptor('AccountChange');
    }

    /**
     * Tests for the getData() method
     *
     * @param  int          $accountId        Account Id to use as constructor argument
     * @param  array|null   $dbResponse       The response for the mocked db to return
     * @param  array|string $expectedResponse The array that is expected to be returned on calling getData()
     *                                        or the string 'default'
     *
     * @dataProvider provideDataForTestGetData
     *
     * @return void
     */
    public function testGetData($accountId, $dbResponse = null, $expectedResponse = 'default')
    {
        if ($expectedResponse == 'default') {
            $expectedResponse = AccountChange_PaymentRequestCardholderHelper::DEFAULT_DATA_RESPONSE;
        }

        $dbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getCustomerDetailsFromAccountId'),
            array(),
            '',
            false
        );

        if (empty($accountId) || !is_numeric($accountId)) {
            $dbAdaptor
                ->expects($this->never())
                ->method('getCustomerDetailsFromAccountId');
        } else {
            $dbAdaptor
                ->expects($this->once())
                ->method('getCustomerDetailsFromAccountId')
                ->will($this->returnValue($dbResponse));
        }

        Db_Manager::setAdaptor('AccountChange', $dbAdaptor);

        $helper = new AccountChange_PaymentRequestCardholderHelper($accountId);
        $response = $helper->getData();
        $this->assertEquals($expectedResponse, $response);
    }

    /**
     * Data provider for testGetData()
     *
     * @return array
     */
    public function provideDataForTestGetData()
    {
        $baseDbResponse = array(
            'name'               => '',
            'email'              => '',
            'mobilePhone'        => '',
            'thoroughfareNumber' => '',
            'thoroughfare'       => '',
            'postTown'           => '',
            'postCode'           => '',
            'county'             => '',
            'country'            => '',
        );

        $validValuesDbResponse = array(
            'name'               => 'Foo Bar-san',
            'email'              => '<EMAIL>',
            'mobilePhone'        => '***************',
            'thoroughfareNumber' => '2',
            'thoroughfare'       => 'Pinfold Street',
            'postTown'           => 'Sheffield',
            'postCode'           => 'S1 2GU',
            'county'             => 'Yorkshire',
            'country'            => 'UK',
        );

        $validValuesExpectedResponse = array(
            'cardholderContactDetails' => array(
                'email'       => $validValuesDbResponse['email'],
                'mobilePhone' => array(
                    'cc'         => 44,
                    'subscriber' => 7123456789,
                )
            ),
            'cardholderAddress' => array(
                'thoroughfareNumber' => $validValuesDbResponse['thoroughfareNumber'],
                'thoroughfare'       => $validValuesDbResponse['thoroughfare'],
                'postTown'           => $validValuesDbResponse['postTown'],
                'postCode'           => $validValuesDbResponse['postCode'],
                'county'             => $validValuesDbResponse['county'],
                'country'            => $validValuesDbResponse['country'],
            ),
            'cardholderName'         => 'Foo Bar-san',
        );


        $invalidEmailDbResponse = $validValuesDbResponse;
        $invalidEmailDbResponse['email'] = 'noAtSign';
        $invalidEmailExpectedResponse = $validValuesExpectedResponse;
        unset($invalidEmailExpectedResponse['cardholderContactDetails']['email']);

        $premisesNameDbResponse = $validValuesDbResponse;
        $premisesNameDbResponse['thoroughfareNumber'] = 'Apartment 123';
        $premisesNameExpectedResponse = $validValuesExpectedResponse;
        $premisesNameExpectedResponse['cardholderAddress']['premisesName'] =
                                                                        $premisesNameDbResponse['thoroughfareNumber'];
        unset($premisesNameExpectedResponse['cardholderAddress']['thoroughfareNumber']);

        return array(
            'emptyAccountIdShouldReturnDefaultDataResponse' => array(
                'accountId' => 0,
            ),
            'nonNumericAccountIdShouldReturnDefaultDataResponse' => array(
                'accountId' => new \stdClass(),
            ),
            'emptyDbResponseShouldReturnDefaultDataResponse' => array(
                'accountId'  => 123,
                'dbResponse' => null,
            ),
            'dbResponseWithNoValidValuesShouldReturnDefaultDataResponse' => array(
                'accountId'  => 123,
                'dbResponse' => $baseDbResponse,
            ),
            'validDbDataShouldPopulateOutputArrayForAllInputKeys' => array(
                'accountId'        => 123,
                'dbResponse'       => $validValuesDbResponse,
                'expectedResponse' => $validValuesExpectedResponse,
            ),
            'invalidDataShouldNotBePresentInOutputArray' => array(
                'accountId'        => 123,
                'dbResponse'       => $invalidEmailDbResponse,
                'expectedResponse' => $invalidEmailExpectedResponse,
            ),
            'shouldPresentPremisesNameIfThoroughfareNumberIsTooLong' => array(
                'accountId'        => 123,
                'dbResponse'       => $premisesNameDbResponse,
                'expectedResponse' => $premisesNameExpectedResponse,
            ),
        );
    }
}
