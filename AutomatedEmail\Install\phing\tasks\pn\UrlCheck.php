<?php
include_once 'phing/Task.php';
class Url<PERSON>heck extends Task {

    private $_strUrl;
    private $_strReturnName;

    
    public function setUrl($str) {
        $this->_strUrl = $str;
    }

    public function setReturnName($str) {
        $this->_strReturnName = $str;
    }    

    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}  

		if (!$this->_strUrl) {
		    throw new BuildException("You must specify the url attribute", $this->getLocation());
		}  

		if(!fopen($this->_strUrl, "r")) {
			return;
		};
		$this->project->setProperty($this->_strReturnName, "Url accessible");
    }
}