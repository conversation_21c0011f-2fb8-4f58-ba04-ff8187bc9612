<?php
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
/**
 * Action Billing
 *
 * Testing class for the AccountChange_Action_Billing class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Action_Billing.test.php,v 1.2 2009-02-17 04:41:25 rmerewood Exp $
 * @since      File available since 2008-09-01
 */
/**
 * Action Billing Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2008 PlusNet
 */
class AccountChange_Action_Billing_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test constructor calls initialise and sets up the action correctly.
     *
     * @covers AccountChange_Action_Billing::__construct
     *
     * @return void
     */
    public function testConstructorCallsInitialiseAndSetsUpTheActionCorrectly()
    {
        $intServiceId = 999;
        $arrOptions = array('Billing' => array(1,2));

        $objBilling = new AccountChange_Action_Billing($intServiceId, $arrOptions);

        $this->assertAttributeEquals($intServiceId, 'intServiceId', $objBilling);
        $this->assertAttributeEquals($arrOptions, 'arrOptions', $objBilling);
    }

    /**
     * Test execute calls correct sub actions.
     *
     * @covers AccountChange_Action_Billing::execute
     *
     * @return void
     */
    public function testExecuteCallsCorrectSubActions()
    {
        $intServiceId = 1;

        $objBillingMock = $this->getMock(
            'AccountChange_Action_Billing',
            array('setInvoicePeriod', 'proRataCharge'),
            array($intServiceId)
        );

        $objBillingMock->expects($this->once())
            ->method('setInvoicePeriod');
        $objBillingMock->expects($this->once())
            ->method('proRataCharge');

        $objBillingMock->execute();
    }

    /**
     * Test set invoice period calls database set invoice period function.
     *
     * @covers AccountChange_Action_Billing::setInvoicePeriod
     *
     * @return void
     */
    public function testSetInvoicePeriodCallsDatabaseSetInvoicePeriodFunction()
    {
        $intServiceId = 1;

        $objBillingMock = $this->getMock(
            'AccountChange_Action_Billing',
            array('raiseServiceEvent'),
            array($intServiceId)
        );

        $objBillingMock->expects($this->once())
            ->method('raiseServiceEvent');

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('setInvoicePeriod'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
            ->method('setInvoicePeriod');

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objBillingMock->setInvoicePeriod();
    }

    /**
     * Test set invoice period raises a ticket.
     *
     * @covers AccountChange_Action_Billing::setInvoicePeriod
     *
     * @return void
     */
    public function testSetInvoicePeriodRaisesATicket()
    {
        $intServiceId = 1;

        $objBillingMock = $this->getMock(
            'AccountChange_Action_Billing',
            array('raiseServiceEvent'),
            array($intServiceId)
        );

        $objBillingMock->expects($this->once())
                       ->method('raiseServiceEvent');

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('setInvoicePeriod'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->once())
                         ->method('setInvoicePeriod');

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objBillingMock->setInvoicePeriod();
    }

    /**
     * Test pro rata change raises a ticket.
     *
     * @param int  $intAccountChangeOperation Upgrade or downgrade
     * @param int  $sdi                       New service definition id
     * @param bool $isOldFibre                Is old product fibre
     * @param bool $isNewFibre                Is new product fibre
     * @param bool $raiseTicket               Should it raise a ticket?
     *
     * @covers AccountChange_Action_Billing::proRataCharge
     * @dataProvider providerDataForProRata
     *
     * @return void
     */
    public function testProRataChangeRaisesATicket(
        $intAccountChangeOperation,
        $sdi,
        $isOldFibre,
        $isNewFibre,
        $raiseTicket
    ) {
        $intServiceId = 1;

        $mockFibreHelper = $this->getMock(
            'AccountChange_FibreHelper',
            array('isFibreProduct'),
            array()
        );

        $mockFibreHelper
            ->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->onConsecutiveCalls($isOldFibre, $isNewFibre));

        $objBillingMock = $this->getMock(
            'AccountChange_Action_Billing',
            array('raiseTicket', 'getFibreHelper'),
            array($intServiceId)
        );

        if ($raiseTicket) {
            $objBillingMock->expects($this->once())
                ->method('raiseTicket');
        } else {
            $objBillingMock->expects($this->never())
                ->method('raiseTicket');
        }

        $objBillingMock
            ->expects($this->any())
            ->method('getFibreHelper')
            ->will($this->returnValue($mockFibreHelper));

        $reg = AccountChange_Registry::instance();
        $reg->setEntry('intAccountChangeOperation', $intAccountChangeOperation);
        $reg->setEntry('intNewServiceDefinitionId', $sdi);

        $objBillingMock->proRataCharge();
    }

    /**
     * Provider data for pro rata.
     *
     * @return array
     */
    public function providerDataForProRata()
    {
        return array(
            // Upgrade, new product fibre, old product adsl - send ticket
            array(AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE, 1234, true, false, false),
            // Upgrade, new product adsl, old product adsl, don't send ticket
            array(AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE, 1234, false, false, false),
            // Upgrade, new product fibre, old product fibre - don't send ticket
            array(AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE, 1234, true, true, false),
            // Downgrade, new product fibre, old adsl - don't send ticket
            array(AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE, 1234, true, false, false),
            // Downgrade, new product adsl, old product adsl - don't send ticket
            array(AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE, 1234, false, false, false),
        );
    }
}
