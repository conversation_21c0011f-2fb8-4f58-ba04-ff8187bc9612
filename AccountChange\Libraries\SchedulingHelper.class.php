<?php

/**
 * Helper functions that allow us to work out when a broadband product change
 * should be scheduled for.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 * @link      http://jira.internal.plus.net/browse/PROF-284
 */

/**
 * Helper functions that allow us to work out when a broadband product change
 * should be scheduled for.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 PlusNet
 * @link      http://jira.internal.plus.net/browse/APC-90
 */
class AccountChange_SchedulingHelper
{
    /**
     * @var bool
     */
    private $changeDateOveridden;

    /**
     * Return the change date has been overidden by a date in the
     * registry, null otherwise
     *
     * @return string
     **/
    public function getOveriddenChangeDate()
    {
        $registry = AccountChange_Registry::instance();

        // If we're coming from the API, there's an option to set the desired change date
        // rather than relying on next invoice.
        $overiddenChangeDate = $registry->getEntry('overiddenChangeDate');
        $selfInstallDate = $registry->getEntry('selfInstallDate');

        if ($overiddenChangeDate !== null) {
            // Make sure it's in the correct mysql format
            if ($overiddenChangeDate instanceof DateTime)
            {
                $overiddenChangeDate = $overiddenChangeDate->format("Y-m-d");
            } else {
                $timestamp = strtotime($overiddenChangeDate);
                $overiddenChangeDate = date('Y-m-d', $timestamp);
            }
            if ($overiddenChangeDate == '1970-01-01') {
                // We've got a dodgy date in the registry, ignore it.
                $overiddenChangeDate = null;
            }
        } elseif ($selfInstallDate !== null) {
            $overiddenChangeDate = $selfInstallDate;
        }

        return $overiddenChangeDate;
    }


    /**
     * Logic to work out the change date for adsl->fibre or fibre->fibre when using self-install
     * Sets a selfInstallDate registry entry with the correct date.
     *
     * @param int $oldSdi Existing broadband product service definition id
     * @param int $newSdi New broadband product service definition id
     *
     * @return string
     **/
    public function calculateSelfInstallDate($oldSdi, $newSdi)
    {
        $currentIsFibre = $this->isFibreProduct($oldSdi);
        $selectedIsFibre = $this->isFibreProduct($newSdi);
        $customerRequiredDate = '';

        if ($selectedIsFibre) {
            if ($currentIsFibre) {
                $customerRequiredDate = $this->addWorkingDays($this->now(), 1) + 7200;
            } else {
                $customerRequiredDate = $this->addWorkingDays($this->now(), 5) + 7200;
            }

            $crd = I18n_Date::fromTimestamp($customerRequiredDate);
            $customerRequiredDate = $crd->toMySql();
            $registry = AccountChange_Registry::instance();
            $registry->setEntry('selfInstallDate', $customerRequiredDate);
        }
        return $customerRequiredDate;
    }

    /**
     * Is the given product a fibre product?
     *
     * @param integer $intServiceDefinitionId ServiceDefinitionId
     *
     * @return boolean
     */
    protected function isFibreProduct($intServiceDefinitionId)
    {
        $isFibre = false;

        if (isset($intServiceDefinitionId)) {
            $fibreHelper = new AccountChange_FibreHelper();
            $isFibre = $fibreHelper->isFibreProduct($intServiceDefinitionId);
        }

        return $isFibre;
    }


    /**
     * Wrapper to the I18n_Date::addWorkingDays
     *
     * @param I18n_Date $date      Unix timestamp of date to add to
     * @param int       $daysToAdd Number of days to add
     *
     * @return uxt
     **/
    protected function addWorkingDays($date, $daysToAdd)
    {
        return I18n_Date::addWorkingDays($date, $daysToAdd);
    }

    /**
     * Wrapper to I18n_Date::now
     *
     * @return integer
     **/
    protected function now()
    {
        return I18n_Date::now()->getTimestamp();
    }

    /**
     * Perform a calculation to work out when a change should be scheduled for based
     * on the accounts billing date and other gathered data.  Returns a mysql formatted
     * date or null if the scheduling is to be deferred.
     *
     * @param int  $serviceId              Service id
     * @param bool $hasEngineerAppointment Whether an engineer appointment is booked or not
     *
     * @return string
     **/
    public function calculateScheduledChangeDate($serviceId, $hasEngineerAppointment = false)
    {
        $registry = AccountChange_Registry::instance();
        $manualAppointment = $registry->getEntry('manualAppointment');

        // If there's an appointment involved, then the account change is scheduled for null.
        // It's completed on the day the engineer goes out.
        if ($hasEngineerAppointment || $manualAppointment) {
            return null;
        }

        $this->includeLegacyFiles();
        $coreService = $this->getCoreService($serviceId);
        $nextInvoice = $coreService->getNextInvoiceDate();
        $overiddenChangeDate = $this->getOveriddenChangeDate();

        if ($overiddenChangeDate !== null && $this->useOverrideDate($registry)) {
            $changeDate = $overiddenChangeDate;
            $this->changeDateOveridden = true;
        } elseif ($this->nextInvoiceIsToday($nextInvoice)) {
            $changeDate = $coreService->getNextNextInvoiceDate()->toMySql();
        } else {
            $changeDate = $nextInvoice->toMySql();
        }

        return $changeDate;
    }

    /**
     * Include legacy files in a way that can be mocked
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
    }


    /**
     * Getter for the core service
     *
     * @param int $serviceId Service id
     *
     * @return Core_Service
     */
    protected function getCoreService($serviceId)
    {
        // We already know that committing the default transaction
        // is wrong, however the legacy code uses private
        // transactions all over the place
        // A decision was made to commit the default transaction
        $service = new Core_Service($serviceId);
        return $service;
    }

    /**
     * Gets today's date - as a function so we can mock it for unit testing
     *
     * @return string
     **/
    protected function getToday()
    {
        return I18n_Date::now()->fShort();
    }

    public function buildProductChangeServiceNotice($currentProductName, $newProductName, $overiddenChangeDate = null, $changeDate = null)
    {
        $notice = 'Broadband product change scheduled successfully' . PHP_EOL
            . 'From: ' . $currentProductName . PHP_EOL
            . 'To: ' . $newProductName . PHP_EOL;

        if ($overiddenChangeDate !== null) {
            $notice .= "This change will take place on {$overiddenChangeDate}";
        } elseif ($changeDate) {
            $notice .= 'This change will take place on the next billing date, ' . $changeDate;
        } else {
            $notice .= 'This change will complete on the morning of the engineer appointment';
        }

        return new AccountChange_ServiceNotice($notice);
    }

    /**
     * True if order is not a recontract or if order is an instant recontract, false otherwise
     *
     * @param AccountChange_Registry $registry account change registry
     * @return bool
     */
    private function useOverrideDate(AccountChange_Registry $registry)
    {
        $isRecontract = $registry->getEntry('isRecontract');
        $isInstantRecontract = !empty($registry->getEntry('instantRecontract'));

        if (!$isRecontract || $isInstantRecontract) {
            return true;
        }

        return false;
    }

    /**
     * @param I18n_Date $nextInvoice next invoice date
     * @return bool
     */
    private function nextInvoiceIsToday(I18n_Date $nextInvoice)
    {
        return $nextInvoice->fShort() == $this->getToday();
    }

    /**
     * @param AccountChange_AccountChangeOrder $order            change order
     * @param AccountChange_PerformChangeApi   $performChangeApi performChangeApi
     * @return DateTime|false|I18n_Date|string|null
     */
    public function calculateChangeDateByOrder(
        AccountChange_AccountChangeOrder $order,
        AccountChange_PerformChangeApi $performChangeApi
    ) {
        switch (true) {
            case !empty($order->getBackDatedDate()): // Backdated change
                return $order->getBackDatedDate();
            case !$order->getIsScheduledChange(): // Instant change
                return date('Y-m-d');
            case $performChangeApi->isRecontract(): // Recontract journey (only set via Contract Management)
                return $this->getNextInvoiceDate($performChangeApi);
            case $this->isFttpProduct($performChangeApi->getToSdi()): // New product is FTTP
                return $this->getFttpOrderLeadTime($order, $performChangeApi);
            case $this->isSogeaProduct($performChangeApi->getToSdi()): // New product is SOGEA
                return $this->calculateSogeaInstallDate($performChangeApi);
            case $this->isFibreProduct($performChangeApi->getToSdi()): // New product is FTTC
                return $this->calculateSelfInstallDate(
                    $performChangeApi->getCurrentSdi(),
                    $performChangeApi->getToSdi()
                );
            default: // New product should be ADSL
                return $this->getNextInvoiceDate($performChangeApi);
        }
    }

    /**
     * @param int $intServiceId serviceId
     * @return LineCheck_Result
     */
    protected function getLineCheck($intServiceId)
    {
        return LineCheck_Result::getPendingResultByServiceId($intServiceId);
    }

    /**
     * @param int $intServiceDefinitionId ServiceDefinitionId
     * @return bool
     */
    private function isFttpProduct($intServiceDefinitionId)
    {
        $isBBO = false;

        if (isset($intServiceDefinitionId)) {
            $bboHelper = $this->getBroadbandOnlyHelper();
            $isBBO = $bboHelper->isFttpProduct($intServiceDefinitionId);
        }

        return $isBBO;
    }

    /**
     * @param int $intServiceDefinitionId ServiceDefinitionId
     * @return bool
     */
    private function isSogeaProduct($intServiceDefinitionId)
    {
        $isSogea = false;

        if (isset($intServiceDefinitionId)) {
            $bboHelper = $this->getBroadbandOnlyHelper();
            $isSogea = $bboHelper->isSogeaProduct($intServiceDefinitionId);
        }

        return $isSogea;
    }

    /**
     * @return AccountChange_BroadbandOnlyHelper
     */
    protected function getBroadbandOnlyHelper()
    {
        return new AccountChange_BroadbandOnlyHelper();
    }

    /**
     * @param AccountChange_AccountChangeOrder $order            account change order
     * @param AccountChange_PerformChangeApi   $performChangeApi perform change api
     * @return string
     */
    private function getFttpOrderLeadTime(
        AccountChange_AccountChangeOrder $order,
        AccountChange_PerformChangeApi $performChangeApi
    ) {
        $lineCheckResult = $this->getLineCheck($performChangeApi->getServiceId());
        if ($lineCheckResult->getFttpInstallProcess() == LineCheck_Result::REMOTE_ACTIVATION) {
            if ($this->isFttpProduct($performChangeApi->getCurrentSdi())
                && $this->isFttpProduct($performChangeApi->getToSdi())
            ) {
                $workingDays = 1;
            } else {
                // the order doesn't determine the activation date
                $workingDays = $lineCheckResult->getFttpLeadTime();
            }
            return date('Y-m-d', $this->addWorkingDays($this->now(), $workingDays));
        } elseif ($order->getAppointment() && !empty($order->getAppointment()->getLiveAppointment())) {
            return $order->getAppointment()->getLiveAppointment()['date'];
        }
        // manual appointment - no date yet
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('manualAppointment', true);
        return null;
    }

    /**
     * @param AccountChange_PerformChangeApi   $performChangeApi perform change api
     * @return string
     */
    private function calculateSogeaInstallDate(AccountChange_PerformChangeApi $performChangeApi)
    {
        $currentIsSogea = $this->isSogeaProduct($performChangeApi->getCurrentSdi());

        if ($currentIsSogea) {
            $customerRequiredDate = $this->addWorkingDays($this->now(), 1) + 7200;
        } else {
            $customerRequiredDate = $this->addWorkingDays($this->now(), 3) + 7200;
        }
        $crd = I18n_Date::fromTimestamp($customerRequiredDate);
        $customerRequiredDate = $crd->toMySql();
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('selfInstallDate', $customerRequiredDate);
        return $customerRequiredDate;
    }

    /**
     * @param AccountChange_PerformChangeApi $performChangeApi perform change api
     * @return string
     */
    private function getNextInvoiceDate(AccountChange_PerformChangeApi $performChangeApi)
    {
        $coreService = $this->getCoreService($performChangeApi->getServiceId());
        $changeDate = $coreService->getNextInvoiceDate();

        if ($this->nextInvoiceIsToday($changeDate)) {
            $changeDate = $coreService->getNextNextInvoiceDate()->toMySql();
        } else {
            $changeDate = $changeDate->toMySql();
        }

        return $changeDate;
    }
}