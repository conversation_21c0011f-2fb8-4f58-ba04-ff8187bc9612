<?php

class AccountChange_AccountChangeOrder_Test extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 123;

    public function testShouldRejectMissingServiceId()
    {
        $accountChangeOrder = new AccountChange_AccountChangeOrder();
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrder->validate();
    }

    public function testShouldRejectNegativeServiceId()
    {
        $accountChangeOrder = new AccountChange_AccountChangeOrder();
        $accountChangeOrder->setServiceId(-1);
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrder->validate();
    }

    public function testShouldRejectServiceIdThatIsNotLinkedToAnAccount()
    {
        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(true);

        $accountChangeOrder->setServiceId(1);
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrder->validate();
    }

    public function testShouldRejectEmptyServiceDefinitionId()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrder->validate();
    }

    public function testShouldRejectNegativeServiceDefinitionId()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(-1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrder->validate();
    }

    public function testShouldRejectPromotionCodeThatDoesNotExist()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M', 'getPromotionFromPresetDiscountTables'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array());

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromPresetDiscountTables')
            ->willReturn(array());

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setAddress(new AccountChange_AccountChangeAddress());
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrder->validate();
    }

    public function testShouldRejectTypeThatDoesNotMatchAnExpectedType()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setType(100);
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrder->validate();
    }

    public function testShouldRejectInvalidAddress()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->setOneOffCharges(
            array(
                array(
                    'name' => 'test',
                    'amount' => 12,
                    'amountExVat' => 10
                )
            )
        );
        $address = new AccountChange_AccountChangeAddress();
        $address->setBuildingName(null);
        $accountChangeOrder->setAddress($address);
        $accountChangeOrder->setIsInstantRecontract(true);
        $accountChangeOrder->setIsHouseMove(true);
        $this->setExpectedException(
            "AccountChange_InvalidAccountChangeOrderException",
            "Supplied address is not valid."
        );
        $accountChangeOrder->validate();
    }

    public function testShouldRejectWithRetainAndContract()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->setOneOffCharges(
            array(
                array(
                    'name' => 'test',
                    'amount' => 12,
                    'amountExVat' => 10
                )
            )
        );
        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory("a");
        $address->setAddressReference("**********");
        $address->setBuildingName("The House");
        $address->setCountry("");
        $address->setCounty("");
        $address->setPostTown("Sheffield");
        $address->setCssDatabaseCode("AT");
        $address->setPostCode("S1 2GU");
        $address->setThoroughfareName("The Street");
        $accountChangeOrder->setAddress($address);
        $accountChangeOrder->setIsInstantRecontract(true);
        $accountChangeOrder->setIsHouseMove(true);
        $accountChangeOrder->setIsRecontract(true);
        $accountChangeOrder->setContract(new AccountChange_AccountChangeOrderContract());
        $accountChangeOrder->setRetainCurrentContracts(false);
        $this->setExpectedException(
            "AccountChange_InvalidAccountChangeOrderException",
            "Contract details supplied are invalid, either retain or contract needs to be set not both"
        );
        $accountChangeOrder->validate();
    }

    public function testShouldRejectWithNoRetainAndNullContract()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->setOneOffCharges(
            array(
                array(
                    'name' => 'test',
                    'amount' => 12,
                    'amountExVat' => 10
                )
            )
        );
        $address = new AccountChange_AccountChangeAddress();
        $accountChangeOrder->setAddress($address);
        $address->setAddressCategory("a");
        $address->setAddressReference("**********");
        $address->setBuildingName("The House");
        $address->setCountry("");
        $address->setCounty("");
        $address->setPostTown("Sheffield");
        $address->setCssDatabaseCode("AT");
        $address->setPostCode("S1 2GU");
        $address->setThoroughfareName("The Street");
        $accountChangeOrder->setIsInstantRecontract(true);
        $accountChangeOrder->setIsHouseMove(true);
        $accountChangeOrder->setRetainCurrentContracts(false);
        $accountChangeOrder->setContract(null);
        $this->setExpectedException(
            "AccountChange_InvalidAccountChangeOrderException",
            "Contract details supplied are invalid, either retain or contract needs to be set not both"
        );
        $accountChangeOrder->validate();
    }

    /**
     * @param boolean                                  $retain   bol
     * @param AccountChange_AccountChangeOrderContract $contract contract object
     * @dataProvider getDataForShouldAcceptValidOrder
     */
    public function testShouldAcceptValidOrder($retain, $contract)
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->setOneOffCharges(
            array(
                array(
                    'name' => 'test',
                    'amount' => 12,
                    'amountExVat' => 10
                )
            )
        );

        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory("a");
        $address->setAddressReference("**********");
        $address->setBuildingName("The House");
        $address->setCountry("");
        $address->setCounty("");
        $address->setPostTown("Sheffield");
        $address->setCssDatabaseCode("AT");
        $address->setPostCode("S1 2GU");
        $address->setThoroughfareName("The Street");
        $accountChangeOrder->setAddress($address);

        $accountChangeOrder->setIsInstantRecontract(true);
        $accountChangeOrder->setIsHouseMove(true);
        $accountChangeOrder->setRetainCurrentContracts($retain);
        $accountChangeOrder->setContract($contract);
        $accountChangeOrder->validate();
    }

    public function getDataForShouldAcceptValidOrder()
    {
        return array(
            array(null, null),
            array(null, new AccountChange_AccountChangeOrderContract()),
            array(true, null),
        );
    }


    /**
     * @test
     */
    public function shouldRegisterAdditionalValidatorsIfBackDatedDateIsSet()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array(
                'doesNotHaveValidAccount',
                'getPromotionFromC2M',
                'getValidationCheck',
                'setAdditionalValidatorInformation',
                'createDateTime'
            ),
            array()
        );

        $expectedValidators = [
            'AccountChange_BackDatedDatePolicy' => AccountChange_BackDatedDatePolicy::class,
            'AccountChange_BillingPendingReratingPolicy' => AccountChange_BillingPendingReratingPolicy::class
        ];

        $validationCheckMock = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed'),
            array(),
            '',
            false
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('getValidationCheck')
            ->with($expectedValidators)
            ->willReturn($validationCheckMock);

        $backdatedString = '2021-09-27';

        $expectedBackDatedDate = DateTime::createFromFormat('Y-m-d', $backdatedString);

        $accountChangeOrder
            ->expects($this->once())
            ->method('createDateTime')
            ->with($backdatedString)
            ->willReturn($expectedBackDatedDate);

        $accountChangeOrder
            ->expects($this->once())
            ->method('setAdditionalValidatorInformation')
            ->with([
                'isScheduledChange' => true,
                'backDatedDate' => $expectedBackDatedDate
            ])
            ->willReturn($validationCheckMock);

        $validationCheckMock
            ->expects($this->once())
            ->method('isAccountChangeAllowed')
            ->willReturn(true);

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));
        $accountChangeOrder->setRetainCurrentContracts(true);

        $accountChangeOrder->setBackDatedDate($backdatedString);
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);

        $accountChangeOrder->validate();
    }

    public function testShouldNotRunAdditionalValidatorsWhenNoneAreSet()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M', 'handleAdditionalValidators'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder
            ->expects($this->never())
            ->method('handleAdditionalValidators');

        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory("a");
        $address->setAddressReference("**********");
        $address->setBuildingName("The House");
        $address->setCountry("");
        $address->setCounty("");
        $address->setPostTown("Sheffield");
        $address->setCssDatabaseCode("AT");
        $address->setPostCode("S1 2GU");
        $address->setThoroughfareName("The Street");
        $accountChangeOrder->setAddress($address);
        $accountChangeOrder->setRetainCurrentContracts(true);
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);

        $accountChangeOrder->validate();
    }

    public function testShouldRunAdditionalValidatorsWhenSet()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array(
                'doesNotHaveValidAccount',
                'getPromotionFromC2M',
                'getEndUserActor',
                'getValidatorAdditionalInformation'
            ),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getValidatorAdditionalInformation')
            ->will($this->returnValue(array('shouldValidateOk' => true)));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getEndUserActor')
            ->will($this->returnValue($this->aMockActor()));

        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory("a");
        $address->setAddressReference("**********");
        $address->setBuildingName("The House");
        $address->setCountry("");
        $address->setCounty("");
        $address->setPostTown("Sheffield");
        $address->setCssDatabaseCode("AT");
        $address->setPostCode("S1 2GU");
        $address->setThoroughfareName("The Street");
        $accountChangeOrder->setAddress($address);
        $accountChangeOrder->setRetainCurrentContracts(true);

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(self::SERVICE_ID);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->registerAdditionalValidator(testValidatorPassesValidation::class);

        $accountChangeOrder->validate();
    }

    public function testShouldOnlyRunEachAdditionalValidatorOnce()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array(
                'doesNotHaveValidAccount',
                'getPromotionFromC2M',
                'getEndUserActor',
                'getValidatorAdditionalInformation'
            ),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getValidatorAdditionalInformation')
            ->will($this->returnValue(array('shouldValidateOk' => true)));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getEndUserActor')
            ->will($this->returnValue($this->aMockActor()));

        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory("a");
        $address->setAddressReference("**********");
        $address->setBuildingName("The House");
        $address->setCountry("");
        $address->setCounty("");
        $address->setPostTown("Sheffield");
        $address->setCssDatabaseCode("AT");
        $address->setPostCode("S1 2GU");
        $address->setThoroughfareName("The Street");
        $accountChangeOrder->setAddress($address);
        $appointmentData = array(
            "notes" => "Test the notes for appointment",
            "live" => array(
                "date" => "10-09-2021",
                "timeSlot" => "AM",
                "ref" => "Andy"
            )
        );
        $accountChangeOrder->setRetainCurrentContracts(true);

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(self::SERVICE_ID);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->registerAdditionalValidator(testValidatorPassesValidation::class);
        $accountChangeOrder->registerAdditionalValidator(testValidatorPassesValidation::class);
        $accountChangeOrder->setAppointment(new AccountChange_AccountChangeAppointment($appointmentData));
        $accountChangeOrder->validate();
    }

    /**
     *
     * @expectedException AccountChange_InvalidAccountChangeOrderException
     *
     */
    public function testShouldRunMultipleValidatorsAndFailIfAnyDoNotPassValidation()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId(1);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array(
                'doesNotHaveValidAccount',
                'getPromotionFromC2M',
                'getEndUserActor',
                'getValidatorAdditionalInformation'
            ),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getValidatorAdditionalInformation')
            ->will($this->returnValue(array()));

        $accountChangeOrder
            ->expects($this->once())
            ->method('getEndUserActor')
            ->will($this->returnValue($this->aMockActor()));

        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory("a");
        $address->setAddressReference("**********");
        $address->setBuildingName("The House");
        $address->setCountry("");
        $address->setCounty("");
        $address->setPostTown("Sheffield");
        $address->setCssDatabaseCode("AT");
        $address->setPostCode("S1 2GU");
        $address->setThoroughfareName("The Street");
        $accountChangeOrder->setAddress($address);
        $accountChangeOrder->setRetainCurrentContracts(true);

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(self::SERVICE_ID);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->registerAdditionalValidator(testValidatorPassesValidation::class);
        $accountChangeOrder->registerAdditionalValidator(testValidatorFailsValidaton::class);
        $accountChangeOrder->validate();
    }

    /**
     */
    public function testAllowServiceComponentIdToBePassedForBroadbandInsteadOfServiceDefinitionId()
    {
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceComponentId(22);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->validate();
    }

    /**
     */
    public function testShouldFailValidationIfNoBroadbandServiceDefinitionOrServiceComponentId()
    {
        $this->setExpectedException("AccountChange_InvalidAccountChangeOrderException");
        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->validate();
    }

    /**
     */
    public function testShouldAcceptAnOrderWhenCreatedUsingProductsArray()
    {
        $products[] = array('type' => 'BROADBAND', 'serviceComponentId' => 1234);
        $products[] = array('type' => 'CALL_PLAN', 'serviceComponentId' => 222);

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();

        $accountChangeOrderProducts->buildProductsFromArray($products);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder->setProducts($accountChangeOrderProducts);

        $orderProducts = $accountChangeOrder->getProducts();
        $this->assertEquals(1234, $orderProducts->getServiceComponentId());
        $this->assertEquals(222, $orderProducts->getPhoneComponentId());

        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->validate();
    }

    /**
     */
    public function testShouldProvideMobileVersionOfCallPlanWhenBoltOnIsInProductsArray()
    {
        $products[] = array('type' => 'BROADBAND', 'serviceComponentId' => 1234);
        $products[] = array('type' => 'CALL_PLAN', 'serviceComponentId' => 222);
        $products[] = array('type' => 'MobileBoltOn', 'selected' => true);

        $boltOnMappings = array(
            array(
                'intServiceComponentID' => 222,
                'intBoltOnServiceComponentID' => 333
            )
        );

        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );

        $phoneHelperMock
            ->expects($this->once())
            ->method('getServiceComponentBoltOnMappings')
            ->will($this->returnValue($boltOnMappings));

        $accountChangeOrderProducts = $this->getMock(
                'AccountChange_AccountChangeOrderProducts',
                array('getPhoneHelper')
        );

        $accountChangeOrderProducts
            ->expects($this->once())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        $accountChangeOrderProducts->buildProductsFromArray($products);

        $accountChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('doesNotHaveValidAccount', 'getPromotionFromC2M'),
            array()
        );

        $accountChangeOrder
            ->expects($this->once())
            ->method('doesNotHaveValidAccount')
            ->willReturn(false);

        $accountChangeOrder
            ->expects($this->once())
            ->method('getPromotionFromC2M')
            ->willReturn(array('PROMOTION DATA'));

        $accountChangeOrder->setProducts($accountChangeOrderProducts);

        $orderProducts = $accountChangeOrder->getProducts();
        $this->assertEquals(1234, $orderProducts->getServiceComponentId());
        $this->assertEquals(333, $orderProducts->getPhoneComponentId());

        $accountChangeOrder->setServiceId(1);
        $accountChangeOrder->setPromotionCode('I AM A PROMOTION CODE');
        $accountChangeOrder->setType(1);
        $accountChangeOrder->validate();
    }

    public function aMockActor()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');
        return $actor;
    }
}


class testValidatorPassesValidation extends \AccountChange_AbstractValidationPolicy
{

    private $isValid;

    public function setIsValid($isValid)
    {
        $this->isValid = $isValid;
    }

    public function getIsValid()
    {
        return $this->isValid;
    }

    public function validate()
    {
        return true;
    }

    public function getFailure()
    {
        return "failed.";
    }

    public function getErrorCode()
    {
        return "errorCode";
    }
}

class testValidatorFailsValidaton extends testValidatorPassesValidation
{
    public function validate()
    {
        return false;
    }
}
