<?php
/**
 * Scheduled Change Class File
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      link
 */
/**
 * Scheduled Change class responsible for the state/change of a scheduled account change
 *
 * This is the manager class to deal with AccountChange_ScheduledChange_* classes which in turn
 * know how to deal with knowing if a change is scheduled and how to cancel them
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      link
 */
class AccountChange_ScheduledChange
{
    /**
     * Array of all the types of change that can take place
     *
     * @var array
     */
    private $_types = array(
        'AccountChange_ScheduledChange_Adsl',
        'AccountChange_ScheduledChange_Wlr'
    );

    /**
     * Store of the all the AccountChange_IScheduledChange we want
     * to know about
     *
     * @var array
     */
    private $_schedules = array();

    /**
     * Customers service id
     *
     * @var int
     */
    private $_serviceId;

    /**
     * Constructor
     *
     * @param int $serviceId Service Id of the customer we want to check
     *
     * @api
     */
    public function __construct($serviceId)
    {
        $this->_serviceId = $serviceId;

        foreach ($this->_types as $type) {
            $this->_schedules[] = new $type($serviceId);
        }
    }

    /**
     * Getter for all of the changes that are scheduled
     *
     * @api
     *
     * @return AccountChange_IScheduledChange[] that have scheduled change
     */
    public function getScheduledChanges()
    {
        $changes = array();

        foreach ($this->_schedules as $schedule) {
            $change = $schedule->hasScheduledChange();

            if ($change) {
                $changes[] = $schedule;
            }
        }

        return $changes;
    }

    /**
     * Cancel any change that is past in
     *
     * @param Auth_BusinessActor $actioner  Business Actor performing the action
     * @param array              $schedules Set of schedules we want to change
     *
     * @api
     *
     * @return void
     */
    public function cancelChange(Auth_BusinessActor $actioner, array $schedules = array())
    {
        if (!empty($schedules)) {
            $comment = 'The following product changes have been cancelled: <br />';
            foreach ($schedules as $schedule) {
                $comment .= '<b>' . $schedule->getProductType() .
                    ' change to ' . $schedule->getNewProductName() . '</b> on the ' .
                    $schedule->getChangeDate() . '<br />';
                $schedule->cancelChange($actioner);
            }

            $notice = new AccountChange_ServiceNotice($comment);
            self::raiseServiceNotice($notice, $this->_serviceId);
        }
    }

    /**
     * Raise a given service notice
     *
     * @param AccountChange_ServiceNotice $notice    Service notice to raise
     * @param int                         $serviceId ServiceId
     *
     * @return void
     */
    public static function raiseServiceNotice(AccountChange_ServiceNotice $notice, $serviceId)
    {
        $client = BusTier_BusTier::getClient('serviceNotices');

        $actor = self::getBusinessActor($serviceId);

        $serviceNotice = $client->createServiceNotice(
            $actor,
            Db_Manager::DEFAULT_TRANSACTION
        );

        $serviceNotice->setBody($notice->getComment());
        $serviceNotice->setServiceNoticeTypeId($notice->getServiceNoticeTypeId());
        $serviceNotice->setActionerId($actor->getActorId());
        $serviceNotice->write();
    }

    /**
     * Get the business actor
     *
     * @param int $serviceId Customer's service id
     *
     * @return Auth_BusinessActor
     */
    public static function getBusinessActor($serviceId)
    {
        $businessActor = Auth_BusinessActor::getActorByExternalUserId(ltrim($serviceId, '0'));
        return $businessActor;
    }
}
