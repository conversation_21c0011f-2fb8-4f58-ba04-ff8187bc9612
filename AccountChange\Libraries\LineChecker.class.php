<?php

use AccountChange_LineCheckerCriteria as LineCheckerCriteria;

class AccountChange_LineChecker
{
    /**
     * The maximum download speed currently supported by Plusnet for all products.
     *
     * @var integer
     */
    const MAX_DOWN_SPEED = 80000;

    /**
     * The maximum upload speed currently supported by Plusnet for all products.
     *
     * @var integer
     */
    const MAX_UP_SPEED = 20000;

    public function performLineCheck(
        $objCliNumber,
        $objPostCode,
        $strAddressReference = '',
        $strCssDatabaseCode = '',
        $strThoroughFareNumber = '',
        $bolHouseMove = false,
        $objCoreService,
        $objLineCheckResult
    ) {
        if (!$bolHouseMove) {
            $objResult = $this->getLineCheckResult(
                LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO,
                $objCliNumber,
                $objCoreService,
                $objLineCheckResult,
                LineCheck_Result::SOURCE_PRODUCT_CHANGE
            );

            if (in_array(
                $objResult->getErrorId(),
                array(
                    LineCheck_Result::ERR_ID_TELNO_NOT_FOUND,
                    LineCheck_Result::ERR_ID_NOT_BT_LINE,
                    LineCheck_Result::ERR_ID_TELNO_CEASED)
            )) {
                $objResult = $this->getLineCheckResult(
                    LineCheck_RequestManager::LINE_CHECK_TYPE_ADDRESS,
                    array(
                        "thoroughfareNumber" => $strThoroughFareNumber,
                        "postcode" => $objPostCode
                    ),
                    $objCoreService,
                    $objLineCheckResult,
                    LineCheck_Result::SOURCE_PRODUCT_CHANGE
                );
            }
            if (in_array(
                    $objResult->getErrorId(),
                    array(
                        LineCheck_Result::ERR_ID_TELNO_NOT_FOUND,
                        LineCheck_Result::ERR_ID_NOT_BT_LINE,
                        LineCheck_Result::ERR_ID_TELNO_CEASED)
                ) && $this->isInternal()) {
                $objResult = $this->getLineCheckResult(
                    LineCheck_RequestManager::LINE_CHECK_TYPE_POSTCODE,
                    $objPostCode,
                    $objCoreService,
                    $objLineCheckResult,
                    LineCheck_Result::SOURCE_PRODUCT_CHANGE
                );
            }
        } else {
            //Criteria #1 - Address Ref + cssDatabaseCode
            if (!empty($strAddressReference) && !empty($strCssDatabaseCode)) {
                $criteriaOne = array('Input' => $strAddressReference, 'DistrictID' => $strCssDatabaseCode);

                $objResult = $this->getLineCheckResult(
                    LineCheck_RequestManager::LINE_CHECK_TYPE_NAD,
                    $criteriaOne,
                    $objCoreService,
                    $objLineCheckResult,
                    LineCheck_Result::SOURCE_HOUSE_MOVE
                );
            }

            //Criteria #2 - ThoroughfareNumber + Postcode
            if ((empty($objResult) || !empty($objResult->getErrorId())) &&
                (!empty($strThoroughFareNumber) && !empty($objPostCode))
            ) {
                $criteriaTwo = LineCheckerCriteria::buildLineCheckCriteria($objPostCode, $strThoroughFareNumber);
                $objResult = $this->getLineCheckResult(
                    LineCheck_RequestManager::LINE_CHECK_TYPE_ADDRESS,
                    $criteriaTwo,
                    $objCoreService,
                    $objLineCheckResult,
                    LineCheck_Result::SOURCE_HOUSE_MOVE
                );
            }

            //Criteria #3 - Postcode Only
            if ((empty($objResult) || !empty($objResult->getErrorId())) &&
                !empty($objPostCode)
            ) {
                $objResult = $this->getLineCheckResult(
                    LineCheck_RequestManager::LINE_CHECK_TYPE_POSTCODE,
                    $objPostCode,
                    $objCoreService,
                    $objLineCheckResult,
                    LineCheck_Result::SOURCE_HOUSE_MOVE
                );
            }
        }

        if (LineCheck_Result::ERR_ID_NO_ERRORS == $objResult->getErrorId()) {
            $this->insertPendingServiceLineData($objCoreService, $objResult);
            return $objResult;
        } else {
            throw new AccountChange_LineCheckResultException($objResult->getErrorId());
        }
    }

    public function insertPendingServiceLineData(
        $objCoreService,
        LineCheck_Result $objResult = null,
        $strPrivateTransaction = 'ShowLineCheckReq_insertPendingServiceLineData'
    ) {
        try {
            $objAdaptor = Db_Manager::getAdaptor('LineCheck', $strPrivateTransaction);

            $intServiceId = $objCoreService->getServiceId();
            $intExpectedSpeed = (is_null($objResult)) ? null : $objResult->getHighestAvailableSpeed();
            $strExchangeCode = (is_null($objResult)) ? null : $objResult->getExchangeCode();
            $arrMarket
                = (is_null($strExchangeCode))
                ? $objAdaptor->getMarketByHandle('MARKET_UNKNOWN') : $objAdaptor->getExchangeMarket($strExchangeCode);
            $intMarketId = $arrMarket['intMarketId'];

            $objAdaptor->insertServiceLineData($intServiceId, $intMarketId, false, $intExpectedSpeed);

            Db_Manager::commit($strPrivateTransaction);
            return true;
        } catch (Exception $objException) {
            Db_Manager::rollback($strPrivateTransaction);
            return false;
        }
    }

    protected function getLineCheckResult(
        $intLineCheckType,
        $strCli,
        $objCoreService,
        $objLineCheckResult,
        $intLineCheckSource = LineCheck_Result::SOURCE_DEFAULT
    ) {
        Db_Manager::setUseMaster(true);

        $objRequest = LineCheck_RequestManager::factory(
            $intLineCheckType,
            $strCli,
            null,
            self::MAX_DOWN_SPEED,
            self::MAX_UP_SPEED
        );

        $objLineCheckResult->setServiceId($objCoreService->getServiceId());
        $objLineCheckResult->setServiceDefinitionId($objCoreService->getType());
        $objLineCheckResult->setLineCheckStatusId(LineCheck_Result::STATUS_ACTIVE);

        $objLineCheck = new LineCheck_LineCheck($objRequest);
        $objLineCheck->sendRequest();
        $objLineCheck->getResult($objLineCheckResult);
        $objLineCheck->save($intLineCheckSource);

        return $objLineCheckResult;
    }

    /**
     * Returns if the current connected user is internal or external.
     *
     * @return bool
     */
    protected function isInternal()
    {
        $objLogin = Auth_Auth::getCurrentLogin();

        if ($objLogin instanceof Auth_Login) {
            return $objLogin->getBusinessActor()->getUserType() === 'PLUSNET_STAFF';
        }
        return false;
    }
}
