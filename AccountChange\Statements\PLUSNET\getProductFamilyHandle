server: coredb
role: slave
rows: single
statement:

SELECT
    pf.vchHandle AS productFamilyHandle,
    pv.vchHandle AS productVariantHandle
FROM
    products.service_definitions AS sd
INNER JOIN products.tblProductVariant AS pv
    ON sd.intProductVariantId = pv.intProductVariantId
INNER JOIN products.tblProductFamily AS pf
    ON pv.intProductFamilyId = pf.intProductFamilyId
WHERE sd.service_definition_id = :intServiceDefinitionId;
