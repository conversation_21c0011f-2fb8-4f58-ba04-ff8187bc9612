<?php
/**
 * Account Change Line Rental Saver Action Test
 *
 * @category  AccountChnage_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-10-31
 */

/**
 * Account Change Line Rental Saver action test
 *
 * Testing class for AccountChange_Action_LineRentalSaverAdd
 *
 * @category  AccountChange_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-10-31
 *
 */
class AccountChange_Action_LineRentalSaverAddTest extends PHPUnit_Framework_TestCase
{
    /**
     * Tests that execute() transfers an lrs contract if we've got an
     * existing one
     *
     * @covers AccountChange_Action_LineRentalSaverAdd::execute
     *
     * @return void
     **/
    public function testExecuteTransfersWhereAppropriate()
    {
        // In this case we've selected line rate saver in the wizard
        AccountChange_Registry::instance()->setEntry(
            'bolAddLineRentalSaver',
            true
        );

        // In this case we've added a new wlr component
        AccountChange_Registry::instance()->setEntry(
            'bolHaveAddedWlrComponent',
            true
        );

        $date = I18n_Date::fromString('2011-01-04');


        $mockAction = $this->getMock(
            'AccountChange_Action_LineRentalSaverAdd',
            array(
                'getEndDateOfActiveLrs',
                'transferLrs',
                'addPendingEntry',
                'createLrsInstance'
            ),
            array(2345)
        );

        $mockAction
            ->expects($this->once())
            ->method('getEndDateOfActiveLrs')
            ->will($this->returnValue($date));

        $mockAction
            ->expects($this->once())
            ->method('transferLrs')
            ->with($date);

        $mockAction
            ->expects($this->once())
            ->method('createLrsInstance')
            ->with('ACTIVE');

        $mockAction->expects($this->never())->method('addPendingEntry');
        $mockAction->execute();
    }

    /**
     * Tests that transferLrs does nothing if there's no unconfigured
     * lrs instance to transfer to
     *
     * @covers AccountChange_Action_LineRentalSaverAdd::transferLrs
     *
     * @return void
     **/
    public function testTransferLrsNothingToTransferTo()
    {
        $sid = 123456;
        AccountChange_Registry::instance()->setEntry('bolSchedule', true);

        $mockAction = $this->getMock(
            'AccountChange_Action_LineRentalSaverAdd',
            array(
                'getServiceId',
                'getLineRentalSaver'
            ),
            array(2345)
        );

        $mockAction
            ->expects($this->once())
            ->method('getServiceId')
            ->will($this->returnValue($sid));

        $mockAction
            ->expects($this->once())
            ->method('getLineRentalSaver')
            ->with($sid, 'UNCONFIGURED')
            ->will($this->returnValue(null));

        $date = I18n_Date::fromTimestamp(*********);

        $mockAction
            ->expects($this->never())
            ->method('transfer');

        $mockAction->transferLrs($date);
    }

    /**
     * Tests that transferLrs defaults to the annual tariff where no tariff id has been
     * found.
     *
     * @covers AccountChange_Action_LineRentalSaverAdd::transferLrs
     *
     * @return void
     **/
    public function testTransferLrsNoTariffIdFound()
    {
        AccountChange_Registry::instance()->setEntry('bolSchedule', true);
        $sid = 123456;
        $pcid = 23456;
        $date = I18n_Date::fromTimestamp(*********);
        $tariffId = 1086;

        $mockAction = $this->getMock(
            'AccountChange_Action_LineRentalSaverAdd',
            array(
                'getServiceId',
                'getLineRentalSaver',
                'getTariffIdFromInstanceId',
                'getLrsInstance'
            ),
            array(2345)
        );

        $mockLrs = $this->getMock(
            'PrepaidContract_Instance',
            array(
                'getProductComponentInstanceId',
                'getTariffByDuration',
                'transfer'
            ),
            array($pcid)
        );

        $mockLrs
            ->expects($this->once())
            ->method('getProductComponentInstanceId')
            ->will($this->returnValue($pcid));

        $mockLrs
            ->expects($this->once())
            ->method('getTariffByDuration')
            ->with('ANNUAL')
            ->will($this->returnValue(array('intTariffID' => $tariffId)));

        $mockLrs
            ->expects($this->once())
            ->method('transfer')
            ->with($tariffId, $date);

        $mockAction
            ->expects($this->once())
            ->method('getServiceId')
            ->will($this->returnValue($sid));

        $mockAction
            ->expects($this->once())
            ->method('getLineRentalSaver')
            ->with($sid, 'UNCONFIGURED')
            ->will($this->returnValue($mockLrs));

        $mockAction
            ->expects($this->once())
            ->method('getLrsInstance')
            ->will($this->returnValue($mockLrs));

        $mockAction
            ->expects($this->once())
            ->method('getTariffIdFromInstanceId')
            ->with($pcid)
            ->will($this->returnValue(null));


        $mockAction->transferLrs($date);
    }

    /**
     * Test transferLrs will set proper tariffId on instant AccountChange
     *
     * @return void
     */
    public function testTransferLrsWillSetProperTariffIdOnInstantAccountChange()
    {
        AccountChange_Registry::instance()->setEntry('bolSchedule', false);

        $date = I18n_Date::fromString('2018-10-25');

        $serviceId = 1234321;
        $newPciId = *********;
        $tariffId = 1221;

        /** @var PrepaidContract_Instance | \PHPUnit_Framework_MockObject_MockObject $mockPrepaidContract */
        $mockPrepaidContract = $this->getMockBuilder('PrepaidContract_Instance')
            ->setMethods(array('getTariffByDuration', 'transfer'))
            ->setConstructorArgs(array($newPciId))
            ->getMock();

        $mockPrepaidContract->expects($this->once())
            ->method('getTariffByDuration')
            ->with('ANNUAL')
            ->will($this->returnValue(array('intTariffID' => $tariffId)));

        $mockPrepaidContract->expects($this->never())
            ->method('transfer');

        /** @var \Db_Adaptor | \PHPUnit_Framework_MockObject_MockObject $mockDbAdaptor */
        $mockDbAdaptor = $this->getMockBuilder('Db_Adaptor')
            ->setMethods(array('updateProductComponentInstanceTariffId', 'updateProductComponentContractTariffId'))
            ->setConstructorArgs(array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, true))
            ->getMock();

        $mockDbAdaptor->expects($this->once())
            ->method('updateProductComponentInstanceTariffId')
            ->with($this->equalTo($tariffId), $this->equalTo($newPciId));

        $mockDbAdaptor->expects($this->once())
            ->method('updateProductComponentContractTariffId')
            ->with($this->equalTo($tariffId), $this->equalTo($newPciId));

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor, Db_Manager::DEFAULT_TRANSACTION);

        /** @var AccountChange_Action_LineRentalSaverAdd | \PHPUnit_Framework_MockObject_MockObject $mockAction */
        $mockAction = $this->getMockBuilder('AccountChange_Action_LineRentalSaverAdd')
            ->setMethods(array('getLineRentalSaver'))
            ->setConstructorArgs(array($serviceId, array()))
            ->getMock();

        $mockAction->expects($this->once())
            ->method('getLineRentalSaver')
            ->with($this->equalTo($serviceId), $this->equalTo('ACTIVE'))
            ->will($this->returnValue($mockPrepaidContract));

        $mockAction->transferLrs($date);
    }

    /**
     * Test transferLrs will not throw exception when tariff update fail
     *
     * @return void
     */
    public function testTransferLrsWillNotThrowExceptionWhenTariffUpdateFail()
    {
        AccountChange_Registry::instance()->setEntry('bolSchedule', false);

        $date = I18n_Date::fromString('2018-10-25');

        $serviceId = 1234321;
        $newPciId = *********;
        $tariffId = 1221;

        /** @var PrepaidContract_Instance | \PHPUnit_Framework_MockObject_MockObject $mockPrepaidContract */
        $mockPrepaidContract = $this->getMockBuilder('PrepaidContract_Instance')
            ->setMethods(array('getTariffByDuration', 'transfer'))
            ->setConstructorArgs(array($newPciId))
            ->getMock();

        $mockPrepaidContract->expects($this->once())
            ->method('getTariffByDuration')
            ->with('ANNUAL')
            ->will($this->returnValue(array('intTariffID' => $tariffId)));

        $mockPrepaidContract->expects($this->never())
            ->method('transfer');

        /** @var \Db_Adaptor | \PHPUnit_Framework_MockObject_MockObject $mockDbAdaptor */
        $mockDbAdaptor = $this->getMockBuilder('Db_Adaptor')
            ->setMethods(array('updateProductComponentInstanceTariffId', 'updateProductComponentContractTariffId'))
            ->setConstructorArgs(array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, true))
            ->getMock();

        $mockDbAdaptor->expects($this->once())
            ->method('updateProductComponentInstanceTariffId')
            ->with($this->equalTo($tariffId), $this->equalTo($newPciId))
            ->will($this->throwException(new Exception('Exception message')));

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor, Db_Manager::DEFAULT_TRANSACTION);

        /** @var AccountChange_Action_LineRentalSaverAdd | \PHPUnit_Framework_MockObject_MockObject $mockAction */
        $mockAction = $this->getMockBuilder('AccountChange_Action_LineRentalSaverAdd')
            ->setMethods(array('getLineRentalSaver'))
            ->setConstructorArgs(array($serviceId, array()))
            ->getMock();

        $mockAction->expects($this->once())
            ->method('getLineRentalSaver')
            ->with($this->equalTo($serviceId), $this->equalTo('ACTIVE'))
            ->will($this->returnValue($mockPrepaidContract));

        $mockAction->transferLrs($date);
    }
}
