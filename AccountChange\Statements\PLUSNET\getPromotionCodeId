server: coredb
role: slave
rows: single
statement:

SELECT
    pc.intPromotionCodeId
FROM products.tblPromotionCode AS pc
INNER JOIN products.tblPromotionCodeServiceDefinition AS pcsd
    ON pcsd.intPromotionCodeId = pc.intPromotionCodeId
WHERE
    pc.vchPromotionCode = :promoCode
    AND pcsd.intServiceDefinitionId = :serviceDefinitionId
    AND (pc.dteValidFrom IS NULL OR pc.dteValidFrom <= NOW())
    AND (pc.dteValidTo IS NULL OR pc.dteValidTo >= NOW())