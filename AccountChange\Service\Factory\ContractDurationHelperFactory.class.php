<?php

/**
 * <AUTHOR>
 */

class AccountChange_ContractDurationHelperFactory implements AccountChange_Factory
{
    /**
     * @param string $serviceName service name
     * @return AccountChange_ContractDurationHelper
     */
    public static function createService($serviceName)
    {
        return new AccountChange_ContractDurationHelper(
            Db_Manager::getAdaptor('AccountChange'),
            AccountChange_ServiceManager::getService('FibreHelper')
        );
    }
}
