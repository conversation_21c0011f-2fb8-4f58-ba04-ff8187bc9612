<?php
/**
 * Testing class for the AccountChange_ServiceNotice
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 Plusnet
 * @since      File available since 2010-07-12
 */
/**
 * ServiceNotice Test Class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 Plusnet
 */
class AccountChange_ServiceNoticeTest extends PHPUnit_Framework_TestCase
{
    /**
     * Test the constructor and getters
     *
     * @covers AccountChange_ServiceNotice::__construct
     * @covers AccountChange_ServiceNotice::getComment
     * @covers AccountChange_ServiceNotice::getServiceNoticeTypeId
     *
     * @dataProvider provideDataForConstructor
     *
     * @return void
     */
    public function testConstructorWithData($comment, $type, $expectedComment, $expectedType)
    {
        $notice = new AccountChange_ServiceNotice($comment, $type);
        $actualComment = $notice->getComment();
        $actualType = $notice->getServiceNoticeTypeId();

        $this->assertEquals($expectedComment, $actualComment);
        $this->assertEquals($expectedType, $actualType);
    }

    /**
     * Data provider for testConstructorWithData
     *
     * @return array
     */
    public static function provideDataForConstructor()
    {
        return array(
            array('Testing is fun', 100, 'Testing is fun', 100)
        );
    }

    /**
     * Test the setters
     *
     * @covers AccountChange_ServiceNotice::__construct
     * @covers AccountChange_ServiceNotice::setComment
     * @covers AccountChange_ServiceNotice::setServiceNoticeTypeId
     *
     * @dataProvider provideDataForSetters
     *
     * @return void
     */
    public function testSettersWithData($comment, $type, $expectedComment, $expectedType)
    {
        $notice = new AccountChange_ServiceNotice();

        $this->assertAttributeEquals('', '_comment', $notice);
        $this->assertAttributeEquals(1, '_serviceNoticeTypeId', $notice);

        $notice->setComment($comment);
        $notice->setServiceNoticeTypeId($type);

        $this->assertAttributeEquals($expectedComment, '_comment', $notice);
        $this->assertAttributeEquals($expectedType, '_serviceNoticeTypeId', $notice);
    }

    /**
     * Data provider for testSettersWithData
     *
     * @return array
     */
    public static function provideDataForSetters()
    {
        return array(
            array('Testing is fun', 100, 'Testing is fun', 100)
        );
    }
}