<?php
require_once(__DIR__.'/PromotionRule.php');
require_once(__DIR__.'/PromotionRuleBase.php');

use Plusnet\C2mApiClient\Entity\Promotion;

class SortPromotionsByHighToLowPriority extends PromotionRuleBase implements PromotionRule
{
    /**
     * Arrange promotions so that those with the highest priority (the lowest
     * number) are at the top.
     *
     * @return array $promotions
     */
    public function handle(array $promotions)
    {
        if (count($promotions) > 1 ){
            usort($promotions, [$this, 'sortHighToLow']);
        }

        return $promotions;
    }

    private function sortHighToLow(Promotion $promo1, Promotion $promo2){
        if ($promo1->getPriority() === $promo2->getPriority()) {
            return 0;
        }
        return ($promo1->getPriority() < $promo2->getPriority()) ? -1 :  1;
    }

}
