CREATE TABLE `dbSystemEvents`.`tblProductChange` (
  `intProductChangeID` int(10) unsigned NOT NULL auto_increment,
  `intScheduledEventID` int(10) unsigned NOT NULL default '0',
  `intComponentID` int(10) unsigned NOT NULL default '0',
  `intNewComponentID` int(10) unsigned NOT NULL default '0',
  `uxtContractEnd` date default NULL,
  `stmLastUpdate` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (`intProductChangeID`),
  KEY `idxScheduledEventID` (`intScheduledEventID`),
  KEY `ixComponentID` (`intComponentID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1