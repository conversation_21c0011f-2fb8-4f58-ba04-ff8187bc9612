<?php

class AccountChange_AuditLogger
{
    /**
     * Function entry audit logging
     *
     * @param string $method should be passed as __METHOD__ | __FILE__.'::'.__FUNCTION__ | __FILE__
     *
     * @return void
     */
    public static function functionEntry($method)
    {
        self::write('ENTRY', $method);
    }

    /**
     * Function exit audit logging
     *
     * @param string $method should be passed as __METHOD__ | __FILE__.'::'.__FUNCTION__ | __FILE__
     *
     * @return void
     */
    public static function functionExit($method)
    {
        self::write('EXIT', $method);
    }

    /**
     * Function audit logging
     *
     * @param string $location where the logger is being called in the method
     * @param string $method   the calling method
     *
     * @return void
     */
    private static function write($location, $method)
    {
        Log_AuditLog::writeStandard(
            Log_LogData::factory("$location : " . $method, 'AccountChange')
        );
    }
}
