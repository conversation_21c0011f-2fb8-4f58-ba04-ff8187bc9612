server: coredb
role: slave
rows: multiple
statement:

SELECT
    DISTINCT sado.intDuration
FROM products.tblStaffAltDurationOption AS sado
INNER JOIN products.tblContractDefinition AS cd
    ON cd.intContractDefinitionID = sado.intContractDefinitionID
INNER JOIN products.tblProductContractDefinition AS pcd
    ON pcd.intContractDefinitionID = cd.intContractDefinitionID
WHERE
    pcd.intServiceDefinitionID = :serviceDefinitionId
    AND sado.intDuration >= :minimumContractDuration
ORDER BY sado.intDuration
