<?php

class AccountChange_EmailHandler_SpeedHelper
{
    const ESTIMATED_MAXIMUM_DOWNLOAD_SPEED_KEY = 'estMaxDownSpeed';
    const ESTIMATED_MINIMUM_DOWNLOAD_SPEED_KEY = 'estMinDownSpeed';

    /**
     * Format of the speed array that this helper will return
     *
     * @var array
     */
    private $speeds = array(
        'minimumEstimatedDownloadSpeedMbs' => null,
        'maximumEstimatedDownloadSpeedMbs' => null,
        'minimumEstimatedUploadSpeedMbs'   => null,
        'maximumEstimatedUploadSpeedMbs'   => null,
        'broadbandSpeedRange'              => null,
        'guaranteedSpeedValue'             => null,
        'maximumDownloadSpeedMbs'          => null,
        'advertisedDownloadSpeedMbs'       => null,
        'advertisedUploadSpeedMbs'         => null
    );

    private $lineCheckId;
    private $serviceDefinitionId;

    /**
     * AccountChange_EmailHandler_SpeedHelper constructor.
     *
     * @param int $lineCheckId         line check id
     * @param int $serviceDefinitionId service def id
     */
    public function __construct($lineCheckId, $serviceDefinitionId)
    {
        $this->lineCheckId = $lineCheckId;
        $this->serviceDefinitionId = $serviceDefinitionId;
    }

    /**
     * Get the estimated speeds from the database using the line check id, get the advertised speeds from C2M
     *
     * @return array
     */
    public function getSpeedData()
    {
        $this->getEstimatedSpeeds();
        $this->getAdvertisedSpeeds();

        return $this->speeds;
    }

    /**
     * @param integer $lineCheckId line check id
     *
     * @return boolean
     */
    public function getOptOut($lineCheckId)
    {
        $arrData['objLineCheckResult'] = $this->getLineCheckResultByLineCheckId($lineCheckId);
        return $arrData['objLineCheckResult']->didCustomerOptOut();
    }

    /**
     * @param integer $lineCheckId line check id
     * @return LineCheck_Result
     */
    protected function getLineCheckResultByLineCheckId($lineCheckId)
    {
        return new LineCheck_Result($lineCheckId);
    }

    /**
     * Use line check id to retrieve the estimated speeds from the database and append the results to the speed array
     * @return void
     */
    private function getEstimatedSpeeds()
    {
        $welcomeEmailSpeeds = $this->getWelcomeEmailSpeeds();
        $speedData = $welcomeEmailSpeeds->getSpeedData();

        $maximumEstimatedDownloadSpeedMbps = $this->formatSpeed($speedData[self::ESTIMATED_MAXIMUM_DOWNLOAD_SPEED_KEY]);
        $minimumEstimatedDownloadSpeedMbps = empty($speedData[self::ESTIMATED_MINIMUM_DOWNLOAD_SPEED_KEY]) ?
            $maximumEstimatedDownloadSpeedMbps :
            $this->formatSpeed($speedData[self::ESTIMATED_MINIMUM_DOWNLOAD_SPEED_KEY]);

        $maximumEstimatedUploadSpeedMbps = $this->formatSpeed($speedData['estMaxUpSpeed']);
        $minimumEstimatedUploadSpeedMbps = empty($speedData['estMinUpSpeed']) ?
            $maximumEstimatedUploadSpeedMbps :
            $this->formatSpeed($speedData['estMinUpSpeed']);

        $broadbandSpeedRange = false;
        if (!empty($speedData[self::ESTIMATED_MINIMUM_DOWNLOAD_SPEED_KEY]) &&
            !empty($speedData[self::ESTIMATED_MAXIMUM_DOWNLOAD_SPEED_KEY]) &&
            $speedData[self::ESTIMATED_MINIMUM_DOWNLOAD_SPEED_KEY] !=
            $speedData[self::ESTIMATED_MAXIMUM_DOWNLOAD_SPEED_KEY]) {
            $broadbandSpeedRange = true;
        }

        $guaranteedSpeedValue    = $this->formatSpeed($speedData['mgs']);
        $maximumDownloadSpeedMbs = $this->formatSpeed($speedData['maxDownSpeed']) . 'Mb';

        $this->speeds['maximumEstimatedDownloadSpeedMbs'] = $maximumEstimatedDownloadSpeedMbps;
        $this->speeds['minimumEstimatedDownloadSpeedMbs'] = $minimumEstimatedDownloadSpeedMbps;
        $this->speeds['maximumEstimatedUploadSpeedMbs']   = $maximumEstimatedUploadSpeedMbps;
        $this->speeds['minimumEstimatedUploadSpeedMbs']   = $minimumEstimatedUploadSpeedMbps;
        $this->speeds['broadbandSpeedRange']              = $broadbandSpeedRange;
        $this->speeds['guaranteedSpeedValue']             = $guaranteedSpeedValue;
        $this->speeds['maximumDownloadSpeedMbs']          = $maximumDownloadSpeedMbs;
        $this->speeds['minUpSpeed']                       = $this->formatSpeed($speedData['minUpSpeed']);
        $this->speeds['maxUpSpeed']                       = $this->formatSpeed($speedData['maxUpSpeed']);
        $this->speeds['maxDownSpeed']                     = $this->formatSpeed($speedData['maxDownSpeed']);
    }

    /**
     * Use C2M API to get the advertised speed for the new service definition
     *
     * @return void
     */
    private function getAdvertisedSpeeds()
    {
        $c2mProductOfferingName = $this->getC2MProductOfferingNameByServiceId($this->serviceDefinitionId);
        $c2mClient = $this->getC2MApiClient();
        $speedData = $c2mClient->getBroadbandSpeedData($c2mProductOfferingName);

        $advertisedDownloadSpeedMbs = $this->formatSpeed($speedData->getAvgDownloadSpeedKBPS());
        $advertisedUploadSpeedMbs   = $this->formatSpeed($speedData->getAvgUploadSpeedKBPS());
        $maximumDownloadSpeedMbps = $this->formatSpeed($speedData->getMaxDownloadSpeedKBPS());
        $maximumUploadSpeedMbps = $this->formatSpeed($speedData->getMaxUploadSpeedKBPS());

        $this->speeds['advertisedDownloadSpeedMbs'] = $advertisedDownloadSpeedMbs;
        $this->speeds['advertisedUploadSpeedMbs'] = $advertisedUploadSpeedMbs;
        $this->speeds['maximumDownloadSpeedMbps'] = $maximumDownloadSpeedMbps;
        $this->speeds['maximumUploadSpeedMbps'] = $maximumUploadSpeedMbps;
    }

    /**
     * Wrapper function for unit testing
     *
     * @return SignupApplication_WelcomeEmailSpeeds
     */
    protected function getWelcomeEmailSpeeds()
    {
        $welcomeEmailSpeeds = new SignupApplication_WelcomeEmailSpeeds();
        $welcomeEmailSpeeds->setLineCheckId($this->lineCheckId);
        $welcomeEmailSpeeds->setServiceDefinitionId($this->serviceDefinitionId);

        return $welcomeEmailSpeeds;
    }

    /**
     * Wrapper function for unit testing
     *
     * @return Plusnet\C2mApiClient\C2mClient
     */
    protected function getC2MApiClient()
    {
        return \BusTier_BusTier::getClient('c2mapi.v5');
    }

    /**
     * Given a service definition id, return the c2m product offering name
     *
     * @param $serviceDefinitionId string The service definition id we want to get the c2m product offering name for
     *
     * @return string The c2m product offering name
     */
    protected function getC2MProductOfferingNameByServiceId($serviceDefinitionId)
    {
        $databaseAdaptor = Db_Manager::getAdaptor('AccountChange');
        $c2mProductOffering = $databaseAdaptor->getProductOfferingsByServiceIDs($serviceDefinitionId);

        return $c2mProductOffering[0]['productOfferingName'];
    }

    /**
     * Convert the speed into megabits per second with two decimal places. If there are trailing zeros or a trailing
     * decimal point, remove them/it.
     *
     * @param int $speedKbps The speed in kilobits per second
     *
     * @return string The formatted speed e.g 23500 -> 23.5
     */
    private function formatSpeed($speedKbps)
    {
        if (!$speedKbps) {
            return null;
        }

        $speedMbps = number_format($speedKbps/1000, 2);
        $speedMbps = rtrim($speedMbps, '0');
        $speedMbps = rtrim($speedMbps, '.');

        return $speedMbps;
    }
}
