<?php
include_once 'phing/Task.php';
class ServerRoles extends Task {

    private $_availableRolesDir;
    private $_serverRolesDir;
    private $_cvsModule;

    public function init() {
    }
    
    public function setAvailableRolesDir($str) {
        $this->_availableRolesDir = $str;
    }

    public function setServerRolesDir($str) {
        $this->_serverRolesDir = $str;
    }

    public function setCvsModule($str) {
        $this->_cvsModule = $str;
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!is_dir($this->_availableRolesDir)) {
		    throw new BuildException("$this->_availableRolesDir not a valid directory", $this->getLocation());
		} 
		if (!is_dir($this->_serverRolesDir)) {
		    throw new BuildException("$this->_serverRolesDir not a valid directory", $this->getLocation());
		} 
		
		//get existing role names
		$arrOldRoles = $this->_getRoleNames($this->_serverRolesDir);
		//get available role names
		$arrAvailableRoles = $this->_getRoleNames($this->_availableRolesDir);
		//match two arrays
		$arrActiveRoles = array(); 
		foreach ($arrAvailableRoles as $key => $rolename) {
			if(in_array($rolename,$arrOldRoles)) $arrActiveRoles[$key] = $rolename;
		}
		if (empty($arrAvailableRoles)) return;
		
        $in = new ConsoleReader();           
	    do {
			//user choice of server roles
			$this->log("Select server Roles:");
			foreach ($arrAvailableRoles as $key => $strRole) {
				//is role active?
				in_array($strRole,$arrActiveRoles) ? $state = '(active)' : $state ='';
				$this->log("$key) $strRole $state");
				
			}
	        //get user input
	        try {
				echo "Enter a number to toggle selection (Enter to continue): ";
	            $input = $in->readLine();
				if(isset($arrActiveRoles[$input])) {
				//toggle active role
					unset($arrActiveRoles[$input]);
				} elseif (isset($arrAvailableRoles[$input])) {
				//activate role
					$arrActiveRoles[$input] = $arrAvailableRoles[$input];					
				}
				
	        } catch (Exception $e) {
	            throw new BuildException("Failed to read input from Console.", $e);
	        }
	    } while ($input !== ""); //continue on (enter)
        
		//rewrite roles
		//delete *.role in _serverRolesDir
		foreach ($arrOldRoles as $roleName) {
			$filename = $this->_serverRolesDir."/$roleName.role";
			if(!unlink($filename)) {
				throw new BuildException("Failed to delete $filename.");
			}
		}
		//copy *.role from _availableRolesDir based on $arrActiveRoles
		foreach ($arrActiveRoles as $roleName) {
			$source = "$this->_availableRolesDir/$roleName.role";
			$target = "$this->_serverRolesDir/$roleName.role";
			if(!copy($source, $target)) {
				throw new BuildException("Failed to copy $source to $target.");
			}
			//create/check /share/admin/portal/cron-<rolename> directories
			$dirName = "/share/admin/portal/cron-$roleName";
			if(is_dir($dirName) or mkdir($dirName)) {
				
				$this->_handleCronFiles($roleName);
			} else {
				
				throw new BuildException("Directory $dirName doesn't exist and cannot be created.");
			}
		}
    }

	private function _handleCronFiles($roleName) 
	{
		//get role users
		$arrCronEntries = $this->_scanCronEntries($roleName);
		foreach ($arrCronEntries as $username => $array) {
			
			$filename = "/share/admin/portal/cron-$roleName/master-$username-crontab";
			if(!file_exists($filename))
			{
				touch($filename);
			}

//			system("sudo crontab -u $username /share/admin/portal/cron-$roleName/master-$username-crontab");
		
			//process cron file
			$this->_processCronFile($filename,$arrCronEntries[$username],$roleName,$username);
		}
	}

	private function _processCronFile($filename,array $arrCronEntriesFiles,$roleName,$username)
	{
		$arrOldCronFile = array_map('rtrim',file($filename));
		//extract existing entries from cron file
			
		$arrOldEntries = preg_grep("/^\d+/", $arrOldCronFile);
		//cron entries to add
		$arrNewEntries = array();
		
		foreach ($arrCronEntriesFiles as $strEntryFile => $type) {

			$arrCronEntriesFile = array_map('rtrim',file($strEntryFile));
			$arrNewEntries = array_merge($arrNewEntries, preg_grep("/^\d+/", $arrCronEntriesFile));
		}
		
		foreach($arrOldEntries as $line => $entry) {
			if(!in_array($entry,$arrNewEntries)) {
				$this->log("$filename:$line has no match in any of the *.cron files!");
			} else {
				unset($arrOldEntries[$line]);
			}
		}
		$arrNewEntries = array_merge($arrNewEntries,$arrOldEntries);

		$arrBeginSection = array(
		'################################## Begin Section ##################################',
		'##',                      
		"##  Server cluster '$roleName'  username  '$username'  application '$this->_cvsModule'",
		'##',         
		'##                  Warning: this section is AUTOGENERATED !!!',
		'##           Any edits within this section WILL BE REMOVED by the next',
		"##               install or rollout of application '$this->_cvsModule'",
		'##',
		'###################################################################################'
		);
		$arrEndSection = array(
		'##################################  End Section  ##################################',
		'##',
		"##     Server cluster '$roleName'  username  '$roleName'  application '$this->_cvsModule'",
		'##',
		'###################################################################################'
		);
		$cronFileContents = array_merge($arrBeginSection, $arrNewEntries, $arrEndSection);
		
		file_put_contents($filename,implode("\n",$cronFileContents));
	}

	/**
	 * Scans directory for *.role files
	 */
	private function _getRoleNames($strDir) 
	{
		$arrScanDir = scandir($strDir);
		
		$arrMatches = array();
		$arrRoleNames = array();
		foreach ($arrScanDir as $strFile) {
			
			if(preg_match('/(\w+).role/', $strFile, $arrMatches)) {
				$arrRoleNames[] = $arrMatches[1];
			}	
		}
		return $arrRoleNames;
	}    

	private function _scanCronEntries($roleName) 
	{
		$arrUserNames = array();
		$arrResult = array();

		$arrScanDir = scandir($this->_availableRolesDir);
	
		//get cron filenames, usernames, etc
		$arrCronFilesg = $this->rglob("$this->_availableRolesDir/$roleName", '*.cron');
		foreach($arrCronFilesg as $filename) {
			preg_match('%\S+/\w+/(\w+)/(\w+)/\w+/\w+.cron%', $filename, $arrMatches);
			
			$arrResult[$arrMatches[1]][$filename] = $arrMatches[2];		
		}
		return $arrResult;
	}

	/**
	 * Recursive version of glob
	 */
	function rglob($strDir, $strPattern)
	{
		$strDir = escapeshellcmd($strDir);
		$aFiles = glob("$strDir/$strPattern");

		foreach (glob("$strDir/*", GLOB_ONLYDIR) as $sSubDir)
		{
			$aSubFiles = $this->rglob($sSubDir, $strPattern);
			$aFiles = array_merge($aFiles, $aSubFiles);
		}
		return $aFiles;
	} 	
}
