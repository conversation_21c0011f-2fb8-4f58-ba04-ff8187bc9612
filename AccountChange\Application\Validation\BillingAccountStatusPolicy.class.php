<?php

use Plusnet\BillingApiClient\Service\ServiceManager as BillingServiceManager;
use Plusnet\BillingApiClient\Entity\Constant\BillingAccountStatus;
use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;

/**
 * <AUTHOR>
 */

class AccountChange_BillingAccountStatusPolicy extends AccountChange_AbstractValidationPolicy
{
    const MC_ERROR_MESSAGE = 'Your account is closing down, so you can\'t make any changes to your services. Give us a call on 0800 432 0200 if you\'d like to know more.';
    const WP_ERROR_MESSAGE = 'Account is terminated or to be terminated in RBM, please DO NOT alter the account.';
    const JL_ERROR_MESSAGE = 'ERROR_INVALID_BILLING_ACCOUNT_STATUS||Your account is closing down, so you can\'t make any changes to your services. Give us a call on <strong>0800 022 3300</strong> if you\'d like to know more.';

    /** @var Core_Service */
    private $service;

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_BILLING_ACCOUNT_CLOSED';

    /**
     * Runs the validation policy
     *
     * @return bool
     * @throws BillingApiClientBillingServiceException
     */
    public function validate()
    {
        $serviceId = $this->actor->getExternalUserId();
        $this->service = $this->getCoreService($serviceId);

        $billingApi = BillingServiceManager::getService("BillingApiFacade");
        $billingAccountStatus = $billingApi->getBillingAccountStatus($serviceId);

        if (in_array(
            $billingAccountStatus,
            array(
                BillingAccountStatus::ACCOUNT_STATUS_TERMINATED,
                BillingAccountStatus::ACCOUNT_STATUS_TERMINATED_AWAITING_BILL
            )
        )) {
            return false;
        }

        return true;
    }

    /**
     * Get the failure reason message
     *
     * @return string
     */
    public function getFailure()
    {
        return ($this->isWorkplace || $this->actor->getUserType() === static::STAFF_USER_TYPE)
            ? self::WP_ERROR_MESSAGE
            : $this->getUserMessage();
    }

    /**
     * @return string
     */
    private function getUserMessage()
    {
        return ($this->service->isJohnLewisUser())
            ? self::JL_ERROR_MESSAGE
            : self::MC_ERROR_MESSAGE;
    }

    /**
     * Wrapper to get Core Service
     *
     * @param int $serviceId Service Id
     *
     * @return Core_Service
     * @throws Core_Exception
     */
    protected function getCoreService($serviceId)
    {
        return new Core_Service($serviceId);
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
