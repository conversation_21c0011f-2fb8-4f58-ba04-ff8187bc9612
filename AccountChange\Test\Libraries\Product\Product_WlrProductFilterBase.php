<?php

/**
 * File Product_WlrProductFilter.test.php
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */

/**
 * Class AccountChange_Product_WlrProductFilterBase
 *
 * Unit tests for AccountChange_Product_WlrProductFilter
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */
abstract class AccountChange_Product_WlrProductFilterBase extends PHPUnit_Framework_TestCase
{
    const PRE_SEPT_2016_DATE = '2016-01-01';
    const POST_SEPT_2016_DATE = '2016-09-04';

    /**
     * Data provider for 'getMappedMobileProduct' test
     *
     * @return array
     */
    public function provideDataForGetMappedMobileProductTest()
    {
        return array(
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME_INTERNATIONAL,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME_INTERNATIONAL_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::PENGUIN_EVENINGS_AND_WEEKENDS,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::PENGUIN_EVENINGS_AND_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::PENGUIN_EVENINGS_AND_WEEKENDS_FREE,
                'intBoltOnServiceComponentID' =>
                AccountChange_Product_WlrProductFilter::PENGUIN_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::PENGUIN_LINE_ONLY,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::PENGUIN_LINE_ONLY_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::PENGUIN_WEEKENDS,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::PENGUIN_WEEKENDS_WITH_MOBILE
            ),
            array(
                'intServiceComponentID' =>  AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME,
                'intBoltOnServiceComponentID' => AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME_WITH_MOBILE
            )
        );
    }

    protected function getFilterMock($dateNow = self::PRE_SEPT_2016_DATE, $filterType = null)
    {
        $filterMock = $this->getMock(
            'AccountChange_Product_WlrProductFilter',
            array('getServiceComponentBoltOnMappings', 'getPhoneHelper', 'timeNow'),
            array($filterType)
        );

        $phoneHelperMock = $this->getMock(
            'AccountChange_PhoneProductHelper',
            array('getServiceComponentBoltOnMappings')
        );
        $phoneHelperMock->expects($this->any())
            ->method('getServiceComponentBoltOnMappings')
            ->will(
                $this->returnValue(
                    $this->provideDataForGetMappedMobileProductTest()
                )
            );

        $filterMock
            ->expects($this->any())
            ->method('getPhoneHelper')
            ->will($this->returnValue($phoneHelperMock));

        $filterMock
            ->expects($this->any())
            ->method('timeNow')
            ->will($this->returnValue(new DateTime($dateNow)));

        return $filterMock;
    }

    protected $June2014LineOnlyResult = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Line Only',
            'isDefaultLrsProduct' => true
        )
    );

    protected $June2014LineOnlyWithMobileResult = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Line Only with Mobile',
            'isDefaultLrsProduct' => true
        )
    );

    protected $June2014LineOnly = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Line Only',
        )
    );

    protected $June2014LineOnlyWithMobile = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_LINE_ONLY_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Line Only with Mobile',
        )
    );

    protected $June2014EveningsAndWeekendsFree = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Evening and Weekends FREE',
        )
    );

    protected $June2014EveningsAndWeekendsFreeMobileSelected = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Evening and Weekends FREE',
            'mobileSelected' => true
        )
    );

    protected $June2014EveningsAndWeekendsFreeWithMobile = array (
        array (
            'intNewWlrId' =>
            AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Evening and Weekends FREE with Mobile',
        )
    );

    protected $June2014Anytime = array(
        array(
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Anytime',
        )
    );

    protected $June2014AnytimeWithMobile = array(
        array(
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Anytime with Mobile',
        )
    );

    protected $June2014Weekends = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Weekends',
        )
    );

    protected $June2014WeekendsMobileSelected = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Weekends',
            'mobileSelected' => true
        )
    );

    protected $June2014WeekendsWithMobile = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_WEEKENDS_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Weekends with Mobile',
        )
    );

    protected $June2014EveningsAndWeekends = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Evening and Weekends',
        )
    );

    protected $June2014EveningsAndWeekendsMobileSelected = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Evening and Weekends',
            'mobileSelected' => true
        )
    );

    protected $June2014EveningsAndWeekendsWithMobile = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Evening and Weekends with Mobile',
        )
    );

    protected $June2014RemainingProductsPostSept16 = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Anytime International 300',
        ),
    );

    protected $June2014RemainingProductsPostSept16MobileVersions = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_ANYTIME_INTERNATIONAL_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Anytime International 300 with Mobile',
        )
    );

    protected $June2014MobileProduct = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::JUNE2014_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'June14 15.95 Mobile',
        ),
    );

    protected $June2016EveningsAndWeekendsWithMobile = array (
        array(
            'intNewWlrId'    => AccountChange_Product_WlrProductFilter::JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
            'strContract'    => 'MONTHLY',
            'strProductName' => 'June16 E+W with mobile'
        ),
    );

    protected $June2016UnlimitedUkWithMobile = array (
        array(
            'intNewWlrId'    => AccountChange_Product_WlrProductFilter::JUNE2016_UNLIMITED_UK_WITH_MOBILE,
            'strContract'    => 'MONTHLY',
            'strProductName' => 'June16 Unlimited with mobile'
        ),
    );

    protected $penguinLrsOnly = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_LINE_ONLY,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Line Only',
            'isDefaultLrsProduct' => true
        ),
    );

    protected $penguinLrsOnlyWithMobile = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_LINE_ONLY_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Line Only with Mobile',
            'isDefaultLrsProduct' => true
        ),
    );

    protected $penguinMlrOnlyProducts = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_WEEKENDS,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Weekends',
        ),
    );

    protected $penguinMlrOnlyProductsMobileVersion = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_WEEKENDS_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Weekends with Mobile',
        ),
    );

    protected $penguinProducts = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_EVENINGS_AND_WEEKENDS,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Evenings and Weekends',
        ),
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Anytime',
        ),
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME_INTERNATIONAL,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Anytime International 300',
        ),
    );

    protected $penguinProductsMobileVersions = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_EVENINGS_AND_WEEKENDS_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Evenings and Weekends with Mobile',
        ),
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Anytime with Mobile',
        ),
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_ANYTIME_INTERNATIONAL_WITH_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Anytime International 300 with Mobile',
        ),
    );

    protected $penguinMobileProduct = array (
        array (
            'intNewWlrId' => AccountChange_Product_WlrProductFilter::PENGUIN_MOBILE,
            'strContract' => 'MONTHLY',
            'strProductName' => 'Penguin 14.50 Mobile',
        ),
    );

    protected $legacyProducts =  array (
        array (
            'intNewWlrId' => '670',
            'strContract' => 'MONTHLY',
            'strProductName' => 'Talk Evenings & Weekends',
        ),
        array (
            'intNewWlrId' => '919',
            'strContract' => 'MONTHLY',
            'strProductName' => 'Talk Anytime',
        ),
        array (
            'intNewWlrId' => '669',
            'strContract' => 'MONTHLY',
            'strProductName' => 'Talk Anytime International 300',
        ),
    );

    protected $legacyMobileProducts =  array (
        array (
            'intNewWlrId' => '921',
            'strContract' => 'MONTHLY',
            'strProductName' => 'Talk Mobile 1',
        )
    );

    protected $noHomePhoneProduct = array(
        array(
            'intNewWlrId' => 0,
            'strContract' => 'MONTHLY',
            'strProductName' => 'No Home Phone',
        )
    );

    protected function getAllProducts($includeMobile = false, $includeNoHomePhone = false)
    {
        $combined = array_merge(
            $this->legacyProducts,
            $this->penguinLrsOnly,
            $this->penguinLrsOnlyWithMobile,
            $this->penguinMlrOnlyProducts,
            $this->penguinMlrOnlyProductsMobileVersion,
            $this->penguinProducts,
            $this->penguinProductsMobileVersions,
            $this->penguinMobileProduct,
            $this->June2014LineOnly,
            $this->June2014LineOnlyWithMobile,
            $this->June2014Weekends,
            $this->June2014WeekendsWithMobile,
            $this->June2014EveningsAndWeekendsFree,
            $this->June2014EveningsAndWeekendsFreeWithMobile,
            $this->June2014EveningsAndWeekends,
            $this->June2014EveningsAndWeekendsWithMobile,
            $this->June2014Anytime,
            $this->June2014AnytimeWithMobile,
            $this->June2014RemainingProductsPostSept16,
            $this->June2014RemainingProductsPostSept16MobileVersions,
            $this->June2014MobileProduct,
            $this->June2016EveningsAndWeekendsWithMobile,
            $this->June2016UnlimitedUkWithMobile
        );
        if ($includeMobile) {
            $combined = array_merge($combined, $this->legacyMobileProducts);
        }
        if ($includeNoHomePhone) {
            $combined = array_merge($combined, $this->noHomePhoneProduct);
        }
        return $combined;
    }

    /**
     * Runs the test for filtering the product
     *
     * @param array  $expectedResult      The expected result
     * @param array  $availableProducts   Available products
     * @param int    $currentWlrProductId ID of current product
     * @param string $filterType          Type of filter
     * @param bool   $throwsException     Exception flag
     * @param bool   $hasLrs              Flag to indicate whether customer has active LRS
     * @param string $dateNow             System date the tests are being run for
     * @param bool   $isFibreProduct      Flag to indicate new product is fibre (passed into filter)
     * @param bool   $devDebug            Set to true during dev to dump expected and actual results to tmp file
     *
     * @return void
     */
    protected function filterProducts (
        $expectedResult,
        $availableProducts,
        $currentWlrProductId,
        $filterType,
        $throwsException,
        $hasLrs,
        $dateNow = self::PRE_SEPT_2016_DATE,
        $isFibreProduct = false,
        $devDebug = false
    ) {
        if ($throwsException) {
            $this->setExpectedException('InvalidArgumentException');
        }
        $filter = $this->getFilterMock($dateNow, $filterType);
        $filteredResult = $filter->filterProducts($availableProducts, $currentWlrProductId, $hasLrs, $isFibreProduct);

        if ($devDebug) {
            $this->dumpIdsToTempFile($expectedResult, '/tmp/expected.txt');
            $this->dumpIdsToTempFile($filteredResult, '/tmp/actual.txt');
        }

        if ($devDebug) {
            fwrite(STDERR, print_r($expectedResult, true));
            fwrite(STDERR, print_r($filteredResult, true));
        }

        $this->assertEquals($expectedResult, $filteredResult);
    }

    /**
     * Utility function to make it easier to compare actual and expected results from
     * filterProducts when writing tests.
     *
     * @param array  $products       Wlr products
     * @param string $outputFilename File to write to
     *
     * @return void
     **/
    public function dumpIdsToTempFile($products, $outputFilename) {

        $out = '';
        $newProd = array();

        foreach ($products as $product) {
            $id = str_pad($product['intNewWlrId'], 4, '0', STR_PAD_LEFT);
            $newProd[$id] = $product['strProductName'];
        }

        ksort($newProd);

        foreach ($newProd as $id => $name) {
            $out .= "[{$id}]  {$name}\n";
        }
        file_put_contents($outputFilename, $out);
    }

    const LEGACY_MOBILE_INCLUDED = true;
    const LEGACY_MOBILE_EXCLUDED = false;
    const NO_HOME_PHONE_INCLUDED = true;
    const NO_HOME_PHONE_EXCLUDED = false;
    const DO_NOT_THROW_EXCEPTION = false;
    const THROW_EXCEPTION = true;
    const LRS_ENABLED = true;
    const LRS_DISABLED = false;
    const FIBRE_PRODUCT = true;
    const ADSL_PRODUCT = false;
}
