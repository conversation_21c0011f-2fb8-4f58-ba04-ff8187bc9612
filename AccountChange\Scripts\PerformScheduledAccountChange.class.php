<?php
/**
 * <PERSON><PERSON><PERSON> to carry out scheduled account changes based on entries in userdata.service_change_schedule
 * Intended to run from cron, but can also be ran manually from the command line
 *
 * Usage:
 * /local/codebase2005/modules/Framework/Scripts/RunScript.php
 *      -c AccountChange_PerformScheduledAccountChange -p PLUSNET [-s sid [-s sid]] [--runcbc] [-q|--quiet] [--debug]
 *
 * Parameter information:
 *  -s [service_id]: the service id for a specific account which has a pending account change for today.  If the
 *  account change is not scheduled for today, no action will be taken.  Multiple -s params can be specified - e.g.
 *      AccountChange_PerformScheduledAccountChange -p PLUSNET -s 1234 -s 2345 -s 3456
 *
 *  --runcbc: if set, GenerateCbcServiceBills will be ran prior to each account being processed
 *
 *  -q or --quiet: if set, AccountChange will not make any forum posts
 *
 *  --debug: if set, AccountChange will write out debug information prior to beginning processing
 *
 * NOTE: if this script is called without any parameters, it will perform all account changes scheduled for
 * today and will NOT run CBC
 * therefore only be enabled when the main CBC run for the day has not yet been actioned.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2009-03-09
 *
 */

use Plusnet\Feature\FeatureToggleManager;
use Plusnet\ContractsClient\Client as ContractsClient;

/**
 * AccountChange_Controller Script to perform scheduled account change
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 *
 */
class AccountChange_PerformScheduledAccountChange extends AccountChange_PerformChangeBase implements Mvc_Script
{
    /**
     * Output buffer
     * @var array
     */
    private $output = array();

    /**
     * Failures array (for output on failure)
     * @var array
     */
    private $arrFailures = array();

    /**
     * Array of service ids we want to perform account change
     * @var array
     */
    private $arrRestrictTo = array();

    /**
     * How many account changes failures have to occure before we halt the script
     * @var int
     */
    const FAILURES_THRESHOLD = 10;

    /**
     * Message count for the topic
     *
     */
    const MESSAGE_COUNT = 1;

    /**
     * This will be used in main billing.
     * this is a timestamp file only and should NOT be deleted.
     */
    const TIMESTAMP_FILE = '/share/admin/locks/PerformScheduledAccountChangeTimestamp';

    /**
     * Period in seconds to wait for until checking if the script can start
     *
     * @var integer
     */
    const WAIT_PERIOD_SECONDS = 30;

    /**
     * Value of mysql lock wait timeout error code
     *
     * @var integer
     */
    const MYSQL_LOCK_WAIT_TIMEOUT_CODE = "1205";

    /**
     * @var string
     */
    const AGREEMENT_DATE = 'agreementDate';

    /**
     * @var string
     */
    const CONTRACT_TYPE = 'contractType';

    /**
     * @var string
     */
    const CONTRACT_SUB_TYPE = 'contractSubType';

    /**
     *
     * @var
     */
    protected static $arrScheduledForTodayCache;

    /**
     * Should the procedure be "quiet"?
     * This controls whether the script makes a forum posting.
     * By default the script is not quiet, hence makes a forum posting.
     * It can be made quiet (i.e. *no* forum posting) via the --quiet command-line switch.
     *
     * @boolean
     */
    private $doForumPosting = true;

    /**
     * Should debug information be written out?
     *
     * @boolean
     */
    protected $doDebugOutput = false;

    /**
     * constructor
     *
     * Do not pass any args here
     *
     */
    public function __construct()
    {
    }

    /**
     * Factory method to get an instance of the current object
     *
     * @return unknown_type
     */
    public static function instance()
    {
        return new self();
    }

    /**
     * Getter for arrRestrictTo
     *
     * @return array
     */
    public function getRestrictTo()
    {
        return $this->arrRestrictTo;
    }

    /**
     * Get Short Options
     *
     * @return string
     */
    public static function getShortOpts()
    {
        return 'qs:';
    }

    /**
     * Get Long Options
     *
     * @return array
     */
    public static function getLongOpts()
    {
        return array('runcbc', 'quiet', 'debug');
    }

    /**
     * Execute and return the return code
     *
     * @param string $command Command to execute
     *
     * @return int The return code
     */
    protected function executeAndReturnCode($command)
    {
        $output = array();
        $exitCode = 0;

        exec($command, $output, $exitCode);

        return $exitCode;
    }

    /**
     * Wrapper function to Financial_BillingRunExclusion::insertAccountToExclude
     *
     * @param int $serviceId Service Id of the account
     *
     * @return void
     */
    protected function addToBillingExclusion($serviceId)
    {
        //Add the account to the billing run exclusion bucket and log the message
        $this->output(
            "The account $serviceId is being added to the billing run exclusion list",
            true
        );

        Financial_BillingRunExclusion::insertAccountToExclude($serviceId, __CLASS__);
    }

    /**
     * Wrapper function for Financial_BillingRunExclusion::shouldExcludeFromBillingRun
     *
     * @param int $serviceId Service Id of the account
     *
     * @return void
     */
    protected function isExcludedFromBilling($serviceId)
    {
        return Financial_BillingRunExclusion::shouldExcludeFromBillingRun($serviceId);
    }

    /**
     * Runs the script
     *
     * @param array $args Parameters passed to the script
     * @param array $opts Flagged Arguments
     *
     * @return void
     */
    public function run(array $args, array $opts)
    {
        AccountChange_AuditLogger::functionEntry(__METHOD__);

        ob_implicit_flush(1);
        $this->flushOutputBuffer();
        set_time_limit(0);

        $this->processCommandLineArgs($opts);

        if ($this->doDebugOutput) {
            try {
                // We write to the error log to give us a record that logging has been attempted
                $this->errorLog(get_class($this) . ": attempting to generate debug output.");
                $debugFile = $this->generateDebugOutput();
                $this->errorLog(get_class($this) . ": debug written to $debugFile.");
            } catch (Exception $e) {
                $this->errorLog(
                    get_class($this) . ": failed to generate debug output. " .
                    "The error message was: " . $e->getMessage()
                );
            }
        }

        $this->output('Command line params processed.', true);
        if (empty($this->arrRestrictTo)) {
            $this->output('No restrictions applied.', true);
        }

        if ($this->doCbcProcessing == true) {
            $this->output('CBC will be ran for each account prior to the change being made');
        } else {
            $this->output('CBC will NOT be ran for each account prior to the change being made');
        }

        $this->output("Getting AccountChange records for today...", true);
        $data = self::getScheduledAccountChanges($this->arrRestrictTo);
        $recordCount = count($data);
        $intRecordsProcessed = 0;

        $this->output("There are $recordCount record(s) to process.", true);

        $intErrorLevel = error_reporting(E_ERROR);
        $this->output("Including legacy files ...", true);
        $this->includeLegacyFiles();

        if ($this->doForumPosting) {
            $intTopicId
                = $this->accountChangeStartForumPosting(
                    I18n_Date::now()->toI18nStrThere('SHORT_DB_DATE_FORMAT'),
                    $recordCount
                );
        }

        foreach ($data as $accountChange) {
            $intServiceId = $accountChange['intServiceId'];
            $changeId = $accountChange['intServiceChangeScheduleId'];
            $promoCode = $accountChange['promoCode'];
            $isPendingBroadbandOrderCompletion = $accountChange['requiresBroadbandOrder'];

            //Check if the account is one of the billing run excluded one
            if ($this->isExcludedFromBilling($intServiceId)) {
                //Log the message that the account is being skipped
                $this->output(
                    "Skipping account $intServiceId since the account present in billing run exclusion list",
                    true
                );
                //Continue to the next account
                continue;
            }

            //Check if the account has open mop order
            if ($this->hasOpenMopOrder($intServiceId)) {
                //Log the message that the account is being skipped
                $this->output(
                    "Skipping account $intServiceId since the account has open mop orders",
                    true
                );
                //Continue to the next account
                continue;
            }


            //Skip account that has house move in progress
            if ($this->isScheduledHouseMove($intServiceId)) {
                //Log the message that the account is being skipped
                $this->output(
                    "Skipping account $intServiceId since the account has house move in progress",
                    true
                );
                //Continue to the next account
                continue;
            }

            $intRecordsProcessed++;

            $this->output("Processing $intServiceId", true);

            // Checking promocode
            if (empty($promoCode)) {
                // getting C2M promotion details
                $c2mPromoCode = $this->getC2MPromoCode($changeId);
                if (!empty($c2mPromoCode)) {
                    $promoCode = $c2mPromoCode['promotionCode'];
                }
            }

            if ($this->isReContractingOnSameProduct($accountChange, null) && empty($promoCode)) {
                $this->output("The request is for Recontract only on same product", true);

                /** @var \Plusnet\InventoryEventClient\Service\EventService $inventoryEventService */
                $inventoryEventService = BusTier_BusTier::getClient('inventoryEventService');
                $context = new \Plusnet\InventoryEventClient\Context\RecontractContext();
                $inventoryEventService->takePreChangeSnapshot($intServiceId, $context);
                $this->processPendingRetentionOffers($intServiceId, $accountChange, true);
                $inventoryEventService->takePostChangeSnapshot($intServiceId);
                $this->completeServiceChangeSchedule($accountChange['intServiceChangeScheduleId']);

                $this->output("End of processing.", true);
                continue;
            }

            $oldSdi = !empty($accountChange['intOldSdi']) ? $accountChange['intOldSdi'] : null;
            $newSdi = !empty($accountChange['intNewSdi']) ? $accountChange['intNewSdi'] : null;

            if ($this->shouldWaitForOrderCompletion($isPendingBroadbandOrderCompletion, $changeId, $newSdi)) {
                if (!$this->isFttpProduct($newSdi) && !$this->isSogeaProduct($newSdi)) {
                    $this->getRadiusConnection()->activate($intServiceId, true);
                    $this->output('Updated connection profile for FTTC account change. Continuing.');
                }
                continue;
            }

            /** @var \Plusnet\InventoryEventClient\Service\EventService $inventoryEventService */
            $inventoryEventService = BusTier_BusTier::getClient('inventoryEventService');
            $context = new \Plusnet\InventoryEventClient\Context\ScheduledAccountChangeContext();
            $inventoryEventService->takePreChangeSnapshot($intServiceId, $context);

            // to let Scheduled Account change context knows that old BB has been ceased
            // used to identify Legacy BB is destroyed or not
            if (!empty($newSdi) && ($oldSdi != $newSdi)) {
                $context->setIsBBCRCease(true);
            }

            if ($this->doCbcProcessing == true) {
                $this->output('Running Generate Cbc Service Bills.', true);
                $bolSuccess = $this->generateCbcServiceBill($intServiceId);

                if ($bolSuccess) {
                    $this->output('GenerateCbcServiceBills completed.', true);
                } else {
                    $this->output('GenerateCbcServiceBills failed.', true);
                }
            }

            $this->output(
                "Customer changing from {$accountChange['strOldProductName']} to {$accountChange['strNewProductName']}",
                true
            );

            if (!$this->isAccountValidForProcessing($accountChange)) {
                $inventoryEventService->discardComponentChange();
                $this->output("Account change can not be performed.", true);
                continue;
            }

            $this->objService = $this->getCoreServiceForServiceId($intServiceId);
            $objServiceDefinition = $this->getCoreServiceDefinition($this->objService->getType());

            //we need to get discount length before performing the change
            $intDiscountLength = $this->getDiscountLength();

            AccountChange_Registry::instance()->reset();

            $manager = $this->restoreAccountManager($accountChange);

            $this->setOldSdi($oldSdi);
            $this->setToSdi($newSdi);
            $this->setAccessTechnologyValues();

            $this->output("Account change manager restored, calling changeAccount.", true);

            //Indicates whether the action is triggered from Account Change script.
            //Overriding default behaviour of parent class.
            AccountChange_Registry::instance()->setEntry('runFromPerformScheduledAccountChange', true);
            if (array_key_exists(self::AGREEMENT_DATE, $accountChange)) {
                AccountChange_Registry::instance()->setEntry(self::AGREEMENT_DATE, $accountChange[self::AGREEMENT_DATE]);
            }
            if (array_key_exists(self::CONTRACT_TYPE, $accountChange)) {
                $contractTypeDto = $this->determineContractType($accountChange[self::AGREEMENT_DATE], $intServiceId);

                AccountChange_Registry::instance()->setEntry(
                    self::CONTRACT_TYPE,
                    $contractTypeDto->getContractType()
                );

                AccountChange_Registry::instance()->setEntry(
                    self::CONTRACT_SUB_TYPE,
                    $contractTypeDto->getContractSubType()
                );
            }

            try {
                AccountChange_Registry::instance()->setEntry('completeServiceChangeSchedule', true);
                $manager->setPartnerUser($this->objService->isPartnerUser());
                $manager->changeAccount();
                $this->makeMGALSLive($intServiceId);
                $this->markServiceChangeScheduleComplete($manager);
                // Setting boolean false Internet supplier order and not placing order to BT
                // if account is Business and product changes from Unlimited Fibre to Limited Fibre or Vice-versa
                if ($objServiceDefinition->isBusiness() && ($this->isFibreProduct($oldSdi) && $this->isFibreProduct($newSdi))) {
                    $context->setIsInternetSupplierOrder(false);
                }
            } catch (Exception $e) {
                $errMsg = get_class($this) . ": account change failed for service id $intServiceId.  " .
                    "The message returned was: " . $e->getMessage() . "\n" .
                    "The stack trace is:\n" . $e->getTraceAsString() . "\n";

                $this->errorLog($errMsg);

                Dbg_Dbg::write($errMsg, 'AccountChange');

                $this->output(
                    "AccountChange for service id $intServiceId failed.  The message returned was: " . $e->getMessage(),
                    true
                );

                // Check if the Exception thrown is due to a Lock wait timeout, then sleep for few seconds
                if (strrpos($e->getMessage(), self::MYSQL_LOCK_WAIT_TIMEOUT_CODE) !== false) {
                    $this->output("DB lock error detected: pausing for " . self::WAIT_PERIOD_SECONDS . " seconds", true);
                    sleep(self::WAIT_PERIOD_SECONDS);
                }

                $this->addToBillingExclusion($intServiceId);

                //Add this failure to _arrFailures
                $this->arrFailures[] = array(
                    'serviceId' => $intServiceId,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'message'   => $e->getMessage()
                );
                $intFailuresCount = $this->getFailuresCounter();

                $this->output(
                    "FailuresCount = $intFailuresCount",
                    true
                );

                if ($intFailuresCount >= self::FAILURES_THRESHOLD) {
                    $this->output(
                        'Error threshold of ' . self::FAILURES_THRESHOLD . ' failures reached. Halting script.',
                        true
                    );

                    //Forum post - Failure (which now includes fail info)
                    if ($this->doForumPosting) {
                        $this->accountChangeEndForumPosting(
                            I18n_Date::now()->toI18nStrThere('SHORT_DB_DATE_FORMAT'),
                            count($data),
                            $intTopicId,
                            $intRecordsProcessed
                        );
                        Db_Manager::commit();
                    }

                    AccountChange_AuditLogger::functionExit(__METHOD__);
                    exit(1);
                }

                continue;
            }

            /**
             * Attempt to apply the pending retention offers, upon failure it skips the
             * account change record.
             */
            if (!$this->processPendingRetentionOffers($intServiceId, $accountChange)) {
                $this->output("Failed to apply the pending retention offer for ${intServiceId}");
                $this->addToBillingExclusion($intServiceId);
                $this->output(
                    "Skipping the account $intServiceId ",
                    true
                );
                continue;
            }

            $this->recreateEngineerChargingScheduledPayments($intServiceId);
            $inventoryEventService->takePostChangeSnapshot($intServiceId);

            $this->output("Account $intServiceId changed successfully.", true);
            $this->output("Sending email ...", true);

            try {
                // As the service has now changed, we need to fetch the details again so the confirmation
                // email contains the correct information.
                $this->objService = $this->getCoreServiceForServiceId($intServiceId);

                //Fix for problem https://workplace.plus.net/programme_tool/problem.html?problem_id=59074
                $arrEmailExcludedProductNames = $this->getBlockedAccountChangeEmailProductNames();
                if (!in_array($accountChange['strNewProductName'], $arrEmailExcludedProductNames)) {
                    $arrEmailData = $this->restoreEmailData($accountChange['strNewProductName'], $intDiscountLength);
                    $manager->sendConfirmationEmails($arrEmailData);
                    $this->output("Email sent.", true);
                }
            } catch (Exception $e) {
                $this->errorLog(
                    get_class($this) . " has failed to send an email following the completion of an account change " .
                    "for service id $intServiceId.  The error message was: " . $e->getMessage()
                );

                $this->output("Failed to send an email", true);

                $this->addToBillingExclusion($intServiceId);
                continue;
            }

            $this->output("End of $intServiceId account processing.", true);

            $memUsage = memory_get_usage(true);
            $this->output('Current memory usage: ' . round($memUsage / 1048576, 3) . " megabytes");
        }

        //Forum post - Success (which now includes fail info)
        if ($this->doForumPosting) {
            $this->accountChangeEndForumPosting(
                I18n_Date::now()->toI18nStrThere('SHORT_DB_DATE_FORMAT'),
                count($data),
                $intTopicId
            );
        }

        Db_Manager::commit();

        $this->output('Updating the timestamp file: ' . self::TIMESTAMP_FILE, true);
        $bolUpdated = $this->maintainTimestampFile(self::TIMESTAMP_FILE);
        if (!$bolUpdated) {
            $this->output('Failed to maintain the timestamp file', true);
        }

        $this->output("End of processing.", true);
        AccountChange_AuditLogger::functionExit(__METHOD__);
    }

    /**
     * Wrapper around error_log for unit test mocking
     *
     * @param string $errMsg Error message text
     *
     * @return void
     */
    protected function errorLog($errMsg)
    {
        error_log($errMsg);
    }

    /**
     * recreate engineer charging scheduled payments
     *
     * @param int $serviceId service id
     *
     * @return void
     */
    public function recreateEngineerChargingScheduledPayments($serviceId)
    {
        EngineerCharging_EngineerChargingServices::recreateEngineerChargingScheduledPaymentsCancelledByAccountChange($serviceId);
    }

    /**
     * returns true if service id has open mop orders
     *
     * @param int $serviceId service id
     *
     * @return int
     */
    public function hasOpenMopOrder($serviceId)
    {
        $this->requireLegacy();

        return F2capiOrders::lastOpenOrderId($serviceId) ? true : false;
    }


    /**
     * Require the legacy files needed
     *
     * @return void
     */
    private function requireLegacy()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once SQL_PRIMITIVES_LIBRARY;
        require_once USERDATA_ACCESS_LIBRARY;
        require_once '/local/data/mis/database/class_libraries/F2capiOrders.php';
    }

    /**
     * Process command line params
     *
     * @param array $arrOpts Options from the cmd line
     *
     * @return void
     */
    public function processCommandLineArgs(array $arrOpts)
    {
        if (empty($arrOpts)) {
            return;
        }

        if (isset($arrOpts['s'])) {
            $mixSids = $arrOpts['s'];
            if (is_array($mixSids)) {
                foreach ($mixSids as $intSid) {
                    $this->checkAndAddSid($intSid);
                }
            } else {
                $this->checkAndAddSid($arrOpts['s']);
            }
        }

        if (isset($arrOpts['quiet']) || isset($arrOpts['q'])) {
            // Disable forum posting (i.e. make the script quiet)
            $this->doForumPosting = false;
        }

        if (isset($arrOpts['runcbc'])) {
            // Run CBC prior to processing each account
            $this->doCbcProcessing = true;
        }

        if (isset($arrOpts['debug'])) {
            // Dump out debug information prior to processing
            $this->doDebugOutput = true;
        }
    }

    /**
     * Checks if sid is an integer
     *
     * @param integer $intSid Sid
     *
     * @return boolean
     */
    protected function checkAndAddSid($intSid)
    {
        if (!is_numeric($intSid)) {
            $this->output("Can not restrict to $intSid - not a number.");

            return false;
        } else {
            $this->output("Added sid restriction $intSid.");
            //adding as a key will not allow duplicates
            $this->arrRestrictTo[$intSid] = $intSid;

            return true;
        }
    }

    /**
     * isScheduledToBeChangedToday
     *
     * Checks if account is going to be scheduled today
     * This function is used in account changes report -
     * /DatabaseAdmin/admin_document_root/reports/account_changes/account_changes.html
     * It is faster to run one query to collect all data required rather then executing one query per customer
     * Estimated size of $arrScheduledForTodayCache is ~100 - so processing overhead is very low
     *
     * @param integer $intServiceId service id
     * @param boolean $bolFlush     flush account change cache
     *
     * @return boolean - true if account is going to be picked by an automated process today
     */
    public static function isScheduledToBeChangedToday($intServiceId, $bolFlush = false)
    {
        if (null === self::$arrScheduledForTodayCache || $bolFlush) {
            self::$arrScheduledForTodayCache = self::getScheduledAccountChanges();
        }

        foreach (self::$arrScheduledForTodayCache as $arrAccount) {
            if ($arrAccount['intServiceId'] == $intServiceId) {
                return true;
            }
        }

        return false;
    }

    /**
     * Getting C2M Promotion Code Details
     *
     * @param integer $intServiceChangeScheduleId Service Schedule Id
     *
     * @return array
     */
    protected function getC2MPromoCode($intServiceChangeScheduleId)
    {
        $result = Db_Manager::getAdaptor('AccountChange')->getC2MPromotionCode($intServiceChangeScheduleId);
        $this->output("C2M Promotion Code result: " . print_r($result, true));

        return $result;
    }

    /**
     * Marks scheduled account change as complete
     *
     * @param integer $intServiceChangeScheduleId Service cange schedule id
     *
     * @return boolean
     */
    protected function completeServiceChangeSchedule($intServiceChangeScheduleId)
    {
        $result = Db_Manager::getAdaptor('AccountChange')->completeServiceChangeSchedule($intServiceChangeScheduleId);
        $this->output("completeServiceChangeSchedule result: " . print_r($result, true));

        return $result;
    }

    /**
     * Attempt to apply the pending retention offers
     *
     * @param int   $intServiceId  service id
     * @param array $accountChange account change
     * @param bool  $isRecontract  is recontract
     *
     * @return boolean indicates the success of applying the discounts
     */
    private function processPendingRetentionOffers($intServiceId, $accountChange, $isRecontract = false)
    {
        try {
            $this->output('Checking for pending retention offers');

            $discountsApplied = $this->applyPendingRetentionDiscounts(
                (int)$intServiceId,
                $accountChange['intServiceChangeScheduleId'],
                $isRecontract
            );

            if ($discountsApplied) {
                $this->output("Finished applying retention discounts for service id: $intServiceId");
            } else {
                $this->output("No pending retention discounts found for service id: $intServiceId");
            }
        } catch (Exception $exception) {
            $this->errorLog(
                get_class($this) . " has failed while attempting to check for and apply pending retention offers " .
                "following the completion of an account change for service id $intServiceId.  " .
                "The error message was: " . $exception->getMessage()
            );

            $this->output('Failed to check/apply pending retention offers', true);

            return false;
        }

        return true;
    }

    /**
     * Function to check if parallel run is allowed
     *
     * @return boolean
     */
    public function bolAllowParallelRuns()
    {
        return false;
    }

    /**
     * Returns accounts scheduled to be changed today
     *
     * @param array $arrRestrictTo Restrict to data
     *
     * @return Array of:
     * scs.schedule_id AS intServiceChangeScheduleId
     * sd_new.name AS strNewProductName,
     * scs.new_type AS intNewSdi,
     * sd_old.name AS strOldProductName,
     * scs.old_type AS intOldSdi,
     * s.service_id AS intServiceId,
     * s.type AS intCurrentSdi,
     * scs.change_complete_date AS dteChangeComplete,
     * scs.change_date as dteChangeDate
     * cl.vchHandle as strContractLengthHandle
     * pc.vchPromotionCode as promoCode
     */
    public static function getScheduledAccountChanges($arrRestrictTo = array())
    {
        $db = Db_Manager::getAdaptor('AccountChange');

        if (empty($arrRestrictTo)) {
            return $db->getAccountsScheduledToChangeTodayUnrestricted();
        }

        $accounts = array_merge(
            $db->getAccountsScheduledToChangeByServiceId($arrRestrictTo),
            $db->getAccountsScheduledToChangeByAppointmentAndServiceId($arrRestrictTo)
        );

        // Remove any duplicates from the array
        $accountsSerialized = array_map("serialize", $accounts);
        $accounts = array_map("unserialize", array_unique($accountsSerialized));

        return $accounts;
    }


    /**
     * Function to post CSC forum when the script starts
     *
     * @param String  $strDate   date the script runs
     * @param Integer $intNumber number of accounts the script run on
     *
     * @return array $arrForumDetails  Topic id of the forum post and forumn message Id
     *
     * @access protected
     */
    protected function accountChangeStartForumPosting($strDate, $intNumber)
    {
        $strUserId = $this->getForumPosterId();
        $strHostname = gethostname();
        $strSubject = "Account Change Script Run";
        $strMessage
            = "Account change has started on host $strHostname.\n\n" .
            "Date: $strDate\n\n" . $intNumber . (1 == $intNumber ? ' record' : ' records') . ' to process';

        $intTopicId = $this->createForumTopicAndPostMessage($strSubject, $strMessage, $strUserId);

        return $intTopicId;
    }

    /**
     * Function to do the forum post when the account change script ends
     *
     * @param String  $strDate                Date the script runs
     * @param Integer $intNumber              Number of accounts the script runs on
     * @param Integer $intTopicId             Topic id of the forum post
     * @param Integer $intProcessedBeforeHalt Number of records actually processed when failure threshold is met
     *
     * @return integer Forum message id
     * @access protected
     */
    protected function accountChangeEndForumPosting($strDate, $intNumber, $intTopicId, $intProcessedBeforeHalt = null)
    {
        $strUserId = $this->getForumPosterId();
        $intFailureCount = $this->getFailuresCounter();
        $intSuccessfulAccounts = $intNumber - $intFailureCount;

        //If intProcessed passed, script has failed (met threshold)
        if ($intProcessedBeforeHalt) {
            $intSuccessfulAccountsBeforeHalt = $intProcessedBeforeHalt - $intFailureCount;
        }

        //Create subject / message for forum
        if ($intFailureCount >= self::FAILURES_THRESHOLD) {
            //Add script failed message
            $strSubject = "Account Change processing run failed to complete";
            $strMessage = 'The Account Change process has finished but failed to complete' . PHP_EOL .
                PHP_EOL . 'FAILURE THRESHOLD (' . self::FAILURES_THRESHOLD . ') WAS MET' . PHP_EOL .
                '** SCRIPT DID NOT COMPLETE **' . PHP_EOL . PHP_EOL .
                'Date: ' . $strDate . PHP_EOL .
                'Total number of accounts to process: ' . $intNumber . PHP_EOL .
                'Accounts successfully processed before failure: '
                . $intSuccessfulAccountsBeforeHalt . PHP_EOL .
                'Accounts which were not successfully processed: ' . $intFailureCount . PHP_EOL . PHP_EOL;
        } else {
            //Add script success message
            $strSubject = "Account Change processing run complete";
            $strMessage = 'The Account Change process has finished' . PHP_EOL .
                'Date: ' . $strDate . PHP_EOL .
                'Total number of accounts processed: ' . $intNumber . PHP_EOL .
                'Accounts successfully processed: ' . $intSuccessfulAccounts . PHP_EOL .
                'Accounts which were not successfully processed: ' . $intFailureCount . PHP_EOL . PHP_EOL;
        }

        //Append any failure details to message (to facilitate cleanup outside of LCST)
        if ($intFailureCount) {
            $strMessage .= 'List of failures: ' . PHP_EOL . PHP_EOL;
            foreach ($this->arrFailures as $arrFailure) {
                $strMessage .= $arrFailure['serviceId'] . ': processing failed at ' .
                    $arrFailure['timestamp'] . '. Message returned: ' . $arrFailure['message'] . PHP_EOL . PHP_EOL;
            }
        }

        $intForumMessageId = $this->createForumMessage($strSubject, $strMessage, $intTopicId, $strUserId);

        return $intForumMessageId;
    }

    /**
     * Function used to create the topic for CSC forum post
     *
     * @param String $strSubject Subject of message
     * @param String $strMessage Message content
     * @param String $strUserId  User id from which the topic is created
     *
     * @return Integer Last insert id
     *
     * @access public
     */
    public function createForumTopicAndPostMessage($strSubject, $strMessage, $strUserId)
    {
        $objForumsClient = BusTier_BusTier::getClient('forums');
        $objMessage = $objForumsClient->getNewMessage();
        $objUser = $this->getActorByExternalUserId($strUserId);
        $objMessage->setUser($objUser);
        $objMessage->setSubject($strSubject);
        $objMessage->setBody($strMessage);
        $strForumTag = 'CSC1_UK';
        $objTopic = $objForumsClient->postMessage($strForumTag, $objMessage);

        return $objTopic->getTopicId();
    }

    /**
     * Function used to create the message on a topic in the forum
     *
     * @param String  $strSubject Subject of the message
     * @param String  $strMessage Message on the topic
     * @param Integer $intTopicId Topic id
     * @param String  $strUserId  User id from which message is posted
     *
     * @return boolean Successful execution
     * @access public
     */
    public function createForumMessage($strSubject, $strMessage, $intTopicId, $strUserId)
    {
        $objForumsClient = BusTier_BusTier::getClient('forums');
        $objMessage = $objForumsClient->getNewMessage();
        $objUser = $this->getActorByExternalUserId($strUserId);
        $objMessage->setUser($objUser);
        $objMessage->setSubject($strSubject);
        $objMessage->setBody($strMessage);
        $objTopic = $objForumsClient->getTopic($intTopicId);
        $objForumsClient->postReply($objTopic, $objMessage);

        return true;
    }

    /**
     * The user ID to post messages into internal forums
     *
     * @return String
     *
     * @access protected
     */
    protected function getForumPosterId()
    {
        // default
        $strUserId = '3ab53331cedca4a1a791c46fde8a1bbc';

        // check for existing definition
        if (defined('SCRIPT_USER') && preg_match('/^[0-9a-fA-F]{32}$/', SCRIPT_USER)) {
            $strUserId = SCRIPT_USER;
        }

        return $strUserId;
    }

    /**
     * get failuresCounter
     *
     * @return Integer
     */
    protected function getFailuresCounter()
    {
        return count($this->arrFailures);
    }


    /**
     * Creates the lock file, if the file exists
     * it will update the file modification time
     *
     * @param string $strFile path to file.
     *
     * @return boolean
     */
    protected function maintainTimestampFile($strFile)
    {
        return touch($strFile);
    }


    /**
     * Function to output a formatted message to standard output
     * If second parameter is false, the message will be stored in internal buffer
     * and flushed when called with second param set to true
     *
     * @param string  $message         a messager to output
     * @param boolean $immediateOutput output message or store it in a buffer
     *
     * @return void
     */
    protected function output($message, $immediateOutput = false)
    {
        //no need to test it  - not business logic
        $this->output[] = date(DATE_COOKIE) . ': ' . $message;
        //$this->output[] = ': '.$message;
        if ($immediateOutput) {
            foreach ($this->output as $line) {
                $line = $line . "\n";
                print $line;

                // To keep things simple, we treat "debug" output as fire-and-forget
                if ($this->doDebugOutput) {
                    $this->writeToDebugFile($line);
                }
            }
            $this->output = array();
        }
    }

    /**
     * Flush output buffer
     *
     * @return void
     */
    protected function flushOutputBuffer()
    {
        ob_end_flush();
    }

    /**
     * Checks if we're waiting for order completion
     *
     * @param bool $isPendingBroadbandOrder whether a broadband order has been placed
     * @param int  $changeId                change ID
     * @param int  $newServiceDefinition    the new service definition
     *
     * @return bool
     */
    private function shouldWaitForOrderCompletion($isPendingBroadbandOrder, $changeId, $newServiceDefinition)
    {
        return $this->isAdslToFttcAccountChangeWithAppointment($changeId) ||
            $isPendingBroadbandOrder &&
            ($this->isFibreProduct($newServiceDefinition)
                || $this->isFttpProduct($newServiceDefinition)
                || $this->isSogeaProduct($newServiceDefinition));
    }

    /**
     * Checks if there is an ADSL to FTTC account change with an appointment
     *
     * @param int $changeId change ID
     *
     * @return bool
     */
    private function isAdslToFttcAccountChangeWithAppointment($changeId)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');

        return $dbAdaptor->isAdslToFttcAccountChangeByScheduleId($changeId);
    }

    /**
     * Gets the radius connection class
     *
     * @return Adsl2Orders_RadiusConnection
     */
    protected function getRadiusConnection()
    {
        return new Adsl2Orders_RadiusConnection();
    }

    /**
     * returns true if supplied service definition id is a fibre product.
     *
     * @param integer $intSdi service definition id
     *
     * @return boolean
     */
    public function isFibreProduct($intSdi = null)
    {
        $helper = new AccountChange_FibreHelper();

        return $helper->isFibreProduct($intSdi);
    }

    /**
     * returns true if supplied service definition id is an fttp product.
     *
     * @param integer $intSdi service definition id
     *
     * @return boolean
     */
    public function isFttpProduct($intSdi = null)
    {
        $helper = new AccountChange_BroadbandOnlyHelper();

        return $helper->isFttpProduct($intSdi);
    }

    /**
     * returns true if supplied service definition id is an SoGEA product.
     *
     * @param $intSdi
     * @return bool
     */
    public function isSogeaProduct($intSdi = null)
    {
        $helper = new AccountChange_BroadbandOnlyHelper();

        return $helper->isSogeaProduct($intSdi);
    }

    /**
     * Determine contract type from agreement date and service id
     *
     * @param string $agreementDate Agreement date
     * @param int    $serviceId     Service Id
     *
     * @return \Plusnet\ContractsClient\Entity\ContractTypeDto
     */
    protected function determineContractType($agreementDate, $serviceId)
    {
        $contractClient = new ContractsClient();

        return $contractClient->getContractTypeByAgreementDate($agreementDate, $serviceId);
    }
}
