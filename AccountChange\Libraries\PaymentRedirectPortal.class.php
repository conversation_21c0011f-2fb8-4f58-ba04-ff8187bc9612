<?php
/**
 * AccountChange PaymentRedirect Portal
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-07-19
 */
/**
 * AccountChange PaymentRedirect Portal
 *
 * This class handles the redirection to external payment application.
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class AccountChange_PaymentRedirectPortal extends AccountChange_PaymentRedirect
{
    /**
     * Get the decorator class
     *
     * @return string
     */
    protected function getDecorator()
    {
        return 'Mvc_PortalDecorator';
    }

    /**
     * Get the view to return to from GImP
     *
     * @see AccountChange_PaymentRedirect::getReturnView
     *
     * @return string
     */
    protected function getReturnView()
    {
        return 'PaymentRedirectPortal';
    }
}
