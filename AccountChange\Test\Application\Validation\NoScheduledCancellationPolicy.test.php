<?php
/**
 * <AUTHOR> <<EMAIL>>
 */


class AccountChange_NoScheduledCancellationPolicyTest extends PHPUnit_Framework_TestCase
{
    const TEST_SERVICE_COMPONENT_ID = 123;
    const TEST_END_DATE = '2022-01-01';

    /** @var Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject  */
    private $mockBusinessActor;

    /**
     * @return void
     */
    public function setUp()
    {
        $this->mockBusinessActor = $this->mockBusinessActor();
    }

    /**
     * @dataProvider dataForTestValidate
     * @param string $endDate        End date
     * @param bool   $expectedResult expected result
     * @return void
     */
    public function testValidate($endDate, $expectedResult)
    {
        $policy = $this->initialisePolicy($endDate);
        $this->assertEquals($expectedResult, $policy->validate());
    }

    /**
     * @return array
     */
    public function dataForTestValidate()
    {
        return [
            'no end date' => [
                'endDate' => null,
                true,
            ],
            'has end date' => [
                'endDate' => static::TEST_END_DATE,
                false,
            ],
        ];
    }

    /**
     * @return void
     */
    public function testGetErrorCodeAndFailure()
    {
        $policy = $this->initialisePolicy();

        $this->assertEquals($policy::ERROR_MESSAGE, $policy->getFailure());
        $this->assertEquals($policy::ERROR_CODE, $policy->getErrorCode());
    }

    /**
     * @param string $endDate end date
     * @return AccountChange_NoScheduledCancellationPolicy
     */
    private function initialisePolicy($endDate = null)
    {
        return new AccountChange_NoScheduledCancellationPolicy(
            $this->mockBusinessActor,
            false,
            false,
            [
                'serviceEndDate' => $endDate,
            ]
        );
    }

    /**
     * @return Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockBusinessActor()
    {
        return $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->getMock();
    }
}
