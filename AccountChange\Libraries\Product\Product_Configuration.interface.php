<?php
/**
 * Product Configuration Interface
 *
 * Interface for all product configuration types.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_Configuration.interface.php,v 1.2 2009-01-27 07:07:22 bselby Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Product Configuration Interface
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
interface AccountChange_Product_Configuration
{
    /**
     * Constructor to make the product configuration object
     *
     * @param int   $intId      Id
     * @param int   $intAction  Action
     * @param array $arrOptions Options
     *
     * @return void
     */
    public function __construct($intId, $intAction, array $arrOptions = array());

    /**
     * Initialise the product configuration object. We may need to pass in new contract lengths and end dates etc
     *
     * @param int   $intId      Service definition id
     * @param int   $intAction  Action
     * @param array $arrOptions Options
     *
     * @return void
     */
    public function initialise($intId, $intAction, array $arrOptions);

    /**
     * Getter for the new product configuration
     *
     * @return object
     */
    public function getNewProductConfiguration();

    /**
     * Return the product id (Service Definition Id or Service Component Id
     *
     * @return int
     */
    public function getProductId();

    /**
     * Check to see if the product is a key product
     *
     * @return bool
     */
    public function isKeyProduct();

    /**
     * Getter for the action on the account change
     *
     * @return int
     */
    public function getAction();

    /**
     * Getter for the account change operation (upgrade or downgrade or same)
     *
     * @return int
     */
    public function getAccountChangeOperation();

    /**
     * Getter for the tickets array
     *
     * @return array
     */
    public function getTickets();

    /**
     * Getter for getting the service notices
     *
     * @return array
     */
    public function getServiceNotices();

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration Account change configuration
     *
     * @return void
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration);

    /**
     * Match the product configuration with another product configuration
     *
     * This can be used if you have the matching object, and you need to perform some checks
     *
     * @param AccountChange_Product_Configuration $objConfiguration Product Configuraiton
     *
     * @return void
     */
    public function setMatchingProductConfigurationManually(AccountChange_Product_Configuration $objConfiguration);

    /**
     * Decide whether it will be an upgrade or downgrade for each product
     *
     * Decision made by the value of the products
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration Account Configuration
     *
     * @return void
     */
    public function setAccountChangeOperation(AccountChange_AccountConfiguration $objAccountConfiguration);

    /**
     * Setter for the service id
     *
     * @param int $intServiceId Service Id
     *
     * @return void
     */
    public function setServiceId($intServiceId);

    /**
     * Calculate the pro rata charge that needs to be taken if we are upgrading
     *
     * @return array
     */
    public function calculateProRataCharge();

    /**
     * Getter for whether or not we are suppose to take payment for this product
     *
     * @return bool
     */
    public function isTakingPayment();

    /**
     * Perform the product change
     *
     * @return void
     */
    public function execute();

    /**
     * Send the emails to confirm what just happened
     *
     * @param array $arrData Data used for confirmation email
     *
     * @return void
     */
    public function sendConfirmationEmail(array $arrData = array());

    /**
     * Send appointment related emails (this is optional)
     *
     * @param array $arrData Data used for appointment email
     *
     * @return void
     */
    public function sendAppointmentEmail(array $arrData = array());
}
