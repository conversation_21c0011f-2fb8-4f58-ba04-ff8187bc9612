<?php

/**
 * Test class for AccountChagnge_PromotionCodeRiskProfilePolicy
 */

class AccountChange_PromotionCodeAccountCompatibilityPolicyTest extends \PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;
    const PROMO_CODE = 'testPromotion';
    private $c2mClient;

    /**
     * @return void
     */
    protected function setup()
    {
        $this->c2mClient = $this->getMockBuilder('Plusnet\C2mApiClient\V5\Client')
            ->disableOriginalConstructor()
            ->setMethods(array('getSalesChannelData', 'getPromotionsForSalesChannelAndCharacteristics'))
            ->getMock();

        BusTier_BusTier::setClient('c2mapi.v5', $this->c2mClient);
    }

    /**
     * Tests that getErrorCode gives the expected error code
     *
     * @return void
     **/
    public function testGetErrorCodeGivesCorrectCode()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => null
        );

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);

        $this->assertEquals(AccountChange_PromotionCodeAccountCompatibilityPolicy::ERROR_CODE, $validator->getErrorCode());
    }


    /**
     * Tests that getFailure gives the expected error message
     *
     * @return void
     **/
    public function testGetFailureGivesExpectedErrorMessage()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => null
        );

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);

        $this->assertEquals(AccountChange_PromotionCodeAccountCompatibilityPolicy::ERROR_MESSAGE, $validator->getFailure());
    }


    /**
     * Tests that validate returns true if there's no offer code passed in
     *
     * @return void
     **/
    public function testValidateReturnsTrueWhenNoPromoIsPassedViaUrl()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => null
        );

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);

        $this->assertTrue($validator->validate());
    }

    /**
     * Tests that validate returns true if the offer is personalised
     *
     * @return void
     **/
    public function testValidateReturnsTrueWhenPromoIsPersonalised()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => self::PROMO_CODE
        );

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(true);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn($c2mPromotion);

        $this->assertTrue($validator->validate());
    }

    /**
     * Tests that validate returns true if there's no campaign
     *
     * @return void
     **/
    public function testValidateContinuesWhenWeAreNotInACampaign()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => self::PROMO_CODE
        );

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(false);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn($c2mPromotion);
        $validator->shouldReceive('hasLineRentalSaver')->andReturnFalse();

        $this->assertFalse($validator->validate());
    }

    /**
     * Tests that validate sets characteristics to null if calls to clients fail
     *
     * @return void
     **/
    public function testValidateSetsCharacteristicsToNullWhenCallToRBMOrCampaignNotificationFails()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => self::PROMO_CODE
        );

        $expectedCharacteristics = array(
            'riskProfile' => null,
            'upgradePropensity' => null,
            'currentProduct' => null,
            'lineRentalPayment' => 'MONTHLY'
        );

        $insightData = Mockery::mock('Plusnet\CampaignNotificationClient\Model\CustomerInsightData');
        $insightData->makePartial();
        $insightData->shouldAllowMockingProtectedMethods();
        $insightData->shouldReceive('getRiskProfile')->andThrow(new Exception('cannot get insight data'));

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();
        $campaignNotificationClient->shouldReceive('getCustomerInsightData')->andReturn($insightData);

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(false);

        $this->c2mClient
            ->expects($this->once())
            ->method('getPromotionsForSalesChannelAndCharacteristics')
            ->with('PlusnetResidential-AccountChange-NoAffiliate-NoCampaign', null, $expectedCharacteristics)
            ->will($this->returnValue(null));

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn($c2mPromotion);
        $validator->shouldReceive('getProductFromRbm')->andThrow(new Exception('Cannot get product details'));
        $validator->shouldReceive('hasLineRentalSaver')->andReturnFalse();

        $this->assertFalse($validator->validate());
    }

    /**
     * Tests that validate sets characteristics to null if getCustomerInsightData does not
     * return a valid object
     *
     * @return void
     **/
    public function testValidateSetsCharacteristicsToNullWhenGetCustomerInsightDataIsEmpty()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => self::PROMO_CODE
        );

        $expectedCharacteristics = array(
            'riskProfile' => null,
            'upgradePropensity' => null,
            'currentProduct' => null,
            'lineRentalPayment' => 'ONE_OFF'
        );

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();
        $campaignNotificationClient->shouldReceive('getCustomerInsightData')->andReturn(null);

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(false);

        $this->c2mClient
            ->expects($this->once())
            ->method('getPromotionsForSalesChannelAndCharacteristics')
            ->with('PlusnetResidential-AccountChange-NoAffiliate-NoCampaign', null, $expectedCharacteristics)
            ->will($this->returnValue(null));

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn($c2mPromotion);
        $validator->shouldReceive('getProductFromRbm')->andThrow(new Exception('Cannot get product details'));
        $validator->shouldReceive('hasLineRentalSaver')->andReturnTrue();

        $this->assertFalse($validator->validate());
    }


    /**
     * Tests that validate returns true when our promo is in the list of allowable promos
     *
     * @return void
     **/
    public function testValidateReturnsTrueWhenPromotionIsInListOfPromotionWithMatchingCharacteristics()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => self::PROMO_CODE
        );

        $expectedCharacteristics = array(
            'riskProfile' => null,
            'upgradePropensity' => null,
            'currentProduct' => null,
            'lineRentalPayment' => 'MONTHLY'
        );

        // A list of promotions returned matching the given characteristics
        $matchingPromotions = $this->getSomePromotions();

        $insightData = Mockery::mock('Plusnet\CampaignNotificationClient\Model\CustomerInsightData');
        $insightData->makePartial();
        $insightData->shouldAllowMockingProtectedMethods();
        $insightData->shouldReceive('getRiskProfile')->andThrow(new Exception('cannot get insight data'));

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();
        $campaignNotificationClient->shouldReceive('getCustomerInsightData')->andReturn($insightData);

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(false);
        $c2mPromotion->setCode(self::PROMO_CODE);

        // Add our promo to the list of allowable promos
        $matchingPromotions = array_merge($matchingPromotions, [$c2mPromotion]);

        $this->c2mClient
            ->expects($this->once())
            ->method('getPromotionsForSalesChannelAndCharacteristics')
            ->with('PlusnetResidential-AccountChange-NoAffiliate-NoCampaign', null, $expectedCharacteristics)
            ->will($this->returnValue($matchingPromotions));

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn($c2mPromotion);
        $validator->shouldReceive('getProductFromRbm')->andThrow(new Exception('Cannot get product details'));
        $validator->shouldReceive('hasLineRentalSaver')->andReturnFalse();

        $this->assertTrue($validator->validate());
    }

    /**
     * Tests that validate returns false when we get a list of promos back but our promo isn't in it.
     *
     * @return void
     **/
    public function testValidateReturnsFalseWhenWeGetAListOfPromosBackButOurPromoIsNotInIt()
    {
        $additionalInfo = array(
            'SalesChannel'     => 'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'C2MPromotionCode' => self::PROMO_CODE
        );

        $expectedCharacteristics = array(
            'riskProfile' => null,
            'upgradePropensity' => null,
            'currentProduct' => null,
            'lineRentalPayment' => 'ONE_OFF'
        );

        // A list of promotions returned matching the given characteristics
        $matchingPromotions = $this->getSomePromotions();

        $insightData = Mockery::mock('Plusnet\CampaignNotificationClient\Model\CustomerInsightData');
        $insightData->makePartial();
        $insightData->shouldAllowMockingProtectedMethods();
        $insightData->shouldReceive('getRiskProfile')->andThrow(new Exception('cannot get insight data'));

        $campaignNotificationClient = Mockery::mock('CampaignNotificationClient');
        $campaignNotificationClient->makePartial();
        $campaignNotificationClient->shouldAllowMockingProtectedMethods();
        $campaignNotificationClient->shouldReceive('getCustomerInsightData')->andReturn($insightData);

        BusTier_BusTier::setClient('campaignNotificationClient', $campaignNotificationClient);

        $c2mPromotion = $this->getC2mPromotionMock();
        $c2mPromotion->shouldReceive('getIsPersonalisedOffer')->andReturn(false);
        $c2mPromotion->setCode(self::PROMO_CODE);

        $this->c2mClient
            ->expects($this->once())
            ->method('getPromotionsForSalesChannelAndCharacteristics')
            ->with('PlusnetResidential-AccountChange-NoAffiliate-NoCampaign', null, $expectedCharacteristics)
            ->will($this->returnValue($matchingPromotions));

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = Mockery::mock('AccountChange_PromotionCodeAccountCompatibilityPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();
        $validator->shouldReceive('getAdditionalInformation')->andReturn($additionalInfo);
        $validator->shouldReceive('getC2mPromotionFromPromotionalCode')->with(self::PROMO_CODE)->andReturn($c2mPromotion);
        $validator->shouldReceive('getProductFromRbm')->andThrow(new Exception('Cannot get product details'));
        $validator->shouldReceive('hasLineRentalSaver')->andReturnTrue();

        $this->assertFalse($validator->validate());
    }

    /**
     * Return a mock c2m promotion
     *
     * @return Promotion
     **/
    protected function getC2mPromotionMock()
    {
        $c2mPromotion = Mockery::mock('Plusnet\C2mApiClient\Entity\Promotion');
        $c2mPromotion->makePartial();
        return $c2mPromotion;
    }

    /**
     * Return a list of promotions with the code attribute set
     *
     * @return array
     **/
    protected function getSomePromotions()
    {
        $promotions = array();
        for ($i=0; $i<=10; $i++) {
            $promotion = $this->getC2mPromotionMock();
            $promotion->setCode("Promo$i");
            $promotions[] = $promotion;
        }
        return $promotions;
    }
}
