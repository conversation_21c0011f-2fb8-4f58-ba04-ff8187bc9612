<?php
/**
 * AccountChange Exchange Helper test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 Plusnet
 */
/**
 * AccountChange Fibre Helper test
 *
 * Test class for AccountChange_FibreHelper
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 Plusnet
 */
class AccountChange_FibreHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Tear down functionality
     *
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::restoreAdaptor('AccountChange');
        $registry = AccountChange_Registry::instance();
        $registry->reset();
    }

    /**
     * Tests that isFibreProduct returns true if the given sdi is for a
     * fibre based product, and the result hasn't been cached.
     *
     * @covers AccountChange_FibreHelper::isFibreProduct
     * @covers AccountChange_FibreHelper::getIsFibreCache
     * @covers AccountChange_FibreHelper::setIsFibreCache
     *
     * @return void
     **/
    public function testIsFibreProductWhenSdiIsFibreNoCache()
    {
        $registry = AccountChange_Registry::instance();
        $registry->reset();

        $sdid = 6666;

        $dbMock = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $dbMock
            ->expects($this->once())
            ->method('isFibreProduct')
            ->with($sdid)
            ->will($this->returnValue(true));

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $helper = new AccountChange_FibreHelper();
        $result = $helper->isFibreProduct($sdid);
        $this->assertTrue($result);

        $cache = $registry->getEntry('isFibreCache');
        $this->assertEquals($cache, array($sdid=>true));
    }

    /**
     * Tests that isFibreProduct builds up a cache of results correctly
     *
     * @covers AccountChange_FibreHelper::isFibreProduct
     * @covers AccountChange_FibreHelper::getIsFibreCache
     * @covers AccountChange_FibreHelper::setIsFibreCache
     *
     * @return void
     **/
    public function testIsFibreProductBuildsUpCache()
    {
        $sdid1 = 6666;
        $sdid2 = 6667;

        $dbMock = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $dbMock
            ->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(true));

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $helper = new AccountChange_FibreHelper();
        $result1 = $helper->isFibreProduct($sdid1);
        $result2 = $helper->isFibreProduct($sdid2);

        $registry = AccountChange_Registry::instance();
        $cache = $registry->getEntry('isFibreCache');
        $this->assertEquals($cache, array($sdid1=>true, $sdid2=>true));
    }

    /**
     * Tests that isFibreProduct returns false if the given sdi is for a
     * fibre based product, and the result hasn't been cached.
     *
     * @covers AccountChange_FibreHelper::isFibreProduct
     * @covers AccountChange_FibreHelper::getIsFibreCache
     * @covers AccountChange_FibreHelper::setIsFibreCache
     *
     * @return void
     **/
    public function testIsFibreProductWhenSdiIsNotFibreNoCache()
    {
        $sdid = 6667;

        $dbMock = $this->getMock(
            'Db_Adaptor',
            array('isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $dbMock
            ->expects($this->once())
            ->method('isFibreProduct')
            ->with($sdid)
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $helper = new AccountChange_FibreHelper();
        $result = $helper->isFibreProduct($sdid);
        $this->assertFalse($result);

        $registry = AccountChange_Registry::instance();
        $cache = $registry->getEntry('isFibreCache');
        $this->assertEquals($cache, array($sdid=>false));
    }

    /**
     * Tests that isFibreProduct returns the correct results from cache
     * when set
     *
     * @covers AccountChange_FibreHelper::isFibreProduct
     * @covers AccountChange_FibreHelper::getIsFibreCache
     *
     * @return void
     **/
    public function testIsFibreProductWhenReadingFromCache()
    {
        $cache = array();
        $cache[6777] = true;
        $cache[6666] = false;

        $helper = new AccountChange_FibreHelper();
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('isFibreCache', $cache);

        $result1 = $helper->isFibreProduct(6777);
        $result2 = $helper->isFibreProduct(6666);

        $this->assertTrue($result1);
        $this->assertFalse($result2);
    }
}
