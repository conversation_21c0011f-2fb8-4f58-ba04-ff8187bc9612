<?php
/**
 * Shared code for broadband product filter in workplace and house moves
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Shared logic for broadband product filters in both standard workplace and house move journey
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
abstract class AccountChange_Product_BroadbandProductFilter_WorkplaceHousemove extends
    AccountChange_Product_BroadbandProductFilter_Common
{
    /**
     * Filter the broadband product variants
     *
     * @param LineCheck_Result $lineCheck         Line check result
     * @param array            $availableProducts Available products
     * @param array            $currentProduct    Current product
     * @param string           $campaignCode      Campaign code indicating which landing page this journey originated from, if any
     *
     * @return array Filtered products
     */
    public function filter(LineCheck_Result $lineCheck, array $availableProducts, array $currentProduct, $campaignCode = '')
    {
        $availableProducts = $this->commonFilter($lineCheck, $availableProducts, $currentProduct);

        $currentProductSdi = $currentProduct['intSdi'];

        $filteredProducts = array();
        $currentProductVariants = array();

        $broadbandProductVariants = $this->getServiceDefinitionVariants($currentProductSdi);

        // The 'current product' is defined as the service definition id of either the solus or
        // dual play version of that product, or just the current service definition if there's no
        // solus / dual play pricing.
        $currentProductSdis = array(
            'solus' => null,
            'dualplay' => null,
            'legacy' => null
        );


        if (isset($availableProducts['arrProducts']) && is_array($availableProducts['arrProducts'])) {
            $availableProducts = $availableProducts['arrProducts'];
        }

        foreach ($availableProducts as $availableProduct) {
            if (in_array($availableProduct['intSdi'], $broadbandProductVariants)) {
                foreach (array('solus', 'dualplay') as $variantType) {
                    if (array_key_exists($variantType, $broadbandProductVariants) &&
                        $broadbandProductVariants[$variantType] == $availableProduct['intSdi']
                    ) {
                        // We've found the solus/dualplay version of the current product.
                        $currentProductVariants[$variantType] = $availableProduct;
                        $currentProductSdis[$variantType] = $availableProduct['intSdi'];
                    }
                }
            } elseif ($currentProductSdi == $availableProduct['intSdi']) {
                // We've found the current legacy product
                $currentProductVariants['legacy'] = $availableProduct;
                $currentProductSdis['legacy'] = $availableProduct['intSdi'];
            }

            $this->categoriseProduct($filteredProducts, $availableProduct, $currentProduct, $currentProductSdis);
        }

        $this->categoriseCurrentProduct($filteredProducts, $currentProduct, $currentProductVariants);

        return $filteredProducts;
    }

    /**
     * Put an available product into a category for display,
     * such as Solus, DualPlay, JohnLewis Products or Legacy Products
     *
     * @param array[] &$filteredProducts  Products filtered into categories so far.
     *                                    $availableProduct will be added to this.
     * @param array   $availableProduct   Product definition to be categorised
     * @param array   $currentProduct     Product definition of the user's current product
     * @param int[]   $currentProductSdis SDIs for the variants of the user's current product
     *
     * @return void
     */
    protected function categoriseProduct(&$filteredProducts, $availableProduct, $currentProduct, $currentProductSdis)
    {
        /** @var ProductFamily_ProductFamily $productFamily */
        $productFamily = $availableProduct['productFamily'];
        $isp = isset($currentProduct['isp']) ? $currentProduct['isp'] : null;

        /** @var ProductFamily_ProductFamily $currentProductFamily */
        $currentProductFamily = $currentProduct['productFamily'];
        $isCurrentProductLegacy = $currentProductFamily->isResidentialLegacy();

        // Show solus products if the user isn't 'allowed' to add WLR
        // We filter this for house moves by turning on or off WLR add allowed
        if ($productFamily->isSolus() &&
            $availableProduct['intSdi'] != $currentProductSdis['solus'] &&
            $this->shouldAllowSolusProducts($currentProduct)
        ) {
            // Showing a solus product
            $filteredProducts['Solus'][] = $availableProduct;

        } elseif ($productFamily->isDualPlay() &&
            $availableProduct['intSdi'] != $currentProductSdis['dualplay'] &&
            $this->shouldAllowDualPlayProducts($currentProduct)
        ) {
            // Showing a dual play product
            $filteredProducts['DualPlay'][] = $availableProduct;

        } elseif ($productFamily->isJohnLewis() &&
            $availableProduct['intSdi'] != $currentProductSdis['legacy'] &&
            $this->shouldAllowJohnLewisProducts($currentProduct)
        ) {
            // Showing a JLBB product
            $filteredProducts['JohnLewis Products'][] = $availableProduct;

        } elseif (!in_array($availableProduct['intSdi'], $currentProductSdis) &&
            $this->shouldAllowLegacyProducts(
                $isCurrentProductLegacy,
                $currentProductFamily,
                $productFamily,
                $isp
            )
        ) {
            // Showing a legacy producy
            $filteredProducts['Legacy Products'][] = $availableProduct;
        }
    }

    /**
     * Work out the correct solus/dualplay versions of the customer's current product
     * and add them under the appropriate keys in the array if applicable
     *
     * @param array[] &$filteredProducts      Products filtered into categories. Will be updated in place.
     * @param array   $currentProduct         Product definition of the user's current product
     * @param array[] $currentProductVariants Variant versions of the user's current product
     *
     * @return array
     */
    abstract protected function categoriseCurrentProduct(&$filteredProducts, $currentProduct, $currentProductVariants);
}
