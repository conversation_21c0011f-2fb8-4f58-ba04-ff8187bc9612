<?php
/**
 * AccountChange Registry Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-15
 */
/**
 * AccountChange Registry Test
 *
 * Test class for AccountChange_Registry
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Registry_Test extends PHPUnit_Framework_TestCase
{
    /**
     * PHPUnit teardown function
     *
     * @return void
     */
    public function teardown()
    {
        $registry = AccountChange_Registry::instance();
        $registry->reset();
    }

    /**
     * Test that the constructor correctly sets up the object
     *
     * @covers AccountChange_Registry::__construct
     *
     * @return void
     */
    public function testConstructor()
    {
        $registry = new AccountChange_Registry();

        $this->assertAttributeEquals(array(), 'data', $registry);
    }

    /**
     * Test that the factory instance methods returns new object if one isn't set
     *
     * @covers AccountChange_Registry::instance
     *
     * @return void
     */
    public function testInstanceReturnsNewObjectIfNotOneSet()
    {
        $registry = AccountChange_Registry::instance();

        $this->assertAttributeEquals(array(), 'data', $registry);
    }

    /**
     * Test that the instance methods returns the object originall set
     *
     * @covers AccountChange_Registry::instance
     * @covers AccountChange_Registry::setInstance
     *
     * @return void
     */
    public function testInstanceReturnsObjectAlreadySetIfThereIsOne()
    {
        $aRegistry = new AccountChange_Registry();

        AccountChange_Registry::setInstance($aRegistry);

        $registry = AccountChange_Registry::instance();

        $this->assertAttributeEquals(array(), 'data', $registry);
    }

    /**
     * Test that the internal data is cleared when reset is called
     *
     * @covers AccountChange_Registry::reset
     *
     * @return void
     */
    public function testResetClearsTheInternalArray()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('key', 'entry');

        $registry->reset();
        $this->assertAttributeEquals(array(), 'data', $registry);
    }

    /**
     * Test that getEntry returns null when the key does not exist
     *
     * @covers AccountChange_Registry::getEntry
     *
     * @return void
     */
    public function testGetEntryReturnsNullIfKeyDoesNotExist()
    {
        $registry = AccountChange_Registry::instance();
        $result = $registry->getEntry('invalidEntry');

        $this->assertNull($result);
    }

    /**
     * Test the getter and setter works correctly
     *
     * @covers AccountChange_Registry::getEntry
     * @covers AccountChange_Registry::setEntry
     *
     * @return void
     */
    public function testSetEntryCorrectlySetsAttributeAndThatGetReturnsCorrectAttribute()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('key', 'entry');

        $result = $registry->getEntry('key');

        $this->assertEquals('entry', $result);
    }
}
