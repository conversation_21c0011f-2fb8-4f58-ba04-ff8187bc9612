<?php

class AccountChange_PromotionCodeWorkplaceChannelPolicyTest extends \PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;
    const PROMO_CODE = 'testPromotion';
    private $c2mClient;

    protected function setup()
    {
        $this->c2mClient = $this->getMockBuilder('Plusnet\C2mApiClient\V5\Client')
            ->disableOriginalConstructor()
            ->setMethods(array('getSalesChannelData'))
            ->getMock();

        BusTier_BusTier::setClient('c2mapi.v5', $this->c2mClient);
    }

    protected function teardown()
    {
        $c2mClient = null;
    }

    /**
     * Tests that validate returns true for non-c2m offers (we want to allow these through)
     *
     * @return void
     */
    public function testValidateReturnsTrueIfThereIsNoC2mOfferCodePresent()
    {
        $additionalInfo = array();

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = new \AccountChange_PromotionCodeWorkplaceChannelPolicy($actor, true, false, $additionalInfo);

        $this->assertTrue($validator->validate());
    }

    public function testValidateReturnsTrueIfPromotionHasStaffAccountChangeChannel()
    {
        $promoSalesChannels = array(
            'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'PlusnetResidential-StaffAccountChange-NoAffiliate-Retention'
        );

        $c2mMock = $this->getC2mPromotionMock();
        $c2mMock->shouldReceive('getSalesChannels')->andReturn($promoSalesChannels);

        $additionalInfo = array(
            'C2MPromotionCode' => self::PROMO_CODE,
            'C2MPromotion' => $c2mMock,
            'SalesChannel' => 'PlusnetResidential-StaffAccountChange-NoAffiliate-Retention'
        );

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = new \AccountChange_PromotionCodeWorkplaceChannelPolicy($actor, true, false, $additionalInfo);

        $this->assertTrue($validator->validate());
    }

    public function testValidateReturnsFalseIfPromotionHasNoChangeChannelWithStaffAccountChange()
    {
        $promoSalesChannels = array(
            'PlusnetResidential-AccountChange-NoAffiliate-NoCampaign',
            'PlusnetResidential-AccountChange-NoAffiliate-Retention'
        );

        $c2mMock = $this->getC2mPromotionMock();
        $c2mMock->shouldReceive('getSalesChannels')->andReturn($promoSalesChannels);

        $additionalInfo = array(
            'C2MPromotionCode' => self::PROMO_CODE,
            'C2MPromotion' => $c2mMock,
            'SalesChannel' => 'PlusnetResidential-StaffAccountChange-NoAffiliate-Retention'
        );

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('CUSTOMER');

        $validator = new \AccountChange_PromotionCodeWorkplaceChannelPolicy($actor, true, false, $additionalInfo);

        $this->assertFalse($validator->validate());
    }

    /**
     * Return a mock c2m promotion
     *
     * @return Promotion
     **/
    protected function getC2mPromotionMock()
    {
        $c2mPromotion = Mockery::mock('Plusnet\C2mApiClient\Entity\Promotion');
        $c2mPromotion->makePartial();

        return $c2mPromotion;
    }
}
