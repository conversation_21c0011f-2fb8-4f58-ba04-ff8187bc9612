<?php
/**
 * Terms and Conditions
 *
 * Testing class for the AccountChange_TermsAndConditions
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-10-01
 */

/**
 * Terms and Conditions Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_TermsAndConditions_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Current tariffs being tested
     *
     * @var array
     */
    private $arrCurrentTari<PERSON>;

    /**
     * Current data being tested with
     *
     * @var array
     */
    private $arrCurrentData;

    public function setUp()
    {
        $this->turnOffNewBillingEngine();
    }

    /**
     * Reset the DB layer
     *
     * @return void
     */
    public function tearDown()
    {
        parent::tearDown();

        Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('Core');
        Db_Manager::restoreAdaptor('Val');
        Db_Manager::restoreAdaptor('Auth');
        Db_Manager::restoreAdaptor('AccountChange');
    }

    /**
     * Checks for the arrInput variables for Terms and Conditions requirement
     *
     * @return void
     */
    public function testArrInputIsSetupCorrectly()
    {
        $arrInputs = array(
            'bolAgreed'               => 'external:Custom',
            'arrCharges'              => 'external:Custom:optional',
            'bolOneOffCharges'        => 'internal:Int:optional',
            'intTotalToPayToday'      => 'internal:Int:optional',
            'bolTakeover'             => 'external:Custom',
            'bolTakeoverAgreed'       => 'external:Custom:optional',
            'bolAdslTakeover'         => 'external:Bool:optional',
            'bolWlrTakeover'          => 'external:Bool:optional',
            'vchConsentVersionHandle' => 'external:Custom:optional'
        );

        $objTermsAndConditionsRequirement = new AccountChange_TermsAndConditions();

        $this->assertAttributeEquals($arrInputs, 'arrInputs', $objTermsAndConditionsRequirement);
    }

    /**
     * Test the getScheduleOverrideForBroadbandIfNeeded function
     *
     * @param Auth_BusinessActor $businessActor The Business Actor
     * @param bool               $bolSchedule   Current scheduling needs
     * @param array              $result        Expected output
     *
     * @covers       AccountChange_TermsAndConditions::isScheduleOverrideForBroadbandNeeded
     *
     * @dataProvider provideWlrScheduleOverrideData
     *
     * @return void
     */
    public function testGetScheduleOverrideForBroadbandWithData($businessActor, $bolSchedule, $result)
    {
        $requirement = new AccountChange_TermsAndConditions();

        $output = $requirement->isScheduleOverrideForBroadbandNeeded($businessActor, $bolSchedule);

        $this->assertEquals($result, $output);
    }

    /**
     * Test the getScheduleOverrideForWlrIfNeeded function
     *
     * @param Auth_BusinessActor $businessActor The Business Actor
     * @param bool               $bolSchedule   Current scheduling needs
     * @param array              $result        Expected output
     *
     * @covers       AccountChange_TermsAndConditions::isScheduleOverrideForWlrNeeded
     *
     * @dataProvider provideWlrScheduleOverrideData
     *
     * @return void
     */
    public function testGetScheduleOverrideForWlrWithData($businessActor, $bolSchedule, $result)
    {
        $requirement = new AccountChange_TermsAndConditions();

        $output = $requirement->isScheduleOverrideForWlrNeeded($businessActor, $bolSchedule);

        $this->assertEquals($result, $output);
    }

    /**
     * Data provider for getScheduledOverride* functions
     *
     * @return array
     */
    public static function provideWlrScheduleOverrideData()
    {
        $actorWorkplace = new Auth_BusinessActor(null);
        $actorWorkplace->setUserType('PLUSNET_STAFF');

        $actorPlusnetAutomatedProcess = new Auth_BusinessActor(null);
        $actorPlusnetAutomatedProcess->setUserType('PLUSNET_AUTOMATED_PROCESS');

        $actorNoneWorkplace = new Auth_BusinessActor(null);
        $actorNoneWorkplace->setUserType('PLUSNET_ENDUSER');

        return array(
            array($actorNoneWorkplace, true, true),
            array($actorWorkplace, '', false),
            array($actorWorkplace, true, true),
            array($actorPlusnetAutomatedProcess, true, true),
        );
    }

    /**
     * Function to provide the test data for AccountChange_TermsAndConditions::describe
     *
     * @return array
     */
    private function dataProviderFortestDescribe()
    {
        $objMockAdaptor = new AccountChange_MockAdaptorHelper();
        $objMockAdaptor->setCoreMockAdaptor();

        $objCoreService = new Core_Service(1234);
        $intNewCost = new I18n_Currency('gbp', 20);
        $intNewCallPlanCost = new I18n_Currency('gbp', 30);
        $intNewLineRentCost = new I18n_Currency('gbp', 10);

        $arrSelectedBroadband = array(
            'strNewProduct'     => 'Force9 Broadband Plus up to 8Mb (Monthly Contract, No Modem)',
            'intNewCost'        => $intNewCost,
            'intNewLeadingCost' => ''
        );

        $arrSelectedWlr = array(
            'strNewProduct'      => 'Talk Evenings & Weekends',
            'intNewCost'         => $intNewCost,
            'bolSplitPrice'      => 1,
            'intNewLineRentCost' => $intNewCallPlanCost,
            'intNewCallPlanCost' => $intNewLineRentCost,
        );

        $arrWlrProduct = array(
            'strProductName'    => '',
            'strCallPlanName'   => '',
            'intProductCost'    => '',
            'intLineRentCost'   => '',
            'intCallPlanCost'   => '',
            'intOldWlrId'       => '',
            'strContractLength' => '',
            'strContractHandle' => '',
            'uxtContractEnd'    => '',
            'intInstanceId'     => '',
            'arrExistingChange' => array(),
            'activeCallFeatures'=> array(),
        );

        $arrValidatedApplicationData = array(
            'arrSelectedBroadband' => $arrSelectedBroadband,
            'arrSelectedWlr'       => $arrSelectedWlr,
            'intNewWlrId'          => 670,
            'objCoreService'       => $objCoreService,
            'arrWlrProduct'        => $arrWlrProduct,
            'intNewSdi'            => ********,
            'appointingType'       => array(
                'serviceHandle' => 'ADSL'
            )
        );

        return $arrValidatedApplicationData;
    }

    /**
     * Tests that the describe method correctly handles when changes have been made during account change.
     *
     * @covers AccountChange_TermsAndConditions::areThereAnyChanges
     * @covers AccountChange_TermsAndConditions::hasBroadbandProductChanged
     * @covers AccountChange_TermsAndConditions::hasWlrProductChanged
     * @covers AccountChange_TermsAndConditions::describe
     * @covers AccountChange_TermsAndConditions::getSignupAppTermsAndConditionsText
     *
     * @group  medium
     *
     * @return void
     */
    public function testDescribeForAccountChangesIsMade()
    {
        $mockTermsAndConditionsText = 'These are the terms and conditions';

        $this->setUpMockValAdaptor();
        $this->setUpMockAuthAdaptor();
        $this->setUpMockAccountChangeAdaptorForDescripe();

        // Create mock Val_DSLConnectivityProduct
        $objMockDslConnectivityProduct = $this->getMock(
            'Val_DSLConnectivityProduct',
            array(
                'setADSLProduct'
            ),
            array(1)
        );

        $objMockAdaptor = new AccountChange_MockAdaptorHelper();
        $objAccountchangeManager = $objMockAdaptor->accountChangeManagerObjectProvider();

        $objReq = $this->getMock(
            'AccountChange_TermsAndConditions',
            array(
                'getApplicationStateVariable',
                'isApplicationStateVariable',
                'createBroadbandConfigurations',
                'createWlrConfigurations',
                'getAccountChangeManager',
                'getHardwareCharges',
                'raiseTicketToCsc',
                'getValDslConnectivityProduct',
                'getSelectedBroadband',
                'isDualPlay',
                'getServiceDefinitionVariants',
                'getSignupAppTermsAndConditionsText',
                'getMinAndMaxSpeedRanges',
                'getScheduledChangeDate'
            )
        );

        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    array('AccountChange_MockAdaptorHelper', 'getDataForGetApplicationStateVariable')
                )
            );

        $objMockController
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnValue('false'));

        $contractDurationHelper = $this
            ->getMockBuilder('AccountChange_ContractDurationHelper')
            ->disableOriginalConstructor()
            ->setMethods(['shouldDisplayContractDuration'])
            ->getMock();

        $contractDurationHelper->method('shouldDisplayContractDuration')->willReturn(false);

        AccountChange_ServiceManager::setService('ContractDurationHelper', $contractDurationHelper);

        $objReq
            ->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $objReq
            ->expects($this->any())
            ->method('createBroadbandConfigurations')
            ->will($this->returnValue('true'));

        $objReq
            ->expects($this->any())
            ->method('createWlrConfigurations')
            ->will($this->returnValue('true'));

        $objReq
            ->expects($this->once())
            ->method('getAccountChangeManager')
            ->will($this->returnValue($objAccountchangeManager));

        $objReq
            ->expects($this->once())
            ->method('getHardwareCharges')
            ->will($this->returnValue(array(
                array(
                    AccountChange_TermsAndConditions::OUTSTANDING_CHARGES_KEY =>
                        AccountChange_TermsAndConditions::POSTAGE_AND_PACKAGING,
                    AccountChange_TermsAndConditions::OUTSTANDING_FEES_KEY => new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        5.99
                    ),
                    AccountChange_TermsAndConditions::OUTSTANDING_FEES_EX_VAT_KEY => new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        4.99
                    )))));

        $objReq
            ->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        $mockProductRules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService')
        );

        $mockProductRules
            ->expects($this->any())
            ->method('getProductProvisionForService')
            ->will(
                $this->returnValue(
                    array(
                        'intSupplierProductId'  => 19,
                        'intSupplierPlatformID' => 6,
                        'strProvisionOn'        => 'FTTC',
                    )
                )
            );

        $objReq
            ->expects($this->any())
            ->method('getValDslConnectivityProduct')
            ->will($this->returnValue($objMockDslConnectivityProduct));

        $objReq
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(array('maxDownstreamSpeed' => 45)));

        $objReq->setAppStateCallback($objMockController);

        $objReq->expects($this->once())
            ->method('getSignupAppTermsAndConditionsText')
            ->will($this->returnValue($mockTermsAndConditionsText));

        $arrValidatedApplicationData = $this->dataProviderFortestDescribe();

        $objReq
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $objReq->expects($this->any())
            ->method('getScheduledChangeDate')
            ->will($this->returnValue('10/10/2009'));

        $arrResult = $objReq->describe($arrValidatedApplicationData);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('bolWlrEssential', $arrResult);
        $this->assertArrayHasKey('strNextInvoiceDate', $arrResult);
        $this->assertEquals('10/10/2009', $arrResult['strNextInvoiceDate']);
        $this->assertArrayHasKey('intOldWlrId', $arrResult);
        $this->assertArrayHasKey('intOldWlrId', $arrResult);
        $this->assertArrayHasKey('intSelectedProductCost', $arrResult);
        $this->assertArrayHasKey('objDiscountedProductCost', $arrResult);
        $this->assertArrayHasKey('intSelectedProductCostTotal', $arrResult);
        $this->assertArrayHasKey('strTermsAndConditions', $arrResult);
        $this->assertArrayHasKey('arrLineCheckResults', $arrResult);
        $this->assertArrayHasKey('bolChangesMade', $arrResult);
        $this->assertArrayHasKey('estimatedSpeed', $arrResult);
        $this->assertEquals(5.99, $arrResult['hardwareCost']);
        $this->assertEquals(4.99, $arrResult['exVatHardwareCost']);

        $this->assertTrue($arrResult['bolChangesMade']);
    }

    /**
     * Tests that the describe method correctly handles when changes have not been made during account change.
     *
     * @covers AccountChange_TermsAndConditions::describe
     *
     * @return void
     */
    public function testDescribeForAccountChangesIsNotMade()
    {
        $objReq = $this->getMock(
            'AccountChange_TermsAndConditions',
            array(
                'areThereAnyChanges',
                'getSelectedBroadband',
                'getServiceDefinitionVariants',
                'getMinAndMaxSpeedRanges',
                'getScheduledChangeDate',
                'getApplicationStateVar',
                'isC2fToggleSet'
            )
        );

        $objReq->method('isC2fToggleSet')->willReturn(false);

        $objReq->expects($this->once())
            ->method('areThereAnyChanges')
            ->will($this->returnValue(false));

        $objReq->expects($this->never())
            ->method('getSelectedBroadband');

        $objReq->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $objReq->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $objReq->expects($this->any())
            ->method('getScheduledChangeDate')
            ->will($this->returnValue('10/10/2009'));

        $objCoreService = $this->getMock('Core_Service', array('getServiceId'));
        $objCoreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(12345));

        $this->arrCurrentData = array('objCoreService' => $objCoreService);

        $objMockController = $this->getMock('AccountChange_Controller', array('getApplicationStateVar'));
        $objMockController->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $objReq->setAppStateCallback($objMockController);

        $arrValidatedApplicationData = $this->dataProviderFortestDescribe();
        $arrResult = $objReq->describe($arrValidatedApplicationData);

        $this->assertInternalType('array', $arrResult);
        $this->assertArrayHasKey('bolChangesMade', $arrResult);
        $this->assertFalse($arrResult['bolChangesMade']);
        $this->assertEquals(5, count($arrResult));
        $this->assertEquals(0, $arrResult['hardwareCost']);
        $this->assertEquals(0, $arrResult['exVatHardwareCost']);
    }

    /**
     * Test that the getNewReqs() function returns the direct debit requirement when GB/WR/BAU customers
     * upgrade to JLP/PN.
     *
     * @param array  $oldServiceDefinition Old Service Definition Details
     * @param array  $newServiceDefinition New Service Definition Details
     * @param string $productIsp
     * @param array  $expectedRequirements Expected results from call to getNewReqs()
     *
     * @covers       AccountChange_TermsAndConditions::getNewReqs
     * @covers       AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJLP
     *
     * @dataProvider dataProviderForTestGetNewReqsAsksForDirectDebitWhenGbWrBauUpgradesToJlpPn
     *
     * @TODO Remove the dependancy on order specific mocks, See Conrad Hodges comments.
     *
     * @return void
     */
    public function testGetNewReqsDontAsksForDirectDebitWhenGbWrBauUpgradesToJlpPn(
        array $oldServiceDefinition,
        array $newServiceDefinition,
        $productIsp,
        array $expectedRequirements
    ) {
        $coreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreDbAdaptor
            ->expects($this->at(0))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($oldServiceDefinition));

        $coreDbAdaptor
            ->expects($this->at(1))
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($newServiceDefinition));

        Db_Manager::setAdaptor('Core', $coreDbAdaptor);

        $actor = new Auth_BusinessActor();


        $termsAndConditions = $this->getMock(
            'AccountChange_TermsAndConditions',
            array('hasOneOffCharges',
                'getServiceDefinitionVariants')
        );
        $termsAndConditions
            ->expects($this->once())
            ->method('hasOneOffCharges')
            ->will($this->returnValue(true));

        $termsAndConditions
            ->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()} and
        // {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar', 'getApplicationStateVar'));

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_TermsAndConditions::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController
            ->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue(true));
        $objMockController
            ->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue(array('intOldWlrId' => 1)));
        $objMockController
            ->expects($this->at(2))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('intNewWlrId'))
            ->will($this->returnValue(true));
        $objMockController
            ->expects($this->at(3))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('intNewWlrId'))
            ->will($this->returnValue(1));
        $objMockController
            ->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue(2));
        $objMockController
            ->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('intNewSdi'))
            ->will($this->returnValue(3));
        $objMockController
            ->expects($this->at(6))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('productIsp'))
            ->will($this->returnValue($productIsp));

        $productIspIsBauer = strpos($productIsp, 'bauer') === 0;
        if ($productIspIsBauer) {
            $objMockController
                ->expects($this->at(7))
                ->method('getApplicationStateVar')

                ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('intOldSdi'))
                ->will($this->returnValue(2));
            $objMockController
                ->expects($this->at(8))
                ->method('getApplicationStateVar')
                ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('intNewSdi'))
                ->will($this->returnValue(3));
        }


        $objMockController
            ->expects($this->at($productIspIsBauer ? 9 : 7))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($termsAndConditions)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($actor));


        $termsAndConditions->setAppStateCallback($objMockController);

        $actualRequirements = $termsAndConditions->getNewReqs();

        $this->assertEquals(in_array('AccountChange_DirectDebitDetails', $expectedRequirements), false);

    }

    /**
     * Data provider for testGetNewReqsAsksForDirectDebitWhenGbWrBauUpgradesToJlpPn
     *
     * @return array
     */
    public function dataProviderForTestGetNewReqsAsksForDirectDebitWhenGbWrBauUpgradesToJlpPn()
    {
        return array(
            // Greenbee to JLP account change
            array(
                array(
                    'isp' => 'greenbee',
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                'greenbee',
                array(
                    0 => 'AccountChange_PaymentMethodDetails'), // Expected requiremetns
            ),

            // Waitrose to JLP account change
            array(
                array(
                    'isp' => 'waitrose',
                ), // Old Service Definition
                array(
                    'isp' => 'johnlewis',
                ), // New Service Definition
                'waitrose',
                array(
                    0 => 'AccountChange_PaymentMethodDetails'), // Expected requiremetns
            ),

            // Bauer to Plusnet account change
            array(
                array(
                    'isp' => 'bauer_yc',
                ), // Old Service Definition
                array(
                    'isp' => 'plus.net',
                ), // New Service Definition
                'bauer',
                array(
                    0 => 'AccountChange_PaymentMethodDetails'), // Expected requiremetns
            ),

            // Plusnet account change
            array(
                array(
                    'isp' => 'plusnet',
                ), // Old Service Definition
                array(
                    'isp' => 'plusnet',
                ), // New Service Definition
                'plus.net',
                array(), // Expected requiremetns
            ),
        );
    }

    /**
     * Check that getSelectedBroadband returns the application state
     * variable that we're expecting
     *
     * @covers AccountChange_TermsAndConditions::getSelectedBroadband
     *
     * @return void
     **/
    public function testGetSelectedBroadbandReturnsExpected()
    {
        $selectedBroadband = array(
            'serviceDefinitionId' => 6768
        );

        $appointment = new AccountChange_TermsAndConditions();

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController
            = $this->getMock('AccountChange_Controller', array('getApplicationStateVar'));

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with('AccountChange_TermsAndConditions', 'arrSelectedBroadband')
            ->will($this->returnValue($selectedBroadband));
        // Set callback object within the requirement itself to application controller
        $appointment->setAppStateCallback($mockController);
        $actual = $appointment->getSelectedBroadband();
        $this->assertEquals($actual, $selectedBroadband);
    }

    /**
     * Test _getFttcAppointmentData returns proper values
     *
     * @param array $arrValidatedApplicationData Validated application data
     * @param array $expectedResult              Expected result
     *
     * @covers       AccountChange_TermsAndConditions::describe
     * @covers       AccountChange_TermsAndConditions::getFttcAppointmentData
     *
     * @dataProvider provideDataToTestGetFttcAppointmentDataReturnsProperValues
     *
     * @return void
     */
    public function testGetFttcAppointmentDataReturnsProperValues(
        $arrValidatedApplicationData,
        $expectedResult
    ) {
        $oldSdi = 6754;
        $newSdi = 6784;

        $valAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getISPConfig', 'isFibreProduct', 'getServiceDefinition'),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $actor = $this->getMock(
            'Auth_BusinessActor',
            array()
        );

        $mockBusActor = $this->getMockBuilder('Auth_BusinessActor')
            ->disableOriginalConstructor()
            ->setMethods(array('getUserType'))
            ->getMock();

        $mockBusActor->expects($this->any())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_STAFF'));

        $valAdaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $valAdaptor->expects($this->any())
            ->method('getISPConfig')
            ->will(
                $this->returnValue(
                    array(
                        'intISPDefinitionID'             => 2,
                        'strFullName'                    => 'Plusnet',
                        'strSupportPhoneNumber'          => '0800 432 0200 or 0345 140 0200',
                        'strPortalMainPageURL'           => 'portal.plus.net',
                        'strDefaultCustomerEmailAddress' => '<EMAIL>'
                    )
                )
            );

        // DB functions for Val_ConnectivityProduct (Val_DSLConnectivityProduct derives from it)
        $valAdaptor
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will(
                $this->returnValue(
                    array(
                        'intProductID'     => 2,
                        'strVariantHandle' => 'bob',
                        'strFamilyHandle'  => 'jom',
                        'strRequires'      => 'dsl',
                        'strType'          => 'residential'
                    )
                )
            );

        Db_Manager::setAdaptor('Val', $valAdaptor);

        // Set the Db_Adaptor for the AccountChange module.
        $accountChangeAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getComponentDetails', 'getServiceDefinitionDetails', 'isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $accountChangeAdaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $accountChangeAdaptor
            ->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue($arrValidatedApplicationData['selectedBroadband']));

        Db_Manager::setAdaptor('AccountChange', $accountChangeAdaptor);

        $accountConfiguration = $this->getMock(
            'AccountChange_AccountConfiguration',
            array('setTotalOneOffCharge'),
            array(array())
        );

        $accountChangeManager = $this->getMock(
            'AccountChange_Manager',
            array(),
            array(
                $arrValidatedApplicationData['objCoreService']->getServiceId(),
                $accountConfiguration,
                $accountConfiguration
            )
        );

        $productRules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService')
        );

        // Create mock Val_DSLConnectivityProduct
        $mockDslConnectivityProduct = $this->getMock(
            'Val_DSLConnectivityProduct',
            array(
                'setADSLProduct'
            ),
            array(1),
            '',
            false
        );

        $contractDurationHelper = $this
            ->getMockBuilder('AccountChange_ContractDurationHelper')
            ->disableOriginalConstructor()
            ->setMethods(['shouldDisplayContractDuration'])
            ->getMock();

        $contractDurationHelper->method('shouldDisplayContractDuration')->willReturn(false);

        AccountChange_ServiceManager::setService('ContractDurationHelper', $contractDurationHelper);

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_TermsAndConditions $requirement */
        $requirement = $this->getMock(
            'AccountChange_TermsAndConditions',
            array(
                'areThereAnyChanges',
                'createProductConfigurations',
                'getAccountChangeManager',
                'hasOneOffCharges',
                'isDualPlay',
                'getServiceDefinitionVariants',
                'getValDslConnectivityProduct',
                'getMinAndMaxSpeedRanges',
                'getScheduledChangeDate'
            )
        );

        $requirement->expects($this->once())
            ->method('areThereAnyChanges')
            ->will($this->returnValue(true));

        $requirement->expects($this->any())
            ->method('createProductConfigurations')
            ->will($this->returnValue(array()));

        $requirement->expects($this->once())
            ->method('getAccountChangeManager')
            ->will($this->returnValue($accountChangeManager));

        $requirement->expects($this->once())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $requirement
            ->expects($this->any())
            ->method('getValDslConnectivityProduct')
            ->will($this->returnValue($mockDslConnectivityProduct));

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getResultAsArray')
        );

        $lineCheckResult->expects($this->any())
            ->method('getResultAsArray')
            ->will($this->returnValue(array('strSpName' => 'PLUSNET')));


        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar',
                'isApplicationStateVar'
            )
        );

        $applicationStateValueMap = array(
            array(get_class($requirement), 'objCoreService', $arrValidatedApplicationData['objCoreService']),
            array(get_class($requirement), 'intOldSdi', $oldSdi),
            array(get_class($requirement), 'intNewSdi', $newSdi),
            array(get_class($requirement), 'objUserActor', $actor),
            array(get_class($requirement), 'objBusinessActor', $mockBusActor),
            array(get_class($requirement), 'objLineCheckResult', $lineCheckResult),
            array(get_class($requirement), 'callerDisplay', $arrValidatedApplicationData['callerDisplay']),
        );

        $controller->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnValueMap($applicationStateValueMap));

        $requirement->setAppStateCallback($controller);

        $requirement
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $requirement
            ->expects($this->any())
            ->method('getScheduledChangeDate')
            ->will($this->returnValue('10/10/2009'));

        $result = $requirement->describe($arrValidatedApplicationData);
        if (isset($expectedResult['callerDisplay'])) {
            // Hacky bit to cover newly added caller display code
            $reflector = new ReflectionObject($requirement);
            $property = $reflector->getProperty('broadBandCallerDisplay');
            $property->setAccessible(true);
            $this->assertEquals($expectedResult['callerDisplay'], $property->getValue($requirement));
            unset($expectedResult['callerDisplay']);
        }

        $this->assertEquals($expectedResult, $result['fibreAppointments']);

    }

    /**
     * Provide data to test _getFttcAppointmentData returns proper values
     *
     * @return array
     */
    public function provideDataToTestGetFttcAppointmentDataReturnsProperValues()
    {
        $appointment = '15/07/2012AM';
        $appointmentDate1 = new DateTime('2012-07-15');
        $appointmentTime1 = 'AM';
        $appointmentDate2 = new DateTime('2012-07-15');
        $appointmentTime2 = 'PM';
        $appointmentDate3 = new DateTime('2012-07-16');
        $appointmentTime3 = 'AM';
        $coreService = $this->getMock(
            'Core_Service',
            array('getIsp', 'getServiceId', 'getNextInvoiceDate')
        );

        $coreService->expects($this->any())
            ->method('getIsp')
            ->will($this->returnValue('plusnet'));

        $coreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1812332));

        $coreService->expects($this->any())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue('2012-07-15'));

        $selectedBroadband = array();

        return array(
            //Live appointing has worked
            array(
                'arrValidatedApplicationData' => array(
                    'objCoreService'    => $coreService,
                    'selectedBroadband' => $selectedBroadband,
                    'arrWlrProduct'     => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array('Caller Display')
                    ),
                    'appointingType'    => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'       => $appointment,
                    'appointmentdate1'  => null,
                    'appointmenttime1'  => null,
                    'appointmentdate2'  => null,
                    'appointmenttime2'  => null,
                    'appointmentdate3'  => null,
                    'appointmenttime3'  => null,
                ),
                'expectedResult'              => array(
                    'hasInstallationAppointment'  => true,
                    'alternativeAppointmentDates' => false,
                    'appointmentDate'             => '15/07/2012',
                    'appointmentTime'             => 'AM',
                    'callerDisplay'               => 'REMOVE',
                )
            ),
            //Live appointing has not worked
            array(
                'arrValidatedApplicationData' => array(
                    'objCoreService'    => $coreService,
                    'selectedBroadband' => $selectedBroadband,
                    'arrWlrProduct'     => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array(),
                    ),
                    'appointingType'    => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'       => null,
                    'appointmentdate1'  => $appointmentDate1->getTimestamp(),
                    'appointmenttime1'  => $appointmentTime1,
                    'appointmentdate2'  => $appointmentDate2->getTimestamp(),
                    'appointmenttime2'  => $appointmentTime2,
                    'appointmentdate3'  => $appointmentDate3->getTimestamp(),
                    'appointmenttime3'  => $appointmentTime3,
                    'callerDisplay'     => true
                ),
                'expectedResult'              => array(
                    'hasInstallationAppointment'  => true,
                    'alternativeAppointmentDates' => true,
                    'appointmentDate1'            => '15/07/2012',
                    'appointmentTime1'            => 'AM',
                    'appointmentDate2'            => '15/07/2012',
                    'appointmentTime2'            => 'PM',
                    'appointmentDate3'            => '16/07/2012',
                    'appointmentTime3'            => 'AM',
                    'callerDisplay'               => 'ADD',
                )
            ),
            //Without any appointment dates
            array(
                'arrValidatedApplicationData' => array(
                    'objCoreService'    => $coreService,
                    'selectedBroadband' => $selectedBroadband,
                    'arrWlrProduct'     => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array()
                    ),
                    'appointingType'    => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'       => null,
                    'appointmentdate1'  => null,
                    'appointmenttime1'  => null,
                    'appointmentdate2'  => null,
                    'appointmenttime2'  => null,
                    'appointmentdate3'  => null,
                    'appointmenttime3'  => null,
                ),
                'expectedResult'              => array(
                    'hasInstallationAppointment' => false,
                )
            ),
        );
    }

    /**
     * Test for Get Fttc ProRata Estimate Via Describe
     *
     * @param array $arrValidatedApplicationData Data which is used by describe method call
     * @param array $arrData                     Current data processing with
     * @param array $arrTariffs                  Tariffs used by the calculation
     * @param array $arrProductConfigurations    Product configuration data
     * @param array $arrProrata                  Prorata test data
     * @param array $arrExpected                 Expected results for verification
     *
     * @covers       AccountChange_TermsAndConditions::describe
     * @covers       AccountChange_TermsAndConditions::getFibreProRataEstimateCosts
     *
     * @covers       AccountChange_Product_ServiceDefinition::getProRataEstimate
     * @covers       AccountChange_Product_ServiceDefinition::_getProRataBillingDays
     * @covers       AccountChange_Product_ServiceDefinition::_calculateProductCostPerDay
     * @covers       AccountChange_Product_ServiceDefinition::getTariffDetails
     *
     * @dataProvider provideDataForTestGetFttcProRataEstimateViaDescribe
     *
     * @return array
     */
    public function testGetFttcProRataEstimateViaDescribe(
        $arrValidatedApplicationData,
        $arrData,
        $arrTariffs,
        $arrProductConfigurations,
        $arrProrata,
        $arrExpected
    ) {
        $this->arrCurrentData = $arrData;
        $this->arrCurrentTariffs = $arrTariffs;

        $this->setUpMockValAdaptor();
        $this->setUpMockAuthAdaptor();

        // Create mock Val_DSLConnectivityProduct
        $objMockDslConnectivityProduct = $this->getMock(
            'Val_DSLConnectivityProduct',
            array(
                'setADSLProduct'
            ),
            array(1)
        );

        // Set the Db_Adaptor for the AccountChange module.
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor
            ->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will(
                $this->returnValue(
                    array(
                        'requires' => 'testreq',
                        'name'     => 'testName',
                        'isp'      => 'testIsp'
                    )
                )
            );

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objAccountConfiguration = $this->getMock(
            'AccountChange_AccountConfiguration',
            array('setTotalOneOffCharge'),
            array(array())
        );

        $objAccountChangeManager = $this->getMock(
            'AccountChange_Manager',
            array(),
            array(
                $arrValidatedApplicationData['objCoreService']->getServiceId(),
                $objAccountConfiguration,
                $objAccountConfiguration
            )
        );

        $objProductRules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService')
        );

        $objRequirement = $this->getMock(
            'AccountChange_TermsAndConditions',
            array(
                'areThereAnyChanges',
                'createProductConfigurations',
                'getAccountChangeManager',
                'hasOneOffCharges',
                'getValDslConnectivityProduct',
                'getTariffDetails',
                'getSelectedBroadband',
                'isOldProductFibre',
                'isNewProductFibre',
                'isDualPlay',
                'getServiceDefinitionVariants',
                'getMinAndMaxSpeedRanges',
                'getScheduledChangeDate'
            )
        );

        $contractDurationHelper = $this
            ->getMockBuilder('AccountChange_ContractDurationHelper')
            ->disableOriginalConstructor()
            ->setMethods(['shouldDisplayContractDuration'])
            ->getMock();

        $contractDurationHelper->method('shouldDisplayContractDuration')->willReturn(false);

        AccountChange_ServiceManager::setService('ContractDurationHelper', $contractDurationHelper);

        $objOldBroadband = $this->getProductServiceDefinitionMock();

        $objOldBroadband->expects($this->any())
            ->method('getProRataEstimate')
            ->will($this->returnValue($arrProrata));

        $arrProductConfigurations['objOldBroadband'] = $objOldBroadband;

        $objRequirement->expects($this->once())
            ->method('areThereAnyChanges')
            ->will($this->returnValue(true));

        $objRequirement->expects($this->any())
            ->method('createProductConfigurations')
            ->will($this->returnValue($arrProductConfigurations));

        $objRequirement->expects($this->once())
            ->method('getAccountChangeManager')
            ->will($this->returnValue($objAccountChangeManager));

        $objRequirement->expects($this->any())
            ->method('isOldProductFibre')
            ->will($this->returnValue($arrData['isFibreFrom']));

        $objRequirement->expects($this->any())
            ->method('isNewProductFibre')
            ->will($this->returnValue($arrData['isFibreTo']));

        $objRequirement
            ->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $objRequirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(array('maxDownstreamSpeed' => 45)));

        $objRequirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(false));

        $objController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objController
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    array(
                        $this,
                        'returnGetApplicationStateVar'
                    )
                )
            );

        $objController
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnValue(true));

        $objRequirement->setAppStateCallback($objController);

        $intOldTariffId = $arrTariffs['arrOldTariff']['intTariffID'];
        $intNewTariffId = $arrTariffs['arrNewTariff']['intTariffID'];

        $objRequirement
            ->expects($this->any())
            ->method('getTariffDetails')
            ->will($this->returnCallback(array($this, 'returnGetTariffDetails')));

        $objRequirement
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $objRequirement
            ->expects($this->any())
            ->method('getScheduledChangeDate')
            ->will($this->returnValue('10/10/2009'));

        $arrResult = $objRequirement->describe($arrValidatedApplicationData);

        if ($arrExpected !== null) {
            $this->assertEquals($arrExpected['hasFibre'], $arrResult['hasFibre']);
            $this->assertEquals($arrExpected['fibreInstallation'], $arrResult['fibreInstallation']);
            $this->assertEquals($arrExpected['fibreProRataCharge'], $arrResult['fibreProRataCharge']);
            $this->assertEquals($arrExpected['liveAppointing'], $arrResult['liveAppointing']);
        } else {
            $this->assertFalse(isset($arrExpected['hasFibre']));
            $this->assertFalse(isset($arrExpected['fibreInstallation']));
            $this->assertFalse(isset($arrExpected['fibreProRataCharge']));
            $this->assertFalse(isset($arrExpected['liveAppointing']));
        }
    }

    /**
     * Data provider for testGetFttcProRataEstimateViaDescribe
     *
     * @return array
     */
    public function provideDataForTestGetFttcProRataEstimateViaDescribe()
    {
        $intOldSdi = 6754;
        $intNewSdi = 6784;

        $objActor = $this->getMock(
            'Auth_BusinessActor',
            array()
        );

        $objLineCheckResult = $this->getMockBuilder(LineCheck_Result::class)
            ->disableOriginalConstructor()
            ->getMock();


        $dteAppointment = '22/07/2012AM';
        $dteAppointmentDate1 = new DateTime('2012-07-15');
        $dteAppointmentTime1 = 'AM';
        $dteAppointmentDate2 = new DateTime('2012-07-15');
        $dteAppointmentTime2 = 'PM';
        $dteAppointmentDate3 = new DateTime('2012-07-16');
        $dteAppointmentTime3 = 'AM';

        $objCoreService = $this->getMock(
            'Core_Service',
            array('getIsp', 'getServiceId', 'getNextInvoiceDate', 'getInvoicePeriod', 'getInvoiceDay')
        );

        $objCoreService->expects($this->any())
            ->method('getIsp')
            ->will($this->returnValue('plusnet'));

        $objCoreService
            ->expects($this->any())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue('2012-07-28'));

        $objCoreService
            ->expects($this->any())
            ->method('getInvoicePeriod')
            ->will($this->returnValue('monthly'));

        $objCoreService
            ->expects($this->any())
            ->method('getInvoiceDay')
            ->will($this->returnValue(28));

        $objCoreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(12345));

        $arrSelectedBroadband = array(
            'intNewCost'        => 123,
            'intNewLeadingCost' => 123,
        );

        $objNewBroadband = $this->getProductServiceDefinitionMock();

        $arrTariffs = array(
            'arrOldTariff' => $this->getTestTariffData(100, 1599),
            'arrNewTariff' => $this->getTestTariffData(101, 2999),
        );

        $mockBusActor = $this->getMockBuilder('Auth_BusinessActor')
            ->disableOriginalConstructor()
            ->setMethods(array('getUserType'))
            ->getMock();

        $mockBusActor->expects($this->any())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_STAFF'));

        return array(

            // Test 1 - live appointing was down
            array(
                array(
                    'objCoreService'       => $objCoreService,
                    'arrSelectedBroadband' => $arrSelectedBroadband,
                    'arrWlrProduct'        => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array()
                    ),
                    'appointingType'       => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'          => null,
                    'appointmentdate1'     => $dteAppointmentDate1->getTimestamp(),
                    'appointmenttime1'     => $dteAppointmentTime1,
                    'appointmentdate2'     => $dteAppointmentDate2->getTimestamp(),
                    'appointmenttime2'     => $dteAppointmentTime2,
                    'appointmentdate3'     => $dteAppointmentDate3->getTimestamp(),
                    'appointmenttime3'     => $dteAppointmentTime3,
                ),

                array(
                    'intOldSdi'          => $intOldSdi,
                    'intNewSdi'          => $intNewSdi,
                    'objCoreService'     => $objCoreService,
                    'bolSchedule'        => false,
                    'liveAppointing'     => 0,
                    'objUserActor'       => $objActor,
                    'objLineCheckResult' => $objLineCheckResult,
                    'objBusinessActor'   => $mockBusActor,
                    'isFibreFrom'        => false,
                    'isFibreTo'          => false,
                ),

                $arrTariffs,

                array(
                    'objNewBroadband' => $objNewBroadband,
                ),

                array(
                    'intFttcEstimateInPence' => 0
                ),

                array(
                    'hasFibre'           => null,
                    'fibreInstallation'  => null,
                    'fibreProRataCharge' => null,
                    'liveAppointing'     => null,
                )
            ),

            // Test 2 - live appointing was ok, but we were not account changing to Fibre
            array(
                array(
                    'objCoreService'       => $objCoreService,
                    'arrSelectedBroadband' => $arrSelectedBroadband,
                    'arrWlrProduct'        => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array()
                    ),
                    'appointingType'       => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'          => $dteAppointment,
                ),

                array(
                    'intOldSdi'          => $intOldSdi,
                    'intNewSdi'          => $intNewSdi,
                    'objCoreService'     => $objCoreService,
                    'bolSchedule'        => false,
                    'liveAppointing'     => 1,
                    'objUserActor'       => $objActor,
                    'objLineCheckResult' => $objLineCheckResult,
                    'objBusinessActor'   => $mockBusActor,
                    'isFibreFrom'        => false,
                    'isFibreTo'          => false,
                ),

                $arrTariffs,

                array(
                    'objNewBroadband' => $objNewBroadband,
                ),

                array(
                    'intFttcEstimateInPence' => 0
                ),

                array(
                    'hasFibre'           => null,
                    'fibreInstallation'  => null,
                    'fibreProRataCharge' => null,
                    'liveAppointing'     => true,
                )
            ),

            // Test 3 - live appointing was ok, we were changing to Fibre
            array(
                array(
                    'objCoreService'       => $objCoreService,
                    'arrSelectedBroadband' => $arrSelectedBroadband,
                    'arrWlrProduct'        => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array()
                    ),
                    'appointingType'       => array(
                        'serviceHandle' => 'FTTC'
                    ),
                ),

                array(
                    'intOldSdi'          => $intOldSdi,
                    'intNewSdi'          => $intNewSdi,
                    'objCoreService'     => $objCoreService,
                    'bolSchedule'        => false,
                    'liveAppointing'     => 1,
                    'objUserActor'       => $objActor,
                    'objLineCheckResult' => $objLineCheckResult,
                    'objBusinessActor'   => $mockBusActor,
                    'isFibreFrom'        => true,
                    'isFibreTo'          => true,
                    'appointment'        => $dteAppointment,
                    'arrSelectedBroadband'
                                         => array('intSelectedTariffID' => 101),
                    'arrAdslProductDetails'
                                         => array('intTariffID' => 100),
                ),

                $arrTariffs,

                array(
                    'objNewBroadband' => $objNewBroadband,
                ),

                array(
                    'intFttcEstimateInPence' => 280
                ),

                array(
                    'hasFibre'           => true,
                    'fibreInstallation'  => false,
                    'fibreProRataCharge' => '2.80',
                    'liveAppointing'     => 1,
                )
            ),

            // Test 4 - live appointing was ok, we were changing to Fibre to Fibre
            array(
                array(
                    'objCoreService'       => $objCoreService,
                    'arrSelectedBroadband' => $arrSelectedBroadband,
                    'arrWlrProduct'        => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array()
                    ),
                    'appointingType'       => array(
                        'serviceHandle' => 'FTTC'
                    ),
                ),

                array(
                    'intOldSdi'          => $intOldSdi,
                    'intNewSdi'          => $intNewSdi,
                    'objCoreService'     => $objCoreService,
                    'bolSchedule'        => false,
                    'liveAppointing'     => 1,
                    'objUserActor'       => $objActor,
                    'objLineCheckResult' => $objLineCheckResult,
                    'objBusinessActor'   => $mockBusActor,
                    'isFibreFrom'        => true,
                    'isFibreTo'          => true,
                    'appointment'        => $dteAppointment,
                    'arrSelectedBroadband'
                                         => array('intSelectedTariffID' => 101),
                    'arrAdslProductDetails'
                                         => array('intTariffID' => 100),
                ),

                $arrTariffs,

                array(
                    'objNewBroadband' => $objNewBroadband,
                ),

                array(
                    'intFttcEstimateInPence' => 280
                ),

                array(
                    'hasFibre'           => true,
                    'fibreInstallation'  => false,
                    'fibreProRataCharge' => '2.80',
                    'liveAppointing'     => 1,
                )
            ),

            // Test 5 - no products provided
            array(
                array(
                    'objCoreService'       => $objCoreService,
                    'arrSelectedBroadband' => $arrSelectedBroadband,
                    'arrWlrProduct'        => array(
                        'intOldWlrId' => 5663,
                        'activeCallFeatures' => array()
                    ),
                    'appointingType'       => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'          => $dteAppointment,
                ),

                array(
                    'intOldSdi'          => $intOldSdi,
                    'intNewSdi'          => $intNewSdi,
                    'objCoreService'     => $objCoreService,
                    'bolSchedule'        => false,
                    'liveAppointing'     => 0,
                    'objUserActor'       => $objActor,
                    'objLineCheckResult' => $objLineCheckResult,
                    'objBusinessActor'   => $mockBusActor,
                    'isFibreFrom'        => false,
                    'isFibreTo'          => false,
                ),

                $arrTariffs,

                array(
                    'objNewBroadband' => $objNewBroadband,
                ),

                array(
                    'intFttcEstimateInPence' => 0
                ),

                null
            ),
        );
    }

    /**
     * Creates a mock object for use in testing
     *
     * @return object
     */
    private function getProductServiceDefinitionMock()
    {
        $objMock = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProRataEstimate'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE),
            '',
            false
        );

        return $objMock;
    }

    /**
     * Call back function so that we can pass back different values for the test for getApplicationStateVariable
     *
     * @return array
     */
    public function returnGetApplicationStateVar()
    {
        $arrArgs = func_get_args();

        if (isset($this->arrCurrentData[$arrArgs[1]])) {
            return $this->arrCurrentData[$arrArgs[1]];
        } else {
            return;
        }
    }

    /**
     * Call back function so that we can pass back different values for the test for isApplicationStateVar
     *
     * @return bool
     */
    public function returnIsApplicationStateVar()
    {
        $arrArgs = func_get_args();

        if (isset($this->arrCurrentData[$arrArgs[1]])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Returns test tariff data
     *
     * @param int $intId          Id for the tariff data
     * @param int $intCostInPence Cost in Pence
     *
     * @return array
     */
    private function getTestTariffData($intId, $intCostInPence)
    {
        $arrTariffData = array(
            "intTariffID"                  => $intId,
            "intContractLengthID"          => 3,
            "strContractLengthHandle"      => 'MONTHLY',
            "strContractLengthDisplayName" => 'Monthly',
            "intPaymentFrequencyID"        => 3,
            "strPaymentFrequencyHandle"    => 'MONTHLY',
            "intQuantityFrom"              => 1,
            "intQuantityTo"                => 1,
            "intCostIncVatPence"           => $intCostInPence,
            "bolAutoRenew"                 => 1,
            "intNextTariffID"              => 522,
            "intNoticePeriodDays"          => 10,
            "uxtStart"                     => **********,
            "uxtEnd"                       => 0,
        );

        return $arrTariffData;
    }

    /**
     * Test for createWlrConfigurations when account change requested add phone
     *
     * @param array $arrData        Current data processing with
     * @param array $actionRequired broadband action required during account change
     * @param array $expectedData   Expected results for verification
     *
     * @medium
     * @covers       AccountChange_TermsAndConditions::createWlrConfigurations
     *
     * @dataProvider provideDataFortestCreateWlrConfigurationsWhenAddPhoneRequested
     *
     * @return array
     */
    public function testCreateWlrConfigurationsWhenAddPhoneRequestedSolusToDualPlay(
        $arrData,
        $actionRequired,
        $expectedData
    ) {
        $this->arrCurrentData = $arrData;
        $serviceId = 22;
        $internetConnectionComponentId = *********;

        $termsAndConditions = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_TermsAndConditions',
            array()
        );

        $termsAndConditions = $this->getMock(
            'AccountChange_TermsAndConditionsProxy',
            array(
                'getScheduleOverrideForWlrIfNeeded',
                'getIdentifyAction',
                'includeLegacyFiles',
                'getComponentIdForInternetConnection',
                'getMarketIdFromLineCheckResult',
                'getScidFromSdiAndMarket',
                'getServiceDefinitionVariants'
            )
        );

        if ($this->arrCurrentData['intOldSdi'] != $this->arrCurrentData['intNewSdi']) {

            $lineCheckDetails = array(
                'intLineCheckId'       => 33,
                'intServiceId'         => $serviceId,
                'intLineCheckStatusId' => 2
            );

            // Set the Db_Adaptor for the linecheck module.
            $mockDbAdaptor = $this->getMock(
                'Db_Adaptor',
                array('getPendingLineCheckResult'),
                array('linecheck', Db_Manager::DEFAULT_TRANSACTION, false)
            );
            $mockDbAdaptor->expects($this->any())
                ->method('getPendingLineCheckResult')
                ->will($this->returnValue($lineCheckDetails));

            Db_Manager::setAdaptor('linecheck', $mockDbAdaptor);

            $identifyAction = $this->getMock(
                'AccountChange_Action_IdentifyAction',
                array('getActionRequired'),
                array($serviceId)
            );

            $identifyAction->expects($this->never())
                ->method('getActionRequired');

            $termsAndConditions->expects($this->any())
                ->method('getIdentifyAction')
                ->will($this->returnValue($identifyAction));


            $termsAndConditions
                ->expects($this->any())
                ->method('getServiceDefinitionVariants')
                ->will(
                    $this->returnValue(
                        array($this->arrCurrentData['intOldSdi'], $this->arrCurrentData['intNewSdi'])
                    )
                );

            $expectedData['manuallyAddPhone'] = false;

        }

        $mockCoreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockCoreDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue(array('type' => 'business')));

        Db_Manager::setAdaptor('Core', $mockCoreDbAdaptor);

        $termsAndConditions
            ->expects($this->any())
            ->method('includeLegacyFiles');

        $termsAndConditions->expects($this->any())
            ->method('getScheduleOverrideForWlrIfNeeded')
            ->will($this->returnValue(false));

        $termsAndConditions
            ->expects($this->any())
            ->method('getComponentIdForInternetConnection')
            ->with(12345)
            ->will($this->returnValue($internetConnectionComponentId));

        $termsAndConditions
            ->expects($this->once())
            ->method('getMarketIdFromLineCheckResult')
            ->will($this->returnValue(2));

        $termsAndConditions
            ->expects($this->once())
            ->method('getScidFromSdiAndMarket')
            ->will($this->returnValue(6666));

        $mockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar', 'getApplicationStateVar'));

        $mockController
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $mockController
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnIsApplicationStateVar')));

        $termsAndConditions->setAppStateCallback($mockController);
        $result = array();
        $termsAndConditions->__call('protected_createWlrConfigurations', array(&$result));

        $this->assertAttributeEquals($expectedData['manuallyAddPhone'], 'manuallyAddPhone', $result['objNewWlr']);
        $this->assertAttributeEquals($expectedData['bolTakePayment'], 'bolTakePayment', $result['objNewWlr']);
        $this->assertAttributeEquals(
            $expectedData['bolEssentialProduct'],
            'bolEssentialProduct',
            $result['objNewWlr']
        );
        $this->assertAttributeEquals($expectedData['uxtScheduledDate'], 'uxtScheduledDate', $result['objNewWlr']);
        $this->assertAttributeEquals($expectedData['strCliNumber'], 'strCliNumber', $result['objNewWlr']);

    }

    /**
     * Test for createWlrConfigurations when account change requested add phone
     *
     * @param array $arrData        Current data processing with
     * @param array $actionRequired broadband action required during account change
     * @param array $expectedData   Expected results for verification
     *
     * @medium
     * @covers       AccountChange_TermsAndConditions::createWlrConfigurations
     *
     * @dataProvider provideDataFortestCreateWlrConfigurationsWhenAddPhoneRequested
     *
     * @return array
     */
    public function testCreateWlrConfigurationsWhenAddPhoneRequested(
        $arrData,
        $actionRequired,
        $expectedData
    ) {
        $this->arrCurrentData = $arrData;
        $serviceId = 22;
        $internetConnectionComponentId = *********;

        $termsAndConditions = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_TermsAndConditions',
            array()
        );

        $termsAndConditions = $this->getMock(
            'AccountChange_TermsAndConditionsProxy',
            array(
                'getScheduleOverrideForWlrIfNeeded',
                'getIdentifyAction',
                'includeLegacyFiles',
                'getComponentIdForInternetConnection',
                'getMarketIdFromLineCheckResult',
                'getScidFromSdiAndMarket',
                'getServiceDefinitionVariants'
            )
        );

        if ($this->arrCurrentData['intOldSdi'] != $this->arrCurrentData['intNewSdi']) {

            $lineCheckDetails = array(
                'intLineCheckId'       => 33,
                'intServiceId'         => $serviceId,
                'intLineCheckStatusId' => 2
            );

            // Set the Db_Adaptor for the linecheck module.
            $mockDbAdaptor = $this->getMock(
                'Db_Adaptor',
                array('getPendingLineCheckResult',
                    'getMaxLineCheckDetailsMGALS',
                    'getWbcLineCheckDetailsMGALS',
                    'getFttcLineCheckDetailsMGALS'),
                array('LineCheck', Db_Manager::DEFAULT_TRANSACTION, false)
            );
            $mockDbAdaptor->expects($this->any())
                ->method('getPendingLineCheckResult')
                ->will($this->returnValue($lineCheckDetails));

            $mockDbAdaptor->expects($this->any())
                ->method('getMaxLineCheckDetailsMGALS')
                ->will($this->returnValue(null));

            $mockDbAdaptor->expects($this->any())
                ->method('getWbcLineCheckDetailsMGALS')
                ->will($this->returnValue(null));

            $mockDbAdaptor->expects($this->any())
                ->method('getFttcLineCheckD etailsMGALS')
                ->will($this->returnValue(null));

            Db_Manager::setAdaptor('LineCheck', $mockDbAdaptor);

            $identifyAction = $this->getMock(
                'AccountChange_Action_IdentifyAction',
                array('getActionRequired'),
                array($serviceId)
            );

            $identifyAction->expects($this->once())
                ->method('getActionRequired')
                ->will($this->returnValue($actionRequired));

            $termsAndConditions->expects($this->any())
                ->method('getIdentifyAction')
                ->will($this->returnValue($identifyAction));

            $termsAndConditions
                ->expects($this->any())
                ->method('getServiceDefinitionVariants')
                ->will($this->returnValue(array()));

        }

        $mockCoreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockCoreDbAdaptor->expects($this->once())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue(array('type' => 'business')));

        Db_Manager::setAdaptor('Core', $mockCoreDbAdaptor);

        $termsAndConditions
            ->expects($this->any())
            ->method('includeLegacyFiles');

        $termsAndConditions->expects($this->any())
            ->method('getScheduleOverrideForWlrIfNeeded')
            ->will($this->returnValue(false));

        $termsAndConditions
            ->expects($this->any())
            ->method('getComponentIdForInternetConnection')
            ->with(12345)
            ->will($this->returnValue($internetConnectionComponentId));

        $termsAndConditions
            ->expects($this->once())
            ->method('getMarketIdFromLineCheckResult')
            ->will($this->returnValue(2));

        $termsAndConditions
            ->expects($this->once())
            ->method('getScidFromSdiAndMarket')
            ->will($this->returnValue(6666));

        $mockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar', 'getApplicationStateVar'));

        $mockController
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $mockController
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnIsApplicationStateVar')));

        $termsAndConditions->setAppStateCallback($mockController);
        $result = array();
        $termsAndConditions->__call('protected_createWlrConfigurations', array(&$result));

        $this->assertAttributeEquals($expectedData['manuallyAddPhone'], 'manuallyAddPhone', $result['objNewWlr']);
        $this->assertAttributeEquals($expectedData['bolTakePayment'], 'bolTakePayment', $result['objNewWlr']);
        $this->assertAttributeEquals(
            $expectedData['bolEssentialProduct'],
            'bolEssentialProduct',
            $result['objNewWlr']
        );
        $this->assertAttributeEquals($expectedData['uxtScheduledDate'], 'uxtScheduledDate', $result['objNewWlr']);
        $this->assertAttributeEquals($expectedData['strCliNumber'], 'strCliNumber', $result['objNewWlr']);

    }

    /**
     * Dataprovider for the function testCreateWlrConfigurationsWhenAddPhoneRequested
     *
     * @return array
     */
    public function provideDataFortestCreateWlrConfigurationsWhenAddPhoneRequested()
    {
        $this->markTestSkipped('This test needs to be updated to work with the latest version of line check (LineCheck dfd8eb works, 39f520 does not)');
        $intOldSdi = 6754;
        $intNewSdi = 6784;

        $objActor = $this->getMock(
            'Auth_BusinessActor',
            array()
        );

        $objCoreService = $this->getMock(
            'Core_Service',
            array('getIsp', 'getServiceId', 'getNextInvoiceDate', 'getCliNumber', 'getInvoiceDay')
        );

        $objCoreService->expects($this->any())
            ->method('getIsp')
            ->will($this->returnValue('plusnet'));

        $objCoreService
            ->expects($this->any())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue('2012-07-28'));

        $objCoreService
            ->expects($this->any())
            ->method('getCliNumber')
            ->will($this->returnValue('**********'));

        $objCoreService
            ->expects($this->any())
            ->method('getInvoiceDay')
            ->will($this->returnValue(28));

        $objCoreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(12345));

        $arrWlrProduct = array(
            'strProductName'    => '',
            'strCallPlanName'   => '',
            'intProductCost'    => '',
            'intLineRentCost'   => '',
            'intCallPlanCost'   => '',
            'intOldWlrId'       => '',
            'strContractLength' => '',
            'strContractHandle' => '',
            'uxtContractEnd'    => '',
            'intInstanceId'     => '',
            'arrExistingChange' => array(),
            'activeCallFeatures'=> array()
        );

        $lineCheckResult = new LineCheck_Result();
        $lineCheckResult->setExchangeCode(12);

        $arrSelectedBroadband = array(
            'strNewProduct'     => 'Value',
            'intNewCost'        => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20),
            'intNewLeadingCost' => ''
        );

        $applicationStateVars1 = array(
            'objCoreService'       => $objCoreService,
            'arrWlrProduct'        => $arrWlrProduct,
            'intOldSdi'            => $intOldSdi,
            'intNewSdi'            => $intNewSdi,
            'bolSchedule'          => true,
            'objBusinessActor'     => $objActor,
            'intNewWlrId'          => 670,
            'bolContractResetWlr'  => false,
            'objLineCheckResult'   => $lineCheckResult,
            'arrSelectedBroadband' => $arrSelectedBroadband,
            'productIsp'           => 'plus.net',
        );
        $applicationStateVars2 = $applicationStateVars1;
        $applicationStateVars2['intNewWlrId'] = 596;
        $applicationStateVars3 = $applicationStateVars1;
        $applicationStateVars3['strWlrContract'] = 'ANNUAL';
        $applicationStateVars4 = $applicationStateVars1;
        $applicationStateVars4['intNewSdi'] = $intOldSdi;


        return array(
            array(
                $applicationStateVars3,
                false,
                array(
                    'strCliNumber'        => '**********',
                    'bolTakePayment'      => false,
                    'bolEssentialProduct' => false,
                    'uxtScheduledDate'    => null,
                    'manuallyAddPhone'    => false
                )
            ),
            array(
                $applicationStateVars1,
                'Modify',
                array(
                    'strCliNumber'        => '**********',
                    'bolTakePayment'      => false,
                    'bolEssentialProduct' => false,
                    'uxtScheduledDate'    => null,
                    'manuallyAddPhone'    => true
                )
            ),
            array(
                $applicationStateVars1,
                'ProvidePFM',
                array(
                    'strCliNumber'        => '**********',
                    'bolTakePayment'      => false,
                    'bolEssentialProduct' => false,
                    'uxtScheduledDate'    => null,
                    'manuallyAddPhone'    => true
                )
            ),
            array(
                $applicationStateVars2,
                'ProvidePFM',
                array(
                    'strCliNumber'        => '**********',
                    'bolTakePayment'      => false,
                    'bolEssentialProduct' => true,
                    'uxtScheduledDate'    => null,
                    'manuallyAddPhone'    => true
                )
            ),
            array(
                $applicationStateVars2,
                false,
                array(
                    'strCliNumber'        => '**********',
                    'bolTakePayment'      => false,
                    'bolEssentialProduct' => true,
                    'uxtScheduledDate'    => null,
                    'manuallyAddPhone'    => false
                )
            ),
            array(
                $applicationStateVars4,
                'Modify',
                array(
                    'strCliNumber'        => '**********',
                    'bolTakePayment'      => false,
                    'bolEssentialProduct' => false,
                    'uxtScheduledDate'    => null,
                    'manuallyAddPhone'    => false
                )
            ),
        );
    }

    /**
     * Test for getIdentifyAction function
     *
     * @covers AccountChange_TermsAndConditions::getIdentifyAction
     *
     * @return void
     */
    public function testGetIdentifyAction()
    {
        $termsAndConditions = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_TermsAndConditions',
            array()
        );
        $serviceId = 45;
        $identifyAction = $termsAndConditions->protected_getIdentifyAction($serviceId);
        $this->assertAttributeEquals($serviceId, 'intServiceId', $identifyAction);

    }

    /**
     * Common code to set up a mock DB adaptor for the Val module
     *
     * @return void
     */
    private function setUpMockValAdaptor()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|Db_Adaptor $objMockValAdaptor */
        $objMockValAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getISPConfig',
                'getServiceComponentDetails',
                'getProductComponentConfig',
                'getServiceDefinition',
                'getServiceComponentConfigByProductID',
                'getADSLProduct',
                'getHasDeferredContract'
            ),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        // DB functions for Val_ISP
        $objMockValAdaptor->expects($this->any())
            ->method('getISPConfig')
            ->will(
                $this->returnValue(
                    array(
                        'intISPDefinitionID'             => 1,
                        'strFullName'                    => 'Bobs ISP',
                        'strSupportPhoneNumber'          => '0800 HELP ME',
                        'strPortalMainPageURL'           => 'http://portal.help.com',
                        'strDefaultCustomerEmailAddress' => '<EMAIL>'
                    )
                )
            );

        // Db functions for Val_ServiceComponentProduct (Val_HomePhoneProduct derives from it)
        $objMockValAdaptor
            ->expects($this->any())
            ->method('getServiceComponentDetails')
            ->will($this->returnValue(array('intServiceComponentProductID' => 5)));

        $objMockValAdaptor
            ->expects($this->any())
            ->method('getProductComponentConfig')
            ->will($this->returnValue(array('intProductComponentConfigID' => 6)));

        // DB functions for Val_ConnectivityProduct (Val_DSLConnectivityProduct derives from it)
        $objMockValAdaptor
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will(
                $this->returnValue(
                    array(
                        'intProductID'     => 2,
                        'strVariantHandle' => 'bob',
                        'strFamilyHandle'  => 'jom',
                        'strRequires'      => 'dsl',
                        'strType'          => 'residential'
                    )
                )
            );

        $objMockValAdaptor
            ->expects($this->any())
            ->method('getServiceComponentConfigByProductID')
            ->will($this->returnValue(array('service_component_config_id' => 3)));

        // DB functions for Val_DSLConnectivityProduct
        $objMockValAdaptor
            ->expects($this->any())
            ->method('getADSLProduct')
            ->will($this->returnValue(array('intADSLProductID' => 4)));

        $objMockValAdaptor
            ->expects($this->any())
            ->method('getHasDeferredContract')
            ->will($this->returnValue(array('bolHasDefferedContract' => false)));

        Db_Manager::setAdaptor('Val', $objMockValAdaptor);
    }

    /**
     * Common code to set up a mock DB adaptor for the Auth module
     *
     * @return void
     */
    private function setUpMockAuthAdaptor()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|Db_Adaptor $objAuthAdaptor */
        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getActorByExternalUserId', 'getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAuthAdaptor
            ->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(array('intActorId' => 1234)));

        $objAuthAdaptor
            ->expects($this->any())
            ->method('getBusinessActor')
            ->will(
                $this->returnValue(
                    array(
                        'intActorId'        => 1,
                        'strUsername'       => 'testuser',
                        'strRealm'          => 'plusnet',
                        'strUserType'       => '',
                        'strExternalUserId' => '12345'
                    )
                )
            );

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);
    }

    /**
     * Common code to set up a mock DB adator for the AccountChange module when running the describe() method
     *
     * @return void
     */
    private function setUpMockAccountChangeAdaptorForDescripe()
    {
        // Set the Db_Adaptor for the AccountChange module.
        /** @var PHPUnit_Framework_MockObject_MockObject|Db_Adaptor $objMockDbAdaptor */
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getComponentDetails', 'getServiceDefinitionDetails', 'isFibreProduct'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $objMockDbAdaptor
            ->expects($this->any())
            ->method('getComponentDetails')
            ->will($this->returnValue(array('component_type_id' => 515)));

        $objMockDbAdaptor
            ->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will(
                $this->returnValue(
                    array(
                        'requires' => 'testreq',
                        'name'     => 'testName',
                        'isp'      => 'testIsp'
                    )
                )
            );

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);
    }

    /**
     * Test whether recontracting is forced when changing to or from fibre without specifying a new contract duration
     *
     * @param boolean $oldProductFibre Is the old product fibre
     * @param boolean $newProductFibre Is the new product fibre
     * @param boolean $recontract      Should we recontract
     *
     * @return void
     *
     * @dataProvider dataProviderForceRecontract
     */
    public function testForceRecontract($oldProductFibre, $newProductFibre, $recontract)
    {
        $mockTermsAndConditionsText = 'These are the terms and conditions';

        $this->setUpMockValAdaptor();

        $this->setUpMockAuthAdaptor();

        $this->setUpMockAccountChangeAdaptorForDescripe();

        // Create mock Val_DSLConnectivityProduct
        $objMockDslConnectivityProduct = $this->getMock(
            'Val_DSLConnectivityProduct',
            array(
                'setADSLProduct'
            ),
            array(1)
        );

        $objMockAdaptor = new AccountChange_MockAdaptorHelper();
        $objAccountchangeManager = $objMockAdaptor->accountChangeManagerObjectProvider();

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_TermsAndConditions $objReq */
        $objReq = $this->getMock(
            'AccountChange_TermsAndConditions',
            array(
                'getApplicationStateVariable',
                'isApplicationStateVariable',
                'createBroadbandConfigurations',
                'createWlrConfigurations',
                'getAccountChangeManager',
                'getHardwareCharges',
                'raiseTicketToCsc',
                'getValDslConnectivityProduct',
                'getSelectedBroadband',
                'isDualPlay',
                'getServiceDefinitionVariants',
                'getSignupAppTermsAndConditionsText',
                'getMinAndMaxSpeedRanges',
                'getScheduledChangeDate',
                'isNewProductFibre',
                'isOldProductFibre',
                'getDefaultContractDuration'
            )
        );

        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_Controller $objMockController */
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $objMockController
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    array('AccountChange_MockAdaptorHelper', 'getDataForGetApplicationStateVariable')
                )
            );

        $contractDurationHelper = $this
            ->getMockBuilder('AccountChange_ContractDurationHelper')
            ->disableOriginalConstructor()
            ->setMethods(['shouldDisplayContractDuration', 'getContractDuration'])
            ->getMock();

        if ($recontract) {
            $contractDurationHelper->method('shouldDisplayContractDuration')->willReturn(true);
            $contractDurationHelper->expects($this->once())->method('getContractDuration')->willReturn(12);
        } else {
            $contractDurationHelper->method('shouldDisplayContractDuration')->willReturn(false);
            $contractDurationHelper->expects($this->never())->method('getContractDuration');
        }

        AccountChange_ServiceManager::setService('ContractDurationHelper', $contractDurationHelper);

        $objMockController
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnValue('false'));

        $objReq
            ->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $objReq
            ->expects($this->any())
            ->method('createBroadbandConfigurations')
            ->will($this->returnValue('true'));

        $objReq
            ->expects($this->any())
            ->method('createWlrConfigurations')
            ->will($this->returnValue('true'));

        $objReq
            ->expects($this->once())
            ->method('getAccountChangeManager')
            ->will($this->returnValue($objAccountchangeManager));

        $objReq
            ->expects($this->any())
            ->method('getHardwareCharges')
            ->will($this->returnValue(array()));

        $objReq
            ->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        $mockProductRules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService')
        );

        $mockProductRules
            ->expects($this->any())
            ->method('getProductProvisionForService')
            ->will(
                $this->returnValue(
                    array(
                        'intSupplierProductId'  => 19,
                        'intSupplierPlatformID' => 6,
                        'strProvisionOn'        => 'FTTC',
                    )
                )
            );

        $objReq
            ->expects($this->any())
            ->method('getValDslConnectivityProduct')
            ->will($this->returnValue($objMockDslConnectivityProduct));

        $objReq
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(array('maxDownstreamSpeed' => 45)));

        $objReq->setAppStateCallback($objMockController);

        $objReq->expects($this->once())
            ->method('getSignupAppTermsAndConditionsText')
            ->will($this->returnValue($mockTermsAndConditionsText));

        $arrValidatedApplicationData = $this->dataProviderFortestDescribe();

        $objReq
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $objReq->expects($this->any())
            ->method('getScheduledChangeDate')
            ->will($this->returnValue('10/10/2009'));

        // Code below here is specific to this test
        $objReq->expects($this->any())
            ->method('isOldProductFibre')
            ->willReturn($oldProductFibre);

        $objReq->expects($this->any())
            ->method('isNewProductFibre')
            ->willReturn($newProductFibre);

        $objReq->describe($arrValidatedApplicationData);
    }

    /**
     * Test whether the method getHardwareCharges uses C2M to get postage and packaging
     * given that a product ISP.
     *
     * @return void
     */
    public function testHardwareChargesUsesC2mToGetPostageAndPackaging()
    {
        $mockController = $this->getMock('AccountChange_Controller', array('getApplicationStateVar', 'isApplicationStateVar'));
        $mockFinancial = $this->getMock('Financial_AdditionalChargeHelper', array('getResidentialPostageAndPackagingFromC2m'));
        $termsController = new AccountChange_TermsAndConditions($mockFinancial);

        $mockController
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'campaignCode')
            ->will($this->returnValue(false));

        $mockController
            ->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'waivePostage')
            ->will($this->returnValue(false));

        $mockController
            ->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'intNewSdi')
            ->will($this->returnValue(6865));

        $mockController
            ->expects($this->at(3))
            ->method('isApplicationStateVar')
            ->with(get_class($termsController), 'hardwareOption')
            ->will($this->returnValue(true));

        $mockController
            ->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'hardwareOption')
            ->will($this->returnValue("Give me a router"));

        $mockController
            ->expects($this->at(5))
            ->method('isApplicationStateVar')
            ->with(get_class($termsController), 'bolIsBusiness')
            ->will($this->returnValue(true));

        $mockFinancial
            ->expects($this->any())
            ->method('getResidentialPostageAndPackagingFromC2m')
            ->will($this->returnValue(array('amount' => 6.99, 'exVatAmount' => 5.83)));

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockDbAdaptor
            ->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will(
                $this->returnValue(
                    array(
                        'requires' => 'testreq',
                        'name'     => 'testName',
                        'isp'      => 'plus.net'
                    )
                )
            );

        \Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        // Set callback object within the requirement itself to application controller
        $termsController->setAppStateCallback($mockController);
        $actual = $termsController->getHardwareCharges();

        $this->assertEquals('Postage and Packaging', $actual[0]['strOutstandingCharges']);
        $this->assertEquals('&pound;6.99', $actual[0]['intOutstandingFees']);
    }

    /**
     * Test whether the method getHardwareCharges uses C2M to get postage and packaging
     * given that a product ISP.
     *
     * @return void
     */
    public function testHardwareChargesUsesAdditionalChargeHelperToGetPostageAndPackaging()
    {
        $mockController = $this->getMock('AccountChange_Controller', array('getApplicationStateVar', 'isApplicationStateVar'));
        $mockCoreService = $this->getMock('Core_Service', array('getServiceId'));
        $financialAdditionalCharge = new Financial_AdditionalCharge("aHandle", "aDescrption", 699);
        $mockFinancialAdditionalChargeHelper = $this->getMock('Financial_AdditionalChargeHelper', array('getAdditionalCharge'));
        $termsController = new AccountChange_TermsAndConditions($mockFinancialAdditionalChargeHelper);

        $mockController
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'campaignCode')
            ->will($this->returnValue(false));

        $mockController
            ->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'waivePostage')
            ->will($this->returnValue(false));

        $mockController
            ->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'intNewSdi')
            ->will($this->returnValue(6865));

        $mockController
            ->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'hardwareOption')
            ->will($this->returnValue("Give me a router"));

        $mockController
            ->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'objCoreService')
            ->will($this->returnValue($mockCoreService));

        $mockController
            ->expects($this->at(3))
            ->method('isApplicationStateVar')
            ->with(get_class($termsController), 'hardwareOption')
            ->will($this->returnValue(true));

        $mockCoreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(6865));

        $mockFinancialAdditionalChargeHelper
            ->expects($this->any())
            ->method('getAdditionalCharge')
            ->will($this->returnValue($financialAdditionalCharge));

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'freePartnerHardwarePostage'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockDbAdaptor
            ->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will(
                $this->returnValue(
                    array(
                        'requires' => 'testreq',
                        'name'     => 'testName',
                        'isp'      => 'not.plus.net'
                    )
                )
            );

        $mockDbAdaptor
            ->expects($this->any())
            ->method('freePartnerHardwarePostage')
            ->will($this->returnValue(false));

        \Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        // Set callback object within the requirement itself to application controller
        $termsController->setAppStateCallback($mockController);
        $actual = $termsController->getHardwareCharges();

        $this->assertEquals('Postage and Packaging', $actual[0]['strOutstandingCharges']);
        $this->assertEquals('&pound;6.99', $actual[0]['intOutstandingFees']);
    }

    /**
     * Data provider for testing forced recontracting
     *
     * @return array
     */
    public function dataProviderForceRecontract()
    {
        return array(
            array(false, false, false),
            array(false, true,  true),
            array(true,  false, true),
            array(true,  true,  false),
        );
    }
    /**
     * The method to off the new billing engine.
     * Rather than mocking for isBillingToday in Core_service, new billing engine is off
     *
     * @return void
     */
    private function turnOffNewBillingEngine()
    {
        $dbFeatureSwitchMock = $this->getMockBuilder('\Db_Adaptor')
            ->setMethods(
                array(
                    'getFeatureToggleByName',
                    'isServiceIdExcludedFromFeatureSwitch',
                    'getUsernameFromServiceId',
                    'isUsernameExcludedFromFeatureSwitch'
                )
            )
            ->setConstructorArgs(array('FeatureSwitch', \Db_Manager::DEFAULT_TRANSACTION, true))
            ->getMock();

        $dbFeatureSwitchMock->expects($this->any())
            ->method('getFeatureToggleByName')
            ->will(
                $this->returnValue(
                    array(
                        'onDateTime'  => '2010-01-01',
                        'offDateTime' => '2010-01-01'
                    )
                )
            );

        $dbFeatureSwitchMock->expects($this->any())
            ->method('isServiceIdExcludedFromFeatureSwitch')
            ->will($this->returnValue(false));

        $dbFeatureSwitchMock->expects($this->any())
            ->method('getUsernameFromServiceId')
            ->will($this->returnValue(false));

        $dbFeatureSwitchMock->expects($this->any())
            ->method('isUsernameExcludedFromFeatureSwitch')
            ->will($this->returnValue(false));

        \Db_Manager::setAdaptor('FeatureSwitch', $dbFeatureSwitchMock);
    }

    /**
     * Tests that handleC2mPromotionPostJourneyValidation returns true where there is a c2m promo code set
     * and the validators all pass.
     *
     * @return void
     */
    public function testHandleC2mPromotionPostJourneyValidationReturnsTrueIfThereIsAPromoAndItValidatesOK()
    {
        $serviceId = 1234;
        $isp = $this->getMockBuilder('Val_ISP')
            ->setMethods(array('getISP'))
            ->disableOriginalConstructor()->getMock();

        $newSdi = 6755;
        $validatedApplicationData = array();
        $chosenContractDuration = 12;
        $impressionOfferId = 333;

        $mockContractDurationHelper = Mockery::mock('AccountChange_ContractDurationHelper');
        $mockContractDurationHelper->makePartial();
        $mockContractDurationHelper->shouldAllowMockingProtectedMethods();
        $mockContractDurationHelper->shouldReceive('getContractDuration')->andReturn($chosenContractDuration);

        $mockBusActor = $this->getMockBuilder('Auth_BusinessActor')
            ->disableOriginalConstructor()
            ->setMethods(array('getUserType'))
            ->getMock();

        $mockValidationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($mockBusActor)
        );

        $mockValidationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $mockController = $this->getMock('AccountChange_Controller', array('getApplicationStateVar', 'isApplicationStateVar'));

        $termsController = Mockery::mock('AccountChange_TermsAndConditions');
        $termsController->makePartial();
        $termsController->shouldAllowMockingProtectedMethods();
        $termsController->shouldReceive('getContractDurationHelper')->andReturn($mockContractDurationHelper);
        $termsController->shouldReceive('getImpressionOfferId')->andReturn($impressionOfferId);
        $termsController->shouldReceive('getValidationCheck')->andReturn($mockValidationCheck);

        $c2mPromotion = Mockery::mock('Plusnet\C2mApiClient\Entity\Promotion');
        $c2mPromotion->makePartial();
        $c2mPromotion->shouldAllowMockingProtectedMethods();
        $c2mPromotion->shouldReceive('getPromotionCode')->andReturn('testPromoCode');
        $c2mPromotion->shouldReceive('isPromoCodeC2mPromoCode')->andReturn(true);

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'promoCode')
            ->will($this->returnValue($c2mPromotion));

        // Set callback object within the requirement itself to application controller
        $termsController->setAppStateCallback($mockController);
        $actual = $termsController->handleC2mPromotionPostJourneyValidation($serviceId, $isp, $newSdi, $validatedApplicationData);
        $this->assertTrue($actual);

    }

    /**
     * Tests that handleC2mPromotionPostJourneyValidation returns true where there is no c2m promo code set.
     *
     * @return void
     */
    public function testHandleC2mPromotionPostJourneyValidationReturnsTrueIfThereIsNoPromoSet()
    {
        $serviceId = 1234;
        $isp = $this->getMockBuilder('Val_ISP')
            ->setMethods(array('getISP'))
            ->disableOriginalConstructor()->getMock();

        $newSdi = 6755;
        $validatedApplicationData = array();
        $chosenContractDuration = 12;
        $impressionOfferId = 333;

        $mockContractDurationHelper = Mockery::mock('AccountChange_ContractDurationHelper');
        $mockContractDurationHelper->makePartial();
        $mockContractDurationHelper->shouldAllowMockingProtectedMethods();
        $mockContractDurationHelper->shouldReceive('getContractDuration')->andReturn($chosenContractDuration);

        $mockBusActor = $this->getMockBuilder('Auth_BusinessActor')
            ->disableOriginalConstructor()
            ->setMethods(array('getUserType'))
            ->getMock();

        $mockValidationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($mockBusActor)
        );

        $mockValidationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $mockController = $this->getMock('AccountChange_Controller', array('getApplicationStateVar', 'isApplicationStateVar'));

        $termsController = Mockery::mock('AccountChange_TermsAndConditions');
        $termsController->makePartial();
        $termsController->shouldAllowMockingProtectedMethods();
        $termsController->shouldReceive('getContractDurationHelper')->andReturn($mockContractDurationHelper);
        $termsController->shouldReceive('getImpressionOfferId')->andReturn($impressionOfferId);
        $termsController->shouldReceive('getValidationCheck')->andReturn($mockValidationCheck);

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'promoCode')
            ->will($this->returnValue(null));

        // Set callback object within the requirement itself to application controller
        $termsController->setAppStateCallback($mockController);
        $actual = $termsController->handleC2mPromotionPostJourneyValidation($serviceId, $isp, $newSdi, $validatedApplicationData);
        $this->assertTrue($actual);
    }

    /**
     * Tests that handleC2mPromotionPostJourneyValidation returns true where there is no c2m promo code set.
     *
     * @return void
     */
    public function testHandleC2mPromotionPostJourneyValidationReturnsTrueIfC2mPromoCodeIsString()
    {
        $serviceId = 1234;
        $isp = $this->getMockBuilder('Val_ISP')
            ->setMethods(array('getISP'))
            ->disableOriginalConstructor()->getMock();

        $newSdi = 6755;
        $validatedApplicationData = array();
        $chosenContractDuration = 12;
        $impressionOfferId = 333;

        $mockContractDurationHelper = Mockery::mock('AccountChange_ContractDurationHelper');
        $mockContractDurationHelper->makePartial();
        $mockContractDurationHelper->shouldAllowMockingProtectedMethods();
        $mockContractDurationHelper->shouldReceive('getContractDuration')->andReturn($chosenContractDuration);

        $mockBusActor = $this->getMockBuilder('Auth_BusinessActor')
            ->disableOriginalConstructor()
            ->setMethods(array('getUserType'))
            ->getMock();

        $mockValidationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($mockBusActor)
        );

        $mockValidationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $mockController = $this->getMock('AccountChange_Controller', array('getApplicationStateVar', 'isApplicationStateVar'));

        $termsController = Mockery::mock('AccountChange_TermsAndConditions');
        $termsController->makePartial();
        $termsController->shouldAllowMockingProtectedMethods();
        $termsController->shouldReceive('getContractDurationHelper')->andReturn($mockContractDurationHelper);
        $termsController->shouldReceive('getImpressionOfferId')->andReturn($impressionOfferId);
        $termsController->shouldReceive('getValidationCheck')->andReturn($mockValidationCheck);

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'promoCode')
            ->will($this->returnValue('StringPromoCode'));

        // Set callback object within the requirement itself to application controller
        $termsController->setAppStateCallback($mockController);
        $actual = $termsController->handleC2mPromotionPostJourneyValidation($serviceId, $isp, $newSdi, $validatedApplicationData);
        $this->assertTrue($actual);
    }


    /**
     * Tests that handleC2mPromotionPostJourneyValidation throws expected exception where there is a c2m promo code set
     * but that promo code fails validation
     *
     * @expectedException AccountChange_InvalidAccountChangeOrderException
     *
     * @return void
     */
    public function testHandleC2mPromotionPostJourneyValidationThrowsExceptionWhenValidationFails()
    {
        $serviceId = 1234;
        $isp = $this->getMockBuilder('Val_ISP')
            ->setMethods(array('getISP'))
            ->disableOriginalConstructor()->getMock();

        $newSdi = 6755;
        $validatedApplicationData = array();
        $chosenContractDuration = 12;
        $impressionOfferId = 333;

        $reasonForBlockage = 'Not allowable';

        $mockContractDurationHelper = Mockery::mock('AccountChange_ContractDurationHelper');
        $mockContractDurationHelper->makePartial();
        $mockContractDurationHelper->shouldAllowMockingProtectedMethods();
        $mockContractDurationHelper->shouldReceive('getContractDuration')->andReturn($chosenContractDuration);

        $mockBusActor = $this->getMockBuilder('Auth_BusinessActor')
            ->disableOriginalConstructor()
            ->setMethods(array('getUserType'))
            ->getMock();

        $mockValidationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($mockBusActor)
        );

        $mockValidationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(false));

        $mockValidationCheck->expects($this->any())
            ->method('getReasonForBlockage')
            ->will($this->returnValue($reasonForBlockage));

        $mockController = $this->getMock('AccountChange_Controller', array('getApplicationStateVar', 'isApplicationStateVar'));

        $termsController = Mockery::mock('AccountChange_TermsAndConditions');
        $termsController->makePartial();
        $termsController->shouldAllowMockingProtectedMethods();
        $termsController->shouldReceive('getContractDurationHelper')->andReturn($mockContractDurationHelper);
        $termsController->shouldReceive('getImpressionOfferId')->andReturn($impressionOfferId);
        $termsController->shouldReceive('getValidationCheck')->andReturn($mockValidationCheck);

        $c2mPromotion = Mockery::mock('Plusnet\C2mApiClient\Entity\Promotion');
        $c2mPromotion->makePartial();
        $c2mPromotion->shouldAllowMockingProtectedMethods();
        $c2mPromotion->shouldReceive('getPromotionCode')->andReturn('testPromoCode');
        $c2mPromotion->shouldReceive('isPromoCodeC2mPromoCode')->andReturn(true);

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with(get_class($termsController), 'promoCode')
            ->will($this->returnValue($c2mPromotion));

        $this->setExpectedException(
            'AccountChange_InvalidAccountChangeOrderException',
            'Not allowable'
        );

        // Set callback object within the requirement itself to application controller
        $termsController->setAppStateCallback($mockController);
        $actual = $termsController->handleC2mPromotionPostJourneyValidation($serviceId, $isp, $newSdi, $validatedApplicationData);
        $this->assertTrue($actual);

    }
    /**
     * @return void
     * @throws ReflectionException
     */
    public function testSetPostageAndPackagingCostsWorksWithMultipleCharges()
    {
        $classInstance = new AccountChange_TermsAndConditions();

        $reflection = new \ReflectionClass(get_class($classInstance));
        $method = $reflection->getMethod('setPostageAndPackagingCosts');
        $method->setAccessible(true);

        $viewAttributes = [
        AccountChange_TermsAndConditions::ONE_OFF_CHARGES_KEY => [
        [
        AccountChange_TermsAndConditions::OUTSTANDING_CHARGES_KEY => 'Not Postage and Packaging',
        ],
        [
        AccountChange_TermsAndConditions::OUTSTANDING_CHARGES_KEY => 'Not Postage and Packaging',
        ],
        [
        AccountChange_TermsAndConditions::OUTSTANDING_CHARGES_KEY => AccountChange_TermsAndConditions::POSTAGE_AND_PACKAGING,
        AccountChange_TermsAndConditions::OUTSTANDING_FEES_KEY => new I18n_Currency('gbp', 1.99),
        AccountChange_TermsAndConditions::OUTSTANDING_FEES_EX_VAT_KEY => new I18n_Currency('gbp', 2.99),
        ]
        ]
        ];

        $return = $method->invokeArgs($classInstance, [$viewAttributes]);

        $this->assertEquals($return['hardwareCost'], 1.99);
        $this->assertEquals($return['exVatHardwareCost'], 2.99);
    }

}
