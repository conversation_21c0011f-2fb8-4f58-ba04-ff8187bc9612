<?php
/**
 * AccountChange Linecheck View Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-14
 */
/**
 * AccountChange Linecheck View Test
 *
 * Test class for AccountChange_LinecheckerView
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_LinecheckerView_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that the view returns correct template name
     *
     * @covers AccountChange_LinecheckerView::processInput
     *
     * @return void
     */
    public function testProcessInputReturnsLineChecSuccessTemplateWhenLineCheckIsPresent()
    {
        $input = array('objLinecheckResult' => 'value', 'LineCheckPhoneNumber' => 'value');
        $view = $this->getMock('AccountChange_LinecheckerView', array('getSmartyErrors'), array(), '', false);

        $view->expects($this->once())
            ->method('getSmartyErrors');

        $output = $view->testProcessInput($this, $input);
        $this->assertEquals('linecheck_success_cli', $output['strSectionToShow']);
    }

    /**
     * Test that the process input function returns correct template for line check result
     *
     * @param string $template Template
     * @param array  $errors   Errors
     *
     * @dataProvider provideData
     * @covers AccountChange_LinecheckerView::processInput
     *
     * @return void
     */
    public function testProcessInputReturnsCorrectTemplateDependingOnLineCheckResult($template, $errors)
    {
        $input = array();
        $view = $this->getMock('AccountChange_LinecheckerView', array('getSmartyErrors'), array(), '', false);

        $view->expects($this->once())
            ->method('getSmartyErrors')
            ->will($this->returnValue($errors));

        $output = $view->testProcessInput($this, $input);
        $this->assertEquals($template, $output['strSectionToShow']);
    }

    /**
     * Data Provider for templates and line check results
     *
     * @return array
     */
    public static function provideData()
    {
        return array(
            array('no_linecheck_results_from_bt', array('intPhoneNumber' => array('LINE_CHECK_FAILED_BT' => 'value'))),
            array('linecheck_failed_pn_problem', array('intPhoneNumber' => array('LINE_CHECK_FAILED_PN' => 'value'))),
            array('cli_check', array('intPhoneNumber' => array('LINE_CHECK_INVALID_TELNO' => 'value'))),
            array('cli_check', array('intPhoneNumber' => array('MISSING' => 'value'))),
            array(
                'linecheck_failed_pn_problem', 
                array('intPhoneNumber' => array('LINE_CHECK_TELNO_NOT_FOUND' => 'value'))
            ),
            array('cli_check', array()),
        );
    }
}
