<?php
/**
 * Account Change Line Rental Saver Action Test
 *
 * @category  AccountChnage_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-10-31
 */
/**
 * Account Change Line Rental Saver action test
 *
 * Testing class for AccountChange_Action_LineRentalSaver
 *
 * @category  AccountChange_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-10-31
 *
 */
class AccountChange_Action_LineRentalSaverTest extends PHPUnit_Framework_TestCase
{

    /**
     * Tests that createLrsInstance attempts to get an lrs instance
     *
     * @covers AccountChange_Action_LineRentalSaver::createLrsInstance
     *
     * @return void
     **/
    public function testCreateLrsInstance()
    {

        $serviceId = 1234;

        $mockLineRentalSaver = $this->getMock(
            'PrepaidContract_Instance',
            array(), array($serviceId)
        );

        $mockAction = $this->getMock(
            'AccountChange_Action_LineRentalSaver',
            array(
                'getServiceId',
                'getLineRentalSaver',
                'setLrsInstance'
            ),
            array($serviceId)
        );

        $mockAction
            ->expects($this->once())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        $mockAction
            ->expects($this->once())
            ->method('getLineRentalSaver')
            ->will($this->returnValue($mockLineRentalSaver));

        $mockAction
            ->expects($this->once())
            ->method('setLrsInstance')
            ->with($mockLineRentalSaver);

        $mockAction->createLrsInstance();
    }

    /**
     * Tests that setLrsInstance sets the instance and getLrsInstance
     * returns it again.
     *
     * @covers AccountChange_Action_LineRentalSaver::getLrsInstance
     * @covers AccountChange_Action_LineRentalSaver::setLrsInstance
     *
     * @return void
     **/
    public function testGettersAndSetters()
    {

        $serviceId = 1234;

        $mockLineRentalSaver = $this->getMock(
            'PrepaidContract_Instance',
            array(), array($serviceId)
        );

        $act = new AccountChange_Action_LineRentalSaver($serviceId);
        $act->setLrsInstance($mockLineRentalSaver);

        $lrs = $act->getLrsInstance();

        $this->assertEquals($mockLineRentalSaver, $lrs);

    }


    /**
     * Tests getEndDateOfActiveLrs returns a date
     *
     * @covers AccountChange_Action_LineRentalSaver::getEndDateOfActiveLrs
     *
     * @return void
     **/
    public function testGetEndDateOfActiveLrs()
    {

        $date = I18n_Date::fromString('2011-01-04');

        $mockLineRentalSaver = $this->getMock(
            'PrepaidContract_Instance',
            array('hasActiveContract', 'getContractExpiryDate'),
            array(********)
        );

        $mockLineRentalSaver
            ->expects($this->once())
            ->method('hasActiveContract')
            ->will($this->returnValue(true));

        $mockLineRentalSaver
            ->expects($this->once())
            ->method('getContractExpiryDate')
            ->will($this->returnValue($date));

        $mockAction = $this->getMock(
            'AccountChange_Action_LineRentalSaver',
            array(
                'getLrsInstance',
            ),
            array(12345)
        );

        $mockAction
            ->expects($this->once())
            ->method('getLrsInstance')
            ->will($this->returnValue($mockLineRentalSaver));

        $res = $mockAction->getEndDateOfActiveLrs();

        $this->assertEquals($date, $res);
    }

    /**
     * Tests getEndDateOfActiveLrs returns false if there's no active
     * contract
     *
     * @covers AccountChange_Action_LineRentalSaver::getEndDateOfActiveLrs
     *
     * @return void
     **/
    public function testGetEndDateOfActiveLrsErrorState()
    {

        $mockLineRentalSaver = $this->getMock(
            'PrepaidContract_Instance',
            array('hasActiveContract', 'getContractExpiryDate'),
            array(********)
        );

        $mockLineRentalSaver
            ->expects($this->once())
            ->method('hasActiveContract')
            ->will($this->returnValue(false));

        $mockLineRentalSaver
            ->expects($this->never())
            ->method('getContractExpiryDate');

        $mockAction = $this->getMock(
            'AccountChange_Action_LineRentalSaver',
            array(
                'getLrsInstance',
            ),
            array(12345)
        );
        $mockAction
            ->expects($this->once())
            ->method('getLrsInstance')
            ->will($this->returnValue($mockLineRentalSaver));

        $res = $mockAction->getEndDateOfActiveLrs();

        $this->assertEquals(false, $res);
    }
}
