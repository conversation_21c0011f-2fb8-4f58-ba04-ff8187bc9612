<?php
/**
 * Cbc action
 *
 * Action that performs all cbc related changes
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_Cbc.class.php,v 1.3 2009-01-29 18:32:01 kp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Exp $
 * @since     File available since 2008-09-02
 */
/**
 * Cbc action class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Action_Cbc extends AccountChange_Action
{
    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $this->cancelDataTransferWatch();
    }

    /**
     * Cancel the data transfer watch
     *
     * @return void
     */
    public function cancelDataTransferWatch()
    {
        // Ignoring this in the coverage reports due to the payment-limit-access file
        // wanting to connect to the database all of its own accord!
        $this->includeLegacyFiles();
        CancelDTWUponAccountTypeChange($this->intServiceId);
    }

    /**
     * Include all the legacy files we need, so we can also mock this for unit tests
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/cbc-access.inc';
        require_once '/local/data/mis/database/database_libraries/payment-limit-access.inc';
    }
}
