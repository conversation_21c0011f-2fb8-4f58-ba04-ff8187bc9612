<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_WaivePostageHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @dataProvider dataForTestShouldShowWaivePostageCheckbox
     * @param boolean $isHousemove    is housemove flag
     * @param boolean $isRecontract   is recontract flag
     * @param String  $routerHandle   router handle
     * @param boolean $isWorkplace    is workplace flag
     * @param boolean $expectedResult expected result
     * @return void
     */
    public function testShouldShowWaivePostageCheckbox(
        $isHousemove,
        $isRecontract,
        $routerHandle,
        $isWorkplace,
        $expectedResult
    ) {
        $waivePostageHelper = new AccountChange_WaivePostageHelper(
            $isHousemove,
            $isRecontract,
            $routerHandle,
            $isWorkplace
        );

        $this->assertEquals($expectedResult, $waivePostageHelper->shouldShowWaivePostageCheckbox());
    }

    /**
     * @return array[]
     */
    public function dataForTestShouldShowWaivePostageCheckbox()
    {
        return [
            'NotHubOne' => [
                'isHousemove' => false,
                'isRecontract' => false,
                'routerHandle' => new String('NOT_HUB_ONE'),
                'isWorkplace' => true,
                'expectedResult' => false,
            ],
            'AccountChangeForPlusnetResWithHubOne' => [
                'isHousemove' => false,
                'isRecontract' => false,
                'routerHandle' => new String('PN_HUB_ONE'),
                'isWorkplace' => true,
                'expectedResult' => true,
            ],
            'AccountChangeForBizWithHubOne' => [
                'isHousemove' => false,
                'isRecontract' => false,
                'routerHandle' => new String('PN_HUB_ONE_BUSINESS'),
                'isWorkplace' => true,
                'expectedResult' => false,
            ],
            'HousemoveWithoutRecontractWithHubOne' => [
                'isHousemove' => true,
                'isRecontract' => false,
                'routerHandle' => new String('PN_HUB_ONE'),
                'isWorkplace' => true,
                'expectedResult' => false,
            ],
            'HousemoveWithRecontractWithHubOne' => [
                'isHousemove' => true,
                'isRecontract' => true,
                'routerHandle' => new String('PN_HUB_ONE'),
                'isWorkplace' => true,
                'expectedResult' => true,
            ],
            'BizPortalAccountChange' => [
                'isHousemove' => false,
                'isRecontract' => false,
                'routerHandle' => new String('PN_HUB_ONE'),
                'isWorkplace' => false,
                'expectedResult' => false,
            ],
        ];
    }
}
