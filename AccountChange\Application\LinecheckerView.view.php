<?php
/**
 * Line Checker View
 *
 * The Line Checker View
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @since     File available since 2008-11-12
 */
/**
 * AccountChange_LinecheckerView class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_LinecheckerView extends Mvc_View
{
    /**
     * Process Input ready for the view
     *
     * @param array $arrInput Data sent to the template
     *
     * @return array
     */
    protected function processInput(array $arrInput)
    {
        $arrErrors = $this->getSmartyErrors();

        $strSectionToShow = 'cli_check';

        if (isset($arrInput['objLinecheckResult']) && isset($arrInput['LineCheckPhoneNumber'])) {
            $arrInput['strSectionToShow'] = 'linecheck_success_cli';
            return $arrInput;
        }

        if (isset($arrErrors['intPhoneNumber']['LINE_CHECK_FAILED_BT'])) {
            $arrInput['strSectionToShow'] = 'no_linecheck_results_from_bt';
            return $arrInput;
        }

        if (isset($arrErrors['intPhoneNumber']['LINE_CHECK_FAILED_PN'])) {
            $arrInput['strSectionToShow'] = 'linecheck_failed_pn_problem';
            return $arrInput;
        }

        if (isset($arrErrors['intPhoneNumber']['LINE_CHECK_INVALID_TELNO'])) {
            $arrInput['strSectionToShow'] = 'cli_check';
            return $arrInput;
        }

        if (isset($arrErrors['intPhoneNumber']['MISSING'])) {
            $arrInput['strSectionToShow'] = 'cli_check';
            return $arrInput;
        }

        if (isset($arrErrors['intPhoneNumber']['LINE_CHECK_TELNO_NOT_FOUND'])) {
            $arrInput['strSectionToShow'] = 'linecheck_failed_pn_problem';
            return $arrInput;
        }

        $arrInput['strSectionToShow'] = $strSectionToShow;

        return $arrInput;
    }

    /**
     * Return the errors
     *
     * @return array
     */
    protected function getSmartyErrors()
    {
        return $this->objSmartyErrors();
    }
}
