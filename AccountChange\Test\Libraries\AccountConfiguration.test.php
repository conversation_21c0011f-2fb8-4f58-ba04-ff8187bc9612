<?php
/**
 * Account Configuration
 *
 * Testing class for the AccountChange_AccountConfiguration
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: AccountConfiguration.test.php,v 1.4 2009-02-26 16:35:17 smarek Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Account Configuration Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_AccountConfiguration_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for mocking the database
     *
     * @var Db_Adaptor
     */
    private $objMockDbAdaptor;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        // Mock Db_Adaptor
        $this->objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('isServiceComponentAllowedOnServiceDefinition'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $this->objMockDbAdaptor->expects($this->any())
                               ->method('isServiceComponentAllowedOnServiceDefinition')
                               ->will($this->returnValue(1));

        Db_Manager::setAdaptor('AccountChange', $this->objMockDbAdaptor);

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);
    }

    /**
     * PHPUnit tearDown function
     *
     */
    public function tearDown()
    {
        Db_Manager::commit();
        Db_Manager::restoreAdaptor('AccountChange');
        Db_Manager::restoreAdaptor('Core');
    }

    /**
     * @covers AccountChange_AccountConfiguration
     * @covers AccountChange_AccountConfigurationException
     * @covers AccountChange_AccountConfiguration::validateAccountConfiguration
     *
     * @expectedException AccountChange_AccountConfigurationException
     */
    public function testValidateAccountConfigurationThrowsExceptionIfTheConfigurationDoesNotStoreProductConfigurations()
    {
        $arrProductConfigurations = array(array(1,2,3), array(1,2));

        $this->setExpectedException(
            'AccountChange_AccountConfigurationException',
            'Invalid Product Configuration object',
            AccountChange_AccountConfigurationException::ERR_INVALID_PRODUCT_CONFIGURATION_OBJECT
        );

        $objAccountConfiguration = new AccountChange_AccountConfiguration($arrProductConfigurations);

        $objAccountConfiguration->validateAccountConfiguration();
    }


    /**
     * @covers AccountChange_AccountConfiguration::__construct
     * @covers AccountChange_AccountConfiguration::validateAccountConfiguration
     */
    public function testValidateAccountConfigurationReturnsTrueEverythingValidated()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                         ->method('isServiceComponentAllowedOnServiceDefinition')
                         ->will($this->returnValue(1));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $arrProductConfigurations[] = new AccountChange_Product_ServiceDefinition(6693, true);
        $arrProductConfigurations[] = new AccountChange_Product_ServiceComponent(515, true);

        $objAccountConfiguration = new AccountChange_AccountConfiguration($arrProductConfigurations);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurations
     */
    public function testGetProductConfigurations()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                         ->method('isServiceComponentAllowedOnServiceDefinition')
                         ->will($this->returnValue(1));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $arrProductConfigurations[] = new AccountChange_Product_ServiceDefinition(6693, true);

        $objAccountConfiguration = new AccountChange_AccountConfiguration($arrProductConfigurations);

        $arrReturn = $objAccountConfiguration->getProductConfigurations();

        $this->assertEquals($arrReturn, $arrProductConfigurations);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getTotalNumberOfKeyProducts
     *
     */
    public function testGetTotalNumberOfKeyProductsReturnsZeroIfTheAccountConfigurationHasNoKeyProductConfigurations()
    {
        $objServiceDefinition = $this->getMock('AccountChange_Product_ServiceDefinition', array('isKeyProduct'), array(23, false));
        $objServiceDefinition->expects($this->any())
                             ->method('isKeyProduct')
                             ->will($this->returnValue(false));

        $arrProductConfigurations[] = $objServiceDefinition;

        $objAccountConfiguration = new AccountChange_AccountConfiguration($arrProductConfigurations);

        $this->assertEquals(0, $objAccountConfiguration->getTotalNumberOfKeyProducts());
    }

    /**
     * @covers AccountChange_AccountConfiguration::getTotalNumberOfKeyProducts
     *
     */
    public function testGetTotalNumberOfKeyProductsReturnsCorrectNumberOfKeyProducts()
    {
        $objServiceDefinition = $this->getMock('AccountChange_Product_ServiceDefinition', array('isKeyProduct'), array(23, true));
        $objServiceDefinition->expects($this->any())
                             ->method('isKeyProduct')
                             ->will($this->returnValue(true));

        $objServiceDefinition2 = $this->getMock('AccountChange_Product_ServiceDefinition', array('isKeyProduct'), array(23, false));
        $objServiceDefinition2->expects($this->any())
                             ->method('isKeyProduct')
                             ->will($this->returnValue(false));

        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent', array('isKeyProduct'), array(23, true));
        $objServiceComponent->expects($this->any())
                             ->method('isKeyProduct')
                             ->will($this->returnValue(true));

        $arrProductConfigurations[] = $objServiceDefinition;
        $arrProductConfigurations[] = $objServiceDefinition2;
        $arrProductConfigurations[] = $objServiceComponent;

        $objAccountConfiguration = new AccountChange_AccountConfiguration($arrProductConfigurations);

        $this->assertEquals(2, $objAccountConfiguration->getTotalNumberOfKeyProducts());
    }

    /**
     * @covers AccountChange_AccountConfiguration::getTotalCostOfKeyProducts
     *
     */
    public function testGetTotalCostOfKeyProductsReturnsZeroIfTheAccountConfigurationHasNoKeyProductConfigurations()
    {
        $objServiceDefinition = $this->getMock('AccountChange_Product_ServiceDefinition', array('isKeyProduct'), array(23, false));
        $objServiceDefinition->expects($this->any())
                             ->method('isKeyProduct')
                             ->will($this->returnValue(false));

        $arrProductConfigurations[] = $objServiceDefinition;

        $objAccountConfiguration = new AccountChange_AccountConfiguration($arrProductConfigurations);

        $this->assertEquals(0, $objAccountConfiguration->getTotalCostOfKeyProducts());
    }

    /**
     * @covers AccountChange_AccountConfiguration::getTotalCostOfKeyProducts
     *
     */
    public function testGetTotalCostOfKeyProductsReturnsTheCorrectCostOfTheKeyProducts()
    {
        $intExpectedCost = 100;

        $objServiceDefinition = $this->getMock('AccountChange_Product_ServiceDefinition', array('getProductCost'), array(23, true));
        $objServiceDefinition->expects($this->any())
                             ->method('getProductCost')
                             ->will($this->returnValue(40));

        $objServiceComponent = $this->getMock('AccountChange_Product_ServiceComponent', array('getProductCost'), array(23, true));
        $objServiceComponent->expects($this->any())
                             ->method('getProductCost')
                             ->will($this->returnValue(60));

        $arrProductConfigurations[] = $objServiceDefinition;
        $arrProductConfigurations[] = $objServiceComponent;

        $objAccountConfiguration = new AccountChange_AccountConfiguration($arrProductConfigurations);

        $this->assertEquals($intExpectedCost, $objAccountConfiguration->getTotalCostOfKeyProducts());
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurationByType
     */
    public function testGetProductConfigurationByTypeThrowsNoValidProductConfigurationFound()
    {
        $objProduct = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct));

        $this->setExpectedException('AccountChange_AccountConfigurationException', 'No valid product configuration found for type',
                                    AccountChange_AccountConfigurationException::ERR_NO_VALID_PRODUCT_CONFIGURATION_FOUND);

        $objAccountConfiguration->getProductConfigurationByType(AccountChange_Product_Manager::PRODUCT_TYPE_WLR);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurationByType
     */
    public function testGetProductConfigurationByTypeReturnsServiceDefinitionProduct()
    {
        $objProduct = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct));

        $objResult = $objAccountConfiguration->getProductConfigurationByType(AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION);

        $this->assertInstanceOf('AccountChange_Product_ServiceDefinition', $objResult);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurationByType
     */
    public function testGetProductConfigurationByTypeReturnsWlrProduct()
    {
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objProduct = new AccountChange_Product_Wlr(1, AccountChange_Product_Manager::ACTION_ADD);

        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct));

        $objResult = $objAccountConfiguration->getProductConfigurationByType(AccountChange_Product_Manager::PRODUCT_TYPE_WLR);

        $this->assertInstanceOf('AccountChange_Product_Wlr', $objResult);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurationByType
     */
    public function testGetProductConfigurationByTypeReturnsInternetConnectionProduct()
    {
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objProduct = new AccountChange_Product_InternetConnection(1,
                                                                   AccountChange_Product_Manager::ACTION_ADD,
                                                                   array('intNewServiceDefinitionId' => 123));

        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct));

        $objResult = $objAccountConfiguration->getProductConfigurationByType(AccountChange_Product_Manager::PRODUCT_TYPE_INTERNET_CONNECTION);

        $this->assertInstanceOf('AccountChange_Product_InternetConnection', $objResult);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurationByType
     */
    public function testGetProductConfigurationByTypeReturnsServiceComponentProduct()
    {
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objProduct = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_ADD);

        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct));

        $objResult = $objAccountConfiguration->getProductConfigurationByType(AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_COMPONENT);

        $this->assertInstanceOf('AccountChange_Product_ServiceComponent', $objResult);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurationByType
     */
    public function testGetProductConfigurationByTypeReturnsFirewallProduct()
    {
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objProduct = new AccountChange_Product_Firewall(1, AccountChange_Product_Manager::ACTION_ADD);

        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct));

        $objResult = $objAccountConfiguration->getProductConfigurationByType(AccountChange_Product_Manager::PRODUCT_TYPE_FIREWALL);

        $this->assertInstanceOf('AccountChange_Product_Firewall', $objResult);
    }

    /**
     * @covers AccountChange_AccountConfiguration::getProductConfigurationByType
     */
    public function testGetProductConfigurationByTypeReturnsCommunityProduct()
    {
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objProduct = new AccountChange_Product_Community(1, AccountChange_Product_Manager::ACTION_ADD);

        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct));

        $objResult = $objAccountConfiguration->getProductConfigurationByType(AccountChange_Product_Manager::PRODUCT_TYPE_COMMUNITY);

        $this->assertInstanceOf('AccountChange_Product_Community', $objResult);
    }

    /**
     * @covers AccountChange_AccountConfiguration::setProductConfigurations
     *
     */
    public function testSetProductConfigurationsValidatesTheAccountConfiguration()
    {
        $objAccountConfiguration = $this->getMock('AccountChange_AccountConfiguration',
                                                  array('validateAccountConfiguration'),
                                                  array(), '', false);

        $objAccountConfiguration->expects($this->once())
                                ->method('validateAccountConfiguration');

        $objProductConfiguration = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_NONE);

        $objAccountConfiguration->setProductConfigurations(array($objProductConfiguration));
    }

    /**
     * @covers AccountChange_AccountConfiguration::setServiceId
     */
    public function testSetServiceIdCallsSetServiceIdForProductConfigurations()
    {
        $objAccountConfigurationMock = $this->getMock('AccountChange_AccountConfiguration',
                                                      array('setServiceIdForProductConfigurations'),
                                                      array(), '', false);

        $objAccountConfigurationMock->expects($this->once())
                                    ->method('setServiceIdForProductConfigurations');

        $objAccountConfigurationMock->setServiceId(999);
    }

    /**
     * @covers AccountChange_AccountConfiguration::setServiceId
     */
    public function testSetServiceIdThrowsInvalidServiceIdException()
    {
        $intServiceId = 'fake';

        $objAccountConfigurationMock = $this->getMock('AccountChange_AccountConfiguration',
                                                      array('setServiceIdForProductConfigurations'),
                                                      array(), '', false);

        $objAccountConfigurationMock->expects($this->never())
                                    ->method('setServiceIdForProductConfigurations');

        $this->setExpectedException('AccountChange_AccountConfigurationException', 'Non numeric service id',
                                    AccountChange_AccountConfigurationException::ERR_INVALID_SERVICE_ID);

        $objAccountConfigurationMock->setServiceId($intServiceId);
    }

    /**
     * @covers AccountChange_AccountConfiguration::setServiceIdForProductConfigurations
     *
     */
    public function testSetServiceIdForProductConfigurationsCallsSetServiceIdForEachProductInArray()
    {
        $intServiceId = 9898;

        $objProductMock = $this->getMock('AccountChange_Product_ServiceDefinition',
                                         array('setServiceId'),
                                         array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objProductMock->expects($this->once())
                       ->method('setServiceId');

        $objProductMock2 = $this->getMock('AccountChange_Product_ServiceComponent',
                                          array('setServiceId'),
                                          array(1, AccountChange_Product_Manager::ACTION_NONE));

        $objProductMock2->expects($this->once())
                        ->method('setServiceId');

        $objAccountConfigurationMock = $this->getMock('AccountChange_AccountConfiguration',
                                                      array('validateAccountConfiguration'),
                                                      array(), '', false);

        $objAccountConfigurationMock->expects($this->once())
                                    ->method('validateAccountConfiguration');

        $objAccountConfigurationMock->setProductConfigurations(array($objProductMock, $objProductMock2));
        $objAccountConfigurationMock->setServiceIdForProductConfigurations($intServiceId);
    }

    /**
     * @covers AccountChange_AccountConfiguration::setServiceIdForProductConfigurations
     *
     */
    public function testSetServiceIdForProductConfigurationsSetsTheCorrectServiceIdForEachProductInArray()
    {
        $intServiceId = 9898;

        $objProduct = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);
        $objProduct2 = new AccountChange_Product_ServiceComponent(1, AccountChange_Product_Manager::ACTION_NONE);

        $objAccountConfigurationMock = $this->getMock('AccountChange_AccountConfiguration',
                                                      array('validateAccountConfiguration'),
                                                      array(), '', false);

        $objAccountConfigurationMock->expects($this->once())
                                    ->method('validateAccountConfiguration');

        $objAccountConfigurationMock->setProductConfigurations(array($objProduct, $objProduct2));
        $objAccountConfigurationMock->setServiceIdForProductConfigurations($intServiceId);

        $this->assertAttributeEquals($intServiceId, '_intServiceId', $objProduct);
        $this->assertAttributeEquals($intServiceId, 'intServiceId', $objProduct2);
    }

    /**
     * @covers AccountChange_AccountConfiguration::setServiceIdForProductConfigurations
     */
    public function testSetServiceIdForProductConfigurationsThrowsExceptionIfServiceIdIsNotValid()
    {
        $objProduct = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_ADD);
        $objAccountConfiguration = new AccountChange_AccountConfiguration(array($objProduct));

        $this->setExpectedException('AccountChange_AccountConfigurationException', 'Non numeric service id',
                                    AccountChange_AccountConfigurationException::ERR_INVALID_SERVICE_ID);

        $objAccountConfiguration->setServiceIdForProductConfigurations('Invalid Service Id');
    }
}
