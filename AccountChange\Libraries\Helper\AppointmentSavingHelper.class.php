<?php
/**
 * Class AccountChange_AppointmentSavingHelper
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_AppointmentSavingHelper
{
    private $appointment;

    private $serviceId;

    private $arrClientData = array();

    /**
     * AccountChange_AppointmentSavingHelper constructor.
     * @param AccountChange_AccountChangeAppointment $appointment Appointment to save
     * @param int                                    $serviceId   Service Id for the appointment
     */
    public function __construct($appointment, $serviceId)
    {
        $this->appointment = $appointment;
        $this->serviceId = $serviceId;
    }

    /**
     * Attempt to save the current appointment details
     *
     * @return void
     */
    public function saveLiveAppointmentToDb()
    {
        $this->setUpClientData();
        $client = $this->createClient();
        $client->storeAppointment(new Int($this->serviceId), null);
    }

    /**
     * @return EngineerAppointmentClient_Service_Fttp
     */
    protected function createClient()
    {
        return new EngineerAppointmentClient_Service_Fttp($this->arrClientData);
    }

    /**
     * Sets up data needed to construct the client
     *
     * @return void
     */
    protected function setUpClientData()
    {
        $liveAppointment = $this->appointment->getLiveAppointment();
        $this->arrClientData['engineerNotes'] = $this->appointment->getNotes();
        $this->arrClientData['bookingReference'] = $liveAppointment['ref'];
        $this->arrClientData['appointments'][] = array(
            'priority' => 1,
            'date' => $liveAppointment['date'],
            'slot' => $liveAppointment['timeSlot']
        );
    }
}
