<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
    <id>distribution</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <baseDirectory></baseDirectory>
    <fileSets>
        <fileSet>
            <useDefaultExcludes>true</useDefaultExcludes>
            <excludes>
                <exclude>pom.xml</exclude>
                <exclude>distribution.xml</exclude>
                <exclude>phpunit.xml.dist</exclude>
                <exclude>${project.build.directory}</exclude>
                <exclude>**/Test/**</exclude>
                <exclude>**/Tests/**</exclude>
                <exclude>**/test/**</exclude>
                <exclude>**/tests/**</exclude>
                <exclude>**/test-suite/**</exclude>
            </excludes>
        </fileSet>
    </fileSets>
</assembly>