<?php

use Plusnet\BillingApiClient\Entity\CustomerProductSubscription;

class AccountChange_BillingApi_SubscriptionsHelper
{
    const LINE_RENTAL_PRODUCT_TYPE = 'Line Rental';
    const BROADBAND_PRODUCT_TYPE = 'Broadband';
    const TARIFF_NAME_LINE_RENTAL_SAVER = 'Line Rental Saver';

    /**
     * Checks if there is any type of line rental saver, either active or inactive by checking RBM.
     *  In RBM (new billing) if they have LRS there will be several LineRental subscriptions of which
     *  at least one will be 0 value.  This is changed the moment the customer pays for LRS so covers both pending
     *  and active.
     *
     * @param int $serviceId service id to check
     *
     * @return bool
     **/
    public function hasActiveOrPendingLineRentalSaver($serviceId)
    {
        $subscriptions = $this->getCustomerCurrentSubscriptions($serviceId);

        if (!empty($subscriptions)) {
            foreach ($subscriptions as $customerProductSubscription) {
                if ($customerProductSubscription instanceof Plusnet\BillingApiClient\Entity\CustomerProductSubscription &&
                    $customerProductSubscription->getCurrentSubscriptionPriceInPounds() === 0 &&
                    $customerProductSubscription->getProductType() === self::LINE_RENTAL_PRODUCT_TYPE
                ) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Gets a list of current subscriptions
     *
     * @param int $serviceId Customer's Service ID
     * @return array
     */
    public function getCustomerCurrentSubscriptions($serviceId)
    {
        return $this->getBillingApiClient()->getCustomerCurrentSubscriptions($serviceId);
    }

    /**
     * Get an instance of the Billing Api Client
     *
     * @return \Plusnet\BillingApiClient\Facade\BillingApiFacade
     */
    private function getBillingApiClient()
    {
        return \Plusnet\BillingApiClient\Service\ServiceManager::getService('BillingApiFacade');
    }

    /**
     * Gets the current broadband product by querying RBM for current subscriptions
     * then translates the legacy id into a service component id
     *
     * @param int $serviceId Service id
     *
     * @return int
     *
     * @throws Db_TransactionException
     */
    public function getServiceComponentIdFromBroadbandSubscription($serviceId)
    {
        $subscriptions = $this->getCustomerCurrentSubscriptions($serviceId);
        $legacyProductComponentInstanceId = null;

        foreach ($subscriptions as $customerProductSubscription) {
            if ($customerProductSubscription instanceof Plusnet\BillingApiClient\Entity\CustomerProductSubscription &&
                $customerProductSubscription->getProductType() == self::BROADBAND_PRODUCT_TYPE
            ) {
                $legacyReferenceParts = explode('|', $customerProductSubscription->getLegacyReference());

                if (!empty($legacyReferenceParts)) {
                    $legacyProductComponentInstanceId = $legacyReferenceParts[0];
                    return $this->getServiceComponentIdFromProductComponentInstanceId($legacyProductComponentInstanceId);
                }
            }
        }
        return null;
    }

    /**
     * Converts a product component instance id into a corresponding service component id
     *
     * @param int $productComponentInstanceId Product component instance id
     *
     * @return int
     *
     * @throws Db_TransactionException
     */
    protected function getServiceComponentIdFromProductComponentInstanceId($productComponentInstanceId)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        return $dbAdaptor->getServiceComponentIdFromProductComponentInstanceId($productComponentInstanceId);
    }

    /**
     * @param $serviceId
     *
     * @return bool
     */
    public function hasActiveLineRentalSaver($serviceId)
    {
        try {
            $subscriptions = $this->getCustomerCurrentSubscriptions($serviceId);

            if (empty($subscriptions)) {
                return false;
            }

            foreach ($subscriptions as $customerProductSubscription) {
                if ($this->isLineRentalSaverActive($customerProductSubscription)) {
                    return true;
                }
            }

            return false;
        } catch (Exception $exception) {
            error_log($exception);
            return false;
        }
    }

    /**
     * @param mixed $customerProductSubscription
     *
     * @return bool
     */
    private function isLineRentalSaverActive($customerProductSubscription)
    {
        return $customerProductSubscription instanceof CustomerProductSubscription &&
            $customerProductSubscription->getTariffName() === self::TARIFF_NAME_LINE_RENTAL_SAVER;
    }
}
