<?php

use \Plusnet\C2mApiClient\Entity\Promotion;
use \Plusnet\C2mApiClient\Entity\Discount;
use \Plusnet\C2mApiClient\Entity\DiscountValue;

require_once(__DIR__.'/../Libraries/PromotionConverter.php');

/**
 * Handle the conversion of C2M Promotions to the format required
 * by AccountChange
 *
 * Class C2MPromotionAdapter
 */
class C2MPromotionAdapter implements PromotionConverter
{
    const CASHBACK_DISCOUNT_TYPE = 'CASH_BACK';

    /**
     * @var \Plusnet\C2mApiClient\Entity\Promotion
     */
    private $promotion;

    /**
     * @var \Plusnet\C2mApiClient\Entity\Discount
     */
    private $discount;

    /**
     * @var \Plusnet\C2mApiClient\Entity\Discount
     */
    private $lineRentalDiscount;

    /**
     * @return Discount
     */
    public function getLineRentalDiscount()
    {
        return $this->lineRentalDiscount;
    }

    /**
     * @param Discount $lineRentalDiscount
     */
    public function setLineRentalDiscount($lineRentalDiscount)
    {
        $this->lineRentalDiscount = $lineRentalDiscount;
    }


    /**
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     *
     * @return $this
     */
    public function setPromotion(Promotion $promotion)
    {
        $this->promotion = $promotion;

        return $this;
    }

    /**
     * @param \Plusnet\C2mApiClient\Entity\Discount $discount
     *
     * @return $this
     */
    public function setDiscount(Discount $discount)
    {
        $this->discount = $discount;

        return $this;
    }

    public function reset()
    {
        $this->discount = null;
        $this->lineRentalDiscount = null;
        $this->promotion = null;
    }

    /**
     * {@inheritdoc}
     *
     * Uses the Promotion and $DiscountValue data from c2m and converts it to
     * the format expected by the AccountChange process.
     *
     * @return array
     */
    public function convertPromotion()
    {
        return array_merge($this->convertBroadbandPromotion(), $this->convertLineRentalPromotion());
    }

    /**
     * Converts the broadband part of a c2m promotion to an account change friendly format.
     *
     * @return array
     **/
    protected function convertBroadbandPromotion()
    {
        $accountChangeDiscount = [];
        if (isset($this->discount) && $this->discount instanceof Plusnet\C2mApiClient\Entity\Discount) {
            $promoDiscountValue = $this->discount->getDiscountValues()[0];

            $requiredCharacteristics = $this->discount->getRequiredCharacteristics();

            $accountChangeDiscount['c2mDiscountRequiredContractLength'] = null;

            /**
             * We're making an assumption here that there will always be a broadband element to the promotion (i.e no line rental only
             * discounts).  If we need to allow line rental only in the future, then we'd need to do the below check in convertLineRentalPromotion too (and make a
             * decision which contract length takes preference if line rental and broadband have different required contract lenghts).
             **/
            if (is_array($requiredCharacteristics)) {
                foreach ($requiredCharacteristics as $characteristic) {
                    if ($characteristic->getName() == 'contractLength') {
                        $accountChangeDiscount['c2mDiscountRequiredContractLength'] = $characteristic->getValue();
                    }
                }
            }

            $accountChangeDiscount['promoCode'] = $this->promotion->getCode();
            $accountChangeDiscount['intDiscountLength'] = $promoDiscountValue->getDuration();
            $accountChangeDiscount['promoDiscountType'] =
                ($promoDiscountValue->getDiscountValueType() === 'FIXED_AMOUNT'
                    ? AccountChange_DiscountHelper::FIXED_DISCOUNT
                    : AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT);
            $accountChangeDiscount['decValue'] = $promoDiscountValue->getValue();
            $accountChangeDiscount['discountSubType'] = $this->discount->getType();
            $accountChangeDiscount['marketingClassification'] = $this->promotion->getMarketingClassification();
            $accountChangeDiscount = $this->addCashbackIfPresent($accountChangeDiscount);
        }
        return $accountChangeDiscount;
    }

    private function addCashbackIfPresent($accountChangeDiscount)
    {
        foreach ($this->promotion->getDiscounts() as $discount) {

            if ($discount->getType() === self::CASHBACK_DISCOUNT_TYPE) {

                $accountChangeDiscount['includesCashback'] = true;
                $accountChangeDiscount['cashbackType']     = $discount->getSubType();
                $accountChangeDiscount['cashbackValue']    = $discount->getDiscountValues()[0]->getValue();

                break;
            }
        }

        return $accountChangeDiscount;
    }

    /**
     * Converts the line rental part of a c2m promotion to an account change friendly format.
     *
     * @return array
     **/
    protected function convertLineRentalPromotion()
    {
        $accountChangeDiscount = [];
        if (isset($this->lineRentalDiscount) && $this->lineRentalDiscount instanceof Plusnet\C2mApiClient\Entity\Discount) {

            $promoDiscountValue = $this->lineRentalDiscount->getDiscountValues()[0];
            $accountChangeDiscount['lineRentalDiscountLength'] = $promoDiscountValue->getDuration();
            $accountChangeDiscount['lineRentalPromoDiscountType'] =
                ($promoDiscountValue->getDiscountValueType() === 'FIXED_AMOUNT'
                    ? AccountChange_DiscountHelper::FIXED_DISCOUNT
                    : AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT);

            // Note this value could be a monetary value, or a percentage depending on the value in $accountChangeDiscount['lineRentalPromoDiscountType']
            // We cannot calculate the final value here as we don't know what the line rental price is (so can't calulate the percentage).  That calculation
            // is the job of whatever is using these values..
            $accountChangeDiscount['lineRentalDiscountValue'] = $promoDiscountValue->getValue();
            $accountChangeDiscount['lineRentalDiscountMarketingClassification'] = $this->promotion->getMarketingClassification();

        }
        return $accountChangeDiscount;
    }
}
