server: coredb
role: slave
rows: single
statement:

SELECT
    s.service_id,
    sd.service_definition_id,
    sd.name,
    sd.isp,
    sd.minimum_charge,
    sd.initial_charge,
    sd.type,
    sd.password_visible_to_support,
    sd.requires,
    sd.date_created,
    sd.end_date,
    sd.signup_via_portal,
    sd.blurb,
    IFNULL(ap.vchContract, 'MONTHLY') AS vchContract,
    0.00 AS decLeadDiscount,
    provProfile.vchName AS provisioningProfile
FROM  userdata.services AS s
INNER JOIN products.service_definitions AS sd
    ON (sd.service_definition_id = s.type)
LEFT JOIN products.adsl_product AS ap
    ON sd.service_definition_id = ap.service_definition_id
LEFT JOIN products.tblProvisioningProfile AS provProfile
    ON provProfile.intProvisioningProfileID = ap.intProvisioningProfileID
WHERE
    s.service_id = :intServiceId
