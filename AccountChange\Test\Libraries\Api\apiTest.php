<?php
require_once '/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php';

/**
 * This is a command line script for test environments that lets us run account change validators through the
 * account change API to see if they pass validation or not.
 *
 * Usage: scp to test environment to any location (e.g /home/<USER>/tmp)
 *   php apiTest.php serviceId  newBroadbandServiceDefinitionId  promoCode  oldPhoneServiceComponentId  newBroadbandServiceDefinitionId [newPhoneServiceComponentId]
 **/

class AccountChangeAPI_ValidationTest
{

    protected $serviceId;
    protected $newSdid;
    protected $promoCode;
    protected $oldPhoneServiceComponentId;
    protected $newBroadbandServiceDefinitionId;

    protected $accountChangeOrder;


    public function __construct($args)
    {
        $this->setupTests($args);
    }


    /**
     * Get arguments from command line
     **/
    protected function setupArgs($args)
    {
        $this->serviceId = $args[1];
        $this->promoCode = $args[2];
        $this->oldPhoneServiceComponentId = $args[3];
        $this->newBroadbandServiceDefinitionId = $args[4];
        $this->newPhoneServiceComponentId = $args[5];
    }

    /**
     * Creates new product configs for addition to an order
     **/
    protected function buildNewProducts()
    {
        $accountChangeOrderProducts = new \AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->setServiceDefinitionId($this->newBroadbandServiceDefinitionId);
        if (!empty($this->newPhoneServiceComponentId) && $this->oldPhoneServiceComponentId != $this->newPhoneServiceComponentId) {
            $accountChangeOrderProducts->addServiceComponentId(\AccountChange_AccountChangeOrderProducts::PHONE_COMPONENT_ID, $this->newPhoneServiceComponentId);
        }
        $this->accountChangeOrder->setProducts($accountChangeOrderProducts);
    }

    /**
     * Builds up an AccountChange_AccountChangeOrder object with submitted details
     **/
    protected function buildOrder()
    {
        $this->accountChangeOrder = new \AccountChange_AccountChangeOrder();
        $this->accountChangeOrder->setServiceId($this->serviceId);
        $this->accountChangeOrder->setPromotionCode($this->promoCode);
        $this->accountChangeOrder->setOldPhoneId($this->oldPhoneServiceComponentId);
        $this->accountChangeOrder->setMarket('3');
        $this->accountChangeOrder->setType(\AccountChange_AccountChangeOrder::TYPE_MY_OFFERS_PRODUCT_CHANGE);

        $this->accountChangeOrder->addConsent(\AccountChange_AccountChangeOrder::CONSENT_L2C, 'true');
        $this->accountChangeOrder->addConsent(\AccountChange_AccountChangeOrder::CONSENT_PROCEED_WITHOUT_LINE_CHECK, "true");

        $this->buildNewProducts();
    }

    /**
     * Wrapper method for unit testing
     *
     * @return \AccountChange_AccountChangeApi
     */
    protected function getAccountChangeApi()
    {
        return new \AccountChange_AccountChangeApi();
    }


    /**
     * Inclusion of legacy files so we can mock them.
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        class_exists('Smarty');
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/class-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWifiComponent.inc';
    }

    public function setupTests($args)
    {
        $this->includeLegacyFiles();

        echo "Args: serviceId  promoCode  oldPhoneServiceComponentId  newBroadbandServiceDefId [newPhoneServiceComponentId]\n\n";

        $this->setupArgs($args);

        echo "Running with service id; ".$this->serviceId."\n";

        echo "Building account change order:\n";
        $this->buildOrder();
        echo "Order built..\n";
    }

    /**
     * This will setup an account change api call, with the StaffAccountChange situation (i.e simulating account
     * change being run from workplace).  It will run the AccountChange_PromotionCodeWorkplaceChannelPolicy validator against
     * the submitted promo code and service id
     *
     **/
    public function testValidatingAPromoWhenUsingStaffAccountChangeSalesChannel()
    {
        echo "Running this promotion against the AccountChange_PromotionCodeWorkplaceChannelPolicy validator\n";
        $accountChangeApi = $this->getAccountChangeApi();
        try {
            $this->accountChangeOrder->registerAdditionalValidator('AccountChange_PromotionCodeWorkplaceChannelPolicy');
            $this->accountChangeOrder->setAdditionalValidatorInformation(array('C2MPromotionCode' => $this->promoCode, 'SalesChannel' => 'PlusnetResidential-StaffAccountChange-NoAffiliate-Retention'));
            $this->accountChangeOrder->validate();
            $this->accountChangeOrder->resetAdditionalValidators();
            echo "Validation PASSED.\n";
        } catch (Exception $e) {
            echo "Validation FAILED with message: ".$e->getMessage()."\n";
        }
    }

    /**
     * This will setup an account change api call with a given promo and service id, then run the AccountChange_PromotionCodePersonalisedPolicy against it.
     *
     * Outcomes:  - if the offer is not personalised, it will PASS validation.
     *            - if the offer is personalised, and is assigned to the customer, then it will PASS validation
     *            - if the offer is personalised, and is not assigned to the customer, then it will FAIL validation
     **/
    public function testValidatingAPromoAgainstThePersonalisedValidator()
    {
        echo "Running this promotion and customer against the AccountChange_PromotionCodePersonalisedPolicy validator\n";
        $accountChangeApi = $this->getAccountChangeApi();
        try {
            $this->accountChangeOrder->registerAdditionalValidator('AccountChange_PromotionCodePersonalisedPolicy');
            $this->accountChangeOrder->setAdditionalValidatorInformation(array('C2MPromotionCode' => $this->promoCode, 'SalesChannel' => 'PlusnetResidential-StaffAccountChange-NoAffiliate-Retention'));
            $this->accountChangeOrder->validate();
            $this->accountChangeOrder->resetAdditionalValidators();
            echo "Validation PASSED.\n";
        } catch (Exception $e) {
            echo "Validation FAILED with message: ".$e->getMessage()."\n";
        }
    }
}

$apiTest = new AccountChangeAPI_ValidationTest($argv);
$apiTest->testValidatingAPromoWhenUsingStaffAccountChangeSalesChannel();
$apiTest->testValidatingAPromoAgainstThePersonalisedValidator();
