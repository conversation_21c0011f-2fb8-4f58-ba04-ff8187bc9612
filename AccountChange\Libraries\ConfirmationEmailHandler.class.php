<?php
/**
 * Holds details and processes the confirmation email as part of the 2013 landing pages
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
/**
 * AccountChange_ConfirmationEmailHandler
 * Holds details and processes the confirmation email as part of the 2013 landing pages
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @example
 *
 * $confEmail = new AccountChange_ConfirmationEmailHandler();
 * $confEmail->setCurrentDualPlay(true)
 * ->setUsageAllowanceFixed(true)
 * ->setNewDirectDebit(true)
 * ->setNewDirectDebitReady(true)
 * ->setRecontracted(true)
 * ->setSpecialOffer(true)
 * ->setNewBroadbandProduct('newBroadbandProduct')
 * ->setNewUsageAllowance('newUsageAllowance')
 * ->setLineRentalFrequency('lineRentalFrequency')
 * ->setNewCallPlan('newCallPlan')
 * ->setCallFeatures('callFeatures')
 * ->setContractDuration('contractDuration')
 * ->setNewPrice('newPrice')
 * ->setOfferPrice('offerPrice')
 * ->setOfferDuration('offerDuration');
 * $confEmail->sendEmail(1994934);
 */


class AccountChange_ConfirmationEmailHandler extends AccountChange_EmailHandler
{
    private $emailName = 'upgrade_confirmation';
    /**
     * @var boolean does the customer already have phone (not are they on dual play pricing)
     * True = they do False = they don't
     */
    private $currentDualPlay;
    /**
     * @var boolean does the new broadband product have a usage allowance?
     * True = Yes False = No (Unlimited)
     */
    private $usageAllowanceFixed;
    /**
     * @var boolean did the customer add a DD instruction while upgrading?
     * True = Yes False = No
     */
    private $newDirectDebit;
    /**
     * @var boolean Will the DD be set up before the customer is billed?
     * (I think it takes 7 days) =  True = Yes False = No
     */
    private $newDirectDebitReady;
    /**
     * @var boolean Did the customer recontract as part of the account change?
     * True = Yes Fales = No
     */
    private $recontracted;
    /**
     * @var boolean Did the customer take a special offer?
     * True = Yes Fales = No
     */
    private $specialOffer;

    /**
     * Current broadband product name
     *
     * @var string
     */
    private $currentBroadbandProduct;

    /**
     * @var string new Broadband package
     */
    private $newBroadbandProduct;
    /**
     * @var string Allowance for new Broadband package
     * (will only be displayed if usageAllowanceFixed is true)
     */
    private $newUsageAllowance;
    /**
     * @var string Monthly line rental or annual line rental? (as per signup)
     */
    private $lineRentalFrequency;

    /**
     * Current call plan name
     *
     * @var string
     */
    private $currentCallPlan;

    /**
     * Current line rental
     *
     * @var float
     */
    private $currentLineRental;

    /**
     * @var string new call plan
     */
    private $newCallPlan;

    /**
     * New line rental
     *
     * @var string
     */
    private $newLineRental;

    /**
     * @var string what extra call features does the customer have? Please list all separated by commas
     */
    private $callFeatures;
    /**
     * @var string How long is the customers contract?
     */
    private $contractDuration;
    /**
     * @var string Broadband + Line rental + Call plan cost
     */
    private $newPrice;
    /**
     * @var string Above - whatever discount they are getting if they took an offer
     */
    private $offerPrice;
    /**
     * @var string Amount of discount - used when itemised separately
     */
    private $discountAmount;
    /**
     * @var string How long is the offer in months?
     */
    private $offerDuration;

    /**
     * @var string when the package is changing
     */
    private $changeDate;

    /**
     * @var bool Is broadband changing
     */
    private $isBroadbandChanging;

    /**
     * @var float Broadband subscription cost
     */
    private $broadbandSubscriptionCost;

    /**
     * @var float Line rental subscription cost
     */
    private $lineRentalSubscriptionCost;

    /**
     * @var float Call plan subscription cost
     */
    private $callPlanSubscriptionCost;

    /**
     * @var float Call feature subscription cost
     */
    private $callFeatureSubscriptionCost;

    /**
     * @var string Class from which this code is being called
     */
    private $callerClassName;

    private $advertisedDownloadSpeedMbs;
    private $advertisedUploadSpeedMbs;
    private $broadbandSpeedRange;
    private $minimumEstimatedDownloadSpeedMbs;
    private $maximumEstimatedDownloadSpeedMbs;
    private $minimumEstimatedUploadSpeedMbs;
    private $maximumEstimatedUploadSpeedMbs;
    private $maximumDownloadSpeedMbs;
    private $guaranteedSpeedAvailable;
    private $guaranteedSpeedValue;

    /**
     * @var string
     */
    private $fttpInstallationType;

    /**
     * @var bool
     */
    private $accessTechnologyChanging;

    /**
     * @var bool
     */
    private $isSogea;

    /**
     * @var bool
     */
    private $isFttp;

    /**
     * @var bool
     */
    private $isFttc;

    /**
     * Setter
     *
     * @param string $changeDate when the package is changing
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setChangeDate($changeDate)
    {
        $this->changeDate = $changeDate;

        return $this;
    }

    /**
     * Getter
     *
     * when the package is changing
     *
     * @return string when the package is changing
     */
    public function getChangeDate()
    {
        return $this->changeDate;
    }

    /**
     * Getter
     *
     * @return string what extra call features does the customer have? Please list all separated by commas
     */
    public function getCallFeatures()
    {
        return $this->callFeatures;
    }

    /**
     * Setter
     *
     * @param string $callFeatures what extra call features does the customer have? Please list all separated by commas
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCallFeatures($callFeatures)
    {
        $this->callFeatures = $callFeatures;

        return $this;
    }

    /**
     * Getter
     *
     * @return string How long is the customers contract?
     */
    public function getContractDuration()
    {
        return $this->contractDuration;
    }

    /**
     * Setter
     *
     * @param string $contractDuration How long is the customers contract?
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setContractDuration($contractDuration)
    {
        $this->contractDuration = $contractDuration;

        return $this;
    }

    /**
     * Getter
     *
     * @return boolean does the customer already have phone (not are they on dual play pricing)
     * True = they do False = they don't
     */
    public function getCurrentDualPlay()
    {
        return $this->currentDualPlay;
    }

    /**
     * Setter
     *
     * @param boolean $currentDualPlay does the customer already have phone (not are they on dual play pricing)
     * True = they do False = they don't
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCurrentDualPlay($currentDualPlay)
    {
        $this->currentDualPlay = $currentDualPlay;

        return $this;
    }

    /**
     * Getter
     *
     * @return string Monthly line rental or annual line rental? (as per signup)
     */
    public function getLineRentalFrequency()
    {
        return $this->lineRentalFrequency;
    }

    /**
     * Setter
     *
     * @param string $lineRentalFrequency Monthly line rental or annual line rental? (as per signup)
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setLineRentalFrequency($lineRentalFrequency)
    {
        $this->lineRentalFrequency = $lineRentalFrequency;

        return $this;
    }

    /**
     * Getter
     *
     * @return string current Broadband package
     */
    public function getCurrentBroadbandProduct()
    {
        return $this->currentBroadbandProduct;
    }

    /**
     * Setter
     *
     * @param string $currentBroadbandProduct Current Broadband package
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCurrentBroadbandProduct($currentBroadbandProduct)
    {
        $this->currentBroadbandProduct = $currentBroadbandProduct;

        return $this;
    }

    /**
     * Getter
     *
     * @return string new Broadband package
     */
    public function getNewBroadbandProduct()
    {
        return $this->newBroadbandProduct;
    }

    /**
     * Setter
     *
     * @param string $newBroadbandProduct new Broadband package
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setNewBroadbandProduct($newBroadbandProduct)
    {
        $this->newBroadbandProduct = $newBroadbandProduct;

        return $this;
    }


    /**
     * Getter
     *
     * @return string current call plan
     */
    public function getCurrentCallPlan()
    {
        return $this->currentCallPlan;
    }

    /**
     * Setter
     *
     * @param string $currentCallPlan current call plan
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCurrentCallPlan($currentCallPlan)
    {
        $this->currentCallPlan = $currentCallPlan;

        return $this;
    }

    /**
     * Getter
     *
     * @return string current line rental
     */
    public function getCurrentLineRental()
    {
        return $this->currentLineRental;
    }

    /**
     * Setter
     *
     * @param string $currentLineRental current line rental
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCurrentLineRental($currentLineRental)
    {
        $this->currentLineRental = $currentLineRental;

        return $this;
    }

    /**
     * Getter
     *
     * @return string new call plan
     */
    public function getNewCallPlan()
    {
        return $this->newCallPlan;
    }

    /**
     * Setter
     *
     * @param string $newCallPlan new call plan
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setNewCallPlan($newCallPlan)
    {
        $this->newCallPlan = $newCallPlan;

        return $this;
    }

    /**
     * Getter
     *
     * @return string new line rental
     */
    public function getNewLineRental()
    {
        return $this->newLineRental;
    }

    /**
     * Setter
     *
     * @param string $newLineRental new line rental
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setNewLineRental($newLineRental)
    {
        $this->newLineRental = $newLineRental;

        return $this;
    }

    /**
     * Getter
     *
     * @return boolean did the customer add a DD instruction while upgrading? True = Yes False = No
     */
    public function getNewDirectDebit()
    {
        return $this->newDirectDebit;
    }

    /**
     * Setter
     *
     * @param boolean $newDirectDebit did the customer add a DD instruction while upgrading? True = Yes False = No
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setNewDirectDebit($newDirectDebit)
    {
        $this->newDirectDebit = $newDirectDebit;

        return $this;
    }

    /**
     * Getter
     *
     * @return boolean Will the DD be set up before the customer is billed?
     * (I think it takes 7 days) =  True = Yes False = No
     */
    public function getNewDirectDebitReady()
    {
        return $this->newDirectDebitReady;
    }

    /**
     * Setter
     *
     * @param boolean $newDirectDebitReady Will the DD be set up before the customer is billed?
     * (I think it takes 7 days) =  True = Yes False = No
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setNewDirectDebitReady($newDirectDebitReady)
    {
        $this->newDirectDebitReady = $newDirectDebitReady;

        return $this;
    }

    /**
     * Getter
     *
     * @return string Broadband + Line rental + Call plan cost
     */
    public function getNewPrice()
    {
        return $this->newPrice;
    }

    /**
     * Setter
     *
     * @param string $newPrice Broadband + Line rental + Call plan cost
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setNewPrice($newPrice)
    {
        $this->newPrice = $newPrice;

        return $this;
    }

    /**
     * Getter
     *
     * @return string Allowance for new Broadband package (will only be displayed if usageAllowanceFixed is true)
     */
    public function getNewUsageAllowance()
    {
        return $this->newUsageAllowance;
    }

    /**
     * Setter
     *
     * @param string $newUsageAllowance Allowance for new Broadband package
     * (will only be displayed if usageAllowanceFixed is true)
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setNewUsageAllowance($newUsageAllowance)
    {
        $this->newUsageAllowance = $newUsageAllowance;

        return $this;
    }

    /**
     * Getter
     *
     * @return string How long is the offer in months?
     */
    public function getOfferDuration()
    {
        return $this->offerDuration;
    }

    /**
     * Setter
     *
     * @param string $offerDuration How long is the offer in months?
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setOfferDuration($offerDuration)
    {
        $this->offerDuration = $offerDuration;

        return $this;
    }

    /**
     * Getter
     *
     * @return string Above - whatever discount they are getting if they took an offer
     */
    public function getOfferPrice()
    {
        return $this->offerPrice;
    }

    /**
     * Setter
     *
     * @param string $offerPrice Above - whatever discount they are getting if they took an offer
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setOfferPrice($offerPrice)
    {
        $this->offerPrice = $offerPrice;

        return $this;
    }

    /**
     * Getter
     *
     * @return boolean Did the customer recontract as part of the account change? True = Yes False = No
     */
    public function getRecontracted()
    {
        return $this->recontracted;
    }

    /**
     * Setter
     *
     * @param boolean $recontracted Did the customer recontract as part of the account change? True = Yes Fales = No
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setRecontracted($recontracted)
    {
        $this->recontracted = $recontracted;

        return $this;
    }

    /**
     * Getter
     *
     * @return boolean Did the customer take a special offer? True = Yes False = No
     */
    public function getSpecialOffer()
    {
        return $this->specialOffer;
    }

    /**
     * Setter
     *
     * @param boolean $specialOffer Did the customer take a special offer? True = Yes False = No
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setSpecialOffer($specialOffer)
    {
        $this->specialOffer = $specialOffer;

        return $this;
    }

    /**
     * Getter
     *
     * @return boolean does the new broadband product have a usage allowance? True = Yes False = No (Unlimited)
     */
    public function getUsageAllowanceFixed()
    {
        return $this->usageAllowanceFixed;
    }

    /**
     * Setter
     *
     * @param boolean $usageAllowanceFixed does the new broadband product have a usage allowance?
     * True = Yes False = No (Unlimited)
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setUsageAllowanceFixed($usageAllowanceFixed)
    {
        $this->usageAllowanceFixed = $usageAllowanceFixed;

        return $this;
    }

    /**
     * Is broadband changing
     *
     * @return boolean
     */
    public function isBroadbandChanging()
    {
        return $this->isBroadbandChanging;
    }

    /**
     * Set if broadband is changing
     *
     * @param boolean $isBroadbandChanging Is broadband changing
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setIsBroadbandChanging($isBroadbandChanging)
    {
        $this->isBroadbandChanging = $isBroadbandChanging;

        return $this;
    }

    /**
     * Tranfers local variables into an array ready to be used
     *
     * @return Array email variables
     */
    protected function populateEmailVariables()
    {
        return array(
            'currentDualPlay' => $this->getCurrentDualPlay(),
            'usageAllowanceFixed' => $this->getUsageAllowanceFixed(),
            'newDirectDebit' => $this->getNewDirectDebit(),
            'newDirectDebitReady' => $this->getNewDirectDebitReady(),
            'recontracted' => $this->getRecontracted(),
            'specialOffer' => $this->getSpecialOffer(),
            'currentBroadbandProduct' => $this->getCurrentBroadbandProduct(),
            'newBroadbandProduct' => $this->getNewBroadbandProduct(),
            'newUsageAllowance' => $this->getNewUsageAllowance(),
            'lineRentalFrequency' => $this->getLineRentalFrequency(),
            'currentCallPlan' => $this->getCurrentCallPlan(),
            'currentLineRental' => $this->getCurrentLineRental(),
            'newCallPlan' => $this->getNewCallPlan(),
            'newLineRental' => $this->getNewLineRental(),
            'callFeatures' => $this->getCallFeatures(),
            'contractDuration' => $this->getContractDuration(),
            'newPrice' => $this->getNewPrice(),
            'offerPrice' => $this->getOfferPrice(),
            'offerDuration' => $this->getOfferDuration(),
            'changeDate' => $this->getChangeDate(),
            'isBroadbandChanging' => $this->isBroadbandChanging(),
            'broadbandCost' => $this->getBroadbandSubscriptionCost(),
            'lineRentalCost' => $this->getLineRentalSubscriptionCost(),
            'callPlanCost' => $this->getCallPlanSubscriptionCost(),
            'callFeatureCost' => $this->getCallFeatureSubscriptionCost(),
            'callerClassName' => $this->getCallerClassName(),
            'discountAmount' => $this->getDiscountAmount(),
            'advertisedDownloadSpeedMbs' => $this->getAdvertisedDownloadSpeedMbs(),
            'advertisedUploadSpeedMbs' => $this->getAdvertisedUploadSpeedMbs(),
            'broadbandSpeedRange' => $this->getBroadbandSpeedRange(),
            'minimumEstimatedDownloadSpeedMbs' => $this->getMinimumEstimatedDownloadSpeedMbs(),
            'maximumEstimatedDownloadSpeedMbs' => $this->getMaximumEstimatedDownloadSpeedMbs(),
            'minimumEstimatedUploadSpeedMbs' => $this->getMinimumEstimatedUploadSpeedMbs(),
            'maximumEstimatedUploadSpeedMbs' => $this->getMaximumEstimatedUploadSpeedMbs(),
            'maximumDownloadSpeedMbs' => $this->getMaximumDownloadSpeedMbs(),
            'guaranteedSpeedAvailable' => $this->getGuaranteedSpeedAvailable(),
            'guaranteedSpeedValue' => $this->getGuaranteedSpeedValue(),
            'installationTypeFTTP' => $this->getFttpInstallationType(),
            'accessTechnologyChanging' => $this->isAccessTechnologyChanging(),
            'isSogea' => $this->isSogeaProduct(),
            'isFttc' => $this->isFttcProduct(),
            'isFttp' => $this->isFttpProduct()
        );
    }

    /**
     * Method to override email template
     *
     * @param string $emailName Email template name to override
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setEmailName($emailName)
    {
        $this->emailName = $emailName;

        return $this;
    }

    /**
     * Get the email name, ie filename without the ext
     *
     * @return string email name
     */
    public function getEmailName()
    {
        return $this->emailName;
    }

    /**
     * Setter
     *
     * @param float $broadbandSubscriptionCost The monthly broadband cost
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setBroadbandSubscriptionCost($broadbandSubscriptionCost)
    {
        $this->broadbandSubscriptionCost = $broadbandSubscriptionCost;

        return $this;
    }

    /**
     * Setter
     *
     * @param float $lineRentalSubscriptionCost The monthly line rental cost
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setLineRentalSubscriptionCost($lineRentalSubscriptionCost)
    {
        $this->lineRentalSubscriptionCost = $lineRentalSubscriptionCost;

        return $this;
    }

    /**
     * Setter
     *
     * @param float $callPlanSubscriptionCost The monthly call plan cost
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCallPlanSubscriptionCost($callPlanSubscriptionCost)
    {
        $this->callPlanSubscriptionCost = $callPlanSubscriptionCost;

        return $this;
    }

    /**
     * Setter
     *
     * @param float $callFeatureSubscriptionCost The monthly call feature cost
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCallFeatureSubscriptionCost($callFeatureSubscriptionCost)
    {
        $this->callFeatureSubscriptionCost = $callFeatureSubscriptionCost;

        return $this;
    }

    /**
     * Get the broadband subscription cost
     *
     * @return float broadband subscription cost
     */
    public function getBroadbandSubscriptionCost()
    {
        return $this->broadbandSubscriptionCost;
    }

    /**
     * Get the line rental subscription cost
     *
     * @return float line rental subscription cost
     */
    public function getLineRentalSubscriptionCost()
    {
        return $this->lineRentalSubscriptionCost;
    }

    /**
     * Get the call plan subscription cost
     *
     * @return float call plan subscription cost
     */
    public function getCallPlanSubscriptionCost()
    {
        return $this->callPlanSubscriptionCost;
    }

    /**
     * Get the call feature subscription cost
     *
     * @return float call feature subscription cost
     */
    public function getCallFeatureSubscriptionCost()
    {
        return $this->callFeatureSubscriptionCost;
    }

    /**
     * Set calling class name
     *
     * @param string $emailName Email template name to override
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setCallerClassName($callerClassName)
    {
        $this->callerClassName = $callerClassName;

        return $this;
    }

    /**
     * Get the calling class name
     *
     * @return string Calling class name
     */
    public function getCallerClassName()
    {
        return $this->callerClassName;
    }

    /**
     * Set discount amount
     *
     * @param string Discount amount when itemised separately
     *
     * @return AccountChange_ConfirmationEmailHandler
     */
    public function setDiscountAmount($discountAmount)
    {
        $this->discountAmount = $discountAmount;

        return $this;
    }

    /**
     * Get the discount amount
     *
     * @return string Discount amount
     */
    public function getDiscountAmount()
    {
        return $this->discountAmount;
    }

    /**
     * @return mixed
     */
    public function getAdvertisedDownloadSpeedMbs()
    {
        return $this->advertisedDownloadSpeedMbs;
    }

    /**
     * @param mixed $advertisedDownloadSpeedMbs
     */
    public function setAdvertisedDownloadSpeedMbs($advertisedDownloadSpeedMbs)
    {
        $this->advertisedDownloadSpeedMbs = $advertisedDownloadSpeedMbs;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getAdvertisedUploadSpeedMbs()
    {
        return $this->advertisedUploadSpeedMbs;
    }

    /**
     * @param mixed $advertisedUploadSpeedMbs
     */
    public function setAdvertisedUploadSpeedMbs($advertisedUploadSpeedMbs)
    {
        $this->advertisedUploadSpeedMbs = $advertisedUploadSpeedMbs;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getBroadbandSpeedRange()
    {
        return $this->broadbandSpeedRange;
    }

    /**
     * @param mixed $broadbandSpeedRange
     */
    public function setBroadbandSpeedRange($broadbandSpeedRange)
    {
        $this->broadbandSpeedRange = $broadbandSpeedRange;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMinimumEstimatedDownloadSpeedMbs()
    {
        return $this->minimumEstimatedDownloadSpeedMbs;
    }

    /**
     * @param mixed $minimumEstimatedDownloadSpeedMbs
     */
    public function setMinimumEstimatedDownloadSpeedMbs($minimumEstimatedDownloadSpeedMbs)
    {
        $this->minimumEstimatedDownloadSpeedMbs = $minimumEstimatedDownloadSpeedMbs;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMaximumEstimatedDownloadSpeedMbs()
    {
        return $this->maximumEstimatedDownloadSpeedMbs;
    }

    /**
     * @param mixed $maximumEstimatedDownloadSpeedMbs
     */
    public function setMaximumEstimatedDownloadSpeedMbs($maximumEstimatedDownloadSpeedMbs)
    {
        $this->maximumEstimatedDownloadSpeedMbs = $maximumEstimatedDownloadSpeedMbs;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMinimumEstimatedUploadSpeedMbs()
    {
        return $this->minimumEstimatedUploadSpeedMbs;
    }

    /**
     * @param mixed $minimumEstimatedUploadSpeedMbs
     */
    public function setMinimumEstimatedUploadSpeedMbs($minimumEstimatedUploadSpeedMbs)
    {
        $this->minimumEstimatedUploadSpeedMbs = $minimumEstimatedUploadSpeedMbs;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMaximumEstimatedUploadSpeedMbs()
    {
        return $this->maximumEstimatedUploadSpeedMbs;
    }

    /**
     * @param mixed $maximumEstimatedUploadSpeedMbs
     */
    public function setMaximumEstimatedUploadSpeedMbs($maximumEstimatedUploadSpeedMbs)
    {
        $this->maximumEstimatedUploadSpeedMbs = $maximumEstimatedUploadSpeedMbs;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMaximumDownloadSpeedMbs()
    {
        return $this->maximumDownloadSpeedMbs;
    }

    /**
     * @param mixed $maximumDownloadSpeedMbs
     */
    public function setMaximumDownloadSpeedMbs($maximumDownloadSpeedMbs)
    {
        $this->maximumDownloadSpeedMbs = $maximumDownloadSpeedMbs;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getGuaranteedSpeedAvailable()
    {
        return $this->guaranteedSpeedAvailable;
    }

    /**
     * @param mixed $guaranteedSpeedAvailable
     */
    public function setGuaranteedSpeedAvailable($guaranteedSpeedAvailable)
    {
        $this->guaranteedSpeedAvailable = $guaranteedSpeedAvailable;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getGuaranteedSpeedValue()
    {
        return $this->guaranteedSpeedValue;
    }

    /**
     * @param mixed $guaranteedSpeedValue
     */
    public function setGuaranteedSpeedValue($guaranteedSpeedValue)
    {
        $this->guaranteedSpeedValue = $guaranteedSpeedValue;
        $this->setGuaranteedSpeedAvailable(guaranteedSpeedValue !== null);

        return $this;
    }

    /**
     * @param string $fttpInstallationType fttp installation type
     * @return void
     */
    public function setFttpInstallationType($fttpInstallationType)
    {
        $this->fttpInstallationType = $fttpInstallationType;
    }

    /**
     * @param bool $accessTechnologyChanging is access technology changing for this order
     * @return void
     */
    public function setAccessTechnologyChanging($accessTechnologyChanging)
    {
        $this->accessTechnologyChanging = $accessTechnologyChanging;
    }

    /**
     * Sets the new product type as a SoGEA product
     * @param bool $isSogea value representing is the product is SoGEA or not
     * @return mixed
     */
    public function setSogeaProduct($isSogea)
    {
        $this->isSogea = $isSogea;

        return $this;
    }

    /**
     * Sets the new product type as a FTTP product
     * @param bool $isFttp value representing is the product is FTTP or not
     * @return mixed
     */
    public function setFttpProduct($isFttp)
    {
        $this->isFttp = $isFttp;

        return $this;
    }

    /**
     * Sets the new product type as a FTTC product
     * @param bool $isFttc value representing is the product is FTTC or not
     * @return mixed
     */
    public function setFttcProduct($isFttc)
    {
        $this->isFttc = $isFttc;

        return $this;
    }

    /**
     * @return string
     */
    public function getFttpInstallationType()
    {
        return $this->fttpInstallationType;
    }

    /**
     * @return bool
     */
    public function isAccessTechnologyChanging()
    {
        return $this->accessTechnologyChanging;
    }

    /**
     * @return bool
     */
    public function isSogeaProduct()
    {
        return $this->isSogea;
    }

    /**
     * @return bool
     */
    public function isFttpProduct()
    {
        return $this->isFttp;
    }

    /**
     * @return bool
     */
    public function isFttcProduct()
    {
        return $this->isFttc;
    }
}
