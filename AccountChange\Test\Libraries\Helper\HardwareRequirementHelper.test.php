<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_HardwareRequirementHelper_Test extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 1234;
    const OLD_SERVICE_DEF_ID = 1234;
    const NEW_SERVICE_DEF_ID = 4321;
    const VISP = 'plus.net';
    const REPORT_SECTOR = 'residential';

    /**
     * @var HardwareClient_Client|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockHardwareClient;

    /**
     * @var AccountChange_FibreHelper|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockFibreHelper;

    /**
     * @var AccountChange_HardwareRequirementHelper
     */
    private $test;

    /**
     * @var HardwareClient_Bundle|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockHardware;
    /**
     * @var Core_Service|\Mockery\LegacyMockInterface|\Mo<PERSON><PERSON>\MockInterface
     */
    private $mockCoreService;

    /**
     * @var HardwareClient_HardwareToggleHelper|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockToggleHelper;

    /**
     * @var HardwareClient_HardwareOfferHelper|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockHardwareOfferHelper;

    /**
     * @var HardwareClient_HardwareOffer|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockHardwareOffer;

    /**
     * setUp
     */
    public function setUp()
    {
        $this->mockHardwareClient = \Mockery::mock(HardwareClient_Client::class);
        $this->mockFibreHelper = \Mockery::mock(AccountChange_FibreHelper::class);
        $this->mockHardware = \Mockery::mock(HardwareClient_Bundle::class);
        $this->mockCoreService = \Mockery::mock(Core_Service::class);
        $this->mockToggleHelper = \Mockery::mock(HardwareClient_HardwareToggleHelper::class);
        $this->mockHardwareOfferHelper = \Mockery::mock(HardwareClient_HardwareOfferHelper::class);
        $this->mockHardwareOffer = \Mockery::mock(HardwareClient_HardwareOffer::class);

        $this->mockHardwareClient->shouldReceive('getHardwareToggleHelper')
            ->with(static::VISP, static::REPORT_SECTOR)
            ->andReturn($this->mockToggleHelper);

        $this->mockHardwareClient->shouldReceive('getHardwareOfferHelper')
            ->andReturn($this->mockHardwareOfferHelper);

        $this->mockCoreService->shouldReceive('getType')
            ->andReturn(static::OLD_SERVICE_DEF_ID);

        $this->mockCoreService->shouldReceive('getServiceId')
            ->andReturn(static::SERVICE_ID);

        $this->mockCoreService->shouldReceive('getIsp')
            ->andReturn(static::VISP);

        $this->mockCoreService->shouldReceive('getReportSector')
            ->andReturn(static::REPORT_SECTOR);
    }

    public function tearDown()
    {
        $this->test = null;
        \Mockery::close();
    }

    /**
     * @return AccountChange_HardwareRequirementHelper
     */
    private function setupTest()
    {
        return new AccountChange_HardwareRequirementHelper(
            $this->mockHardwareClient,
            $this->mockCoreService,
            false,
            true,
            static::NEW_SERVICE_DEF_ID,
            $this->mockFibreHelper
        );
    }

    /**
     * @test
     * @dataProvider useHelperToggleProvider
     * @param $oldIsFibreProduct
     * @param $newIsFibreProduct
     * @param $expectedToggleResult
     * @param $expectedResult
     */
    public function shouldReturnUseHelperValueBasedOnToggleHelperResult(
        $oldIsFibreProduct,
        $newIsFibreProduct,
        $expectedToggleResult,
        $expectedResult
    ) {
        $this->mockFibreHelper
            ->shouldReceive('isFibreProduct')
            ->andReturn($oldIsFibreProduct, $newIsFibreProduct);

        $this->mockToggleHelper->shouldReceive('checkAllToggles')
            ->once()
            ->andReturn($expectedToggleResult);

        $this->test = $this->setupTest();
        $this->assertEquals($expectedResult, $this->test->shouldUseHelper());
    }

    /**
     * @return array[]
     */
    public function useHelperToggleProvider()
    {
        return [
            'Off: C2C' =>
                [false, false, false, false],
            'Off: C2FTTC' =>
                [false, true, false, false],
            'Off: FTTC2FTTC' =>
                [true, true, false, false],
            'Off: FTTC2C' =>
                [true, false, false, false],
            'On : C2C' =>
                [false, false, true, true],
            'On : C2FTTC' =>
                [false, true, true, true],
            'On : FTTC2FTTC' =>
                [true, true, true, true],
            'On : FTTC2C' =>
                [true, false, true, true],
        ];
    }

    /**
     * @test
     * @dataProvider hardwareMessageDisplayProvider
     * @param $hubTwoToggleValue
     * @param $expectedResult
     */
    public function shouldReturnDisplayHardwareMessageValueBasedOnToggleHelperResult(
        $hubTwoToggleValue,
        $expectedResult
    ) {
        $this->mockFibreHelper
            ->shouldReceive('isFibreProduct')
            ->andReturn(false, true);

        $this->mockToggleHelper->shouldReceive('isHubTwoToggleOn')
            ->once()
            ->andReturn($hubTwoToggleValue);

        $this->test = $this->setupTest();
        $this->assertEquals($expectedResult, $this->test->shouldDisplayHardwareMessage());
    }

    /**
     * @return array
     */
    public function hardwareMessageDisplayProvider()
    {
        return [
            'Toggle on show message' => [true, true],
            'Toggle off dont message' => [false, false]
        ];
    }

    /**
     * @test
     * @dataProvider shouldOfferHardwareProvider
     * @param $canOfferHardware
     * @param $shouldOfferHardware
     * @param $expectedResult
     */
    public function shouldReturnDisplayHardwarePageValueFromOfferHelper(
        $canOfferHardware,
        $shouldOfferHardware,
        $expectedResult
    ) {
        $this->mockFibreHelper
            ->shouldReceive('isFibreProduct')
            ->andReturn(false, true);

        $this->mockHardwareOfferHelper
            ->shouldReceive('canOfferHardware')
            ->once()
            ->andReturn($canOfferHardware);

        if ($canOfferHardware) {
            $this->mockHardwareOfferHelper
                ->shouldReceive('getHardwareOfferDetails')
                ->once()
                ->andReturn($this->mockHardwareOffer);

            $this->mockHardwareOffer
                ->shouldReceive('shouldOfferHardware')
                ->once()
                ->andReturn($shouldOfferHardware);
        }

        $this->test = $this->setupTest();
        $this->assertEquals($expectedResult, $this->test->shouldShowHardwarePage());
    }

    /**
     * @return array
     */
    public function shouldOfferHardwareProvider()
    {
        return [
            'can, will, offer' => [true, true, true],
            'can, will not, no offer' => [true, false, false],
            'can not, irrelevant, no offer' => [false, null, false]
        ];
    }

    /**
     * @test
     */
    public function shouldReturnHardwareOffer()
    {
        $this->mockFibreHelper
            ->shouldReceive('isFibreProduct')
            ->andReturn(false, true);

        $this->mockHardwareOfferHelper
            ->shouldReceive('getHardwareOfferDetails')
            ->once()
            ->andReturn($this->mockHardwareOffer);

        $this->test = $this->setupTest();
        $this->assertEquals($this->mockHardwareOffer, $this->test->getHardwareOffer());
    }

    public function invokePrivateMethod(&$object, $methodName, array $parameters = array())
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $parameters);
    }

    public function testIsPortalShouldReturnTrueForDifferentServicesOnPortal()
    {
        $expectedResult = true;
        $this->mockFibreHelper
            ->shouldReceive('isFibreProduct')
            ->times(2)
            ->andReturn(true, true);
        $this->mockCoreService->shouldReceive('getType')
            ->andReturn(static::OLD_SERVICE_DEF_ID);
        $hardwareHelper = new AccountChange_HardwareRequirementHelper(
            $this->mockHardwareClient,
            $this->mockCoreService,
            false,
            false,
            static::NEW_SERVICE_DEF_ID,
            $this->mockFibreHelper,
            true
        );
        $actualResult = $this->invokePrivateMethod($hardwareHelper, 'determineReContract');

        $this->assertEquals($expectedResult, $actualResult);
    }

}
