CREATE TABLE `products`.`tblPromotionCode` (
  `intPromotionCodeId` int(10) unsigned NOT NULL auto_increment,
  `vchPromotionCode` varchar(16) NOT NULL default '',
  `intPresetDiscountId` int(10) unsigned NOT NULL default '0',
  `dteValidFrom` datetime default NULL,
  `dteValidTo` datetime default NULL,
  `vchDescription` varchar(255) default NULL,
  `stmTimestamp` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (`intPromotionCodeId`),
  KEY `idxVchPromotionCode` (`vchPromotionCode`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1