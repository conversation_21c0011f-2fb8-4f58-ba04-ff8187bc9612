server: coredb
role: slave
rows: multiple
statement:

SELECT
	scs.schedule_id AS intServiceChangeScheduleId,
	scs.intTariffID AS intNewTariffId,
	sd_new.name AS strNewProductName,
	scs.new_type AS intNewSdi,
	sd_old.name AS strOldProductName,
	scs.old_type AS intOldSdi,
	s.service_id AS intServiceId,
	s.type AS intCurrentSdi,
	scs.change_complete_date AS dteChangeComplete,
	scs.change_date as dteChangeDate,
	cl.vchHandle as strContractLengthHandle,
	pc.vchPromotionCode as promoCode,
    scs.agreementDate as agreementDate,
    scs.contractType as contractType,
    scs.requiresBroadbandOrder

FROM
	userdata.service_change_schedule scs 
	INNER JOIN products.service_definitions sd_new ON (sd_new.service_definition_id = scs.new_type) 
	INNER JOIN products.tblProductVariant pv ON (pv.intProductVariantId = sd_new.intProductVariantId) 
	INNER JOIN products.tblProductFamily pf USING (intProductFamilyId) 
	INNER JOIN userdata.services s ON (s.service_id = scs.service_id) 
	INNER JOIN products.service_definitions sd_old ON (sd_old.service_definition_id = scs.old_type)
	INNER JOIN products.adsl_product ap ON (ap.service_definition_id = scs.old_type)
	LEFT JOIN dbProductComponents.tblContractLength cl ON (scs.intContractLengthID = cl.intContractLengthID)
	LEFT JOIN products.tblPromotionCode pc ON (scs.intPromotionCodeId = pc.intPromotionCodeId)

WHERE
	pf.vchHandle != 'PARTNER_ADMIN'
	AND sd_new.type IN ('business', 'residential')
	AND sd_old.type IN ('business', 'residential')
	AND scs.active = 'yes'
	AND scs.change_complete = 'no'
	AND s.status IN ('queued-activate','queued-reactivate','active')
	AND scs.change_date = date(now())
	AND (NOT :bolRestrict OR s.service_id IN (:arrRestrictToServiceIds))
