<?php

/**
 * Class AccountChange_SalesJourneyViewHelper
 *
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_SalesJourneyViewHelper
{
    /**
     * Add the name of the validated view to the completed view array.
     *
     * When a view is completed it can be hyperlinked to in the progress bar.
     * The progress bar will use the 'completedSteps' value to identify which
     * steps can be hyperlinked.
     *
     * @param array $views
     */
    public static function setCompletedViews(array $views = array())
    {
        $completedSteps = self::getCompletedViews();

        if (empty($completedSteps)){
            $completedSteps = [];
        }

        $completedSteps = array_merge($completedSteps, $views);

        AccountChange_Registry::instance()->setEntry(
          'completedViews',
          implode(',', $completedSteps)
        );
    }

    public static function getCompletedViews()
    {
        return explode(',',  AccountChange_Registry::instance()->getEntry('completedViews'));
    }


    public static function getWizardUri($app)
    {
        if(!isset($_SESSION['PNET_FRAMEWORK_.plus.net']) && !isset($_SESSION['PNET_FRAMEWORK_.plus.net'][$app . '_Controller'])){
            return '/';
        }
        return $_SESSION['PNET_FRAMEWORK_.plus.net'][$app . '_Controller']->getAppUri();
    }
    /**
     * Get the ID of this instance of the Wizard
     *
     * @return int
     */
    public static function getWizardInstanceID($app)
    {
        if(!isset($_SESSION['PNET_FRAMEWORK_.plus.net']) && !isset($_SESSION['PNET_FRAMEWORK_.plus.net'][$app. '_Controller'])){
            return 0;
        }
        return (int) $_SESSION['PNET_FRAMEWORK_.plus.net'][$app. '_Controller']->getWizardInstanceId();
    }
}
