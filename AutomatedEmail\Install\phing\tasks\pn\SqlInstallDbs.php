<?php
include_once 'phing/Task.php';
class SqlInstallDbs extends Task {

    private $_strReturnName;
    private $_strAction;
    private $_strDir;
    private $_strDb;
    private $_strHost;

    public function setReturnName($str) {
        $this->_strReturnName = $str;
    }    
    public function setAction($str) {
        $this->_strAction = $str;
    }
    
    public function setHost($str) {
        $this->_strHost = $str;
    }
    
    public function setDir($str) {
        $this->_strDir = $str;
    }
    
    public function setDb($str) {
        $this->_strDb = $str;
    }
    
    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		} 
		 
		if(isset($this->_strAction)) {

			switch($this->_strAction) {
				
				case 'checkHost':
					$this->_checkHost();
					return;
				case 'getDbNames':
					$this->_getDbNames();
					return;
				case 'checkDb':
					$this->_checkDb();
					return;
				case 'createDb':
					$this->_createDb();
					return;
				case 'createTables':
					$this->_createTables();
					return;
				case 'populateTables':
					$this->_populateTables();
					return;
				case 'verifyTables':
					$this->_verifyTables();
					return;
			}

			throw new BuildException("$this->_strAction is not a valid action for sqlInstallDbs", $this->getLocation());		
		}
    }

	private function _checkHost()
	{
		if (!$this->_strHost) {
		    throw new BuildException("Host name is missing", $this->getLocation());
		} 
		//temporary solution
		if (!@include('/local/data/mis/database/database_local.inc')) {
		    throw new BuildException("Failed to include database_local.inc", $this->getLocation());
		} 
		
		$dbData = $databases[$this->_strHost];

		$conn = mysql_connect($dbData['hostname'], $dbData['admin_user'][0], $dbData['admin_user'][1]);
		if (!$conn) {
   			throw new BuildException("Connection to $this->_strHost failed.", $this->getLocation());
		}
		$this->project->setProperty('dbData', $dbData);
		$this->project->setProperty('conn', $conn);
		$this->project->setProperty($this->_strReturnName, "Host $this->_strHost is accessible.");		
	}
	
	private function _getDbNames() 
	{
		$arrScanDir = scandir($this->project->getProperty('project.basedir'));
		
		$arrMatches = array();
		$arrDbNames = array();
		foreach ($arrScanDir as $strFile) {
			
			if(preg_match('/(\w+).structure.sql/', $strFile, $arrMatches)) {
				$arrDbNames[] = $arrMatches[1];
			}	
		}
		if (empty($arrDbNames)) {
			$this->log('No sql files found.');
		}
		$strReturn = implode(',',$arrDbNames);
		$this->project->setProperty($this->_strReturnName, $strReturn);
		
	}    
    
    private function _checkDb()
    {
		if (!$this->_strDb) {
		    throw new BuildException("Db name is missing", $this->getLocation());
		} 

		$conn = $this->project->getProperty('conn');
		
		if (mysql_select_db($this->_strDb, $conn)) {
		   $this->project->setProperty($this->_strReturnName, "Database $this->_strDb exists.");
		}
    }
    
    private function _createDb()
    {
		if (!$this->_strDb) {
		    throw new BuildException("Db name is missing", $this->getLocation());
		} 

		$conn = $this->project->getProperty('conn');
		$query = "CREATE DATABASE $this->_strDb";
		
		if (mysql_query($query, $conn)) {
			$this->project->setProperty($this->_strReturnName, "Database $this->_strDb created.");
		} else {
			throw new BuildException(mysql_error(), $this->getLocation());
		}
    }

	private function _createTables()
	{
		if (!$this->_strDb) {
		    throw new BuildException("Db name is missing", $this->getLocation());
		} 
		
		$dbData = $this->project->getProperty('dbData');
		
		$sqlFileName = $this->project->getProperty('project.basedir').'/'.$this->_strDb.'.structure.sql';
		
		system("mysql -h{$dbData['hostname']} -u{$dbData['admin_user'][0]} -p{$dbData['admin_user'][1]} $this->_strDb < $sqlFileName");		
	}    

	private function _populateTables()
	{
		if (!$this->_strDb) {
		    throw new BuildException("Db name is missing", $this->getLocation());
		} 
		
		$arrScanDir = scandir($this->project->getProperty('project.basedir'));
		
		$arrMatches = array();
		$arrPopulateFiles = array();
		foreach ($arrScanDir as $strFile) {

			if(preg_match("/$this->_strDb.populate(\d+).sql/", $strFile, $arrMatches)) {
				$arrPopulateFiles[$arrMatches[1]] = $arrMatches[0];
			}	
		}
		
		natsort($arrPopulateFiles);
		$dbData = $this->project->getProperty('dbData');
			
		foreach ($arrPopulateFiles as $strFilename) {
			$this->log("Using file $strFilename");				
			$sqlFileName = $this->project->getProperty('project.basedir').'/'.$strFilename;
			system("mysql -h{$dbData['hostname']} -u{$dbData['admin_user'][0]} -p{$dbData['admin_user'][1]} $this->_strDb < $sqlFileName");	
		}
				
	}    

	private function _verifyTables()
	{
		if (!$this->_strDb) {
		    throw new BuildException("Db name is missing", $this->getLocation());
		} 
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}  		
		
		$dbData = $this->project->getProperty('dbData');
		$arrDump = array();
		
		exec("mysqldump -h{$dbData['hostname']} -udbuser -pdbuser $this->_strDb --compact -d", $arrDump);
		
		$file = file($this->project->getProperty('project.basedir').'/'.$this->_strDb.'.structure.sql');
		
		$arrDumpStms = $this->_processSqlDump($arrDump);
		$arrFileStms = $this->_processSqlDump($file);
		
		$arrDifference = array_diff($arrDumpStms, $arrFileStms);
		if(!empty($arrDifference))	{
			$failedTables =  implode(', ',array_keys($arrDifference));
			$this->project->setProperty($this->_strReturnName, "Structure check on database $this->_strDb failed. Differences found in tables: $failedTables.");	
		}
	}
	
	private function _processSqlDump($arrDump)
	{
		foreach ($arrDump as &$line) {
			//remove constraint and whitespace
			$line = preg_replace('/CONSTRAINT `\w*`|\s/','',$line);
		}
		//combine lines
		$str = implode($arrDump);
		//seperate statements
		$arrStms = explode(';',$str);

		$arrCreateStms = array();
		foreach ($arrStms as $strStmt) {
			$arrMatches = array();
			//extract tables names and statements
			if(preg_match('/^CREATETABLE`(\w+)`\S*$/',$strStmt, $arrMatches)) {
				
				$arrCreateStms[$arrMatches[1]] = $arrMatches[0];
			} 
		}		
		return $arrCreateStms;
	}    
}