<?php
/**
 * Product Configuration for Community component
 *
 * Holds information about a service definition product than an account may have
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_Community.class.php,v 1.2 2009-01-27 07:07:21 bselby Exp $
 * @since     File available since 2008-08-27
 */
/**
 * Product Community class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_Community extends AccountChange_Product_ServiceComponent
{
    /**
     * Is the product a key product
     *
     * @var boolean
     */
    protected $bolKeyProduct = false;

    /**
     * Is the product a free product
     *
     * @var boolean
     */
    protected $bolFreeProduct = false;

    /**
     * Initialise the community product
     *
     * @param int   $intServiceComponentId service component Id
     * @param int   $intAction             action
     * @param array $arrOptions            options array
     *
     * @return void
     */
    public function initialise($intServiceComponentId, $intAction, array $arrOptions)
    {
        parent::initialise($intServiceComponentId, $intAction, $arrOptions);

        if (isset($arrOptions['bolFreeProduct'])) {

            $this->bolFreeProduct = $arrOptions['bolFreeProduct'];
        }
    }

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration the product configuration
     *
     * @return void
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {

            $this->objNewProductConfiguration = $objAccountConfiguration->getProductConfigurationByType(
                AccountChange_Product_Manager::PRODUCT_TYPE_COMMUNITY
            );
        }
    }

    /**
     * Perform a refresh on the product component
     *
     * @return void
     */
    protected function refresh()
    {
        // No code coverage for this code as it is calling legacy functions which we cannot mock
        $this->includeLegacyFiles();

        $objCommunity = new CommunitySite();
        $objCommunity->initialize();
        $strUsername = $objCommunity->getUsernameFromComponentID($this->intComponentId);
        $objCommunity->refresh($strUsername, $this->bolFreeProduct);
    }

    /**
     * Need to include the legacy files, but we need to be able to mock it for the unit tests
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/application_apis/CommunitySite.class.php';
    }
}
