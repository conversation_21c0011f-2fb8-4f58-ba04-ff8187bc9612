<?php
/**
 * AccountChange DirectDebitDetails Requirement Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-23
 */
/**
 * AccountChange DirectDebitDetails Requirement Test
 *
 * Test class for AccountChange_DirectDebitDetails Requirement
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_DirectDebitDetails_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test the direct debit name validation
     *
     * @covers AccountChange_DirectDebitDetails::valDirectDebitName
     *
     * @dataProvider provideDataForValDirectDebitNameForValidation
     *
     * @return void
     */
    public function testValDirectDebitNameWithData($name, $expected, $errors)
    {
        $requirement = $this->getMock(
            'AccountChange_DirectDebitDetails',
            array('addValidationError'),
            array()
        );

        $requirement->expects($this->any())
                    ->method('addValidationError');

        $output = $requirement->valDirectDebitName($name);
        $actualErrors = $requirement->getValidationErrors();

        $this->assertEquals($expected, $output);
        $this->assertEquals($errors, $actualErrors);
    }

    /**
     * Data provider
     *
     * @return void
     */
    public static function provideDataForValDirectDebitNameForValidation()
    {
        return array(
            array('', array('strDirectDebitName' => ''), array('strDirectDebitName' => array('MISSING' => true))),
            array('Test Name', array('strDirectDebitName' => new Val_Name('Test Name')), array()),
            array('@Bad !? Name@�', array('strDirectDebitName' => array('INVALID' => true)), array(
                'strDirectDebitName' => array('INVALID' => true)
            )),
        );
    }

    /**
     * Test the direct debit sort code validation
     *
     * @covers AccountChange_DirectDebitDetails::valDirectDebitSortCode
     *
     * @dataProvider provideDataForValDirectDebitSortCodeForValidation
     *
     * @return void
     */
    public function testValDirectDebitSortCodeWithData($code1, $code2, $code3, $expected, $errors)
    {
        $requirement = $this->getMock(
            'AccountChange_DirectDebitDetails',
            array('addValidationError'),
            array()
        );

        $requirement->expects($this->any())
                    ->method('addValidationError');

        $output = $requirement->valDirectDebitSortCode($code1, $code2, $code3);
        $actualErrors = $requirement->getValidationErrors();

        $this->assertEquals($expected, $output);
        $this->assertEquals($errors, $actualErrors);
    }

    /**
     * Data provider
     *
     * @return void
     */
    public static function provideDataForValDirectDebitSortCodeForValidation()
    {
        return array(
            array(10, 20, 30, array('intDirectDebitSortCode1' => 10,
                                    'intDirectDebitSortCode2' => 20,
                                    'intDirectDebitSortCode3' => 30,
                                    'objDirectDebitSortCode' => new Val_DirectDebit_SortCode(10, 20, 30)),
                              array()),
            array(1, 2, 3, array('intDirectDebitSortCode1' => '',
                                 'intDirectDebitSortCode2' => '',
                                 'intDirectDebitSortCode3' => '',
                                 'objDirectDebitSortCode' => ''),
                              array('intDirectDebitSortCode' => array('INVALID' => true))),
        );
    }

    /**
     * Test the direct debit account number validation
     *
     * @covers AccountChange_DirectDebitDetails::valDirectDebitAccountNumber
     *
     * @dataProvider provideDataForValDirectDebitAccountNumberForValidation
     *
     * @return void
     */
    public function testValDirectDebitAccountNumberWithData($accountNumber, $expected, $errors)
    {
        $requirement = $this->getMock(
            'AccountChange_DirectDebitDetails',
            array('addValidationError'),
            array()
        );

        $requirement->expects($this->any())
                    ->method('addValidationError');

        $output = $requirement->valDirectDebitAccountNumber($accountNumber);
        $actualErrors = $requirement->getValidationErrors();

        $this->assertEquals($expected, $output);
        $this->assertEquals($errors, $actualErrors);
    }

    /**
     * Data provider
     *
     * @return void
     */
    public static function provideDataForValDirectDebitAccountNumberForValidation()
    {
        return array(
            array('', array('intDirectDebitAccountNumber' => ''), array(
                'intDirectDebitAccountNumber' => array('MISSING' => true)
            )),
            array('********', array(
                'intDirectDebitAccountNumber' => new Val_DirectDebit_AccountNumber('********')
            ),
            array()),
            array('BadAccountNumber', array(
                'intDirectDebitAccountNumber' => array('INVALID' => true)),
                array('intDirectDebitAccountNumber' => array('INVALID' => true))
            ),
        );
    }
}
