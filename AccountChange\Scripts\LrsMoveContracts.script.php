<?php
/**
 * <PERSON><PERSON><PERSON> to look for accounts that have active line rental saver contracts that need redeeming
 *
 * Usage:
 * php /local/codebase2005/modules/Framework/Scripts/RunScript.php -c \
 *     AccountChange_LrsMoveContracts -p PLUSNET | \
 *     /share/admin/portal/crontab/cronlog 323AccountChange_LrsMoveContracts
 *
 * It will look for all accounts that need renewing.
 * You can provide multiple -s params - they are all get added.
 * I.e. passing -s 12 -s 2343 -s 45245 will restrct the change to 3 sids
 *
 * If executed with one or more -l[filename] flags will process all service
 * ids listed one per line in that file.  Header on first line is optional.
 * e.g, pass:
 *    -l/pathToFIle1 -l/path/to/file2 -l/path/to/file3  etc
 * This can be used with or without the -s flag
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 PlusNet
 * @since     File available since 2011-12-08
 */

/**
 * AccountChange Script to redeem LRS prepaid contracts
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 PlusNet
 */
class AccountChange_LrsMoveContracts implements Mvc_Script
{
    /**
     * Output buffer
     * @var array
     */
    private $output = array();

    /**
     * Array of service ids we want to perform on
     * @var array
     */
    private $arrRestrictTo = array();

    /**
     * How many account changes failures have to occure before we halt the script
     * @var int
     */
    const FAILURES_TRESHOLD = 10;

    /**
     * This will be used in main billing.
     * this is a timestamp file only and should NOT be deleted.
     */
    const TIMESTAMP_FILE = 'timestamp';

    /**
     * constructor
     *
     * Do not pass any args here
     *
     */
    public function __construct()
    {

    }

    /**
     * Getter for arrRestrictTo
     *
     * @return array
     */
    public function getRestrictTo()
    {
        return $this->arrRestrictTo;
    }

    /**
     * Setup output buffer to dump line by line
     *
     * @return void
     **/
    protected function setupOutputBuffer()
    {
        ob_implicit_flush(true);
        ob_end_flush();
        set_time_limit(0);
    }

    /**
     * Tone down the error reporting from strict, as some of the
     * legacy calls required for creating scheduled payments
     * cause warnings.
     *
     * @return void
     **/
    protected function toneDownErrorReporting()
    {
        // Tone down the error handling whilst we play in the legacy codebase
        $previousErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_STRICT);
    }

    /**
     * Get Short Options
     *
     * @return string
     */
    public static function getShortOpts()
    {
        return 's:ql:';
    }

    /**
     * Get Long Options
     *
     * @return array
     */
    public static function getLongOpts()
    {
        return array();
    }

    /**
     * Runs the script
     *
     * @param array $args Parameters passed to the script
     * @param array $opts Flagged Arguments
     *
     * @return void
     */
    public function run(array $args, array $opts)
    {

        $this->setupOutputBuffer();
        $this->toneDownErrorReporting();

        $this->processCommandLineArgs($opts);
        $this->output('Command line params processed.', true);

        $restrictTo = $this->getRestrictTo();

        if (empty($restrictTo)) {
            $this->output('No restictions applied.', true);
            $accountsToProcess = $this->getAccounts();
            $this->output('Found ' . count($accountsToProcess) . ' accounts to process.', true);
        } else {
            $this->output('Restricted to accounts specified on command line.', true);
            $accountsToProcess = $restrictTo;
        }

        foreach ($accountsToProcess as $serviceId) {

            if (Financial_BillingRunExclusion::shouldExcludeFromBillingRun($serviceId)) {
                $this->output("$serviceId: The account excluded from Billing Run", true);
                continue;
            }
            try {
                $this->processAccount($serviceId);
                $this->output("[SUCCESS] $serviceId", true);
            } catch (Exception $e) {
                $this->output("[ERROR]   $serviceId: {$e->getMessage()}", true);
            }
        }

        $this->output("End of processing.", true);
    }

    /**
     * Get accounts to process from a query
     *
     * @return array Array of service ids
     **/
    public function getAccounts()
    {
        $adaptor = $this->getDbAdaptor();

        return $adaptor->getServicesWithPrepaidContractsThatNeedRedeeming();
    }

    /**
     * Returns a new Core_Service object
     *
     * @param int $serviceId Service id
     *
     * @return Core_Service
     */
    protected function getCoreService($serviceId)
    {
        $this->includeLegacyFiles();

        return new Core_Service($serviceId);
    }

    /**
     * Get the old expiry date of a canceled contract
     * Used when copying a destroyed LRS instance over
     * to a new component
     *
     * @param int $pcid Destroyed lrs product component instance id
     *
     * @return string Mysql formatted date
     **/
    protected function getEndDateOfDestroyedLrsInstance($pcid)
    {
        $adaptor = $this->getDbAdaptor();

        return $adaptor->getEndDateOfDestroyedLrsInstance($pcid);
    }

    /**
     * Locates any destroyed lrs instances on another component that are still
     * in contract and transfers to the new component.
     *
     * @param int $serviceId Service id
     *
     * @return void
     **/
    public function findLrsDestroyedByAccountChangeAndTransfer($serviceId)
    {
        $this->output('Looking for old lrs instance that needs transfering to the new component..', true);

        $instance = $this->getPrepaidContractInstance($serviceId, 'DESTROYED');

        if (!$instance instanceof PrepaidContract_Instance) {
            return false;
        }

        $pcid = $instance->getProductComponentInstanceId();
        $endDate = $this->getEndDateOfDestroyedLrsInstance($pcid);

        // It's possible to pick up on a destroyed instance even if lrs was never in contract
        // (as all accounts get inactive lrs by default, which will get switched to destroyed on
        // a/c change).  If we don't get an end date, then there was never a contract and hence
        // nothing to do here..
        if (!$endDate instanceof I18n_Date) {
            $this->output('An active line rental saver was not found to transfer.. adding a new pending one.', true);

            return false;
        }

        $this->output('Old instance found to transfer with end date of ' . $endDate->toMySql(), true);

        $newInstance = $this->getPrepaidContractInstance($serviceId, 'ACTIVE');

        if (!$newInstance instanceof PrepaidContract_Instance) {
            throw new RuntimeException('No new line rental saver instance to transfer to.. skipping.', true);
        }

        $tariff = $instance->getTariffByDuration('ANNUAL');
        $newInstance->transfer($tariff['intTariffID'], $endDate);
        $this->output('Transferred lrs.', true);

        // It's possible that we've transferred an lrs that's not yet been paid for.
        // Check here and see:
        $coreService = $this->getCoreService($serviceId);
        $billingDate = $coreService->getBillingDate();
        if ($billingDate == '1970-01-01' || $billingDate == '9999-99-99') {
            throw new UnexpectedValueException('Account has no billing date yet (probably in presignup) -- skipping.');
        }

        $billingDate = strtotime($billingDate);

        // Check they've not already paid for lrs..
        $scheduledPaymentId = $this->getAnyPaidOrPendingLrsScheduledPayments($serviceId);
        $invoiceId = $this->getLrsSalesInvoiceItem($serviceId);

        $hasLrsPayment = ($scheduledPaymentId || $invoiceId);

        $pcid = $newInstance->getProductComponentInstanceId();

        if (!$hasLrsPayment) {
            $this->output("Creating new scheuled payment", true);
            $scheduledPaymentId = $this->createScheduledPaymentForLrs(
                $pcid,
                $billingDate
            );
            $this->output("Created scheduled payment (scheduledPaymentId: $scheduledPaymentId)", true);

        } else {
            $this->output("Using existing scheduled payment / invoice", true);
            if (empty($scheduledPaymentId) && !empty($invoiceId)) {
                // Restore scheduled payment link if necessary
                $scheduledPaymentId = $this->_createFullyPaidScheduledPayment($invoiceId, $pcid);
                $this->output("Paid invoice had missing fully paid scheduled invoice - created", true);
            }
        }

        return true;
    }

    /**
     * Main account processor:
     *  1. Check accounts have an active line rental saver instance
     *  2. Create a scheduled payment for 113.88
     *  3. Add a pending entry and redeem
     *
     * @param int $serviceId Account to fix
     *
     * @return bool
     **/
    public function processAccount($serviceId)
    {

        $this->output("----------- Processing service id: {$serviceId} ----------- ", true);

        $instance = $this->getPrepaidContractInstance($serviceId, 'ACTIVE');

        if (!$instance instanceof PrepaidContract_Instance) {
            throw new UnexpectedValueException('Unable to locate an active lrs instance to copy from');
        }

        // Problem 74070 - don't run this script against any account where the LRS has expired and the customer
        // has not paid to renew it (previously it found the historic payment for LRS, assumed the contract hadn't
        // been setup and added a new one without charge).
        $currentPayments = $this->getCurrentLrsPayments($serviceId);

        if ($currentPayments == false) {
            throw new LogicException(
                'Skipping this account as there is no current payment for LRS, i.e it has not been renewed.'
            );
        }

        if ($this->findLrsDestroyedByAccountChangeAndTransfer($serviceId)) {
            // We've transferred at this point, so remove any superfluous line rental scheduled
            // payments and finish processing this account.
            $this->checkForAndCancelAnyLineRentalScheduledPayments($serviceId);

            return;
        }

        $pcid = $instance->getProductComponentInstanceId();

        if ($instance->hasActiveContract()) {
            throw new LogicException('Nothing to do as this instance already has an active contract.');
        }

        $coreService = $this->getCoreService($serviceId);
        $billingDate = $coreService->getBillingDate();
        if ($billingDate == '1970-01-01' || $billingDate == '9999-99-99') {
            throw new UnexpectedValueException('Account has no billing date yet (probably in presignup) -- skipping.');
        }
        $billingDate = strtotime($billingDate);

        // Check they've not already paid for lrs..
        $scheduledPaymentId = $this->getAnyPaidOrPendingLrsScheduledPayments($serviceId);
        $invoiceId = $this->getLrsSalesInvoiceItem($serviceId);

        $hasLrsPayment = ($scheduledPaymentId || $invoiceId);

        if (!$hasLrsPayment) {
            $this->output("Creating new scheuled payment", true);
            $scheduledPaymentId = $this->createScheduledPaymentForLrs(
                $pcid,
                $billingDate
            );
            $this->output("Created scheduled payment (scheduledPaymentId: $scheduledPaymentId)", true);

        } else {
            $this->output("Using existing scheduled payment / invoiced item", true);
            if (empty($scheduledPaymentId) && !empty($invoiceId)) {
                // Restore scheduled payment link if necessary
                $scheduledPaymentId = $this->_createFullyPaidScheduledPayment($invoiceId, $pcid);
                $this->output("Paid invoice had missing fully paid scheduled invoice - created", true);
            }
        }
        if (empty($scheduledPaymentId) || $scheduledPaymentId < 1) {
            throw new RuntimeException('Failed to create a scheduled payment, skipping');
        }

        // We've now created the scheduled payment, so if anything fails from now
        // on we'll need to cancel it..
        try {
            $tariff = $instance->getTariffByDuration('ANNUAL');
            $pendingEntry = $this->createPendingLrs($scheduledPaymentId, $tariff['intTariffID']);

            if (!$pendingEntry instanceof PrepaidContract_PendingEntry) {
                $this->output("Error - Cancelling the scheduled payment we've just created", true);
                $this->cancelScheduledPayment($scheduledPaymentId);
                throw new RuntimeException('Failed to create a new pending entry for lrs');
            }

            // Add the pending entry to the instance
            $instance->add($pendingEntry);

            $this->output("Scheduled payment successfully created.", true);

            // Ugly, but necessary as we need the pending entry to be committed at this point.
            Db_Manager::commit();

            $addedPendingContract = $instance->getPendingPrepaidContracts();

            // Will redeem the new contract (else throw an exception which we catch above)
            $instance->redeem($addedPendingContract[0]);
            $this->output("Pending payment redeemed", true);
        } catch (Exception $e) {
            // Cancel scheduled payment
            $this->output("Error - Cancelling the scheduled payment we've just created..", true);
            $this->cancelScheduledPayment($scheduledPaymentId);
            // Re-throw exception for the caller to deal with..
            throw $e;
        }

        $this->checkForAndCancelAnyLineRentalScheduledPayments($serviceId);
    }

    /**
     * Query the db to see if lrs is already paid for
     *
     * @param int $serviceId Service id
     *
     * @return bool
     **/
    public function getAnyPaidOrPendingLrsScheduledPayments($serviceId)
    {
        $adaptor = $this->getDbAdaptor();

        return $adaptor->getAnyPaidOrPendingLrsScheduledPayments($serviceId);
    }

    /**
     * Query to see if there's an invoice wjth an LRS line item
     *
     * @param int $serviceId Service id
     *
     * @return int
     **/
    public function getLrsSalesInvoiceItem($serviceId)
    {
        $adaptor = $this->getDbAdaptor();

        return $adaptor->getLrsSalesInvoiceItem($serviceId);
    }

    /**
     * Finds any pending scheduled payments for subscripton and
     * cancels them.
     *
     * @param int $serviceId The service id
     *
     * @return void
     **/
    public function checkForAndCancelAnyLineRentalScheduledPayments($serviceId)
    {
        $adaptor = $this->getDbAdaptor();
        $scheduledPaymentId = $adaptor->getWlrSubscriptionPendingScheduledPayment($serviceId);

        if (!empty($scheduledPaymentId)) {
            $this->cancelScheduledPayment($scheduledPaymentId);
            $this->output(
                "Found a line rental scheduled payment (id = $scheduledPaymentId), which has been cancelled",
                true
            );
        }
    }

    /**
     * Cancels a scheduled payment (wrapper to legacy)
     *
     * @param int $scheduledPaymentId Scheduled payment id
     *
     * @return void
     **/
    protected function cancelScheduledPayment($scheduledPaymentId)
    {
        $this->includeLegacyFiles();
        $scheduledPayment = CScheduledPayment::createInstance($scheduledPaymentId);
        $scheduledPayment->cancel();
    }

    /**
     * Creates a pending line rental saver entry
     *
     * @param $int $scheduledPaymentId Scheduled payment that covers lrs
     * @param $int $tariffId           Tariff id (annual)
     *
     * @return PrepaidContract_PendingEntry
     **/
    protected function createPendingLrs($scheduledPaymentId, $tariffId)
    {
        return PrepaidContract_Factory::createPendingEntry(
            $scheduledPaymentId,
            $tariffId
        );
    }

    /**
     * Creates a scheduled payment for LRS.
     *
     * @param $int $pcid        Product component instance id
     * @param $uxt $billingDate Timestamp of next billing date
     *
     * @return int
     **/
    public function createScheduledPaymentForLrs($pcid, $billingDate)
    {
        $scheduler = $this->getPaymentScheduler($pcid);
        $scheduledPaymentId = $scheduler->addIncVatAmount(
            11388,
            $billingDate,
            'Line Rental Saver charge'
        );

        return $scheduledPaymentId;
    }

    /**
     * Wrapper to get a legacy payment scheduler
     *
     * @param $int $pcid Product component instance id
     *
     * @return CProductComponentPaymentScheduler
     **/
    protected function getPaymentScheduler($pcid)
    {
        return new CProductComponentPaymentScheduler(
            $pcid,
            'b53331cedca4a1a791c46fde8a1bbc'
        );
    }

    /**
     * Gets a new prepaid contract instance
     *
     * @param int    $serviceId Service id
     * @param string $status    Status pci should be in
     *
     * @return PrepaidContract_Instance
     **/
    protected function getPrepaidContractInstance($serviceId, $status)
    {
        return PrepaidContract_Factory::getLineRentalSaver(
            $serviceId,
            $status
        );
    }

    /**
     * Get a db adaptor
     *
     * @return Db_Manager_Adaptor
     **/
    protected function getDbAdaptor()
    {
        return Db_Manager::getAdaptor('AccountChange');
    }

    /**
     * Process command line params
     *
     * @param array $opts Flagged Arguments
     *
     * @return void
     */
    public function processCommandLineArgs(array $opts)
    {
        if (empty($opts)) {
            return;
        }

        if (isset($opts['s'])) {

            $mixSids = $opts['s'];

            if (!is_array($mixSids)) {
                $mixSids = explode(',', $mixSids);
            }
            foreach ($mixSids as $intSid) {
                $this->checkAndAddSid($intSid);
            }

        }

        if (isset($opts['l'])) {
            $sidLists = $opts['l'];
            if (is_array($sidLists)) {
                foreach ($sidLists as $listFile) {
                    $this->_addSidsFromFile($listFile);
                }
            } else {
                $this->_addSidsFromFile($sidLists);
            }
        }
    }

    /**
     * Add service ids listed in a file
     * File should be a header, then service ids, one per line
     *
     * @param string $filename Filename of list file
     *
     * @return void
     **/
    private function _addSidsFromFile($filename)
    {

        if (!is_readable($filename)) {
            die("[ERROR] Input file $filename is not readable, please check this and run again.  Halting\n");
        }

        $list = file($filename);

        foreach ($list as $sid) {
            $this->checkAndAddSid(trim($sid));
        }

    }

    /**
     * Checks if sid is an integer
     *
     * @param int $intSid ID of the service
     *
     * @return boolean
     */
    protected function checkAndAddSid($intSid)
    {
        if (!is_numeric($intSid)) {
            $this->output("Can not restrict to $intSid - not a number.");

            return false;
        } else {
            if (isset($this->arrRestrictTo[$intSid])) {
                $this->output("Already added $intSid");
            } else {
                $this->output("Added sid restriction $intSid.");
            }
            //adding as a key will not allow duplicates
            $this->arrRestrictTo[$intSid] = $intSid;

            return true;
        }
    }

    /**
     * Allow Parrallel Runs
     *
     * @return bool
     */
    public function bolAllowParallelRuns()
    {
        return false;
    }

    /**
     * Function to output a formatted message to standard output
     * If second parameter is false, the message will be stored in internal buffer
     * and flushed when called with second param set to true
     *
     * @param string $message         a messager to output
     * @param bool   $immediateOutput output message or store it in a buffer
     *
     * @return void
     */
    public function output($message, $immediateOutput = false)
    {
        $this->output[] = date(DATE_COOKIE) . ': ' . $message;
        if ($immediateOutput) {
            foreach ($this->output as $line) {
                print "$line\n";
            }
            $this->output = array();
        }
    }

    /**
     * Create a scheduled payment for a component and mark it as paid
     *
     * @param int   $invoiceId      The invoice id that the payment app created
     * @param array $subscriptionId LRS product component instance id
     *
     * @return int The scheduled payment id
     **/
    private function _createFullyPaidScheduledPayment($invoiceId, $subscriptionId)
    {
        $this->includeLegacyFiles();

        $invoiceDescription = "Line rental saver";

        // Get the invoice details
        $invoiceDetails = financial_sales_invoice_get_full($invoiceId);

        // try and find out how much we charged for the thing
        $invoiceAmount = 0;
        foreach ($invoiceDetails["items"] as $invoiceItem) {

            if (strtoupper($invoiceDescription) == strtoupper(trim($invoiceItem["item_description"]))) {
                $invoiceAmount = $invoiceItem["value"];
                $invoiceDescription = $invoiceItem["item_description"];
                break;
            }
        }

        if ($invoiceAmount > 0) {

            $paymentScheduler = new CProductComponentPaymentScheduler($subscriptionId, SCRIPT_USER);

            $scheduledPaymentId = $paymentScheduler->addIncVatAmount(
                bcmul($invoiceAmount, 100),
                time(),
                $invoiceDescription
            );
            if (($scheduledPaymentId > 0) && ($invoiceId > 0)) {

                $lineItemId = $this->_getInvoiceLineItem($invoiceDetails, $invoiceDescription);

                if ($lineItemId > 0) {

                    $scheduledPayment = new CProductComponentScheduledPayment($scheduledPaymentId);
                    $scheduledPayment->markAsInvoiced($invoiceId, $lineItemId);

                    return $scheduledPaymentId;
                }
            }
        }

        return 0;
    }

    /**
     * Get the line item from the sales invoice that matches the description
     *
     * @param array  $invoice            Full invoice details
     * @param string $invoiceDescription The invoice description we're looking for
     *
     * @return int
     */
    private function _getInvoiceLineItem($invoice, $invoiceDescription)
    {
        $lineItemId = 0;

        foreach ($invoice["items"] as $lineItem) {

            if (stristr($lineItem["item_description"], $invoiceDescription)) {

                $lineItemId = $lineItem["sales_invoice_item_id"];
                break;
            }
        }
        unset($invoice);

        return $lineItemId;
    }

    /**
     * Find out if there's been a payment for LRS that covers the current time
     * i.e we could have made a payment in the past for an LRS subscription that's now expired.
     *
     * @param int $serviceId Service id
     *
     * @return bool
     **/
    protected function getCurrentLrsPayments($serviceId)
    {
        $adaptor = $this->getDbAdaptor();
        $unredeemedLrsContractPayment = $adaptor->getScheduledPaymentAgainstUnredeemedLrsContract($serviceId);
        $lrsPaymentNoContract = $adaptor->getScheduledPaymentAgainstNonExistingLrsContract($serviceId);
        if (!empty($unredeemedLrsContractPayment) || !empty($lrsPaymentNoContract)) {
            $this->output("$serviceId: account excluded from billing run due to some billing exception", true);

            return true;
        }

        return false;
    }

    /**
     * Includes legacy files required to perform account change
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
        require_once '/local/data/mis/database/database_libraries/secure-transaction-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/HomePhone.class.php';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc';
    }
}
