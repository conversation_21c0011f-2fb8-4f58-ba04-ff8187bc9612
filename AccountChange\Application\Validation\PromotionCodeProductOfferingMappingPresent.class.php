<?php
/**
 * Created by IntelliJ IDEA.
 * User: kbuthpur
 * Date: 2020-01-30
 * Time: 17:03
 */

use Plusnet\C2mApiClient\Entity\Promotion;
use Plusnet\CampaignNotificationClient\Exception\PersonalisedPromotionNotFoundException;

class AccountChange_PromotionCodeProductOfferingMappingPresent extends \AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE = 'Oops, that offer isn\'t available. Please call us on 0800 432 0080 to find out the latest deal we can give you.';

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_PROMO_MISSING_PRODUCT_OFFERING_MAPPING';

    const SUBSCRIPTION_PRODUCT_COMPONENT = "1";

    const DEFAULT_MARKET_ID = 3;

    private $c2mClient;
    private $c2mProductOfferings = array();
    protected $actor;

    /**
     * AccountChange_PromotionCodeProductOfferingMappingPresent constructor.
     * @param Auth_BusinessActor $actor
     * @param bool $isWorkplace
     * @param bool $isScript
     * @param array $additionalInformation
     *
     * @return \AccountChange_PromotionCodeProductOfferingMappingPresent
     */
    public function __construct(Auth_BusinessActor $actor, $isWorkplace = false, $isScript = false, $additionalInformation = array())
    {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);

        $this->actor = $actor;
        $this->c2mClient = \BusTier_BusTier::getClient('c2mapi.v5');
    }

    /**
     * Validates that components that a user will have post account change are compatible with the discount that's been
     * given, i.e there should be a mapping in products.tblC2mProductOfferings between the users service component id and the
     * c2m product type name that's setup as part of the discount.
     *
     *  @return bool
     */
    public function validate()
    {
        $c2mPromotion   = $this->getC2mPromotionFromAdditionalData();
        $additionalInfo = $this->getAdditionalInformation();

        $serviceComponents[] = $additionalInfo['phoneServiceComponentId'];
        $serviceComponents[] = $this->getScidFromSdiAndMarket($additionalInfo['broadbandServiceDefinitionId'], $additionalInfo['marketId']);

        if ($c2mPromotion instanceof Promotion) {
            $discounts = $c2mPromotion->getDiscounts();
            if (!empty($discounts) && count($discounts)>0) {
                $c2mProductOfferings = $this->getC2mProductOfferings($serviceComponents);
                foreach ($discounts as $discount) {
                    if ($discount->getType() == Plusnet\C2mApiClient\Entity\DiscountType::ON_GOING) {
                        foreach($discount->getProductOfferingPaymentInformations() as $discountElement) {
                            $discountedProductName = $discountElement->getName();
                            $found = false;
                            foreach($serviceComponents as $serviceComponentId) {
                                if (isset($c2mProductOfferings[(int)$serviceComponentId][$discountedProductName][self::SUBSCRIPTION_PRODUCT_COMPONENT])) {
                                    $found = true;
                                }
                            }
                            if (!$found) {
                                return false;
                            }
                        }
                    }
                }
            }
        } // if we don't have a promotion then let the validator return true so the process can continue without one.
        return true;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }

    /**
     * Get the new service component id given a market and service definition id
     *
     * @param int $sdi      Service defintion id
     * @param int $marketId Market id
     *
     * @return int
     **/
    protected function getScidFromSdiAndMarket($sdi, $marketId)
    {
        if (empty($sdi)) {
            return null;
        }

        if (empty($marketId)) {
            // No market id in this context probably relates to a failed line check.
            // In this case, we assume market 3 (low cost) as we don't want to fail validation
            // here for this reason
            $marketId = self::DEFAULT_MARKET_ID;
        }
        return $this->getScid($sdi, $marketId);
    }

    /**
     * Wrapper to the leagacy ProductFamily_InternetConnectionHelper::getId function
     *
     * @param int $sdi      Service defintion id
     * @param int $marketId Market id
     *
     * @return int
     **/
    protected function getScid($sdi, $marketId)
    {
        try {
            return ProductFamily_InternetConnectionHelper::getId($sdi, $marketId);
        } catch (Exception $e) {
            // If we've got a legacy service definition based product then the above will throw an
            // exception.  In this case it's valid to return null for the new scid
            return null;
        }
    }



    /**
     * Get a list of c2m product offering mappings - either from cache or the database if necessary
     *
     * @param array $serviceComponentIds List of service component ids to get mappings for§
     *
     * @return array
     */
    protected function getC2mProductOfferings($serviceComponentIds)
    {
        if (empty($this->c2mProductOfferings)) {

            $offerings = $this->getC2mProductOfferingsFromDb($serviceComponentIds);

            foreach ($offerings as $offering) {

                $this->c2mProductOfferings[(int)$offering['intServiceComponentID']][$offering['vchProductOfferingName']][$offering['intProductComponentID']] = 1;
            }

        }
        return $this->c2mProductOfferings;
    }

    /**
     * Query the database to get mappings from products.tblC2mProductOfferings
     *
     * @param array $serviceComponentIds List of service component ids to get mappings for§
     *
     * @return array
     */
    protected function getC2mProductOfferingsFromDb(array $serviceComponentIds)
    {
        $adaptor = Db_Manager::getAdaptor('AccountChange');
        return $adaptor->getC2mProductOfferings($serviceComponentIds);
    }
}
