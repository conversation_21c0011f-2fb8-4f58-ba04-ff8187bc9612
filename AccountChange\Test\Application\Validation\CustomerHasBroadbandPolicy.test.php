<?php
/**
 * <AUTHOR> <<EMAIL>>
 */


class AccountChange_CustomerHasBroadbandPolicyTest extends PHPUnit_Framework_TestCase
{
    const TEST_SERVICE_DEFINITION_ID = 5678;

    /** @var Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject $mockBusinessActor */
    private $mockBusinessActor;

    /** @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_ProductFamily $mockProductFamily */
    private $mockProductFamily;

    /**
     * @return void
     */
    public function setUp()
    {
        $this->mockBusinessActor = $this->mockBusinessActor();
        $this->mockProductFamily = $this->mockProductFamily();
    }

    /**
     * @return void
     */
    public function tearDown()
    {
        BusTier_BusTier::reset();
    }

    /**
     * @dataProvider dataForTestValidate
     * @param bool $isPhoneOnly    isPhoneOnly
     * @param bool $expectedResult expectedResult
     * @return void
     */
    public function testValidate($isPhoneOnly, $expectedResult)
    {
        $policy = $this->initialisePolicy();

        $policy->expects($this->once())
            ->method('getProductFamily')
            ->willReturn($this->mockProductFamily);

        $this->mockProductFamily->expects($this->once())
            ->method('isPhoneOnly')
            ->willReturn($isPhoneOnly);

        $this->assertEquals($expectedResult, $policy->validate());
    }

    /**
     * @return array
     */
    public function dataForTestValidate()
    {
        return [
            'has broadband' => [
                'isPhoneOnly' => false,
                'expectedResult' => true,
            ],
            'phone only' => [
                'isPhoneOnly' => true,
                'expectedResult' => false,
            ],
        ];
    }

    /**
     * @return void
     */
    public function testGetErrorCodeAndFailure()
    {
        $policy = $this->initialisePolicy();

        $this->assertEquals($policy::ERROR_MESSAGE, $policy->getFailure());
        $this->assertEquals($policy::ERROR_CODE, $policy->getErrorCode());
    }

    /**
     * @return AccountChange_CustomerHasBroadbandPolicy|PHPUnit_Framework_MockObject_MockObject
     */
    private function initialisePolicy()
    {
        return $this->getMockBuilder(AccountChange_CustomerHasBroadbandPolicy::class)
            ->setConstructorArgs([
                $this->mockBusinessActor,
                false,
                false,
                [
                    'serviceDefinitionId' => static::TEST_SERVICE_DEFINITION_ID,
                ]
            ])->setMethods(['getProductFamily'])
            ->getMock();
    }

    /**
     * @return Auth_BusinessActor|PHPUnit_Framework_MockObject_MockObject
     */
    private function mockBusinessActor()
    {
        return $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->getMock();
    }

    /**
     * @return PHPUnit_Framework_MockObject_MockObject|ProductFamily_Generic
     */
    private function mockProductFamily()
    {
        return $this->getMockBuilder(ProductFamily_Generic::class)
            ->disableOriginalConstructor()
            ->setMethods(['isPhoneOnly'])
            ->getMock();
    }
}
