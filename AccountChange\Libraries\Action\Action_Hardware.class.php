<?php
/**
 * Account Change Hardware Action
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2009 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */

use Plusnet\C2mApiClient\Exception\ProductOfferingNotFoundException;
use Plusnet\C2mApiClient\V5\Client;
use Plusnet\Hardware\Action\Api\ManualOrderSubmit;
use Plusnet\Hardware\Entity\Input\ApiRequestData\SubmitHardwareOrderRequestData;

/**
 * Account Change Hardware Action
 *
 * This class represents the logic surrounding the hardware ordering
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2009 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Action_Hardware extends AccountChange_Action
{
    const MARKET_ID = 3;

    /**
     * The hardware option handle
     *
     * @var string
     */
    private $hardwareOption = null;

    /**
     * The service definition id
     *
     * @var int
     */
    private $newServiceDefinitionId = null;

    /** @var int $oldServiceDefinitionId */
    private $oldServiceDefinitionId = null;

    /** @var bool $isRecontract */
    private $isRecontract = false;

    /**
     * Main execute of the action
     *
     * @return void
     * @throws Db_TransactionException
     * @throws WrongTypeException
     */
    public function execute()
    {
        $registry = AccountChange_Registry::instance();

        $this->hardwareOption = $registry->getEntry('hardwareOption');
        $this->newServiceDefinitionId = $registry->getEntry('intNewServiceDefinitionId');
        $this->oldServiceDefinitionId = $registry->getEntry('intOldServiceDefinitionId');
        $this->isRecontract = $registry->getEntry('isRecontract');

        $serviceComponentId = $this->getHardwareComponentIdFromOption();

        if (null != $serviceComponentId) {
            $this->setupHardwareComponent($serviceComponentId->getValue());
        }
    }

    /**
     * Get the hardware component id for the hardware
     *
     * @return Int|null
     */
    public function getHardwareComponentIdFromOption()
    {
        $componentId = null;

        if (isset($this->hardwareOption) && false != $this->hardwareOption) {
            $client = BusTier_BusTier::getClient('hardware');
            $bundle = $client->getBundleFromServiceDefinitionAndHandle(
                new Int($this->newServiceDefinitionId),
                new String($this->hardwareOption)
            );
            $componentId = $bundle->getServiceComponentId();
        }

        return $componentId;
    }

    /**
     * Create the hardware component
     *
     * @param int $serviceComponentId The service component Id
     *
     * @return void
     * @throws Db_TransactionException
     * @throws WrongTypeException
     */
    protected function setupHardwareComponent($serviceComponentId)
    {
        $this->includeLegacyFiles();

        Dbg_Dbg::write(
            'AccountChange_Action_Hardware::setupHardwareComponent '. var_export($serviceComponentId, 1),
            'AccountChange'
        );

        $componentId = $this->createHardwareComponent($serviceComponentId);

        $this->configureHardware($serviceComponentId, $componentId);

        if ($componentId > 0 && !$this->isTechnologyChanging()) {
            $this->submitManualHardwareOrder($componentId, ManualOrderSubmit::MANUAL_ORDER_REASON_RECONTRACT_POSTAGE);
        }

        $productFamily = $this->getProductFamily($this->newServiceDefinitionId);

        $allowedContractDefinitions = $this->getAllowedContractDefinitions();

        if (!($productFamily instanceof \ProductFamily_Res2012)) {
            $contract = $this->createHardwareContract($serviceComponentId, $allowedContractDefinitions);

            if (!empty($contract)) {
                $this->configureContract($contract, $componentId);
            }
        }

    }

    /**
     * @param int $componentId Component ID
     * @param int $reasonId    Hardware Order Reason ID, stored as constants in ManualOrderSubmit class
     * @return void
     */
    protected function submitManualHardwareOrder($componentId, $reasonId)
    {
        try {
            $hardwareOrderAction = $this->buildHardwareOrderAction($componentId, $reasonId);

            // @TODO: Remove this commit when insert component SQL is moved out of Legacy and into Framework
            Db_Manager::commit();
            $hardwareOrderAction->execute();
        } catch (\Exception $e) {
            error_log(sprintf(
                "ERROR: Unable to place hardware order for component (%d) with reason (%d) due to: %s - Stack Trace:"
                . "\n%s",
                $componentId,
                $reasonId,
                $e->getMessage(),
                $e->getTraceAsString()
            ));
        }
    }

    /**
     * @param int $componentId Component ID
     * @param int $reasonId    Hardware Order Reason ID, stored as constants in ManualOrderSubmit class
     * @return ManualOrderSubmit
     */
    protected function buildHardwareOrderAction($componentId, $reasonId)
    {
        $requestData = new SubmitHardwareOrderRequestData([
            'componentId' => $componentId,
            'manualOrderReasonId' => $reasonId,
        ]);

        return new ManualOrderSubmit($requestData);
    }

    /**
     * Wrapper for signupGetAllowedContracts to get allowed contract definitions
     *
     * @return array
     */
    protected function getAllowedContractDefinitions()
    {
        return signupGetAllowedContracts($this->intServiceId);
    }

    /**
     * Wrapper for signupCreateHardwareComponent to create a hardware component
     *
     * @param int $serviceComponentId Service Component Id of the hardware
     *
     * @return int Component Id
     */
    protected function createHardwareComponent($serviceComponentId)
    {
        return signupCreateHardwareComponent($this->intServiceId, $serviceComponentId, $this->newServiceDefinitionId);
    }

    /**
     * Wrapper for signupCreateHardwareContract to create a hardware contract
     *
     * @param int   $serviceComponentId         Service component Id of the hardware
     * @param array $allowedContractDefinitions Array of allowed contract definitions
     *
     * @return \Plusnet\ContractsClient\Entity\Contract object on success
     *                                                  false on failure
     */
    protected function createHardwareContract($serviceComponentId, $allowedContractDefinitions)
    {
        return signupCreateHardwareContract(
            $this->intServiceId,
            $serviceComponentId,
            $allowedContractDefinitions['arrHardware']
        );
    }

    /**
     * We need to configure the hardware component
     *
     * @param int $serviceComponentId The Service component type id
     * @param int $componentId        The newly created hardware component id
     *
     * @return void
     */
    protected function configureHardware($serviceComponentId, $componentId)
    {
        $this->includeLegacyFiles();

        global $global_component_configurators;

        if (isset($global_component_configurators[$serviceComponentId])
            && function_exists($global_component_configurators[$serviceComponentId])
        ) {
            Dbg_Dbg::write(
                'AccountChange_Action_Hardware::configureHardware ' .
                var_export($serviceComponentId, 1),
                'AccountChange'
            );
            $global_component_configurators[$serviceComponentId]($componentId, 'auto_configure');
        } else {
            error_log("AccountChange_Action_Hardware::setupGlobalConfigurator : Unable to setup "
                . " global_component_configurators for Component Type [$serviceComponentId], "
                . " Component [$componentId], SKIPPING.");
        }
    }

    /**
     * Configure new hardware contract.
     *
     * This gets all the existing active hardware contracts, mark them as withdrawn and
     * activate new hardware contracts
     *
     * @param Contract $newContract New hardware contract
     * @param int      $componentId The newly created hardware component id
     *
     * @return void
     */
    protected function configureContract(
        \Plusnet\ContractsClient\Entity\Contract $newContract,
        $componentId
    ) {
        Dbg_Dbg::write(
            'AccountChange_Action_Hardware::configureContract configuring contract for ' . var_export($componentId, 1),
            'AccountChange'
        );

        $contractsClient = BusTier_BusTier::getClient('contracts')
            ->setServiceId($this->intServiceId);

        //Retrieve existing hardware contracts
        $criteria = array(
            'typeHandle' => \Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_HARDWARE,
            'status'     => 'ACTIVE'
        );

        $availableHardwareContracts = $contractsClient->getContracts($criteria);

        //Withdraw existing hardware contracts
        foreach ($availableHardwareContracts as $availableHardwareContract) {
            foreach ($availableHardwareContract->getContractedServices() as $hardwareContractedService) {
                $contractsClient->withdrawContractedService(
                    $availableHardwareContract,
                    $hardwareContractedService,
                    'Withdrawing existing hardware contract for account change'
                );
            }
        }

        //Activate new hardware contract
        foreach ($newContract->getContractedServices() as $newContractedService) {
            $contractsClient->activateContractedService(
                $newContract,
                $newContractedService,
                'Activating new hardware contract for account change'
            );
        }
    }

    /**
     * Include the legacy files that we need
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/portal_modules/signup/v4/signup_regrade_shared_functions.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/database/database_libraries/config-access.inc';
        require_once '/local/data/mis/common_library_functions/common_application_apis/common-adsl-api.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/class-access.inc';
    }

    /**
     * @return bool
     */
    private function isChangingServiceDefinition()
    {
        return $this->oldServiceDefinitionId !== $this->newServiceDefinitionId;
    }

    /**
     * @return bool
     * @throws Db_TransactionException
     * @throws WrongTypeException
     */
    private function isTechnologyChanging()
    {
        try {
            $oldTechnologyType = $this->getTechnologyType($this->oldServiceDefinitionId);
            $newTechnologyType = $this->getTechnologyType($this->newServiceDefinitionId);
        } catch (AccountChange_TechnologyChangingException $exception) {
            error_log("ERROR: " . $exception->getMessage() . " - Stack trace:\n" . $exception->getTraceAsString());
            return $this->hasOrderNeededBeenFlagged();
        } catch (ProductOfferingNotFoundException $exception) {
            error_log("ERROR: " . $exception->getMessage() . " - Stack trace:\n" . $exception->getTraceAsString());
            return $this->hasOrderNeededBeenFlagged();
        }
        return $oldTechnologyType !== $newTechnologyType;
    }

    /**
     * @param int $serviceDefinitionId Service Definition ID
     * @return string
     * @throws Db_TransactionException
     * @throws ProductOfferingNotFoundException
     * @throws AccountChange_TechnologyChangingException
     * @throws WrongTypeException
     */
    private function getTechnologyType($serviceDefinitionId)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');

        $productDetails = $dbAdaptor->getProductDetailsByServiceDefinitionIdAndMarket(
            $serviceDefinitionId,
            static::MARKET_ID
        );

        if (empty($productDetails['intServiceComponentId'])) {
            throw new AccountChange_TechnologyChangingException(
                'Unable to find service component ID from service definition ID'
            );
        }

        /** @var Client $client */
        $client = BusTier_BusTier::getClient('c2mapi.v5');

        $productOfferings = $client->getProductOfferings(
            $this->getCorrelationId(),
            null,
            null,
            $productDetails['intServiceComponentId']
        );

        if (empty($productOfferings)) {
            throw new AccountChange_TechnologyChangingException(sprintf(
                'Unable to find any product offerings for component ID (%s) from c2m',
                $productDetails['intServiceComponentId']
            ));
        }

        /** @var Plusnet\C2mApiClient\Entity\ProductOffering $offering */
        $offering = array_pop($productOfferings);

        return $offering->getClassOfService();
    }

    /**
     * @return boolean
     */
    private function hasOrderNeededBeenFlagged()
    {
        $registry = AccountChange_Registry::instance();
        return (bool)$registry->getEntry('bolOrderNeeded');
    }

    /**
     * @return string
     * @throws WrongTypeException
     */
    private function getCorrelationId()
    {
        $log = Log_LogData::factory('');

        return $log->getDefaultCorrelationId();
    }
}
