server: coredb
role: master
rows: multiple
statement:
SELECT
   sp.intScheduledPaymentId
FROM
   financial.tblScheduledPayment sp
INNER JOIN
   userdata.tblPendingPrepaidContract ppc
ON
   sp.intScheduledPaymentId = ppc.intScheduledPaymentId
INNER JOIN
   userdata.tblProductComponentInstance pci
ON
   pci.intProductComponentInstanceId = ppc.intProductComponentInstanceId
INNER JOIN
   userdata.accounts a
ON
   a.account_id = sp.intAccountId
INNER JOIN
   userdata.users u
ON
   u.customer_id = a.customer_id
INNER JOIN
   userdata.services s
ON
   s.user_id = u.user_id
LEFT JOIN
   userdata.tblProductComponentContract pcc
ON
   ppc.intProductComponentInstanceId = pcc.intProductComponentInstanceId
WHERE
   pci.intProductComponentId = 43
AND
   sp.vchDescription = '%Line Rental Saver%'
AND
   sp.dtmCancelled IS NULL
AND
   sp.dtmFailed IS NULL
AND
   sp.dteDue = ppc.dteCreated
AND
   ppc.dteCancelled IS NULL
AND
   ppc.dteRedeemed IS NOT NULL
AND
   pcc.intProductComponentContractId IS NULL
AND
   s.service_id = :serviceId
