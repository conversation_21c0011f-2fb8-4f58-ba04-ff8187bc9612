<?php
/**
 * AutomatedEmail TemplateConfig
 *
 * @category  AutomatedEamil
 * @package   AutomatedEmail
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @version   git
 * @link      Projects/AutomatedEmail/PDD
 * @since     File available since 2009-09-28
 */

/**
 * AutomatedEmail TemplateConfig
 *
 * Ability to configure the paths we use in AutomatedEmail, so that we can inject paths for unit testing
 * TODO: Probably need some way of applying 'path rules' to passed in paths so people can't perform directory traversal
 *   or pass in the names of files, also without breaking it for unit tests (which are like to pass vfsStream paths?)
 *
 * @category  AutomatedEamil
 * @package   AutomatedEmail
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      Projects/AutomatedEmail/PDD
 */
class AutomatedEmail_TemplateConfig
{
	/**
	 * The path to compile templates in
	 *
	 * @var String
	 */
	private static $compilePath     = null;

	/**
	 * The path where the templates are stored
	 *
	 * @var String
	 */
	private static $contentBasePath = null;

	/**
	 * Sets the path of where to compile templates
	 *
	 * @param String $path Path to set the compile path to
	 *
	 * @return null
	 */
	public static function setCompilePath(String $path)
	{
		if (!file_exists($path) || !is_dir($path) || !is_writable($path)) {

			throw new AutomatedEmail_Exception(
					'The provided path did not appear to be a valid directory',
					AutomatedEmail_Exception::INVALID_COMPILE_PATH
			);
		}

		self::$compilePath = $path;
	}

	/**
	 * Returns the compile path. If the compile path hasn't been explicitly set then the default will be returned.
	 * Under normal circumstances the path will not need to be set to anything special - this is mainly for testing
	 * purposes.
	 *
	 * @return String
	 */
	public static function getCompilePath()
	{
		if (self::$compilePath instanceOf String) {
			$path = self::$compilePath;
		}
		else {
			$path = new String(realpath(Auto_ClassLoader::getModulesRoot() . '/../shared/AutomatedEmail/CompiledSmartyTemplates'));
		}

		return $path;
	}

	/**
	 * Sets the path of where the templates may be found
	 *
	 * @param String $path path to set the template path to
	 *
	 * @return null
	 */
	public static function setContentBasePath(String $path)
	{
		if (!file_exists($path) || !is_dir($path) || !is_writable($path)) {

			throw new AutomatedEmail_Exception(
					'The provided path did not appear to be a valid directory',
					AutomatedEmail_Exception::INVALID_CONTENT_BASE_PATH
			);
		}

		self::$contentBasePath = $path;
	}

	/**
	 * Returns the content base path. If the path has not been explicitly set the default will be returned.
	 * Under normal circumstances the path will not need to be set to anything special - this is mainly for testing
	 * purposes.
	 *
	 * @return String
	 */
	public static function getContentBasePath()
	{
		if (self::$contentBasePath instanceOf String) {
			$path = self::$contentBasePath;
		}
		else {
			$path = new String(realpath(Auto_ClassLoader::getModulesRoot() . '/../content/templatedemails'));
		}

		return $path;
	}

	/**
	 * Resets the compile path and content base path
	 *
	 * @return null
	 */
	public static function reset()
	{
		self::$compilePath     = null;
		self::$contentBasePath = null;
	}
}