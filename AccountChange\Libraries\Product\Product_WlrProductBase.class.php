<?php

/**
 * File Product_WlrProductSorter
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://jira.internal.plus.net/browse/PRI-281
 */

/**
 * Context:
 *
 * Because of large list of products shown for customers with pre 2013 WLR
 * products, we need to change the sorting logic to group these together
 * sensibly
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link    http://jira.internal.plus.net/browse/PRI-281
 */
class AccountChange_Product_WlrProductBase
{
    /**
     * Definition of product families
     */
    const FAMILY_UNKNOWN = 0;
    const FAMILY_PENGUIN = 1;
    const FAMILY_JUNE2014 = 2;
    const FAMILY_BGL = 3;

    /**
     * The current default family used in the filters.
     */
    const DEFAULT_PRODUCT_FAMILY = self::FAMILY_JUNE2014;

    /**
     * The no home phone product
     */
    const NO_HOME_PHONE = 0;

    /**
     * Definition of June2014 (no codename) products
     */
    const JUNE2014_ANYTIME_INTERNATIONAL = 1638;
    const JUNE2014_ANYTIME_INTERNATIONAL_WITH_MOBILE = 1639;
    const JUNE2014_ANYTIME = 1640;
    const JUNE2014_ANYTIME_WITH_MOBILE = 1641;
    const JUNE2014_EVENINGS_AND_WEEKENDS_FREE = 1642;
    const JUNE2014_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE = 1643;
    const JUNE2014_EVENINGS_AND_WEEKENDS = 1644;
    const JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE = 1645;
    const JUNE2014_MOBILE = 1646;
    const JUNE2014_LINE_ONLY = 1651;
    const JUNE2014_LINE_ONLY_WITH_MOBILE = 1652;
    const JUNE2014_WEEKENDS = 1647;
    const JUNE2014_WEEKENDS_WITH_MOBILE = 1648;

    /**
     * Products that are available with any of our line rental
     * saver tariffs.
     **/
    const PENGUIN_ANYTIME_INTERNATIONAL = 1345;
    const PENGUIN_ANYTIME_INTERNATIONAL_WITH_MOBILE = 1346;
    const PENGUIN_ANYTIME = 1347;
    const PENGUIN_ANYTIME_WITH_MOBILE = 1348;
    const PENGUIN_EVENINGS_AND_WEEKENDS_FREE = 1349;
    const PENGUIN_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE = 1350;
    const PENGUIN_EVENINGS_AND_WEEKENDS = 1351;
    const PENGUIN_EVENINGS_AND_WEEKENDS_WITH_MOBILE = 1352;
    const PENGUIN_MOBILE = 1353;
    const PENGUIN_LINE_ONLY = 1354;
    const PENGUIN_LINE_ONLY_WITH_MOBILE = 1355;
    const PENGUIN_WEEKENDS = 1356;
    const PENGUIN_WEEKENDS_WITH_MOBILE = 1357;

    /**
     * Definition of June2016 products
     **/
    const JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE = 2057;
    const JUNE2016_UNLIMITED_UK_WITH_MOBILE = 2058;

    /**
     * Definition of BGL products
     **/
    const BGL_BUSINESS_PAYG = 2418;
    const BGL_BUSINESS_UNLIMITED_UK_CALLS = 2405;
    const BGL_BUSINESS_UNLIMITED_AND_500_INTERNATIONAL = 2406;

    const PORTAL = "PORTAL";
    const WORKPLACE = "WORKPLACE";

    /**
     * Definition of products
     */
    protected $products = array (
        self::NO_HOME_PHONE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false, // This overrides other rules if LRS enabled
            'family' => self::DEFAULT_PRODUCT_FAMILY
        ),

        self::PENGUIN_LINE_ONLY => array (
            'mlrOnly' => false,
            'lrsOnly' => true,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => true,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_LINE_ONLY_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => true,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => true,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_WEEKENDS => array (
            'mlrOnly' => true,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN,
            'isFreeProduct' => true
        ),
        self::PENGUIN_WEEKENDS_WITH_MOBILE => array (
            'mlrOnly' => true,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN,
            'isFreeProduct' => true
        ),
        self::PENGUIN_EVENINGS_AND_WEEKENDS => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_EVENINGS_AND_WEEKENDS_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_ANYTIME => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_ANYTIME_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_ANYTIME_INTERNATIONAL => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct'=> false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_ANYTIME_INTERNATIONAL_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),
        self::PENGUIN_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct'=> false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_PENGUIN
        ),

        self::JUNE2014_LINE_ONLY => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => true,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014
        ),
        self::JUNE2014_LINE_ONLY_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => true,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014
        ),
        self::JUNE2014_WEEKENDS => array (
            'mlrOnly' => true,
            'lrsOnly' => false,
            'adslOnly' => true,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014,
            'mobileVersion' => self::JUNE2014_WEEKENDS_WITH_MOBILE,
            'deprecatedBy' => array(self::JUNE2014_LINE_ONLY,
                                    self::JUNE2014_LINE_ONLY_WITH_MOBILE
            ),
        ),
        self::JUNE2014_WEEKENDS_WITH_MOBILE => array (
            'mlrOnly' => true,
            'lrsOnly' => false,
            'adslOnly' => true,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014,
            'nonMobileVersion' => self::JUNE2014_WEEKENDS,
            'deprecatedBy' => array(self::JUNE2014_LINE_ONLY,
                                    self::JUNE2014_LINE_ONLY_WITH_MOBILE
            )
        ),
        self::JUNE2014_EVENINGS_AND_WEEKENDS_FREE => array (
            'mlrOnly' => true,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => true,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014,
            'mobileVersion' => self::JUNE2014_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE,
            'deprecatedBy' => array(self::JUNE2014_LINE_ONLY,
                                    self::JUNE2014_LINE_ONLY_WITH_MOBILE,
                                    self::JUNE2014_EVENINGS_AND_WEEKENDS,
                                    self::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE
            )
        ),
        self::JUNE2014_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE => array (
            'mlrOnly' => true,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => true,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014,
            'nonMobileVersion' => self::JUNE2014_EVENINGS_AND_WEEKENDS_FREE,
            'deprecatedBy' => array(self::JUNE2014_LINE_ONLY,
                                    self::JUNE2014_LINE_ONLY_WITH_MOBILE,
                                    self::JUNE2014_EVENINGS_AND_WEEKENDS,
                                    self::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE)
        ),
        self::JUNE2014_EVENINGS_AND_WEEKENDS => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => true, // This overrides other rules apart from availableOnPortal if LRS
            'family' => self::FAMILY_JUNE2014,
            'deprecatedBy' => array(self::JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE),
            'mobileVersion' => array (
                self::PORTAL => self::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE
             ),
            'deprecatedFromDate' => '2016-09-01'

        ),
        self::JUNE2014_EVENINGS_AND_WEEKENDS_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => true, // This overrides other rules apart from availableOnPortal if LRS
            'family' => self::FAMILY_JUNE2014,
            'deprecatedBy' => array(self::JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE),
            'nonMobileVersion' =>  array(
                self::PORTAL => self::JUNE2014_EVENINGS_AND_WEEKENDS
            ),
            'deprecatedFromDate' => '2016-09-01'
        ),
        self::JUNE2014_ANYTIME => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014,
            'mobileVersion' => array(
                self::PORTAL => self::JUNE2014_ANYTIME_WITH_MOBILE
            ),
            'deprecatedBy' => array(self::JUNE2016_UNLIMITED_UK_WITH_MOBILE),
            'deprecatedFromDate' => '2016-09-01'
        ),
        self::JUNE2014_ANYTIME_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'nonMobileVersion' => array(
                self::PORTAL => self::JUNE2014_ANYTIME
            ),
            'family' => self::FAMILY_JUNE2014,
            'deprecatedBy' => array(self::JUNE2016_UNLIMITED_UK_WITH_MOBILE),
            'deprecatedFromDate' => '2016-09-01'
        ),
        self::JUNE2014_ANYTIME_INTERNATIONAL => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014
        ),
        self::JUNE2014_ANYTIME_INTERNATIONAL_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014
        ),
        self::JUNE2014_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => false,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'family' => self::FAMILY_JUNE2014
        ),
        /* newVariantOfExistingProductFamily flag:
         *
         * Show this product even if new product is superseded by this.
         *  - There’s a rule that filters out the latest version of a product if the current product we’re
         *    on is superseded by it.
         *  - If this flag is set, then we ignore that and show the newer (this) version too.
        */
        self::JUNE2016_EVENINGS_AND_WEEKENDS_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false, // This overrides other rules apart from availableOnPortal if LRS
            'family' => self::FAMILY_JUNE2014,
            'newVariantOfExistingProductFamily' => true // * see above
        ),
        self::JUNE2016_UNLIMITED_UK_WITH_MOBILE => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'newVariantOfExistingProductFamily' => true,
            'family' => self::FAMILY_JUNE2014
        ),
        self::BGL_BUSINESS_PAYG => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'newVariantOfExistingProductFamily' => false,
            'family' => self::FAMILY_BGL
        ),
        self::BGL_BUSINESS_UNLIMITED_UK_CALLS => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'newVariantOfExistingProductFamily' => false,
            'family' => self::FAMILY_BGL
        ),
        self::BGL_BUSINESS_UNLIMITED_AND_500_INTERNATIONAL => array (
            'mlrOnly' => false,
            'lrsOnly' => false,
            'adslOnly' => false,
            'fibreOnly' => false,
            'availableOnPortal' => true,
            'isLineOnlyProduct' => false,
            'alwaysShowOnLRS' => false,
            'newVariantOfExistingProductFamily' => false,
            'family' => self::FAMILY_BGL
        ),
    );

    /**
     * Checks a product to see if it belongs to the family that is passed in.
     *
     * @param int $wlrProductId The ID of the potential product
     * @param int $family       The target product family
     *
     * @return bool True if the family of the product matches the target.
     */
    protected function familiesMatch($wlrProductId, $family)
    {
        $wlrProductFamily = self::FAMILY_UNKNOWN;
        if ($wlrProductId == null || $wlrProductId == 0) {
            $wlrProductFamily = self::DEFAULT_PRODUCT_FAMILY;
        } elseif (array_key_exists((int)$wlrProductId, $this->products)) {
            $wlrProductFamily = $this->products[(int)$wlrProductId]['family'];
        }

        return $wlrProductFamily == $family;
   }
}
