<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
    <groupId>net.plus</groupId>
    <artifactId>ActiveDirectoryClient</artifactId>
    <name>ActiveDirectoryClient</name>
    <version>1.5.0</version>
    <packaging>pom</packaging>
    <parent>
        <groupId>net.plus.php</groupId>
        <artifactId>plusnet-php-ant-pom</artifactId>
        <version>3.3.1-SNAPSHOT</version>
    </parent>
    <properties>
        <sonar.language>php</sonar.language>
        <sonar.sources>Test,src</sonar.sources>
    </properties>
    <build>
        <directory>target</directory>
        <sourceDirectory>Libraries</sourceDirectory>
        <testSourceDirectory>Test</testSourceDirectory>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <descriptors>
                        <descriptor>distribution.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>run-composer</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target name="package-install" unless="skipPackageInstall">
                                <move file="composer-php8.json" tofile="composer.json" />
                            </target>
                            <exportAntProperties>true</exportAntProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <scm>
        <connection>scm:git:https://bitbucket.int.plus.net/projects/SERVICES/repos/ActiveDirectoryClient</connection>
        <developerConnection>scm:git:https://bitbucket.int.plus.net/projects/SERVICES/repos/ActiveDirectoryClient</developerConnection>
        <url>https://bitbucket.int.plus.net/projects/SERVICES/repos/ActiveDirectoryClient</url>
    </scm>
    <ciManagement>
        <system>Jenkins</system>
        <url>https://jenkins.env.plus.net/job/Build/job/ActiveDirectoryClient</url>
    </ciManagement>
</project>
