<?php
/**
 * Parent class for email handlers, provides implementation for emails
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
/**
 * AccountChange_EmailHandler
 * Parent class for email handlers, provides implementation for emails
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
use Plusnet\MsnEmailClient\Libraries\MsnSendEmail;
abstract class AccountChange_EmailHandler
{
    /**
     * Handle for auto problem template handle.
     *
     * @var string
     */
    protected $autoProblemHandle = 'accountChangeActionFailed';

    /**
     * Tranfers local variables into an array ready to be used
     *
     * @return Array email variables
     */
    abstract protected function populateEmailVariables();

    /**
     * Get the email name, ie filename without the ext
     *
     * @return string email name
     */
    abstract protected function getEmailName();

    /**
     * Send a confirmation email to the customer
     *
     * @param int $serviceId Customers service id
     *
     * @return void;
     */
    public function sendEmail($serviceId)
    {
        try {
            $objIspAutomatedEmail = $this->getIspAutomatedEmail($serviceId);
            $objIspAutomatedEmail->prepareIspAutomatedEmail($this->getEmailName(), $this->populateEmailVariables());
            $objIspAutomatedEmail->send();

        } catch (Exception $e) {
            $this->raiseAutoProblem($e, $serviceId);
        }
    }

    /**
     * Returns a new IspAutomatedEmail_IspAutomatedEmail object for the service ID given.
     *
     * @param integer $serviceId Service id
     *
     * @return IspAutomatedEmail_IspAutomatedEmail
     */
    protected function getIspAutomatedEmail($serviceId)
    {
        return new IspAutomatedEmail_IspAutomatedEmail($serviceId);
    }

    /**
     * Raises an auto problem using the template stored in
     * /local/codebase2005/content/templatedautoproblems/PLUSNET/WORKPLACE_ISP_ADMIN
     *
     * @param Exception $e         Exception thrown
     * @param int       $serviceId Customers service id
     *
     * @return void
     */
    public function raiseAutoProblem($e, $serviceId)
    {
        $client = $this->getAutoProblem();
        $actor = $this->getLoggedInOrScriptActor();

        $autoProblem = $client->prepareAutoProblem(
            $this->autoProblemHandle,
            array(
                'className' => get_class($this),
                'actorId' => $actor->getActorId(),
                'serviceId' => $serviceId,
                'exception' => $e
            ),
            $actor
        );

        $problemId = $autoProblem->raiseProblem();
    }

    /**
     * Return an autoproblem object
     *
     * @return AutoProblem_AutoProblemClient
     */
    protected function getAutoProblem()
    {
        return BusTier_BusTier::getClient('autoproblem');
    }

    /**
     * Return the logged in or script actor object
     *
     * @return Auth_BusinessActor
     */
    protected function getLoggedInOrScriptActor()
    {
        return BusTier_BusTier::getLoggedInOrScriptActor();
    }

    /**
     * Send MSN Mail
     *
     * @param int    $intServiceId         Customer ServiceId
     * @param string $strEmailTemplateName Email Template Name
     * @param array  $arrEmailTemplateData Email Template Data
     * @param string $strEventName         Event Name
     *
     * @return void
     */
    public function sendEmailMSN($intServiceId, $strEmailTemplateName, $arrEmailTemplateData, $strEventName)
    {
        try {
            $objMsnSendEmail = new MsnSendEmail();
            $objMsnSendEmail->sendEmail($intServiceId, $strEmailTemplateName, $arrEmailTemplateData, $strEventName);
        } catch (Exception $e) {
            $this->raiseAutoProblem($e, $intServiceId);
        }
    }
}
