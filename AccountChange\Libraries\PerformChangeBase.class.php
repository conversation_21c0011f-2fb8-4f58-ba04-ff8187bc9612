<?php
/**
 * Helper functions to perform the required actions in actually changing an account.
 *
 * Used by the scheduled change script and account change api
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2013 PlusNet
 * @link      link
 * @since     File available since 2013-02-19
 */

/**
 * Used by the scheduled change script and account change api
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 PlusNet
 * @link      link
 */
use Plusnet\HouseMoves\Services\ServiceManager;

class AccountChange_PerformChangeBase
{

    /**
     * A service we currently process
     *
     * @var Core_Service | Core_ServiceDao
     */
    protected $objService = null;

    /**
     * The service definition id we're moving to
     *
     * @var int
     **/
    protected $toSdi;

    /**
     * Should CBC be ran before processing each account?
     *
     * @boolean
     */
    protected $doCbcProcessing = false;

    /**
     * Should debug information be written out?
     *
     * @boolean
     */
    protected $doDebugOutput = true;

    /**
     * The (dynamically generated) filename to which debug should be written to
     *
     * @string
     */
    protected $debugFile = null;

    /**
     * This is a scheduled change rather than instant
     *
     * @var bool
     */
    protected $isScheduled;

    /**
     * The old service definition id
     *  - This will almost always be the same as the current sdi, and is in here to maintain
     *    compatibility with the scheduled account change script where these could be different if
     *    the scheduled change has already happened.
     *
     * @var int
     **/
    protected $oldSdi;

    /**
     * @var string
     */
    protected $currentAccessTechnology;

    /**
     * The last set of line check results
     * @var LineCheck_Result | LineCheck_ResultDao
     */
    protected $lineCheckResults;

    /**
     * Business actor id
     * @var int
     */
    protected static $intBusinessActorId = null;

    /**
     * @var string
     */
    protected $newAccessTechnology;

    /**
     *This is an api call so may need to do line check
     * @var bool
     */
    protected $isLineCheckNeededIfNotFound;

    /**
     * @var AccountChange_Registry
     */
    protected $registry;

    /**
     * Where to put debug information.  The filename's will contain a timestamp
     */
    const DEBUG_DIR = '/share/admin/portal/crontab_output/AccountChangeDebug';

    /**
     * sets up default variables
     */
    public function __construct()
    {
        $this->isScheduled = false;
        $this->registry = AccountChange_Registry::instance();
    }

    /**
     * Get core service
     *
     * @param int $serviceId Service id for the customer
     *
     * @return Core_Service
     */
    protected function getCoreServiceForServiceId($serviceId)
    {
        return new Core_Service($serviceId);
    }

    /**
     * Get core service definition
     *
     * @param int $type Type for the customer
     *
     * @return Core_ServiceDefinition
     */
    protected function getCoreServiceDefinition($type)
    {
        return new Core_ServiceDefinition($type);
    }

    /**
     * Setter for doDebugOutput
     *
     * @param bool $doDebugOutput Turn debug on/off
     *
     * @return void
     **/
    public function setDoDebugOutput($doDebugOutput)
    {
        $this->doDebugOutput = $doDebugOutput;
    }

    /**
     * Get blocked account change email product names
     *
     * @return array
     */
    protected function getBlockedAccountChangeEmailProductNames()
    {
        return AccountChange_Manager::getBlockedAccountChangeEmailProductNames();
    }

    /**
     * Setter for $this->objService
     *
     * @param Core_Service $objCoreService Core service object
     *
     * @return void
     */
    protected function setCoreService(Core_Service $objCoreService)
    {
        $this->objService = $objCoreService;
    }

    /**
     * Getter for $this->objService
     *
     * @return Core_Service
     */
    protected function getCoreService()
    {
        return $this->objService;
    }

    /**
     * Setter for isLineCheckNeededIfNotFound
     *
     * @param bool $doDebugOutput True to output debug
     *
     * @return void
     **/
    public function setLineCheckNeededIfNotFound($doDebugOutput)
    {
        $this->isLineCheckNeededIfNotFound = $doDebugOutput;
    }


    /**
     * Method to apply pending retention discounts once product change is complete
     *
     * @param int $serviceId Service Id of the customer
     * @param int $scheduleId Schedule id of the product change
     * @param bool $isRecontract Is recontracting
     *
     * @return bool
     */
    public function applyPendingRetentionDiscounts($serviceId, $scheduleId, $isRecontract = false)
    {
        $discountApplied = false;

        $adslComponentId = $this->getActiveBroadbandComponentId($serviceId);
        $targetActor = $this->getActorByExternalUserId($serviceId);
        $retentionManager = $this->getRetentionManagerByAdslScheduleId($scheduleId, $targetActor);

        if (!empty($adslComponentId) && !empty($retentionManager)) {
            $productComponentInstanceId = $this->getProductComponentInstanceId($adslComponentId);
            $pendingOffers = $retentionManager->getOfferManager()->getPendingOffers();

            if (!empty($pendingOffers) && $isRecontract) {
                $this->cancelExistingDiscounts($serviceId);
            }

            foreach ($pendingOffers as $pendingOffer) {
                // Filtering the offers that are specific to the current ADSL product change
                if ($pendingOffer->getAdslScheduleId() != $scheduleId) {
                    continue;
                }

                $pendingOffer->setComponentId($adslComponentId);

                if ($pendingOffer->getProductComponentInstanceId() === null) {
                    $pendingOffer->setProductComponentInstanceId($productComponentInstanceId);
                }

                $pendingOffer->apply(
                    $targetActor,
                    $retentionManager->getOfferingActor(),
                    $retentionManager->getTicketId()
                );

                $discountApplied = true;

                $this->output(
                    'Applied discount of ' . $pendingOffer->getDiscountValueIncVat() .
                    ' pounds for ' . $pendingOffer->getDiscountDurationMonths() . ' months'
                );
            }
        }

        return $discountApplied;
    }

    /**
     * Returns how many months of discount left on their account
     *
     * @return int
     */
    public function getDiscountLength()
    {
        $intRemaningMonths = Core_ServiceDefinition::DEFAULT_DISCOUNT_LENGTH;

        $intServiceId = $this->objService->getServiceId();

        $uxtContractStart = $this->getCustomerExistingContractStartDate($intServiceId, 'INTERNET_CONNECTION');

        if (!empty($uxtContractStart)) {
            $arrProductDetails = $this->objService->getAdslDetails();

            if (isset($arrProductDetails['intTariffID']) && !empty($arrProductDetails['intTariffID'])) {
                $objTariffDeviation = new ProductComponent_TariffDeviation($arrProductDetails['intTariffID']);
                $intRemaningMonths = $objTariffDeviation->getRemainingMonths($uxtContractStart);
            }
        }

        return $intRemaningMonths;
    }

    /**
     * Get customer existing contract start date
     *
     * @param integer $intServiceId ServiceId
     * @param string $strServiceCompProdTypeHandle Product type handle
     *
     * @return array - array which is later passed as a template vars to the email template
     */
    protected function getCustomerExistingContractStartDate($intServiceId, $strServiceCompProdTypeHandle)
    {
        $this->includeLegacyFiles();

        return CProductComponent::getCustomerExistingContractStartDate($intServiceId, $strServiceCompProdTypeHandle);
    }

    /**
     * Returns price information for customers on Value family products
     *
     * @param integer $intServiceId ServiceId
     *
     * @return array
     * ['subscription']['lead']
     * ['subscription']['ongoing']
     */
    protected function getUserdataGetPrices($intServiceId)
    {
        return userdata_get_prices($intServiceId);
    }


    /**
     * Returns line check result for the entered service
     *
     * @param Core_Service $service service
     *
     * @return LineCheck_Result
     */
    public static function getLineCheck($service)
    {
        $objRequest = LineCheck_RequestManager::factory(
            LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO,
            $service->getCliNumber(),
            null,
            null,
            null
        );

        $objLineCheck = new LineCheck_LineCheck($objRequest);
        $objResult = new LineCheck_Result();

        try {
            $objLineCheck->sendRequest();
        } catch (LineCheck_BtRequestException $objException) {
            $arrLineCheckResult['bolServiceUnavaliable'] = 1;
            $arrLineCheckResult['strLineCheckInput'] = $service->getCliNumber();

            return $arrLineCheckResult;
        }

        $objLineCheck->getResult($objResult);

        return $objResult;
    }


    /**
     * Returns line check result for the entered service
     *
     * @param Core_Service $service service
     *
     * @return LineCheck_Result
     */
    protected function doLineCheck($service)
    {
        return static::getLineCheck($service);
    }

    /**
     * write the link check result to the database
     *
     * @param Core_Service $service service
     * @param LineCheck_Result $lineCheckResult lineCheckResult
     *
     * @return LineCheck_Result
     */
    protected function writeLineCheckResultToDb($service, $lineCheckResult)
    {
        $lineCheckResult->setServiceId($this->objService->getServiceId());
        $adslDetails = $this->objService->getAdslDetails();
        $lineCheckResult->setServiceDefinitionId($adslDetails['service_definition_id']);
        $lineCheckResult->setLineCheckStatusId(LineCheck_Result::STATUS_ACTIVE);
        $lineCheckResult->write();
    }

    /**
     * Returns line check result for the entered serviceId
     *
     * @param Core_Service $service service
     *
     * @return LineCheck_Result
     */
    public function getLineCheckFromDatabase($service)
    {
        $lineCheckResult = $this->getLineCheckByServiceId($service->getServiceId());

        if ($lineCheckResult instanceof LineCheck_Result && $lineCheckResult->isValidResult()) {
            return $lineCheckResult;
        } elseif ($this->isLineCheckNeededIfNotFound === true) {
            $lineCheckResult = $this->doLineCheck($this->objService);
            if ($lineCheckResult instanceof LineCheck_Result && $lineCheckResult->isValidResult()) {
                $this->writeLineCheckResultToDb($this->objService, $lineCheckResult);
            }
            return $lineCheckResult;
        }
    }

    /**
     * @param int $serviceId service id
     * @return LineCheck_Result
     */
    protected function getLineCheckByServiceId($serviceId)
    {
        return LineCheck_Result::getPendingResultByServiceId($serviceId);
    }

    /**
     * Returns market of the current service going through account change
     *
     * @return int marketId
     */
    public function getMarketId()
    {
        return LineCheck_Market::DEFAULT_MARKET_ID;
    }

    /**
     * Based on a record from userdata.service_change_schedule restores account change manager
     *
     * @param array $a Account manager data
     * @param array $wlrConfiguration wlr configuration
     *
     * @return AccountChange_Manager
     * @throws AccountChange_ManagerException
     * @throws AccountChange_Product_ManagerException
     * @throws Db_TransactionException
     */
    public function restoreAccountManager(array $a, $wlrConfiguration = null)
    {
        if (empty($this->lineCheckResults)) {
            $this->lineCheckResults = $this->getLineCheckFromDatabase($this->objService);
        };

        $marketId = $this->getMarketId();
        $newAdslScid = $this->getInternetConnectionId($a['intNewSdi'], $marketId);

        $activeAdslComponentId = $this->getComponentIdForInternetConnection($a['intServiceId'], 'active');
        if (is_array($activeAdslComponentId) && count($activeAdslComponentId) == 1) {
            $existingAdslComponentId = $activeAdslComponentId[0];
        } else {
            $existingAdslComponentId = null;
        }

        // ST-950 Get all the defunct INTERNET_CONNECTION components so we can cleanup broken contracts.
        $defunctAdslComponentIds = $this->getComponentIdForInternetConnection($a['intServiceId'], 'destroyed');

        //set options
        $arrOptionsBB = array(
            'bolScheduleDowngrade' => false,
            'bolSchedule' => $this->isScheduled,
            'bolContractReset' => false,
            'bolTakePayment' => false,
            'bolRegrade' => false,
            'objLineCheckResult' => $this->lineCheckResults,
            'newAdslScid' => $newAdslScid,
            'oldAdslComponentId' => $existingAdslComponentId,
            'defunctAdslComponentIds' => $defunctAdslComponentIds,
            'bolHousemove' => false
        );

        // Set house move in options if we're creating a scheduled change or completing one
        $isNewHouseMove = isset($a['bolHouseMove']) && $a['bolHouseMove'] === true;

        if ($isNewHouseMove || $this->isScheduledHouseMove($a['intServiceId'])) {
            $arrOptionsBB['bolHousemove'] = true;
        }

        if (isset($wlrConfiguration['objNewWlr'])) {
            $arrOptionsBB['newWlrScid'] = $wlrConfiguration['objNewWlr']->getNewWlrScid();
        }

        $arrOldProducts = array();

        // Business Logic. RES-1136 If the customer has WLR, then a deferred activation contract is
        // added for FTTC upgrades.  We therefore need to know whether the customer has WLR (active or pending)
        // when completing the broadband account change.

        $registryInstance = AccountChange_Registry::instance();

        $registryInstance->setEntry('bolHousemove', $arrOptionsBB['bolHousemove']);

        $registryInstance
            ->setEntry('intSelectedTariffID', $this->fetchTariffId($a['intNewSdi'], $marketId))
            ->setEntry('bolActivationContract', $this->hasWlr($a['intServiceId']));
        $a['intNewTariffId'] = $registryInstance->getEntry('intSelectedTariffID');

        // Provide backdated date to actions if set
        if (!empty($a['backDatedDate'])) {
            $arrOptionsBB['backDatedDate'] = $a['backDatedDate'];
            $registryInstance->setEntry('backDatedDate', $a['backDatedDate']);
        }

        // make promoCode accessible during the action execution
        $promoCode = (!empty($a['promoCode'])) ? $a['promoCode'] : null;
        $registryInstance
            ->setEntry('promoCode', $promoCode)
            ->setEntry('c2mPromotion', false);

        if ($promoCode == null || empty($promoCode)) {
            $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
            $c2mPromoCode = $dbAdaptor->getC2MPromotionCode($a['intServiceChangeScheduleId']);
            if (!empty($c2mPromoCode)) {
                $promoCode = $c2mPromoCode['promotionCode'];
                $registryInstance
                    ->setEntry('promoCode', $promoCode)
                    ->setEntry('c2mPromotion', true)
                    ->setEntry('impressionOfferId', $c2mPromoCode['impressionOfferId']);

                $contractLengthMonths = $c2mPromoCode['contractLengthMonths'];
                $registryInstance->setEntry('contractLengthMonths', $contractLengthMonths);
            }
        }

        //Indicates whether the action is triggered from Account Change script. Set to false by default.
        //This is overridden by Account Change script (which is a child class of this)
        $registryInstance->setEntry('runFromPerformScheduledAccountChange', false);

        if ($a['intNewSdi']) {
            $arrOptionsBB['strContract'] = strtoupper($this->objService->getBroadbandContract());
            $arrOldProducts[] = $this->getProductConfiguration(
                $a['intOldSdi'],
                $arrOptionsBB
            );
            $arrOldProducts[0]->setBusinessActorId($this->getBusinessActorId());
        }
        if (isset($this->objOldWlr)) {
            $arrOldProducts[] = $this->objOldWlr;
        }

        //hack around not implemented framework functionality

        $arrNewProducts = array();
        if ($a['intNewSdi']) {
            $arrOptionsBB['strContract'] = $a['strContractLengthHandle'];
            $arrNewProducts[] = $this->getProductConfiguration(
                $a['intNewSdi'],
                $arrOptionsBB
            );
            //hack around not implemented framework functionality
            $arrNewProducts[0]->setBusinessActorId($this->getBusinessActorId());
        }
        if (isset($this->objNewWlr)) {
            $arrNewProducts[] = $this->objNewWlr;
        }

        if (isset($wlrConfiguration['objOldWlr'])) {
            $arrOldProducts[] = $wlrConfiguration['objOldWlr'];
        }

        if (isset($wlrConfiguration['objNewWlr'])) {
            $arrNewProducts[] = $wlrConfiguration['objNewWlr'];
        }

        /**
         * Check if the customer is re-contracting to the same products.
         * If this is the case and there's no new promo code to be applied, we
         * should retain any active discounts on the account.
         */
        if ($this->isReContractingOnSameProduct($a, $wlrConfiguration)) {
            $registryInstance->setEntry('activateRecontract', true);
            if (empty($promoCode)) {
                $registryInstance->setEntry('retainDiscounts', true);
            }
        }

        $registryInstance
            ->setEntry('lineCheckResult', $this->lineCheckResults);

        $accountChangeManager = new AccountChange_Manager(
            $a['intServiceId'],
            new AccountChange_AccountConfiguration($arrOldProducts),
            new AccountChange_AccountConfiguration($arrNewProducts)
        );

        return $accountChangeManager;
    }

    /**
     * Method to check if any of the products are changing. No changes mean customer is re-contracting
     * to the same product
     *
     * @param array $accountChange Account Change details
     * @param array $wlrConfiguration Wlr change configs
     *
     * @return bool
     */
    protected function isReContractingOnSameProduct(array $accountChange, array $wlrConfiguration = null)
    {
        $newSdi = !empty($accountChange['intNewSdi']) ? $accountChange['intNewSdi'] : null;
        $oldSdi = !empty($accountChange['intOldSdi']) ? $accountChange['intOldSdi'] : null;

        $oldWlrScId = null;
        if (!empty($wlrConfiguration['objOldWlr'])) {
            $oldWlrScId = $wlrConfiguration['objOldWlr']->getProductId();
        }

        $newWlrScId = null;
        if (!empty($wlrConfiguration['objNewWlr'])) {
            $newWlrScId = $wlrConfiguration['objNewWlr']->getProductId();
        }

        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $oldTariffId = $dbAdaptor->getServiceDefinitionTariffData($accountChange['intServiceId'])['intTariffID'];

        return (
            ($newSdi === $oldSdi)
            && ($newWlrScId === $oldWlrScId)
            && ($accountChange['intNewTariffId'] === null || $accountChange['intNewTariffId'] === $oldTariffId)
            && !empty($accountChange['strContractLengthHandle'])
        );
    }

    /**
     * Function to get script user actor id
     *
     * @return int
     */
    public function getBusinessActorId()
    {
        if (null === self::$intBusinessActorId) {
            $arrUser = SoapSession::getScriptUserDetails('PLUSNET');
            self::$intBusinessActorId = $arrUser['actorId'];
        }

        return self::$intBusinessActorId;
    }


    /**
     * Decides if account is valid for automated account change
     *
     * @param array $a Account data
     *
     * @return boolean
     */
    public function isAccountValidForProcessing(array $a)
    {
        //current SDI does not equal old SDI ?! - not right
        if ($a['intOldSdi'] != $a['intCurrentSdi']) {
            $this->output("Old SDI ({$a['intOldSdi']} not equal current SDI {$a['intCurrentSdi']})");

            return false;
        }
        $this->output("Checks fine - account ready for processing");

        return true;
    }


    /**
     * Includes legacy files required to perform account change
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
        require_once '/local/data/mis/database/database_libraries/secure-transaction-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/class_libraries/Brightview/Account.class.php';
        require_once '/local/data/mis/database/database_libraries/components/HomePhone.class.php';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc';

        // used to apply promo code
        require_once '/local/data/mis/portal_modules/signup/v4/signup_regrade_shared_functions.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/Retention/BroadbandDiscount.class.php';
    }


    /**
     * Get the actor
     *
     * @param String $strUserId User id
     *
     * @return Auth_BusinessActor business actor
     */
    protected function getActorByExternalUserId($strUserId)
    {
        return Auth_BusinessActor::getActorByExternalUserId($strUserId);
    }

    /**
     * Does a given service Id have an active or pending Wlr component?
     *
     * @param int $serviceId Service id
     *
     * @return bool
     */
    protected function hasWlr($serviceId)
    {
        return (CWlrProduct::getWlrProductFromServiceId($serviceId) !== false);
    }

    /**
     * Executes Generate CBC Service Bill for
     * the accounts that have a pending
     * account change for today's run.
     *
     * @param int $intServiceId The service id for the account being processed
     *
     * @return bool
     */
    protected function generateCbcServiceBill($intServiceId)
    {
        $success = true;
        $cmd = $this->getCbcScriptToRunForService($intServiceId);

        if (!empty($cmd)) {
            $cbcExitCode = $this->executeAndReturnCode($cmd);

            if ($cbcExitCode != 0) {
                $success = false;
            }
        } else {
            $this->output('GenerateCbcServiceBills does not need to be run for this account', true);
        }

        return $success;
    }


    /**
     * Returns the CBC script to be run for the account based on CBC Billing Period of the account.
     *
     * @param int $serviceId Service id
     *
     * @return string Script to be executed along with all the parameters
     */
    protected function getCbcScriptToRunForService($serviceId)
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $billingPeriod = $db->getCbcBillingPeriodForService($serviceId);

        $script = "";

        if ($billingPeriod == 'SERVICE') {
            $script = 'php /local/codebase2005/modules/Framework/Scripts/RunScript.php '
                . '-c GenerateCbcServiceBills_InvoicePeriodScript -p PLUSNET '
                . date('d m Y')
                . ' POPULATE DEBUG LIVE ' . $serviceId;
        }

        return $script;
    }

    /**
     * Return the market id from the database based on service definition and market
     *
     * @param int $serviceDefinitionId Service Definition id
     * @param int $marketId market id
     *
     * @return int Tariff Id
     */
    protected function fetchTariffId($serviceDefinitionId, $marketId)
    {
        $db = Db_Manager::getAdaptor('AccountChange');

        return $db->getTariffId($marketId, $serviceDefinitionId);
    }


    /**
     * Returns the active broadband component Id on an account
     *
     * @param int $serviceId Service id of the account
     *
     * @return int
     */
    protected function getActiveBroadbandComponentId($serviceId)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $adslComponentId = $dbAdaptor->getComponentIdForTypeAndStatus('active', 'INTERNET_CONNECTION', $serviceId);

        if (is_array($adslComponentId) && count($adslComponentId) == 1) {
            $activeAdslComponentId = $adslComponentId[0];
        } else {
            $activeAdslComponentId = null;
        }

        return $activeAdslComponentId;
    }

    /**
     * @param int $componentId The id of a component
     *
     * @return int
     *
     * @throws Db_TransactionException
     */
    protected function getProductComponentInstanceId($componentId)
    {
        $dbAdapter = Db_Manager::getAdaptor("AccountChange");

        return $dbAdapter->getSubscriptionProductComponentInstanceIdByComponentId($componentId);
    }

    /**
     * Method to create RetentionsTool_Manager object from adsl scheduled product change Id
     *
     * @param int $scheduleId Adsl scheduled product change Id
     * @param Auth_BusinessActor $actor Target actor
     *
     * @return void
     */
    public function getRetentionManagerByAdslScheduleId($scheduleId, Auth_BusinessActor $actor)
    {
        return RetentionsTool_Manager::getByScheduledEvent(
            RetentionsTool_Manager::SCHEDULE_TYPE_ADSL,
            $scheduleId,
            $actor
        );
    }

    /**
     * Look up the current INTERNET_CONNECTION component id for
     * the given service.
     *
     * @param int $serviceId Service id
     * @param string $status Status
     *
     * @return array
     **/
    protected function getComponentIdForInternetConnection($serviceId, $status)
    {
        $db = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);

        return $db->getComponentIdForTypeAndStatus($status, 'INTERNET_CONNECTION', $serviceId);
    }

    /**
     * Get the INTERNET_CONNECTION service component id from the
     * service definition id and market id
     *
     * @param int $sdi Service definition id
     * @param int $marketId Market id
     *
     * @return int
     **/
    protected function getInternetConnectionId($sdi, $marketId)
    {
        try {
            return ProductFamily_InternetConnectionHelper::getId($sdi, $marketId);
        } catch (Exception $e) {
            // We'll get an exception for service definition based product, return null
            // in this case
            return null;
        }
    }


    /**
     * get the filename to which the debug output for this run will be written
     *
     * @return string
     */
    protected function getDebugFile()
    {
        if (empty($this->debugFile)) {
            $debugFilename = 'AccountChanngeDebug.log-' . date("YmdHis");
            $this->debugFile = self::DEBUG_DIR . '/' . $debugFilename;
        }

        return $this->debugFile;
    }


    /**
     * Write out as much information on the script/environment as possible, for debug purposes
     *
     * @return string
     */
    protected function generateDebugOutput()
    {
        $debugStr = '';
        $sep = "\n===\n";
        $debugStr .= $sep . "Current time: " . date("Y-m-d H:i:s") . "\n";
        $debugStr .= $sep . "Current working directory: " . getcwd() . "\n";
        $debugStr .= $sep . "Hostname: " . gethostname() . "\n";
        $debugStr .= $sep . "\$_SERVER:\n" . print_r($_SERVER, true) . "\n";

        $interfaceList = false;
        exec('/sbin/ifconfig -a', $interfaceList);
        $debugStr .= $sep . "System interface details:\n" . print_r($interfaceList, true) . "\n";

        $processUserDetails = posix_getpwuid(posix_geteuid());
        $debugStr .= $sep . "Process ID: " . getmypid() . "\n";
        $debugStr .= $sep . "Process user details:\n" . print_r($processUserDetails, true) . "\n";

        $debugStr .= $sep . "Stack trace:\n" . print_r(debug_backtrace(), true) . "\n";

        $processList = false;
        exec('ps -ef', $processList);
        $debugStr .= $sep . "System process list:\n" . print_r($processList, true) . "\n";

        return $this->writeToDebugFile($debugStr);
    }

    /**
     * Write debug to file - if the file cannot be written to, the output will be written to the local error log
     *
     * @param str $debugStr the message to be written
     *
     * @return void
     **/
    protected function writeToDebugFile($debugStr)
    {
        $debugFile = $this->getDebugFile();

        if (is_writable(self::DEBUG_DIR)) {
            $bytes = file_put_contents($debugFile, $debugStr, FILE_APPEND);

            if ($bytes === false) {
                throw new Exception(__METHOD__ . "Unable to write to $debugFile\n");
            }
        } else {
            error_log("Unable to write to " . self::DEBUG_DIR . ".  Debug being written to error_log():\n" . $debugStr);
            $debugFile = "error_log_fallback";
        }

        return $debugFile;
    }

    /**
     * Getter for selected contract duration from the registry - which is set when we're
     * recontracting.
     *
     * @return int
     **/
    protected function getSelectedContractDuration()
    {
        return $this->registry->getEntry('selectedContractDuration');
    }

    /**
     * See if there is a scheduled house move for this service
     *
     * @param int $serviceId Service Id
     *
     * @return boolean
     */
    protected function isScheduledHouseMove($serviceId)
    {
        $houseMoveService = ServiceManager::getService('HouseMoveService');

        return $houseMoveService->isScheduledHouseMove($serviceId);
    }

    /**
     * Gets the discount manager
     *
     * @return Retention_DiscountManager
     */
    protected function getDiscountManager()
    {
        return Retention_DiscountManager::getInstance();
    }

    /**
     * Cancels any pending discounts to be applied
     *
     * @param int $serviceId service ID
     *
     * @return void
     */
    private function cancelExistingDiscounts($serviceId)
    {
        $discountManager = $this->getDiscountManager();
        $currentDiscounts = $discountManager->getServiceDiscounts($serviceId);

        if (!empty($currentDiscounts)) {
            foreach ($currentDiscounts as $currentDiscount) {
                if ($currentDiscount->getState() instanceof Retention_DiscountPendingState) {
                    $currentDiscount->cancel(
                        'superseded',
                        AccountChange_Manager::getMyId(),
                        'Cancelled from: ' . __FILE__
                    );
                }
            }

            $discountManager->saveDiscountArray($currentDiscounts);
        }
    }

    /**
     * @param int $sdi product sdi
     * @param array $options account change options
     * @return AccountChange_Product_Configuration
     * @throws AccountChange_Product_ManagerException
     */
    protected function getProductConfiguration($sdi, $options)
    {
        return AccountChange_Product_Manager::factory(
            AccountChange_Product_Manager::ACTION_CHANGE,
            $sdi,
            AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION,
            $options
        );
    }

    /**
     * @return AccountChange_MGALSHelper
     */
    protected function getMGALSHelper()
    {
        if (empty($this->mgalsHelper)) {
            $this->mgalsHelper = new AccountChange_MGALSHelper();
        }
        return $this->mgalsHelper;
    }

    /**
     * @param int $serviceId customer service id
     * @return void
     */
    protected function makeMGALSLive($serviceId)
    {
        /** @var AccountChange_MGALSHelper $mgalsHelper */
        $mgalsHelper = AccountChange_ServiceManager::getService('MgalsHelper');
        $mgalsHelper->makeScheduledMGALSLiveForServiceId($serviceId);
        $this->output($mgalsHelper->getMakeLiveResultMessage());
    }

    /**
     * @param AccountChange_Manager $manager account change manager
     * @return void
     */
    protected function markServiceChangeScheduleComplete($manager)
    {
        $adaptor = Db_Manager::getAdaptor('AccountChange');
        $manager->completeServiceChangeSchedule(
            $manager->getServiceChangeScheduleId(),
            $adaptor
        );
        Db_Manager::commit();
    }

    /**
     * @param int $sdi service definition id
     * @return string
     * @throws Db_TransactionException
     */
    protected function getTechnologyTypeFromSdi($sdi)
    {
        $adaptor = Db_Manager::getAdaptor('AccountChange');
        return $adaptor->getTechnologyType($sdi);
    }

    /**
     * Setter for service definition id
     *
     * @param int $toSdi Service id
     *
     * @return void
     **/
    public function setToSdi($toSdi)
    {
        $this->toSdi = (int)$toSdi;
    }

    /**
     * Getter for new service definition id
     *
     * @return int
     **/
    public function getToSdi()
    {
        return $this->toSdi;
    }

    /**
     * Setter for current sdi
     *
     * @param int $currentSdi Current sdi
     *
     * @return void
     **/
    public function setCurrentSdi($currentSdi)
    {
        $this->currentSdi = (int)$currentSdi;
    }

    /**
     * Getter for current sdi
     *
     * @return int
     **/
    public function getCurrentSdi()
    {
        return $this->currentSdi;
    }

    /**
     * Setter for old sdi
     *
     * @param int $oldSdi Current sdi
     *
     * @return void
     **/
    public function setOldSdi($oldSdi)
    {
        $this->oldSdi = (int)$oldSdi;
    }

    /**
     * Getter for old sdi
     *
     * @return int
     **/
    public function getOldSdi()
    {
        return $this->oldSdi;
    }

    /**
     * @return string
     */
    public function getCurrentAccessTechnology()
    {
        return $this->currentAccessTechnology;
    }

    /**
     * @return string
     */
    public function getNewAccessTechnology()
    {
        return $this->newAccessTechnology;
    }

    /**
     * @return boolean
     */
    protected function setAccessTechnologyValues()
    {
        $this->currentAccessTechnology = $this->getTechnologyTypeFromSdi($this->getCurrentSdi());
        if (!empty($this->getToSdi())) {
            $this->newAccessTechnology = $this->getTechnologyTypeFromSdi($this->getToSdi());
        }
    }

    protected function getFTTPInstallationProcessForFTTPProductChange()
    {
        return $this->getNewAccessTechnology() == 'FTTP' ? $this->lineCheckResults->getFttpInstallProcess() : null;
    }

    /**
     * Restores data required to send email confirming account change
     *
     * @param string  $strNewProductName Product name
     * @param integer $intDiscountLength Discount length
     *
     * @return array - array which is later passed as a template vars to the email template
     */
    protected function restoreEmailData($strNewProductName, $intDiscountLength)
    {
        $arrData = array();

        if (Core_ServiceDefinition::instance($this->objService->getType())->isValueFamilyProduct()) {
            $prices = $this->getUserdataGetPrices($this->objService->getServiceId());
            $floOngoing = empty($prices['subscription']['ongoing']) ? 0 :
                number_format($prices['subscription']['ongoing'], 2);
            $floLead = empty($prices['subscription']['lead']) ? 0 : $prices['subscription']['lead'];
            $objOngoingCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $floOngoing);
            $objDiscountedCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $floLead);
        } else {
            $objOngoingCost
                = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $this->objService->getMinimumCharge());
            $floOngoing = empty($objOngoingCost->toDecimal()) ? 0 : number_format($objOngoingCost->toDecimal(), 2);
            $floLead = 0;
            $objDiscountedCost = null;
        }

        if ($this->lineCheckResults instanceof LineCheck_Result) {
            $arrData['objLineCheckResult'] = $this->lineCheckResults;
            $arrData['bolLinecheckOptOut'] = false;
            $arrData['installationTypeFTTP'] = $this->getFTTPInstallationProcessForFTTPProductChange();
            if ($arrData['objLineCheckResult']->didCustomerOptOut()) {
                $arrData['bolLinecheckOptOut'] = true;
            }
        }

        $arrData['arrSelectedBroadband'] = array();
        $arrData['arrSelectedBroadband']['strNewProduct'] = $strNewProductName;
        $arrData['objOngoingProductCost'] = $objOngoingCost;
        $arrData['objDiscountedProductCost'] = $objDiscountedCost;
        $arrData['floOngoingProductCost'] = $floOngoing;
        $arrData['floDiscountedProductCost'] = $floLead;
        $arrData['intDiscountLength'] = $intDiscountLength;

        return $arrData;
    }
}
