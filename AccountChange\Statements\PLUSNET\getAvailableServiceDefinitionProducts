server: coredb
role: slave
rows: multiple
statement:

SELECT
    sd.name,
    sd.service_definition_id,
    sd.minimum_charge,
    ap.intMaximumSpeed,
    ap.bolCapped,
    ap.vchContract,
    ap.vchContract as vchDisplayName,
    sd.blurb,
    NULL as intTariffID,
    NULL as decLeadPrice,
    NULL as intStartMonth,
    NULL as intEndMonth,
    provProfile.vchName AS provisioningProfile,
    IF(sd.signup_via_portal = 'Y', 1, 0) AS signup_via_portal
FROM products.service_definitions AS sd
LEFT JOIN products.adsl_product AS ap
    ON sd.service_definition_id = ap.service_definition_id
LEFT JOIN products.tblProvisioningProfile AS provProfile
    ON provProfile.intProvisioningProfileID = ap.intProvisioningProfileID
WHERE
    sd.type = :strType
    AND IF (:strIsp = 'force9',
        sd.isp = :strIsp AND sd.name NOT LIKE 'plus.net.uk%',
        IF (:strIsp IN ('force9', 'freeonline'),
            sd.isp = 'plus.net',
            IF (:strIsp IN ('greenbee', 'waitrose'),
                sd.isp ='johnlewis',
                sd.isp = :strIsp)
        )
    )
    AND sd.end_date IS NULL
    AND IF (:bolSignupViaPortalOnly = 1, sd.signup_via_portal = 'Y', true)
    AND IF (:intNumberOfExcludedServiceDefinitions = 0, true, sd.service_definition_id NOT IN (:arrExcludedServiceDefinitionIds))
