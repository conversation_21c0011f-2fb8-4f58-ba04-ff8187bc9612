<?php
/**
 * <PERSON><PERSON><PERSON> to send contract renewal emails
 *
 * @category  AutomatedEmail
 * @package   AutomatedEmail
 * @subpackage Scripts
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @version   CVS:$Id: ContractRenewalEmails.class.php,v ******* 2009/06/12 12:36:34 ajacob Exp $ 
 * @since     File available since 2009-06-10
 */

/**
 * AutomatedEmail_ContractRenewalEmails class - To send contract renewal emails
 *
 * @category  AutomatedEmail
 * @package   AutomatedEmail
 * @subpackage Scripts
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 */

class AutomatedEmail_ContractRenewalEmails implements Mvc_Script
{
    /**
     * Constant for Market area
     */
    const MARKET = 3;
    
    /**
     * Object of LineCheck_Market.
     *
     * @var LineCheck_Market.
     */
    protected $objMarket;
    
    /**
     * Object of IspAutomatedEmail_IspAutomatedEmail.
     *
     * @var IspAutomatedEmail_IspAutomatedEmail.
     */
    protected $objIspAutomatedEmail;
    
    /**
     * Object of LineCheck_Result.
     *
     * @var LineCheck_Result.
     */
    protected $objResult;
    
    /**
     * Object of LineCheck_LineCheck.
     *
     * @var LineCheck_LineCheck.
     */
    protected $objLineCheck;
    
    /**
     * Object of LineCheck_Result.
     *
     * @var LineCheck_Result.
     */
    protected $objLineCheckResult;
    
    /**
     * Function for getting object of LineCheck_Market, if the object is not set.
     *
     * @return  Object objMarket Object of LineCheck_Market.
     */
    private function getMarketObject($strExchangeCode, $strPrivateTransaction)
    {
        if (!isset($this->objMarket)) {
        
            $this->objMarket = LineCheck_Market::getMarketFromExchange($strExchangeCode, $strPrivateTransaction);
        }
        
        return $this->objMarket;
    }
    
    /**
     * Function for getting object of IspAutomatedEmail_IspAutomatedEmail, if the object is not set.
     *
     * @return  Object objIspAutomatedEmail Object of IspAutomatedEmail_IspAutomatedEmail.
     */
    private function createIspAutomatedEmailObject($serviceId)
    {
        if (!isset($this->objIspAutomatedEmail)) {
            
            $this->objIspAutomatedEmail =  new IspAutomatedEmail_IspAutomatedEmail($serviceId, Db_Manager::DEFAULT_TRANSACTION);
        }
        
        return $this->objIspAutomatedEmail;
    }
    
    /**
     * Function for getting object of LineCheck_Result, if the object is not set.
     *
     * @return  Object objLineCheckResult Object of LineCheck_Result.
     */
    private function getLineCheckResultObject()
    {
        if (!isset($this->objLineCheckResult)) {
        
            $this->objLineCheckResult = new LineCheck_Result();
        }
        
        return $this->objLineCheckResult;
    }
    
    /**
     * Function for setting object of LineCheck_Market.
     *
     * @param  Object $objMarket Object of LineCheck_Market.
     */
    public function setMarket(LineCheck_Market $objMarket)
    {
        $this->objMarket = $objMarket;
    }
    
    /**
     * Function for setting object of IspAutomatedEmail_IspAutomatedEmail.
     *
     * @param  Object $objIspAutomatedEmail Object of IspAutomatedEmail_IspAutomatedEmail.
     */
    public function setIspAutomatedEmail(IspAutomatedEmail_IspAutomatedEmail $objIspAutomatedEmail)
    {
        $this->objIspAutomatedEmail = $objIspAutomatedEmail;
    }
    
    /**
     * Function for setting object of LineCheck_Result.
     *
     * @param  Object $objLineCheckResult Object of LineCheck_Result.
     */
    public function setLineCheckResult(LineCheck_Result $objLineCheckResult)
    {
        $this->objLineCheckResult = $objLineCheckResult;
    }
    
    /**
     * Function for getting object of LineCheck_LineCheck, if the object is not set.
     *
     * @return  Object objMarket Object of LineCheck_LineCheck.
     */
    private function getLineCheckObject($objRequest)
    {
        if (!isset($this->objLineCheck)) {
            
            $this->objLineCheck = new LineCheck_LineCheck($objRequest);
        }
        return $this->objLineCheck ;
    }
    
    /**
     * Function for setting object of LineCheck_LineCheck, if the object is not set.
     *
     * @return  Object objMarket Object of LineCheck_LineCheck.
     */
    public function setLineCheckObject(LineCheck_LineCheck $objLineCheck)
    {
        $this->objLineCheck = $objLineCheck;
    }
        
    /**
     * Get Short Options
     * 
     * @return string
     */
    public static function getShortOpts()
    {
        return '';
    }

    /**
     * Get Long Options
     * 
     * @return array
     */
    public static function getLongOpts()
    {
        return array();
    }
    /**
     * Runs the script
     *
     * @param array $arrArgs Parameters passed to the script
     * @param array $opts    Flagged Arguments
     *
     * @return void
     */
    public function run(array $arrArgs, array $opts)
    {  
        try {
            $objAdaptor = Db_Manager::getAdaptor('AutomatedEmail');
            
            //Retrieves list of services due to expire 2 months or 2 weeks from today
            $arrServices = $objAdaptor->getServiceDetailsForRecontract();
            
            foreach ($arrServices as $arrService) {
                
                $objResult       = $this->getLineCheckResult(LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO, (string)$arrService['cli_number']);
                
                $strExchangeCode = (is_null($objResult)) ? null : $objResult->getExchangeCode();
                
                $objMarket = $this->getMarketObject($strExchangeCode, $strPrivateTransaction = Db_Manager::DEFAULT_TRANSACTION);
                $intMarket = $objMarket->getOfcomMarket();
                unset($this->objMarket);

                $objIspAutomatedEmail = $this->createIspAutomatedEmailObject($arrService['service_id']);
                
                if ($intMarket != self::MARKET) {
                    
                    $strTemplate = 'Market_1_2_price_increase';
                    $objIspAutomatedEmail->prepareIspAutomatedEmail($strTemplate);
                } else {                    
                    
                    $strTemplate = 'Market_3_recontracting_offer';
                    $objIspAutomatedEmail->prepareIspAutomatedEmail($strTemplate);
                }
                $objIspAutomatedEmail->send(IspAutomatedEmail_IspAutomatedEmail::SN_INFO);
            }
        } catch (Exception $objException) {
            
            $strError =
                "\n\nAn unexpected Exception of type '" . get_class($objException) .
                "'  was thrown." . "\n\nMessage: " . $objException->getMessage() . "\n\n";
            error_log($objException);
            echo ($strError);
            
            return false;
        }
        
        return true;
    }
     
    /**
     * Function to get the LineChecker_Result object
     *
     * @param string $strLineCheckType line check type
     * @param string $strLineCheckInput cli_number
     * @return $objResult LineChecker_Result object
     */
    public function getLineCheckResult($strLineCheckType, $strLineCheckInput)
    {
        try {
            $objRequest = LineCheck_RequestManager::factory($strLineCheckType, $strLineCheckInput);
            
            $objLineCheck = $this->getLineCheckObject($objRequest);
            
            $objResult = $this->getLineCheckResultObject();
            
            $objLineCheck->sendRequest();

            $objLineCheck->getResult($objResult);

            $objLineCheck->save();
        } 
        catch (LineCheck_BtRequestException $e) {

            $objResult = null;
        }

        return $objResult;
    }
    
    /**
     * Allow Parrallel Runs
     *
     * @return bool
     */
    public function bolAllowParallelRuns()
    {
        return false;
    }
    
    /**
     * Function to include the required files for the script
     *
     */
    private function requireFiles()
    {
        require_once('/local/data/mis/database/standard_include.inc');
        require_once('/local/www/database-admin/config/config.inc');
        require_once('/local/data/mis/database/database_libraries/tickets-access.inc');
        require_once('/local/data/mis/database/database_libraries/CServiceNotice.inc');
    }
}
