<?php
/**
 * Contains the AccountChange_EngineerDetails class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2011 Plusnet
 */

/**
 * Engineer Details requirements class
 *
 * @package    AccountChange
 * @subpackage Application
 * <AUTHOR> <<EMAIL>>
 * @copyright  2011 Plusnet
 */
class AccountChange_EngineerDetails extends Mvc_WizardRequirement
{
    /**
     * Array of form inputs
     * @var array
     */
    protected $arrInputs = array(
        'appointmentdate1'     => 'external:custom:optional',
        'appointmentdate2'     => 'external:custom:optional',
        'appointmentdate3'     => 'external:custom:optional',
        'appointmenttime1'     => 'external:custom:optional',
        'appointmenttime2'     => 'external:custom:optional',
        'appointmenttime3'     => 'external:custom:optional',
        'appointment'          => 'external:custom:optional',
        'engineeringNotes'     => 'external:custom:optional',
        'liveAppointing'       => 'external:bool:optional',
    );

    /**
     * Mappage to find out how internal variables are
     * spawned into creation
     *
     * @var array
     */
    protected $arrValidatorProducts = array(
        'liveAppointingFailed' => 'valLiveAppointingFailed',
    );

    /**
     * A couple of the validators need to run in the right order.
     * The rest will run in whatever order Framework picks them up.
     *
     * @var array
     */
    protected $arrCustomValidatorOrder = array(
        'valTimes',
        'valDates',
    );

    /**
     * A flag to indicate whether live appointing dates were retrived
     * @var bool
     */
    private $_liveAppointingUsed = false;

    /**
     * A regex string to assist parsing dates in 'dd/mm/yyyy' format
     * @var string
     */
    private $_dateregex = '/^(\d{2})\/(\d{2})\/(\d{2,4})/';

    /**
     * Variables to pass to the template for AJAX requests.
     * @var array
     */
    private $_appointingVars = array();

    /**
     * Index action
     *
     * @param array &$arrData Validated Application Data
     *
     * @return void
     */
    public function describe(array &$arrData)
    {
        //display index page
        $leadTime     = $this->getLeadTime();
        $bankHolidays = $this->getBankHolidays($this->getLeadDate($leadTime, I18n_Date::now()));


        //assign engineer data
        $arrReturn['leadTime']       = $leadTime;
        $arrReturn['bankHolidays']   = $this->formatBankHolidaysList($bankHolidays);
        $arrReturn['liveAppointing'] = false;

        $arrReturn['appointingType'] = $this->getAppointingType();
        if ($this->isLiveAppointingRequired()) {
            // Fetch live appointing dates
            $arrReturn['appointmentsDecorated'] =  $this->fetchAvailableAppointments($arrReturn['appointingType']);
            $arrReturn['liveAppointing'] = $this->_liveAppointingUsed;
            $arrReturn = array_merge($arrReturn, $this->_appointingVars);
        }

        $arrReturn['selectedBroadband'] = $this->getSelectedBroadband();

        return $arrReturn;
    }

    /**
     * Get the selected broadband product from an application state variable
     *
     * @return array
     **/
    public function getSelectedBroadband()
    {
        return $this->getApplicationStateVariable('arrSelectedBroadband');
    }

    /**
     * Validator for engineering notes
     *
     * @param string $engineeringNotes the notes
     *
     * @return array
     */
    public function valEngineeringNotes($engineeringNotes)
    {
        if (!Wlr3_OrderValidation::isValid(Wlr3_OrderValidation::NOTES, $engineeringNotes) ||
            !EngineerAppointmentClient_Validator_Fttc::isValid(
                EngineerAppointmentClient_Validator_Fttc::NOTES,
                $engineeringNotes
            )) {

            $this->addValidationError('engineeringNotes', 'INVALID');
        }

        return array(
                'engineeringNotes' => $engineeringNotes,
        );
    }

    /**
     * Validates the live appointment input against the available dates.
     *
     * @param bool   $liveAppointing is live appointing
     * @param string $appointment    the appointment
     *
     * @return array
     */
    public function valAppointment($liveAppointing, $appointment)
    {
        if (empty($liveAppointing)) {
            return array(
                'appointment' => null
            );
        }
        $date = substr($appointment, 0, 10);
        $slot = substr($appointment, 10);

        if (!in_array($slot, AccountChange_EngineerAppointing::$validTimeslots)) {
            $this->addValidationError('appointment', 'INVALID');
        }
        if (!preg_match($this->_dateregex, $date)) {
            $this->addValidationError('appointment', 'INVALID');
        }

        return array(
            'appointment'    => $appointment,
            'appointingType' => $this->getAppointingType(),
        );
    }

    /**
     * Determine whether live appointing failed
     *
     * @param bool $liveAppointing is live appointing
     *
     * @return array
     */
    public function valLiveAppointingFailed($liveAppointing)
    {
        return array(
            'liveAppointing' => $liveAppointing,
            'liveAppointingFailed' => ($this->isLiveAppointingRequired() && !$liveAppointing)
        );
    }

    /**
     * If live appointing was not used, check each of the three times for validity.
     *
     * @param string $appointmenttime1 time 1
     * @param string $appointmenttime2 time 2
     * @param string $appointmenttime3 time 3
     * @param string $liveAppointing   is live appointing
     *
     * @return array
     */
    public function valTimes($appointmenttime1, $appointmenttime2, $appointmenttime3, $liveAppointing)
    {
        if ($liveAppointing) {
            return array (
                'appointmenttime1' => null,
                'appointmenttime2' => null,
                'appointmenttime3' => null,
            );
        }
        if (!in_array($appointmenttime1, AccountChange_EngineerAppointing::$validTimeslots)) {
            $this->addValidationError('appointmenttime1', 'INVALID');
        }
        if (!in_array($appointmenttime2, AccountChange_EngineerAppointing::$validTimeslots)) {
            $this->addValidationError('appointmenttime2', 'INVALID');
        }
        if (!in_array($appointmenttime3, AccountChange_EngineerAppointing::$validTimeslots)) {
            $this->addValidationError('appointmenttime3', 'INVALID');
        }

        return array(
            'appointmenttime1' => $appointmenttime1,
            'appointmenttime2' => $appointmenttime2,
            'appointmenttime3' => $appointmenttime3,
        );
    }

    /**
     * If Live Appointing was not used, check each date for validity
     *
     * @param string $appointmentdate1 date 1
     * @param string $appointmentdate2 date 2
     * @param string $appointmentdate3 date 3
     * @param string $appointmenttime1 time 1
     * @param string $appointmenttime2 time 2
     * @param string $appointmenttime3 time 3
     * @param bool   $liveAppointing   is live appointing
     *
     * @return array
     */
    public function valDates(
        $appointmentdate1,
        $appointmentdate2,
        $appointmentdate3,
        $appointmenttime1,
        $appointmenttime2,
        $appointmenttime3,
        $liveAppointing
    ) {
        if ($liveAppointing) {
            return array (
                'appointmentdate1' => null,
                'appointmentdate2' => null,
                'appointmentdate3' => null,
            );
        }

        //calculate the mininum date
        $leadTime = $this->getLeadTime();
        $leadDate = $this->getLeadDate($leadTime, I18n_Date::now());
        $minLeadTimestamp = $leadDate->getTimestamp();

        foreach (array('appointmentdate1', 'appointmentdate2', 'appointmentdate3') as $appointmentVar) {
            if (preg_match($this->_dateregex, $$appointmentVar, $matches)) {
                $$appointmentVar = I18n_Date::getValidated(
                    array(
                        'Day'   => $matches[1],
                        'Month' => $matches[2],
                        'Year'  => $matches[3],
                    )
                );

                if ($$appointmentVar->getTimestamp() < $minLeadTimestamp) {
                    $this->addValidationError($appointmentVar, 'LEADTIME');
                }

                if ($$appointmentVar->isSunday()) {
                    $this->addValidationError($appointmentVar, 'WEEKEND');
                }

                if ($this->isHoliday($$appointmentVar->getTimestamp())) {
                    $this->addValidationError($appointmentVar, 'BANKHOLIDAY');
                }

            } else {
                    $this->addValidationError($appointmentVar, 'INVALID');
            }

        }

        if ($appointmentdate1 == $appointmentdate2 && $appointmenttime1 == $appointmenttime2) {
            $this->addValidationError('appointmentdate1', 'NOTUNIQUE');
            $this->addValidationError('appointmentdate2', 'NOTUNIQUE');
        }

        if ($appointmentdate2 == $appointmentdate3 && $appointmenttime2 == $appointmenttime3) {
            $this->addValidationError('appointmentdate2', 'NOTUNIQUE');
            $this->addValidationError('appointmentdate3', 'NOTUNIQUE');
        }

        if ($appointmentdate1 == $appointmentdate3 && $appointmenttime1 == $appointmenttime3) {
            $this->addValidationError('appointmentdate1', 'NOTUNIQUE');
            $this->addValidationError('appointmentdate3', 'NOTUNIQUE');
        }

        return array(
            'appointmentdate1' => $appointmentdate1 instanceof I18n_Date ? $appointmentdate1->getTimestamp() : null,
            'appointmentdate2' => $appointmentdate2 instanceof I18n_Date ? $appointmentdate2->getTimestamp() : null,
            'appointmentdate3' => $appointmentdate3 instanceof I18n_Date ? $appointmentdate3->getTimestamp() : null,
            'appointingType' => $this->getAppointingType(),
        );
    }

    /**
     * Which service is the appointment for?
     *
     * @return array
     */
    public function getAppointingType()
    {
        // Presently there is only one type of appointing available in account change
        return array(
            'serviceHandle' => EngineerAppointmentClient_Service::FTTC_HANDLE,
            'service'        => EngineerAppointmentClient_Service::FTTC,
        );
    }

    /**
     * Should we try to do live appointing? We try if we've got a valid matched address
     *
     * @return bool
     */
    public function isLiveAppointingRequired()
    {
        return $this->isApplicationStateVariable('addressRef');
    }

    /**
     * Returns the min date based on lead days
     *
     * @param int       $leadDays The mininum number of working days
     * @param I18n_Date $start    The date to count from
     *
     * @return I18n_Date
     */
    public function getLeadDate($leadDays, I18n_Date $start)
    {
        $leadDate = clone $start;
        for ($i = 0; $i < $leadDays; $i++) {
            //if we detect a weekend, add another day
            if (I18n_Date::isWeekend($leadDate->getTimestamp())) {
                $leadDays++;
            }

            //add 1 day to lead time for every iteration
            $leadDate->modify(1, I18n_Date::DAYS);
        }

        return $leadDate;
    }

    /**
     * Return min lead required for an engineer appointment
     *
     * @return int
     */
    public function getLeadTime()
    {
        $addressParts = $this->isApplicationStateVariable('addressRef') ?
                        explode(':', $this->getApplicationStateVariable('addressRef'))
                        : null;
        $addressCategory = isset($addressParts[2]) ? $addressParts[2] : null;

        $leadTimes = new SignupApplication_LeadTimes();
        $fttcAttached = true;
        $simOrder = false;
        $wlrAttached  = $this->isApplicationStateVariable('intNewWlrId');
        $linecheck = $this->getApplicationStateVariable('objLineCheckResult');

        return $leadTimes->getFttcLeadTime(
            'A',
            $wlrAttached,
            $fttcAttached,
            $linecheck->isFttcOnLine(),
            $linecheck->isAdslOnLine(),
            false,
            $simOrder,
            $addressCategory
        );
    }

    /**
     * Fetch a list of bank holidays starting from the specified date
     *
     * @param I18n_Date $startDate the start date
     *
     * @return array
     */
    public function getBankHolidays(I18n_Date $startDate)
    {
        return I18n_Date::getHolidays(
            $startDate->getTimestamp(),
            500
        );
    }

    /**
     * Returns list of bank holidays as string formatted for JavaScript
     * Json encoded array
     * Timestamps with appended milliseconds part
     * Double quotes replaced with single quotes
     *
     * @param array $bankHolidays List of bank holidays as timestamps
     *
     * @return string Json encoded array with timestamps
     */
    protected function formatBankHolidaysList(array $bankHolidays)
    {
        $bankHolidaysList = array();

        foreach ($bankHolidays as $bankHoliday) {

            //Append milliseconds part to timestamp for JS compatibility
            $bankHolidaysList[] = $bankHoliday . '000';
        }

        //Encode as JSON array and replace double quotes with single quotes
        return str_replace('"', '\'', json_encode($bankHolidaysList));
    }


    /**
     * Fetch the available live appointments using the data collected so far from the
     * signup application
     *
     * @param array $appointingType Should contain keys 'serviceHandle' and 'service'
     *
     * @api
     *
     * @return array
     */
    public function fetchAvailableAppointments(array $appointingType)
    {
        $appointmentHelper = $this->getEngineeringAppointingHelper();
        $appointments = array();
        $appointmentsDecorated = array();

        $addressParts = explode(':', $this->getApplicationStateVariable('addressRef'));

        $vars['service']         = $appointingType['service'];
        $vars['serviceHandle']   = $appointingType['serviceHandle'];
        $vars['sparePairs']      = null;
        $vars['journeyType']     = 'A'; // An assumption. All account changes should already have active bb.
        $vars['addressRef']      = isset($addressParts[0]) ? $addressParts[0] : null;
        $vars['databaseCode']    = isset($addressParts[1]) ? $addressParts[1] : null;
        $vars['addressCategory'] = isset($addressParts[2]) ? $addressParts[2] : null;

        $vars['cli'] = $this->isApplicationStateVariable('intPhoneNumber')
                     ? $this->getApplicationStateVariable('intPhoneNumber')
                     : null;

        $vars['extensionKitId'] = $this->isApplicationStateVariable('extensionKitId')
                                ? $this->getApplicationStateVariable('extensionKitId')
                                : EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED;

        $vars['locationType'] = ($this->isApplicationStateVariable('bolIsBusiness')
                                 && $this->getApplicationStateVariable('bolIsBusiness') == true)
                ? EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_BUSINESS
                : EngineerAppointmentClient_Service_Pstn::APPOINTMENTREQUEST_LOCATIONTYPE_RESIDENTIAL;

        //Get live appointment dates
        try {
            $result = $appointmentHelper->getLiveAppointments($vars);
            $appointments = $result['appointments'];
            $vars['offsetDate'] = $result['offsetDate'];

            if (is_array($appointments) && !empty($appointments)) {

                $this->_liveAppointingUsed = true;
                $appointmentsDecorated = $appointmentHelper->decorateAppointments($appointments);
            }

        } catch (Exception $exception) {
            $this->auditLog('Live appointing failed: ' . $exception->getMessage());
            $this->_liveAppointingUsed = false;
        }

        // Save the variables we used, we'll need to send them to the template for AJAX requests
        $this->_appointingVars = $vars;
        return $appointmentsDecorated;
    }

    /**
     * Get the EngineerAppointing Helper object
     *
     * @return AccountChange_EngineerAppointing
     */
    protected function getEngineeringAppointingHelper()
    {
        return new AccountChange_EngineerAppointing();
    }

    /**
     * Does a given date fall on a bank holiday
     * Encapsulated here to make unit testing easier.
     *
     * @param int $date Date in unix timestamp format
     *
     * @return bool
     */
    protected function isHoliday($date)
    {
        return I18n_Date::isHoliday($date);
    }

    /**
     * Write to the audit log
     *
     * @param string $message the message
     *
     * @return void
     */
    protected function auditLog($message)
    {
        Log_AuditLog::write($message, __CLASS__);
    }
}
