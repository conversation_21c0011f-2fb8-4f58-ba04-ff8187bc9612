<?php
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/database_libraries/product-access.inc';
/**
 * Static Ip Action
 *
 * Testing class for the AccountChange_Action_StaticIp class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Action_StaticIp.test.php,v 1.2 2009-01-27 09:07:37 bselby Exp $
 * @since      File available since 2008-10-22
 */
/**
 * Static Ip Action Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Action_StaticIp_Test extends PHPUnit_Framework_TestCase
{
    protected $intServiceId;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        $this->intServiceId = 999;
    }


    /**
     * @covers AccountChange_Action_StaticIp::execute
     *
     */
    public function testExecuteCallsConfigureStaticIpIfThereIsNoActiveStaticIp()
    {
        $objDatabaseMock = $this->getMock('Db_Adaptor',
                                          array('getNumberAllocatedForIpZone'),
                                          array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objDatabaseMock->expects($this->once())
                        ->method('getNumberAllocatedForIpZone')
                        ->will($this->returnValue('10'));

        Db_Manager::setAdaptor('AccountChange', $objDatabaseMock);


        $objUserdataSplitter = $this->getMock('Lib_Userdata',
                                              array('userdata_component_find'),
                                              array(), '', false);

        $objUserdataSplitter->expects($this->at(0))
                            ->method('userdata_component_find')
                            ->will($this->returnValue(array()));

        $objUserdataSplitter->expects($this->at(1))
                            ->method('userdata_component_find')
                            ->will($this->returnValue(array(array('component_id' => 1,
                                                                  'component_type_id' => 1,
                                                                  'number_allocated' => 1))));

        Lib_Userdata::setInstance($objUserdataSplitter);


        $objMock = $this->getMock('AccountChange_Action_StaticIp',
                                  array('includeLegacyFiles', 'configureStaticIp', 'getStaticComponentTypeIds'),
                                  array($this->intServiceId));

        $objMock->expects($this->once())
                ->method('includeLegacyFiles');

        $objMock->expects($this->once())
                ->method('getStaticComponentTypeIds')
                ->will($this->returnValue(array()));

        $objMock->expects($this->once())
                ->method('configureStaticIp');

        $objMock->execute();
    }

    /**
     * @covers AccountChange_Action_StaticIp::configureStaticIp
     *
     */
    public function testExecuteDoesNothingIfThereIsAnActiveStaticIp()
    {
        $objDatabaseMock = $this->getMock('Db_Adaptor',
                                          array('getNumberAllocatedForIpZone'),
                                          array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objDatabaseMock->expects($this->never())
                        ->method('getNumberAllocatedForIpZone')
                        ->will($this->returnValue(null));

        Db_Manager::setAdaptor('AccountChange', $objDatabaseMock);


        $objUserdataSplitter = $this->getMock('Lib_Userdata',
                                              array('userdata_component_find'),
                                              array(), '', false);

        $objUserdataSplitter->expects($this->once())
                            ->method('userdata_component_find')
                            ->will($this->returnValue(array(array('someKey' => 'someValue'))));

        Lib_Userdata::setInstance($objUserdataSplitter);


        $objMock = $this->getMock('AccountChange_Action_StaticIp',
                                  array('includeLegacyFiles', 'getStaticComponentTypeIds'),
                                  array($this->intServiceId));

        $objMock->expects($this->once())
                ->method('includeLegacyFiles');

        $objMock->expects($this->once())
                ->method('getStaticComponentTypeIds')
                ->will($this->returnValue(array()));

        $objMock->execute();
    }

    /**
     * @covers AccountChange_Action_StaticIp::getStaticComponentTypeIds
     *
     */
    public function testGetStaticComponentTypeIdsCallsCorrectLegacyFunction()
    {
        $objProductSplitter = $this->getMock('Lib_Product',
                                             array('product_staticip_component_types_get'),
                                             array(), '', false);

        $objProductSplitter->expects($this->once())
                           ->method('product_staticip_component_types_get')
                           ->will($this->returnValue(array('service_component_id' => 1)));

        Lib_Product::setInstance($objProductSplitter);

        $objMock = $this->getMock('AccountChange_Action_StaticIp',
                                  array('includeLegacyFiles'),
                                  array($this->intServiceId));

        $objMock->expects($this->once())
                ->method('includeLegacyFiles');

        $objMock->getStaticComponentTypeIds();
    }
}
