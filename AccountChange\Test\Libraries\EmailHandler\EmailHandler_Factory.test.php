<?php


class AccountChange_EmailHandler_Factory_Test extends PHPUnit_Framework_TestCase
{
    public function emailHandlerFactoryDataProvider()
    {
        return [
            'upgrade has current contract' => [
                'changeDate' => '12/12/2050',
                'oldSdi' => 123,
                'toSdi' => 456,
                'old product name' => 'OLD PRODUCT',
                'new product name' => 'NEW PRODUCT',
                'existingContract' => true,
                'isBusiness' => false,
                'expectedTemplateHandle' => AccountChange_EmailHandler_Factory::UPGRADE_EMAIL_HANDLE
            ],
            'upgrade no current contract' => [
                'changeDate' => '12/12/2050',
                'oldSdi' => 123,
                'toSdi' => 456,
                'old product name' => 'OLD PRODUCT',
                'new product name' => 'NEW PRODUCT',
                'existingContract' => false,
                'isBusiness' => false,
                'expectedTemplateHandle' => AccountChange_EmailHandler_Factory::UPGRADE_EMAIL_HANDLE
            ],
            'recontract' => [
                  'changeDate' => '12/12/2050',
                  'oldSdi' => 123,
                  'toSdi' => 123,
                  'old product name' => 'OLD PRODUCT',
                  'new product name' => 'OLD PRODUCT',
                  'existingContract' => true,
                  'isBusiness' => false,
                  'expectedTemplateHandle' => AccountChange_EmailHandler_Factory::RECONTRACT_EMAIL_HANDLE
            ],
            'recontract business ' => [
                'changeDate' => '12/12/2050',
                'oldSdi' => 123,
                'toSdi' => 123,
                'old product name' => 'OLD PRODUCT',
                'new product name' => 'OLD PRODUCT',
                'existingContract' => true,
                'isBusiness' => true,
                'expectedTemplateHandle' => AccountChange_EmailHandler_Factory::RECONTRACT_EMAIL_HANDLE_BUSINESS
            ],
        ];
    }

    /**
     * Test that the factory correctly builds a confirmation email object and populates it with the correct data
     *
     * @dataProvider emailHandlerFactoryDataProvider
     * @param $changeDate
     * @param $oldSdi
     * @param $toSdi
     * @param $oldProductName
     * @param $newProductName
     * @param $existingContract
     * @param $isBusiness
     * @param $expectedTemplateHandle
     */
    public function testConfirmationEmailIsSetUpCorrectly(
        $changeDate,
        $oldSdi,
        $toSdi,
        $oldProductName,
        $newProductName,
        $existingContract,
        $isBusiness,
        $expectedTemplateHandle
    ) {
        $performChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getScheduledChangeDate','getTechnologyType'),
            array()
        );

        $performChangeApi
            ->expects($this->once())
            ->method('getScheduledChangeDate')
            ->willReturn($changeDate);

        $performChangeApi
            ->expects($this->once())
            ->method('getTechnologyType')
            ->willReturn('ADSL');

        $performChangeApi->setOldSdi($oldSdi);
        $performChangeApi->setToSdi($toSdi);
        $performChangeApi->setOldProductName($oldProductName);
        $performChangeApi->setNewProductName($newProductName);

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrderProducts->addServiceComponentId(
            AccountChange_AccountChangeOrderProducts::PHONE_COMPONENT_ID,
            999
        );

        $accountChangeOrder = new AccountChange_AccountChangeOrder();
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        if ($existingContract) {
            $accountChangeOrder->setContract(new AccountChange_AccountChangeOrderContract());
        }

        $emailHandlerFactory = $this->getMock(
            'AccountChange_EmailHandler_Factory',
            array(
                'getAccountInstance',
                'doesNotHaveActiveDirectDebitInstruction',
                'isNewDDReady',
                'getPriceHelper',
                'hasActiveLineRentalSaver',
                'getCallPlanDetailsByWlrServiceComponentId',
                'getSpeedHelper',
                'getContractMessageHelper',
                'getCoreServiceDefinition'),
            array($performChangeApi, $accountChangeOrder)
        );
        $account = $this->getMock(
            'AccountChange_Account',
            array(
                'getWlrInformation',
                'getNextInvoiceDate'),
            array(new Int(1))
        );

        $coreServiceDef = $this->getMock(
            'Core_ServiceDefinition',
            array('isBusiness'),
            [],
            '',
            false
        );

        $account
            ->expects($this->once())
            ->method('getWlrInformation')
            ->willReturn(array(
                'intOldWlrId'        => '123',
                'activeCallFeatures' => array(
                    1 => 'callfeature1',
                    2 => 'callfeature2',
                    3 => 'callfeature3'
                )
            ));

        $coreServiceDef
            ->expects($this->any())
            ->method('isBusiness')
            ->willReturn($isBusiness);

        $account
            ->expects($this->once())
            ->method('getNextInvoiceDate')
            ->willReturn('1945-12-12');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getAccountInstance')
            ->willReturn($account);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getCoreServiceDefinition')
            ->willReturn($coreServiceDef);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('doesNotHaveActiveDirectDebitInstruction')
            ->willReturn(true);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('isNewDDReady')
            ->willReturn(true);

        $priceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices',),
            array(1, 2, 3, 4, 5, null)
        );

        $priceHelper
            ->expects($this->once())
            ->method('getNewPackagePrices')
            ->willReturn(array(
                'broadband'        => 11.11,
                'lineRental'       => 22.22,
                'callPlan'         => 33.33,
                'callFeatures'     => 44.44,
                'total'            => 10,
                'discountDuration' => 18,
                'discountAmount'   => 4
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getPriceHelper')
            ->willReturn($priceHelper);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->willReturn(false);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getCallPlanDetailsByWlrServiceComponentId')
            ->with(999)
            ->willReturn(array(
                'strDisplayName' => 'NEW CALL PLAN'
            ));

        $speedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array('getSpeedData'),
            array(1, 2)
        );

        $speedHelper
            ->expects($this->once())
            ->method('getSpeedData')
            ->willReturn(array(
                'minimumEstimatedDownloadSpeedMbs' => 1,
                'maximumEstimatedDownloadSpeedMbs' => 2,
                'minimumEstimatedUploadSpeedMbs'   => 3,
                'maximumEstimatedUploadSpeedMbs'   => 4,
                'broadbandSpeedRange'              => 5,
                'guaranteedSpeedValue'             => 6,
                'maximumDownloadSpeedMbs'          => 7,
                'advertisedDownloadSpeedMbs'       => 8,
                'advertisedUploadSpeedMbs'         => 9
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getSpeedHelper')
            ->willReturn($speedHelper);

        $contractMessageHelper = $this->getMock(
            'AccountChange_EmailHandler_ContractMessageHelper',
            array('generateContractMessage'),
            array(null, null)
        );

        $contractMessageHelper
            ->expects($this->once())
            ->method('generateContractMessage')
            ->willReturn('CONTRACT MESSAGE');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getContractMessageHelper')
            ->willReturn($contractMessageHelper);

        $expected = new AccountChange_ConfirmationEmailHandler();
        $expected->setEmailName($expectedTemplateHandle);
        $expected->setNewDirectDebit(true);
        $expected->setNewDirectDebitReady(true);
        $expected->setChangeDate(date('d/m/Y', strtotime('12/12/2050')));
        $expected->setCurrentDualPlay(true);
        $expected->setBroadbandSubscriptionCost('11.11');
        $expected->setLineRentalSubscriptionCost('22.22');
        $expected->setCallPlanSubscriptionCost('33.33');
        $expected->setCallFeatureSubscriptionCost('44.44');
        $expected->setNewPrice('10.00');
        $expected->setNewLineRental('22.22');
        $expected->setSpecialOffer(true);
        $expected->setOfferDuration(18);
        $expected->setDiscountAmount('4.00');
        $expected->setOfferPrice('6.00');
        $expected->setCurrentBroadbandProduct($oldProductName);
        $expected->setNewBroadbandProduct($newProductName);
        $expected->setLineRentalFrequency('Monthly');
        $expected->setNewCallPlan('NEW CALL PLAN');
        $expected->setCallFeatures('callfeature1, callfeature2, callfeature3');
        $expected
            ->setMinimumEstimatedDownloadSpeedMbs(1)
            ->setMaximumEstimatedDownloadSpeedMbs(2)
            ->setMinimumEstimatedUploadSpeedMbs(3)
            ->setMaximumEstimatedUploadSpeedMbs(4)
            ->setBroadbandSpeedRange(5)
            ->setGuaranteedSpeedValue(6)
            ->setMaximumDownloadSpeedMbs(7)
            ->setAdvertisedDownloadSpeedMbs(8)
            ->setAdvertisedUploadSpeedMbs(9);
        $expected->setRecontracted(true);
        $expected->setContractDuration('CONTRACT MESSAGE');
        $expected->setCallerClassName('AccountChangeApi');

        $actual = $emailHandlerFactory->buildConfirmationEmailForPerformChangeApi();

        $this->assertEquals([$expected], $actual);
    }

    /**
     * @test
     */
    public function shouldSetUpConfirmationEmailCorrectlyIfTechnologyTypeIsFTTP()
    {
        $performChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            [
                'getScheduledChangeDate',
                'getTechnologyType',
                'getLineCheckResults',
                'getCurrentAccessTechnology',
                'getNewAccessTechnology'
            ],
            []
        );

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            ['getFttpInstallProcess'],
            []
        );

        $mockLineCheck
            ->expects($this->once())
            ->method('getFttpInstallProcess')
            ->willReturn('FTTP INSTALLATION TYPE');

        $performChangeApi
            ->expects($this->once())
            ->method('getScheduledChangeDate')
            ->willReturn('2022-07-05');

        $performChangeApi
            ->expects($this->once())
            ->method('getTechnologyType')
            ->willReturn('FTTP');

        $performChangeApi
            ->expects($this->once())
            ->method('getCurrentAccessTechnology')
            ->willReturn('ADSL');

        $performChangeApi
            ->expects($this->once())
            ->method('getNewAccessTechnology')
            ->willReturn('FTTP');

        $performChangeApi
            ->expects($this->once())
            ->method('getLineCheckResults')
            ->willReturn($mockLineCheck);

        $performChangeApi->setOldSdi(1);
        $performChangeApi->setToSdi(2);
        $performChangeApi->setOldProductName('OLD PRODUCT NAME');
        $performChangeApi->setNewProductName('NEW PRODUCT NAME');

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrder = new AccountChange_AccountChangeOrder();
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setContract(new AccountChange_AccountChangeOrderContract());

        $emailHandlerFactory = $this->getMock(
            'AccountChange_EmailHandler_Factory',
            array(
                'getAccountInstance',
                'doesNotHaveActiveDirectDebitInstruction',
                'isNewDDReady',
                'getPriceHelper',
                'hasActiveLineRentalSaver',
                'getCallPlanDetailsByWlrServiceComponentId',
                'getSpeedHelper',
                'getContractMessageHelper',
                'getCoreServiceDefinition'
            ),
            array($performChangeApi, $accountChangeOrder)
        );
        $account = $this->getMock(
            'AccountChange_Account',
            array(
                'getWlrInformation',
                'getNextInvoiceDate'),
            array(new Int(1))
        );

        $coreServiceDef = $this->getMock(
            'Core_ServiceDefinition',
            array('isBusiness'),
            [],
            '',
            false
        );

        $account
            ->expects($this->never())
            ->method('getWlrInformation');

        $coreServiceDef
            ->expects($this->any())
            ->method('isBusiness')
            ->willReturn(false);

        $account
            ->expects($this->once())
            ->method('getNextInvoiceDate')
            ->willReturn('2022-08-05');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getAccountInstance')
            ->willReturn($account);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getCoreServiceDefinition')
            ->willReturn($coreServiceDef);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('doesNotHaveActiveDirectDebitInstruction')
            ->willReturn(true);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('isNewDDReady')
            ->willReturn(true);

        $priceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(1, 2, 3, 4, 5, null)
        );

        $priceHelper
            ->expects($this->once())
            ->method('getNewPackagePrices')
            ->willReturn(array(
                'broadband'        => 11.11,
                'lineRental'       => 0.00,
                'callPlan'         => 0.00,
                'callFeatures'     => 0.00,
                'total'            => 11.11,
                'discountDuration' => 18,
                'discountAmount'   => 4
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getPriceHelper')
            ->willReturn($priceHelper);

        $emailHandlerFactory
            ->expects($this->never())
            ->method('hasActiveLineRentalSaver');

        $emailHandlerFactory
            ->expects($this->never())
            ->method('getCallPlanDetailsByWlrServiceComponentId');

        $speedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array('getSpeedData'),
            array(1, 2)
        );

        $speedHelper
            ->expects($this->once())
            ->method('getSpeedData')
            ->willReturn(array(
                'minimumEstimatedDownloadSpeedMbs' => 1,
                'maximumEstimatedDownloadSpeedMbs' => 2,
                'minimumEstimatedUploadSpeedMbs'   => 3,
                'maximumEstimatedUploadSpeedMbs'   => 4,
                'broadbandSpeedRange'              => 5,
                'guaranteedSpeedValue'             => 6,
                'maximumDownloadSpeedMbs'          => 7,
                'advertisedDownloadSpeedMbs'       => 8,
                'advertisedUploadSpeedMbs'         => 9
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getSpeedHelper')
            ->willReturn($speedHelper);

        $contractMessageHelper = $this->getMock(
            'AccountChange_EmailHandler_ContractMessageHelper',
            array('generateContractMessage'),
            array(null, null)
        );

        $contractMessageHelper
            ->expects($this->once())
            ->method('generateContractMessage')
            ->willReturn('CONTRACT MESSAGE');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getContractMessageHelper')
            ->willReturn($contractMessageHelper);

        $expected = new AccountChange_ConfirmationEmailHandler();
        $expected->setEmailName(AccountChange_EmailHandler_Factory::UPGRADE_EMAIL_HANDLE);
        $expected->setNewDirectDebit(true);
        $expected->setNewDirectDebitReady(true);
        $expected->setChangeDate('2022-07-05');
        $expected->setCurrentDualPlay(false);
        $expected->setBroadbandSubscriptionCost('11.11');
        $expected->setLineRentalSubscriptionCost('0.00');
        $expected->setCallPlanSubscriptionCost('0.00');
        $expected->setCallFeatureSubscriptionCost('0.00');
        $expected->setNewPrice('11.11');
        $expected->setNewLineRental('0.00');
        $expected->setSpecialOffer(true);
        $expected->setOfferDuration(18);
        $expected->setDiscountAmount('4.00');
        $expected->setOfferPrice('7.11');
        $expected->setCurrentBroadbandProduct('OLD PRODUCT NAME');
        $expected->setNewBroadbandProduct('NEW PRODUCT NAME');
        $expected->setLineRentalFrequency(null);
        $expected->setNewCallPlan(null);
        $expected->setCallFeatures(null);
        $expected
            ->setMinimumEstimatedDownloadSpeedMbs(1)
            ->setMaximumEstimatedDownloadSpeedMbs(2)
            ->setMinimumEstimatedUploadSpeedMbs(3)
            ->setMaximumEstimatedUploadSpeedMbs(4)
            ->setBroadbandSpeedRange(5)
            ->setGuaranteedSpeedValue(6)
            ->setMaximumDownloadSpeedMbs(7)
            ->setAdvertisedDownloadSpeedMbs(8)
            ->setAdvertisedUploadSpeedMbs(9);
        $expected->setRecontracted(true);
        $expected->setContractDuration('CONTRACT MESSAGE');
        $expected->setCallerClassName('AccountChangeApi');
        $expected->setFttpInstallationType('FTTP INSTALLATION TYPE');
        $expected->setFttpProduct(true);
        $expected->setAccessTechnologyChanging(true);

        $actual = $emailHandlerFactory->buildConfirmationEmailForPerformChangeApi();

        $this->assertEquals([$expected], $actual);
    }

    /**
     * @param AccountChange_EmailHandler $expectedClass    expected email class
     * @param string                     $orderType        order type
     * @param boolean                    $isInstant        is instant change
     * @param int                        $confirmCalled    how many times should confirm be called
     * @param int                        $completionCalled how many times should completion be called
     * @dataProvider getDataForFactory()
     */
    public function testFactoryReturnsCorrectType(
        $expectedClass,
        $orderType,
        $isInstant,
        $isPhoneOnlyChange,
        $confirmCalled,
        $completionCalled
    ) {

        $mockPerformAccountChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getServiceId','getMarketId','getPromoCode','getLineCheckResults','getToSdi', 'getOldWlrServiceComponentId', 'getNewWlrServiceComponentId'),
            array(),
            '',
            false
        );

        $mockOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getType'),
            array(),
            '',
            false
        );

        $mockOrder->setIsScheduledChange(!$isInstant);

        $mockOrder->expects($this->once())
            ->method('getType')
            ->will($this->returnValue($orderType));

        $emailHandlerFactory = $this->getMock(
            'AccountChange_EmailHandler_Factory',
            array('isRecontracting',
                'getAccountChangeConfirmation',
                'getAccountChangeCompletion',
                'getPhoneOnlyChangeCompletion'),
            array($mockPerformAccountChangeApi, $mockOrder)
        );

        $mockWorkplaceConfirmation = $this->getMock(
            'AccountChange_EmailHandler_Confirmation',
            array(),
            array(),
            '',
            false
        );

        $mockWorkplaceCompletion = $this->getMock(
            'AccountChange_EmailHandler_Completion',
            array(),
            array(),
            '',
            false
        );


        $mockPhoneOnlyChange = $this->getMock(
            'AccountChange_EmailHandler_PhoneOnlyChange',
            array(),
            array(),
            '',
            false
        );

        $emailHandlerFactory->expects($this->once())
            ->method('isRecontracting')
            ->will($this->returnValue(false));

        if($isInstant) {
            if ($completionCalled && !$isPhoneOnlyChange) {
                $mockPerformAccountChangeApi->setCurrentSdi(1);
                $emailHandlerFactory->expects($this->exactly($completionCalled))
                    ->method('getAccountChangeCompletion')
                    ->will($this->returnValue($mockWorkplaceCompletion));
            } else {
                $mockPerformAccountChangeApi->setToSdi(1);

                $mockPerformAccountChangeApi->expects($this->any())
                    ->method('getOldWlrServiceComponentId')
                    ->will($this->returnValue(1));

                $mockPerformAccountChangeApi->expects($this->any())
                    ->method('getNewWlrServiceComponentId')
                    ->will($this->returnValue(2));

                $emailHandlerFactory->expects($this->exactly(1))
                    ->method('getPhoneOnlyChangeCompletion')
                    ->will($this->returnValue($mockPhoneOnlyChange));

                if ($completionCalled) {
                    $mockPerformAccountChangeApi->setToSdi(1);
                    $mockPerformAccountChangeApi->setCurrentSdi(1);

                    $emailHandlerFactory->expects($this->exactly($completionCalled))
                        ->method('getAccountChangeCompletion')
                        ->will($this->returnValue($mockWorkplaceCompletion));
                }
            }
        } else {

            $emailHandlerFactory->expects($this->exactly($confirmCalled))
                ->method('getAccountChangeConfirmation')
                ->will($this->returnValue($mockWorkplaceConfirmation));
        }

        $returnedValue = $emailHandlerFactory->buildConfirmationEmailForPerformChangeApi();

        $this->assertTrue($returnedValue[0] instanceof $expectedClass[0]);

        if ($isPhoneOnlyChange && $completionCalled) {
            $this->assertTrue($returnedValue[1] instanceof $expectedClass[1]);
        }
    }

    /**
     * @return array
     */
    public function getDataForFactory()
    {
        return [
            'Workplace account change - confirmation' => [
                [AccountChange_EmailHandler_Confirmation::class],
                AccountChange_AccountChangeOrder::TYPE_WORKPLACE_ACCOUNT_CHANGE,
                false,
                false,
                1,
                0
            ],
            'Workplace account change - completion' => [
                [AccountChange_EmailHandler_Completion::class],
                AccountChange_AccountChangeOrder::TYPE_WORKPLACE_ACCOUNT_CHANGE,
                true,
                false,
                0,
                1
            ],
            'Workplace phone only change' => [
                [AccountChange_EmailHandler_PhoneOnlyChange::class],
                AccountChange_AccountChangeOrder::TYPE_WORKPLACE_ACCOUNT_CHANGE,
                true,
                true,
                0,
                0
            ],
            'Workplace broadband and phone change' => [
                [
                    AccountChange_EmailHandler_Completion::class,
                    AccountChange_EmailHandler_PhoneOnlyChange::class]
                ,
                AccountChange_AccountChangeOrder::TYPE_WORKPLACE_ACCOUNT_CHANGE,
                true,
                true,
                0,
                1
            ]
        ];
    }

    /**
     * @test
     */
    public function shouldSetUpConfirmationEmailCorrectlyIfTechnologyTypeIsSOGEA()
    {
        $performChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            [
                'getScheduledChangeDate',
                'getTechnologyType',
                'getCurrentAccessTechnology',
                'getNewAccessTechnology'
            ],
            []
        );

        $performChangeApi
            ->expects($this->once())
            ->method('getScheduledChangeDate')
            ->willReturn('2022-07-05');

        $performChangeApi
            ->expects($this->once())
            ->method('getTechnologyType')
            ->willReturn('SOGEA');

        $performChangeApi
            ->expects($this->once())
            ->method('getCurrentAccessTechnology')
            ->willReturn('ADSL');

        $performChangeApi
            ->expects($this->once())
            ->method('getNewAccessTechnology')
            ->willReturn('SOGEA');

        $performChangeApi->setOldSdi(1);
        $performChangeApi->setToSdi(2);
        $performChangeApi->setOldProductName('OLD PRODUCT NAME');
        $performChangeApi->setNewProductName('NEW PRODUCT NAME');

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrder = new AccountChange_AccountChangeOrder();
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setContract(new AccountChange_AccountChangeOrderContract());

        $emailHandlerFactory = $this->getMock(
            'AccountChange_EmailHandler_Factory',
            array(
                'getAccountInstance',
                'doesNotHaveActiveDirectDebitInstruction',
                'isNewDDReady',
                'getPriceHelper',
                'hasActiveLineRentalSaver',
                'getCallPlanDetailsByWlrServiceComponentId',
                'getSpeedHelper',
                'getContractMessageHelper',
                'getCoreServiceDefinition'
            ),
            array($performChangeApi, $accountChangeOrder)
        );
        $account = $this->getMock(
            'AccountChange_Account',
            array(
                'getWlrInformation',
                'getNextInvoiceDate'),
            array(new Int(1))
        );

        $coreServiceDef = $this->getMock(
            'Core_ServiceDefinition',
            array('isBusiness'),
            [],
            '',
            false
        );

        $account
            ->expects($this->never())
            ->method('getWlrInformation');

        $coreServiceDef
            ->expects($this->any())
            ->method('isBusiness')
            ->willReturn(false);

        $account
            ->expects($this->once())
            ->method('getNextInvoiceDate')
            ->willReturn('2022-08-05');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getAccountInstance')
            ->willReturn($account);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getCoreServiceDefinition')
            ->willReturn($coreServiceDef);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('doesNotHaveActiveDirectDebitInstruction')
            ->willReturn(true);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('isNewDDReady')
            ->willReturn(true);

        $priceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(1, 2, 3, 4, 5, null)
        );

        $priceHelper
            ->expects($this->once())
            ->method('getNewPackagePrices')
            ->willReturn(array(
                'broadband'        => 11.11,
                'lineRental'       => 0.00,
                'callPlan'         => 0.00,
                'callFeatures'     => 0.00,
                'total'            => 11.11,
                'discountDuration' => 18,
                'discountAmount'   => 4
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getPriceHelper')
            ->willReturn($priceHelper);

        $emailHandlerFactory
            ->expects($this->never())
            ->method('hasActiveLineRentalSaver');

        $emailHandlerFactory
            ->expects($this->never())
            ->method('getCallPlanDetailsByWlrServiceComponentId');

        $speedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array('getSpeedData'),
            array(1, 2)
        );

        $speedHelper
            ->expects($this->once())
            ->method('getSpeedData')
            ->willReturn(array(
                'minimumEstimatedDownloadSpeedMbs' => 1,
                'maximumEstimatedDownloadSpeedMbs' => 2,
                'minimumEstimatedUploadSpeedMbs'   => 3,
                'maximumEstimatedUploadSpeedMbs'   => 4,
                'broadbandSpeedRange'              => 5,
                'guaranteedSpeedValue'             => 6,
                'maximumDownloadSpeedMbs'          => 7,
                'advertisedDownloadSpeedMbs'       => 8,
                'advertisedUploadSpeedMbs'         => 9
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getSpeedHelper')
            ->willReturn($speedHelper);

        $contractMessageHelper = $this->getMock(
            'AccountChange_EmailHandler_ContractMessageHelper',
            array('generateContractMessage'),
            array(null, null)
        );

        $contractMessageHelper
            ->expects($this->once())
            ->method('generateContractMessage')
            ->willReturn('CONTRACT MESSAGE');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getContractMessageHelper')
            ->willReturn($contractMessageHelper);

        $expected = new AccountChange_ConfirmationEmailHandler();
        $expected->setEmailName(AccountChange_EmailHandler_Factory::UPGRADE_EMAIL_HANDLE);
        $expected->setNewDirectDebit(true);
        $expected->setNewDirectDebitReady(true);
        $expected->setChangeDate('2022-07-05');
        $expected->setCurrentDualPlay(false);
        $expected->setBroadbandSubscriptionCost('11.11');
        $expected->setLineRentalSubscriptionCost('0.00');
        $expected->setCallPlanSubscriptionCost('0.00');
        $expected->setCallFeatureSubscriptionCost('0.00');
        $expected->setNewPrice('11.11');
        $expected->setNewLineRental('0.00');
        $expected->setSpecialOffer(true);
        $expected->setOfferDuration(18);
        $expected->setDiscountAmount('4.00');
        $expected->setOfferPrice('7.11');
        $expected->setCurrentBroadbandProduct('OLD PRODUCT NAME');
        $expected->setNewBroadbandProduct('NEW PRODUCT NAME');
        $expected->setLineRentalFrequency(null);
        $expected->setNewCallPlan(null);
        $expected->setCallFeatures(null);
        $expected
            ->setMinimumEstimatedDownloadSpeedMbs(1)
            ->setMaximumEstimatedDownloadSpeedMbs(2)
            ->setMinimumEstimatedUploadSpeedMbs(3)
            ->setMaximumEstimatedUploadSpeedMbs(4)
            ->setBroadbandSpeedRange(5)
            ->setGuaranteedSpeedValue(6)
            ->setMaximumDownloadSpeedMbs(7)
            ->setAdvertisedDownloadSpeedMbs(8)
            ->setAdvertisedUploadSpeedMbs(9);
        $expected->setRecontracted(true);
        $expected->setContractDuration('CONTRACT MESSAGE');
        $expected->setCallerClassName('AccountChangeApi');
        $expected->setSogeaProduct(true);
        $expected->setAccessTechnologyChanging(true);

        $actual = $emailHandlerFactory->buildConfirmationEmailForPerformChangeApi();

        $this->assertEquals([$expected], $actual);
    }

    /**
     * @test
     */
    public function shouldSetUpConfirmationEmailCorrectlyIfTechnologyTypeIsFTTC()
    {
        $performChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            [
                'getScheduledChangeDate',
                'getTechnologyType',
                'getCurrentAccessTechnology',
                'getNewAccessTechnology'
            ],
            []
        );

        $performChangeApi
            ->expects($this->once())
            ->method('getScheduledChangeDate')
            ->willReturn('2022-07-05');

        $performChangeApi
            ->expects($this->once())
            ->method('getTechnologyType')
            ->willReturn('FTTC');

        $performChangeApi->setOldSdi(1);
        $performChangeApi->setToSdi(2);
        $performChangeApi->setOldProductName('OLD PRODUCT NAME');
        $performChangeApi->setNewProductName('NEW PRODUCT NAME');

        $accountChangeOrderProducts = new AccountChange_AccountChangeOrderProducts();
        $accountChangeOrder = new AccountChange_AccountChangeOrder();
        $accountChangeOrder->setProducts($accountChangeOrderProducts);
        $accountChangeOrder->setContract(new AccountChange_AccountChangeOrderContract());

        $emailHandlerFactory = $this->getMock(
            'AccountChange_EmailHandler_Factory',
            array(
                'getAccountInstance',
                'doesNotHaveActiveDirectDebitInstruction',
                'isNewDDReady',
                'getPriceHelper',
                'hasActiveLineRentalSaver',
                'getCallPlanDetailsByWlrServiceComponentId',
                'getSpeedHelper',
                'getContractMessageHelper',
                'getCoreServiceDefinition'
            ),
            array($performChangeApi, $accountChangeOrder)
        );
        $account = $this->getMock(
            'AccountChange_Account',
            array(
                'getWlrInformation',
                'getNextInvoiceDate'),
            array(new Int(1))
        );

        $coreServiceDef = $this->getMock(
            'Core_ServiceDefinition',
            array('isBusiness'),
            [],
            '',
            false
        );

        $account
            ->expects($this->once())
            ->method('getWlrInformation');

        $coreServiceDef
            ->expects($this->any())
            ->method('isBusiness')
            ->willReturn(false);

        $account
            ->expects($this->once())
            ->method('getNextInvoiceDate')
            ->willReturn('2022-08-05');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getAccountInstance')
            ->willReturn($account);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getCoreServiceDefinition')
            ->willReturn($coreServiceDef);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('doesNotHaveActiveDirectDebitInstruction')
            ->willReturn(true);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('isNewDDReady')
            ->willReturn(true);

        $priceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(1, 2, 3, 4, 5, null)
        );

        $priceHelper
            ->expects($this->once())
            ->method('getNewPackagePrices')
            ->willReturn(array(
                'broadband'        => 11.11,
                'lineRental'       => 0.00,
                'callPlan'         => 0.00,
                'callFeatures'     => 0.00,
                'total'            => 11.11,
                'discountDuration' => 18,
                'discountAmount'   => 4
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getPriceHelper')
            ->willReturn($priceHelper);

        $emailHandlerFactory
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver');

        $emailHandlerFactory
            ->expects($this->never())
            ->method('getCallPlanDetailsByWlrServiceComponentId');

        $speedHelper = $this->getMock(
            'AccountChange_EmailHandler_SpeedHelper',
            array('getSpeedData'),
            array(1, 2)
        );

        $speedHelper
            ->expects($this->once())
            ->method('getSpeedData')
            ->willReturn(array(
                'minimumEstimatedDownloadSpeedMbs' => 1,
                'maximumEstimatedDownloadSpeedMbs' => 2,
                'minimumEstimatedUploadSpeedMbs'   => 3,
                'maximumEstimatedUploadSpeedMbs'   => 4,
                'broadbandSpeedRange'              => 5,
                'guaranteedSpeedValue'             => 6,
                'maximumDownloadSpeedMbs'          => 7,
                'advertisedDownloadSpeedMbs'       => 8,
                'advertisedUploadSpeedMbs'         => 9
            ));

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getSpeedHelper')
            ->willReturn($speedHelper);

        $contractMessageHelper = $this->getMock(
            'AccountChange_EmailHandler_ContractMessageHelper',
            array('generateContractMessage'),
            array(null, null)
        );

        $contractMessageHelper
            ->expects($this->once())
            ->method('generateContractMessage')
            ->willReturn('CONTRACT MESSAGE');

        $emailHandlerFactory
            ->expects($this->once())
            ->method('getContractMessageHelper')
            ->willReturn($contractMessageHelper);

        $expected = new AccountChange_ConfirmationEmailHandler();
        $expected->setEmailName(AccountChange_EmailHandler_Factory::UPGRADE_EMAIL_HANDLE);
        $expected->setNewDirectDebit(true);
        $expected->setNewDirectDebitReady(true);
        $expected->setChangeDate('2022-07-05');
        $expected->setCurrentDualPlay(false);
        $expected->setBroadbandSubscriptionCost('11.11');
        $expected->setLineRentalSubscriptionCost('0.00');
        $expected->setCallPlanSubscriptionCost('0.00');
        $expected->setCallFeatureSubscriptionCost('0.00');
        $expected->setNewPrice('11.11');
        $expected->setNewLineRental('0.00');
        $expected->setSpecialOffer(true);
        $expected->setOfferDuration(18);
        $expected->setDiscountAmount('4.00');
        $expected->setOfferPrice('7.11');
        $expected->setCurrentBroadbandProduct('OLD PRODUCT NAME');
        $expected->setNewBroadbandProduct('NEW PRODUCT NAME');
        $expected->setLineRentalFrequency('Monthly');
        $expected->setNewCallPlan(null);
        $expected->setCallFeatures(null);
        $expected
            ->setMinimumEstimatedDownloadSpeedMbs(1)
            ->setMaximumEstimatedDownloadSpeedMbs(2)
            ->setMinimumEstimatedUploadSpeedMbs(3)
            ->setMaximumEstimatedUploadSpeedMbs(4)
            ->setBroadbandSpeedRange(5)
            ->setGuaranteedSpeedValue(6)
            ->setMaximumDownloadSpeedMbs(7)
            ->setAdvertisedDownloadSpeedMbs(8)
            ->setAdvertisedUploadSpeedMbs(9);
        $expected->setRecontracted(true);
        $expected->setContractDuration('CONTRACT MESSAGE');
        $expected->setCallerClassName('AccountChangeApi');
        $expected->setFttcProduct(true);
        $expected->setAccessTechnologyChanging(null);

        $actual = $emailHandlerFactory->buildConfirmationEmailForPerformChangeApi();

        $this->assertEquals([$expected], $actual);
    }

}
