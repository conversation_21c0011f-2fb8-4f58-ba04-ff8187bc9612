<?php

/**
 * <AUTHOR>
 */

class AccountChange_AuthSessionPolicyTest extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function testValidationFailureWhenTheSessionIsNotAValidObject()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getUserType')->andReturn(AccountChange_AuthSessionPolicy::STAFF_USER_TYPE);

        $authSession = Mockery::mock();
        $busTierClient = Mockery::mock();
        $busTierClient->shouldReceive('checkSessionValid')->andReturnTrue();

        $validator = Mockery::mock('AccountChange_AuthSessionPolicy', [$actor]);
        $validator->shouldAllowMockingProtectedMethods();
        $validator->makePartial();

        $validator->shouldR<PERSON>ei<PERSON>('getBusinessTierSession')->once()->andR<PERSON>urn($authSession);
        $validator->shouldR<PERSON>eive('getBusinessTierClient')->once()->andR<PERSON>urn($busTierClient);

        $this->assertFalse($validator->validate());
        $this->assertNull($validator->getFailure());
    }

    /**
     * @return void
     */
    public function testValidationFailureWhenTheSessionIsNotValid()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getUserType')->andReturn("STAFF");

        $authSession = Mockery::mock('Auth_Session');
        $busTierClient = Mockery::mock();
        $busTierClient->shouldReceive('checkSessionValid')->andReturnFalse();

        $validator = Mockery::mock('AccountChange_AuthSessionPolicy', [$actor]);
        $validator->shouldAllowMockingProtectedMethods();
        $validator->makePartial();

        $validator->shouldReceive('getBusinessTierSession')->once()->andReturn($authSession);
        $validator->shouldReceive('getBusinessTierClient')->once()->andReturn($busTierClient);

        $this->assertFalse($validator->validate());
        $this->assertEquals(AccountChange_AuthSessionPolicy::ERROR_MESSAGE, $validator->getFailure());
    }

    /**
     * @return void
     */
    public function testValidationSuccessWithValidSession()
    {
        $authSession = Mockery::mock('Auth_Session');
        $busTierClient = Mockery::mock();
        $busTierClient->shouldReceive('checkSessionValid')->andReturnTrue();

        $validator = Mockery::mock('AccountChange_AuthSessionPolicy');
        $validator->shouldAllowMockingProtectedMethods();
        $validator->makePartial();

        $validator->shouldReceive('getBusinessTierSession')->once()->andReturn($authSession);
        $validator->shouldReceive('getBusinessTierClient')->once()->andReturn($busTierClient);

        $this->assertTrue($validator->validate());
    }
}
