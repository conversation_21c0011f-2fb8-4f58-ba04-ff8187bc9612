<?php

use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\ContractsClient\Client;
use Plusnet\ContractsClient\Entity\DurationUnit;

/**
 * AccountChange Account
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-13
 */

/**
 * AccountChange Account
 *
 * This class represents an overall customer account
 * It can provide overview information on products
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Account
{
    const LINE_RENTAL_CURRENT_BASE_PRICE_KEY = 'LineRentalCurrentBasePrice';
    const LINE_RENTAL_CURRENT_SUBSCRIPTION_PRICE_KEY = 'LineRentalCurrentSubscriptionPrice';
    const CALL_PLAN_CURRENT_SUBSCRIPTION_PRICE_KEY = 'CallPlanCurrentSubscriptionPrice';
    const PHONE_PACKAGE_CURRENT_BASE_PRICE_KEY = 'PhonePackageCurrentBasePrice';
    const PHONE_PACKAGE_CURRENT_SUBSCRIPTION_PRICE_KEY = 'PhonePackageCurrentSubscriptionPrice';

    /**
     * Factory Instance, represents overview of account
     *
     * @var AccountChange_Account
     */
    private static $instance;

    /**
     * send
     * @var Int
     */
    private $serviceId;

    /**
     * Constructor
     *
     * Should only be used by the internal factory methods, left public for unit tests
     *
     * @param Int $serviceId The service Id we want to work with
     *
     * @return AccountChange_Account
     */
    public function __construct(Int $serviceId)
    {
        $this->serviceId = $serviceId;
    }

    /**
     * Factory instance getter
     *
     * Gets an account object that provides overview information
     *
     * @param Int $serviceId Service Id of the account
     *
     * @return AccountChange_Account
     */
    public static function instance(Int $serviceId)
    {
        if (!isset(self::$instance)) {
            self::$instance = new AccountChange_Account($serviceId);
        }

        return self::$instance;
    }

    /**
     * Setter for the factory
     *
     * Set a bespoke account instance
     *
     * @param AccountChange_Account $instance The account instance
     *
     * @return void
     */
    public static function setInstance(AccountChange_Account $instance = null)
    {
        self::$instance = $instance;
    }

    /**
     * Get wlr information about the account
     *
     * @return array
     */
    public function getWlrInformation()
    {
        $this->includeLegacyFiles();

        $arrReturn = array();
        $arrReturn['strProductName'] = '';
        $arrReturn['strCallPlanName'] = '';
        $arrReturn['intProductCost'] = '';
        $arrReturn['intLineRentCost'] = '';
        $arrReturn['intCallPlanCost'] = '';
        $arrReturn['intOldWlrId'] = '';
        $arrReturn['strContractLength'] = '';
        $arrReturn['strContractHandle'] = '';
        $arrReturn['uxtContractEnd'] = '';
        $arrReturn['intInstanceId'] = '';
        $arrReturn['strComponentStatus'] = '';
        $arrReturn['arrExistingChange'] = array();

        try {
            $objHomePhone = $this->getHomePhone();

            if ($objHomePhone->getServiceComponentId() != null) {

                $uxtContractEnd = I18n_Date::fromTimestamp($objHomePhone->getHomePhoneSubscriptionContractEnd());

                $arrScheduledChange = $this->getExistingWlrChangeScheduledData($this->serviceId->getValue());

                $isSplitCharge = $objHomePhone->isSplitCharge();
                $phoneSubscriptionData = $this->populateFromRBMPhoneSubscriptions($isSplitCharge);
                $arrReturn['intLineRentCost'] = $phoneSubscriptionData[self::LINE_RENTAL_CURRENT_BASE_PRICE_KEY];
                $arrReturn['intLineRentCostInContract'] =
                    $phoneSubscriptionData[self::LINE_RENTAL_CURRENT_SUBSCRIPTION_PRICE_KEY];
                $arrReturn['intCallPlanCost'] = $phoneSubscriptionData[self::CALL_PLAN_CURRENT_SUBSCRIPTION_PRICE_KEY];
                $arrReturn['intProductCost'] =
                    $phoneSubscriptionData[self::PHONE_PACKAGE_CURRENT_BASE_PRICE_KEY];
                $arrReturn['intProductCostInContract'] =
                    $phoneSubscriptionData[self::PHONE_PACKAGE_CURRENT_SUBSCRIPTION_PRICE_KEY];
                $arrReturn['strProductName'] = $objHomePhone->getHomePhoneProduct();
                $arrReturn['bolSplitPrice'] = $isSplitCharge;
                $arrReturn['intOldWlrId'] = $objHomePhone->getServiceComponentId();
                $arrReturn['strContractLength'] = $objHomePhone->getHomePhoneSubscriptionContractLength();
                $arrReturn['strContractHandle'] = $objHomePhone->getHomePhoneSubscriptionContractHandle();
                $arrReturn['intInstanceId'] = $objHomePhone->getWlrComponentId();
                $arrReturn['strComponentStatus'] = $objHomePhone->getHomePhoneStatus();
                $arrReturn['uxtContractEnd'] = $uxtContractEnd;
                $arrReturn['arrExistingChange'] = $arrScheduledChange;
                $arrReturn['activeCallFeatures'] = $this->getActiveCallFeatures();
            }
        } catch (Exception $objEx) {
            error_log($objEx->getmessage());
            // Let it fall through as we have defined the return array at this point
        }

        $arrReturn['bolWlrChangeAllowed'] = $this->canChangeWlr();
        $arrReturn['bolWlrAddAllowed'] = $this->canAddWlr();

        return $arrReturn;
    }

    /**
     * Returns all the active call features on the account
     *
     * @return array
     */
    public function getActiveCallFeatures()
    {
        $callFeatures = array();

        $db = Db_Manager::getAdaptor('AccountChange');
        $activeCallFeatures = $db->getActiveCallFeatures($this->serviceId->getValue());

        foreach ($activeCallFeatures as $callFeature) {
            $callFeatures[$callFeature['productComponentInstanceId']] = $callFeature['name'];
        }

        return $callFeatures;
    }

    /**
     * Get voip information about the account
     *
     * @return array
     */
    public function getVoipInformation()
    {
        $arrReturn = array();

        $this->includeLegacyFiles();

        $intVoipComponentId = CProduct::getComponentIDByServiceID($this->serviceId, 'PLUSTALK');

        if ($intVoipComponentId > 0) {
            $objVoipProduct = CProduct::createInstance($intVoipComponentId);
            $intVoipServiceComponentId = CProduct::getServiceComponentIDByComponentID(
                $intVoipComponentId
            );

            $objDatabase = Db_Manager::getAdaptor('AccountChange');

            $intCost = $objDatabase->getProductComponentPrice(
                'MONTHLY',
                'MONTHLY',
                $intVoipServiceComponentId,
                'SUBSCRIPTION'
            );

            // In order to deal with the legacy and framework code locking each other
            // if was decided to commit the default transaction everytime we called the database
            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

            $arrReturn['bolHasVoip'] = true;
            $arrReturn['strVoipProduct'] = $objVoipProduct->getComponentName();
            $arrReturn['intVoipProductCost'] = new I18n_Currency(
                AccountChange_Manager::CURRENCY_UNIT,
                (is_numeric($intCost) ? ($intCost / 100) : 0)
            );
        } else {
            $arrReturn['bolHasVoip'] = false;
            $arrReturn['strVoipProduct'] = '';
            $arrReturn['intVoipProductCost'] = '';
        }

        return $arrReturn;
    }

    /**
     * Has the account passed the grace period for adsl change
     *
     * @return boolean
     */
    public function hasPassedAdslChangeGracePeriod()
    {
        $this->includeLegacyFiles();

        // make sure user has been on account for 30 days before allowing them to change
        $arrEventCriteria = array(
            'service_id' => $this->serviceId->getValue(),
            'event_type_id' => array(
                SERVICE_EVENT_SIGNUP, SERVICE_EVENT_TYPE_CHANGE
            )
        );

        $arrEvents = userdata_service_events_find(
            $arrEventCriteria,
            'event_date desc',
            0,
            1
        );

        if (!empty($arrEvents) && is_array($arrEvents[0]) && isset($arrEvents[0]['event_date'])) {
            $uxtEventDate = strtotime($arrEvents[0]['event_date']);

            if (time() < ($uxtEventDate + (60 * 60 * 24 * 30))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the date for the adsl grace period
     *
     * @return I18n_Date
     */
    public function getAdslGracePeriod()
    {
        $this->includeLegacyFiles();

        // make sure user has been on account for 30 days before allowing them to change
        $arrEventCriteria = array(
            'service_id' => $this->serviceId->getValue(),
            'event_type_id' => array(
                SERVICE_EVENT_SIGNUP, SERVICE_EVENT_TYPE_CHANGE
            )
        );

        $arrEvents = userdata_service_events_find(
            $arrEventCriteria,
            'event_date desc',
            0,
            1
        );

        if (!empty($arrEvents) && is_array($arrEvents[0]) && isset($arrEvents[0]['event_date'])) {
            $uxtEventDate = strtotime("+30 days", strtotime($arrEvents[0]['event_date']));

            if (time() < $uxtEventDate) {
                return I18n_Date::fromTimestamp($uxtEventDate);
            }
        }

        return null;
    }

    /**
     * Get the existing wlr change data
     *
     * @return array
     */
    public function getExistingWlrChangeScheduledData()
    {
        $arrReturn = array();

        $this->includeLegacyFiles();

        $intExistingComponentId = CProduct::getComponentIDByServiceID(
            $this->serviceId->getValue(),
            'WLR'
        );

        $arrExistingEvents = CProductChange::getExistingEvents(
            $intExistingComponentId,
            true
        );

        $uxtExistingEventDate = null;

        if (sizeof($arrExistingEvents) > 0) {
            foreach ($arrExistingEvents as $arrExistingEventDetails) {
                $intExistingEventComponentId = $arrExistingEventDetails['intNewComponentID'];
                $uxtExistingEventDate = I18n_Date::fromTimestamp($arrExistingEventDetails['uxtDue']);
                break;
            }

            $objProduct = new CProduct($intExistingEventComponentId);

            $arrReturn['uxtScheduledDate'] = $uxtExistingEventDate;
            $arrReturn['intWlrId'] = $objProduct->getServiceComponentId();
            $arrReturn['strProductName'] = $objProduct->getComponentName();
        }

        return $arrReturn;
    }

    /**
     * Get the existing contract start date
     *
     * @param string $strServiceCompProdTypeHandle Handle for the service component
     *
     * @return unk
     */
    public function getExistingContractStartDate($strServiceCompProdTypeHandle)
    {
        $this->includeLegacyFiles();

        return CProductComponent::getCustomerExistingContractStartDate(
            $this->serviceId->getValue(),
            $strServiceCompProdTypeHandle
        );
    }

    /**
     * Get the broadband tarrif for a service id
     *
     * @return array
     */
    public function getCurrentBroadbandTariff()
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $arrData = $db->getServiceComponentDetailsForService($this->serviceId);

        if (isset($arrData['intTariffID'])) {
            return $arrData['intTariffID'];
        }

        return null;
    }

    /**
     * Returns monthly broadband price
     *
     * @return float
     */
    public function getMonthlyBroadbandPrice()
    {
        $monthlyPrice = 0;

        $broadbandDetails = $this->getBroadbandDetails();

        if (!empty($broadbandDetails['minimum_charge'])) {
            $monthlyPrice = $broadbandDetails['minimum_charge'];
        }

        return $monthlyPrice;
    }

    /**
     * Checks whether the customer has active LRS
     *
     * @return boolean
     */
    public function hasActiveLrs()
    {
        return AccountChange_Manager::hasActiveLineRentalSaver($this->serviceId->getValue());
    }

    /**
     * Returns the details of the requested promotion code for the current broadband product of the account
     *
     * @param string $promoCode Promotion code
     *
     * @return array
     */
    public function getPromoCodeDetails($promoCode)
    {
        $promoDetails = array();
        $broadbandDetails = $this->getBroadbandDetails();

        if (!empty($broadbandDetails['service_definition_id'])) {
            $db = Db_Manager::getAdaptor('AccountChange');

            $promoDetails = $db->getPromoCodeDetails($promoCode, $broadbandDetails['service_definition_id']);
        }

        return $promoDetails;
    }

    /**
     * Returns broadband details of the account
     *
     * @return array
     */
    public function getBroadbandDetails()
    {
        $db = Db_Manager::getAdaptor('Core');

        $broadbandDetails = $db->getServiceComponentDetailsForService($this->serviceId);

        if (empty($broadbandDetails)) {
            $broadbandDetails = $db->getServiceDefinitionDetailsForService($this->serviceId);
        }

        $broadbandDetails['isCbcProduct'] = false;
        if (!empty($broadbandDetails['service_definition_id'])) {
            $productFamily = $this->getProductFamily($broadbandDetails['service_definition_id']);

            $broadbandDetails['isCbcProduct'] = $productFamily->isCbcProduct();
        }

        return $broadbandDetails;
    }

    /**
     * Get contract details about an account.
     *
     * Implemented here under DLIFE-152. Taken from LegacyCodebase MC_ContractDetails
     *
     * @param int $serviceId The customer's serviceId
     * @return array
     * @throws Core_AccountException
     */
    public function getContractDetails($serviceId)
    {

        $service = userdata_service_get($serviceId);
        $accountID = Core_Account::getAccountIdByUserId($service["user_id"]);
        $activeBroadbandComponentID = $this->getActiveBroadbandComponentId($serviceId);

        $filterCriteria = [
            'status' => 'ACTIVE',  // or PENDING
            'componentId' => $activeBroadbandComponentID,
            'serviceID' => $serviceId,
        ];

        try {
            $contracts = (new Client())->setAccount($accountID)->getContracts(
                $filterCriteria
            );

            if (!isset($contracts[0])) {
                return [];
            }

            $this->contract_details['contractDuration'] = $contracts[0]->getDefinition()->getDuration();
            $this->contract_details['startDate'] = $contracts[0]->getStartDate();
            $this->contract_details['endDate'] = $contracts[0]->getEndDate();
            $this->contract_details['daysUntilExpiry'] = $contracts[0]->getRemainingTime(DurationUnit::DAY);
            $this->contract_details['daysIntoContract'] =
                $this->calculateDaysIntoContract($this->contract_details['startDate']);
        } catch (BillingApiClientBillingServiceException $e) {
            error_log($e->getmessage());
            $this->contract_details = [];
        }

        return $this->contract_details;
    }

    /**
     * Calculate dates into contract between today and start date.
     * Basic date calculation but useful for mocks if required.
     *
     * @param $startDate
     * @return null|string
     * @throws Exception
     */
    public function calculateDaysIntoContract($startDate)
    {
        $daysIntoContract = null;
        $today = new DateTime();
        $startDate = new DateTime($startDate);

        $daysIntoContract = $today->diff($startDate)->format('%a');

        return $daysIntoContract;
    }

    /**
     * Wrapper for retrieving product family details of the current product
     *
     * @param int $serviceDefinitionId Broadband service definition id
     *
     * @return ProductFamily_ProductFamily
     */
    protected function getProductFamily($serviceDefinitionId)
    {
        return ProductFamily_Factory::getFamily($serviceDefinitionId);
    }

    /**
     * Returns the cost level for a service id
     *
     * @return string
     */
    public function getCostLevel()
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $strCostLevel = $db->getCostLevelForServiceId($this->serviceId);

        if (!empty($strCostLevel)) {
            return $strCostLevel;
        }

        return null;
    }

    /**
     * Returns payment information
     *
     * @param string $promoCode Optional promo code. If supplied, offer prices are calculated on the fly.
     *
     * @return array
     */
    public function getPaymentDetails($promoCode = null)
    {
        $paymentDetails = array();
        $paymentDetails['broadbandPrice'] = $this->getMonthlyBroadbandPrice();
        $paymentDetails['phonePrice'] = $this->getMonthlyPhonePrice();
        $paymentDetails['totalPackagePrice'] =
            $paymentDetails['broadbandPrice']
            + $paymentDetails['phonePrice']['lineRental']
            + $paymentDetails['phonePrice']['subscription'];

        $paymentDetails['specialOffer'] = false;
        $paymentDetails['offerPrice'] = null;
        $paymentDetails['offerDuration'] = null;

        if (!empty($promoCode)) {
            $promoCodeDetails = $this->getPromoCodeDetails($promoCode);

            $discountValue = 0;
            if (!empty($promoCodeDetails)) {
                if ($promoCodeDetails['discountType'] == 'percentage') {
                    $discountValue +=
                        ($paymentDetails['broadbandPrice'] * ($promoCodeDetails['discountValue'] / 100)) + 0.001;
                } else {
                    $discountValue += $promoCodeDetails['discountValue'];
                }

                $paymentDetails['specialOffer'] = true;
                $paymentDetails['offerPrice'] =
                    number_format($paymentDetails['totalPackagePrice'] - $discountValue, 2);
                $paymentDetails['offerDuration'] = $promoCodeDetails['discountLength'];
            }
        }

        return $paymentDetails;
    }

    /**
     * Returns the next invoice date
     *
     * @param string $format Specifies the string to format the invoice date. Defaults to 'd/m/Y'
     *
     * @return string
     */
    public function getNextInvoiceDate($format = 'd/m/Y')
    {
        return $this->getService()->getNextInvoiceDate($format);
    }

    /**
     * Wrapper to create Val_Service object
     *
     * @return Val_Service
     */
    public function getService()
    {
        return new Val_Service($this->serviceId);
    }

    /**
     * Wrapper to build HomePhone object
     *
     * @return HomePhone
     * @throws Exception
     */
    protected function getHomePhone()
    {
        return new HomePhone($this->serviceId->getValue());
    }

    /**
     * Wrapper to build CurrentPhoneSubscriptionsHelper object
     *
     * @return AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper
     */
    protected function getCurrentPhoneSubscriptionsHelper()
    {
        return new AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper();
    }

    /**
     * Returns monthly phone price
     *
     * NOTE: This does not take into consideration any active LRS on the account.
     * The value 'lineRental' will still have the actual line rental price of the product
     * regardless of whether the customer has taken LRS or not.
     *
     * @return array
     */
    public function getMonthlyPhonePrice()
    {
        $phonePrice = array();
        $lineRental = null;
        $subscription = null;

        try {
            $homePhone = $this->getHomePhone();
            $serviceComponentId = $homePhone->getServiceComponentId();

            $db = \Db_Manager::getAdaptor('AccountChange');

            $lineRental = $db->getProductComponentPrice(
                'MONTHLY',
                'MONTHLY',
                $serviceComponentId,
                'SUBSCRIPTION'
            );

            $subscription = $db->getProductComponentPrice(
                'MONTHLY',
                'MONTHLY',
                $serviceComponentId,
                'WlrLineRent'
            );
        } catch (Exception $exception) {
            //Carry on, no phone on the account
        }

        $phonePrice['lineRental'] = !is_null($lineRental) ? number_format($lineRental / 100, 2) : 0;
        $phonePrice['subscription'] =
            !is_null($subscription) ? number_format($subscription / 100, 2) : 0;

        return $phonePrice;
    }

    /**
     * Returns the active broadband component Id on an account
     *
     * @param int $serviceId Service id of the account
     *
     * @return int
     */
    protected function getActiveBroadbandComponentId($serviceId)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $adslComponentId = $dbAdaptor->getComponentIdForTypeAndStatus('active', 'INTERNET_CONNECTION', $serviceId);

        if (is_array($adslComponentId) && count($adslComponentId) == 1) {
            $activeAdslComponentId = $adslComponentId[0];
        } else {
            $activeAdslComponentId = null;
        }

        return $activeAdslComponentId;
    }

    /**
     * Inclusion of legacy files so we can mock them
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
        require_once '/local/data/mis/database/database_libraries/secure-transaction-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/HomePhone.class.php';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
    }

    /**
     * @return bool
     */
    protected function canChangeWlr()
    {
        return CWlrProduct::canChangeWlr($this->serviceId->getValue());
    }

    /**
     * @return bool
     */
    protected function canAddWlr()
    {
        return CWlrProduct::canAddWlr($this->serviceId->getValue());
    }


    /**
     * @return bool
     */
    protected function hasActiveLineRentalSaver()
    {
        return AccountChange_Manager::hasActiveLineRentalSaver($this->serviceId->getValue());
    }

    /**
     * @param $isSplitCharge
     * @return array
     * @throws Exception
     */
    private function populateFromRBMPhoneSubscriptions($isSplitCharge)
    {
        $phoneSubscriptionData = array();
        $phoneSubscriptionData[self::LINE_RENTAL_CURRENT_BASE_PRICE_KEY] = '';
        $phoneSubscriptionData[self::LINE_RENTAL_CURRENT_SUBSCRIPTION_PRICE_KEY] = '';
        $phoneSubscriptionData[self::CALL_PLAN_CURRENT_SUBSCRIPTION_PRICE_KEY] = '';
        $phoneSubscriptionData[self::PHONE_PACKAGE_CURRENT_BASE_PRICE_KEY] = '';
        $phoneSubscriptionData[self::PHONE_PACKAGE_CURRENT_SUBSCRIPTION_PRICE_KEY] = '';

        $lineRentalBasePrice = '';
        $lineRentalSubscriptionPrice = '';
        $callPlanSubscriptionPrice = '';

        $currentPhoneSubscriptionData = $this->getRBMPhoneSubscriptionData();

        if (!empty($currentPhoneSubscriptionData)) {
            $lineRentalSubscription = $currentPhoneSubscriptionData->getCurrentLineRentalSubscription();
            if (!empty($lineRentalSubscription)) {
                if ($this->hasActiveLineRentalSaver()) {
                    $lineRentalBasePrice =
                        AccountChange_Controller::createCurrencyObject(
                            $lineRentalSubscription->getCurrentBasePriceInPounds());
                } else {
                    $lineRentalBasePrice =
                        AccountChange_Controller::createCurrencyObject(
                            $lineRentalSubscription->getNextSubscriptionPriceInPounds());
                }
                $lineRentalSubscriptionPrice =
                    AccountChange_Controller::createCurrencyObject(
                        $lineRentalSubscription->getCurrentSubscriptionPriceInPounds());
                $phoneSubscriptionData[self::LINE_RENTAL_CURRENT_BASE_PRICE_KEY] = $lineRentalBasePrice;
                $phoneSubscriptionData[self::LINE_RENTAL_CURRENT_SUBSCRIPTION_PRICE_KEY] = $lineRentalSubscriptionPrice;
            }

            $callPlanSubscription = $currentPhoneSubscriptionData->getCurrentCallPlanSubscription();
            if (!empty($callPlanSubscription)) {
                $callPlanSubscriptionPrice =
                    AccountChange_Controller::createCurrencyObject(
                        $callPlanSubscription->getCurrentSubscriptionPriceInPounds());
                $phoneSubscriptionData[self::CALL_PLAN_CURRENT_SUBSCRIPTION_PRICE_KEY] = $callPlanSubscriptionPrice;
            }

            $phoneSubscriptionData[self::PHONE_PACKAGE_CURRENT_BASE_PRICE_KEY] =
                $this->getCurrencyPrice($isSplitCharge, $lineRentalBasePrice, $callPlanSubscriptionPrice);

            $phoneSubscriptionData[self::PHONE_PACKAGE_CURRENT_SUBSCRIPTION_PRICE_KEY] =
                $this->getCurrencyPrice($isSplitCharge, $lineRentalSubscriptionPrice, $callPlanSubscriptionPrice);
        }

        return $phoneSubscriptionData;
    }

    /**
     * @return AccountChange_BillingApi_CurrentPhoneSubscriptions|null
     */
    private function getRBMPhoneSubscriptionData()
    {
        $currentPhoneSubscriptionData = null;

        try {
            $currentPhoneSubscriptionsHelper = $this->getCurrentPhoneSubscriptionsHelper();
            $currentPhoneSubscriptionData =
                $currentPhoneSubscriptionsHelper->getCurrentPhoneSubscriptionData($this->serviceId->getValue());
        } catch (Exception $objEx) {
            error_log($objEx->getmessage());
        }

        return $currentPhoneSubscriptionData;
    }

    /**
     * @param $isSplitCharge
     * @param I18n_Currency $lineRentalPrice
     * @param I18n_Currency $callPlanPrice
     * @return I18n_Currency|string
     * @throws Exception
     */
    private function getCurrencyPrice($isSplitCharge, $lineRentalPrice, $callPlanPrice)
    {
        $price = '';

        if ($isSplitCharge) {
            if ($lineRentalPrice !== '' || $callPlanPrice !== '') {
                $price1 = ($lineRentalPrice === '') ? 0 : $lineRentalPrice->toDecimal();
                $price2 = ($callPlanPrice === '') ? 0 : $callPlanPrice->toDecimal();
                $price = $price1 + $price2;
            }
        } else {
            $price = ($lineRentalPrice === '') ? '' : $lineRentalPrice->toDecimal();
        }

        return ($price === '') ? '' : new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, $price);
    }
}
