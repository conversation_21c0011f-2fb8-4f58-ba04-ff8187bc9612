CREATE TABLE `products`.`adsl_product` (
  `adsl_product_id` int(11) NOT NULL auto_increment,
  `service_definition_id` int(11) NOT NULL default '0',
  `intProductGroupID` int(10) unsigned NOT NULL default '1',
  `product_name` varchar(255) NOT NULL default 'unknown',
  `intProvisioningProfileID` int(10) unsigned default NULL,
  `intDefaultConnectionProfileGroupId` int(10) unsigned default '1',
  `isp` varchar(10) NOT NULL default 'unknown',
  `nat` enum('yes','no') NOT NULL default 'no',
  `deferred_install_fee` enum('yes','no') NOT NULL default 'no',
  `ip_stream` varchar(255) NOT NULL default 'unknown',
  `timestamp` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  `db_src` varchar(255) NOT NULL default 'unknown',
  `deferred_install_fee_monthly_addition` decimal(5,2) NOT NULL default '0.00',
  `deferred_install_fee_quarterly_addition` decimal(5,2) NOT NULL default '0.00',
  `speed` enum('250','500','1000','2000') default NULL,
  `install_type` enum('bt','si') default NULL,
  `bt_product_code` varchar(255) NOT NULL default '',
  `hardware_type` enum('pci','usb','router','none','router4','easystart','wifi','wifi_laptop','wifi_desktop') NOT NULL default 'none',
  `bolCapped` tinyint(1) NOT NULL default '0',
  `vchContract` enum('Annual','Monthly','EasyStart','18Months') NOT NULL default 'Monthly',
  `intADSLContentionRatioID` int(10) NOT NULL default '0',
  `intMaximumDailyBandwidth` bigint(20) default NULL,
  `bolSDSL` tinyint(1) NOT NULL default '0',
  `usiFairUsageBytes` bigint(20) NOT NULL default '0',
  `intMaximumSpeed` int(10) unsigned default NULL,
  `vchNetworkContentionRatio` varchar(255) default NULL,
  `intDefaultPlustalkComponentID` int(10) default NULL,
  `intCbcBillingPeriodId` tinyint(3) unsigned NOT NULL default '1',
  PRIMARY KEY  (`adsl_product_id`),
  UNIQUE KEY `u_service_definition_id` (`service_definition_id`),
  KEY `product_name` (`product_name`,`isp`),
  KEY `nat` (`nat`),
  KEY `deferred_install_fee` (`deferred_install_fee`),
  KEY `intProvisioningProfileID` (`intProvisioningProfileID`),
  KEY `idx_ProductGroup` (`intProductGroupID`),
  KEY `idxCbCBillingPeriodId` (`intCbcBillingPeriodId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1