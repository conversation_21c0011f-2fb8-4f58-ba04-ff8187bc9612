<?php
/**
 * Class AccountChange_AppointmentRebookHelper
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_AppointmentRebookerHelper
{
    private $address;

    private $appointments;

    private $serviceId;

    private $arrDetailsForEngineerData = array();

    /**
     * AccountChange_AppointmentRebookerHelper constructor.
     * @param AccountChange_Address                  $address         address
     * @param AccountChange_AccountChangeAppointment $arrAppointments appointment
     * @param string                                 $serviceId       service id
     */
    public function __construct($address, $arrAppointments, $serviceId)
    {
        $this->address = $address;
        $this->appointments = $arrAppointments;
        $this->serviceId = $serviceId;
    }

    /**
     * @return AccountChange_AccountChangeAppointment
     */
    public function attemptToRebookFromManualAppointment()
    {
        $this->updateAddressIfNotPresent();
        $this->setUpEngineerArray();
        $engineerAppointmentData = $this->getEngineerAppointmentData();
        $container = $this->getEngineerServiceContainer();
        $container->add(
            EngineerAppointmentClient_Service::FTTP,
            $engineerAppointmentData->toArray()
        );

        $client = $this->getEngineerAppointmentClient($container);
        $client->bookAppointmentFromArray(new Int($this->serviceId));
        $arrAppointment = $client->getServiceBookedAppointmentData(EngineerAppointmentClient_Service::FTTP_HANDLE);
        if (!empty($arrAppointment)) {
            $this->updateAppointment($arrAppointment);
        }
        return $this->appointments;
    }

    /**
     * @param array $arrAppointment apppointment array
     * @return void
     */
    private function updateAppointment($arrAppointment)
    {
        $newAppointment = array();
        $newAppointment['notes'] = $this->appointments->getNotes();
        $newAppointment['live'] = $arrAppointment;
        $newAppointment['live']['date'] =  str_replace('/', '-', $newAppointment['live']['date']);
        $this->appointments = new AccountChange_AccountChangeAppointment($newAppointment);
    }

    /**
     * @return void
     */
    private function setUpEngineerArray()
    {
        $this->arrDetailsForEngineerData['addressReference'] = $this->address->getAddressReference();
        $this->arrDetailsForEngineerData['cssDatabaseCode'] = $this->address->getCssDatabaseCode();
        $this->arrDetailsForEngineerData['strEngineeringNotes'] = $this->appointments->getNotes();
        $this->arrDetailsForEngineerData['appointmentList'] = $this->appointments->getManualAppointment();
    }

    /**
     * @param EngineerAppointmentClient_Service_Container $container service container
     * @return EngineerAppointmentClient_Client
     */
    protected function getEngineerAppointmentClient($container)
    {
        return new EngineerAppointmentClient_Client($container);
    }

    /**
     * @return EngineerAppointmentClient_Service_Container
     */
    protected function getEngineerServiceContainer()
    {
        return new EngineerAppointmentClient_Service_Container();
    }

    /**
     * @return void
     */
    private function updateAddressIfNotPresent()
    {
        if (empty($this->address)) {
            $arrAddress = $this->getAddressFromDb();
            $this->address = new AccountChange_AccountChangeAddress();
            $this->address->setAddressReference($arrAddress['addressReference']);
            $this->address->setCssDatabaseCode($arrAddress['cssDatabaseCode']);
        }
    }

    /**
     * @return array
     * @throws Db_TransactionException
     */
    private function getAddressFromDb()
    {
        $db = Db_Manager::getAdaptor("AccountChange");
        return $db->getAddressFromServiceId($this->serviceId);
    }

    /**
     * @return EngineerAppointmentClient_AppointmentData
     * @throws Exception
     */
    protected function getEngineerAppointmentData()
    {
        return EngineerAppointmentClient_AppointmentDataFactory::getAppointmentData(
            $this->arrDetailsForEngineerData,
            new DateTime(),
            EngineerAppointmentClient_AppointmentDataFactory::REQUEST_TYPE_ACCOUNT_CHANGE
        );
    }
}
