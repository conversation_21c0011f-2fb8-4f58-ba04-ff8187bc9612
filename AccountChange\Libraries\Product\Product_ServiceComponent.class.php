<?php
/**
 * Service Component
 *
 * Holds information about a service component product than an account may have
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Product_ServiceComponent.class.php,v 1.3 2009-05-11 04:19:01 fzaki Exp $
 * @since     File available since 2008-08-19
 */
/**
 * Service Component class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Product_ServiceComponent implements AccountChange_Product_Configuration
{
    /**
     * Component Id
     * Component Id from <i>userdata.components</i> table
     *
     * @var int
     */
    protected $intComponentId;

    /**
     * Service Component Id
     * This is the actual type id of the component we want to deal with
     *
     * @var int
     */
    protected $intServiceComponentId;

    /**
     * Action
     *
     * @var int
     */
    protected $intAction = null;

    /**
     * Service Id
     *
     * @var int
     */
    protected $intServiceId;

    /**
     * Is the product a key product
     *
     * @var boolean
     */
    protected $bolKeyProduct = true;

    /**
     * Product configuration moving too
     *
     * @var AccountChange_Product_Configuration
     */
    protected $objNewProductConfiguration = null;

    /**
     * Service component contract
     *
     * @var AccountChange_Product_ServiceComponentContract
     */
    protected $objServiceComponentContract = null;

    /**
     * Account Change Operation
     *
     * AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE
     * AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE
     * AccountChange_Product_Manager::ACCOUNT_CHANGE_SAME
     *
     * @var int
     */
    protected $intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_SAME;

    /**
     * Boolean as to whether or not we are scheduling the change
     *
     * @var boolean
     */
    protected $bolScheduleChange = false;

    /**
     * Boolean as to whether or not we are scheduling the downgrade
     *
     * @var boolean
     */
    protected $bolScheduleDowngrade = false;

    /**
     * Array of AccountChange_Ticket's
     *
     * @var array
     */
    protected $_tickets = array();

    /**
     * Array of AccountChange_ServiceNotice's
     *
     * @var array
     */
    protected $_serviceNotices = array();

    /**
     * Constructor to make the product configuration object
     *
     * @param int   $intServiceComponentId Service component id
     * @param int   $intAction             Action
     * @param array $arrOptions            Options
     *
     * @return void
     */
    public function __construct($intServiceComponentId, $intAction, array $arrOptions = array())
    {
        $this->initialise($intServiceComponentId, $intAction, $arrOptions);
    }

    /**
     * Initialise the Service Component Product Configuration object
     *
     * @param int   $intServiceComponentId Service component id
     * @param int   $intAction             Action
     * @param array $arrOptions            Options
     *
     * @return void
     */
    public function initialise($intServiceComponentId, $intAction, array $arrOptions)
    {
        if (!is_numeric($intServiceComponentId)) {

            throw new AccountChange_Product_ManagerException(
                'Non numeric service component id',
                AccountChange_Product_ManagerException::ERR_INVALID_COMPONENT_ID_TYPE
            );
        }

        $this->intServiceComponentId = $intServiceComponentId;
        $this->intAction = $intAction;

        if (isset($arrOptions['bolSchedule'])) {

            $this->bolScheduleChange = $arrOptions['bolSchedule'];
        }

        if (isset($arrOptions['bolScheduleDowngrade'])) {

            $this->bolScheduleDowngrade = $arrOptions['bolScheduleDowngrade'];
        }
    }

    /**
     * Getter for the new product configuration
     *
     * @return object
     */
    public function getNewProductConfiguration()
    {
        return $this->objNewProductConfiguration;
    }

    /**
     * Getter for the Service Component Id
     *
     * @return int
     */
    public function getProductId()
    {
        return $this->intServiceComponentId;
    }

    /**
     * Get the cost of the product
     *
     * @return int
     */
    public function getProductCost()
    {
        $intCost = 0;

        if (isset($this->objServiceComponentContract)) {

            $intCost = $this->objServiceComponentContract->getProductCost($this->intServiceComponentId);
        }

        return $intCost;
    }

    /**
     * Getter for the component instance Id
     *
     * @return int
     */
    public function getComponentId()
    {
        return $this->intComponentId;
    }

    /**
     * Getter for tickets
     *
     * @see Libraries/Product/AccountChange_Product_Configuration::getTickets() (non-PHPdoc)
     *
     * @return array
     */
    public function getTickets()
    {
        return $this->_tickets;
    }

    /**
     * Getter for service notices
     *
     * @see Libraries/Product/AccountChange_Product_Configuration::getServiceNotices() (non-PHPdoc)
     *
     * @return array
     */
    public function getServiceNotices()
    {
        return $this->_serviceNotices;
    }

    /**
     * Setter for the component instance Id
     *
     * @param int $intComponentId Component Id
     *
     * @return void
     */
    public function setComponentId($intComponentId)
    {
        $this->intComponentId = $intComponentId;
    }

    /**
     * Check to see if the product is a key product
     *
     * @return bool
     */
    public function isKeyProduct()
    {
        return $this->bolKeyProduct;
    }

    /**
     * Is the product going to be scheduled for the change
     *
     * @return boolean
     */
    public function isScheduled()
    {
        $registry = AccountChange_Registry::instance();
        $eventDate = $registry->getEntry('backDatedDate');
        // back dated cant be scheduled
        if (empty($eventDate) && (($this->bolScheduleChange)
            || ($this->bolScheduleDowngrade
            && $this->intAccountChangeOperation == AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE))
        ) {

            return true;
        }

        return false;
    }

    /**
     * Getter for the action of the account change
     *
     * @return int
     */
    public function getAction()
    {
        return $this->intAction;
    }

    /**
     * Getter for the account change operation (upgrade or downgrade or same)
     *
     * @return int
     */
    public function getAccountChangeOperation()
    {
        return $this->intAccountChangeOperation;
    }

    /**
     * Getter for the core service
     *
     * @return Core_Service
     */
    protected function getCoreService()
    {
        return new Core_Service($this->intServiceId);
    }

    /**
     * Match the product configuration for the product change
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration Account configuration
     *
     * @return void
     */
    public function setMatchingProductConfiguration(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {

            $this->objNewProductConfiguration
                = $objAccountConfiguration->getProductConfigurationByType(
                    AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_COMPONENT
                );
        }
    }

    /**
     * Match the product configuration with another product configuration
     *
     * This can be used if you have the matching object, and you need to perform some checks
     *
     * @param AccountChange_Product_Configuration $objConfiguration Configuration
     *
     * @return void
     */
    public function setMatchingProductConfigurationManually(AccountChange_Product_Configuration $objConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {

            if (!$objConfiguration instanceof AccountChange_Product_ServiceComponent) {

                throw new AccountChange_Product_Exception(
                    'Matching product is not the same product type',
                    AccountChange_Product_Exception::ERR_MATCHING_PRODUCT_NOT_VALID
                );
            }

            $this->objNewProductConfiguration = $objConfiguration;
            $this->setAccountChange();
        }
    }

    /**
     * Decide whether it will be an upgrade or downgrade for each product
     *
     * Decision made by the value of the products
     *
     * @param AccountChange_AccountConfiguration $objAccountConfiguration Account Configuration
     *
     * @return void
     */
    public function setAccountChangeOperation(AccountChange_AccountConfiguration $objAccountConfiguration)
    {
        if ($this->intAction === AccountChange_Product_Manager::ACTION_CHANGE) {

            // We need to have a new product configuration to do the checking we need
            if (!isset($this->objNewProductConfiguration)) {

                Dbg_Dbg::write(
                    get_class($this) . '::setAccountChangeOperation : ' .
                    'No new product configuration had been specified, so trying to match one', 'AccountChange'
                );

                $this->setMatchingProductConfiguration($objAccountConfiguration);
            }

            $this->setAccountChange();
        }
    }

    /**
     * Setter for the account change
     *
     * Refactored out of the setAccountChangeOperation so that the setMatchingProductConfigurationManually function
     * can also use it, which means we can perform checks before the account change process starts
     *
     * @return void
     */
    protected function setAccountChange()
    {
        if (!isset($this->objNewProductConfiguration)) {

            throw new AccountChange_Product_Exception(
                'There is no matching product set',
                AccountChange_Product_Exception::ERR_NEW_PRODUCT_NOT_SET
            );
        }

        if ($this->getProductCost() > $this->objNewProductConfiguration->getProductCost()) {

            Dbg_Dbg::write(get_class($this) . ' has decided to downgrade', 'AccountChange');

            $this->intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_DOWNGRADE;
        } else {

            Dbg_Dbg::write(get_class($this) . ' has decided to upgrade', 'AccountChange');

            $this->intAccountChangeOperation = AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE;
        }
    }

    /**
     * Getter for the service id
     *
     * @return int
     */
    public function getServiceId()
    {
        return $this->intServiceId;
    }

    /**
     * Setter for the service id
     *
     * @param int $intServiceId Service id
     *
     * @return void
     */
    public function setServiceId($intServiceId)
    {
        $this->intServiceId = $intServiceId;
    }

    /**
     * Enter description here...
     *
     * @return AccountChange_Product_ServiceComponentContract
     */
    public function getContract()
    {
        return $this->objServiceComponentContract;
    }

    /**
     * Enter description here...
     *
     * @param AccountChange_Product_ServiceComponentContract $objContract Contract
     *
     * @return void
     */
    public function setContract($objContract)
    {
        $this->objServiceComponentContract = $objContract;
    }

    /**
     * Getter for calculating the cancellation charges with a given product
     *
     * @return array
     */
    public function calculateCancellationCharges()
    {
        return array();
    }

    /**
     * Calculate the pro rata charge that needs to be taken if we are upgrading
     *
     * @return array
     */
    public function calculateProRataCharge()
    {
        return array();
    }

    /**
     * Getter for whether are taking payment or not
     *
     * @return boolean
     */
    public function isTakingPayment()
    {
        return false;
    }

    /**
     * Perform the product change
     *
     * @return void
     */
    public function execute()
    {
        switch ($this->getAction()) {
            case AccountChange_Product_Manager::ACTION_REMOVE:
                $this->remove();
                break;
            case AccountChange_Product_Manager::ACTION_ADD:
                $this->create();
                break;
            case AccountChange_Product_Manager::ACTION_REFRESH:
                $this->refresh();
                break;
            case AccountChange_Product_Manager::ACTION_CHANGE:
                $this->change();
                break;
            default:
                break;
        }
    }

    /**
     * Removal of the component
     *
     * @return void
     */
    protected function remove()
    {
        trigger_error('Implement for specific functionality for removing a product component');
    }

    /**
     * Creation of the component
     *
     * @return bool
     */
    protected function create()
    {
        trigger_error('Implement for specific functionality for creating a product component');
    }

    /**
     * Refreshing of the component
     *
     * @return void
     */
    protected function refresh()
    {
        trigger_error('Implement for specific functionality for refreshing a product component');
    }

    /**
     * Changing of the component
     *
     * @return void
     */
    protected function change()
    {
        trigger_error('Implement for specific functionality for changing a product component');
    }

    /**
     * Send the emails to confirm what just happened
     *
     * @param array $arrData Data used for confirmation email
     *
     * @return void
     */
    public function sendConfirmationEmail(array $arrData = array())
    {
    }

    /**
     * Get ticket information
     *
     * @return AccountChange_Ticket
     */
    public function getTicketInformation()
    {
        trigger_error('Implement for specific functionality for getting the ticket information');
    }

    /**
     * Send appointment related emails (this is optional)
     *
     * @param array $arrData Data used for appointment email
     *
     * @return void
     */
    public function sendAppointmentEmail(array $arrData = array())
    {
    }
}
