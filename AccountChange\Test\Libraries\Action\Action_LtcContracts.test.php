<?php
/**
 * Tests for the LtcContracts action
 *
 * PHP version 5
 *
 * @category   AccountChange
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2012 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */

use Plusnet\ContractsClient\Entity\Contract;

/**
 * Tests for the LtcContracts action
 *
 * @category   AccountChange
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2012 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Action_LtcContracts_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Make sure the registry is cleared between tests
     *
     * @return void
     */
    protected function setUp()
    {
        AccountChange_Registry::instance()->reset();

        // Make sure exceptions are re-thrown
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('disableContractsErrorHandle', true);
    }

    /**
     * Test state correct for WLR change
     *
     * @covers AccountChange_Action_LtcContracts::__construct
     * @covers AccountChange_Action_LtcContracts::getProductOfferingId
     * @covers AccountChange_Action_LtcContracts::getServicesToAdd
     * @covers AccountChange_Action_LtcContracts::getComponentIdsToRemove
     * @covers AccountChange_Action_LtcContracts::addService
     * @covers AccountChange_Action_LtcContracts::getNewContractDuration
     *
     * @return void
     */
    public function testAddRemovePropertiesSetForWlrChange()
    {
        $registry = AccountChange_Registry::instance();

        // not null, so phone service has changed
        $registry->setEntry('newWlrServiceComponentId', 123);
        $registry->setEntry('oldWlrComponentId', 456);
        $registry->setEntry('newWlrComponentId', 222);

        // these are the same, so no broadband change
        $registry->setEntry('intNewServiceDefinitionId', 789);
        $registry->setEntry('intOldServiceDefinitionId', 789);

        $registry->setEntry('selectedContractDuration', 24);

        $serviceId = 1;
        $options = array();

        $action = new AccountChange_Action_LtcContracts($serviceId, $options);

        $expectedServicesToAdd = array(
           'serviceTypeId' => 123,
           'serviceId'     => 222
        );

        $this->assertEquals(789, $action->getProductOfferingId());
        $this->assertEquals(array($expectedServicesToAdd), $action->getServicesToAdd());
        $this->assertEquals(array(456), $action->getComponentIdsToRemove());
        $this->assertEquals(24, $action->getNewContractDuration());
    }

    /**
     * Test state correct for broadband change
     *
     * @covers AccountChange_Action_LtcContracts::__construct
     * @covers AccountChange_Action_LtcContracts::getProductOfferingId
     * @covers AccountChange_Action_LtcContracts::getServicesToAdd
     * @covers AccountChange_Action_LtcContracts::getComponentIdsToRemove
     * @covers AccountChange_Action_LtcContracts::addService
     *
     * @return void
     */
    public function testAddRemovePropertiesSetForBroadbandChange()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intNewServiceDefinitionId', 123);
        $registry->setEntry('intOldServiceDefinitionId', 456);
        $registry->setEntry('oldAdslComponentId', 654);
        $registry->setEntry('defunctAdslComponentIds', array(111, 112));
        $registry->setEntry('newAdslServiceComponentId', 321);
        $registry->setEntry('newAdslComponentId', 333);

        $action = new AccountChange_Action_LtcContracts(1, array());

        $expectedServicesToAdd = array(
           'serviceTypeId' => 321,
           'serviceId'     => 333
        );

        $this->assertEquals(123, $action->getProductOfferingId());
        $this->assertEquals(array($expectedServicesToAdd), $action->getServicesToAdd());
        $this->assertEquals(array(654, 111, 112), $action->getComponentIdsToRemove());
    }

    /**
     * Test state correct for WLR *and* broadband change
     *
     * @covers AccountChange_Action_LtcContracts::__construct
     * @covers AccountChange_Action_LtcContracts::getProductOfferingId
     * @covers AccountChange_Action_LtcContracts::getServicesToAdd
     * @covers AccountChange_Action_LtcContracts::getComponentIdsToRemove
     * @covers AccountChange_Action_LtcContracts::addService
     *
     * @return void
     */
    public function testAddRemovePropertiesSetForWlrAndBroadbandChange()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('newWlrServiceComponentId', 111);
        $registry->setEntry('oldWlrComponentId', 222);
        $registry->setEntry('intNewServiceDefinitionId', 333);
        $registry->setEntry('intOldServiceDefinitionId', 444);
        $registry->setEntry('oldAdslComponentId', 555);
        $registry->setEntry('defunctAdslComponentIds', array(777, 778));
        $registry->setEntry('newAdslServiceComponentId', 666);
        $registry->setEntry('newWlrComponentId', 222);
        $registry->setEntry('newAdslComponentId', 333);

        $action = new AccountChange_Action_LtcContracts(1, array());

        $expectedServicesToAdd = array(
            array(
               'serviceTypeId' => 666,
               'serviceId'     => 333
            ),
            array(
               'serviceTypeId' => 111,
               'serviceId'     => 222
            ),
        );

        $this->assertEquals(333, $action->getProductOfferingId());
        $this->assertEquals(
            $expectedServicesToAdd,
            $action->getServicesToAdd()
        );
        $this->assertEquals(
            array(555, 777, 778, 222),
            $action->getComponentIdsToRemove()
        );
    }

    /**
     * Test state correct for no changes
     *
     * @covers AccountChange_Action_LtcContracts::__construct
     * @covers AccountChange_Action_LtcContracts::getProductOfferingId
     * @covers AccountChange_Action_LtcContracts::getServicesToAdd
     * @covers AccountChange_Action_LtcContracts::getComponentIdsToRemove
     *
     * @return void
     */
    public function testAddRemovePropertiesSetForNoChange()
    {
        $serviceId = 1;
        $options = array();
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intOldServiceDefinitionId', 444);

        $action = new AccountChange_Action_LtcContracts($serviceId, $options);

        // If there's no change to Broadband, the serivce expects the productOfferingId
        // to be the old service definition id
        $this->assertEquals(444, $action->getProductOfferingId());
        $this->assertEquals(array(), $action->getServicesToAdd());
        $this->assertEquals(array(), $action->getComponentIdsToRemove());
    }

    /**
     * Provide data for testClientMethodCalledWithExpectedParameters
     *
     * @return array
     **/
    public function provideDataForClientMethodCalledWithExpectedParameters()
    {
        return array(
            array(
                false,
                false,
                true,
                'END_USER',
                null,
                null,
                2,
                'productChange',
                \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_CUSTOMER_REQUEST
            ),
            array(
                false,
                false,
                true,
                'PLUSNET_STAFF',
                null,
                null,
                2,
                'productChange',
                \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST
            ),
            array(
                false,
                false,
                false,
                'PLUSNET_STAFF',
                null,
                null,
                2,
                'productChange',
                \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE
            ),

            array(
                false,
                false,
                false,
                'PLUSNET_STAFF',
                true,
                null,
                2,
                'productChange',
                \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE
            ),
            array(
                false,
                false,
                true,
                'END_USER',
                null,
                24,
                null,
                'productChange',
                \Plusnet\ProductChangePlanClient\ChangeChannels::SCHEDULE_ACCOUNT_CHANGE_RECONTRACT
            ),
            array(
                false,
                false,
                true,
                'PLUSNET_STAFF',
                null,
                24,
                null,
                'productChange',
                \Plusnet\ProductChangePlanClient\ChangeChannels::SCHEDULE_ACCOUNT_CHANGE_RECONTRACT
            ),
            array(
                false,
                false,
                false,
                'PLUSNET_STAFF',
                null,
                24,
                null,
                'productChange',
                \Plusnet\ProductChangePlanClient\ChangeChannels::IMMEDIATE_ACCOUNT_CHANGE_RECONTRACT
            ),
        );
    }

    /**
     * Test the parameters used to call $client->productChange
     *
     * @param bool   $runFromScript       runFromPerformScheduledAccountChange reg entry
     * @param bool   $scheduledReg        bolSchedule reg entry
     * @param string $userType            Type of the current logged in user
     * @param bool   $variantSwitchForWlr Whether registry bolVariantSwitchForWlrAddOrRemove is set
     * @param int    $recontractDuration  Duration of new contract, or null if none
     * @param int    $productOfferingId   New service definition id
     * @param string $expectedMethod      Action function we're expecting to call
     * @param string $expectedChannel     Expected change channel to use
     *
     * @dataProvider provideDataForClientMethodCalledWithExpectedParameters
     *
     * @covers AccountChange_Action_LtcContracts::getChangePlanClient
     * @covers AccountChange_Action_LtcContracts::getChangeChannel
     * @covers AccountChange_Action_LtcContracts::setChangePlanClient
     * @covers AccountChange_Action_LtcContracts::execute
     *
     * @return void
     */
    public function testClientMethodCalledWithExpectedParameters(
        $runFromScript,
        $runFromBbcrScript,
        $scheduledReg,
        $userType,
        $variantSwitchForWlr,
        $recontractDuration,
        $productOfferingId,
        $expectedMethod,
        $expectedChannel
    ) {
        $serviceId = 1;
        $oldProductOfferingId = 456;
        $oldAdslComponentId = 3;
        $newAdslServiceComponentId = 4;

        // set up the registry values
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intOldServiceDefinitionId', 456);
        $registry->setEntry('intNewServiceDefinitionId', $productOfferingId);
        $registry->setEntry('oldAdslComponentId', $oldAdslComponentId);
        $registry->setEntry('newAdslServiceComponentId', $newAdslServiceComponentId);
        $registry->setEntry('runFromPerformScheduledAccountChange', $runFromScript);
        $registry->setEntry('runFromBBCRScript', $runFromBbcrScript);
        $registry->setEntry('bolSchedule', $scheduledReg);
        $registry->setEntry('newWlrComponentId', 222);
        $registry->setEntry('newAdslComponentId', 333);
        $registry->setEntry('selectedContractDuration', $recontractDuration);
        $registry->setEntry('bolVariantSwitchForWlrAddOrRemove', $variantSwitchForWlr);

        // empty change plan the client will return
        $changePlan = new \Plusnet\ProductChangePlanClient\Entity\ChangePlan(array());

        // set up the client with expectations on the parameters to productChange()

        $client = $this->getMockBuilder('\Plusnet\ProductChangePlanClient\Client')
            ->disableOriginalConstructor()
            ->getMock();

        $expectedServicesToAdd = array(
            array(
               'serviceTypeId' => 4,
               'serviceId'     => 333
            ),
        );

        if ($recontractDuration == null) {
            $client
                ->expects($this->once())
                ->method($expectedMethod)
                ->with(
                    $expectedChannel,
                    $serviceId,
                    $oldProductOfferingId,
                    $productOfferingId,
                    $expectedServicesToAdd,
                    array($oldAdslComponentId)
                )
                ->will($this->returnValue($changePlan));
        } else {
            $client
                ->expects($this->once())
                ->method($expectedMethod)
                ->will($this->returnValue($changePlan));
        }

        $mockActor = $this->getMock(
            'Auth_BusinessActor',
            array('getUserType'),
            array()
        );

        $action = $this->getMock(
            'AccountChange_Action_LtcContracts',
            array('getCurrentBusinessActor'),
            array($serviceId, array())
        );

        if (!$runFromScript && !$runFromBbcrScript) {
            if (empty($variantSwitchForWlr)) {
                $mockActor
                    ->expects($this->once())
                    ->method('getUserType')
                    ->will($this->returnValue($userType));
            }
            $action
                ->expects($this->once())
                ->method('getCurrentBusinessActor')
                ->will($this->returnValue($mockActor));
        }

        // execute the action
        $action->setChangePlanClient($client);
        $action->execute();
    }

    /**
     * Test the parameters used to call $changePlan->execute
     *
     * @covers AccountChange_Action_LtcContracts::getChangePlanClient
     * @covers AccountChange_Action_LtcContracts::setChangePlanClient
     * @covers AccountChange_Action_LtcContracts::execute
     *
     * @return void
     */
    public function testChangePlanExecutedWithExpectedParameters()
    {
        $serviceId = 222334;

        $expectedExecuteOptions = array(
            'CreateContract' => array(
                'newAdslComponentId' => 111,
                'newWlrComponentId' => 222
            )
        );

        // set up the change plan with expected parameters to execute()
        $changePlan = $this->getMockBuilder('Plusnet\ProductChangePlanClient\Entity\ChangePlan')
            ->disableOriginalConstructor()
            ->getMock();

        // set up the client to return the change plan
        $client = $this->getMockBuilder('Plusnet\ProductChangePlanClient\Client')
            ->disableOriginalConstructor()
            ->getMock();
        $client
            ->expects($this->once())
            ->method('productChange')
            ->will($this->returnValue($changePlan));

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('newAdslComponentId', 111);
        $registry->setEntry('newWlrComponentId', 222);

        $mockActor = $this->getMock(
            'Auth_BusinessActor',
            array('getUserType'),
            array()
        );

        $mockActor
            ->expects($this->once())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_STAFF'));

        // execute the action
        $action = $this->getMock(
            'AccountChange_Action_LtcContracts',
            array('getCurrentBusinessActor'),
            array($serviceId, array())
        );

        $action
            ->expects($this->once())
            ->method('getCurrentBusinessActor')
            ->will($this->returnValue($mockActor));

        // execute the action
        $action->setChangePlanClient($client);
        $action->execute();
    }

    /**
     * Test to ensure we do not try to execute the change plan when
     * House move is in progress
     *
     * @covers AccountChange_Action_LtcContracts::execute
     * @covers AccountChange_Action_LtcContracts::isRetainContract
     *
     * @return void
     */
    public function testChangePlanNotExecutedWhenHouseMoveIsSet()
    {
        $serviceId = 222334;

        $mockSubject = $this->getMock(
            'AccountChange_Action_LtcContracts',
            array('getChangePlan'),
            array($serviceId, array(
                'bolHousemove' => true
            ))
        );

        $mockSubject
            ->expects($this->never())
            ->method('getChangePlan');

        $mockSubject->execute();
     }

    /**
     * Test to ensure we do not try to execute the change plan when
     * requested to retain the contract
     *
     * @covers AccountChange_Action_LtcContracts::execute
     * @covers AccountChange_Action_LtcContracts::isRetainContract
     *
     * @return void
     */
    public function testChangePlanNotExecutedWhenRetainingContract()
    {
        $serviceId = 222334;

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('retainContract', 1);

        $mockSubject = $this->getMock(
            'AccountChange_Action_LtcContracts',
            array('getChangePlan'),
            array($serviceId, array())
        );

        $mockSubject
            ->expects($this->never())
            ->method('getChangePlan');

        $mockSubject->execute();
    }

    /**
     * Test to ensure the newContractOrder flag drives traffic away from change plan
     */
    public function testExecuteSendsOrderToContractsClientForNewContractOrder()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('retainContract', false);
        $registry->setEntry('newContractOrder', true);
        $registry->setEntry('agreementDate', '01-01-2000');
        $registry->setEntry('contractType', 'F');
        $registry->setEntry('contractSubType', 'CPI');
        $registry->setEntry('duration', '18');
        $registry->setEntry('activateRecontract', false);

        $mockContractsClient = $this->getMock(
            'Plusnet\\ContractsClient\\Client',
            array(
                'createContractFromOrder',
                'executeChangeByChangePlan',
                'setServiceId',
                'updateStatus'
            )
        );

        BusTier_BusTier::setClient('contracts', $mockContractsClient);

        $mockContractsClient->expects($this->once())
            ->method('createContractFromOrder');

        $mockContractsClient->expects($this->once())
            ->method('setServiceId')
            ->willReturn($mockContractsClient);

        $mockContractsClient->expects($this->never())
            ->method('executeChangeByChangePlan');

        $mockContractsClient->expects($this->never())
            ->method('updateStatus');

        $action = new AccountChange_Action_LtcContracts(123456, array());
        $action->execute();

    }

    /**
     * Test to ensure an instant recontract is activated after the new contract is created
     */
    public function testExecuteActivatesAnInstantRecontractThroughContractsClient()
    {
        $registry = AccountChange_Registry::instance();
        $registry->setEntry('retainContract', false);
        $registry->setEntry('newContractOrder', true);
        $registry->setEntry('agreementDate', '01-01-2000');
        $registry->setEntry('contractType', 'F');
        $registry->setEntry('contractSubType', 'CPI');
        $registry->setEntry('duration', '18');
        $registry->setEntry('instantRecontract', true);

        $mockContractsClient = $this->getMock(
            'Plusnet\\ContractsClient\\Client',
            array(
                'createContractFromOrder',
                'executeChangeByChangePlan',
                'setServiceId',
                'updateStatus'
            )
        );

        BusTier_BusTier::setClient('contracts', $mockContractsClient);

        $newContract = new Contract();

        $mockContractsClient->expects($this->once())
            ->method('createContractFromOrder')
            ->willReturn($newContract);

        $mockContractsClient->expects($this->once())
            ->method('setServiceId')
            ->willReturn($mockContractsClient);

        $mockContractsClient->expects($this->never())
            ->method('executeChangeByChangePlan');

        $mockContractsClient->expects($this->once())
            ->method('updateStatus')
            ->with(
                $newContract,
                'Immediate re-contract',
                'ACTIVE',
                null,
                true
            );

        $action = new AccountChange_Action_LtcContracts(123456, array());
        $action->execute();

    }

    /**
     * Test getCurrentBusinessActor returns a valid business actor
     *
     * @covers AccountChange_Action_LtcContracts::getCurrentBusinessActor
     *
     * @return void
     */
    public function testGetCurrentBusinessActorReturnsAValidBusinessActor()
    {
        $businessActor = new Auth_BusinessActor(null);

        $loginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $loginMock
            ->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue($businessActor));

        Auth_Auth::setCurrentLogin($loginMock);

        $controller = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Action_LtcContracts', array(1, array()));

        $result = $controller->protected_getCurrentBusinessActor();

        $this->assertEquals($businessActor, $result);
    }

    /**
     * Provide data for testExecuteGetsCorrectChangePlan
     *
     * @return array
     **/
    public function provideDataForTestExecuteGetsCorrectChangePlan()
    {
        $changePlan = new Plusnet\ProductChangePlanClient\Entity\ChangePlan(array());
        $changePlan = $this->getMockBuilder('\Plusnet\ProductChangePlanClient\Entity\ChangePlan')
            ->disableOriginalConstructor()
            ->getMock();

        $changePlan
            ->expects($this->any())
            ->method('execute');

        $mockChangePlanExecute = $this->getMockBuilder('\Plusnet\ProductChangePlanClient\Client')
            ->disableOriginalConstructor()
            ->getMock();

        $mockChangePlanExecute
            ->expects($this->once())
            ->method('executeScheduledProductChange')
            ->will($this->returnValue($changePlan));

        $mockChangePlanChangeOne = $this->getMockBuilder('\Plusnet\ProductChangePlanClient\Client')
            ->disableOriginalConstructor()
            ->getMock();

        $mockChangePlanChangeOne
            ->expects($this->once())
            ->method('productChange')
            ->will($this->returnValue($changePlan));

        $mockChangePlanChangeTwo = $this->getMockBuilder('\Plusnet\ProductChangePlanClient\Client')
            ->disableOriginalConstructor()
            ->getMock();

        $mockChangePlanChangeTwo
            ->expects($this->once())
            ->method('productChange')
            ->will($this->returnValue($changePlan));

        return array(
            array($mockChangePlanExecute, \Plusnet\ProductChangePlanClient\ChangeChannels::EXECUTE_ACCOUNT_CHANGE),
            array($mockChangePlanChangeOne, \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST),
            array($mockChangePlanChangeTwo, \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_CUSTOMER_REQUEST)
        );
    }

    /**
     * Test that execute() gets an appropriate change plan and sets the optional parameters
     * correctly
     *
     * @param Mock_Client $mockChangePlanClient Mock change plan client
     * @param string      $changeChannel        Change channel
     *
     * @dataProvider provideDataForTestExecuteGetsCorrectChangePlan
     *
     * @covers AccountChange_Action_LtcContracts::execute
     * @covers AccountChange_Action_LtcContracts::getChangePlan
     *
     * @return void
     **/
    public function testExecuteGetsCorrectChangePlan($mockChangePlanClient, $changeChannel)
    {
        $serviceId = 123456;

        $mockPlan = $this->getMock(
            'AccountChange_Action_LtcContracts',
            array('getChangePlanClient', 'getChangeChannel'),
            array($serviceId, array())
        );

        $mockPlan
            ->expects($this->once())
            ->method('getChangePlanClient')
            ->will($this->returnValue($mockChangePlanClient));

        $mockPlan
            ->expects($this->once())
            ->method('getChangeChannel')
            ->will($this->returnValue($changeChannel));

        $mockPlan->execute();
    }

    /**
     * Test if getChangeChannel returns proper ChangeChannel
     *
     * @param bool   $runFromPerformScheduledAccountChange If run from script
     * @param bool   $scheduled                            If is scheduled
     * @param bool   $variantSwitchForWlrAddOrRemove       If product variants switch for add/remove Wlr
     * @param string $userType,                            Business actor user type
     * @param string $expectedChangeChannel                Expected ChangeChannel
     *
     * @covers AccountChange_Action_LtcContracts::getChangeChannel
     * @dataProvider provideDataForGetChangeChannelReturnsProperChangeChannel
     *
     * @return void
     */
    public function testGetChangeChannelReturnsProperChangeChannel(
        $runFromPerformScheduledAccountChange,
        $scheduled,
        $variantSwitchForWlrAddOrRemove,
        $userType,
        $expectedChangeChannel
    ) {
        $serviceId = ********;
        $oldServiceDefinitionId = 6827;
        $newServiceDefinitionId = 6833;
        $oldAdslComponentId = ***********;
        $newAdslComponentId = ***********;
        $newWlrComponentId = ***********;
        $newAdslServiceComponentId = 1400;

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intOldServiceDefinitionId', $oldServiceDefinitionId);
        $registry->setEntry('intNewServiceDefinitionId', $newServiceDefinitionId);
        $registry->setEntry('oldAdslComponentId', $oldAdslComponentId);
        $registry->setEntry('newAdslComponentId', $newAdslComponentId);
        $registry->setEntry('newWlrComponentId', $newWlrComponentId);
        $registry->setEntry('newAdslServiceComponentId', $newAdslServiceComponentId);
        $registry->setEntry('runFromPerformScheduledAccountChange', $runFromPerformScheduledAccountChange);
        $registry->setEntry('bolSchedule', $scheduled);
        $registry->setEntry('bolVariantSwitchForWlrAddOrRemove', $variantSwitchForWlrAddOrRemove);

        $servicesToAdd = array(
            0 => array(
                'serviceTypeId' => $newAdslServiceComponentId,
                'serviceId'     => $newAdslComponentId
            )
        );

        $servicesToRemove = array(
            0 => $oldAdslComponentId
        );

        $changePlan = $this->getMock(
            '\Plusnet\ProductChangePlanClient\Entity\ChangePlan',
            array(
                'execute',
                'isRecontractingOrNewContract'
            ),
            array(),
            '',
            false
        );
        $changePlan->expects($this->any())
            ->method('isRecontractingOrNewContract')
            ->will($this->returnValue(true));

        $changePlanClient = $this->getMock(
            '\Plusnet\ProductChangePlanClient\Client',
            array(
                'executeScheduledProductChange',
                'productChange'
            ),
            array(),
            '',
            false
        );

        $changePlanClient
            ->expects($this->any())
            ->method('executeScheduledProductChange')
            ->with(
                $this->equalTo($expectedChangeChannel),
                $serviceId,
                $oldServiceDefinitionId,
                $newServiceDefinitionId,
                $servicesToAdd,
                $servicesToRemove
            )
            ->will($this->returnValue($changePlan));

        $changePlanClient
            ->expects($this->any())
            ->method('productChange')
            ->with(
                $this->equalTo($expectedChangeChannel),
                $serviceId,
                $oldServiceDefinitionId,
                $newServiceDefinitionId,
                $servicesToAdd,
                $servicesToRemove
            )
            ->will($this->returnValue($changePlan));

        $currentBusinessActor = $this->getMock(
            'Auth_BusinessActor',
            array('getUserType'),
            array()
        );

        if (!$runFromPerformScheduledAccountChange) {
            $currentBusinessActor
                ->expects($this->any())
                ->method('getUserType')
                ->will($this->returnValue($userType));
        }

        $ltcContracts = $this->getMock(
            'AccountChange_Action_LtcContracts',
            array(
                'getNewContractDuration',
                'getChangePlanClient',
                'getCurrentBusinessActor'
            ),
            array($serviceId, array())
        );

        $ltcContracts
            ->expects($this->any())
            ->method('getChangePlanClient')
            ->will($this->returnValue($changePlanClient));

        if (!$runFromPerformScheduledAccountChange) {
            $ltcContracts
                ->expects($this->any())
                ->method('getCurrentBusinessActor')
                ->will($this->returnValue($currentBusinessActor));
        }

        $ltcContracts->execute();

        $this->assertEquals(true, $registry->getEntry('willAccountChangeResultInRecontract'));
    }

    /**
     * Provide data for test if getChangeChannel reurns proper ChangeChannel
     *
     * @return array
     */
    public function provideDataForGetChangeChannelReturnsProperChangeChannel()
    {
        return array(
            array(
                'runFromPerformScheduledAccountChange'  => false,
                'scheduled'                             => true,
                'variantSwitchForWlrAddOrRemove'        => false,
                'userType'                              => 'PLUSNET_ENDUSER',
                'expectedChangeChannel'
                    => \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_CUSTOMER_REQUEST
            ),
            array(
                'runFromPerformScheduledAccountChange'  => false,
                'scheduled'                             => true,
                'variantSwitchForWlrAddOrRemove'        => false,
                'userType'                              => 'PLUSNET_STAFF',
                'expectedChangeChannel'
                    => \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST
            ),
            array(
                'runFromPerformScheduledAccountChange'  => false,
                'scheduled'                             => false,
                'variantSwitchForWlrAddOrRemove'        => false,
                'userType'                              => 'PLUSNET_STAFF',
                'expectedChangeChannel'
                    => \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE
            ),
            array(
                'runFromPerformScheduledAccountChange'  => false,
                'scheduled'                             => true,
                'variantSwitchForWlrAddOrRemove'        => true,
                'userType'                              => 'PLUSNET_ENDUSER',
                'expectedChangeChannel'
                    => \Plusnet\ProductChangePlanClient\ChangeChannels::CHANGE_AGENT_REQUEST_IMMEDIATE
            )
        );
    }
}
