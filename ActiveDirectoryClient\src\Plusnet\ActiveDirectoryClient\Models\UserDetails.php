<?php

/**
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\ActiveDirectoryClient\Models;

/**
 * Class UserDetails
 *
 * @package Plusnet\ActiveDirectoryClient
 */
final class UserDetails implements \JsonSerializable
{
    /**
     * We're recording that we've found an invalid value rather than throwing an exception.
     * This information needs to be propagated to GIMP using the cookie. This is because the agent
     * will take a different journey if they've not logged in (no cookie) vs if we can't get data
     * from active directory / it's invalid.
     */
    const INVALID_EIN = "INVALID_EIN";

    /**
     * Logs partner flag is invalid
     */
    const INVALID_IS_PARTNER = "INVALID_PARTNER";

    /**
     * REGEX to check EIN is 9 digits only.
     */
    const IS_EIN_VALID_REGEX = '/^\d{9}$/';

    /**
     * Key used for the agent EIN in JSON.
     */
    const KEY_EIN = 'ein';

    /**
     * Key used for isPartner in JSON.
     */
    const KEY_IS_PARTNER = 'partner';

    /**
     * @var string
     */
    private $ein;

    /**
     * @var bool|string
     */
    private $isPartner;

    /**
     * UserDetails constructor.
     *
     * @param string  $ein       Agents EIN
     * @param boolean $isPartner Is agent a partner agent
     */
    public function __construct($ein, $isPartner)
    {
        $this->isPartner = $this->isValidIsPartner($isPartner)
            ? $isPartner : self::INVALID_IS_PARTNER;
        $this->ein = $this->isValidEin($ein)
            ? $ein : self::INVALID_EIN;
    }

    /**
     * Get the EIN value.
     *
     * @return string
     */
    public function getEin()
    {
        return $this->ein;
    }

    /**
     * Get the isPartner value.
     *
     * @return bool|string
     */
    public function getIsPartner()
    {
        return $this->isPartner;
    }

    /**
     * Check if the EIN is valid.
     *
     * @return bool
     */
    public function hasValidEin()
    {
        return self::INVALID_EIN !== $this->getEin();
    }

    /**
     * Check if the isPartner is valid.
     *
     * @return bool
     */
    public function hasValidIsPartner()
    {
        return self::INVALID_IS_PARTNER !== $this->getIsPartner();
    }

    /**
     * JSON representation of the user details.
     *
     * @return array
     */
    public function jsonSerialize()
    {
        return array(
            self::KEY_EIN => $this->getEin(),
            self::KEY_IS_PARTNER => $this->getIsPartner()
        );
    }

    /**
     * Check to see if the partner flag is a bool
     *
     * @param $isPartner
     *
     * @return bool
     */
    private function isValidIsPartner($isPartner)
    {
        return is_bool($isPartner);
    }

    /**
     * Checks to see if the EIN is a valid format
     *
     * @param $ein
     *
     * @return bool
     */
    private function isValidEin($ein)
    {
        return 1 === preg_match(self::IS_EIN_VALID_REGEX, $ein);
    }
}
