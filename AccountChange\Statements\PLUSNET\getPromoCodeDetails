server: coredb
role: slave
rows: single
statement:

SELECT
    pc.intPromotionCodeId AS promoCodeId,
    pd.decValue AS discountValue,
    dt.vchHandle AS discountType,
    pd.intDiscountLength AS discountLength
  FROM products.tblPromotionCode AS pc
 INNER JOIN financial.tblPresetDiscount pd
    ON (pd.intPresetDiscountId = pc.intPresetDiscountId)
 INNER JOIN financial.tblDiscountType dt
    ON (pd.intDiscountTypeId = dt.intDiscountTypeId)
 INNER JOIN products.tblPromotionCodeServiceDefinition AS pcsd
    ON pcsd.intPromotionCodeId = pc.intPromotionCodeId
 WHERE pc.vchPromotionCode = :promoCode
   AND pcsd.intServiceDefinitionId = :serviceDefinitionId
   AND (pc.dteValidFrom IS NULL OR pc.dteValidFrom <= NOW())
   AND (pc.dteValidTo IS NULL OR pc.dteValidTo >= NOW())