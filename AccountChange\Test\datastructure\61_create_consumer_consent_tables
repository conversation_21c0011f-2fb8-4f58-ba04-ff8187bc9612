CREATE TABLE `userdata`.`services` (
  `service_id` int(8) unsigned zerofill NOT NULL AUTO_INCREMENT,
  `user_id` int(8) unsigned NOT NULL DEFAULT '0',
  `isp` varchar(10) NOT NULL DEFAULT '',
  `username` varchar(30) NOT NULL DEFAULT '',
  `password` varchar(255) NOT NULL DEFAULT '',
  `cli_number` varchar(30) NOT NULL DEFAULT '',
  `type` int(8) NOT NULL DEFAULT '0',
  `status` enum('queued-activate','queued-reactivate','active','queued-deactivate','deactive','queued-destroy','destroyed','unconfigured','invalid','presignup') NOT NULL DEFAULT 'invalid',
  `startdate` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `enddate` datetime DEFAULT NULL,
  `next_invoice` date NOT NULL DEFAULT '0000-00-00',
  `invoice_period` enum('monthly','quarterly','yearly','never','half-yearly') NOT NULL DEFAULT 'monthly',
  `db_src` varchar(4) NOT NULL DEFAULT '',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `next_invoice_warned` enum('yes','no') DEFAULT 'yes',
  `invoice_day` int(2) NOT NULL DEFAULT '0',
  `authorised_switch_payment` enum('yes','no','immediate') NOT NULL DEFAULT 'no',
  `bolMailOptOut` tinyint(3) unsigned DEFAULT '0',
  PRIMARY KEY (`service_id`),
  KEY `isp` (`isp`,`username`),
  KEY `user_id` (`user_id`),
  KEY `startdate` (`startdate`),
  KEY `username` (`username`),
  KEY `cli_number` (`cli_number`),
  KEY `status` (`status`),
  KEY `type` (`type`),
  KEY `next_invoice` (`next_invoice`),
  KEY `invoice_period` (`invoice_period`),
  KEY `enddate` (`enddate`)
) ENGINE=InnoDB AUTO_INCREMENT=2506982 DEFAULT CHARSET=latin1;

CREATE TABLE dbCommonLookupTable.tblApplication(
    intApplicationId tinyint unsigned NOT NULL AUTO_INCREMENT,
    vchHandle varchar(20) NOT NULL,
    vchDisplayName varchar(100) NOT NULL,
    stmLastUpdate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (intApplicationId)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE dbCommonLookupTable.tblAffiliate(
    intAffiliateId tinyint unsigned NOT NULL AUTO_INCREMENT,
    vchHandle varchar(20) NOT NULL,
    vchDisplayName varchar(100) NOT NULL,
    stmLastUpdate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (intAffiliateId)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE dbCommonLookupTable.tblAffiliateIPRange(
    intAffiliateIPRangeId tinyint unsigned NOT NULL AUTO_INCREMENT,
    intAffiliateId tinyint unsigned NOT NULL,
    stmLastUpdate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    strIPRangeStart varchar(45) NOT NULL,
    strIPRangeEnd varchar(45) NOT NULL,
    PRIMARY KEY (intAffiliateIPRangeId),
    CONSTRAINT tblAffiliateIPRangefk_1 FOREIGN KEY (intAffiliateId) REFERENCES dbCommonLookupTable.tblAffiliate (intAffiliateId),
    INDEX idxAffiliateId (intAffiliateId)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE dbCommonLookupTable.tblConsumerConsentVersion(
    intConsumerConsentVersionId tinyint unsigned NOT NULL AUTO_INCREMENT,
    vchHandle varchar(20) NOT NULL,
    vchDisplayName varchar(255) NOT NULL,
    stmLastUpdate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (intConsumerConsentVersionId)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE userdata.tblConsumerConsent(
    intConsumerConsentId smallint unsigned NOT NULL AUTO_INCREMENT,
    intServiceId int(8) unsigned zerofill NOT NULL,
    intAgentActorId int,
    intServiceComponentId int unsigned zerofill NOT NULL,
    dtmCaptured timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    intApplicationId tinyint unsigned NOT NULL,
    intAffiliateId tinyint unsigned,
    intConsentVersionId tinyint unsigned,
    PRIMARY KEY (intConsumerConsentId),
    CONSTRAINT tblConsumerConsentfk_1 FOREIGN KEY (intServiceId) REFERENCES userdata.services (service_id),
    CONSTRAINT tblConsumerConsentfk_2 FOREIGN KEY (intAgentActorId) REFERENCES PlusnetSession.tblBusinessActor (actorID),
    CONSTRAINT tblConsumerConsentfk_3 FOREIGN KEY (intServiceComponentId) REFERENCES products.service_components (service_component_id),
    CONSTRAINT tblConsumerConsentfk_4 FOREIGN KEY (intApplicationId) REFERENCES dbCommonLookupTable.tblApplication (intApplicationId),
    CONSTRAINT tblConsumerConsentfk_5 FOREIGN KEY (intAffiliateId) REFERENCES dbCommonLookupTable.tblAffiliate (intAffiliateId),
    CONSTRAINT tblConsumerConsentfk_6 FOREIGN KEY (intConsentVersionId) REFERENCES dbCommonLookupTable.tblConsumerConsentVersion (intConsumerConsentVersionId),
    INDEX idxServiceId (intServiceId),
    INDEX idxAgentActorId (intAgentActorId),
    INDEX idxServiceComponentId (intServiceComponentId),
    INDEX idxApplicationId (intApplicationId),
    INDEX idxAffiliateId (intAffiliateId),
    INDEX idxConsentVersionId (intConsentVersionId)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;
