<?php
/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_MGALSHelperTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_DEF = 1234;
    const LINE_CHECK_ID = 666;
    const SERVICE_CHANGE_ID = 1337;
    const SERVICE_ID = 555;

    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|ProductComponent_MGALS
     */
    private $mockProductComponentMGALS;

    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|AccountChange_MGALSHelper
     */
    private $test;

    /**
     * setUp
     */
    public function setUp()
    {
        $this->mockProductComponentMGALS = Mockery::mock(ProductComponent_MGALS::class);

        $this->test = Mockery::mock(AccountChange_MGALSHelper::class)
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        $this->test->__construct($this->mockProductComponentMGALS);
    }

    /**
     * tearDown
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @test
     */
    public function shouldGetValuesFromLineCheckAndPassToProductComponentMGALSClass()
    {
        $lineCheckData = [
            static::SERVICE_DEF => [
                'mgs' => 40000
            ]
        ];

        $this->test
            ->shouldReceive('getDataFromLineCheckHelper')
            ->once()
            ->with(static::LINE_CHECK_ID, static::SERVICE_DEF)
            ->andReturn($lineCheckData);

        $this->mockProductComponentMGALS
            ->shouldReceive('saveScheduledMinimumLineSpeedValue')
            ->with(static::SERVICE_CHANGE_ID, \Mockery::on(function ($argument) {
                $this->assertTrue($argument === 40);
                return true;
            }));

        $this->test->saveMGALSFromLineCheckData(
            static::LINE_CHECK_ID,
            static::SERVICE_DEF,
            static::SERVICE_CHANGE_ID
        );
    }

    /**
     * @test
     * @dataProvider makeMGALSLiveProvider
     */
    public function shouldMakeMGALSLiveAndLogResultCorrectly($makeLiveResult, $expectedMessage)
    {
        $defaultMessage = $this->test->getMakeLiveResultMessage();

        $this->mockProductComponentMGALS
            ->shouldReceive('makeScheduledMGALSLive')
            ->with(static::SERVICE_ID)
            ->andReturn($makeLiveResult);

        $this->test->makeScheduledMGALSLiveForServiceId(static::SERVICE_ID);
        $this->assertSame('Not attempted', $defaultMessage);
        $this->assertSame($expectedMessage, $this->test->getMakeLiveResultMessage());
    }

    /**
     * @return array
     */
    public function makeMGALSLiveProvider()
    {
        $successMessage = AccountChange_MGALSHelper::MAKE_LIVE_LOG_MESSAGE_SUCCESS;
        $failMessage = sprintf(AccountChange_MGALSHelper::MAKE_LIVE_LOG_MESSAGE_FAIL, static::SERVICE_ID);

        return [
            ['Success' => true, 'ExpectedMessage' => $successMessage],
            ['Fail' => false, 'ExpectedMessage' => $failMessage]
        ];
    }

}