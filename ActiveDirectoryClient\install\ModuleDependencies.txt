#                         'Payment2CustomerPortalApplication' Module Dependencies
#
# A comma separated, whitespace padded list of dependencies on other Plusnet Framework
# modules the 'Framework' Module has
#
# a Namespace is a functional division  within a module identified by a unique
# prefix (classes conform to <Namespace>_<Prefix>). Modules are collections of
# namespaces sharing a single CVS module.  A module is the smallest deployable
# code grouping. For multiple namespaces to share a # module there must be inherent
# inter-dependencies between those namespaces.
#
# Each application is a single module. Library functions shared between several
# applications also form a distinct modules. Often a namespace will begin as an
# intrinsic part of one application and then migrate into its own module when
# another application becomes dependent upon it.
#
# Dependencies can be specified to either an entire module or a namespace within
# that module. Specifying individual namespaces is preferred.
#
#
#    Local Namespace  |                Dependent Upon                     |
#       Name          |  module / namespace   |            Name           |
#==========================================================================
#
ActiveDirectoryClient, module, Framework