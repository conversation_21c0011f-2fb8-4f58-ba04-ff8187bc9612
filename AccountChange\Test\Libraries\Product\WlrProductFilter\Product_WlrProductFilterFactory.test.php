<?php

/**
 * <AUTHOR>
 */

class AccountChange_Product_WlrProductFilterFactoryTest extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function testShouldGetWlrProductFilterPlusnetBusinessWhenPlusnetBusiness()
    {
        $this->assertInstanceOf(
            AccountChange_Product_WlrProductFilterPlusnetBusiness::class,
            AccountChange_Product_WlrProductFilterFactory::getFilter(true, true)
        );
    }

    /**
     * @return void
     */
    public function testShouldGetNullWhenNoMatchingFilterFound()
    {
        $this->assertInstanceOf(
            AccountChange_Product_WlrProductFilterDefault::class,
            AccountChange_Product_WlrProductFilterFactory::getFilter(true, false)
        );
        $this->assertInstanceOf(
            AccountChange_Product_WlrProductFilterDefault::class,
            AccountChange_Product_WlrProductFilterFactory::getFilter(false, true)
        );
        $this->assertInstanceOf(
            AccountChange_Product_WlrProductFilterDefault::class,
            AccountChange_Product_WlrProductFilterFactory::getFilter(false, false)
        );
    }
}
