<?php

class AccountChange_AccountChangeOrderContract
{
    /**
     * @var int
     */
    private $length;

    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $agreementDate;

    /**
     * @var string
     */
    private $subType;

    /**
     * @return int
     */
    public function getLength()
    {
        return $this->length;
    }

    /**
     * @param int $length - contract length
     * @return void
     */
    public function setLength($length)
    {
        $this->length = $length;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type - contract type
     * @return void
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * @return string
     */
    public function getSubType()
    {
        return $this->subType;
    }

    /**
     * @param string $subType - contract sub type
     * @return void
     */
    public function setSubType($subType)
    {
        $this->subType = $subType;
    }

    /**
     * @return string
     */
    public function getAgreementDate()
    {
        return $this->agreementDate;
    }

    /**
     * @param string $agreementDate - agreement date
     * @return void
     */
    public function setAgreementDate($agreementDate)
    {
        $this->agreementDate = $agreementDate;
    }

}
