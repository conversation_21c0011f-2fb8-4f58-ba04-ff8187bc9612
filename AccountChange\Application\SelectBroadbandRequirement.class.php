<?php

use \Plusnet\PriceProtected\Services\Factory\StatusServiceFactory;

/**
 * Select Broadband Requirement
 *
 * Shared functions between the portal and workplace select broadband
 * requirements.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2009-09-14
 */
/**
 * AccountChange_SelectBroadband class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_SelectBroadbandRequirement extends Mvc_WizardRequirement
{
    /**
     * To meet one of the requirements for ADSL2 project we need to see if the customer has hardware post/pre this date.
     *
     * @var string
     */
    const OFFER_HARDWARE_DATE = '01-04-2007';

    /**
     * Date at which we start dispatch TR069 routers to FTTC customers
     *
     * @var string
     */
    const NEW_FTTC_HARDWARE_DISPATCH_DATE = '21-06-2012';

    /**
     * Plusnet Value Fibre service definition id
     *
     * @var integer
     */
    const PLUSNET_VALUE_FIBRE_SDI = 6768;

    /* @var HardwareClient_Client $hardwareClient */
    private $hardwareClient;

    /**
     * Should we display the hub two info message
     * @var bool
     */
    protected $shouldDisplayHardwareMessage = false;

    /**
     * @var AccountChange_HardwareRequirementHelper
     */
    protected $hardwareHelper;

    /**
     * Returns an AccountChange_Mode instance
     *
     * @param $objBusinessActor
     * @return AccountChange_Mode
     */
    public function createAccountChangeModeInstance($objBusinessActor)
    {
        return AccountChange_Mode::instance($objBusinessActor);
    }

    /**
     * Get the new requirements depending on the input we currently have.
     *
     * @return array
     */
    public function getNewReqs()
    {
        $arrNewReqs = array();

        if (!$this->isApplicationStateVariable('intOldSdi') || !$this->isApplicationStateVariable('objBusinessActor')) {
            return $arrNewReqs;
        }

        $intNewSdi = $this->getNewSdi();

        if (0 != $intNewSdi) {
            if (!AccountChange_Controller::isBundle($intNewSdi)) {
                $objBusinessActor = $this->getApplicationStateVariable('objBusinessActor');


                /**
                 * DLIFE-118
                 * Here we are setting the change mode for how to perform the account change journey
                 * This allows us to skip the select broadband and hardward step,
                 * progressing to the homephone step instead.
                 */

                $mode = $this->createAccountChangeModeInstance($objBusinessActor);
                $changeMode = null;

                if ($this->getApplicationStateVariable('bolChangeBroadbandOnly')) {
                    $mode->setChangeMode(AccountChange_Mode::CHANGE_BROADBAND_ONLY);

                    $changeMode = $mode->getChangeMode();
                }

                if ($this->isWlrSelectionNeeded() &&
                    ($changeMode != AccountChange_Mode::CHANGE_BROADBAND_ONLY ||
                    $changeMode == null)) {
                    if ('PLUSNET_STAFF' == $objBusinessActor->getUserType()) {
                        $arrNewReqs[] = 'AccountChange_SelectHomephoneWorkplace';
                    } else {
                        $arrNewReqs[] = 'AccountChange_SelectHomephone';
                    }
                }
            }
        }

        if ($this->isHardwareRequirementNeeded()) {
            $arrNewReqs[] = 'AccountChange_Hardware';
        }

        if ($this->isEngineerAppointmentNeeded()) {
            $arrNewReqs[] = 'AccountChange_EngineerDetails';

            // If the address matcher gave us a list of addresses, then we also need to display the Address match page.
            if ($this->isApplicationStateVariable('addresses') &&
                $this->getApplicationStateVariable('addresses')->count() > 1
            ) {
                $arrNewReqs[] = 'AccountChange_Address';
            }
        }

        return $arrNewReqs;
    }

    /**
     * Does Checks for business and Solus/Dualplay 2013.
     *
     * @return boolean
     */
    protected function isWlrSelectionNeeded()
    {
        $arrWlrProduct   = $this->getApplicationStateVariable('arrWlrProduct');
        $bolHousemove   = $this->getApplicationStateVariable('bolHousemove');
        $isBroadbandOnly = false;

        if ($this->isApplicationStateVariable('selectedProductFamily')) {
            $productFamily = $this->getApplicationStateVariable('selectedProductFamily');

            if ($productFamily->isSolus()) {
                $isBroadbandOnly = true;
            }
        }

        // Check that the account can change their WLR product. Problem 57327
        // We also exclude the phone requirement page if a user has selected to only have a broadband product
        if (($arrWlrProduct['bolWlrChangeAllowed'] || $arrWlrProduct['bolWlrAddAllowed']) && !$isBroadbandOnly) {
            return true;
        } elseif ($bolHousemove && $arrWlrProduct['bolWlrAddAllowed']) {
            return true;
        }

        return false;
    }

    /**
     * An Engineer appointment is needed to upgrade a line to FTTC / FTTP. So, if the new product is FTTC or FTTC and
     * the old product was not, then we need an appointment. We don't book an appointment if an instant account change
     * is happening.
     *
     * @return boolean
     */
    protected function isEngineerAppointmentNeeded()
    {
        if (!$this->isApplicationStateVariable('intOldSdi') ||
            !$this->isApplicationStateVariable('arrSelectedBroadband') ||
            !$this->isApplicationStateVariable('currentSupplierProduct')
        ) {
            return false;
        }

        // Don't show engineer page if in WP and performing an instant account change
        if ((!$this->isApplicationStateVariable('bolPortal') ||
                $this->getApplicationStateVariable('bolPortal') == false
            ) &&
            (!$this->isApplicationStateVariable('bolSchedule') ||
                $this->getApplicationStateVariable('bolSchedule') == false
            )
        ) {
            return false;
        }

        $selectedBroadband = $this->getApplicationStateVariable('arrSelectedBroadband');
        $currentSupplierProduct = $this->getApplicationStateVariable('currentSupplierProduct');

        return (
            isset($selectedBroadband['provisioningProfile']) &&
            in_array($selectedBroadband['provisioningProfile'], array()) &&
            $selectedBroadband['provisioningProfile'] != $currentSupplierProduct->getProductCode()
        );
    }

    /**
     * Getter for the new service definition id.
     *
     * @return integer
     */
    protected function getNewSdi()
    {
        return (isset($this->arrData['intNewSdi']) ? $this->arrData['intNewSdi'] : 0);
    }

    /**
     * Check to see if the hardware requirement is needed.
     *
     * @return boolean
     */
    public function isHardwareRequirementNeeded()
    {
        /* @var Core_Service $service */
        $service = $this->getApplicationStateVariable('objCoreService');
        $this->hardwareClient = BusTier_BusTier::getClient('hardware');
        $fibreHelper = new AccountChange_FibreHelper();
        $newSdi = (int)$this->getApplicationStateVariable('intNewSdi');
        $isHousemove = (bool)$this->getApplicationStateVariable('bolHousemove');
        $isRecontract = (bool)$this->getApplicationStateVariable('selectedContractDuration');
        $this->hardwareHelper = $this->getHardwareHelper(
            $service,
            $isHousemove,
            $isRecontract,
            $newSdi,
            $fibreHelper
        );

        if ($this->hardwareHelper->shouldUseHelper()) {
            return $this->hardwareHelper->shouldShowHardwarePage();
        } else {
            $provisionOn = $this->getApplicationStateVariable('strProvisionOn');
            $lineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');
            $selectedBroadband = $this->getApplicationStateVariable('arrSelectedBroadband');

            $provisionedService = $this->getProvisionedService($service->getServiceId());
            if (empty($provisionedService)) {
                $provisionedService = $this->getProvisionedService($service->getServiceId(), false);
            }

            $oldSupplierProduct
                = isset($provisionedService['vchProductCode']) ? $provisionedService['vchProductCode'] : '';
            $newSupplierProduct
                = isset($selectedBroadband['provisioningProfile']) ? $selectedBroadband['provisioningProfile'] : '';

            // Moving from non-FTTC to FTTC should always provide hardware
            // This solution is wrong for many reasons. First off it's comparing product codes
            // which will be fixed in RES-616.
            // Second off, it shouldn't hard-code FTTC as a product to move to. What about
            // FTTP and the like?
            // Hardware should have a list of "capabilities" (PPPoA, PPPoE, ADSL, ADSL2, etc) and each
            // supplier product should have a list of "requirements". The correct hardware
            // can be chosen by comparing the available hardware with requirements.
            // Similarly, we can check if a customer has already had hardware capable of supporting
            // the service they're changing to.
            // However, in the interests of expediency, this will do until RES-616.
            if ($newSupplierProduct == 'FTTC') {
                if ($oldSupplierProduct == 'FTTC') {
                    $lastDispatchedStatus = $this->hardwareClient->getLastHardwareDispatchedStatusForService(
                        new Int($service->getServiceId())
                    );

                    if (empty($lastDispatchedStatus)) {
                        return true;
                    } else {
                        $lastDispatchDate = I18n_Date::fromString($lastDispatchedStatus['dtmStart']);

                        return ($newSdi != self::PLUSNET_VALUE_FIBRE_SDI &&
                            $lastDispatchDate < I18n_Date::fromString(self::NEW_FTTC_HARDWARE_DISPATCH_DATE)
                        );
                    }
                } else {
                    return true;
                }
            }

            $rules = AccountChange_ProductRules::instance();

            // From the DDD v1.1
            // (filestore/Projects/ADSL2+ Products/DDD and Requirements/DDD_ADSL2+_Refresh_2009_v1.1.doc
            //
            // Where ADSL2+ available, from the line check results, perform a check to see
            // 1. If the customer already has had hardware added to their account post
            //    April 2007 then do not offer hardware
            // 2. If the customer has no hardware on their account or has purchased hardware
            //    before April 2007 then offer the customer the following to purchase
            $arrProductProvDetails = $rules->getProductProvisionForService($newSdi, $lineCheckResult);
            $this->arrData['strCompulsoryHardware'] = '';

            // If there is compulsory hardware linked to sdi, there is no need to show the hardware selection page.
            $arrCompusloryHardware = $this->hardwareClient->getCompulsoryHardwareForServiceDefinition(new Int($newSdi));

            if (!empty($arrCompusloryHardware['strHandle'])) {
                $this->arrData['strCompulsoryHardware'] = $arrCompusloryHardware['strHandle'];
            }

            if ($arrProductProvDetails['bolWbcProduct'] && 'Adsl' != $provisionOn) {
                Dbg_Dbg::write(
                    'AccountChange_SelectBroadbandRequirement::isHardwareRequirementNeeded product is wbc',
                    'AccountChange'
                );

                if (!empty($this->arrData['strCompulsoryHardware'])) {
                    return false;
                }

                $components = $this->hardwareClient->getHardwareForService(new Int($service->getServiceId()));

                if (empty($components)) {
                    Dbg_Dbg::write(
                        'AccountChange_SelectBroadbandRequirement::isHardwareRequirementNeeded no hardware on account',
                        'AccountChange'
                    );
                    return true;
                }

                $lastComponent = $this->hardwareClient->getLastAddedHardwareForService(
                    new Int($service->getServiceId())
                );

                if ($lastComponent['dtmCreated'] < I18n_Date::fromString(self::OFFER_HARDWARE_DATE)) {
                    Dbg_Dbg::write(
                        'AccountChange_SelectBroadbandRequirement::isHardwareRequirementNeeded current hardware pre dates '.
                        self::OFFER_HARDWARE_DATE,
                        'AccountChange'
                    );
                    return true;
                }
            }
        }



        return false;
    }

    /**
     * A wrapper around legacy call financial_calculate_refund to make it possible to mock it.
     *
     * @param integer $intServiceId         Service ID
     * @param integer $intNewServiceDef     New service definition
     * @param integer $intCurrentServiceDef Current service definition
     *
     * @return array
     */
    public function get_financial_calculate_refund($intServiceId, $intNewServiceDef, $intCurrentServiceDef)
    {
        $this->includeLegacyFiles();

        return financial_calculate_refund($intServiceId, $intNewServiceDef, $intCurrentServiceDef);
    }

    /**
     * Return financial errors if there are any. If there are errors we can not calculate BB pro-rata charge - and we
     * can not continue.
     *
     * @param integer $intServiceId         Service ID
     * @param integer $intNewServiceDef     New service definition
     * @param integer $intCurrentServiceDef Current service definition
     *
     * @return array
     */
    public function getFinancialErrors($intServiceId, $intNewServiceDef, $intCurrentServiceDef)
    {
        $arrRet = $this->get_financial_calculate_refund($intServiceId, $intNewServiceDef, $intCurrentServiceDef);

        if (array_key_exists('error', $arrRet)) {
            return $arrRet;
        }

        return array();
    }

    /**
     * Validates output option.
     *
     * If Linecheck has thrown an LineCheck_BtRequestException customer has to agree to progress with account change
     * without valid linecheck results.
     *
     * @param boolean $bolOptOut      Opt out
     * @param boolean $bolBtException BT Exception
     *
     * @see AccountChange_ShowLinechecker::valPhoneNumber()
     * @see AccountChange_ShowLinechecker::performLineCheck()
     *
     * @return array
     */
    public function valOptOut($bolOptOut)
    {
        $arrValidatedReturn = array();

        $arrValidatedReturn['bolOptOut'] = $bolOptOut;


        return $arrValidatedReturn;
    }

    /**
     * Validation for the provisioning radio boxes.
     *
     * @param string $strProvisionOn Input from the radio boxes
     *
     * @return array
     */
    public function valProvisionOn($strProvisionOn)
    {
        $validatedReturn = array();

        if (isset($strProvisionOn)) {
            if (!in_array($strProvisionOn, array('Adsl2', 'Adsl', 'FTTC', 'FTTP', ''))) {
                $this->addValidationError('strProvisionOn', 'INVALID');
            }
        }

        $validatedReturn['strProvisionOn'] = $strProvisionOn;

        return $validatedReturn;
    }

    /**
     * Get the current provisioned service details.
     *
     * @param integer $serviceId Service ID
     * @param boolean $active    If we want active provisioned service
     *
     * @return array
     */
    protected function getProvisionedService($serviceId, $active = true)
    {
        $this->includeLegacyFiles();

        return adslGetProvisionedService($serviceId, $active);
    }

    /**
     * Determine whether or not the currently provisioned platform is ADSL or ADSL2.
     *
     * @param integer $serviceId Service id
     *
     * @return boolean
     */
    protected function isProvisionedOnAdsl2($serviceId)
    {
        $currentPlatform = $this->getProvisionedService($serviceId);

        if (isset($currentPlatform['vchSupplierPlatform']) &&
            'BT21CN' == $currentPlatform['vchSupplierPlatform']
        ) {
            return true;
        }

        return false;
    }

    /**
     * Gets the type provisioned on for the service. This is either: Adsl, Adsl2, FTTC or FTTP.
     *
     * @param integer $intServiceId          Service Id
     * @param boolean $bolProvisionedOnAdsl2 Was provisioned on Adsl2
     *
     * @return string
     */
    protected function getProvisionedOn($intServiceId, $bolProvisionedOnAdsl2)
    {
        $strResult = "";

        if ($bolProvisionedOnAdsl2) {
            $provisionedService = $this->getProvisionedService($intServiceId);

            if (isset($provisionedService['vchProductCode']) && $provisionedService['vchProductCode'] == 'FTTC') {
                $strResult =  $provisionedService['vchProductCode'];
            } else {
                $strResult = 'Adsl2';
            }
        } else {
            $strResult = 'Adsl';
        }

        return $strResult;
    }

    /**
     * Return true if the old service definition is a fibre product.
     *
     * @return boolean
     **/
    protected function isOldProductFibre()
    {
        $oldSdi = $this->getApplicationStateVariable('intOldSdi');
        $fibreHelper = new AccountChange_FibreHelper();

        return $fibreHelper->isFibreProduct($oldSdi);
    }

    /**
     * Get a status service object for fixed price contracts
     *
     * @return \Plusnet\PriceProtected\Services\StatusService
     **/
    protected function getFpcStatusService()
    {
        return StatusServiceFactory::createService();
    }

    /**
     * @param Core_Service              $coreService          core service object
     * @param boolean                   $isHousemove          housemove flag
     * @param boolean                   $isRecontract         recontract flag
     * @param int                       $newServiceDefinition the product being changed to
     * @param AccountChange_FibreHelper $fibreHelper          Fibre helper object
     * @return AccountChange_HardwareRequirementHelper
     */
    protected function getHardwareHelper(
        Core_Service $coreService,
        $isHousemove,
        $isRecontract,
        $newServiceDefinition,
        AccountChange_FibreHelper $fibreHelper
    ) {
        return new AccountChange_HardwareRequirementHelper(
            $this->hardwareClient,
            $coreService,
            $isHousemove,
            $isRecontract,
            $newServiceDefinition,
            $fibreHelper,
            $this->isApplicationStateVariable('bolPortal')
        );
    }
}
