server:    coredb
role:      slave
rows:      multiple
statement:

    SELECT DISTINCT d.intDiscountId, c.service_id
      FROM financial.tblDiscount d
INNER JOIN userdata.tblProductComponentInstance pci
        ON pci.intProductComponentInstanceID = d.intProductComponentInstanceId
INNER JOIN userdata.components c
        ON c.component_id = pci.intComponentID
INNER JOIN financial.tblDiscountRedemption dr
        ON dr.intDiscountId = d.intDiscountId
 LEFT JOIN financial.tblDiscountCancellation dc
        ON dc.intDiscountRedemptionId = dr.intDiscountRedemptionId
     WHERE d.vchPromotionCode IN (:promotionCodes)
       AND d.dteCreatedDate >= '2018-03-04'
       AND d.dteCreatedDate < '2018-06-05'
       AND dc.intDiscountRedemptionId IS NULL