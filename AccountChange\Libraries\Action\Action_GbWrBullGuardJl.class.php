<?php
/**
 * Greenbee/Waitrose Product Set Change to <PERSON> action
 *
 * Action that converts BullGuard components to AddOns for GB/WR accounts
 * that are changing to a John Lewis product.
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @since      File available since 2011-09-14
 */

/**
 * Greenbee/Waitrose BullGuard Change to <PERSON> action class
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_Action_GbWrBullGuardJl extends AccountChange_Action_AbstractGbWrChangeToJl
{
    /**
     * Execute the action
     *
     * If the account has a bullguard component:
     * - Destroy any existing bullguard components
     * - Get the BullGuard add-on and add it to the account
     *
     * @return void
     */
    public function execute()
    {
        $this->includeLegacy();

        parent::execute();

        if ($this->isCustomerSwitchingFromGbWrToJlp()) {
            // Try and find the existing bullguard component, if there is one
            $arrComponents = $this->getComponents($this->intServiceId);
            $bolHasBullguard = false;
            foreach ($arrComponents as $arrComponent) {
                if (in_array($arrComponent['status'], array('active', 'queued-activate')) &&
                    in_array($arrComponent['component_type_id'], array(COMPONENT_BULL_GUARD, COMPONENT_BULL_GUARD_TRIAL))
                ) {
                    $bolHasBullguard = true;
                    $this->destroyOldComponent(
                        $arrComponent['component_type_id'],
                        $arrComponent['component_id']
                    );
                }
            }

            // If the service had a bullguard component of any type, add a new AddOn version
            if ($bolHasBullguard) {
                $arrAvailableBoltOns = $this->getAvailableBoltOns($this->intServiceId);
                foreach ($arrAvailableBoltOns as $availableBoltOn) {

                    // Update to use BullGuard AddOn.
                    if ($availableBoltOn->hasBehaviour(Products_ServiceComponent::BULLGUARD)) {
                        $this->addBoltOn($this->intServiceId, $availableBoltOn);
                        break;
                    }
                }
            }
        }
    }

    /**
     * Add a bolton to a service
     *
     * @param integer $intServiceId    service Id
     * @param object  $availableBoltOn bolt on
     *
     * @return void
     */
    public function addBoltOn($intServiceId, $availableBoltOn)
    {
        $boltOnService = new BoltOn_ServiceBoltOn(
            new Int($intServiceId),
            new String('PLUSNET_ENDUSER')
        );

        $boltOnService->addBoltOn($availableBoltOn);
    }

    /**
     * Get a list of available boltons for a given service
     *
     * @param integer $intServiceId the service Id
     *
     * @return array
     */
    protected function getAvailableBoltOns($intServiceId)
    {
        return BoltOn_AvailableBoltOn::getAvailablePortalBoltOnByServiceId(
            new Int($intServiceId),
            new String('PLUSNET_ENDUSER')
        );
    }

    /**
     * Get a list of existing components.
     * Wrapper for legacy function
     *
     * @param integer $intServiceId the service Id
     *
     * @return array
     */
    protected function getComponents($intServiceId)
    {
        return userdata_component_get_by_service($intServiceId);
    }

    /**
     * Call the auto-destroy configurator for a component
     * Wrapper for legacy function
     *
     * @param int $intComponentTypeId The service component ID
     * @param int $intComponentId     The component instance ID
     *
     * @return void
     */
    protected function destroyOldComponent($intComponentTypeId, $intComponentId)
    {
        require_once('/local/www/database-admin/config/config.inc');
        require_once('/local/data/mis/database/database_libraries/components/config-bullguard-access.inc');

        config_bullguard_configurator($intComponentId, 'auto_destroy');
    }

    /**
     * Include any legacy code required
     *
     * @return void
     */
    protected function includeLegacy()
    {
        require_once('/local/data/mis/database/database_libraries/components/component-defines.inc');
    }
}
