server: coredb
role: slave
rows: single
statement:
    SELECT s.type AS intCurrentSdi,
           s.type AS intOldSdi,
           sd.name AS strOldProductName,
           sdNew.name AS strNewProductName
      FROM userdata.services AS s 
INNER JOIN products.service_definitions as sd 
        ON sd.service_definition_id = s.type
INNER JOIN products.service_definitions as sdNew
        ON sdNew.service_definition_id = :newSdi
     WHERE s.service_id = :serviceId
