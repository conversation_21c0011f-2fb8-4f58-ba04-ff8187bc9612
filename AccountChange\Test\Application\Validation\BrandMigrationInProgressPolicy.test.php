<?php

class AccountChange_BrandMigrationInProcessPolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;

    /**
     * @return void
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @dataProvider validatorResponses
     * @param $serviceId
     * @param $data
     * @param $expectedResponse
     * @return void
     *
     */
    public function testValidatorRespondsCorrectly($serviceId, $data, $expectedResponse)
    {
        $migration = Mockery::mock('BrandMigration');
        $migration->shouldReceive('getCompletedAt')
            ->andReturn($data['completedAt']);
        $migration->shouldReceive('getCancelledAt')
            ->andReturn($data['cancelledAt']);
        $migration->shouldReceive('getTypeId')
            ->andReturn(2);

        $migrationService = Mockery::mock('BrandMigrationServiceFactory');
        if (is_null($data)) {
            $migrationService->shouldReceive('getBrandMigrationByServiceId')
                ->andReturn(null);
        } else {
            $migrationService->shouldReceive('getBrandMigrationByServiceId')
                ->andReturn($migration);
        }

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn($serviceId);

        $service = Mockery::mock();
        $service->shouldReceive('isJohnLewisUser')->andReturnFalse();

        $validator = Mockery::mock('AccountChange_BrandMigrationInProgressPolicy', [$actor])
            ->shouldAllowMockingProtectedMethods()
            ->makePartial();

        $validator->shouldReceive('getCoreService')
            ->andReturn($service);
        $validator->shouldReceive('getService')
            ->andReturn($migrationService);

        $this->assertEquals($expectedResponse, $validator->validate());
    }

    /**
     * @return array
     */
    public function validatorResponses()
    {
        return [
            'empty' => [
                'serviceId' => 1,
                'data' => null,
                'expectedResponse' => true
            ],
            'completed' => [
                'serviceId' => 2,
                'data' => ['recordId' => 1, 'serviceId' => self::SERVICE_ID, 'migratingCLI' => '0000', 'migratingBBEU' => 'AAAAA', 'startedAt' => '2019-01-01T00:00:00', 'completedAt' => '2019-01-01T00:00:00', 'cancelledAt' => null],
                'expectedResponse' => true
            ],
            'cancelled' => [
                'serviceId' => 3,
                'data' => ['recordId' => 1, 'serviceId' => self::SERVICE_ID, 'migratingCLI' => '0000', 'migratingBBEU' => 'AAAAA', 'startedAt' => '2019-01-01T00:00:00', 'completedAt' => null, 'cancelledAt' => '2019-01-01T00:00:00'],
                'expectedResponse' => true
            ],
            'in_progress' => [
                'serviceId' => 4,
                'data' => ['recordId' => 1, 'serviceId' => self::SERVICE_ID, 'migratingCLI' => '0000', 'migratingBBEU' => 'AAAAA', 'startedAt' => '2019-01-01T00:00:00', 'completedAt' => null, 'cancelledAt' => null],
                'expectedResponse' => false
            ]
        ];
    }
}
