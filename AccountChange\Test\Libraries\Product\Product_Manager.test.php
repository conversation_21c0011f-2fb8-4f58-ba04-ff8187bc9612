<?php
/**
 * Product Manager
 *
 * Testing class for the AccountChange_Product_Manager class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Product_Manager.test.php,v 1.2 2009-01-27 09:07:53 bselby Exp $
 * @since      File available since 2008-08-19
 */
/**
 * Product Manager Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Product_Manager_Test extends TestCaseWithProxy
{
    /**
     * PHPUnit method
     *
     */
    protected function tearDown()
    {
        //Lib_Product::resetInstance();
        Db_Manager::restoreAdaptor('AccountChange');
    }

    /**
     * @covers AccountChange_Product_ManagerException
     * @covers AccountChange_Product_Manager::factory
     * @covers AccountChange_Product_Manager::validateAction
     */
    public function testFactoryThrowsInvalidActionException()
    {
        $intAction = '';
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION;

        $this->setExpectedException('AccountChange_Product_ManagerException', 'Invalid action called',
                                    AccountChange_Product_ManagerException::ERR_INVALID_ACTION);

        AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);
    }

    /**
     * @covers AccountChange_Product_ManagerException
     * @covers AccountChange_Product_Manager::factory
     * @covers AccountChange_Product_Manager::validateProductId
     */
    public function testFactoryThrowsInvalidProductIdException()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = '';
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION;

        $this->setExpectedException('AccountChange_Product_ManagerException', 'Invalid product id provided',
                                    AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_ID);

        AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);
    }

    /**
     * @covers AccountChange_Product_ManagerException
     * @covers AccountChange_Product_Manager::factory
     * @covers AccountChange_Product_Manager::validateProductType
     */
    public function testFactoryThrowsInvalidProductTypeException()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = '';

        $this->setExpectedException('AccountChange_Product_ManagerException', 'Invalid product type provided',
                                    AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_TYPE);

        AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);
    }

    /**
     * @covers AccountChange_Product_ManagerException
     * @covers AccountChange_Product_Manager::factory
     */
    public function FactoryThrowsProductConfigurationClassNotExistException()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = 'Wlr';

        $this->setExpectedException('AccountChange_Product_ManagerException', 'Product configuration class doesn\'t exists',
                                    AccountChange_Product_ManagerException::ERR_PRODUCT_CONFIGURATION_CLASS_NOT_EXIST);

        AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);
    }

    /**
     * @covers AccountChange_Product_ManagerException
     * @covers AccountChange_Product_Manager::factory
     */
    public function FactoryThrowsInvalidProductConfigurationClass()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = 'Wlr';

        $this->setExpectedException('AccountChange_Product_ManagerException', 'Invalid Product configuration class',
                                    AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_CONFIGURATION_CLASS);

        AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);
    }

    /**
     * @covers AccountChange_Product_Manager::factory
     */
    public function testFactoryReturnsServiceDefinitionProductConfigurationObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION;

        $objProductConfiguration = AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);

        $this->assertInstanceOf('AccountChange_Product_ServiceDefinition', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_Manager::factory
     */
    public function testFactoryReturnsWlrProductConfigurationObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_WLR;

        $objProductConfiguration = AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);

        $this->assertInstanceOf('AccountChange_Product_Wlr', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_Manager::factory
     *
     */
    public function testFactoryReturnsInternetConnectionConfigurationObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_INTERNET_CONNECTION;

        $objProductConfiguration = AccountChange_Product_Manager::factory($intAction,
                                                                          $intId,
                                                                          $strProductType,
                                                                          array('intNewServiceDefinitionId' => 123));

        $this->assertInstanceOf('AccountChange_Product_InternetConnection', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_Manager::factory
     *
     */
    public function testFactoryReturnsServiceComponentConfigurationObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_COMPONENT;

        $objProductConfiguration = AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);

        $this->assertInstanceOf('AccountChange_Product_ServiceComponent', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_Manager::factory
     *
     */
    public function testFactoryReturnsFirewallConfigurationObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_FIREWALL;

        $objProductConfiguration = AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);

        $this->assertInstanceOf('AccountChange_Product_Firewall', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_Manager::factory
     *
     */
    public function testFactoryReturnsCommunityConfigurationObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_COMMUNITY;

        $objProductConfiguration = AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);

        $this->assertInstanceOf('AccountChange_Product_Community', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_Manager::factory
     *
     */
    public function testFactoryReturnsMailboxConfigurationObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $intId     = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_MAILBOX;

        $objProductConfiguration = AccountChange_Product_Manager::factory($intAction, $intId, $strProductType);

        $this->assertInstanceOf('AccountChange_Product_Mailbox', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_ManagerException
     * @covers AccountChange_Product_Manager::factoryUsingComponentId
     */
    public function testFactoryUsingComponentIdThrowsComponentTypeIdNotSetException()
    {
        $intAction      = AccountChange_Product_Manager::ACTION_ADD;
        $intComponentId = 1;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_WLR;

        $objAdaptor = $this->getMock('Db_Adaptor', array('getComponentDetails'), array(), '', false);

        Db_Manager::setAdaptor('AccountChange', $objAdaptor);

        $this->setExpectedException('AccountChange_Product_ManagerException', 'Component type Id not set in arrComponentDetails',
                                    AccountChange_Product_ManagerException::ERR_COMPONENT_TYPE_ID_NOT_SET);

        $objProductConfiguration = AccountChange_Product_Manager::factoryUsingComponentId($intAction, $intComponentId, $strProductType);
    }

    /**
     * @covers AccountChange_Product_Manager::factoryUsingComponentId
     */
    public function testFactoryUsingComponentIdPopulatesComponentId()
    {
        $intAction      = AccountChange_Product_Manager::ACTION_ADD;
        $intComponentId = 123;
        $intComponentTypeId = 456;
        $strProductType = AccountChange_Product_Manager::PRODUCT_TYPE_WLR;

        $objAdaptor = $this->getMock('Db_Adaptor', array('getComponentDetails'), array(), '', false);
        $objAdaptor->expects($this->once())
                   ->method('getComponentDetails')
                   ->will($this->returnValue(array('component_type_id' => $intComponentTypeId)));

        Db_Manager::setAdaptor('AccountChange', $objAdaptor);

        $objProductConfiguration = AccountChange_Product_Manager::factoryUsingComponentId($intAction, $intComponentId, $strProductType);

        $this->assertInstanceOf('AccountChange_Product_Configuration', $objProductConfiguration);
        $this->assertAttributeEquals($intComponentId, 'intComponentId', $objProductConfiguration);
    }

    /**
     * @covers AccountChange_Product_Manager::validateAction
     *
     */
    public function testValidateActionReturnsTrueIfSuccessful()
    {
        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Product_Manager', array());

        $this->assertTrue($objManager->protected_validateAction(1));
    }

    /**
     * @covers AccountChange_Product_Manager::validateAction
     *
     */
    public function testValidateActionThrowsExceptionIfActionIsNotValid()
    {
        $this->setExpectedException('AccountChange_Product_ManagerException',
                                    'Invalid action called - aString',
                                    AccountChange_Product_ManagerException::ERR_INVALID_ACTION);

        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Product_Manager', array());

        $this->assertTrue($objManager->protected_validateAction('aString'));
    }

    /**
     * @covers AccountChange_Product_Manager::validateProductId
     *
     */
    public function testValidateProductIdReturnsTrueIfSuccessful()
    {
        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Product_Manager', array());

        $this->assertTrue($objManager->protected_validateProductId(1));
    }

    /**
     * @covers AccountChange_Product_Manager::validateProductId
     *
     */
    public function testValidateProductIdThrowsExceptionIfIdIsNotValidInt()
    {
        $this->setExpectedException('AccountChange_Product_ManagerException',
                                    'Invalid product id provided - aString',
                                    AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_ID);

        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Product_Manager', array());

        $this->assertTrue($objManager->protected_validateProductId('aString'));
    }

    /**
     * @covers AccountChange_Product_Manager::validateProductType
     *
     */
    public function testValidateProductTypeReturnsTrueIfSuccessful()
    {
        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Product_Manager', array());

        $this->assertTrue($objManager->protected_validateProductType('ServiceDefinition'));
    }

    /**
     * @covers AccountChange_Product_Manager::validateProductType
     * @covers AccountChange_Product_ManagerException
     */
    public function testValidateProductTypeThrowsExceptionIfProductTypeIsInValid()
    {
        $this->setExpectedException('AccountChange_Product_ManagerException',
                                    'Invalid product type provided - aString',
                                    AccountChange_Product_ManagerException::ERR_INVALID_PRODUCT_TYPE);

        $objManager = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Product_Manager', array());

        $this->assertTrue($objManager->protected_validateProductType('aString'));
    }

    /**
     * @covers AccountChange_Product_Manager::factoryUsingFamilyObject
     *
     */
    public function testFactoryUsingFamilyObjectWhenMarketIsAnObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $arrOptions = array(
            'intLineCheckId' => 123,
            'intServiceId' => 111,
            'intNewServiceDefinitionId' => 6718
        );

        $dbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getExchangeMarket'),
            array('LineCheck',
            Db_Manager::DEFAULT_TRANSACTION,
            false)
        );

        $marketDetails = array(
            'intOfcomMarket' => 3,
            'intMarketId'    => 12,
            'vchDescription' => 'Market 3 - Price Match 12',
            'vchHandle'      => 'MARKET_3_PM_12'
        );


        $dbAdaptor->expects($this->once())
            ->method('getExchangeMarket')
            ->will($this->returnValue($marketDetails));

        Db_Manager::setAdaptor('LineCheck', $dbAdaptor);

        $productFamilyBpr = $this->getMock(
            'ProductFamily_Bpr09',
            array('getInternetConnectionComponent', 'getLineCheckResult'),
            array(),
            '',
            false
        );

        $productFamilyBpr->expects($this->once())
            ->method('getInternetConnectionComponent')
            ->will($this->returnValue(1));

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getExchangeCode'),
            array(),
            '',
            false
        );

        $lineCheckResult->expects($this->once())
            ->method('getExchangeCode')
            ->will($this->returnValue(1));


        $productFamilyBpr->expects($this->once())
            ->method('getLineCheckResult')
            ->will($this->returnValue($lineCheckResult));

        $result = AccountChange_Product_Manager::factoryUsingFamilyObject($productFamilyBpr, $intAction, $arrOptions);

        $this->assertInstanceOf('AccountChange_Product_InternetConnection', $result);
    }

    /**
     * @covers AccountChange_Product_Manager::factoryUsingFamilyObject
     * @covers AccountChange_Product_Manager::raiseAutoProblem
     *
     */
    public function testFactoryUsingFamilyObjectWhenMarketIsNotAnObject()
    {
        $intAction = AccountChange_Product_Manager::ACTION_ADD;
        $arrOptions = array(
            'intLineCheckId' => 123,
            'intServiceId' => 111,
            'intNewServiceDefinitionId' => 6718
        );

        $productFamilyBpr = $this->getMock(
            'ProductFamily_Bpr09',
            array('getInternetConnectionComponent', 'getMarketFromLineCheckId'),
            array(),
            '',
            false
        );

        $productFamilyBpr->expects($this->once())
            ->method('getInternetConnectionComponent')
            ->will($this->returnValue(1));

        $productFamilyBpr->expects($this->once())
            ->method('getMarketFromLineCheckId')
            ->will($this->returnValue(12));

        $arrActorDetails = array(
            array(
                'intActorId'        => 345,
                'strUsername'       => 'Customer',
                'strRealm'          => '',
                'strUserType'       => '',
                'strExternalUserId' => 6543
            )
        );

        $arrActorData = array (
            'intActorId' => 345,
            'strLocale' => 'EN_GB'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor', 'getActorData', 'getScriptSessionDetails'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $objAuthAdaptor->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($arrActorDetails));

        $objAuthAdaptor->expects($this->any())
            ->method('getActorData')
            ->will($this->returnValue($arrActorData));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $problem = $this->getMock(
            'PomsClient_AutoProblem',
            array('raiseProblem'),
            array(),
            '',
            false
        );

        $problem->expects($this->once())
            ->method('raiseProblem')
            ->will($this->returnValue(9999));

        $client = $this->getMock(
            'AutoProblem_AutoProblemClient',
            array('prepareAutoProblem'),
            array()
        );

        $client->expects($this->once())
            ->method('prepareAutoProblem')
            ->will($this->returnValue($problem));

        BusTier_BusTier::setClient('autoproblem', $client);

        $result = AccountChange_Product_Manager::factoryUsingFamilyObject($productFamilyBpr, $intAction, $arrOptions);

        $this->assertInstanceOf('AccountChange_Product_InternetConnection', $result);
    }
}
