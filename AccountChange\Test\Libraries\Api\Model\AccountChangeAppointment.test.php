<?php

/***
 * @package   AccountChange
 * <AUTHOR> Bhatia
 */

class AccountChange_AccountChangeAppointment_Test extends \PHPUnit_Framework_TestCase
{
    public function testArrayIsReturnedCorrectly()
    {
        $expectedArray = array(
            "notes" => "Test the notes for appointment",
            "live" => array(
                "date" => "10-09-2021",
                "timeSlot" => "AM",
                "ref" => "Andy"
            )
        );
        $appointment = new AccountChange_AccountChangeAppointment($expectedArray);

        $this->assertEquals($expectedArray, $appointment->getAppointmentArray());
    }

    public function testArrayIsReturnedCorrectlyWithManual()
    {
        $expectedArray = array(
            "notes" => "Test the notes for appointment",
            "manual" => array(
                array(
                    "date" => "10-09-2021",
                    "timeSlot" => "AM",
                ),
                array(
                    "date" => "10-09-2021",
                    "timeSlot" => "PM",
                ),
                array(
                    "date" => "11-09-2021",
                    "timeSlot" => "AM",
                )
            )
        );
        $appointment = new AccountChange_AccountChangeAppointment($expectedArray);

        $this->assertEquals($expectedArray, $appointment->getAppointmentArray());
    }
}