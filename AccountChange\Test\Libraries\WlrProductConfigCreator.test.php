<?php

/**
 * File WlrProductConfigCreator.test.php
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Class AccountChange_WlrProductConfigCreator_Test
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_WlrProductConfigCreator_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @covers AccountChange_WlrProductConfigCreator::createWlrConfigurations
     * @covers AccountChange_WlrProductConfigCreator::setTermsAndConditions
     * @covers AccountChange_WlrProductConfigCreator::__construct
     */
    public function testCreateWlrConfigurations()
    {
        $termsAndConditions = $this->getMock(
            'AccountChange_TermsAndConditions',
            array('createWlrConfigurations')
        );

        $termsAndConditions->expects($this->once())
            ->method('createWlrConfigurations')
            ->with(array());

        $configurator = new AccountChange_WlrProductConfigCreator();
        $configurator->setTermsAndConditions($termsAndConditions);

        $this->assertEquals(array(), $configurator->createWlrConfigurations());
    }

    /**
     * @covers AccountChange_WlrProductConfigCreator::setObjCoreService
     * @covers AccountChange_WlrProductConfigCreator::setArrWlrProduct
     * @covers AccountChange_WlrProductConfigCreator::setBolSchedule
     * @covers AccountChange_WlrProductConfigCreator::setObjBusinessActor
     * @covers AccountChange_WlrProductConfigCreator::setIntNewWlrId
     * @covers AccountChange_WlrProductConfigCreator::setObjLineCheckResults
     * @covers AccountChange_WlrProductConfigCreator::setBolContractResetWlr
     * @covers AccountChange_WlrProductConfigCreator::setStrWlrContext
     * @covers AccountChange_WlrProductConfigCreator::setIntOldSdi
     * @covers AccountChange_WlrProductConfigCreator::setIntNewSdi
     * @covers AccountChange_WlrProductConfigCreator::getApplicationStateVar
     *
     */
    public function testGetApplicationStateVar()
    {
        $callingClass = 'AccountChange_TermsAndConditions';
        $configurator = new AccountChange_WlrProductConfigCreator();

        $service = new \Core_Service();
        $businessActor = new \Auth_BusinessActor();
        $linecheckResult = new \LineCheck_Result();
        $wlrProduct = array('');
        $wlrId = 999;
        $oldSdi = 6865;
        $newSdi = 6868;
        $houseMove = true;

        $configurator->setObjCoreService($service);
        $configurator->setArrWlrProduct($wlrProduct);
        $configurator->setBolSchedule(true);
        $configurator->setObjBusinessActor($businessActor);
        $configurator->setIntNewWlrId($wlrId);
        $configurator->setObjLineCheckResults($linecheckResult);
        $configurator->setBolContractResetWlr(true);
        $configurator->setStrWlrContext(false);
        $configurator->setIntOldSdi($oldSdi);
        $configurator->setIntNewSdi($newSdi);
        $configurator->setIsHouseMove(true);

        $this->assertEquals($service, $configurator->getApplicationStateVar($callingClass, 'objCoreService'));
        $this->assertEquals($wlrProduct, $configurator->getApplicationStateVar($callingClass, 'arrWlrProduct'));
        $this->assertEquals(true, $configurator->getApplicationStateVar($callingClass, 'bolSchedule'));
        $this->assertEquals($businessActor, $configurator->getApplicationStateVar($callingClass, 'objBusinessActor'));
        $this->assertEquals($wlrId, $configurator->getApplicationStateVar($callingClass, 'intNewWlrId'));
        $this->assertEquals($linecheckResult, $configurator->getApplicationStateVar($callingClass, 'objLineCheckResult'));
        $this->assertEquals(true, $configurator->getApplicationStateVar($callingClass, 'bolContractResetWlr'));
        $this->assertEquals(false, $configurator->getApplicationStateVar($callingClass, 'strWlrContext'));
        $this->assertEquals($oldSdi, $configurator->getApplicationStateVar($callingClass, 'intOldSdi'));
        $this->assertEquals($newSdi, $configurator->getApplicationStateVar($callingClass, 'intNewSdi'));
        $this->assertEquals(true, $configurator->getApplicationStateVar($callingClass, 'bolHousemove'));
        $this->assertEquals(null, $configurator->getApplicationStateVar('Test', 'Test'));
    }

    /**
     * @covers AccountChange_WlrProductConfigCreator::isApplicationStateVar
     * @covers AccountChange_WlrProductConfigCreator::getApplicationStateVar
     */
    public function testIsApplicationStateVar()
    {
        $callingClass = 'AccountChange_TermsAndConditions';
        $configurator = new AccountChange_WlrProductConfigCreator();

        $service = new \Core_Service();

        $configurator->setObjCoreService($service);
        $configurator->setBolSchedule(false);
        $configurator->setBolContractResetWlr(true);

        $this->assertEquals(true, $configurator->isApplicationStateVar($callingClass, 'objCoreService'));
        $this->assertEquals(true, $configurator->isApplicationStateVar($callingClass, 'bolSchedule'));
        $this->assertEquals(true, $configurator->isApplicationStateVar($callingClass, 'bolContractResetWlr'));
        $this->assertEquals(false, $configurator->isApplicationStateVar($callingClass, 'Test'));
        $this->assertEquals(false, $configurator->isApplicationStateVar('Test', 'Test'));
    }
}