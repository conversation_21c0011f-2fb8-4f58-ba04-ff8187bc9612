<?php
/**
 * Class to control which features are currently switched on and off
 *
 * Used to allow features to be developed and then turned on when required.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2013 PlusNet
 * @link      link
 * @since     File available since 2013-02-19
 */

/**
 * Used to allow features to be developed and then turned on when required.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 PlusNet
 * @link      link
 */
class AccountChange_FeatureToggle
{
    /**
     * Handle for auto problem template handle.
     *
     * @var string
     */
    const BPR12_ENABLED = false;

    /**
     * @return bool
     */
    public static function isC2fToggleSet()
    {
        return true;
//        return \Plusnet\Feature\Toggle::isOn('copperToFibre');
    }
}
