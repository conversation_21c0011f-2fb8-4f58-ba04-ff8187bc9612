<?php
use \Plusnet\C2mApiClient\Entity\Promotion;

class BumpPromotionToTopTest extends PHPUnit_Framework_TestCase
{

    /**
     * @test
     */
    public function itSuccessfullyAddsThePromotionToTheTopOfTheArray()
    {
        $promotionToBump = new Promotion();
        $promotionToBump->setCode('TopPromo');

        $promotion1 = new Promotion();
        $promotion1->setCode('Promo1');
        $promotion2 = new Promotion();
        $promotion2->setCode('Promo2');
        $promotion3 = new Promotion();
        $promotion3->setCode('Promo3');

        $promotions = [$promotion1, $promotion2, $promotion3];

        $promotions = (new BumpPromotionToTop($promotionToBump))->handle(
          $promotions
        );

        $this->assertEquals(4, count($promotions));
        $this->assertEquals('TopPromo', $promotions[0]->getCode());
        $this->assertEquals('Promo1', $promotions[1]->getCode());
        $this->assertEquals('Promo2', $promotions[2]->getCode());
        $this->assertEquals('Promo3', $promotions[3]->getCode());

    }
}
