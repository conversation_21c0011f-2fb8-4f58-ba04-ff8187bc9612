<?php
/**
 * Created by IntelliJ IDEA.
 * User: mledger1
 * Date: 13/12/2018
 * Time: 11:48
 */

require_once(__DIR__.'/../../Libraries/MapProduct.php');
require_once(__DIR__.'/../../Libraries/ServiceDefinitionProductOfferingNameMapper.php');


class ServiceDefinitionProductOfferingNameMapperTest extends PHPUnit_Framework_TestCase
{

    /**
     * @test
     */
    public function itReturnsOriginalProductsArrayUntouchedIfIntSdiKeyDoesntExistForAnyOfTheProductsAndDoesntCallgetProductOfferingsByServiceIDs(
    )
    {
        $products = [
          ['Name' => 'Fibre'],
          ['Name' => 'Fibre Extra'],
        ];

        $mockSDIProductMapper = $this->getMockBuilder(
          ServiceDefinitionProductOfferingNameMapper::class
        )->setMethods(['getProductOfferingsByServiceIDs'])
          ->getMock();

        $mockSDIProductMapper->expects($this->never())
          ->method('getProductOfferingsByServiceIDs');

        $returnedProducts = $mockSDIProductMapper->mapTo($products);

        $this->assertEquals(2, count($returnedProducts));

        $this->assertEquals(count(array_keys($returnedProducts[0])), 1);
        $this->assertArrayHasKey('Name', $returnedProducts[0]);
        $this->assertEquals("Fibre", $returnedProducts[0]['Name']);

        $this->assertEquals(count(array_keys($returnedProducts[1])), 1);
        $this->assertArrayHasKey('Name', $returnedProducts[0]);
        $this->assertEquals("Fibre Extra", $returnedProducts[1]['Name']);
    }

    /**
     * @test
     */
    public function itAddsTheProductOfferingMappingToOnlyTheProductsWithAnIntSdi(
    )
    {
        $products = [
          [
            'Name'   => 'Fibre',
            'intSdi' => '00006565',
          ],
          [
            'Name' => 'Fibre Extra',
          ],
        ];

        $mockSDIProductMapper = $this->getMockBuilder(
          ServiceDefinitionProductOfferingNameMapper::class
        )->setMethods(['getProductOfferingsByServiceIDs'])
          ->getMock();

        $mockSDIProductMapper->expects($this->once())
          ->method('getProductOfferingsByServiceIDs')
          ->will(
            $this->returnValue(
              [
                [
                  'intSdi'              => '00006565',
                  'productOfferingName' => 'Unlimited Fibre',
                ],
              ]
            )
          );

        $returnedProducts = $mockSDIProductMapper->mapTo($products);

        $this->assertEquals(
          'Unlimited Fibre',
          $returnedProducts[0]['productOfferingName']
        );

        $this->assertArrayNotHasKey(
          'productOfferingName',
          $returnedProducts[1]
        );

    }

}
