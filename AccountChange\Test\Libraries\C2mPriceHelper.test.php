<?php
use \Plusnet\C2mApiClient\Entity\PriceIncreaseData;
use \Plusnet\C2mApiClient\C2MClient;

require_once(__DIR__.'/../../Libraries/C2mPriceHelper.php');

class C2mPriceHelperTest extends PHPUnit_Framework_TestCase
{
    public function testGetPriceIncreaseDataShouldReturnData()
    {
        $mockedResponse = new PriceIncreaseData(1.5, 4.5);
        $mockC2MClient = $this->getMockBuilder('Plusnet\C2mApiClient\V5\Client')
            ->disableOriginalConstructor()
            ->setMethods(array('getPriceIncreaseData'))
            ->getMock();
        BusTier_BusTier::setClient('c2mapi.v5', $mockC2MClient);
        $mockC2MClient
            ->method('getPriceIncreaseData')
            ->will($this->returnValue($mockedResponse));
        $expectedResult = 4.5;
        $this->assertEquals(AccountChange_C2mPriceHelper::getPriceIncreasePercent('johnlewis', '2021'), $expectedResult);
    }

    /**
     * @testGetPriceIncreaseDataShouldReturnData
     */
    public function testGetPriceIncreaseDataIsGreaterThanOrEqualToZero()
    {
        $actualResponse = AccountChange_C2mPriceHelper::getPriceIncreasePercent('johnlewis', '2021');
        $this->assertTrue($actualResponse >= 0);
    }

    /*
     * @testGetPriceIncreaseDataShouldreturnFalse
     * */
    public function testGetPriceIncreaseDataShouldreturnFalse()
    {
        $actualResponse = AccountChange_C2mPriceHelper::getPriceIncreasePercent('johnlewis', '1081');
        $this->assertFalse($actualResponse);
    }

}