<?php
/**
 * Action_IdentifyAction
 *
 * Testing class for the AccountChange_Action_IdentifyAction class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 PlusNet
 * @link       http://www.plus.net/
 */
/**
 * Action Appointment Test Class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 PlusNet
 * @link       http://www.plus.net/
 */
class AccountChange_Action_IdentifyAction_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for service id
     *
     * @var int
     */
    private $serviceId = 123;

    /**
     *
     * Test the constructor
     *
     * @covers AccountChange_Action_IdentifyAction::__construct
     *
     * @return void
     */
    public function testConstructor()
    {
        $identifyAction = new AccountChange_Action_IdentifyAction($this->serviceId);

        // AccountChange_Action_IdentifyAction stored the SID in $serviceId and its parent
        // AccountChange_Action store the SID in $intServiceId. I am not changing the code at
        // the moment as it works fine for now.
        $this->assertEquals($identifyAction->getServiceId(), $this->serviceId);
    }

    /**
     *
     * Test execute
     *
     * @covers AccountChange_Action_IdentifyAction::execute
     *
     * @return void
     */
    public function testExecute()
    {
        $identifyAction = new AccountChange_Action_IdentifyAction(123);
        $this->assertNull($identifyAction->execute());
    }

    /**
     * Test the function getActionRequired for no action required scenario
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     *
     * @return void
     */
    public function testGetActionRequiredFalse()
    {
        $currentPlatform = array(
            'vchSupplierPlatform'  => 'BT21CN',
            'vchProductCode'       => 'WBC MAX8 mb/s (ADSL1)',
            'intSupplierProductID' => 1
        );

        $supplierProductCode = new String('WBC MAX8 mb/s (ADSL1)');
        $supplierPlatform = new String('BTIPS');

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierPlatform',
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue(self::getAdsl2SupplierProduct($supplierProductCode)));

        $identifyAction->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertFalse($action);
        unset($identifyAction);
    }

    /**
     * Test the function getActionRequired for a modify action required when
     * the supplier platform has not changed but the supplier product had
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     *
     * @return void
     */
    public function testGetActionRequiredModifySupplierProduct()
    {
        $currentPlatform = array(
            'vchSupplierPlatform'  => 'BT21CN',
            'vchProductCode'       => 'WBC MAX8 mb/s (ADSL1)',
            'intSupplierProductID' => 18
        );

        $supplierProductCode = new String('WBC End User Access (EUA)');
        $supplierPlatform = new String('BT21CN');

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierPlatform',
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue(self::getAdsl2SupplierProduct($supplierProductCode)));

        $identifyAction->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertEquals($action, 'Modify');
        unset($identifyAction);
    }

    /**
     * Test the function getActionRequired,
     * 20CN to 21CN, providePfm Scenario
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     *
     * @return void
     */
    public function testGetActionRequiredProvidePfm()
    {
        $currentPlatform = array(
            'vchSupplierPlatform' => 'BTIPS',
            'vchProductCode'      => 'BT IPStream Home 500 BT Install'
        );

        $supplierProductCode = new String('WBC End User Access (EUA)');
        $supplierPlatform = new String('BT21CN');

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierPlatform'
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue(self::getAdsl2SupplierProduct($supplierProductCode)));

        $identifyAction->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertEquals('ProvidePFM', $action);
    }

    /**
     * Test the function getActionRequired,
     * 21CN to 21CN,
     * ie Adsl1 over 21CN to Adsl2 over 21CN,
     *    Adsl2 over 21CN to Fibre,
     *    Adsl1 over 21CN to Fibre
     * placeModify Scenario
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     * @covers AccountChange_Action_IdentifyAction::isModifyOrderRequired
     *
     * @return void
     */
    public function testGetActionRequiredPlaceModify()
    {
        $currentPlatform = array(
            'vchSupplierPlatform' => 'BT21CN',
            'vchProductCode'      => 'WBC End User Access (EUA)',
            'intSupplierProductID'=> 166,
            'intMaxDownstream'    => 40000,
            'vchMaxUpstream'      => '20Mb/s'
        );

        $newSupplierProduct = new Product_SupplierProduct(
            new Int(167),
            new Int(2),
            new String('name'),
            new String('FTTC'),
            new Int(20000),
            new Int(40000),
            new Int(38000),
            new String('Standard'),
            new String('Auto'),
            new String('20Mb/s'),
            new String(''),
            new String('')
        );

        $supplierPlatform = new String('BT21CN');

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierPlatform'
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue($newSupplierProduct));

        $identifyAction->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertEquals('Modify', $action);
    }

    /**
     * Test the function getActionRequired,
     * FTTC to FTTC,
     * ie Fibre to Fibre with different supplier products
     * placeModify Scenario
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     * @covers AccountChange_Action_IdentifyAction::isModifyOrderRequired
     *
     * @return void
     */
    public function testGetActionRequiredPlaceModifyForFTTC()
    {
        $currentPlatform = array(
            'vchSupplierPlatform' => 'BT21CN',
            'vchProductCode'      => 'FTTC',
            'intSupplierProductID'=> 166,
            'intMaxDownstream'    => 40000,
            'vchMaxUpstream'      => '20Mb/s'
        );

        $newSupplierProduct = new Product_SupplierProduct(
            new Int(167),
            new Int(2),
            new String('name'),
            new String('FTTC'),
            new Int(20000),
            new Int(80000),
            new Int(78000),
            new String('Standard'),
            new String('Auto'),
            new String('20Mb/s'),
            new String(''),
            new String('')
        );

        $supplierPlatform = new String('BT21CN');

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierPlatform'
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue($newSupplierProduct));

        $identifyAction->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertEquals('Modify', $action);
    }

    /**
     * Test the function getActionRequired,
     * FTTC to FTTC,
     * ie Fibre to Fibre with different supplier product Ids but features the same
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     * @covers AccountChange_Action_IdentifyAction::isModifyOrderRequired
     *
     * @return void
     */
    public function testGetActionRequiredReturnsFalseForDifferentSupplierProductIdsButSameSupplierFeatures()
    {
        $currentPlatform = array(
            'vchSupplierPlatform' => 'BT21CN',
            'vchProductCode'      => 'FTTC',
            'intSupplierProductID'=> 166,
            'intMaxDownstream'    => 80000,
            'vchMaxUpstream'      => '20Mb/s'
        );

        $newSupplierProduct = new Product_SupplierProduct(
            new Int(167),
            new Int(2),
            new String('name'),
            new String('FTTC'),
            new Int(20000),
            new Int(80000),
            new Int(78000),
            new String('Standard'),
            new String('Auto'),
            new String('20Mb/s'),
            new String(''),
            new String('')
        );

        $supplierPlatform = new String('BT21CN');

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierPlatform'
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue($newSupplierProduct));

        $identifyAction->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertFalse($action);
    }

    /**
     * Test the function getActionRequired,
     * FTTC to FTTC with same supplier product
     * No order should be placed
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     *
     * @return void
     */
    public function testGetActionRequiredNoOrderForFTTC()
    {
        $currentPlatform = array(
            'vchSupplierPlatform' => 'BT21CN',
            'vchProductCode'      => 'FTTC',
            'intSupplierProductID'=> 1
        );

        $supplierProductCode = new String('FTTC');
        $supplierPlatform = new String('BT21CN');

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierPlatform'
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue(self::getAdsl2SupplierProduct($supplierProductCode)));

        $identifyAction->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertEquals(false, $action);
    }


    /**
     * Test the function getActionRequired,
     * FTTC to FTTC with same supplier product
     * No order should be placed
     *
     * @covers AccountChange_Action_IdentifyAction::getActionRequired
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl2
     * @covers AccountChange_Action_IdentifyAction::includeLegacyFiles
     * @covers AccountChange_Action_IdentifyAction::adslGetProvisionedService
     * @covers AccountChange_Action_IdentifyAction::isProvisionedOnAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isAdsl1Over21Cn
     * @covers AccountChange_Action_IdentifyAction::isFttc
     * @covers AccountChange_Action_IdentifyAction::isAdsl2
     * @covers AccountChange_Action_IdentifyAction::getSupplierPlatform
     *
     * @return void
     */
    public function testGetSupplierPlatform()
    {
        $currentPlatform = array(
            'vchSupplierPlatform' => 'BT21CN',
            'vchProductCode'      => 'FTTC',
            'intSupplierProductID'=> 1
        );

        $supplierProductCode = new String('FTTC');
        $supplierPlatform = new String('BT21CN');

        $supplierProductRules = $this->getMock(
            'Product_SupplierProductRules',
            array('getSupplierPlatform'),
            array(),
            '',
            false
        );

        $supplierProductRules->expects($this->once())
            ->method('getSupplierPlatform')
            ->will($this->returnValue(self::getSupplierPlatform($supplierPlatform)));

        $identifyAction =  $this->getMock(
            'AccountChange_Action_IdentifyAction',
            array(
                'includeLegacyFiles',
                'adslGetProvisionedService',
                'getSupplierProduct',
                'getSupplierProductRules'
            ),
            array($this->serviceId)
        );

        $identifyAction->expects($this->exactly(3))
            ->method('includeLegacyFiles');

        $identifyAction->expects($this->exactly(4))
            ->method('adslGetProvisionedService')
            ->will($this->returnValue($currentPlatform));

        $identifyAction->expects($this->exactly(3))
            ->method('getSupplierProduct')
            ->will($this->returnValue(self::getAdsl2SupplierProduct($supplierProductCode)));

        $identifyAction->expects($this->once())
            ->method('getSupplierProductRules')
            ->will($this->returnValue($supplierProductRules)); //self::getSupplierPlatform($supplierPlatform)));

        $action = $identifyAction->getActionRequired();
        $this->assertEquals(false, $action);
    }
    /**
     * Get the Supplier Product
     *
     * @param int $supplierProductCode Supplier product id
     *
     * @return Product_SupplierProduct
     */
    public static function getAdsl2SupplierProduct($supplierProductCode)
    {
        return new Product_SupplierProduct(
            new Int(1),
            new Int(2),
            new String('name'),
            $supplierProductCode,
            new Int(3),
            new Int(4),
            new Int(5),
            new String('Standard'),
            new String('Auto'),
            new String(''),
            new String(''),
            new String('')
        );
    }

    /**
     * Get the supplier platform
     *
     * @param string $supplierPlatform Supplier platform
     *
     * @return Product_SupplierPlatform
     */
    public static function getSupplierPlatform($supplierPlatform)
    {
        $supplier = new Product_Supplier(
            new Int(1),
            new String('BT'),
            new String('BT')
        );

        return new Product_SupplierPlatform(
            new Int(1),
            $supplier,
            $supplierPlatform,
            new String('BT IPStream')
        );
    }

    /**
     * @covers AccountChange_Action_IdentifyAction::isOrderPlacementRequiredForAction
     *
     * @param $actionName
     * @param $isOrderPlacementRequired
     *
     * @dataProvider provideDataForIsOrderPlacementRequiredForActionTest
     */
    public function testIsOrderPlacementRequiredForAction($actionName, $isOrderPlacementRequired)
    {
        $identifyAction = new AccountChange_Action_IdentifyAction($this->serviceId);

        $this->assertEquals($isOrderPlacementRequired, $identifyAction->isOrderPlacementRequiredForAction($actionName));
    }

    public function provideDataForIsOrderPlacementRequiredForActionTest()
    {
        return array(
            array('Modify', true),
            array('ProvidePFM', true),
            array('UpdateSupplierProduct', false),
            array('Invalid', false)
        );
    }
}
