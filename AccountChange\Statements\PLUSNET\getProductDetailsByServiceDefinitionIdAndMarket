server: coredb
role: slave
rows: single
statement:

SELECT
    sd.name,
    sd.service_definition_id,
    t.intCostIncVatPence/100 AS minimum_charge,
    ap.intMaximumSpeed,
    ap.bolCapped,
    cl.vchHandle AS vchContract,
    cl.vchDisplayName,
    sd.blurb,
    t.intTariffID,
    td.intCostIncVatPence/100 AS decLeadPrice,
    td.intStartMonth,
    td.intEndMonth,
    pp.vchDescription,
    provProfile.vchName AS provisioningProfile,
    ap.intMaxUploadSpeed,
    pfm.vchHandle as vchProductFamily,
    IF(sd.signup_via_portal = 'Y', 1, 0) AS signup_via_portal,
    scp.intServiceComponentId
FROM
    products.service_definitions AS sd
LEFT JOIN products.adsl_product AS ap
    ON sd.service_definition_id = ap.service_definition_id
LEFT JOIN products.tblProvisioningProfile AS provProfile
    ON provProfile.intProvisioningProfileID = ap.intProvisioningProfileID
LEFT JOIN products.service_component_config scc
    ON sd.service_definition_id = scc.service_definition_id
LEFT JOIN products.service_components sc
    ON (sc.service_component_id = scc.service_component_id)
LEFT JOIN products.tblServiceComponentProduct scp
    ON sc.service_component_id = scp.intServiceComponentId
LEFT JOIN products.tblServiceComponentProductType scpt
    USING (intServiceComponentProductTypeID)
LEFT JOIN dbProductComponents.tblProductComponentConfig pcc
    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
LEFT JOIN dbProductComponents.tblTariff t
    USING (intProductComponentConfigID)
LEFT JOIN dbProductComponents.tblContractLength cl
    USING (intContractLengthID)
LEFT JOIN dbProductComponents.tblPaymentFrequency pf
    ON (pf.intPaymentFrequencyID = t.intPaymentFrequencyID)
LEFT JOIN dbProductComponents.tblTariffDeviation td
    ON (t.intTariffID = td.intTariffId)
LEFT JOIN products.tblServiceComponentMarket scm
    ON scm.intServiceComponentId = sc.service_component_id
LEFT JOIN
    dbProductComponents.tblTariffPricePlan tpp ON tpp.intTariffId = t.intTariffID
LEFT JOIN
    dbProductComponents.tblPricePlan pp ON pp.intPricePlanId = tpp.intPricePlanId
LEFT JOIN
    products.tblProductVariant pv ON pv.intProductVariantId = sd.intProductVariantId
LEFT JOIN
    products.tblProductFamily pfm ON pfm.intProductFamilyId = pv.intProductFamilyId
WHERE
    scpt.vchHandle = 'INTERNET_CONNECTION'
    AND (t.dtmEnd IS NULL OR t.dtmEnd > NOW())
    AND pf.vchHandle = 'MONTHLY'
    AND sd.service_definition_id = :serviceDefinitionId
    AND (scm.intMarketId = :marketId OR scm.intMarketId IS NULL)
