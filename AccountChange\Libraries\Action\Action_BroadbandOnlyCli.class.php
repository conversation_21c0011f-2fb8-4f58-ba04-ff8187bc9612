<?php
/**
 * Remove CLI if account change from DSL -> BBO product
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_Action_BroadbandOnlyCli extends AccountChange_Action
{
    /**
     * @var int
     */
    private $serviceId;

    /**
     * @var int
     */
    private $oldServiceDefinitionId;

    /**
     * @var int
     */
    private $newServiceDefinitionId;

    /**
     * @param int   $intServiceId service id
     * @param array $arrOptions   account change options
     */
    public function __construct($intServiceId, array $arrOptions = array())
    {
        parent::__construct($intServiceId, $arrOptions);
        $registry = AccountChange_Registry::instance();
        $this->serviceId = $intServiceId;
        $this->oldServiceDefinitionId = $registry->getEntry('intOldServiceDefinitionId');
        $this->newServiceDefinitionId = $registry->getEntry('intNewServiceDefinitionId');
    }

    /**
     * @return void
     */
    public function execute()
    {
        if ($this->movingToBroadbandOnlyProduct()) {
            $db = Db_Manager::getAdaptor('AccountChange');
            $db->removeCli($this->serviceId);
            $db->deactivatePrimaryCliRecords($this->serviceId);
        }
    }

    /**
     * @return bool
     */
    private function movingToBroadbandOnlyProduct()
    {
        $broadbandOnlyHelper = $this->getBroadbandOnlyHelper();
        if (!$broadbandOnlyHelper->isBroadbandOnlyProduct($this->oldServiceDefinitionId)) {
            return $broadbandOnlyHelper->isBroadbandOnlyProduct($this->newServiceDefinitionId);
        }

        return false;
    }

    /**
     * @return AccountChange_BroadandOnlyHelper
     */
    protected function getBroadbandOnlyHelper()
    {
        return new AccountChange_BroadbandOnlyHelper();
    }
}
