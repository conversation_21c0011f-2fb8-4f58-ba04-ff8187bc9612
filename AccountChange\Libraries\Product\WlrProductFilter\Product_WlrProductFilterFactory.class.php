<?php

/**
 * <AUTHOR>
 */

class AccountChange_Product_WlrProductFilterFactory
{
    /**
     * @param boolean $isPlusnet  is a plusnet account
     * @param boolean $isBusiness whether the account is business
     * @return AccountChange_Product_ProductFilter
     */
    public static function getFilter($isPlusnet, $isBusiness)
    {
        if ($isPlusnet && $isBusiness) {
            return new AccountChange_Product_WlrProductFilterPlusnetBusiness(
                new AccountChange_Product_WlrProductFilter()
            );
        }

        return new AccountChange_Product_WlrProductFilterDefault();
    }
}
