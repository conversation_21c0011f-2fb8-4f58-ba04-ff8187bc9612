<?php
/**
 * Account Change Select Broadband Workplace Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
/**
 * Account Change Select Broadband Workplace Test
 *
 * Testing class for AccountChange_SelectBroadbandWorkplace requirement
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_SelectBroadbandRequirement_Test extends PHPUnit_Framework_TestCase
{
    const MOCK_SERVICE_ID = '********';
    // These are the types given in the application. We must type cast for int and check this in the tests.
    const MOCK_OLD_SERVICE_DEFINITION_ID = '1234';
    const MOCK_NEW_SERVICE_DEFINITION_ID = 5678;

    /* @var AccountChange_SelectBroadbandRequirement $selectBroadband */
    private $selectBroadband;

    /**
     * PHPUnit tear down method
     *
     * @return void
     */
    protected function tearDown()
    {
        Db_Manager::restoreAdaptor('Auth');
        Db_Manager::restoreAdaptor('HardwareClient');
        Db_Manager::restoreAdaptor('AccountChange');
        Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
        BusTier_BusTier::reset();

        unset($this->selectBroadband);
    }

    /**
     * Test getFinancialErrors
     *
     * @covers AccountChange_SelectBroadbandRequirement::getFinancialErrors
     *
     * @return void
     */
    public function testGetFinancialErrors()
    {
        //errors
        $arrExpected = array('error'=>'This is a test error');

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('get_financial_calculate_refund'),
            array(),
            '',
            false
        );

        $objSelectBroadband->expects($this->once())
            ->method('get_financial_calculate_refund')
            ->will($this->returnValue($arrExpected));

        $arrReturned = $objSelectBroadband->getFinancialErrors(0, 0, 0);

        $this->assertEquals($arrExpected, $arrReturned);

        //no errors
        $arrExpected = array();

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('get_financial_calculate_refund'),
            array(),
            '',
            false
        );

        $objSelectBroadband->expects($this->once())
            ->method('get_financial_calculate_refund')
            ->will($this->returnValue($arrExpected));

        $arrReturned = $objSelectBroadband->getFinancialErrors(0, 0, 0);

        $this->assertEquals($arrExpected, $arrReturned);
    }

    /**
     * Test getNewReqs returns empty array when Wlr not allowed
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     *
     * @return void
     */
    public function testGetNewReqsReturnsEmptyArrayWhenWlrNotAllowed()
    {
        $intNewSdi = 0;

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi', 'isHardwareRequirementNeeded')
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()} and
        // {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadbandWorkplace::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue(true));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(true));

        // {@link AccountChange_Controller::isBundle()) is a static method but in current circumstances
        // when run as it is it will always return false, so we don't bother to mock it.
        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(array('AccountChange_Hardware'), $arrNewReqs);
    }

    /**
     * Test getNewReqs returns empty array when we're on business solus
     *  - i.e being on business solus should skip the add phone page.
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     *
     * @return void
     */
    public function testGetNewReqsSkipsPhoneForBusinessSolus()
    {
        $intNewSdi = 6999;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_STAFF'
        );
        $arrWlrProduct['bolWlrChangeAllowed'] = true;
        $arrWlrProduct['bolWlrAddAllowed'] = false;

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = true;

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi', 'isHardwareRequirementNeeded', 'isEngineerAppointmentNeeded')
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()} and
        // {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar', 'isEngineerAppointmentNeeded')
        );

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('isSolus'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue(true));

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadbandWorkplace::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));

        $objMockController->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolHousemove'))
            ->will($this->returnValue(false));

        // Set callback object within the requirement itself to application controller

        $objMockController->expects($this->at(6))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('selectedProductFamily'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(7))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('selectedProductFamily'))
            ->will($this->returnValue($mockFamily));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(array(), $arrNewReqs);
    }

    /**
     * Test getNewReqs returns a workplace requirement when Wlr change allowed
     * and you are workplace user
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     *
     * @return void
     */
    public function testGetNewReqsReturnsAWorkplaceRequirementWhenWlrChangeAllowedAndYouAreWorkplaceUser()
    {
        $intOldSdi = 666;
        $intNewSdi = 1;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_STAFF'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = true;
        $arrWlrProduct['bolWlrAddAllowed'] = false;

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi', 'isHardwareRequirementNeeded')
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()} and
        // {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadband::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue($intOldSdi));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(false));

        // {@link AccountChange_Controller::isBundle()) is a static method but in current circumstances
        // when run as it is it will always return false, so we don't bother to mock it.

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(array('AccountChange_SelectHomephoneWorkplace'), $arrNewReqs);
    }

    /**
     * Test getNewReqs returns a workplace requirement when Wlr change allowed
     * and you are portal user
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     *
     * @return void
     */
    public function testGetNewReqsReturnsAWorkplaceRequirementWhenWlrChangeAllowedAndYouArePortalUser()
    {
        $intOldSdi = 666;
        $intNewSdi = 1;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_USER'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = true;
        $arrWlrProduct['bolWlrAddAllowed'] = false;

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi', 'isHardwareRequirementNeeded')
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()} and
        // {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadband::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue($intOldSdi));
        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));
        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));
        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(false));

        // {@link AccountChange_Controller::isBundle()) is a static method but in current circumstances
        // when run as it is it will always return false, so we don't bother to mock it.

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(array('AccountChange_SelectHomephone'), $arrNewReqs);
    }

    /**
     * Test getNewReqs returns a workplace requirement when Wlr adding allowed
     * and you are workplace user
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     *
     * @return void
     */
    public function testGetNewReqsReturnsAWorkplaceRequirementWhenWlrAddingAllowedAndYouAreWorkplaceUser()
    {
        $intOldSdi = 666;
        $intNewSdi = 1;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_STAFF'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION,
            false)
        );
        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = false;
        $arrWlrProduct['bolWlrAddAllowed'] = true;

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi', 'isHardwareRequirementNeeded')
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()} and
        // {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadband::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue($intOldSdi));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(false));

        // {@link AccountChange_Controller::isBundle()) is a static method but in current circumstances
        // when run as it is it will always return false, so we don't bother to mock it.

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(array('AccountChange_SelectHomephoneWorkplace'), $arrNewReqs);
    }

    /**
     * Test getNewReqs returns a workplace requirement when Wlr adding allowed
     * and you are portal user
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     *
     * @return void
     */
    public function testGetNewReqsReturnsAWorkplaceRequirementWhenWlrAddingAllowedAndYouArePortalUser()
    {
        $intOldSdi = 666;
        $intNewSdi = 1;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_USER'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION,
            false)
        );

        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = false;
        $arrWlrProduct['bolWlrAddAllowed'] = true;

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi', 'isHardwareRequirementNeeded')
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()} and
        // {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadband::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue($intOldSdi));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(false));

        // {@link AccountChange_Controller::isBundle()) is a static method but in current circumstances
        // when run as it is it will always return false, so we don't bother to mock it.

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(array('AccountChange_SelectHomephone'), $arrNewReqs);
    }

    /**
     * Test getNewReqs returns a hardware requirement when it is required
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     * @covers AccountChange_SelectBroadbandRequirement::isEngineerAppointmentNeeded
     *
     * @return void
     */
    public function testGetNewReqsReturnsAHardwareRequirementWhenItIsRequired()
    {
        $intOldSdi = 666;
        $intNewSdi = 1;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_USER'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION,
            false)
        );

        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = false;
        $arrWlrProduct['bolWlrAddAllowed'] = true;

        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi', 'isHardwareRequirementNeeded')
        );

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()}
        // and {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadband::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue($intOldSdi));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(false));

        // {@link AccountChange_Controller::isBundle()) is a static method but in current circumstances
        // when run as it is it will always return false, so we don't bother to mock it.

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(array('AccountChange_SelectHomephone'), $arrNewReqs);
    }

    /**
     * Test getNewReqs does not show engineer page for instant account change
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     * @covers AccountChange_SelectBroadbandRequirement::isEngineerAppointmentNeeded
     *
     * @return void
     */
    public function testGetNewReqsDoesNotShowEngineerPageForInstantAccChange()
    {
        $intOldSdi = 666;
        $intNewSdi = 1;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_USER'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION,
            false)
        );

        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = false;
        $arrWlrProduct['bolWlrAddAllowed'] = true;

        $objSelectBroadband
            = $this->getMock(
                'AccountChange_SelectBroadbandWorkplace',
                array('getNewSdi', 'isHardwareRequirementNeeded')
            );

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('isSolus'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue(false));

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()}
        // and {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar', 'getApplicationStateVar'));

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadband::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue($intOldSdi));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));
        $objMockController->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolHousemove'))
            ->will($this->returnValue(false));

        $objMockController->expects($this->at(6))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('selectedProductFamily'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(7))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('selectedProductFamily'))
            ->will($this->returnValue($mockFamily));

        $objMockController->expects($this->at(8))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(9))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrSelectedBroadband'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(10))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('currentSupplierProduct'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(11))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolPortal'))
            ->will($this->returnValue(false));

        $objMockController->expects($this->at(12))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolSchedule'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(13))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolSchedule'))
            ->will($this->returnValue(false));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(false));

        // {@link AccountChange_Controller::isBundle()) is a static method but in current circumstances
        // when run as it is it will always return false, so we don't bother to mock it.

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(
            array('AccountChange_SelectHomephone'),
            $arrNewReqs
        );
    }

    /**
     * Test getNewReqs doesn't return the phone view when a business solus product
     * has been selected.
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     * @covers AccountChange_SelectBroadbandRequirement::isEngineerAppointmentNeeded
     *
     * @return void
     */
    public function testGetNewReqsSkipsPhonePageWhenOnBusSolus()
    {
        $intOldSdi = 666;
        $intNewSdi = 1;
        $intActorId = 1;
        $arrActor = array(
            'intActorId' => $intActorId,
            'strUserType' => 'PLUSNET_USER'
        );

        $objAuthAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION,
            false)
        );

        $objAuthAdaptor->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue(array($arrActor)));

        Db_Manager::setAdaptor('Auth', $objAuthAdaptor);

        $objBusinessActor = Auth_BusinessActor::get($intActorId);

        $arrWlrProduct['bolWlrChangeAllowed'] = false;
        $arrWlrProduct['bolWlrAddAllowed'] = true;

        $currentSupplierProduct = $this->getMockBuilder('Product_SupplierProduct')
            ->setMethods(array('getProductCode'))
            ->disableOriginalConstructor()
            ->getMock();


        $addresses = $this->getMockBuilder('Wlr3_Addresses')
            ->setMethods(array('count'))
            ->disableOriginalConstructor()
            ->getMock();


        $objSelectBroadband = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi',
            'isHardwareRequirementNeeded')
        );

        $mockFamily = $this
            ->getMockBuilder('ProductFamily_Brp12Solus')
            ->setMethods(array('isSolus'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockFamily
            ->expects($this->any())
            ->method('isSolus')
            ->will($this->returnValue(true));

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::isApplicationStateVar()}
        // and {@link Mvc_WizardController::getApplicationStateVar()}
        $objMockController = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar',
            'getApplicationStateVar')
        );

        // Unfortunately we can't make those mocks generic and they have to run in a specific order.
        // So if anything changes with the {@link AccountChange_SelectBroadband::getNewReqs()}
        // you will have to update indexes
        // down here
        $objMockController->expects($this->at(0))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue($intOldSdi));

        $objMockController->expects($this->at(1))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('objBusinessActor'))
            ->will($this->returnValue($objBusinessActor));

        $objMockController->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrWlrProduct'))
            ->will($this->returnValue($arrWlrProduct));
        $objMockController->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolHousemove'))
            ->will($this->returnValue(false));

        $objMockController->expects($this->at(6))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('selectedProductFamily'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(7))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('selectedProductFamily'))
            ->will($this->returnValue($mockFamily));

        $objMockController->expects($this->at(8))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('intOldSdi'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(9))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrSelectedBroadband'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(10))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('currentSupplierProduct'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(11))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolPortal'))
            ->will($this->returnValue(false));

        $objMockController->expects($this->at(12))
            ->method('isApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolSchedule'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(13))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('bolSchedule'))
            ->will($this->returnValue(true));

        $objMockController->expects($this->at(14))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($objSelectBroadband)), $this->equalTo('arrSelectedBroadband'))
            ->will($this->returnValue(array('provisioningProfile' => 'FTTC')));

        $objMockController->expects($this->at(15))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($objSelectBroadband)),
                $this->equalTo('currentSupplierProduct')
            )
            ->will($this->returnValue($currentSupplierProduct));

        // Set callback object within the requirement itself to application controller
        $objSelectBroadband->setAppStateCallback($objMockController);

        $objSelectBroadband->expects($this->once())
            ->method('getNewSdi')
            ->will($this->returnValue($intNewSdi));

        $objSelectBroadband->expects($this->once())
            ->method('isHardwareRequirementNeeded')
            ->will($this->returnValue(false));

        $arrNewReqs = $objSelectBroadband->getNewReqs();

        $this->assertEquals(
            array(),
            $arrNewReqs
        );
    }

    /**
     * Test that the getNewReqs function returns empty array if old sdi is not set
     *
     * @covers AccountChange_SelectBroadbandRequirement::getNewReqs
     *
     * @return void
     */
    public function testGetNewReqsReturnsEmptyArrayIfOldSdiIsNotSet()
    {
        $broadband = new AccountChange_SelectBroadbandWorkplace();

        $result = $broadband->getNewReqs();

        $this->assertEquals(array(), $result);
    }

    /**
     * @group
     * Test the valOptOut validator
     *
     * @param bool $bolOptOut      If opt opt
     * @param bool $bolBtException If BT exception
     * @param bool $bolOutput      If outpuut
     *
     * @covers AccountChange_SelectBroadband::valOptOut
     *
     * @dataProvider provideOptOutData
     *
     * @return void
     */
    public function testValOptOutValidatorWithData($bolOptOut, $bolOutput)
    {
        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array('addValidationError'),
            array()
        );

        $requirement->expects($this->any())
            ->method('addValidationError');

        $result = $requirement->valOptOut($bolOptOut);

        $this->assertArrayHasKey('bolOptOut', $result);
        $this->assertEquals($bolOutput, $result['bolOptOut']);
    }

    /**
     * Data provider for testValOptOutAllowsOptOutIfBtExceptionThrown
     *
     * @return array
     */
    public static function provideOptOutData()
    {
        return array(
            array(true, true),
            array(false, ''),
            array(false, false),
        );
    }

    /**
     * We only need to add the requirement if specific conditions are met.
     * Please read the comments in the code for those requirements
     *
     * @param array  $components         Components
     * @param array  $component          Component
     * @param string $provision          Provision
     * @param array  $selectedBroadband  Selected broadband
     * @param array  $defaultHardware    Default hardware
     * @param string $currentProductCode Current product code
     * @param bool   $expected           Expected result
     * @param array  $hardwareStatuses   Hardware statuses array
     *
     * @covers AccountChange_SelectBroadbandRequirement::isHardwareRequirementNeeded
     *
     * @dataProvider provideDataForIsHardwareRequirementTests
     *
     * @return void
     */
    public function testIsHardwareRequirementNeededCorrectlyReturnsTheDecisionOutput(
        $components,
        $component,
        $provision,
        $selectedBroadband,
        $defaultHardware,
        $currentProductCode,
        $expected,
        $hardwareStatuses
    ) {
        $arrProductProvDetails = array(
           'intSupplierProductId' => 1,
           'bolWbcProduct'        => true,
           'strProvisionOn'       => 'Adsl2'
        );

        $rules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService'),
            array()
        );

        $rules->expects($this->any())
            ->method('getProductProvisionForService')
            ->will($this->returnValue($arrProductProvDetails));

        AccountChange_ProductRules::setInstance($rules);

        $client = $this->getMock(
            'HardwareClient_Client',
            array(
                'getHardwareForService',
                'getLastAddedHardwareForService',
                'getCompulsoryHardwareForServiceDefinition',
                'getHardwareStatusForService',
                'getLastHardwareDispatchedStatusForService',
                'isHubZeroRetirementToggleOn'
            ),
            array()
        );

        $client->expects($this->any())
            ->method('getHardwareForService')
            ->will($this->returnValue($components));

        $client->expects($this->any())
            ->method('getLastAddedHardwareForService')
            ->will($this->returnValue($component));

        $client->expects($this->any())
            ->method('getCompulsoryHardwareForServiceDefinition')
            ->will($this->returnValue($defaultHardware));

        $client->expects($this->any())
            ->method('getHardwareStatusForService')
            ->will($this->returnValue($hardwareStatuses));

        $client
            ->expects($this->any())
            ->method('getLastHardwareDispatchedStatusForService');


        BusTier_BusTier::setClient('hardware', $client);

        $service = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array(),
            '',
            false
        );

        $service->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(123));

        $requirement = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('getNewSdi','getProvisionedService','getHardwareHelper'),
            array()
        );

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(false);

        $requirement->expects($this->once())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $requirement->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(array('vchProductCode' => $currentProductCode)));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'),
            array()
        );

        $controller->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('objCoreService'))
            ->will($this->returnValue($service));

        $controller->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('intNewSdi'))
            ->will($this->returnValue(123));

        $controller->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('bolHousemove')
            )
            ->will($this->returnValue(false));

        $controller->expects($this->at(3))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('selectedContractDuration')
            )
            ->will(
                $this->returnValue('18')
            );

        $controller->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('strProvisionOn'))
            ->will($this->returnValue($provision));

        $controller->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('objLineCheckResult'))
            ->will(
                $this->returnValue(
                    $this->getMockBuilder(LineCheck_Result::class)
                        ->disableOriginalConstructor()
                        ->getMock()
                )
            );

        $controller->expects($this->at(6))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('arrSelectedBroadband'))
            ->will($this->returnValue($selectedBroadband));

        // Set callback object within the requirement itself to application controller
        $requirement->setAppStateCallback($controller);

        $output = $requirement->isHardwareRequirementNeeded();

        $this->assertEquals($expected, $output);
    }

    /**
     * Data provider for testIsHardwareRequirementNeededCorrectlyReturnsTheDecisionOutput
     *
     * @return void
     */
    public static function provideDataForIsHardwareRequirementTests()
    {
        $hardwareStatuses = array(
                    array(
                        'intComponentId'    => 22621138,
                        'strStatus'         => 'Dispatched',
                        'dtmStart'          => '2012-02-04 02:47:26',
                        'dtEndDate'         => null
                    ),
                );

        return array(
            array(
                array(),
                array(),
                'Adsl2',
                array(),
                array('strHandle' => ''),
                'WBC MAX8 Mb/s (EUA)',
                true,
                $hardwareStatuses
            ),
            array(
                array(),
                array(),
                'Adsl2',
                array(),
                array('strHandle' => 'pci_router'),
                'WBC MAX8 Mb/s (EUA)',
                false,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2007')),
                'Adsl2',
                array(),
                array('strHandle' => ''),
                'WBC MAX8 Mb/s (EUA)',
                true,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2007')),
                'Adsl2',
                array(),
                array('strHandle' => 'pci_router'),
                'WBC MAX8 Mb/s (EUA)',
                false,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2009')),
                'Adsl2',
                array(),
                array(),
                'WBC MAX8 Mb/s (EUA)',
                false,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2009')),
                'Adsl2',
                array(),
                array('strHandle' => ''),
                'WBC MAX8 Mb/s (EUA)',
                false,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2009')),
                'Adsl',
                array(),
                array('strHandle' => ''),
                'WBC MAX8 Mb/s (EUA)',
                false,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2007')),
                'Adsl2',
                array('provisioningProfile' => 'FTTC'),
                array('strHandle' => ''),
                'FTTC',
                true,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2007')),
                'Adsl2',
                array('provisioningProfile' => 'FTTC'),
                array('strHandle' => ''),
                'WBC MAX8 Mb/s (EUA)',
                true,
                $hardwareStatuses
            ),
            array(
                array(array('dtmCreated' => '2009-09-21')),
                array('dtmCreated' => I18n_Date::fromString('01-03-2007')),
                'Adsl2',
                array('provisioningProfile' => 'ADSL2'),
                array('strHandle' => ''),
                'WBC MAX8 Mb/s (EUA)',
                true,
                $hardwareStatuses
            ),
        );
    }

    /**
     * Test that the valProvisionOn validator works
     *
     * @param string $times          Frequency
     * @param string $strProvisionOn Providion on
     * @param array  $expected       Expected result
     *
     * @covers AccountChange_SelectBroadbandWorkplace::valProvisionOn
     *
     * @dataProvider provideDataForValProvisionOnTest
     *
     * @return void
     */
    public function testValProvisionOnCorrectlyValidatesTheInput($times, $strProvisionOn, $expected)
    {
        $requirement = $this->getMock(
            'AccountChange_SelectBroadbandWorkplace',
            array('addValidationError'),
            array()
        );

        $requirement->expects($this->$times())
            ->method('addValidationError');

        $result = $requirement->valProvisionOn($strProvisionOn);
        $this->assertEquals($expected, $result);
    }

    /**
     * Data provider for testValProvisionOnCorrectlyValidatesTheInput
     *
     * @return array
     */
    public static function provideDataForValProvisionOnTest()
    {
        return array(
            array('never', '', array('strProvisionOn' => '')),
            array('never', 'Adsl', array('strProvisionOn' => 'Adsl')),
            array('never', 'Adsl2', array('strProvisionOn' => 'Adsl2')),
            array('never', 'FTTC', array('strProvisionOn' => 'FTTC')),
            array('never', 'FTTP', array('strProvisionOn' => 'FTTP')),
            array('any', 'badbaddata', array('strProvisionOn' => 'badbaddata'))
        );
    }

    /**
     * Test is hardware requirement needed when change product
     *
     * @param int    $serviceId            Curent service id
     * @param string $provisionOn          Provision
     * @param array  $selectedBroadband    Selected broadband details
     * @param array  $lastDispatchedStatus Last dispatched status data
     * @param bool   $expectedResult       Is hardware requirement needed
     *
     * @dataProvider provideDataToTestIsHardwareRequirementNeededWhenChangeProduct
     *
     * @covers AccountChange_SelectBroadbandRequirement::isHardwareRequirementNeeded
     *
     * @return void
     */
    public function testIsHardwareRequirementNeededWhenChangeProduct(
        $serviceId,
        $provisionOn,
        $selectedBroadband,
        $lastDispatchedStatus,
        $expectedResult
    ) {
        $service = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array(),
            '',
            false
        );

        $service->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        $client = $this->getMock(
            'HardwareClient_Client',
            array('getLastHardwareDispatchedStatusForService'),
            array()
        );

        $client->expects($this->any())
            ->method('getLastHardwareDispatchedStatusForService')
            ->will($this->returnValue($lastDispatchedStatus));

        BusTier_BusTier::setClient('hardware', $client);

        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array('getProvisionedService','getHardwareHelper'),
            array()
        );

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(false);


        $requirement->expects($this->once())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'),
            array()
        );

        $controller->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('objCoreService')
            )
            ->will($this->returnValue($service));

        $controller->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('intNewSdi')
            )
            ->will($this->returnValue($selectedBroadband['intSdi']));

        $controller->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('bolHousemove')
            )
            ->will($this->returnValue(false));

        $controller->expects($this->at(3))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('selectedContractDuration')
            )
            ->will(
                $this->returnValue('18')
            );

        $controller->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('strProvisionOn')
            )
            ->will($this->returnValue($provisionOn));

        $controller->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('objLineCheckResult')
            )
            ->will(
                $this->returnValue(
                    $this->getMockBuilder(LineCheck_Result::class)
                        ->disableOriginalConstructor()
                        ->getMock()
                )
            );

        $controller->expects($this->at(6))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('arrSelectedBroadband')
            )
            ->will($this->returnValue($selectedBroadband));

        $requirement->setAppStateCallback($controller);

        $requirement->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(array('vchProductCode' => $provisionOn)));

        $isHardwareRequirementNeeded = $requirement->isHardwareRequirementNeeded();
        $this->assertEquals($expectedResult, $isHardwareRequirementNeeded);
    }

    /**
     * Provide data to test is hardware requirement needed when change product
     *
     * @return array
     */
    public function provideDataToTestIsHardwareRequirementNeededWhenChangeProduct()
    {
        $valueFibre = array(
            'intSdi'                => 6768,
            'provisioningProfile'   => 'FTTC'
        );

        $newExtraFibre = array(
            'intSdi'                => 6784,
            'provisioningProfile'   => 'FTTC'
        );

        $dispatchedBefore = array(
            'intComponentId' => 16815790,
            'strStatus'      => 'Dispatched',
            'dtmStart'       => '2012-06-16 06:36:09',
            'dtEndDate'      => null
        );

        $dispatchedAfter = array(
            'intComponentId' => 16815790,
            'strStatus'      => 'Dispatched',
            'dtmStart'       => '2012-06-22 06:36:09',
            'dtEndDate'      => null
        );

        return array(
            //New Plusnet Extra Fibre selected:
            //From ADSL
            array(
                'serviceId'             => 1812332,
                'provisionOn'           => 'Adsl',
                'selectedBroadband'     => $newExtraFibre,
                'lastDispatchedStatus'  => $dispatchedBefore,
                'expectedResult'        => true
            ),
            //From Value Fibre dispatched after 21/06/2012
            array(
                'serviceId'             => 1519880,
                'provisionOn'           => 'FTTC',
                'selectedBroadband'     => $newExtraFibre,
                'lastDispatchedStatus'  => $dispatchedAfter,
                'expectedResult'        => false
            ),
            //From Value Fibre dispatched before 21/06/2012
            array(
                'serviceId'             => 1519881,
                'provisionOn'           => 'FTTC',
                'selectedBroadband'     => $newExtraFibre,
                'lastDispatchedStatus'  => $dispatchedBefore,
                'expectedResult'        => true
            ),
            //From Value Fibre not dispatched
            array(
                'serviceId'             => 1519882,
                'provisionOn'           => 'FTTC',
                'selectedBroadband'     => $newExtraFibre,
                'lastDispatchedStatus'  => null,
                'expectedResult'        => true
            ),
            //Plusnet Value Fibre selected:
            //From ADSL
            array(
                'serviceId'             => 1812332,
                'provisionOn'           => 'Adsl',
                'selectedBroadband'     => $valueFibre,
                'lastDispatchedStatus'  => $dispatchedBefore,
                'expectedResult'        => true
            ),
            //From Extra Fibre dispatched after 21/06/2012
            array(
                'serviceId'             => 1785817,
                'provisionOn'           => 'FTTC',
                'selectedBroadband'     => $valueFibre,
                'lastDispatchedStatus'  => $dispatchedAfter,
                'expectedResult'        => false
            ),
            //From Extra Fibre dispatched before 21/06/2012
            array(
                'serviceId'             => 1785818,
                'provisionOn'           => 'FTTC',
                'selectedBroadband'     => $valueFibre,
                'lastDispatchedStatus'  => $dispatchedBefore,
                'expectedResult'        => false
            ),
            //From Extra Fibre not dispatched
            array(
                'serviceId'             => 1785819,
                'provisionOn'           => 'FTTC',
                'selectedBroadband'     => $valueFibre,
                '$lastDispatchedStatus' => null,
                'expectedResult'        => true
            ),
        );
    }

    /**
     * Test is hardware requirement needed when change product from product
     * which is not available to signup (disabed)
     *
     * @param int    $serviceId            Curent service id
     * @param string $provisionOn          Provision
     * @param array  $selectedBroadband    Selected broadband details
     * @param array  $lastDispatchedStatus Last dispatched status data
     * @param bool   $expectedResult       Is hardware requirement needed
     *
     * @dataProvider provideDataToTestIsHardwareRequirementNeededWhenChangeProduct
     *
     * @covers AccountChange_SelectBroadbandRequirement::isHardwareRequirementNeeded
     *
     * @return void
     */
    public function testIsHardwareRequirementNeededWhenChangeProductFromNotAvailableProduct(
        $serviceId,
        $provisionOn,
        $selectedBroadband,
        $lastDispatchedStatus,
        $expectedResult
    ) {
        $service = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array(),
            '',
            false
        );

        $service->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        $client = $this->getMock(
            'HardwareClient_Client',
            array('getLastHardwareDispatchedStatusForService'),
            array()
        );

        $client->expects($this->any())
            ->method('getLastHardwareDispatchedStatusForService')
            ->will($this->returnValue($lastDispatchedStatus));

        BusTier_BusTier::setClient('hardware', $client);

        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array('getProvisionedService', 'getHardwareHelper'),
            array()
        );

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockToggleHelper = $this->getMockBuilder(AccountChange_HardwareToggleHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(false);


        $requirement->expects($this->once())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'),
            array()
        );

        $controller->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('objCoreService')
            )
            ->will($this->returnValue($service));

        $controller->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('intNewSdi')
            )
            ->will($this->returnValue($selectedBroadband['intSdi']));

        $controller->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('bolHousemove')
            )
            ->willReturn(0);

        $controller->expects($this->at(3))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('selectedContractDuration')
            )
            ->willReturn(18);

        $controller->expects($this->at(4))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('strProvisionOn')
            )
            ->will($this->returnValue($provisionOn));

        $controller->expects($this->at(5))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('objLineCheckResult')
            )
            ->will(
                $this->returnValue(
                    $this->getMockBuilder(LineCheck_Result::class)
                        ->disableOriginalConstructor()
                        ->getMock()
                )
            );

        $controller->expects($this->at(6))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($requirement)),
                $this->equalTo('arrSelectedBroadband')
            )
            ->will($this->returnValue($selectedBroadband));

        $requirement->setAppStateCallback($controller);

        $requirement->expects($this->at(0))
            ->method('getProvisionedService')
            ->will($this->returnValue(null));

        $requirement->expects($this->at(1))
            ->method('getProvisionedService')
            ->will($this->returnValue(array('vchProductCode' => $provisionOn)));

        $isHardwareRequirementNeeded = $requirement->isHardwareRequirementNeeded();
        $this->assertEquals($expectedResult, $isHardwareRequirementNeeded);
    }

    /**
     * @dataProvider dataForTestIsHardwareRequirementNeeded
     *
     * @param bool $hardwareHelperResult   will this customer be offered hardware?
     * @param bool $expectedResult         expected result
     *
     * @return void
     */
    public function testIsHardwareRequiredWhenHelperIsUsed(
        $hardwareHelperResult,
        $expectedResult
    ) {
        $this->setUpPartialMock($hardwareHelperResult);
        $this->setUpFibreHelper();
        $this->setUpController(static::MOCK_NEW_SERVICE_DEFINITION_ID);

        $this->assertEquals($expectedResult, $this->selectBroadband->isHardwareRequirementNeeded());
    }

    public function dataForTestIsHardwareRequirementNeeded()
    {
        return [
            'expectsTrue' => [
                'hardwareHelperResult' => true,
                'expectedResult' => true,
            ],
            'expectsFalse' => [
                'hardwareHelperResult' => false,
                'expectedResult' => false,
            ],
        ];
    }

    /**
     * @param bool   $changingProducts       Is customer changing products?
     * @param string $newServiceDefinitionId new broadband product service definition ID
     * @return void
     */
    private function setUpController($newServiceDefinitionId)
    {
        $mockService = $this->getMockBuilder('Core_Service')
            ->disableOriginalConstructor()
            ->setMethods(['getServiceId'])
            ->getMock();

        $mockService->expects($this->any())
            ->method('getServiceId')
            ->willReturn(static::MOCK_SERVICE_ID);

        $controller = $this->getMockBuilder('AccountChange_Controller')
            ->setMethods(['getApplicationStateVar'])
            ->getMock();

        $controller->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($this->selectBroadband)),
                $this->equalTo('objCoreService')
            )
            ->willReturn($mockService);

        $controller->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($this->selectBroadband)),
                $this->equalTo('intNewSdi')
            )
            ->willReturn($newServiceDefinitionId);

        $controller->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($this->selectBroadband)),
                $this->equalTo('bolHousemove')
            )
            ->willReturn(1);

        $controller->expects($this->at(3))
            ->method('getApplicationStateVar')
            ->with(
                $this->equalTo(get_class($this->selectBroadband)),
                $this->equalTo('selectedContractDuration')
            )
            ->willReturn('0');

        $this->selectBroadband->setAppStateCallback($controller);
    }

    private function setUpFibreHelper()
    {
        $mockFibreHelper = $this->getMockBuilder(AccountChange_FibreHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['isFibreProduct'])
            ->getMock();

        $mockFibreHelper->expects($this->any())
            ->method('isFibreProduct')
            ->willReturn(true);

        AccountChange_ServiceManager::setService('FibreHelper', $mockFibreHelper);
    }

    private function setUpPartialMock($hardwareHelperResult)
    {
        $this->selectBroadband = $this->getMockBuilder(AccountChange_SelectBroadband::class)
            ->setMethods(['getHardwareHelper'])
            ->getMock();

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldShowHardwarePage','shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(true);

        $mockHelper->expects($this->once())
            ->method('shouldShowHardwarePage')
            ->willReturn($hardwareHelperResult);

        $this->selectBroadband->expects($this->once())
            ->method('getHardwareHelper')
            ->willReturn($mockHelper);
    }
}
