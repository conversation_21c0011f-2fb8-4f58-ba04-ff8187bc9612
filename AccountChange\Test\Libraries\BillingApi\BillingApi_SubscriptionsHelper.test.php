<?php

use Plusnet\BillingApiClient\Entity\CustomerProductSubscription;

class AccountChange_BillingApi_SubscriptionsHelper_Test extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 1234;
    const LINE_RENTAL = 'Line Rental';
    const TARIFF_NAME_LINE_RENTAL_SAVER = 'Line Rental Saver';

    /**
     * Tests that if we have a subscription for Line Rental with value 0, then we have LRS
     *
     * @return void
     */
    public function testHasActiveOrPendingLineRentalSaverReturnsTrueWhenThereIsLrsSub()
    {
        $subs = array(
            $this->aMockSubscription(self::LINE_RENTAL, 210),
            $this->aMockSubscription(self::LINE_RENTAL, 0),
            $this->aMockSubscription('Some other product', '100')
        );

        $helper = Mockery::mock('AccountChange_BillingApi_SubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCustomerCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $this->assertTrue($helper->hasActiveOrPendingLineRentalSaver(self::SERVICE_ID));
    }

    /**
     * Tests that if we have a subscription for Line Rental with value > 0, and none with value 0,
     * then we do not  have LRS
     *
     * @return void
     */
    public function testHasActiveOrPendingLineRentalSaverReturnsFalseWhenThereIsNoZeroLineRentalSubscription()
    {
        $subs = array(
            $this->aMockSubscription(self::LINE_RENTAL, 210),
            $this->aMockSubscription('Some other product', '100')
        );

        $helper = Mockery::mock('AccountChange_BillingApi_SubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCustomerCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);
        $this->assertFalse($helper->hasActiveOrPendingLineRentalSaver(self::SERVICE_ID));
    }

    /**
     * Tests that if we have no line rental subscriptions, then we do not  have LRS
     *
     * @return void
     */
    public function testHasActiveOrPendingLineRentalSaverReturnsFalseWhenThereAreNoLineRentalSubs()
    {
        $subs = array(
            $this->aMockSubscription('Some other product', '100')
        );

        $helper = Mockery::mock('AccountChange_BillingApi_SubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCustomerCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);
        $this->assertFalse($helper->hasActiveOrPendingLineRentalSaver(self::SERVICE_ID));
    }

    /**
     * Tests that if we have no subscriptions at all, then we do not  have LRS
     *
     * @return void
     */
    public function testHasActiveOrPendingLineRentalSaverReturnsFalseWhenThereAreNoSubs()
    {
        $subs = array();

        $helper = Mockery::mock('AccountChange_BillingApi_SubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCustomerCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);
        $this->assertFalse($helper->hasActiveOrPendingLineRentalSaver(self::SERVICE_ID));
    }

    /**
     * Returns a mock subscription entity
     *
     * @param string $productType Product type
     * @param string $subscriptionPrice Subscription price
     *
     * @return Plusnet\BillingApiClient\Entity\CustomerProductSubscription
     **/
    private function aMockSubscription($productType, $subscriptionPrice)
    {
        $mockSubs = Mockery::mock('Plusnet\BillingApiClient\Entity\CustomerProductSubscription');
        $mockSubs->makePartial();
        $mockSubs->setProductType($productType);
        $mockSubs->setCurrentSubscriptionPriceInPounds($subscriptionPrice);
        return $mockSubs;
    }

    public function testHasActiveLineRentalSaverReturnFalseWhenNoLrsInSubscriptionResponse()
    {
        $subs = array(
            $this->LrsMockSubscription(self::LINE_RENTAL),
            $this->LrsMockSubscription(self::LINE_RENTAL),
            $this->LrsMockSubscription('Some other product')
        );

        $helper = Mockery::mock('AccountChange_BillingApi_SubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCustomerCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $this->assertFalse($helper->hasActiveLineRentalSaver(self::SERVICE_ID));
    }

    public function testHasActiveLineRentalSaverReturnTrueWhenThereIsLrsInSubscriptionResponse()
    {
        $subs = array(
            $this->LrsMockSubscription(self::LINE_RENTAL),
            $this->LrsMockSubscription(self::TARIFF_NAME_LINE_RENTAL_SAVER),
            $this->LrsMockSubscription('Some other product')
        );

        $helper = Mockery::mock('AccountChange_BillingApi_SubscriptionsHelper');
        $helper->makePartial();
        $helper->shouldAllowMockingProtectedMethods();
        $helper->shouldReceive('getCustomerCurrentSubscriptions')->with(self::SERVICE_ID)->andReturn($subs);

        $this->assertTrue($helper->hasActiveLineRentalSaver(self::SERVICE_ID));
    }

    /**
     * @param string $tariffName Product type
     *
     * @return CustomerProductSubscription
     **/
    private function LrsMockSubscription($tariffName)
    {
        $mockSubs = Mockery::mock('Plusnet\BillingApiClient\Entity\CustomerProductSubscription');
        $mockSubs->makePartial();
        $mockSubs->setTariffName($tariffName);
        return $mockSubs;
    }
}
