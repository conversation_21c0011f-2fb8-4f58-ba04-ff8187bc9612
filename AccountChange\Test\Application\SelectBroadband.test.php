<?php
/**
 * Account Change Select Broadband Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: SelectBroadband.test.php,v 1.4 2009-07-14 15:34:48 bselby Exp $
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
/**
 * Account Change Select Broadband Test
 *
 * Testing class for AccountChange_SelectBroadband requirement
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */

use Plusnet\Feature\FeatureToggleManager;
use Plusnet\Feature\FeatureTogglePersistenceImpl;

class AccountChange_SelectBroadband_Test extends PHPUnit_Framework_TestCase
{
    protected function setup()
    {
        Db_Manager::reset();
        FeatureToggleManager::reset();
        FeatureTogglePersistenceImpl::reset();

        $c2mClient = $this->getMock(
            'Plusnet\C2mApiClient\V5\Client',
            array('getPrice'),
            array(),
            '',
            false
        );

        BusTier_BusTier::setClient('c2mapi.v5', $c2mClient);

        $c2mClient
            ->method('getPrice')
            ->with('LineRental')
            ->willReturn(array( 'amount' => '19.7900000000000000000000'));
    }

    protected function teardown()
    {
        $c2mClient = null;
    }

    /**
     * Test that decorateProducts can add Wbc information when required
     *
     * @covers AccountChange_SelectBroadband::decorateProducts
     *
     * @return void
     */
    public function testDecorateProductsAddsWbcDataIfApplicable()
    {
        $arrProductProvDetails = array(
           'intSupplierProductId' => 1,
           'bolWbcProduct'        => true,
           'strProvisionOn'       => 'Adsl2'
        );
        $productSet = array(
            array('intSdi' => 123,),
        );

        $expected = array( 123 =>
            array(
                'intSdi' => 123,
                'bolWbcProduct' => true,
                'strProvisionOn' => 'Adsl2',
                'isBizADSLCapped' => false,
                'isBizFibreCapped' => false
            )
        );

        $rules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService'),
            array()
        );

        $rules
            ->expects($this->any())
            ->method('getProductProvisionForService')
            ->will($this->returnValue($arrProductProvDetails));

        AccountChange_ProductRules::setInstance($rules);

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getFttcDownSpeed'),
            [],
            '',
            false
        );
        $lineCheckResult->expects($this->any())
               ->method('getFttcDownSpeed')
            ->will($this->returnValue(80000));

        AccountChange_Registry::instance()->setEntry('objLineCheckResult', $lineCheckResult);

        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array('isProvisionedOnAdsl2', 'getProvisionedService', 'isBusiness')
        );

        $requirement
            ->expects($this->once())
            ->method('isProvisionedOnAdsl2')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->once())
            ->method('getProvisionedService')
            ->will($this->returnValue(array()));

        $requirement
            ->expects($this->once())
            ->method('isBusiness')
            ->will($this->returnValue(true));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar'),
            array()
        );

        $controller
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('objLineCheckResult'))
            ->will(
                $this->returnValue(
                    $this->getMockBuilder(LineCheck_Result::class)
                        ->disableOriginalConstructor()
                        ->getMock()
                )
            );

        $requirement->setAppStateCallback($controller);

        $products = $requirement->decorateProducts($productSet, 123);

        $this->assertEquals($expected, $products);
    }

    /**
     * Test the function describe
     * to check whether the selected broadband is provisoned
     *
     * @covers AccountChange_SelectBroadband::describe
     *
     * @return void
     */
    public function testDescribeForSelectBroadbandIsProvisionedReturnsOne()
    {
        $productChangePlanClientMock = $this->getMock(
            'stdClass',
            array('getServiceDefinitionVariants')
        );

        $productChangePlanClientMock->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        BusTier_BusTier::setClient('productChangePlan', $productChangePlanClientMock);

        $mockProductFamily = $this->getMock(
            'ProductFamily_Generic',
            array(),
            array(),
            '',
            false
        );

        $wlrInformation = array(
            'bolWlrAddAllowed' => 1
        );

        $accountInstance = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation'),
            array(),
            '',
            false
        );

        $accountInstance->expects($this->once())
            ->method('getWlrInformation')
            ->will($this->returnValue($wlrInformation));

        AccountChange_Account::setInstance($accountInstance);

        $strUnit = AccountChange_Manager::CURRENCY_UNIT;
        $arrProducts = array('arrProducts' => array('132' =>
            array(
                'intSdi'            => 132,
                'intNewWlrId'       => 1111,
                'strContractHandle' => 'ANNUAL',
                'strProductName'    => 'Test Wlr Product',
                'intProductCost'    => new I18n_Currency($strUnit, 10),
                'strProvisionOn'    => 'Adsl2',
                'currentBasePrice'  => new I18n_Currency($strUnit, 10)
            )), 'errorCode' =>  'error');
        $intDiscountLength = 10;

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );

        $objCoreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1110));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);
        $objCoreAdslService = $this->getMock(
            'C_Core_ADSLService',
            array('isProvisionedAsADSLOrMaxDSL', 'getSupplierPlatform'),
            array()
        );
        $objLineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getExchangeCode', 'getFttcUncappedDownSpeed'),
            array(),
            '',
            false
        );
        $objLineCheckResult
            ->expects($this->any())
            ->method('getExchangeCode')
            ->will($this->returnValue('E'));

        $objCoreAdslService
            ->expects($this->any())
            ->method('isProvisionedAsADSLOrMaxDSL')
            ->will($this->returnValue(true));

        $objCoreAdslService
            ->expects($this->any())
            ->method('getSupplierPlatform')
            ->will($this->returnValue('BT21CN'));

        $objLineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );
        $objLineCheckMarket
            ->expects($this->once())
            ->method('getMarketId')
            ->will($this->returnValue(1));


        $mockFpcStatusService = $this->getMock(
            '\Plusnet\PriceProtected\Services\StatusService',
            array('isServicePriceProtected')
        );

        $mockFpcStatusService
            ->expects($this->once())
            ->method('isServicePriceProtected')
            ->will($this->returnValue(true));

        $objBusinessActor = $this->getMockBusinesActor();

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array(
                'getCoreADSLService',
                'decorateProducts',
                'getBroadbandProducts',
                'getLineCheckMarket',
                'isProvisionedOnAdsl2',
                'getProvisionedService',
                'isOldProductFibre',
                'getProductFamily',
                'getServiceDefinitionVariants',
                'getMinAndMaxSpeedRanges',
                'getFpcStatusService',
                'isC2fToggleSet',
                'isBGLCallPlansToggleSet',
                'setUpValidator',
                'checkIfAllowedToMakeChange',
                'makeChangeErrorMessage',
                'makeChangeErrorCode'
                )
        );

        $requirement->method('isBGLCallPlansToggleSet')->willReturn(false);

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        $requirement->expects($this->once())
            ->method('getFpcStatusService')
            ->will($this->returnValue($mockFpcStatusService));

        $requirement
            ->expects($this->once())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($objLineCheckMarket));

        $requirement
            ->expects($this->once())
            ->method('getBroadbandProducts')
            ->will($this->returnValue($arrProducts));

        $requirement
            ->expects($this->once())
            ->method('decorateProducts')
            ->will($this->returnValue($arrProducts['arrProducts']));

        $requirement
            ->expects($this->once())
            ->method('getCoreADSLService')
            ->will($this->returnValue($objCoreAdslService));

        $requirement
            ->expects($this->once())
            ->method('getCoreADSLService')
            ->will($this->returnValue($objCoreAdslService));

        $requirement
            ->expects($this->once())
            ->method('isProvisionedOnAdsl2')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->once())
            ->method('getProvisionedService')
            ->will($this->returnValue(array()));

        $requirement
            ->expects($this->once())
            ->method('isOldProductFibre')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->any())
            ->method('getProductFamily')
            ->will($this->returnValue($mockProductFamily));

        $requirement
            ->expects($this->once())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $requirement
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $requirement
            ->expects($this->atLeastOnce())
            ->method('setUpValidator')
            ->will(
                $this->returnValue(
                    $validationCheck
                )
            );

        $requirement
            ->expects($this->atLeastOnce())
            ->method('checkIfAllowedToMakeChange')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->atLeastOnce())
            ->method('makeChangeErrorMessage')
            ->will($this->returnValue(false));

        $requirement
            ->expects($this->atLeastOnce())
            ->method('makeChangeErrorMessage')
            ->will($this->returnValue(null));

        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->returnCallback(
                    function (
                        $strCallingClass,
                        $strVariableName
                    ) use (
                        $objCoreService,
                        $objLineCheckResult,
                        $intDiscountLength
                    ) {
                        switch ($strVariableName) {
                            case 'objCoreService':
                                return $objCoreService;
                            case 'intOldSdi':
                                return 132;
                            case 'objLineCheckResult':
                                return $objLineCheckResult;
                            case 'intDiscountLength':
                                return $intDiscountLength;
                        }
                    }
                )
            );

        $requirement->setAppStateCallback($controller);
        $validatedApplicationData = array('productIsp' => 'plusnet');

        $mockCoreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockCoreDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will(
                $this->returnValue(
                    array(
                        'service_definition_id' => 1234,
                        'provisioningProfile' => null
                    )
                )
            );

        Db_Manager::setAdaptor('Core', $mockCoreDbAdaptor);

        $result = $requirement->describe($validatedApplicationData);
        $this->assertArrayHasKey('bolProvisioned', $result);
        $this->assertEquals(1, $result['bolProvisioned']);
    }


    /**
     * Test the function describe
     * to check the behavior due to ISP
     *
     * @param array $isp            Isp
     * @param array $arrProducts    Products
     * @param array $expectedResult Expected result
     *
     * @covers AccountChange_SelectBroadband::describe
     * @dataProvider dataProviderForTestDescribeToCheckBehaviorDueToIsp
     *
     * @return void
     */
    public function testDescribeToCheckBehaviorDueToIsp(
        $isp,
        array $arrProducts,
        array $expectedResult
    ) {

        if ($isp == 'business') {
            $isBusiness = true;
            $isp = 'plus.net';
        } else {
            $isBusiness = false;
        }

        $productChangePlanClientMock = $this->getMock(
            'stdClass',
            array('getServiceDefinitionVariants')
        );

        $productChangePlanClientMock->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        BusTier_BusTier::setClient('productChangePlan', $productChangePlanClientMock);

        $mockProductFamily = $this->getMock(
            'ProductFamily_Generic',
            array(),
            array(),
            '',
            false
        );

        $intDiscountLength = 10;
        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );
        $objCoreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1110));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);
        $objCoreAdslService = $this->getMock(
            'C_Core_ADSLService',
            array('isProvisionedAsADSLOrMaxDSL', 'getSupplierPlatform'),
            array()
        );
        /** @var PHPUnit_Framework_MockObject_MockObject|LineCheck_Result $objLineCheckResult */
        $objLineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getExchangeCode', 'getFttcUncappedDownSpeed'),
            array(),
            '',
            false
        );
        $objLineCheckResult
            ->expects($this->any())
            ->method('getExchangeCode')
            ->will($this->returnValue('E'));

        $objCoreAdslService
            ->expects($this->any())
            ->method('isProvisionedAsADSLOrMaxDSL')
            ->will($this->returnValue(true));

        $objCoreAdslService
            ->expects($this->any())
            ->method('getSupplierPlatform')
            ->will($this->returnValue('BT21CN'));

        $objLineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );

        $objLineCheckMarket
            ->expects($this->once())
            ->method('getMarketId')
            ->will($this->returnValue(1));

        $mockFpcStatusService = $this->getMock(
            '\Plusnet\PriceProtected\Services\StatusService',
            array('isServicePriceProtected')
        );

        $mockFpcStatusService
            ->expects($this->once())
            ->method('isServicePriceProtected')
            ->will($this->returnValue(true));

        $objBusinessActor = $this->getMockBusinesActor();

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array(
                'getCoreADSLService',
                'getBroadbandProducts',
                'getLineCheckMarket',
                'isProvisionedOnAdsl2',
                'getProvisionedService',
                'isOldProductFibre',
                'getProductFamily',
                'getServiceDefinitionVariants',
                'getMinAndMaxSpeedRanges',
                'getFpcStatusService',
                'isFixedPriceContractsFeatureToggleOn',
                'isC2fToggleSet',
                'isBGLCallPlansToggleSet',
                'getServiceDefinitionDetails',
                'setUpValidator',
                'checkIfAllowedToMakeChange',
                'makeChangeErrorMessage',
                'makeChangeErrorCode'
            )
        );

        $requirement->method('isBGLCallPlansToggleSet')->willReturn(false);

        $requirement->expects($this->any())
            ->method('isFixedPriceContractsFeatureToggleOn')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->once())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($objLineCheckMarket));

        $requirement
            ->expects($this->once())
            ->method('getFpcStatusService')
            ->will($this->returnValue($mockFpcStatusService));

        $requirement
            ->expects($this->once())
            ->method('isOldProductFibre')
            ->will($this->returnValue(true));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );
        $requirement
            ->expects($this->once())
            ->method('getBroadbandProducts')
            ->will($this->returnValue($arrProducts));

        $requirement
            ->expects($this->once())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $requirement
            ->expects($this->once())
            ->method('getCoreADSLService')
            ->will($this->returnValue($objCoreAdslService));

        $requirement
            ->expects($this->once())
            ->method('getCoreADSLService')
            ->will($this->returnValue($objCoreAdslService));

        $requirement
            ->expects($this->exactly(2))
            ->method('isProvisionedOnAdsl2')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(array()));

        $requirement
            ->expects($this->any())
            ->method('getProductFamily')
            ->will($this->returnValue($mockProductFamily));

        $requirement
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $requirement->expects($this->any())
            ->method('isBusiness')
            ->willReturn($isBusiness);

        $requirement->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->willReturnCallback(function () use ($isBusiness) {
                if ($isBusiness) {
                    return ['type' => Core_ServiceDefinition::BUSINESS_ACCOUNT];
                } else {
                    return ['type' => Core_ServiceDefinition::RESIDENTIAL_ACCOUNT];
                }
            });

        $requirement
            ->expects($this->atLeastOnce())
            ->method('setUpValidator')
            ->will(
                $this->returnValue(
                    $validationCheck
                )
            );

        $requirement
            ->expects($this->atLeastOnce())
            ->method('checkIfAllowedToMakeChange')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->atLeastOnce())
            ->method('makeChangeErrorMessage')
            ->will($this->returnValue(false));

        $requirement
            ->expects($this->atLeastOnce())
            ->method('makeChangeErrorCode')
            ->will($this->returnValue(null));

        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will(
                $this->onConsecutiveCalls(
                    ($objCoreService),
                    (132),
                    ($objLineCheckResult),
                    $intDiscountLength
                )
            );

        $requirement->setAppStateCallback($controller);
        $validatedApplicationData = array('productIsp' => $isp);

        $accountInstance = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation'),
            array(),
            '',
            false
        );

        $wlrInformation = array(
            'bolWlrAddAllowed' => 1
        );

        $accountInstance->expects($this->once())
            ->method('getWlrInformation')
            ->will($this->returnValue($wlrInformation));

        AccountChange_Account::setInstance($accountInstance);
        $result = $requirement->describe($validatedApplicationData);

        $index = 0;
        foreach ($result['arrNewProducts'] as $products) {
            $this->assertEquals(
                $expectedResult[$index]['strProductName'],
                $products['strProductName']
            );
            $index++;
        }
    }

    /**
     * Data provider for testDescribeToCheckBehaviorDueToIsp
     *
     * @return array
     */
    public function dataProviderForTestDescribeToCheckBehaviorDueToIsp()
    {
        $arrSource = array('arrProducts' => array(
            0 => array(
                'strProductName'    => 'Standard',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 19.99),
                'intSdi'            => 1,
                'currentBasePrice'  => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 19.99)

            ),
            1 => array(
                'strProductName'    => 'Unlimited',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20.99),
                'intSdi'            => 2,
                'currentBasePrice'  => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20.99)
            ),
            2 => array(
                'strProductName'    => 'Fibre',
                'intProductCost'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 29.99),
                'intSdi'            => 3,
                'currentBasePrice'  => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 29.99)
            )), 'errorCode' =>  'error');

        $arrResult = array(
            0 => array(
                'strProductName'  => 'Unlimited',
                'intProductCost'  => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 19.99),
            ),
            1 => array(
                'strProductName'  => 'Standard',
                'intProductCost'  => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20.99),
            ),
            2 => array(
                'strProductName'  => 'Fibre',
                'intProductCost'  => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 29.99),
           )
        );

        return array(
            array('greenbee', $arrSource, $arrResult),
            array('waitrose', $arrSource, $arrResult),
            array('johnlewis', $arrSource, $arrResult),
            array('plus.net', $arrSource, $arrSource['arrProducts']),
            array('business', $arrSource, $arrSource['arrProducts'])
        );
    }

    /**
     * Test the moveProductToFront method
     *
     * @param array $arrSource     Products array
     * @param unk   $key           By which key seeks for given value
     * @param unk   $value         Desired value
     * @param array $expectedArray Expected result array
     *
     * @covers AccountChange_SelectBroadband::moveProductToFront
     * @dataProvider dataProviderForTestMoveProductToFrontMethod
     *
     * @return void
     */
    public function testMoveProductToFrontMethod(
        array $arrSource,
        $key,
        $value,
        array $expectedArray
    ) {
        $selectBroadband = $this->getMock(
            'AccountChange_SelectBroadband',
            array('includeLegacyFiles')
        );

        $result = $selectBroadband->moveProductToFront($arrSource, $key, $value);

        $this->assertEquals($expectedArray, $result);
    }

    /**
     * Data provider for testMoveProductToFrontMethod
     *
     * @return array
     */
    public function dataProviderForTestMoveProductToFrontMethod()
    {
        $arrSource = array(
                        0 => array(
                                'key'  => 0,
                                'hole' => 1,
                                'door' => 1
                             ),
                        1 => array(
                                'key'  => 1,
                                'hole' => 1,
                                'door' => 1
                             ),
                        2 => array(
                                'key'  => 2,
                                'hole' => 1,
                                'door' => 1
                             ),
                        3 => array(
                                'key'  => 3,
                                'hole' => 1,
                                'door' => 1
                             ),
                        4 => array(
                                'key'  => 4,
                                'hole' => 1,
                                'door' => 1
                             )
        );
        $arrResult = array(
                        0 => array(
                                'key'  => 3,
                                'hole' => 1,
                                'door' => 1
                             ),
                        1 => array(
                                'key'  => 0,
                                'hole' => 1,
                                'door' => 1
                             ),
                        2 => array(
                                'key'  => 1,
                                'hole' => 1,
                                'door' => 1
                             ),
                        3 => array(
                                'key'  => 2,
                                'hole' => 1,
                                'door' => 1
                             ),
                        4 => array(
                                'key'  => 4,
                                'hole' => 1,
                                'door' => 1
                             )
        );

        return array(
            //No key provided
            array(
                'arrSource'     => $arrSource,
                'key'           => null,
                'value'         => 3,
                'expectedArray' => $arrSource
            ),
            //No value provided
            array(
                'arrSource'     => $arrSource,
                'key'           => 'key',
                'value'         => null,
                'expectedArray' => $arrSource
            ),
            //Key not in array
            array(
                'arrSource'     => $arrSource,
                'key'           => 'other key',
                'value'         => 3,
                'expectedArray' => $arrSource
            ),
            //Value not in array
            array(
                'arrSource'     => $arrSource,
                'key'           => 'key',
                'value'         => 5,
                'expectedArray' => $arrSource
            ),
            //Every elements provided
            array(
                'arrSource'     => $arrSource,
                'key'           => 'key',
                'value'         => 3,
                'expectedArray' => $arrResult
            ),
        );
    }

    /**
     * Test that valNewSdi correctly validates a service definition id
     *
     * @covers AccountChange_SelectBroadband::valNewSdi
     *
     * @return void
     **/
    public function testValNewSdiCorrectlyValidatesServiceDefinitionId()
    {
        $broadbandProducts = array('arrProducts' => array(
            array(
                'intSdi' => 6758,
                'intTariffID' => 1235,
                'strProductName' => 'ProductX',
                'intProductCost' => 2149,
                'objProductDiscountedCost' => null,
                'provisioningProfile' => null,
                'maxDownloadSpeed' => 40000,
                'maxUploadSpeed' => 20000,
                'downloadSpeedRangeMin' => 36000,
                'downloadSpeedRangeMax' => 40000,
                'uploadSpeedRangeMin' => 1000,
                'uploadSpeedRangeMax' => 2000,
            )), 'errorCode' =>  'error');

        $productProvision = array(
            'bolWbcProduct'  => true,
            'strProvisionOn' => 'Adsl'
        );

        $rules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService'),
            array()
        );

        $rules
            ->expects($this->any())
            ->method('getProductProvisionForService')
            ->will($this->returnValue($productProvision));

        AccountChange_ProductRules::setInstance($rules);

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar', 'getApplicationStateVar'));

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getExchangeCode'),
            array(),
            '',
            false
        );
        $lineCheckResult
            ->expects($this->any())
            ->method('getExchangeCode')
            ->will($this->returnValue('E'));

        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );
        $coreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1110));

        $selectBroadband = $this->getMock(
            'AccountChange_SelectBroadband',
            array(
                'getBroadbandProducts',
                'getFinancialErrors',
                'getHardwareClient',
                'getProductFamily',
                'getLineCheckMarket',
                'getWlrDetailsFromCoreService',
                'isBusiness'
            )
        );

        $productFamily = $this->getMock(
            'ProductFamily_Brp12Solus',
            array(),
            array(),
            '',
            false
        );

        $mockLineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );
        $mockLineCheckMarket
            ->expects($this->once())
            ->method('getMarketId')
            ->will($this->returnValue(1));

        $selectBroadband
            ->expects($this->once())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($mockLineCheckMarket));

        $selectBroadband
            ->expects($this->once())
            ->method('getBroadbandProducts')
            ->will($this->returnValue($broadbandProducts));

        $selectBroadband
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($productFamily));

        $selectBroadband
            ->expects($this->once())
            ->method('getWlrDetailsFromCoreService')
            ->will($this->returnValue(array()));

        $selectBroadband
            ->expects($this->once())
            ->method('isBusiness')
            ->will($this->returnValue(false));

        $mockHardwareClient = $this->getMock(
            'BusTier_Client',
            array('getCompulsoryHardwareForServiceDefinition')
        );

        $mockHardwareClient
            ->expects($this->once())
            ->method('getCompulsoryHardwareForServiceDefinition')
            ->will($this->returnValue(null));

        $selectBroadband
            ->expects($this->once())
            ->method('getHardwareClient')
            ->will($this->returnValue($mockHardwareClient));

        $selectBroadband
            ->expects($this->once())
            ->method('getFinancialErrors')
            ->will($this->returnValue(array()));

        $mockController
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(get_class($selectBroadband), 'objCoreService')
            ->will($this->returnValue($coreService));

        $mockController
            ->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(get_class($selectBroadband), 'objLineCheckResult')
            ->will($this->returnValue($lineCheckResult));

        // Set callback object within the requirement itself to application controller
        $selectBroadband->setAppStateCallback($mockController);
        $sdi['6758_1235'] = '6758';
        $selectBroadband->valNewSdi($sdi);
    }

    /**
     * Test that valNewSdi returns validation errors when there's financial
     * errors.
     *
     * @covers AccountChange_SelectBroadband::valNewSdi
     *
     * @return void
     **/
    public function testValNewSdiWhenThereAreFinancialErrors()
    {
        $oldSdi = 6754;

        $broadbandProducts = array('arrProducts' => array(
            array(
                'intSdi' => 6758,
                'intTariffID' => 1235
            )), 'errorCode' =>  'error');

        $productProvision = array(
            'bolWbcProduct'  => true,
            'strProvisionOn' => 'Adsl'
        );

        $rules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService'),
            array()
        );

        $rules
            ->expects($this->any())
            ->method('getProductProvisionForService')
            ->will($this->returnValue($productProvision));

        AccountChange_ProductRules::setInstance($rules);

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getExchangeCode'),
            array(),
            '',
            false
        );
        $lineCheckResult
            ->expects($this->any())
            ->method('getExchangeCode')
            ->will($this->returnValue('E'));

        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );
        $coreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1110));

        $selectBroadband = $this->getMock(
            'AccountChange_SelectBroadband',
            array(
                'getBroadbandProducts',
                'getFinancialErrors',
                'getLineCheckMarket'
            )
        );

        $mockLineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );
        $mockLineCheckMarket
            ->expects($this->once())
            ->method('getMarketId')
            ->will($this->returnValue(1));

        $selectBroadband
            ->expects($this->once())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($mockLineCheckMarket));

        $selectBroadband
            ->expects($this->once())
            ->method('getBroadbandProducts')
            ->will($this->returnValue($broadbandProducts));

        $financialErrors = array(
            'error' => 'Oh no, this account has some financial errors'
        );

        $selectBroadband
            ->expects($this->once())
            ->method('getFinancialErrors')
            ->will($this->returnValue($financialErrors));

        $mockController
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(get_class($selectBroadband), 'objCoreService')
            ->will($this->returnValue($coreService));

        $mockController
            ->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(get_class($selectBroadband), 'objLineCheckResult')
            ->will($this->returnValue($lineCheckResult));

        $mockController
            ->expects($this->at(2))
            ->method('getApplicationStateVar')
            ->with(get_class($selectBroadband), 'intOldSdi')
            ->will($this->returnValue($oldSdi));

        // Set callback object within the requirement itself to application controller
        $selectBroadband->setAppStateCallback($mockController);
        $sdi[] = 6755;
        $this->assertEquals(array(), $selectBroadband->valNewSdi($sdi));
        $expectedValidationError = array(
           'intNewSdi' => array(
               'FINANCIAL_ERROR' => array(
                   'strError' => 'Oh no, this account has some financial errors'
                )
            )
        );
        $this->assertEquals($expectedValidationError, $selectBroadband->getValidationErrors());
    }

    /**
     * Data provider for testValNewSdiWhenThereAreNumericErrors
     *
     * @return array
     **/
    public function provideDataForValNewSdiWhenThereAreNumericErrors()
    {
        $sdiNotNumeric = array('arrProducts' => array(
            array(
                'intSdi' => 'nan',
                'intTariffID' => 1235
            )), 'errorCode' =>  'error');
        $tariffIdNotNumeric['1235_22']= array('arrProducts' => array(
            array(
                'intSdi' => 1235,
                'intTariffID' => 'nan'
            )), 'errorCode' =>  'error');
        $tariffIdNotNumeric['1235_27']= array('arrProducts' => array(
            array(
                'intSdi' => 1235,
                'intTariffID' => 'nan'
            )), 'errorCode' =>  'error');

        return array(array($sdiNotNumeric), $tariffIdNotNumeric);
    }

    /**
     * Test that valNewSdi returns validation errors intNewSdi isn't numeric
     *
     * @param array $broadbandProducts Broadband products returned for service def id
     *
     * @dataProvider provideDataForValNewSdiWhenThereAreNumericErrors
     * @covers AccountChange_SelectBroadband::valNewSdi
     *
     * @return void
     **/
    public function testValNewSdiWhenThereAreNumericErrors($broadbandProducts)
    {

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController
            = $this->getMock('AccountChange_Controller', array('isApplicationStateVar', 'getApplicationStateVar'));

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getExchangeCode'),
            array(),
            '',
            false
        );
        $lineCheckResult
            ->expects($this->any())
            ->method('getExchangeCode')
            ->will($this->returnValue('E'));

        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );
        $coreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1110));

        $selectBroadband = $this->getMock(
            'AccountChange_SelectBroadband',
            array(
                'getBroadbandProducts',
                'getFinancialErrors',
                'getLineCheckMarket'
            )
        );

        $mockLineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );
        $mockLineCheckMarket
            ->expects($this->once())
            ->method('getMarketId')
            ->will($this->returnValue(1));

        $selectBroadband
            ->expects($this->once())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($mockLineCheckMarket));

        $selectBroadband
            ->expects($this->once())
            ->method('getBroadbandProducts')
            ->will($this->returnValue($broadbandProducts));

        $financialErrors = array();

        $selectBroadband
            ->expects($this->once())
            ->method('getFinancialErrors')
            ->will($this->returnValue($financialErrors));

        $mockController
            ->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(get_class($selectBroadband), 'objCoreService')
            ->will($this->returnValue($coreService));

        $mockController
            ->expects($this->at(1))
            ->method('getApplicationStateVar')
            ->with(get_class($selectBroadband), 'objLineCheckResult')
            ->will($this->returnValue($lineCheckResult));

        // Set callback object within the requirement itself to application controller
        $selectBroadband->setAppStateCallback($mockController);
        $sdi['6755_222'] = 6755;
        $this->assertEquals(array(), $selectBroadband->valNewSdi($sdi));
        $expectedValidationError = array(
           'intNewSdi' => array(
               'INVALID' => true
            )
        );
        $this->assertEquals($expectedValidationError, $selectBroadband->getValidationErrors());
    }

    /**
     * Test that valNewSdi returns validation errors intNewSdi isn't set
     *
     * @covers AccountChange_SelectBroadband::valNewSdi
     *
     * @return void
     **/
    public function testValNewSdiWhenNotSet()
    {

        $selectBroadband = new AccountChange_SelectBroadband();

        $this->assertEquals(array('intNewSdi' => ''), $selectBroadband->valNewSdi(array()));
        $expectedValidationError = array(
           'intNewSdi' => array(
               'MISSING' => true
            )
        );
        $this->assertEquals($expectedValidationError, $selectBroadband->getValidationErrors());
    }

    public function testValCallerDisplayNotAvailableForBusiness()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadband $sut */
        $sut = $this->getMock(
            'AccountChange_SelectBroadband',
            ['getServiceDefinitionDetails'],
            [],
            '',
            false
        );

        $sut->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->willReturn(['type' => Core_ServiceDefinition::BUSINESS_ACCOUNT]);

        $this->assertEquals(
            ['callerDisplay' => false],
            $sut->valCallerDisplay(false)
        );
        $this->assertEquals(
            ['callerDisplay' => false],
            $sut->valCallerDisplay('on')
        );
    }

    public function testValCallerDisplayNotAvailableForJohnLewis()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadband $sut */
        $sut = $this->getMock(
            'AccountChange_SelectBroadband',
            ['getServiceDefinitionDetails'],
            [],
            '',
            false
        );

        $sut->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->willReturn(['type' => Core_ServiceDefinition::FAMILY_JOHNLEWIS]);

        $this->assertEquals(
            ['callerDisplay' => false],
            $sut->valCallerDisplay(false)
        );
        $this->assertEquals(
            ['callerDisplay' => false],
            $sut->valCallerDisplay('on')
        );
    }

    /**
     * @param $isDualPlay
     * @param $changeBroadbandOnly
     * @param $allowCallerDisplay
     *
     * @return void
     * @dataProvider dataProviderValCallerDisplayForPlusnetResidential
     */
    public function testValCallerDisplayForPlusnetResidential($isDualPlay, $changeBroadbandOnly, $allowCallerDisplay)
    {
        /** @var PHPUnit_Framework_MockObject_MockObject|AccountChange_SelectBroadband $sut */
        $sut = $this->getMock(
            'AccountChange_SelectBroadband',
            ['getServiceDefinitionDetails', 'getProductFamily', 'getApplicationStateVariable'],
            [],
            '',
            false
        );

        // A dummy implementation would be better, but this will do
        $state = $this->getMock('AccountChange_Controller', ['getApplicationStateVar'], [], '', false);
        $state->expects($this->any())
            ->method('getApplicationStateVar')
            ->willReturnCallback(function ($caller, $input) use ($changeBroadbandOnly) {
                if ($input == 'intOldSdi') {
                    return 2342;
                } elseif ($input == 'bolChangeBroadbandOnly') {
                    return $changeBroadbandOnly;
                }
            });
        $sut->setAppStateCallback($state);

        /** @var PHPUnit_Framework_MockObject_MockObject|ProductFamily_ProductFamily $productFamily */
        $productFamily = $this->getMock('ProductFamily_Generic', ['isDualPlay'], [], '', false);

        $sut->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->willReturn(['type' => Core_ServiceDefinition::RESIDENTIAL_ACCOUNT]);

        $sut->expects($this->any())
            ->method('getProductFamily')
            ->willReturn($productFamily);

        $productFamily->expects($this->any())
            ->method('isDualPlay')
            ->willReturn($isDualPlay);

        $result = $sut->valCallerDisplay(false);
        $this->assertArrayHasKey('callerDisplay', $result);
        $this->assertEmpty($result['callerDisplay']);

        $result = $sut->valCallerDisplay('on');
        $this->assertArrayHasKey('callerDisplay', $result);
        // Quick fix because we didn't spot the test hadn't been updated before merging to develop.
        // Some of this test can probably be removed
        $this->assertEquals(true, $result['callerDisplay']);
    }

    public function dataProviderValCallerDisplayForPlusnetResidential()
    {
        return [
            [false, false, false],
            [false, true,  false],
            [true,  false, false],
            [true,  true,  true ]
        ];
    }



    /**
     * Test describe properly set strProvisionOn in result array
     *
     * @param string $expectedProvisioningOn Expected provisioning on
     *
     * @dataProvider provideDataToTestDescribeAndDecorateProperlySetProvisioningOnInResultArray
     *
     * @covers AccountChange_SelectBroadband::describe
     *
     * @return void
     */
    public function testDescribeProperlySetProvisioningOnInResultArray(
        $expectedProvisioningOn
    ) {

        $productChangePlanClientMock = $this->getMock(
            'stdClass',
            array('getServiceDefinitionVariants')
        );

        $productChangePlanClientMock->expects($this->any())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        BusTier_BusTier::setClient('productChangePlan', $productChangePlanClientMock);

        $mockProductFamily = $this->getMock(
            'ProductFamily_Generic',
            array(),
            array(),
            '',
            false
        );

        $validatedApplicationData = array(
            'productIsp'    => 'plus.net'
        );

        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId', 'getCliNumber')
        );

        $coreService->expects($this->once())
            ->method('getCliNumber')
            ->will($this->returnValue('***********'));

        $coreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1968642));

        $coreAdslService = $this->getMock(
            'C_Core_ADSLService',
            array('isProvisionedAsADSLOrMaxDSL', 'getSupplierPlatform'),
            array()
        );

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array(
                'getLineCheckMarket',
                'isValidResult',
                'getExchangeCode',
                'getFttcUncappedDownSpeed',
            ),
            [],
            '',
            false
        );

        $lineCheckResult->expects($this->any())
            ->method('isValidResult')
            ->will($this->returnValue(true));

        AccountChange_Registry::instance()->setEntry('objLineCheckResult', $lineCheckResult);

        $mockFpcStatusService = $this->getMock(
            '\Plusnet\PriceProtected\Services\StatusService',
            array('isServicePriceProtected')
        );

        $mockFpcStatusService
            ->expects($this->once())
            ->method('isServicePriceProtected')
            ->will($this->returnValue(true));

        $objBusinessActor = $this->getMockBusinesActor();

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array(
                'getCoreADSLService',
                'getBroadbandProducts',
                'decorateProducts',
                'isProvisionedOnAdsl2',
                'getProvisionedService',
                'isProvisionedAsADSLOrMaxDSL',
                'isOldProductFibre',
                'getProductFamily',
                'getServiceDefinitionVariants',
                'getLineCheckMarket',
                'getMinAndMaxSpeedRanges',
                'overrideProductContractLengthWithPromotion',
                'getFpcStatusService',
                'isFixedPriceContractsFeatureToggleOn',
                'isC2fToggleSet',
                'isBGLCallPlansToggleSet',
                'setUpValidator',
                'checkIfAllowedToMakeChange',
                'makeChangeErrorMessage',
                'makeChangeErrorCode'
            )
        );

        $requirement->method('isBGLCallPlansToggleSet')->willReturn(false);
        $requirement->method('isC2fToggleSet')->willReturn(false);

        $mockLineCheckMarket = $this->getMock(
            'LineCheck_Market',
            array('getMarketId'),
            array(),
            '',
            false
        );
        $mockLineCheckMarket
            ->expects($this->once())
            ->method('getMarketId')
            ->will($this->returnValue(1));

        $requirement->expects($this->any())
            ->method('isFixedPriceContractsFeatureToggleOn')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->once())
            ->method('getLineCheckMarket')
            ->will($this->returnValue($mockLineCheckMarket));

        $requirement
            ->expects($this->once())
            ->method('getFpcStatusService')
            ->will($this->returnValue($mockFpcStatusService));

        $requirement->expects($this->once())
            ->method('getCoreADSLService')
            ->will($this->returnValue($coreAdslService));

        $requirement
            ->expects($this->once())
            ->method('isOldProductFibre')
            ->will($this->returnValue(true));

        $requirement->expects($this->once())
            ->method('getBroadbandProducts')
            ->will($this->returnValue(array('arrProducts' => array(
                                        array("intSdi"                         => 6755,
                                              "isFibre"                        => true,
                                              "strProductName"                 => null,
                                              "downloadSpeedRangeMgsFormatted" => null)), 'errorCode' =>  'error')));

        $requirement->expects($this->once())
            ->method('decorateProducts')
            ->will($this->returnValue(array()));

        $requirement->expects($this->once())
            ->method('overrideProductContractLengthWithPromotion')
            ->will($this->returnValue(null));

        $requirement->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(array('vchProductCode' => $expectedProvisioningOn)));

        $requirement->expects($this->once())
            ->method('isProvisionedOnAdsl2')
            ->will($this->returnValue($expectedProvisioningOn != 'Adsl'));

        $requirement->expects($this->any())
            ->method('getProductFamily')
            ->will($this->returnValue($mockProductFamily));

        $requirement
            ->expects($this->once())
            ->method('getServiceDefinitionVariants')
            ->will($this->returnValue(array()));

        $requirement
            ->expects($this->any())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $requirement
            ->expects($this->atLeastOnce())
            ->method('setUpValidator')
            ->will(
                $this->returnValue(
                    $validationCheck
                )
            );

        $requirement
            ->expects($this->atLeastOnce())
            ->method('checkIfAllowedToMakeChange')
            ->will($this->returnValue(true));

        $requirement
            ->expects($this->atLeastOnce())
            ->method('makeChangeErrorMessage')
            ->will($this->returnValue(false));

        $requirement
            ->expects($this->atLeastOnce())
            ->method('makeChangeErrorCode')
            ->will($this->returnValue('error'));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar')
        );

        $controller->expects($this->any())
            ->method('getApplicationStateVar')
            ->willReturnCallback(function ($callingClass, $strInputName) use ($coreService, $lineCheckResult) {
                switch ($strInputName) {
                    case 'objCoreService':
                        return $coreService;
                    case 'intOldSdi':
                        return 6755;
                    case 'objLineCheckResult':
                        return $lineCheckResult;
                    case 'intDiscountLength':
                        return 0;
                    case 'campaignCode':
                        return $coreService;
                    case 'wlrStatus':
                        return 'active';
                }
            });

        $mockCoreDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockCoreDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will(
                $this->returnValue(
                    array(
                        'service_definition_id' => 1234,
                        'provisioningProfile' => null
                    )
                )
            );

        Db_Manager::setAdaptor('Core', $mockCoreDbAdaptor);

        $requirement->setAppStateCallback($controller);

        $expectedResult = array(
            'bolProvisioned'                      => 0,
            'arrNewProducts'                      => null,
            'intDiscountLength'                   => 0,
            'objLinecheckResult'                  => $lineCheckResult,
            'strTelephoneNo'                      => '***********',
            'strProvisionOn'                      => $expectedProvisioningOn,
            'strAccountType'                      => null,
            'bolHaveHomePhone'                    => true,
            'oldProductFibre'                     => true,
            'currentProductIsSolusDualplay'       => null,
            'broadbandProductVariants'            => array('current' => 6755),
            'bolBBPhoneChoice'                    => true,
            'currentProductDownloadSpeedRangeMin' => null,
            'currentProductDownloadSpeedRangeMax' => null,
            'isCurrentlyPriceProtected'           => true,
            'currentProduct'                      => array(
                'intSdi'          => 6755,
                'isFibre'         => true,
                'productFamily'   => $mockProductFamily,
                'hasPhone'        => false,
                'strProductName'  => null,
                'wlr'             => null,
                'isDual'          => true,
                'intTariffID'     => null,
                'downloadSpeedRangeMgsFormatted' => null,
                'priceWithLineRental' => '&pound;19.79'
            ),
            'displayCallerDisplay'                => false,
            'bolWlrChangeAllowed'                 => true,
            'makeChangeErrorMessage'              => false,
            'makeChangeErrorCode'                 => 'error',
            'resPricingDrop2Date' => null,
            'showResPricing' => true,
            'filterBroadbandProducts' => true,
        );

        //mock Core_Service
        $objCoreService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array()
        );

        $objCoreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1110));

        $objCoreService->setDao(new Core_ServiceDao());
        $objCoreService->setServiceId(1110);

        $accountInstance = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation'),
            array(),
            '',
            false
        );

        $wlrInformation = array(
            'bolWlrAddAllowed' => 1
        );

        $accountInstance->expects($this->once())
            ->method('getWlrInformation')
            ->will($this->returnValue($wlrInformation));

        AccountChange_Account::setInstance($accountInstance);

        $result = $requirement->describe($validatedApplicationData);

        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test decorateProducts properly set strProvisionOn in result array
     *
     * @param string $expectedProvisioningOn Expected provisioning on
     * @param array  $products               Products details array
     *
     * @dataProvider provideDataToTestDescribeAndDecorateProperlySetProvisioningOnInResultArray
     *
     * @covers AccountChange_SelectBroadband::decorateProducts
     *
     * @return void
     */
    public function testDecorateProductsProperlySetProvisioningOnInResultArray(
        $expectedProvisioningOn,
        $products
    ) {
        $serviceId = 1968642;

        $productProvision = array(
            'bolWbcProduct'  => true,
            'strProvisionOn' => $expectedProvisioningOn
        );

        $rules = $this->getMock(
            'AccountChange_ProductRules',
            array('getProductProvisionForService'),
            array()
        );

        $rules
            ->expects($this->any())
            ->method('getProductProvisionForService')
            ->will($this->returnValue($productProvision));

        AccountChange_ProductRules::setInstance($rules);

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array('getLineCheckMarket'),
            [],
            '',
            false
        );

        $requirement = $this->getMock(
            'AccountChange_SelectBroadband',
            array('isProvisionedOnAdsl2', 'getProvisionedService', 'isBusiness')
        );

        $requirement->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(array('vchProductCode' => $expectedProvisioningOn)));

        $requirement->expects($this->once())
            ->method('isProvisionedOnAdsl2')
            ->will($this->returnValue($expectedProvisioningOn != 'Adsl'));

        $requirement
            ->expects($this->once())
            ->method('isBusiness')
            ->will($this->returnValue(true));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar')
        );

        $controller->expects($this->at(0))
            ->method('getApplicationStateVar')
            ->with(get_class($requirement), 'objLineCheckResult')
            ->will($this->returnValue($lineCheckResult));

        $requirement->setAppStateCallback($controller);

        $expectedResult = array(
            $products[0]['intSdi'] => array(
                'strProvisionOn'    => $expectedProvisioningOn,
                'bolWbcProduct'     => true,
                'intSdi'            => $products[0]['intSdi'],
                'isBizADSLCapped' => false,
                'isBizFibreCapped' => false
            )
        );

        $result = $requirement->decorateProducts($products, $serviceId);

        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Provide data to test describe and decorate properly set provisioningOn
     * in result array
     *
     * @return array
     */
    public function provideDataToTestDescribeAndDecorateProperlySetProvisioningOnInResultArray()
    {
        return array(
            array(
                'expectedProvisioningOn' => 'FTTC',
                'products'               => array(
                    array('intSdi' => 6784)
                )
            ),
            array(
                'expectedProvisioningOn' => 'Adsl2',
                'products'               =>  array(
                    array('intSdi' => 6754)
                )
            ),
            array(
                'expectedProvisioningOn' => 'Adsl',
                'products'               =>  array(
                    array('intSdi' => 6569)
                )
            ),
        );
    }

    /**
     * @throws Exception
     */
    public function testOverrideProductDefaultContractLengthWithPromotionNoC2mContractLength()
    {
        $requirement = new AccountChange_SelectBroadband();
        $product = $requirement->overrideProductContractLengthWithPromotion(array(
                array('presetDiscount' => array('intDiscountLength' => '12'))
            ));

        $this->assertTrue(array_key_exists('defaultContractLength', $product[0]));
        $this->assertEquals($product[0]['defaultContractLength'], '12MONTH');
    }

    /**
     * @expectedException Exception
     * @expectedExceptionMessage A preset discount exists, but it has no contract length.
     */
    public function testShouldThrowExceptionInOverrideProductContractLengthWithPromotionIfPresetDiscountMissingContractLengthNoC2mContractLength()
    {
        $requirement = new AccountChange_SelectBroadband();
        $requirement->overrideProductContractLengthWithPromotion(array(
                array('presetDiscount' => array('intDiscountLength' => null))
            ));
    }

    /**
     * @expectedException Exception
     * @expectedExceptionMessage A preset discount exists, but it has no contract length.
     */
    public function testShouldThrowExceptionInOverrideProductContractLengthWithPromotionIfPresetDiscountMissingContractLengthKeyNoC2mContractLength()
    {
        $requirement = new AccountChange_SelectBroadband();
        $requirement->overrideProductContractLengthWithPromotion(array(
                array('presetDiscount' => array('foo' => null))
            ));
    }

    /**
     * @throws Exception
     */
    public function testOverrideProductDefaultContractLengthWithPromotion()
    {
        $requirement = new AccountChange_SelectBroadband();
        $product = $requirement->overrideProductContractLengthWithPromotion(array(
                array('presetDiscount' => array('c2mDiscountRequiredContractLength' => '12'))
            ));

        $this->assertTrue(array_key_exists('defaultContractLength', $product[0]));
        $this->assertEquals($product[0]['defaultContractLength'], '12MONTH');
    }

    /**

    /**
     * Tests that getC2mDiscountRequiredContractLengthFromDiscount returns contract length where set
     *
     * @return void
     **/
    public function testGetC2mDiscountRequiredContractLengthFromDiscountPullsBackRequiredContractLengthWhereSet()
    {
        $contractLength = 18;
        $requirement = Mockery::mock('AccountChange_SelectBroadband');
        $requirement->makePartial();
        $requirement->shouldAllowMockingProtectedMethods();
        $presetDiscount = array(
            'c2mDiscountRequiredContractLength' => $contractLength,
            'someOtherKey'                      => 'otherValue'
        );

        $this->assertEquals(
            $contractLength,
            $requirement->getC2mDiscountRequiredContractLengthFromDiscount($presetDiscount)
        );
    }


    /**
     * Tests that addPromotionEndDateAndEndPrice adds a broadband promotion when one is set
     *
     * @return array
     **/
    public function testAddPromotionEndDateAndEndPriceAddsBroadbandPromotion()
    {
        $discountLength = 10;
        $priceWithLineRental = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 2000);
        $invoiceDate = I18n_Date::fromString('2020-01-01');
        $discountEndDate = 'November 1st 2020';
        $presetDiscount = array(
            'decValue'          => 100,
            'intDiscountLength' => $discountLength,
        );

        $expectedResult = array(
            'priceWithLineRental' => $priceWithLineRental,
            'presetDiscount'      => $presetDiscount,
            'supplementaryDiscountData' => array('priceSansDiscount' => 2100, 'endDiscountDate' => $discountEndDate)
        );

        $requirement = Mockery::mock('AccountChange_SelectBroadband');
        $requirement->makePartial();
        $requirement->shouldAllowMockingProtectedMethods();


        $newProduct = array(
            'priceWithLineRental' => $priceWithLineRental,
            'presetDiscount'      => $presetDiscount,
        );

        $coreService = Mockery::mock('Core_Service');
        $coreService->makePartial();
        $coreService->shouldAllowMockingProtectedMethods();
        $coreService->shouldReceive('getNextInvoiceDate')->andReturn($invoiceDate);

        $result = $requirement->addPromotionEndDateAndEndPrice($newProduct, $coreService);
        $this->assertEquals($expectedResult, $result);
    }


    /**
     * Tests that addPromotionEndDateAndEndPrice adds a line rental promotion when one is set
     *
     * @return array
     **/
    public function testAddPromotionEndDateAndEndPriceAddsLineRentalPromotion()
    {
        $discountLength = 10;
        $priceWithLineRental = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 3000);
        $priceWithoutLineRental = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 1000);
        $invoiceDate = I18n_Date::fromString('2020-01-01');
        $discountEndDate = 'November 1st 2020';
        $presetDiscount = array(
            'decValue'                    => 100,
            'lineRentalDiscountValue'     => 200,
            'lineRentalPromoDiscountType' => AccountChange_DiscountHelper::FIXED_DISCOUNT,
            'intDiscountLength'           => $discountLength,
        );

        /*
         * The expected priceSansDiscount is made up in this case of:
         *  decValue (the broadband discount)   +
         *  lineRentalDiscountValue +
         *  priceWithLineRental
         *
         * i.e the full retail price for bb + line rent before any discounts
         */
        $expectedResult = array(
            'priceWithLineRental'       => $priceWithLineRental,
            'priceWithoutLineRental'    => $priceWithoutLineRental,
            'presetDiscount'            => $presetDiscount,
            'supplementaryDiscountData' => array('priceSansDiscount' => 3300, 'endDiscountDate' => $discountEndDate)
        );

        $requirement = Mockery::mock('AccountChange_SelectBroadband');
        $requirement->makePartial();
        $requirement->shouldAllowMockingProtectedMethods();


        $newProduct = array(
            'priceWithLineRental' => $priceWithLineRental,
            'priceWithoutLineRental'    => $priceWithoutLineRental,
            'presetDiscount'      => $presetDiscount,
        );

        $coreService = Mockery::mock('Core_Service');
        $coreService->makePartial();
        $coreService->shouldAllowMockingProtectedMethods();
        $coreService->shouldReceive('getNextInvoiceDate')->andReturn($invoiceDate);

        $result = $requirement->addPromotionEndDateAndEndPrice($newProduct, $coreService);

        $this->assertEquals($expectedResult, $result);
    }


    /**
     * Tests that getC2mDiscountRequiredContractLengthFromDiscount returns false where contract length not set
     *
     * @return void
     **/
    public function testGetC2mDiscountRequiredContractLengthFromDiscountReturnsFalseWhereNoContractLenghtSet()
    {
        $requirement = Mockery::mock('AccountChange_SelectBroadband');
        $requirement->makePartial();
        $requirement->shouldAllowMockingProtectedMethods();
        $presetDiscount = array(
            'someOtherKey'                      => 'otherValue'
        );

        $this->assertFalse($requirement->getC2mDiscountRequiredContractLengthFromDiscount($presetDiscount));
    }


    /*
     * Get a mock business actor object
     *
     * @return Auth_BusinessActor
     */
    private function getMockBusinesActor()
    {
        $objMockAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAdaptor->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objMockAdaptor, Db_Manager::DEFAULT_TRANSACTION);

        $objBusinessActor = Auth_BusinessActor::get(1);
        $objBusinessActor->setRealm('workplace.plus.net');
        $objBusinessActor->setUserType('PLUSNET_ENDUSER');
        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);

        return $objBusinessActor;
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getBusinessActorData()
    {
        return array(
            array(
                'intActorId'        => 1,
                'strUsername'       => 'testuser',
                'strRealm'          => 'plusnet',
                'strUserType'       => '',
                'strExternalUserId' => '12345'
            )
        );
    }
}
