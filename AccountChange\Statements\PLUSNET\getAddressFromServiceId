server: coredb
role: slave
rows: single
statement:

SELECT a.address_id,
       a.customer_id,
       a.house,
       a.street,
       a.town,
       a.county,
       a.postcode,
       a.addressReference,
       a.cssDatabaseCode
    FROM userdata.addresses AS a
    INNER JOIN userdata.users     AS u
    ON a.address_id = u.address_id
    INNER JOIN userdata.services  AS s
    ON s.user_id    = u.user_id
    WHERE s.service_id = :serviceId