<?php
include_once 'phing/Task.php';
class RegExpSearch extends Task {

    private $_strFile = null;
    private $_strPattern = null;
    private $_strReturnName;
    
    public function setFile($strFile) {
        $this->_strFile = $strFile;
    }
    
    public function setPattern($strPattern) {
        $this->_strPattern = $strPattern;
    }
    
    public function setReturnName($strName) {
        $this->_strReturnName = $strName;
    }
    
    public function init() {
      // nothing to do here
    }
    
    /**
     * The main entry point method.
     */
    public function main() {

		if (!$this->_strFile) {
		    throw new BuildException("You must specify the file attribute", $this->getLocation());
		}      
		if (!$this->_strPattern) {
		    throw new BuildException("You must specify the pattern attribute", $this->getLocation());
		}      
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}      

		if (!$arrFile = file($this->_strFile)) {
			throw new BuildException("Cannot load $this->_strFile", $this->getLocation());      	
		}

		foreach ($arrFile as $strLine) {

    	  	if(preg_match($this->_strPattern, $strLine)) {
      			
				$this->project->setProperty($this->_strReturnName, "true");
      			return;
    	  	}
		}
    }
}