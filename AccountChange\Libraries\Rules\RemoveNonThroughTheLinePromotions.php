<?php
require_once(__DIR__.'/PromotionRule.php');
require_once(__DIR__.'/PromotionRuleBase.php');

use Plusnet\C2mApiClient\Entity\Promotion;

class RemoveNonThroughTheLinePromotions extends PromotionRuleBase implements PromotionRule
{
    /**
     * @param array<Promotion> $promotions
     *
     * @return array
     */
    public function handle(array $promotions)
    {
        return array_filter(
          $promotions,
          [$this, 'filterOutNonThroughTheLinePromotions']
        );
    }

    /**
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     *
     * @return bool
     */
    private function filterOutNonThroughTheLinePromotions(Promotion $promotion)
    {
        return $promotion->getIsVisibleOnSalesHomePage();
    }
}
