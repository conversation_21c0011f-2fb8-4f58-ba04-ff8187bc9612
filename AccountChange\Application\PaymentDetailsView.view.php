<?php
/**
 * AccountChange_PaymentDetailsView
 *
 * @package   AccountChange
 * <AUTHOR> <kp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 * @copyright 2008 PlusNet
 */
/**
 * AccountChange_PaymentDetailsView class
 *
 * There are 5 sections on Payment details screen:
 * 1) DdDetailsReadOnlySection - to show DD details that are currently on the account
 * 2) DdDetailsFormSection - to show form for new DD details
 * 3) UseExistingCcDetailsSection - a check box to select if we want to use existing CC details
 * 4) CcDetailsReadOnlySection - to show CC details
 * 5) CcDetailsForm - to show CC form
 *
 *  Based on :
 *  1) $arrInput (collected data)
 *  2) $this->objSmarty->getErrors()
 *  we have to figureout which section we need to display.
 *
 *  Summary :
 *  Rules what to display and what not are not trivial,
 *  we use about 7 variables from $arrInput to calculate  5 (+2 dates) variables
 *
 * @package   AccountChange
 * <AUTHOR> <kp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 * @copyright 2008 PlusNet
 */
class AccountChange_PaymentDetailsView extends Mvc_View
{
    /**
     * Process Input ready for the view
     *
     * @param array $arrInput Data sent to the template
     *
     * @return array
     */
    protected function processInput(array $arrInput)
    {
        $bolNoErrors = (count($this->objSmarty->getErrors()) === 0);

        // Sections to display - all off by default
        $bolDdDetailsReadOnlySection = false;

        // Some temporary variables to init
        $bolAddingOrChagingHomePhone = false;
        $ddDetails = $arrInput['arrDirectDebitDetails'];
        $strPaymentDetailsValid = 'no';
        // Is the customer switching from a Greenbee/Waitrose product to a John Lewis product?
        if (isset($arrInput['intOldSdi']) && isset($arrInput['intNewSdi'])) {
            $isCustomerSwitchingFromGbWrToJlp = AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJlp(
                $arrInput['intOldSdi'],
                $arrInput['intNewSdi']
            );

            // As we can only switch from Bauer products to Plus.net, we can assume that if the SDIs
            // differ then we are switching to Plus.net
            $isCustomerSwitchingFromBauToPn = (strpos($arrInput['productIsp'], 'bauer') === 0
                && $arrInput['intOldSdi'] != $arrInput['intNewSdi']);
        } else {
            // If there are no broadband changes then assume that customers is not switching from a
            // Greenbee/Waitrose product to a John Lewis product because GB/WR to JLP is dual-play.
            $isCustomerSwitchingFromGbWrToJlp = false;
            $isCustomerSwitchingFromBauToPn = false;
        }

        // If we adding or changing Home Phone - we have to force user to add DD
        if (!empty($arrInput['arrWlrProduct']['intOldWlrId']) || !empty($arrInput['intNewWlrId'])) {
            $bolAddingOrChagingHomePhone = true;
        }

        $bolDdInstructionActive = !empty($ddDetails);
        if ($bolNoErrors || $bolDdInstructionActive) {
            $strPaymentDetailsValid = 'yes';
        }

        // Now let's decide which section to add

        // Existing DD details - only show if adding home phone, upgrading to JLP or BAU
        if ($bolDdInstructionActive
            && ($bolAddingOrChagingHomePhone || $isCustomerSwitchingFromGbWrToJlp || $isCustomerSwitchingFromBauToPn)
        ) {
            // Show read only DD details if adding Home Phone and not upgrading to JLP or PN (from BAU)
            if ($bolAddingOrChagingHomePhone &&
                !$isCustomerSwitchingFromGbWrToJlp &&
                !$isCustomerSwitchingFromBauToPn
            ) {
                $bolDdDetailsReadOnlySection = true;
            }

            $arrInput['strDdName'] = $ddDetails['name'];
            $arrInput['strDdAccountNumber'] = $ddDetails['accountNumber'];
            $arrInput['strDdSortCode'] = $ddDetails['sortCode'];
        }

        // return sections data in arrInput which is passed by reference
        $arrInput['bolDdDetailsReadOnlySection'] = $bolDdDetailsReadOnlySection;
        $arrInput['strPaymentDetailsValid'] = $strPaymentDetailsValid;
        $arrInput['isCustomerSwitchingFromGbWrToJlp'] = $isCustomerSwitchingFromGbWrToJlp;
        $arrInput['isCustomerSwitchingFromBauToPn'] = $isCustomerSwitchingFromBauToPn;

        return $arrInput;
    }

    /**
     * Return bool false if no errors have occured
     *
     * @return bool
     */
    protected function noErrorrs()
    {
        return (count($this->objSmarty->getErrors()) === 0);
    }
}
