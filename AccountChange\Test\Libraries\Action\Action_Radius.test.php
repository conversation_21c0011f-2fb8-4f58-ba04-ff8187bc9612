<?php
require_once '/local/data/mis/database/database_libraries/radius-access.inc';
/**
 * Action Radius
 *
 * Testing class for the AccountChange_Action_Billing class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Action_Radius.test.php,v 1.2 2009-01-27 09:07:37 bselby Exp $
 * @since      File available since 2008-09-02
 */
/**
 * Action Radius Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Action_Radius_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Service Id
     *
     * @var int
     */
    protected $intServiceId;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        $this->intServiceId = 999;
    }

    /**
     * PHPUnit TearDown function
     *
     */
    public function tearDown()
    {
        Lib_Radius::resetInstance();
    }

    /**
     * @covers AccountChange_Action_Radius::execute
     *
     */
    public function testExecuteCallsCorrectSubActions()
    {
        $objMock = $this->getMock('AccountChange_Action_Radius',
                                  array('setRadiusData', 'resetToDefaultSpeed'),
                                  array($this->intServiceId));

        $objMock->expects($this->once())
                       ->method('setRadiusData');

        $objMock->expects($this->once())
                       ->method('resetToDefaultSpeed');

        $objMock->execute();
    }

    /**
     * @covers AccountChange_Action_Radius::resetToDefaultSpeed
     *
     */
    public function testCancelDataTransferWatchCallsLegacyFunction()
    {
        // Payment Limit Splitter Mock
        $objRadiusSplitter = $this->getMock('Lib_Radius',
                                            array('RadiusResetToDefaultSpeed'),
                                            array(), '', false);

        $objRadiusSplitter->expects($this->once())
                                ->method('RadiusResetToDefaultSpeed');

        Lib_Radius::setInstance($objRadiusSplitter);

        $objMock = $this->getMock('AccountChange_Action_Radius',
                                  array('includeLegacyFiles', 'setRadiusData'),
                                  array($this->intServiceId));

        $objMock->expects($this->once())
                ->method('includeLegacyFiles');

        $objMock->expects($this->once())
                ->method('setRadiusData');

        $objMock->execute();
    }
}