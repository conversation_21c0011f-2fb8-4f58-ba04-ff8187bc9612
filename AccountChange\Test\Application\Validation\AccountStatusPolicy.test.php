<?php
/**
 * <AUTHOR>
 */

class AccountChange_AccountStatusPolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;

    /**
     * @dataProvider testValidatorFailsValidationForInvalidStatusDataProvider
     * @param string $status account status
     * @return void
     * @throws PHPUnit_Framework_AssertionFailedError
     */
    public function testValidatorFailsValidationForInvalidStatus($status)
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getStatus')->once()->andReturn($status);

        $validator = Mockery::mock('AccountChange_AccountStatusPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldR<PERSON>eive('getCoreService')->with(self::SERVICE_ID)
            ->once()
            ->andReturn($service);

        $this->assertFalse($validator->validate());
        $expectedError = sprintf(AccountChange_AccountStatusPolicy::ERROR_MESSAGE, $status);
        $this->assertEquals($expectedError, $validator->getFailure());
    }

    /**
     * @return array
     */
    public function testValidatorFailsValidationForInvalidStatusDataProvider()
    {
        return [
            ['queued-destroy'],
            ['destroyed'],
        ];
    }

    /**
     * @dataProvider testValidatorPassesValidationForValidStatusDataProvider
     * @param string $status account status
     * @return void
     * @throws PHPUnit_Framework_AssertionFailedError
     */
    public function testValidatorPassesValidationForValidStatus($status)
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')
            ->andReturn(self::SERVICE_ID);

        $service = Mockery::mock();
        $service->shouldReceive('getStatus')
            ->once()
            ->andReturn($status);

        $validator = Mockery::mock('AccountChange_AccountStatusPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')
            ->with(self::SERVICE_ID)
            ->once()
            ->andReturn($service);

        $this->assertTrue($validator->validate());
    }

    /**
     * @return array
     */
    public function testValidatorPassesValidationForValidStatusDataProvider()
    {
        return [
            ['active'],
            ['queued-activate'],
            ['queued-reactivate'],
            ['queued-deactivate'],
            ['deactive'],
            ['unconfigured'],
            ['invalid'],
            ['presignup']
        ];
    }


    /**
     * @return void
     * @throws PHPUnit_Framework_AssertionFailedError
     */
    public function testValidatorFailsValidationOnException()
    {
        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $validator = Mockery::mock('AccountChange_AccountStatusPolicy', [$actor]);
        $validator->makePartial();
        $validator->shouldAllowMockingProtectedMethods();

        $validator->shouldReceive('getCoreService')->with(self::SERVICE_ID)
            ->once()
            ->andThrow(new Exception('No likey, no lighty'));

        $this->assertFalse($validator->validate());
        $this->assertEquals(AccountChange_AccountStatusPolicy::EXCEPTION_MESSAGE, $validator->getFailure());
    }
}
