server: coredb
role: slave
rows: single
statement:

SELECT
    scs.schedule_id AS intScheduleId,
    scs.change_date AS dteChangeDate,
    scs.old_type AS intOldServiceDefinitionId,
    scs.new_type AS intNewServiceDefinitionId,
    scs.intPromotionCodeId AS intPromoCode,
    sd.name AS strNewProductName,
    pp.vchName AS strProvisioningProfile,
    scs.new_type AS intNewSdi,
    t.intTariffID AS tariffId,
    t.intCostIncVatPence / 100 AS decCostPound,
    pc.vchPromotionCode AS strPromoCode
FROM
    userdata.service_change_schedule AS scs
INNER JOIN products.service_definitions AS sd
    ON sd.service_definition_id = scs.new_type
LEFT JOIN products.tblPromotionCode pc
    ON scs.intPromotionCodeId = pc.intPromotionCodeId
LEFT JOIN products.adsl_product AS ap
    ON sd.service_definition_id = ap.service_definition_id
LEFT JOIN products.tblProvisioningProfile AS pp
    ON pp.intProvisioningProfileID = ap.intProvisioningProfileID
INNER JOIN dbProductComponents.tblTariff AS t
   ON t.intTariffID = scs.intTariffID
WHERE scs.active = 'yes'
  AND scs.change_complete = 'no'
  AND service_id = :serviceId
