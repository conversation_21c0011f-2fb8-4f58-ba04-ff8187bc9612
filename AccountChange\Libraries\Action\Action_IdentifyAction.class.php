<?php
/**
 * Account Change Identify Action
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @link       http://www.plus.net/
 */
/**
 * AccountChange_Action_IdentifyAction
 *
 * Class used to identify the action required to perfrom
 * whether providePfm or placeModify or no action required
 *
 * @package    AccountChange
 * @subpackage Action
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @link       http://www.plus.net/
 */
class AccountChange_Action_IdentifyAction extends AccountChange_Action
{
    const ACTION_MODIFY = 'Modify';
    const ACTION_PROVIDE_PFM = 'ProvidePFM';

    /**
     * Actions that require placing orders
     *
     * @var array
     */
    private $actionsThatRequireOrderPlacement = array(
        self::ACTION_MODIFY,
        self::ACTION_PROVIDE_PFM
    );

    /**
     * Checks if the action name requires an order to be placed with BT
     *
     * @param string $actionName Action name to check
     *
     * @return bool
     */
    public function isOrderPlacementRequiredForAction($actionName)
    {
        return in_array($actionName, $this->actionsThatRequireOrderPlacement);
    }

    /**
     * Execute action does nothing in this case as it's not a specifically
     * executable action
     *
     * @return void
     **/
    public function execute()
    {
        return;
    }

    /**
     * Determine the action required to perform,
     * whether providePfm or placeModify or no action required
     *
     * @return string $action
     */
    public function getActionRequired()
    {
        // THIS ENTIRE CLASS IS WRONG
        // We should check if the user is currently provisionded on
        // IPStream and is migrating to WBC
        // That is all - NO NEED TO CHECK FOR WHICH SPECIFIC PRODUCT
        // Unfortunately the state of the codebase and supplier product DB
        // means that this is the only way to do it, and it looks like
        // it will do the right thing ... for now.
        // FIX IN RES-616
        $action = false;
        $bolProvisionedOnFttc = $this->isProvisionedOnFttc();
        $bolProvisionedOnAdsl2 = $this->isProvisionedOnAdsl2();
        $bolProvisionedOnAdsl1Over21Cn = $this->isProvisionedOnAdsl1Over21Cn();
        $bolAdsl1Over21Cn = $this->isAdsl1Over21Cn();
        $bolFttc = $this->isFttc();
        $bolAdsl2 = $this->isAdsl2();

        $currentPlatform = $this->adslGetProvisionedService();

        $newSupplierProduct = $this->getSupplierProduct();

        // If the product code isn't going to change, see if the supplier product is going to as
        // we could be changing some aspect of the same service with a modify order, e.g. downstream
        // FTTC from 40Mbit/s to 80Mbit/s
        if ($bolProvisionedOnFttc && $bolFttc) {
            if ($this->isModifyOrderRequired($currentPlatform, $newSupplierProduct)) {
                $action = self::ACTION_MODIFY;
            } else {
                $action = false;
            }
        } elseif (!$bolProvisionedOnAdsl2 && !$bolAdsl1Over21Cn && $bolAdsl2) {
            //Handling 20Cn to 21Cn, excluding Adsl1 over 21CN
            $action = self::ACTION_PROVIDE_PFM;
        } elseif (($bolProvisionedOnAdsl2 && $bolFttc) ||
            //Handling 21Cn to Fibre, including Adsl1 over 21Cn to Adsl2
            (
                $bolProvisionedOnAdsl1Over21Cn &&
                $bolAdsl2 &&
                $currentPlatform['vchProductCode'] != $newSupplierProduct->getProductCode()->getValue()
            )
        ) {
            $action = self::ACTION_MODIFY;
        } elseif ($bolProvisionedOnFttc && $bolAdsl2) {
           //Handling downgrade Fibre to Adsl2
            $action = self::ACTION_MODIFY;
        }

        return $action;
    }

    /**
     * Check whether the new supplier product is Adsl1 over 21Cn
     *
     * @return bool
     */
    protected function isAdsl1Over21Cn()
    {
        return Product_SupplierProduct::WBC_ADSL1_PRODUCT_CODE ==
            $this->getSupplierProduct()->getProductCode()->getValue();
    }

    /**
     * Check whether the new supplier product is FTTC
     *
     * @return bool
     */
    protected function isFttc()
    {
        return Product_SupplierProduct::WBC_FTTC_PRODUCT_CODE ==
            $this->getSupplierProduct()->getProductCode()->getValue();
    }

    /**
     * Check whether the new supplier platform is Adsl2
     *
     * @return bool
     */
    protected function isAdsl2()
    {
        return 'BT21CN' == $this->getSupplierPlatform()->getHandle()->getValue();
    }

    /**
     * Accessor for supplier platform
     *
     * @return Product_SupplierPlatform
     */
    protected function getSupplierPlatform()
    {
        return $this->getSupplierProductRules()->getSupplierPlatform();
    }

    /**
     * We do not want to re-provision on adsl2 if the customer
     *
     * is already on this network
     *
     * @return boolean
     */
    protected function isProvisionedOnAdsl2()
    {
        $this->includeLegacyFiles();
        $currentPlatform = $this->adslGetProvisionedService();

        return isset($currentPlatform['vchSupplierPlatform']) &&
            'BT21CN' == $currentPlatform['vchSupplierPlatform'];
    }

    /**
     * Check whether it is Provisioned on FTTC
     *
     * @return boolean
     */
    protected function isProvisionedOnFttc()
    {
        $this->includeLegacyFiles();
        $currentPlatform = $this->adslGetProvisionedService();

        return isset($currentPlatform['vchProductCode']) &&
            Product_SupplierProduct::WBC_FTTC_PRODUCT_CODE == $currentPlatform['vchProductCode'];
    }

    /**
     * Check whether the customer is provisioned on Adsl1 over 21CN
     *
     * @return bool
     */
    protected function isProvisionedOnAdsl1Over21Cn()
    {
        $this->includeLegacyFiles();
        $currentPlatform = $this->adslGetProvisionedService();

        return isset($currentPlatform['vchProductCode']) &&
            Product_SupplierProduct::WBC_ADSL1_PRODUCT_CODE == $currentPlatform['vchProductCode'];
    }

    /**
     * Wrapper function for calling the legacy code
     *
     * @return array
     */
    protected function adslGetProvisionedService()
    {
        return adslGetProvisionedService($this->intServiceId);
    }

    /**
     * Checks if a modify order is required.
     *
     * @param array                   $currentPlatform    Current platform and speed details
     * @param Product_SupplierProduct $newSupplierProduct New Supplier Product
     *
     * @return bool
     */
    protected function isModifyOrderRequired(array $currentPlatform, Product_SupplierProduct $newSupplierProduct)
    {
        $isSupplierProductIdChanging =
            isset($currentPlatform['intSupplierProductID'])
            && ($newSupplierProduct->getSupplierProductId()->getValue() != $currentPlatform['intSupplierProductID']);

        $isProductCodeChanging =
            isset($currentPlatform['vchProductCode'])
            && ($newSupplierProduct->getProductCode()->getValue() != $currentPlatform['vchProductCode']);

        $currentMaxDownstream = isset($currentPlatform['intMaxDownstream']) ? $currentPlatform['intMaxDownstream'] : null;
        $currentMaxUpstream = isset($currentPlatform['vchMaxUpstream'])? $currentPlatform['vchMaxUpstream'] : null;

        $isSpeedChanging = ($currentMaxDownstream != $newSupplierProduct->getMax()->getValue())
            || ($currentMaxUpstream != $newSupplierProduct->getMaxUpStream()->getValue());

        return ($isSupplierProductIdChanging && ($isProductCodeChanging || $isSpeedChanging));
    }
}
