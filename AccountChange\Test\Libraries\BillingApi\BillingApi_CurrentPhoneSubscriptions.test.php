<?php

use Plusnet\BillingApiClient\Entity\CustomerProductSubscription;

class AccountChange_BillingApi_CurrentPhoneSubscriptions_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Tests that we can construct object with two nulls
     *
     * @return void
     */
    public function testsConstructorWithTwoNullConstructorParams()
    {
        $currentPhoneSubscriptions = new AccountChange_BillingApi_CurrentPhoneSubscriptions(null, null);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $currentPhoneSubscriptions);
        $this->assertNull($currentPhoneSubscriptions->getCurrentLineRentalSubscription());
        $this->assertNull($currentPhoneSubscriptions->getCurrentCallPlanSubscription());
    }

    /**
     * Tests that we can construct object with two valid CustomerProductSubscriptions
     *
     * @return void
     */
    public function testsConstructorWithTwoValidConstructorParams()
    {
        $customerProductSubscription1 = new CustomerProductSubscription();
        $customerProductSubscription1->setProductName('Prod1');
        $customerProductSubscription2 = new CustomerProductSubscription();
        $customerProductSubscription2->setProductName('Prod2');

        $currentPhoneSubscriptions = new AccountChange_BillingApi_CurrentPhoneSubscriptions(
            $customerProductSubscription1,
            $customerProductSubscription2);

        $this->assertInstanceOf('AccountChange_BillingApi_CurrentPhoneSubscriptions', $currentPhoneSubscriptions);
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $currentPhoneSubscriptions->getCurrentLineRentalSubscription());
        $this->assertInstanceOf('Plusnet\BillingApiClient\Entity\CustomerProductSubscription', $currentPhoneSubscriptions->getCurrentCallPlanSubscription());
        $this->assertEquals('Prod1', $currentPhoneSubscriptions->getCurrentLineRentalSubscription()->getProductName());
        $this->assertEquals('Prod2', $currentPhoneSubscriptions->getCurrentCallPlanSubscription()->getProductName());
    }

    /**
     * Tests that we get the right exception and message if the first constructor parameter is not a
     * CustomerProductSubscription
     *
     * @return void
     */
    public function testsConstructorWithInvalidFirstConstructorParam()
    {
        $customerProductSubscription = new CustomerProductSubscription();
        $customerProductSubscription->setProductName('Product');

        $this->setExpectedException('AccountChange_BillingApi_SubscriptionHelperException',
            'currentLineRentalSubscription is not of type CustomerProductSubscription');

        new AccountChange_BillingApi_CurrentPhoneSubscriptions('A string', $customerProductSubscription);
    }

    /**
     * Tests that we get the right exception and message if the second constructor parameter is not a
     * CustomerProductSubscription
     *
     * @return void
     */
    public function testsConstructorWithInvalidSecondConstructorParam()
    {
        $customerProductSubscription = new CustomerProductSubscription();
        $customerProductSubscription->setProductName('Product');

        $this->setExpectedException('AccountChange_BillingApi_SubscriptionHelperException',
            'currentCallPlanSubscription is not of type CustomerProductSubscription');

        new AccountChange_BillingApi_CurrentPhoneSubscriptions($customerProductSubscription, 'A string');
    }
}
