<?php
require_once(__DIR__ . '/../Libraries/MapProduct.php');


/**
 * Create the association/link between  a C2M ProductOffering
 * and an SDI product.
 *
 * Class C2MProductAdapter
 */
class ServiceDefinitionProductOfferingNameMapper implements MapToProduct
{

    /**
     * Add the C2M ProductOfferingName that is associated with each products
     * SDI to the products contained withing the $products array.  It is added
     * with the key of "productOfferingName"
     *
     * @param array $products
     *
     * @return array products array containing the "productOfferingName" if it was found.
     */
    public function mapTo(array $products)
    {
        $serviceDefinitionIDs = array_column($products, 'intSdi');

        if(empty($serviceDefinitionIDs)){
            return $products;
        }

        $productOfferingNames = $this->getProductOfferingsByServiceIDs($serviceDefinitionIDs);

        if(empty($productOfferingNames)){
            return $products;
        }

        foreach($productOfferingNames as $productOffering){
            foreach($products as &$product){
                if($product['intSdi'] === $productOffering['intSdi']){
                    $product['productOfferingName'] = $productOffering['productOfferingName'];
                }
            }
        }

        return $products;
    }

    /**
     * Query the database for the provided service definition IDs (sdi) and
     * return the sdi alongside the productOfferingName
     *
     * @param array $serviceDefinitionIDs
     *
     * @return array
     */
    protected function getProductOfferingsByServiceIDs(array $serviceDefinitionIDs)
    {
        try {
            $db = Db_Manager::getAdaptor('AccountChange');
            return $db->getProductOfferingsByServiceIDs($serviceDefinitionIDs);
        } catch (Exception $e) {
            error_log(__CLASS__ . '->'. __METHOD__ . $e->getMessage());
        }
        return [];
    }
}
