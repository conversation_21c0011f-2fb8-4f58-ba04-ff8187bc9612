<?php

/**
 * <AUTHOR>
 */

use Plusnet\BillingApiClient\Service\ServiceManager;

class AccountChange_BillingAccountSuspendedPolicyTest extends PHPUnit_Framework_TestCase
{
    const SERVICE_ID = 12345;

    /**
     * @return void
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
     * @return void
     */
    public function testValidatorIsValidWhenNotSuspended()
    {
        $billingApiClient = Mockery::mock('BillingApiFacade');
        $billingApiClient->shouldReceive('isBillingAccountSuspended')->once()->with(self::SERVICE_ID)->andReturnFalse();

        ServiceManager::setService('BillingApiFacade', $billingApiClient);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);

        $validator = new AccountChange_BillingAccountSuspendedPolicy($actor);

        $this->assertEquals(true, $validator->validate());
    }

    /**
     * @return void
     */
    public function testValidatorIsInvalidWhenSuspended()
    {
        $billingApiClient = Mockery::mock('BillingApiFacade');
        $billingApiClient->shouldReceive('isBillingAccountSuspended')->once()->with(self::SERVICE_ID)->andReturnTrue();

        ServiceManager::setService('BillingApiFacade', $billingApiClient);

        $actor = Mockery::mock('Auth_BusinessActor');
        $actor->shouldReceive('getExternalUserId')->andReturn(self::SERVICE_ID);
        $actor->shouldReceive('getUserType')->andReturn('PLUSNET_ENDUSER');

        $validator = new AccountChange_BillingAccountSuspendedPolicy($actor);

        $this->assertEquals(false, $validator->validate());
        $this->assertEquals(AccountChange_BillingAccountSuspendedPolicy::MC_ERROR_MESSAGE, $validator->getFailure());
    }
}
