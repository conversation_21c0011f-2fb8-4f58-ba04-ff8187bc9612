<?php
/**
 * Action Greenbee/Waitrose Product Set Change to <PERSON>
 *
 * Testing class for the AccountChange_Action_Appointment class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 * @since      File available since 2011-09-07
 */
/**
 * Action Greenbee/Waitrose Product Set Change to <PERSON> Test Class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
class AccountChange_Action_GbWrBullGuardJl_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Reset the account change registry when we're done
     *
     * @return void
     */
    public function tearDown()
    {
        AccountChange_Registry::instance()->reset();
        Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Test that execute sends the email when the conditions are met.
     *
     * @covers AccountChange_Action_GbWrBullGuardJl::__construct
     * @covers AccountChange_Action_GbWrBullGuardJl::execute
     * @covers AccountChange_Action_GbWrBullGuardJl::gatherRequiredData
     * @covers AccountChange_Action_GbWrBullGuardJl::isCustomerSwitchingFromGbWrToJLP
     * @covers AccountChange_Action_GbWrBullGuardJl::sendEmail
     *
     * @dataProvider dataProviderForTestBullGuard
     *
     */
    public function testAddBullGuard(
        $intComponentToReturn,
        $bolPerformChange,
        $bolAddOnAvailable
    )
    {
        // Get a mock for the bolton to be added, or not
        $addon = $this->getMock(
            'BoltOn_AvailableBoltOn',
            array('hasBehaviour'),
            array(
                new Int(12345),
                new String('Handle'),
                new String('Name'),
                new Bool(true),
                new Bool(true),
                new Int(1),
                new String('Description'),
                new String(''),
                new String(''),
                new Int(0),
                new Int(0),
                new String('HANDLE'),
                new Bool(false),
                new Bool(false),
                new Bool(false),
                new String('configTemplatePath'),
                new Bool(false),
                new Int(1)
            )
        );

        // Get a mock for the bullguard class itself
        $bullguard = $this->getMock(
            'AccountChange_Action_GbWrBullGuardJl',
            array(
                'isCustomerSwitchingFromGbWrToJLP',
                'gatherRequiredData',
                'getComponents',
                'destroyOldComponent',
                'getAvailableBoltOns',
                'addBoltOn'
            ),
            array(
                12345
            )
        );

        // For this test, always try to switch
        $bullguard->expects($this->once())
            ->method('isCustomerSwitchingFromGbWrToJLP')
            ->will($this->returnValue(true));

        // Get a list of components
        // Only 1 component, as determined by the data provider
        $bullguard->expects($this->once())
            ->method('getComponents')
            ->will($this->returnValue(
                array(
                    array(
                        'component_type_id' => $intComponentToReturn,
                        'component_id' => '1234',
                        'status' => 'active'
                    )
                )
            )
        );

        // If we should expect the change to run, continue
        if ($bolPerformChange) {

            $bullguard->expects($this->once())
                ->method('destroyOldComponent');

            $bullguard->expects($this->once())
                ->method('getAvailableBoltOns')
                ->will($this->returnValue(array($addon)));

            $addon->expects($this->any())
                ->method('hasBehaviour')
                ->with(Products_ServiceComponent::BULLGUARD)
                ->will($this->returnValue($bolAddOnAvailable));

            // If the addon is in the included list, expect it to be added
            if ($bolAddOnAvailable) {
                $bullguard->expects($this->once())
                    ->method('addBoltOn');
            } else {
                $bullguard->expects($this->never())
                    ->method('addBoltOn');
            }

        // Otherwise expect the component never to be destroyed
        } else {

            $bullguard->expects($this->never())
                ->method('destroyOldComponent');

            $bullguard->expects($this->never())
                ->method('getAvailableBoltOns')
                ->will($this->returnValue(array($addon))
            );


            $bullguard->expects($this->never())
                ->method('addBoltOn');
        }

        $bullguard->execute();
    }

    /**
     * Data provider for testAddBullGuard
     *
     * @return array
     */
    public function dataProviderForTestBullGuard()
    {
        return array(
            //Bullguard
            array(
                546,
                true,
                false
            ),
            array(
                546,
                true,
                true
            ),
            //BullGuard Trial
            array(
                547,
                true,
                false
            ),
            array(
                547,
                true,
                true
            ),
            //Other
            array(
                548,
                false,
                false
            ),
        );
    }
}
