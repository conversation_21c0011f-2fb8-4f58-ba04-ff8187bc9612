<?php

/**
 * Provide an API that gives access to functions to change the type of a users account.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2013 PlusNet
 * @link      link
 * @since     File available since 2013-02-21
 */
use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\CustomerConsent\CustomerConsentManager;
use Plusnet\CustomerConsent\Request\CreateConsentRequest;
use Plusnet\PhpMonitoringLogger\EndToEndOrderLogger;

/**
 * Provide an API that gives access to functions to change the type of a users account.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 PlusNet
 * @link      link
 */
class AccountChange_PerformChangeApi extends AccountChange_PerformChangeBase
{
    /**
     * @var string
     */
    const OLD_BB_MGALS_VALUE = 'oldBBMgalsValue';

    /**
     * @var string
     */
    const OLD_BB_IMPACTED_MGALS_VALUE = 'oldBBImpactedMgalsValue';

    /**
     * @var string
     */
    const INT_SERVICE_ID_KEY = 'intServiceId';

    const PROMO_CODE_KEY = 'promoCode';

    const MONITORING_EVENT_ERROR = 'internal_error';

    /**
     * Service id of the account we're changing
     *
     * @var int
     **/
    protected $serviceId;

    /**
     * @var AccountChange_EmailHandler Email Handler for confirmation
     */
    private $confirmationEmail;

    /**
     * Audit logger instance to use
     *
     * @var Log_AuditLog
     **/
    protected $auditLogger;

    /**
     * Any promo code to be applied during the change
     *
     * @var string
     **/
    protected $promoCode = null;

    /**
     * Flag to make Action_LtcContracts re-throw rather than discard
     * any exceptions on requesting / actioning change plans.
     *
     * @var bool
     **/
    protected $disableContractsErrorHandle = false;

    /**
     * The current service definition id
     *
     * @var int
     **/
    protected $currentSdi;

    /**
     * Old product name
     *
     * @var string
     **/
    protected $oldProductName;

    /**
     * New product name
     *
     * @var string
     **/
    protected $newProductName;

    /**
     * Contract length handle for the new product
     * Only applied to pre-falcon products that don't use the new contracting
     * system - otherwise to force a recontract use $recontractLengthMonths
     *
     * @var string
     **/
    protected $contractLengthHandle;

    /**
     * This is set if we're recontracting a post-falcon product with new style
     * contracts and is a number of months.  Null for no re-contracting required.
     *
     * @var int
     **/
    protected $recontractLengthMonths = null;

    /**
     * Date on which we wish to schedule the change in mysql YYYY-MM-DD format.
     * If this is set to null, then we just use the next billing date as usual.
     *
     * @param string
     **/
    protected $scheduledChangeDate = null;

    /**
     * Identifier for the scheduled change
     *
     * @var integer
     */
    protected $changeScheduleId = null;

    /**
     * The Old Wlr Component Id
     *
     * @var int
     */
    protected $oldWlrComponentId;

    /**
     * The New Wlr Service Component Id
     *
     * @var int
     */
    protected $newWlrServiceComponentId;

    /**
     * The old Wlr Service Component Id
     *
     * @var int
     */
    protected $oldWlrServiceComponentId;

    /**
     * tariff id for the new service definition
     *
     * @var int
     */
    protected $tariffId;

    /**
     * Flag to keep the discounts on the account intact
     *
     * @var bool
     */
    protected $retainDiscounts = false;

    /**
     * Flag to migrate the broadband discount on the account
     *
     * @var bool
     */
    protected $migrateBroadbandDiscount = false;

    /**
     * Flag if the changes is for Recontract
     *
     * @var bool
     */
    protected $isRecontract = false;

    /**
     * Flag if this is an instant recontract request
     *
     * @var bool
     */
    private $isInstantRecontract = false;

    /**
     * @var Date
     */
    protected $agreementDate;

    /**
     * @var string
     */
    protected $contractType;

    /**
     * @var string
     */
    protected $contractSubType;

    /**
     * @var int
     */
    protected $impressionOfferId;

    /**
     * @var boolean
     */
    private $retainContract;

    /**
     * @var array
     */
    private $validationPolicies;

     /**
     * @var AccountChange_AccountChangeAppointment
     */
    private $appointment;

    /**
     * @var string
     */
    protected $hardwareOption;

    /**
     * @var array
     */
    private $oneOffCharges;

    /**
     * @var bool
     */
    private $isProductChangeForHouseMove = false;

    /**
     * @var AccountChange_AccountChangeAddress
     */
    private $address;

    /**
     * @var bool
     */
    private $newContractOrder = false;

    /**
     * @var string
     */
    private $backDatedDate;

    /**
     * @var bool
     */
    private $includeCallerDisplay;

    /**
     * @var bool
     */
    private $shouldSaveScheduledMGALS = false;

    /**
     * @return AccountChange_AccountChangeAddress
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @param AccountChange_AccountChangeAddress $address address object
     */
    public function setAddress($address)
    {
        $this->address = $address;
    }

    /**
     * getter for line check results
     *
     * @return LineCheck_Result the last line check results
     */
    public function getLineCheckResults()
    {
        return $this->lineCheckResults;
    }

    /**
     * setter for isRecontract
     *
     * @param bool $recontractFlag Recontract
     *
     * @return void
     */
    public function setIsRecontract($recontractFlag)
    {
        $this->isRecontract = $recontractFlag;
    }

    /**
     * @return bool
     */
    public function getIsInstantRecontract()
    {
        return $this->isInstantRecontract;
    }

    /**
     * @param bool $isInstantRecontract flag for if this is an instant recontract
     *
     * @return void
     */
    public function setIsInstantRecontract($isInstantRecontract)
    {
        $this->isInstantRecontract = $isInstantRecontract;
    }

    /**
     * getter for isRecontract
     *
     * @return bool
     */
    public function isRecontract()
    {
        return $this->isRecontract;
    }

    /**
     * setter for tariffId
     *
     * @param int $tariffId - tariff id
     *
     * @return void
     */
    public function setTariffId($tariffId)
    {
        $this->tariffId = $tariffId;
    }

    /**
     * Setter for disableContractsErrorHandle
     * Set this to true when calling the API if you want Action_LtcContracts
     * to re-throw any exceptions caught on calling into ProductChangePlanClient
     * to action contacting changes.
     * Note: If this is set, then the calling code should deal with these exceptions.
     *
     * @param bool $status True to disable catching of exceptions
     *
     * @return void
     **/
    public function setDisableContractsErrorHandle($status)
    {
        $this->disableContractsErrorHandle = $status;
    }

    /**
     * Getter for disableContractsErrorHandle
     *
     * @return bool
     **/
    public function getDisableContractsErrorHandle()
    {
        return $this->disableContractsErrorHandle;
    }

    /**
     * setter for wlr service component Id to change to
     *
     * @param int $componentId wlr component id
     *
     * @return void
     */
    public function setNewWlrServiceComponentId($componentId)
    {
        $this->newWlrServiceComponentId = (int)$componentId;
    }

    /**
     * setter for wlr service component Id to change to
     *
     * @param int $componentId wlr component id
     *
     * @return void
     */
    public function setOldWlrServiceComponentId($componentId)
    {
        $this->oldWlrServiceComponentId = (int)$componentId;
    }

    /**
     * Set the overidden scheduled change date
     * If this is left unset then we'll schedule using the existing
     * account change logic, i.e next invoice date
     *
     * @param string $scheduledChangeDate Mysql format date (YYYY-MM-DD)
     *
     * @return void
     **/
    public function setScheduledChangeDate($scheduledChangeDate)
    {
        $this->scheduledChangeDate = $scheduledChangeDate;
    }

    /**
     * Getter for scheduled change date
     *
     * @return string
     **/
    public function getScheduledChangeDate()
    {
        return $this->scheduledChangeDate;
    }

    /**
     * Get the identifier for the scheduled change
     *
     * @return integer
     */
    public function getChangeScheduleId()
    {
        return $this->changeScheduleId;
    }

    /**
     * Set the identifier for the scheduled change
     *
     * @param integer $changeScheduleId Scheduled change id
     *
     * @return void
     */
    public function setChangeScheduleId($changeScheduleId)
    {
        $this->changeScheduleId = $changeScheduleId;
    }

    /**
     * getter for Email Handler for confirmation
     *
     * @return AccountChange_EmailHandler Email Handler for confirmation
     */
    public function getConfirmationEmail()
    {
        return $this->confirmationEmail;
    }

    /**
     * setter Email Handler for confirmation
     *
     * @param AccountChange_EmailHandler $emailHandler Email Handler for confirmation
     *
     * @return void
     */
    public function setConfirmationEmail(AccountChange_EmailHandler $emailHandler)
    {
        $this->confirmationEmail = $emailHandler;
    }

    /**
     * getter for new wlr service component Id
     *
     * @return int
     */
    public function getNewWlrServiceComponentId()
    {
        return $this->newWlrServiceComponentId;
    }

    /**
     * getter for old wlr service component Id to change to
     *
     * @return int
     */
    public function getOldWlrServiceComponentId()
    {
        return $this->oldWlrServiceComponentId;
    }

    /**
     * Setter for the audit logger
     *
     * @param Log_AuditLog $auditLogger A class that implements audit logging
     *
     * @return void
     **/
    public function setAuditLogger($auditLogger)
    {
        $this->auditLogger = $auditLogger;
    }

    /**
     * Getter for audit logger
     *
     * @return Log_AuditLog
     **/
    public function getAuditLogger()
    {
        return $this->auditLogger;
    }

    /**
     * Setter for service id
     *
     * @param int $serviceId Service id
     *
     * @return void
     **/
    public function setServiceId($serviceId)
    {
        $this->serviceId = (int)$serviceId;
    }

    /**
     * Getter for service id
     *
     * @return int
     **/
    public function getServiceId()
    {
        return $this->serviceId;
    }

    /**
     * Setter for promo code
     *
     * @param string $promoCode Promo code
     *
     * @return void
     **/
    public function setPromoCode($promoCode)
    {
        $this->promoCode = $promoCode;
    }

    /**
     * Getter for promo code
     *
     * @return string
     **/
    public function getPromoCode()
    {
        return $this->promoCode;
    }

    /**
     * Setter for old product name
     *
     * @param string $oldProductName Old product name
     *
     * @return void
     **/
    public function setOldProductName($oldProductName)
    {
        $this->oldProductName = $oldProductName;
    }

    /**
     * Getter for old product name
     *
     * @return string
     **/
    public function getOldProductName()
    {
        return $this->oldProductName;
    }

    /**
     * Setter for new product name
     *
     * @param string $newProductName New product name
     *
     * @return void
     **/
    public function setNewProductName($newProductName)
    {
        $this->newProductName = $newProductName;
    }

    /**
     * Getter for new contract length handle
     *
     * @return string
     **/
    public function getContractLengthHandle()
    {
        return $this->contractLengthHandle;
    }

    /**
     * Setter for new contract length handle
     * NOTE: Only applied to pre-falcon products that don't use the new contracting
     * system - otherwise to force a recontract use $recontractLengthMonths
     *
     * @param string $contractLengthHandle New contract length handle
     *
     * @return void
     **/
    public function setContractLengthHandle($contractLengthHandle)
    {
        $this->contractLengthHandle = $contractLengthHandle;
    }

    /**
     * Getter for recontract length
     *
     * @return int
     **/
    public function getRecontractLengthMonths()
    {
        return $this->recontractLengthMonths;
    }

    /**
     * Setter for recontract length
     * Used to force a recontract of a specified number of months for post-falcon
     * products using the new contracting system.  Values passed in here eventually
     * get used when calling PCPC in Action_LtcContracts
     *
     * @param int $recontractLengthMonths Recontract length in number of months
     *
     * @return void
     **/
    public function setRecontractLengthMonths($recontractLengthMonths)
    {
        $this->recontractLengthMonths = $recontractLengthMonths;
    }


    /**
     *  Setter for impression offer id
     * @param int $impressionOfferId - impression offer id
     * @return void
     */
    public function setImpressionOfferId($impressionOfferId)
    {
        $this->impressionOfferId = $impressionOfferId;
    }

    /**
     * Getter for impression offer id
     * @return void
     */
    public function getImpressionOfferId()
    {
        return $this->impressionOfferId;
    }

    /**
     * Getter for new product name
     *
     * @return string
     **/
    public function getNewProductName()
    {
        return $this->newProductName;
    }

    /**
     * Sets the flag to keep existing discounts on the account intact
     *
     * @param bool $retainDiscounts Flag for keeping the discounts
     *
     * @return void
     */
    public function setRetainDiscounts($retainDiscounts)
    {
        $this->retainDiscounts = $retainDiscounts;
    }

     /**
     * get appointment array for account change
     *
     * @return AccountChange_AccountChangeAppointment
     */
    public function getAppointment()
    {
        return $this->appointment;
    }

    /**
     * set appointment array for the account change
     *
     * @param AccountChange_AccountChangeAppointment $appointment appointment array
     * @return void
     */
    public function setAppointment($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Gets the flag to keep existing discounts on the account intact
     *
     * @return boolean
     */
    public function getRetainDiscounts()
    {
        return $this->retainDiscounts;
    }

    /**
     * Sets the flag to migrate broadband discount
     *
     * @param bool $migrateBroadbandDiscount Flag for migrate broadband discount
     *
     * @return void
     */
    public function setMigrateBroadbandDiscount($migrateBroadbandDiscount)
    {
        $this->migrateBroadbandDiscount = $migrateBroadbandDiscount;
    }

    /**
     * @return string
     */
    public function getAgreementDate()
    {
        return $this->agreementDate;
    }

    /**
     * @param string $date Date
     * @return $this
     */
    public function setAgreementDate($date)
    {
        $this->agreementDate = $date;
        return $this;
    }

    /**
     * @return string
     */
    public function getContractType()
    {
        return $this->contractType;
    }

    /**
     * @param string $type Contract type
     * @return $this
     */
    public function setContractType($type)
    {
        $this->contractType = $type;
        return $this;
    }

    /**
     * @return string
     */
    public function getContractSubType()
    {
        return $this->contractSubType;
    }

    /**
     * @param string $subType Contract sub type
     * @return $this
     */
    public function setContractSubType($subType)
    {
        $this->contractSubType = $subType;
        return $this;
    }

    /**
     * Getter for the retain contract flag
     *
     * @return boolean Flag to indicate whether the existing contract should be retained.
     */
    public function getRetainContract()
    {
        return $this->retainContract;
    }

    /**
     * Setter to tell account change to retain the customers existing contract.
     *
     * @param boolean $retainContract retain contract flag
     * @return $this
     */
    public function setRetainContract($retainContract)
    {
        $this->retainContract = $retainContract;
        return $this;
    }

    /**
     * @return string $hardwareOption
     */
    public function getHardwareOption()
    {
        return $this->hardwareOption;
    }

    /**
     * @param string $hardwareOption - hardware option
     * @return void
     */
    public function setHardwareOption($hardwareOption)
    {
        $this->hardwareOption = $hardwareOption;
    }

    /**
     * get one off charges for account change
     *
     * @return array
     */
    public function getOneOffCharges()
    {
        return $this->oneOffCharges;
    }

    /**
     * set one off charges for the account change
     *
     * @param array $oneOffCharges - one off charges for the account change
     * @return void
     */
    public function setOneOffCharges($oneOffCharges)
    {
        $this->oneOffCharges = $oneOffCharges;
    }

    /**
     * AccountChange_PerformChangeApi constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->auditLogger = new Log_AuditLog();
    }

    /**
     * Initialise the details that account change needs to make the change
     *
     * @param int    $serviceId            Service id
     * @param string $contractLengthHandle Contract length handle for new product
     * @param string $promoCode            Any promo code to apply during the change
     *
     * @return void
     **/
    public function init($serviceId, $contractLengthHandle = 'MONTHLY', $promoCode = null)
    {
        $this->setServiceId((int)$serviceId);
        $this->setPromoCode($promoCode);
        $this->setContractLengthHandle($contractLengthHandle);
        $this->tariffId = 0;
        $this->toSdi = 0;
        $this->newWlrServiceComponentId = 0;
        $this->setIsVariantSwitchForWlrAddOrRemoveFlag(false);
    }

    /**
     * Setups instance before any databases are change and validation
     *
     * @return void
     */
    private function preRun()
    {
        $accountChange = $this->getProductDetailsForAccountChange($this->getServiceId(), $this->getToSdi());
        $this->setCurrentSdi($accountChange['intCurrentSdi']);
        $this->setOldSdi($accountChange['intOldSdi']);
        $this->setOldProductName($accountChange['strOldProductName']);
        $this->setNewProductName($accountChange['strNewProductName']);
        $this->setLineCheckNeededIfNotFound(true);
    }

    /**
     * Execute a query to get account change details
     *
     * @param int $serviceId Service id
     * @param int $toSdi     Service definition id we wish to change to
     *
     * @return array
     **/
    protected function getProductDetailsForAccountChange($serviceId, $toSdi)
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        return $db->getProductDetailsForAccountChange($toSdi, $serviceId);
    }

    public function setIsScheduled($isScheduled)
    {
        $this->isScheduled = $isScheduled;
    }

    /**
     * @param string $sessionId Session id used to ovveride business tier session
     * @return void
     */
    public function setOverrideBusinessTierSessionId($sessionId)
    {
        $this->registry->setEntry('overridenBusinessTierSessonId', $sessionId);
    }

    /**
     * @param int $actorId Actor ID to override
     * @return void
     */
    public function setActorId($actorId)
    {
        $this->registry->setEntry('overriddenActorId', $actorId);
    }

    /**
     * Actually change the account type
     * @param bool $bolMgalsBBValueUpdateRequired Do we need to copy the MGALS to the new component
     *
     * @return bool
     * @throws Exception
     */
    public function doInstantChange($bolMgalsBBValueUpdateRequired = false)
    {
        $this->preRun();
        $accountChange = $this->getAccountChangeDetailsArray();

        $this->setAccessTechnologyValues();

        $this->registry
            ->setEntry('bolSchedule', $this->isScheduled)
            ->setEntry('disableContractsErrorHandle', $this->getDisableContractsErrorHandle())
            ->setEntry('isRecontract', $this->isRecontract)
            ->setEntry('retainDiscounts', $this->retainDiscounts)
            ->setEntry('migrateBroadbandDiscount', $this->migrateBroadbandDiscount)
            ->setEntry('agreementDate', $this->getAgreementDate())
            ->setEntry('contractType', $this->getContractType())
            ->setEntry('contractSubType', $this->getContractSubType())
            ->setEntry('retainContract', $this->getRetainContract())
            ->setEntry('hardwareOption', $this->getHardwareOption())
            ->setEntry('invoiceItems', $this->getOneOffCharges())
            ->setEntry('newContractOrder', $this->isNewContractOrder())
            ->setEntry('currentWlrComponentId', $this->getWlrInformation($this->getServiceId())['intOldWlrId']);

        // Caller display can be null and we don't want to set anything in the registry if this is the case.
        if ($this->includeCallerDisplay === true || $this->includeCallerDisplay === false) {
            $this->registry->setEntry('callerDisplay', $this->includeCallerDisplay);
        }

        if ($bolMgalsBBValueUpdateRequired) {
            // get existing - if any - MGALS and save against the new component
            $this->setMgals($accountChange['intServiceId']);
        }

        // If we're recontracting set the number of months required into the registry to be
        // picked up in Action_LtcContracts and sent to PCPC as ForceRecontractLength
        if ($this->recontractLengthMonths !== null) {
            $this->registry->setEntry('selectedContractDuration', $this->recontractLengthMonths);
        }

        if ($this->isInstantRecontract === true) {
            $this->registry->setEntry('instantRecontract', true);
        }

        /**
         *  An impressionOfferId is used by PEGA Marketing
         */
        if ($this->impressionOfferId !== null) {
            $this->registry->setEntry('impressionOfferId', $this->impressionOfferId);
        }

        $this->registry->setEntry('bolPortal', $this->isCustomerUser());

        $this->output(
            "Customer changing from {$accountChange['strOldProductName']} to {$accountChange['strNewProductName']}"
        );

        if (!empty($this->getBackDatedDate())) {
            $this->registry->setEntry('backDatedDate', $this->getBackDatedDate());
        }

        if (!$this->isAccountValidForProcessing($accountChange) && !$this->getNewWlrServiceComponentId()) {
            $this->output("Account change can not be performed.");
            return false;
        }
        $this->objService = $this->getCoreServiceForServiceId($accountChange[self::INT_SERVICE_ID_KEY]);
        $this->lineCheckResults = $this->getLineCheckFromDatabase($this->objService);

        //we need to get discount length before performing the change
        //Please dont delete getDiscountLength does additional
        //LegacyCodebase includes
        $intDiscountLength = $this->getDiscountLength();

        //generate wlr configuration if we are doing it
        $wlrConfiguration = null;
        if ($this->getNewWlrServiceComponentId()) {
            $wlrConfiguration = $this->doProcessWlrRequest($accountChange);

            if ($this->isNewContractOrder()) {
                $this->registry->setEntry('phoneContractChange', true);
            }
        }

        $manager = $this->restoreAccountManager($accountChange, $wlrConfiguration);

        $this->output("Account change manager restored, calling changeAccount.");

        try {
            // Tone down the error handling whilst we play in the legacy codebase
            $intPreviousErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_STRICT);

            // If we've opted to set our own scheduled change date.
            // Picked up in Product_ServiceDefinition.class.php when adding the scheduled change
            // using userdata_service_schedule_add()
            if ($this->scheduledChangeDate !== null) {
                $this->registry->setEntry(
                    'overiddenChangeDate',
                    $this->scheduledChangeDate
                );
            }

            if ($this->getAddress() != null && !$this->isProductChangeForHouseMove) {
                $this->address->saveAddressRefAndDatabaseCode($accountChange[self::INT_SERVICE_ID_KEY]);
            }

            if ($this->getAppointment() != null || !empty($this->getAppointment())) {
                $this->registry->setEntry('appointmentData', $this->getAppointment()->getAppointmentArray());
            }
            $manager->changeAccount();

            if ($this->registry->getEntry('runFromBBCRScript')) {
                // Refresh Core Service as account has now changed
                $this->objService = $this->getCoreServiceForServiceId($accountChange[self::INT_SERVICE_ID_KEY]);
                $arrEmailData = $this->restoreEmailData($accountChange['strNewProductName'], $intDiscountLength);
                $manager->sendConfirmationEmails($arrEmailData);
            }

            $intOldBBMgalsValue = $this->registry->getEntry(self::OLD_BB_MGALS_VALUE);
            if ($intOldBBMgalsValue) {
                $this->saveMgals($accountChange[self::INT_SERVICE_ID_KEY]);
            }

            $bolLineCheckConsent = $this->registry->getEntry('bolContinueWithoutLineCheckConsent');
            if ($bolLineCheckConsent) {
                $this->saveLineCheckDownConsent($accountChange[self::INT_SERVICE_ID_KEY]);
            }

            if ($this->isProductChangeForHouseMove() && $this->isScheduled) {
                $postChangeHouseMoveActions = $this->getHouseMovePostScheduledChangeHelper(
                    $this->serviceId,
                    $this->recontractLengthMonths
                );
                $postChangeHouseMoveActions->execute();
            }

            $serviceChangeScheduleId = $manager->getServiceChangeScheduleId();

            if ($this->shouldSaveScheduledMGALS() && !empty($serviceChangeScheduleId)) {
                $this->saveScheduledMGALS($serviceChangeScheduleId);
            }

            if ($this->registry->getEntry('completeServiceChangeSchedule')) {
                $this->makeMGALSLive($this->serviceId);
                $this->markServiceChangeScheduleComplete($manager);
            }

            if ($this->getConfirmationEmail() !== null) {
                $this->getConfirmationEmail()->sendEmail($accountChange[self::INT_SERVICE_ID_KEY]);
            }

            error_reporting($intPreviousErrorReporting);
        } catch (Exception $e) {
            if ($e instanceof \Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException) {
                $this->errorLog("error occurred during RBM call with message ".$e->getMessage());
                throw $e;
            }

            $errMsg = get_class($this) .
                ": account change failed for service id {$accountChange[self::INT_SERVICE_ID_KEY]}.  " .
                    "The message returned was: " . $e->getMessage();

            $logData['serviceId'] = $accountChange[self::INT_SERVICE_ID_KEY];
            $logData['errorMessage'] = $errMsg;
            $orderLogger = new EndToEndOrderLogger();
            $orderLogger->log(EndToEndOrderLogger::GROUP_ACCOUNT_CHANGE, self::MONITORING_EVENT_ERROR, $logData);

            Dbg_Dbg::write($errMsg, 'AccountChange');

            $this->output(
                "AccountChange for service id {$accountChange[self::INT_SERVICE_ID_KEY]} failed.  ".
                "The message returned was: " . $e->getMessage()
            );

            if ($this->getDisableContractsErrorHandle()) {
                throw $e;
            }
        }
    }

    /**
     * Set MGALS value in the registry
     * @param int $intServiceId Service ID
     *
     * @return void
     */
    public function setMgals($intServiceId)
    {
        $objCustomerDetails  = new CustomerDetails_HttpApi();
        $retrieveMgalsResult = $objCustomerDetails->retrieveMGALS($intServiceId);
        if (!empty($retrieveMgalsResult) && is_array($retrieveMgalsResult)) {
            // Get MGALS value
            if (array_key_exists("mgals", $retrieveMgalsResult)
                && $retrieveMgalsResult['mgals'] > 0
            ) {
                $this->registry->setEntry(
                    self::OLD_BB_MGALS_VALUE,
                    $retrieveMgalsResult['mgals']
                );
            }
            // get impacted MGALS value
            if (array_key_exists("impactedMgals", $retrieveMgalsResult)
                && $retrieveMgalsResult['impactedMgals'] > 0
            ) {
                $this->registry->setEntry(
                    OLD_BB_IMPACTED_MGALS_VALUE,
                    $retrieveMgalsResult['impactedMgals']
                );
            }
        }
    }

    /**
     * write MGALS to database
     *
     * @param int $intServiceId Service ID
     *
     * @return boolean
     */
    public function saveMgals($intServiceId)
    {
        // do we have an old value to keep
        $intOldBBMgalsValue = $this->registry->getEntry(self::OLD_BB_MGALS_VALUE);
        if (!empty($intOldBBMgalsValue)) {
            // Is this value ADSL or FTTC?
            $intImpactedMgals = $this->registry
                ->getEntry(OLD_BB_IMPACTED_MGALS_VALUE);
            if (empty($intImpactedMgals)) {
                // No Impacted MGALS, must be ADSL
                $intMgals = $intOldBBMgalsValue;
                $intCleanMgals = 0;
                $intImpactedMgals = 0;
            } else {
                // Impacted exists, must be FTTC
                $intMgals = 0;
                $intCleanMgals = $intOldBBMgalsValue;
            }

            $objMGALS = new ProductComponent_MGALS();
            $bolSavedMGALS = $objMGALS->saveMGALS(
                $intServiceId,
                null,
                'active',
                $intMgals,
                $intCleanMgals,
                $intImpactedMgals
            );

            if (!$bolSavedMGALS) {
                Dbg_Dbg::write(
                    "Error when saving MGALS {$intServiceId}"
                );
            }
        }
        return true;
    }

    /**
     * Write consent to proceed without a line check to the database
     *
     * @param int $intServiceId The service ID
     * @return bool
     */
    public function saveLineCheckDownConsent($intServiceId)
    {
        $adaptor = Db_Manager::getAdaptor('AccountChange');

        // shouldn't need product component instance ID at this point, however
        // could call CustomerConsentManager()->findInternetConnectionComponentDetails
        // with the service ID to fetch it if it's available at the point this
        // method is called
        $componentId = $adaptor->getComponentIdForTypeAndStatus('active', 'INTERNET_CONNECTION', $intServiceId);

        (new CustomerConsentManager())->createConsent(
            (new CreateConsentRequest())
                ->setComponentId($componentId)
                ->setConsentTypeHandle(
                    CustomerConsentManager::CONSENT_TYPE_HANDLE_PROCEED_RECONTRACT_WITHOUT_LINE_CHECK
                )
        );

        return true;
    }

    /**
     * Get the last used instant change date
     *
     * @return string
     **/
    public function getLastUsedChangeDate()
    {
        return $this->registry->getEntry('changeDateUsedLast');
    }

    /**
     * Wrapper around error_log for unit test mocking
     *
     * @param string $errMsg Error message
     *
     * @return void
     */
    protected function errorLog($errMsg)
    {
        error_log($errMsg);
    }

    /**
     * Process for wlr changes based on wlr information with AccountChangeDetailsArray
     *
     * @param Array $accountChange AccountChangeDetailsArray
     *
     * @return Array the change objects
     */
    private function doProcessWlrRequest(array $accountChange)
    {
        $wlrProductConfigCreator = $this->getWlrProductConfigCreator();

        $wlrProductConfigCreator
            ->setObjCoreService($this->getCoreService())
            ->setArrWlrProduct($this->getWlrInformation($this->getServiceId()))
            ->setBolSchedule($this->isScheduled)
            ->setObjBusinessActor($this->getBusinessActor())
            ->setIntNewWlrId($this->getNewWlrServiceComponentId())
            ->setObjLineCheckResults($this->getLineCheckResults())
            ->setBolContractResetWlr(true)
            ->setStrWlrContext(false)
            ->setIntOldSdi($accountChange['intOldSdi'])
            ->setIntNewSdi($this->getToSdi())
            ->setIsHouseMove($this->isHouseMove());

        return $wlrProductConfigCreator->createWlrConfigurations();
    }

    /**
     * get a new AccountChange_WlrProductConfigCreator object
     *
     * @return AccountChange_WlrProductConfigCreator new object
     */
    protected function getWlrProductConfigCreator()
    {
        return new AccountChange_WlrProductConfigCreator();
    }

    /**
     * get the business actor
     *
     * @return Auth_BusinessActor
     */
    public function getBusinessActor()
    {
        return Auth_BusinessActor::get($this->getBusinessActorId());
    }

    /**
     * get the wlr information for a customer based on service id
     *
     * @param int $serviceId customers service id
     *
     * @return Array
     */
    public function getWlrInformation($serviceId)
    {
        return AccountChange_Account::instance(new Int($serviceId))->getWlrInformation();
    }

    /**
     * Does a scheduled change on the attached service definitions
     *
     * @return bool
     * @throws Exception
     */
    public function doScheduledChange()
    {
        $this->isScheduled = true;
        return $this->doInstantChange();
    }

    /**
     * Completes an account change triggered by a BBCR script
     *
     * @return void
     * @throws Exception
     */
    public function bbcrCompleteAccountChange()
    {
        $this->registry->setEntry('runFromBBCRScript', true);

        $this->doInstantChange();
    }

    /**
     * Function to output a formatted message to the audit log
     *
     * @param string $message A message to output
     *
     * @return void
     */
    protected function output($message)
    {
        $logger = $this->getAuditLogger();
        $logger->write($message, __CLASS__);
    }

    /**
     * @param string $backDatedDate back dated date "DD/MM/YYYY"
     * @return void
     */
    public function setBackDatedDate($backDatedDate)
    {
        $this->backDatedDate = $backDatedDate;
    }

    /**
     * @return string
     */
    public function getBackDatedDate()
    {
        return $this->backDatedDate;
    }

    /**
     * Setter for include caller display
     *
     * @param $includeCallerDisplay
     * @return void
     */
    public function setIncludeCallerDisplay($includeCallerDisplay)
    {
        $this->includeCallerDisplay = $includeCallerDisplay;
    }

    /**
     * @return bool
     */
    public function includeCallerDisplay()
    {
        return $this->includeCallerDisplay;
    }

    /**
     * get the service component id based on service definition and market
     *
     * @param int $sdi      service definition id
     * @param int $marketId The customers market
     *
     * @return int|null
     */
    protected function getScidFromSdiAndMarket($sdi, $marketId)
    {
        try {
            return ProductFamily_InternetConnectionHelper::getId($sdi, $marketId);
        } catch (Exception $e) {
            // If we've got a legacy service definition based product then the above will throw an
            // exception.  In this case it's valid to return null for the new scid
            return null;
        }
    }

    /**
     * Helper function to build an array of product details in the format used by
     * many of the account change library functions
     *
     * @return array
     **/
    public function getAccountChangeDetailsArray()
    {
        return array(
            self::PROMO_CODE_KEY         => $this->getPromoCode(),
            'intOldSdi'                  => $this->getOldSdi(),
            'intCurrentSdi'              => $this->getCurrentSdi(),
            'intNewSdi'                  => $this->getToSdi(),
            self::INT_SERVICE_ID_KEY     => $this->getServiceId(),
            'strContractLengthHandle'    => $this->getContractLengthHandle(),
            'strOldProductName'          => $this->getOldProductName(),
            'strNewProductName'          => $this->getNewProductName(),
            'newWlrScid'                 => $this->getNewWlrServiceComponentId(),
            'agreementDate'              => $this->getAgreementDate(),
            'contractType'               => $this->getContractType(),
            'contractSubType'            => $this->getContractSubType(),
            'retainContract'             => $this->getRetainContract(),
            'intServiceChangeScheduleId' => $this->getChangeScheduleId(),
            'oneOffCharges'              => $this->getOneOffCharges(),
            'bolHouseMove'               => $this->isHouseMove(),
            'address'                    => $this->getAddress(),
            'fttpAppointment'            => $this->getAppointment(),
            'backDatedDate'              => $this->getBackDatedDate(),
            'newContractOrder'           => !$this->isRecontract()
        );
    }

    /**
     * Performs account change pre-flight checks and returns either true if the
     * account change is allowed, or a reason for it not being allowed.
     *
     * @param bool $isScript Is script
     *
     * @return bool|string
     **/
    public function isAllowed($isScript = false)
    {
        $additionalValidatorParams = array();
        $details = $this->getAccountChangeDetailsArray();
        if (!empty($details[self::PROMO_CODE_KEY])) {
            $promotion = AccountChange_C2mPromotionsHelper::getActivePromotionWithPromocode(
                $details[self::PROMO_CODE_KEY]
            );
            if ($promotion !== false) {
                $additionalValidatorParams['C2MPromotion'] = $promotion;
            }
        }

        $this->validationPolicies = AccountChange_ValidationCheck::getDefaultPolicies();

        if ($this->isProductChangeForHouseMove) {
            $this->removeValidationPolicy(AccountChange_HouseMovePolicy::class);
        }

        return $this->checkValidationPolicies($isScript, $additionalValidatorParams);
    }

    /**
     * @param bool  $isScript                  is script
     * @param array $additionalValidatorParams addition details for validation policies
     * @return bool|string
     * @throws AccountChange_InvalidValidatorException
     */
    protected function checkValidationPolicies($isScript, $additionalValidatorParams)
    {
        $userActor = $this->getActorByExternalUserId($this->getServiceId());

        $validationCheck = new AccountChange_ValidationCheck(
            $userActor,
            $this->validationPolicies,
            false,
            $isScript,
            $additionalValidatorParams
        );

        $allowed = $validationCheck->isAccountChangeAllowed();

        if (!$allowed) {
            return $validationCheck->getReasonForBlockage();
        }
        return $allowed;
    }

    /**
     * Remove a policy from the validator array
     *
     * @param string $policy Policy we want to filter out
     * @return void
     */
    private function removeValidationPolicy($policy)
    {
        $filteredArray = array_filter($this->validationPolicies, function ($policyClassname) use ($policy) {
            if ($policyClassname === $policy) {
                return false;
            }
            return true;
        });

        $this->validationPolicies = array_values($filteredArray);
    }

    /**
     * returns true if supplied service definition id is a fibre product
     *
     * @param int $intSdi service definition id
     *
     * @return bool
     */
    public function isFibreProduct($intSdi = null)
    {
        $bolIsFibre = false;
        if (isset($intSdi)) {
            $adaptor = Db_Manager::getAdaptor('AccountChange');
            $fibreSdiLookup = $adaptor->isFibreProduct($intSdi);
            $bolIsFibre = (!empty($fibreSdiLookup));
        }
        return $bolIsFibre;
    }

    /**
     * @return string
     * @throws Db_TransactionException
     */
    public function getTechnologyType()
    {
        $this->preRun();
        $accountChangeParams = $this->getAccountChangeDetailsArray();
        $newSdi = $accountChangeParams['intNewSdi'];
        return $this->getTechnologyTypeFromSdi($newSdi);
    }

    /**
     *  Set flag in registry if this is product variant switch for Add or Remove Phone
     *
     * @param bool $bolVariantSwitchForWlrAddOrRemove If this is product variant switch for Add or Remove Phone
     *
     * @return void
     */
    public function setIsVariantSwitchForWlrAddOrRemoveFlag($bolVariantSwitchForWlrAddOrRemove)
    {
        $this->registry->setEntry(
            'bolVariantSwitchForWlrAddOrRemove',
            $bolVariantSwitchForWlrAddOrRemove
        );
    }

    /**
     * Getter for newWlr component Id - schould be called after AccountChange_PerformChangeApi::doInstantChange()
     * AccountChange_PerformChangeApi::doScheduledChange()
     *
     * @return int
     */
    public function getNewWlrComponentId()
    {
        return $this->registry->getEntry('newWlrComponentId');
    }

    /**
     * Performs a new line check and stores the result in the line checker history
     *
     * @return void
     **/
    public function performNewLineCheckAndStoreResult()
    {
        $coreService = $this->getCoreServiceForServiceId($this->getServiceId());
        $this->setCoreService($coreService);
        $lineCheck = $this->doLineCheck($coreService);
        $this->writeLineCheckResultToDb($coreService, $lineCheck);
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Gets a AccountChange_SchedulingHelper instance
     *
     * @return AccountChange_SchedulingHelper
     **/
    protected function getSchedulingHelper()
    {
        return new AccountChange_SchedulingHelper();
    }

    /**
     * Returns the calculated scheduled change date
     *
     * @param int  $serviceId              Service id
     * @param bool $hasEngineerAppointment Whether an engineer appointment is booked or not
     * @param bool $selfInstall            Calculates the self-install date if set to true
     *
     * @return string
     **/
    public function calculateScheduledChangeDate($serviceId, $hasEngineerAppointment = false, $selfInstall = false)
    {
        $helper = $this->getSchedulingHelper();

        if ($selfInstall) {
            $helper->calculateSelfInstallDate($this->getOldSdi(), $this->getToSdi());
        }

        return $helper->calculateScheduledChangeDate($serviceId, $hasEngineerAppointment);
    }

    /**
     * Sets data required for recording the customer's consent to switch
     *
     * @param int    $intServiceId            The Service id
     * @param int    $intAppId                The application Id
     * @param bool   $bolWlrTakeover          Is this a phone line takeover
     * @param bool   $bolAdslTakeover         Is this a broadband takover
     * @param string $vchConsentVersionHandle The consent text version handle
     *
     * @return void
     **/
    public function setConsentToSwitchData(
        $intServiceId,
        $intAppId,
        $bolWlrTakeover,
        $bolAdslTakeover,
        $vchConsentVersionHandle
    ) {
        $this->registry
            ->setEntry(self::INT_SERVICE_ID_KEY, $intServiceId)
            ->setEntry('intAppId', $intAppId)
            ->setEntry('bolWlrTakeover', $bolWlrTakeover)
            ->setEntry('bolAdslTakeover', $bolAdslTakeover)
            ->setEntry('vchConsentVersionHandle', $vchConsentVersionHandle);
    }

    /**
     * Sets a flag in the registry to indicate to Product_Wlr whether or not to suppress the confirmation email
     * Called from the CampaignHelper as the package change email already informs the user that their
     * phone is changing
     *
     * @return void
     */
    public function suppressWlrConfirmationEmail()
    {

        $this->registry->setEntry(
            'bolSuppressWlrConfirmationEmail',
            true
        );
    }

    /**
     * Set whether consent to continue without a linecheck has been provided
     *
     * @param bool $consent - consent
     * @return void
     */
    public function setContinueWithoutLineCheckConsentGiven($consent)
    {
        $this->registry->setEntry(
            'bolContinueWithoutLineCheckConsent',
            $consent
        );
    }

    /**
     * Set product change request is triggered by house move activation
     *
     * @param bool $isProductChangeForHouseMove Is this product change triggered by house move
     * @return void
     */
    public function setProductChangeForHouseMove($isProductChangeForHouseMove)
    {
        $this->isProductChangeForHouseMove = $isProductChangeForHouseMove;
    }

    /**
     * @return bool
     */
    public function isProductChangeForHouseMove()
    {
        return $this->isProductChangeForHouseMove;
    }

    /**
     * @return array
     */
    public function getValidationPolicies()
    {
        return $this->validationPolicies;
    }

    /**
     * Set tariff ID using service definition and market
     *
     * @param int $serviceDefinition service def
     * @param int $market            market id
     * @return void
     */
    public function setTariffIdByServiceDefinitionAndMarket($serviceDefinition, $market)
    {
        $dbAdaptor = Db_Manager::getAdaptor('AccountChange');
        $this->tariffId = $dbAdaptor->getTariffId($market, $serviceDefinition);
    }


    /**
     * Checks the business actor to check if the user is a customer.
     *
     * @return bool
     */
    protected function isCustomerUser()
    {
        $objLogin = Auth_Auth::getCurrentLogin();

        if ($objLogin instanceof Auth_Login) {
            $businessActor = $objLogin->getBusinessActor();
            return $businessActor->getUserType() !== 'PLUSNET_STAFF';
        }

        return true;
    }

    /**
     * @param int      $intServiceId     customer service id
     * @param int|null $contractDuration contract duration
     * @return AccountChange_HouseMovePostScheduledChangeHelper
     */
    protected function getHouseMovePostScheduledChangeHelper($intServiceId, $contractDuration)
    {
        return new AccountChange_HouseMovePostScheduledChangeHelper(
            $intServiceId,
            $contractDuration
        );
    }

    /**
     * @return bool
     */
    private function isHouseMove()
    {
        return $this->isProductChangeForHouseMove() && $this->isScheduled;
    }

    /**
     * @return bool
     */
    public function isNewContractOrder()
    {
        return $this->newContractOrder;
    }

    /**
     * @param boolean $newContractOrder - new order flag
     * @return void
     */
    public function setNewContractOrder($newContractOrder)
    {
        $this->newContractOrder = $newContractOrder;
    }

    /**
     * @param bool $shouldSaveScheduledMGALS should scheduled MGALS be saved as part of this change?
     * @return void
     */
    public function setShouldSaveScheduledMGALS($shouldSaveScheduledMGALS)
    {
        $this->shouldSaveScheduledMGALS = $shouldSaveScheduledMGALS;
    }

    /**
     * @return bool
     */
    public function shouldSaveScheduledMGALS()
    {
        return $this->shouldSaveScheduledMGALS;
    }

    /**
     * @param int $serviceChangeScheduleId schedule change schedule id
     * @return void
     */
    private function saveScheduledMGALS($serviceChangeScheduleId)
    {
        if ($this->lineCheckResults instanceof LineCheck_Result && $this->lineCheckResults->isValidResult()) {
            /** @var AccountChange_MGALSHelper $mgalsHelper */
            $mgalsHelper = AccountChange_ServiceManager::getService('MgalsHelper');
            $mgalsHelper->saveMGALSFromLineCheckData(
                $this->lineCheckResults->getLineCheckId(),
                $this->getToSdi(),
                $serviceChangeScheduleId
            );
        } else {
            $this->output('No line check result available to retrieve MGS Data');
        }
    }
}
