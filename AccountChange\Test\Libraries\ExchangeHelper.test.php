<?php
/**
 * AccountChange Exchange Helper test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 Plusnet
 */
/**
 * AccountChange Exchange Helper test
 *
 * Test class for AccountChange_ExchangeHelper
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 Plusnet
 */
class AccountChange_ExchangeHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Tear down functionality
     *
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::restoreAdaptor('AccountChange');
    }

    /**
     * Tests maintainCustomerExhangeData calls update if a row already exists
     *
     * @covers AccountChange_ExchangeHelper::maintainCustomerExhangeData
     *
     * @return void
     **/
    public function testMaintainCustomerExhangeDataUpdatesWhereRequired()
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('serviceHasExhangeDataSet'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db
            ->expects($this->once())
            ->method('serviceHasExhangeDataSet')
            ->will($this->returnValue(true));

        Db_Manager::setAdaptor('AccountChange', $db);

        $mockHelper = $this->getMock(
            'AccountChange_ExchangeHelper',
            array('updateCurrentMarketAndExchange', 'insertCurrentMarketAndExhange'),
            array()
        );

        $sid = 12345;
        $marketId = 34534;
        $exchangeId = ********;

        $mockHelper
            ->expects($this->once())
            ->method('updateCurrentMarketAndExchange')
            ->with($sid, $marketId, $exchangeId);

        $mockHelper
            ->expects($this->never())
            ->method('insertCurrentMarketAndExhange');

        $mockHelper->maintainCustomerExhangeData($sid, $marketId, $exchangeId);

    }

    /**
     * Tests maintainCustomerExhangeData inserts a row if one doesn't exist
     *
     * @covers AccountChange_ExchangeHelper::maintainCustomerExhangeData
     *
     * @return void
     **/
    public function testMaintainCustomerExhangeDataInsertsWhereRequired()
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('serviceHasExhangeDataSet'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db
            ->expects($this->once())
            ->method('serviceHasExhangeDataSet')
            ->will($this->returnValue(false));

        Db_Manager::setAdaptor('AccountChange', $db);

        $mockHelper = $this->getMock(
            'AccountChange_ExchangeHelper',
            array('updateCurrentMarketAndExchange', 'insertCurrentMarketAndExhange'),
            array()
        );

        $sid = 12345;
        $marketId = 34534;
        $exchangeId = ********;

        $mockHelper
            ->expects($this->never())
            ->method('updateCurrentMarketAndExchange');

        $mockHelper
            ->expects($this->once())
            ->method('insertCurrentMarketAndExhange')
            ->with($sid, $marketId, $exchangeId);

        $mockHelper->maintainCustomerExhangeData($sid, $marketId, $exchangeId);

    }

    /**
     * Tests updateCurrentMarketAndExchange calls the expected db statement
     *
     * @covers AccountChange_ExchangeHelper::updateCurrentMarketAndExchange
     *
     * @return void
     **/
    public function testUpdateCurrentMarketAndExchangeRunsCorrectStatement()
    {
        $sid = 12345;
        $marketId = 34534;
        $exchangeId = ********;

        $db = $this->getMock(
            'Db_Adaptor',
            array('updateCurrentMarketAndExchange'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db
            ->expects($this->once())
            ->method('updateCurrentMarketAndExchange')
            ->with($exchangeId, $marketId, $sid)
            ->will($this->returnValue(true));

        Db_Manager::setAdaptor('AccountChange', $db);

        $helper = new AccountChange_ExchangeHelper();

        $helper->updateCurrentMarketAndExchange($sid, $marketId, $exchangeId);
    }

    /**
     * Tests insertCurrentMarketAndExhange calls the expected db statement
     *
     * @covers AccountChange_ExchangeHelper::insertCurrentMarketAndExhange
     *
     * @return void
     **/
    public function testInsertCurrentMarketAndExhangeRunsCorrectStatement()
    {
        $sid = 12345;
        $marketId = 34534;
        $exchangeId = ********;

        $db = $this->getMock(
            'Db_Adaptor',
            array('insertCurrentMarketAndExhange'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db
            ->expects($this->once())
            ->method('insertCurrentMarketAndExhange')
            ->with($sid, $exchangeId, $marketId)
            ->will($this->returnValue(true));

        Db_Manager::setAdaptor('AccountChange', $db);

        $helper = new AccountChange_ExchangeHelper();

        $helper->insertCurrentMarketAndExhange($sid, $marketId, $exchangeId);
    }

    /**
     * Tests serviceHasExhangeDataSet calls the expected db statement
     *
     * @covers AccountChange_ExchangeHelper::serviceHasExhangeDataSet
     *
     * @return void
     **/
    public function testServiceHasExhangeDataSetRunsCorrectStatement()
    {
        $sid = 12345;

        $db = $this->getMock(
            'Db_Adaptor',
            array('serviceHasExhangeDataSet'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db
            ->expects($this->once())
            ->method('serviceHasExhangeDataSet')
            ->with($sid)
            ->will($this->returnValue(true));

        Db_Manager::setAdaptor('AccountChange', $db);

        $helper = new AccountChange_ExchangeHelper();

        $helper->serviceHasExhangeDataSet($sid);
    }

    /**
     * Tests that getMarketAndExchangeIdFromRegistry gets a line check object from the
     * registry and extracts appropriate data.
     *
     * @covers AccountChange_ExchangeHelper::getMarketAndExchangeIdFromRegistry
     *
     * @return void
     **/
    public function testGetMarketAndExchangeIdFromRegistry()
    {
        $marketId = 12345;
        $exchangeId = 435334;

        $exchangeAndMarketId = array(
            'marketId'   => $marketId,
            'exchangeId' => $exchangeId
        );

        $lineCheckMock = $this->getMock(
            'LineCheck_Result',
            array('getExchangeAndMarketId'),
            array()
        );

        $lineCheckMock
            ->expects($this->once())
            ->method('getExchangeAndMarketId')
            ->will($this->returnValue($exchangeAndMarketId));

       AccountChange_Registry::instance()->setEntry('lineCheckResult', $lineCheckMock);

       $helper = new AccountChange_ExchangeHelper();
       $result = $helper->getMarketAndExchangeIdFromRegistry();

       $this->assertEquals($exchangeAndMarketId, $result);
    }

    /**
     * Tests that getMarketAndExchangeIdFromRegistry returns false if it fails to find line
     * check results
     *
     * @covers AccountChange_ExchangeHelper::getMarketAndExchangeIdFromRegistry
     *
     * @return void
     **/
    public function testGetMarketAndExchangeIdFromRegistryErrorCondition()
    {

       AccountChange_Registry::instance()->setEntry('lineCheckResult', null);

       $helper = new AccountChange_ExchangeHelper();
       $result = $helper->getMarketAndExchangeIdFromRegistry();

       $this->assertEquals(false, $result);
    }
}
