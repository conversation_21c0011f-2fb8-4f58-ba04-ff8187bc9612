<?php

/**
 * Confirmation of the Terms and Conditions
 *
 * Acceptance of the Terms and Coniditions requirement for the wizard
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-09-29
 */

use Plusnet\Feature\FeatureToggleManager;

/**
 * AccountChange_TermsAndConditions class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_TermsAndConditions extends Mvc_WizardRequirement
{
    /**
     * Key used multiple times to retrieve and add values from/to arrays
     */
    const SELECTED_CONTRACT_DURATION_KEY = 'selectedContractDuration';
    const DISCOUNTED_PRODUCT_COST_KEY = 'objDiscountedProductCost';
    const DISCOUNT_AMOUNT_KEY = 'discountAmount';
    const DISCOUNT_AMOUNT_BROADBAND_KEY = 'discountAmountBroadband';
    const CURRENT_BASE_PRICE_BROADBAND_KEY = 'currentBasePriceBroadband';
    const LINE_RENTAL_DISCOUNT_VALUE_KEY = 'lineRentalDiscountValue';
    const ONGOING_LINE_RENTAL_PRICE_KEY = 'ongoingLineRentalPrice';
    const NEW_LINE_RENT_COST_KEY = 'intNewLineRentCost';
    const LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY = 'lineRentalPromoDiscountType';
    const DISCOUNT_AMOUNT_LINE_RENTAL_KEY = 'discountAmountLineRental';
    const OUTSTANDING_FEES_KEY = 'intOutstandingFees';
    const OUTSTANDING_FEES_EX_VAT_KEY = 'intOutstandingFeesExVat';
    const POSTAGE_AND_PACKAGING = 'Postage and Packaging';
    const ONE_OFF_CHARGES_KEY = 'arrOneOffCharges';
    const OUTSTANDING_CHARGES_KEY = 'strOutstandingCharges';
    const NEW_COST_KEY = 'intNewCost';
    const ALLOWED_INSTANT_CHANGE_ACTORS = array('PLUSNET_STAFF', 'PLUSNET_AUTOMATED_PROCESS');

    /**
     * Inputs for this requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'bolAgreed' => 'external:Custom',
        'bolTakeover' => 'external:Custom',
        'bolTakeoverAgreed' => 'external:Custom:optional',
        'bolAdslTakeover' => 'external:Bool:optional',
        'bolWlrTakeover' => 'external:Bool:optional',
        'vchConsentVersionHandle' => 'external:Custom:optional',
        'arrCharges' => 'external:Custom:optional',
        'bolOneOffCharges' => 'internal:Int:optional',
        'intTotalToPayToday' => 'internal:Int:optional'
    );

    /**
     * Boolean as to whether there are changes
     *
     * @var boolean
     */
    protected $bolCharges = false;

    /**
     * Boolean as to whether we are taking pro rata charges
     *
     * @var boolean
     */
    protected $bolProRataCharges = false;

    /**
     * Account Change Manager
     *
     * @var AccountChange_Manager
     */
    protected $objAccountChangeManager = null;

    /**
     * Financial Additional Charge Helper
     *
     * @var Financial_AdditionalChargeHelper
     */
    protected $additionalChargeHelper = null;


    /**
     * Has the promo code been invalidated?
     *
     * @var bool
     */
    private $promoCodeInvalidated = false;

    /**
     *
     * @var string|null $broadBandCallerDisplay Broadband Caller Display Flag
     *
     */
    protected $broadBandCallerDisplay = null;

    public function __construct(Financial_AdditionalChargeHelper $additionalChargeHelper = null)
    {
        $this->additionalChargeHelper = $additionalChargeHelper;
    }

    /**
     * Validation for when we are agreeing the terms and conditions
     *
     * @param boolean $bolAgreed If agreed
     *
     * @return array
     */
    public function valAgreed($bolAgreed)
    {
        $arrValidatedReturn = array();

        $bolAgreed = ('on' == $bolAgreed);
        if (!$bolAgreed) {
            $this->addValidationError('bolAgreed', 'MISSING');
        }

        $arrValidatedReturn['bolAgreed'] = $bolAgreed;
        $arrValidatedReturn['intTotalOneOffCharge'] = $this->getOneOffChargeTotal();
        return $arrValidatedReturn;
    }

    /**
     * Validation for when we are agreeing to take control of the customers service from another provider
     *
     * @param boolean $bolTakeoverAgreed       If agreed
     * @param boolean $bolTakeover             If this is a service takeover
     * @param string  $vchConsentVersionHandle The handle representing the consent version
     *
     * @return array
     */
    public function valTakeoverAgreed($bolTakeoverAgreed, $bolTakeover, $vchConsentVersionHandle)
    {
        $arrValidatedReturn = array();

        if ($bolTakeover === "true") {
            if ($bolTakeoverAgreed != null) {
                $bolTakeoverAgreed = ('on' == $bolTakeoverAgreed);
                if (!$bolTakeoverAgreed) {
                    $this->addValidationError('bolTakeoverAgreed', 'UNCONFIRMED');
                }
            } else {
                $this->addValidationError('bolTakeoverAgreed', 'UNCONFIRMED');
            }
        }
        $arrValidatedReturn['bolTakeoverAgreed'] = $bolTakeoverAgreed;
        $arrValidatedReturn['bolTakeover'] = $bolTakeover;
        $arrValidatedReturn['vchConsentVersionHandle'] = $vchConsentVersionHandle;
        return $arrValidatedReturn;
    }

    /**
     * Validation for the charges that we can take
     *
     * @param array $arrCharges Charges array
     *
     * @return array
     */
    public function valCharges($arrCharges)
    {
        $arrValidKeys = array('strCanxBb', 'strCanxHp');
        $arrValidatedReturn = array();

        if (!empty($arrCharges)) {
            foreach (array_keys($arrCharges) as $strKey) {
                if (!in_array($strKey, $arrValidKeys)) {
                    $this->addValidationError('arrCharges', 'INVALID');
                }
            }
        }

        $arrValidatedReturn['arrCharges'] = $arrCharges;

        return $arrValidatedReturn;
    }

    /**
     * Get the new requirements depending on the input we currently have
     *
     * @return array
     */
    public function getNewReqs()
    {
        $arrNewReqs = array();

        $arrWlrProduct = null;
        if ($this->isApplicationStateVariable('arrWlrProduct')) {
            $arrWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');
        }

        $intNewWlrId = null;
        if ($this->isApplicationStateVariable('intNewWlrId')) {
            $intNewWlrId = $this->getApplicationStateVariable('intNewWlrId');
        }

        $bolWlr = ((!empty($arrWlrProduct['intOldWlrId']) || !empty($intNewWlrId))
            && $arrWlrProduct['intOldWlrId'] != $intNewWlrId);

        $isCustomerSwitchingFromGbWrToJlp = AccountChange_JohnLewis::isCustomerSwitchingFromGbWrToJlp(
            $this->getApplicationStateVariable('intOldSdi'),
            $this->getApplicationStateVariable('intNewSdi')
        );

        // As we can only switch from Bauer products to Plus.net, we can assume that if the SDIs
        // differ then we are switching to Plus.net
        $isCustomerSwitchingFromBauToPn =
            (strpos($this->getApplicationStateVariable('productIsp'), 'bauer') === 0
                && $this->getApplicationStateVariable('intOldSdi') != $this->getApplicationStateVariable('intNewSdi'));

        if ($bolWlr || $isCustomerSwitchingFromGbWrToJlp || $isCustomerSwitchingFromBauToPn) {
            // If customer has added WLR product
            // or customer has switched from a greenbee/waitrose product to a john lewis product
            // then end-user needs to pay using DD for this.
            // We are using existing DD payment option in AccountChange to handle DD payment
            $arrNewReqs[] = 'AccountChange_PaymentMethodDetails';
        }

        $businessActor = $this->getApplicationStateVariable('objBusinessActor');

        if ($this->hasOneOffCharges($businessActor)) {
            // Has one-off payment, need to send end-user to GImP for take payment
            if ('PLUSNET_STAFF' != $businessActor->getUserType()) {
                $arrNewReqs[] = 'AccountChange_PaymentPortal';
            } else {
                $arrNewReqs[] = 'AccountChange_PaymentWorkplace';
            }
            if (!$bolWlr) {
                // Add in the final confirm details page only if there are any one-off payments
                // This view/requirement is needed for the Wizard app to complete once we are redirected back from GImP
                // Only needed if we are not collecting DD, if DD is there then that will be the final requirement
                $arrNewReqs[] = 'AccountChange_ConfirmDetails';
            }
        }

        return $arrNewReqs;
    }

    /**
     * Are there any changes to the account configuration
     *
     * We don't want to show them the the summary screen if nothing has changed
     * https://workplace.plus.net/programme_tool/problem.html?problem_id=54967
     * We would rather show them the message, saying that nothing has changed
     * so would they like to change something
     *
     * @return boolean
     */
    protected function areThereAnyChanges()
    {
        if ($this->hasBroadbandProductChanged()
            || $this->hasWlrProductChanged()
        ) {
            return true;
        }

        return false;
    }

    /**
     * Calls into product change plan client to get the solus version of a dual play product
     * and vice versa
     *
     * @param int $serviceDefinitionId Service definition id
     *
     * @return array
     **/
    protected function getServiceDefinitionVariants($serviceDefinitionId)
    {
        $client = \BusTier_BusTier::getClient('productChangePlan');
        return $client->getServiceDefinitionVariants($serviceDefinitionId);
    }

    /**
     * Have any changes been made to the broadband product?
     *
     * @return boolean
     */
    protected function hasBroadbandProductChanged()
    {
        // BB Contract Changes
        if ($this->isApplicationStateVariable('bolContractResetBroadband')) {
            if ($this->getApplicationStateVariable('bolContractResetBroadband')) {
                return true;
            }
        }

        // BB change
        if ($this->getApplicationStateVariable('bolHousemove') ||
            $this->getApplicationStateVariable('intOldSdi') != $this->getApplicationStateVariable('intNewSdi')) {
            $broadbandProductVariants = $this->getServiceDefinitionVariants(
                $this->getApplicationStateVariable('intOldSdi')
            );
            //ignore if just doing solus dualplay swap as no real change to the product
            if (!$this->isApplicationStateVariable('bolHousemove')
                && (in_array($this->getApplicationStateVariable('intOldSdi'), $broadbandProductVariants)
                    && in_array($this->getApplicationStateVariable('intNewSdi'), $broadbandProductVariants))
            ) {
                return false;
            }
            return true;
        }
    }

    /**
     * Have any changes been made to the WLR prdouct?
     *
     * @return boolean
     */
    protected function hasWlrProductChanged()
    {
        // WLR Contract Changes
        if ($this->isApplicationStateVariable('bolContractResetWlr')) {
            if ($this->getApplicationStateVariable('bolContractResetWlr')) {
                return true;
            }
        }

        // WLR change
        $arrWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');
        $intOldWlrId = $arrWlrProduct['intOldWlrId'];

        $intNewWlrId = '';
        if ($this->isApplicationStateVariable('intNewWlrId')) {
            $intNewWlrId = $this->getApplicationStateVariable('intNewWlrId');
        }

        if ($intNewWlrId != $intOldWlrId && $intNewWlrId != 0) { // 0 being no wlr selected
            return true;
        }

        if ($this->isApplicationStateVariable('strWlrContract')) {
            $strWlrContract = $this->getApplicationStateVariable('strWlrContract');

            if ($strWlrContract != $arrWlrProduct['strContractHandle']) {
                return true;
            }
        }
    }

    /**
     * Describe
     * Pull all the information needed for the view of this requirement
     *
     * @param array $arrValidatedApplicationData Validated application data
     *
     * @return array
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        $bolChangesMade = $this->areThereAnyChanges();
        $objCoreService = $this->getApplicationStateVariable('objCoreService');

        /** userType might be set around line 497, but initialising here to account
         * for logic around C2F journey towards the bottom of the method */
        $userType = null;

        if ($bolChangesMade) {
            $arrReturn = array();
            // Setup
            $objOngoingProductCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
            $objSelectedProductCostTotal = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
            $objSelectedProductCostTotalNoDiscounts = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);
            $intOldSdi = $this->getApplicationStateVariable('intOldSdi');
            $intNewSdi = $this->getApplicationStateVariable('intNewSdi');
            $strIsp = new Val_ISP($objCoreService->getIsp());
            $objWlrProduct = null;
            $arrOneOffCharges = array();
            $objConnectivityProduct = null;
            $objBusinessActor = $this->getApplicationStateVariable('objUserActor');
            $intServiceId = $objBusinessActor->getExternalUserId();

            $arrProductConfigurations = $this->createProductConfigurations();
            $objAccountChangeManager = $this->getAccountChangeManager($arrProductConfigurations);

            $this->objAccountChangeManager = $objAccountChangeManager;

            $arrReturn['promoCodeValid'] = true;
            try {
                $this->handleC2mPromotionPostJourneyValidation($intServiceId, $strIsp, $intNewSdi, $arrValidatedApplicationData);
            } catch (AccountChange_InvalidAccountChangeOrderException $e) {
                $arrReturn['promoCodeValid'] = false;
            }

            // Broadband work
            if ($this->isApplicationStateVariable('intNewSdi')) {
                $arrNewBroadbandProduct = $arrValidatedApplicationData['arrSelectedBroadband'];

                $objDiscountedProductCost = null;

                if ($arrNewBroadbandProduct[self::NEW_COST_KEY] instanceof I18n_Currency) {
                    if (!empty($arrNewBroadbandProduct[self::DISCOUNT_AMOUNT_KEY]) && $arrNewBroadbandProduct[self::DISCOUNT_AMOUNT_KEY] instanceof I18n_Currency) {
                        $objDiscountedProductCost = $arrNewBroadbandProduct[self::NEW_COST_KEY];
                        $objOngoingProductCost =
                            new I18n_Currency(
                                AccountChange_Manager::CURRENCY_UNIT,
                                $arrNewBroadbandProduct[self::NEW_COST_KEY]->toDecimal() +
                                $arrNewBroadbandProduct[self::DISCOUNT_AMOUNT_KEY]->toDecimal()
                            );
                    } else {
                        $objOngoingProductCost = $arrNewBroadbandProduct[self::NEW_COST_KEY];
                    }
                }

                $objSelectedProductCost = (is_null($objDiscountedProductCost))
                    ? $objOngoingProductCost
                    : $objDiscountedProductCost;

                $objSelectedProductCostTotalNoDiscounts = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $objOngoingProductCost->toDecimal() + $objSelectedProductCostTotal->toDecimal()
                );

                $objSelectedProductCostTotal = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $objSelectedProductCost->toDecimal() + $objSelectedProductCostTotal->toDecimal()
                );


                if ($arrNewBroadbandProduct[self::CURRENT_BASE_PRICE_BROADBAND_KEY] instanceof I18n_Currency &&
                    $arrNewBroadbandProduct[self::DISCOUNT_AMOUNT_BROADBAND_KEY] instanceof I18n_Currency
                ) {
                    $arrReturn[self::CURRENT_BASE_PRICE_BROADBAND_KEY] = $arrNewBroadbandProduct[self::CURRENT_BASE_PRICE_BROADBAND_KEY];
                    $arrReturn[self::DISCOUNT_AMOUNT_BROADBAND_KEY] = $arrNewBroadbandProduct[self::DISCOUNT_AMOUNT_BROADBAND_KEY];
                    $arrReturn['ongoingBroadbandPrice'] = new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $arrNewBroadbandProduct[self::CURRENT_BASE_PRICE_BROADBAND_KEY]->toDecimal() + $arrNewBroadbandProduct[self::DISCOUNT_AMOUNT_BROADBAND_KEY]->toDecimal()
                    );
                }
            }

            $arrReturn['intContractLengthMonths'] = $arrNewBroadbandProduct['intContractLengthMonths'];
            $arrReturn['intNewWlrId'] = 0;

            if (isset($arrValidatedApplicationData['arrSelectedWlr'])) {
                // New WLR Work
                $arrNewWlrProduct = $arrValidatedApplicationData['arrSelectedWlr'];
                $arrReturn[self::ONGOING_LINE_RENTAL_PRICE_KEY] = $arrNewWlrProduct[self::NEW_LINE_RENT_COST_KEY];

                if ($arrNewWlrProduct[self::NEW_COST_KEY] instanceof I18n_Currency) {
                    $objSelectedProductCostTotal = new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $arrNewWlrProduct[self::NEW_COST_KEY]->toDecimal() +
                        $objSelectedProductCostTotal->toDecimal()
                    );

                    $objSelectedProductCostTotalNoDiscounts = new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $arrNewWlrProduct['intNewLineRentCost']->toDecimal() +
                        $arrNewWlrProduct['intNewCallPlanCost']->toDecimal() +
                        $objSelectedProductCostTotalNoDiscounts->toDecimal()
                    );
                }
                $arrReturn['intNewWlrId'] = $arrValidatedApplicationData['intNewWlrId'];
            } else {
                // Existing WLR product work
                $phoneProducts = $this->getApplicationStateVariable('arrWlrProduct');
                $callPlanCost = 0;
                $lineRentalCost = 0;
                if ($phoneProducts) {
                    if (!empty($phoneProducts['intCallPlanCost'])
                        && $phoneProducts['intCallPlanCost'] instanceof I18n_Currency) {
                        $callPlanCost = $phoneProducts['intCallPlanCost']->toDecimal();
                    }
                    if (!empty($phoneProducts['intLineRentCost'])
                        && $phoneProducts['intLineRentCost'] instanceof I18n_Currency) {
                        $lineRentalCost = $phoneProducts['intLineRentCost']->toDecimal();
                    }
                }

                $arrReturn[self::ONGOING_LINE_RENTAL_PRICE_KEY] = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $lineRentalCost
                );

                // There's no WLR product selected so we need to add the current line rental value onto the basket total here..
                $objSelectedProductCostTotal = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $objSelectedProductCostTotal->toDecimal() + $callPlanCost + $lineRentalCost
                );

                $objSelectedProductCostTotalNoDiscounts = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $objSelectedProductCostTotalNoDiscounts->toDecimal() + $callPlanCost + $lineRentalCost
                );
            }

            // C2m Promotion has been applied to BB product, because of this we need to inspect it and see if that promo also has a
            // line rental element.  If it does, then apply it..     lineRentalDiscountValue is only present for c2m promotions.
            if ($arrReturn['promoCodeValid'] && isset($arrNewBroadbandProduct[self::LINE_RENTAL_DISCOUNT_VALUE_KEY])
                && $arrNewBroadbandProduct[self::LINE_RENTAL_DISCOUNT_VALUE_KEY] > 0) {
                // Base price is the discounted price that applies at the point of change..
                $arrReturn['currentBasePriceLineRental'] = AccountChange_DiscountHelper::calculateLeadingPrice(
                    $arrReturn[self::ONGOING_LINE_RENTAL_PRICE_KEY],
                    $arrNewBroadbandProduct[self::LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY],
                    $arrNewBroadbandProduct[self::LINE_RENTAL_DISCOUNT_VALUE_KEY]
                );

                $arrReturn[self::DISCOUNT_AMOUNT_LINE_RENTAL_KEY] =
                    AccountChange_DiscountHelper::calculateDiscountAmount(
                        $arrReturn[self::ONGOING_LINE_RENTAL_PRICE_KEY],
                        $arrNewBroadbandProduct[self::LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY],
                        $arrNewBroadbandProduct[self::LINE_RENTAL_DISCOUNT_VALUE_KEY]
                    );

                if (isset($arrNewBroadbandProduct[self::CURRENT_BASE_PRICE_BROADBAND_KEY])) {
                    $arrReturn['currentBasePrice'] = AccountChange_DiscountHelper::calculateLeadingPrice(
                        $arrNewBroadbandProduct[self::CURRENT_BASE_PRICE_BROADBAND_KEY],
                        $arrNewBroadbandProduct[self::LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY],
                        $arrNewBroadbandProduct[self::LINE_RENTAL_DISCOUNT_VALUE_KEY]
                    );
                    $arrReturn[self::DISCOUNT_AMOUNT_KEY] = AccountChange_DiscountHelper::calculateDiscountAmount(
                        $arrNewBroadbandProduct[self::CURRENT_BASE_PRICE_BROADBAND_KEY],
                        $arrNewBroadbandProduct[self::LINE_RENTAL_PROMO_DISCOUNT_TYPE_KEY],
                        $arrNewBroadbandProduct[self::LINE_RENTAL_DISCOUNT_VALUE_KEY]
                    );
                }

                $objSelectedProductCostTotal = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $objSelectedProductCostTotal->toDecimal()
                );
            }

            $arrOneOffCharges = $this->getOneOffCharges();

            $objOneOffCharge = $this->getOneOffChargeTotal($arrOneOffCharges);

            $arrReturn['intTotalOneOffCharge'] = $objOneOffCharge;
            $objAccountChangeManager->setTotalOneOffCharge($objOneOffCharge);
            $arrReturn[self::ONE_OFF_CHARGES_KEY] = $arrOneOffCharges;

            $this->bolCharges = ($objOneOffCharge->toDecimal() > 0);
            if (isset($arrOneOffCharges['arrFibreProrataEstimate'])) {
                $arrProrataEstimate = $arrOneOffCharges['arrFibreProrataEstimate'];

                $arrReturn['hasFibre']
                    = isset($arrProrataEstimate['hasFibre']) ? $arrProrataEstimate['hasFibre'] : null;

                $arrReturn['fibreInstallation'] = false;

                if ($arrReturn['hasFibre'] == true) {
                    $bolFromFibre = $this->isOldProductFibre();
                    $bolToFibre = $this->isNewProductFibre();

                    // if we are Fibre to Fibre or there's no decUpgradeCharge set in products.service_definitions
                    // there is NO installation fee to be paid
                    $freeSetup = false;
                    $productConfigurations = $this->createProductConfigurations();
                    if ($productConfigurations['objNewBroadband']->getSetupFee() === false) {
                        $freeSetup = true;
                    }

                    $arrReturn['fibreInstallation'] = (!($bolFromFibre && $bolToFibre)) && !$freeSetup;
                }

                $arrReturn['fibreProRataCharge']
                    = isset($arrProrataEstimate['fibreProRataCharge']) ?
                    $arrProrataEstimate['fibreProRataCharge'] : null;

                $arrReturn['liveAppointing']
                    = isset($arrProrataEstimate['liveAppointing']) ? $arrProrataEstimate['liveAppointing'] : null;
            }

            if (isset($arrProductConfigurations['objOldBroadband'])) {
                $arrReturn['bolBroadbandScheduled'] = $arrProductConfigurations['objOldBroadband']->isScheduled();
            }


            $objNextInvoiceDate = I18n_Date::fromString(
                $arrValidatedApplicationData['objCoreService']->getNextInvoiceDate()
            );

            if ($objNextInvoiceDate->fShort() == I18n_Date::now()->fShort()) {
                $objNextInvoiceDate = I18n_Date::fromString(UserdataServiceGetNextNextInvoiceDate($intServiceId));
            }

            if (isset($arrProductConfigurations['objOldWlr'])) {
                $arrReturn['bolWlrScheduled'] = $arrProductConfigurations['objOldWlr']->isScheduled();
            }
            $arrReturn['strNextInvoiceDate'] = $objNextInvoiceDate->toI18nStrHere('STANDARD');

            if (!empty($arrValidatedApplicationData['intNewWlrId'])) {
                $arrReturn['bolWlrEssential'] = AccountChange_Controller::isWlrEssential(
                    $arrValidatedApplicationData['intNewWlrId']
                );
            }
            $arrReturn['intOldWlrId'] = $arrValidatedApplicationData['arrWlrProduct']['intOldWlrId'];

            // Final work
            $arrReturn['intSelectedProductCost'] = $objOngoingProductCost;
            $arrReturn[self::DISCOUNTED_PRODUCT_COST_KEY] = !empty($objDiscountedProductCost) ? $objDiscountedProductCost : null;

            $arrReturn['bolIsDualPlay'] = $this->isDualPlay($intOldSdi);

            $arrReturn['intSelectedProductCostTotal'] = $objSelectedProductCostTotal;
            $arrReturn['selectedProductCostTotalNoDiscounts'] = $objSelectedProductCostTotalNoDiscounts;

            if ($this->hasBroadbandProductChanged()) {
                $objConnectivityProduct = $this->getValDslConnectivityProduct($intNewSdi);
            }

            if ($this->hasWlrProductChanged()) {
                $objWlrProduct = new Val_HomePhoneProduct($arrValidatedApplicationData['intNewWlrId']);
            } elseif (isset($arrValidatedApplicationData['arrWlrProduct']) && is_array($arrValidatedApplicationData['arrWlrProduct']['activeCallFeatures'])) {
                // Activate/deactivate caller display if we're not changing WLR
                $userHasCallerDisplay = false;
                foreach ($arrValidatedApplicationData['arrWlrProduct']['activeCallFeatures'] as $feature) {
                    if ($feature == 'Caller Display') {
                        $userHasCallerDisplay = true;
                        break;
                    }
                }

                if (!$userHasCallerDisplay && $this->getApplicationStateVariable('callerDisplay')) {
                    $this->broadBandCallerDisplay = 'ADD';
                } elseif ($userHasCallerDisplay && !$this->getApplicationStateVariable('callerDisplay')) {
                    $this->broadBandCallerDisplay = 'REMOVE';
                }
            }

            // http://jira.internal.plus.net/browse/JLPR-1466
            if (in_array($objCoreService->getIsp(), array('greenbee', 'waitrose'))) {
                $strIsp = new Val_ISP('johnlewis');
            }

            // Terms and conditions are not deployed to Workplace.
            // Detect if user is workplace, if so then don't set the terms object.
            $userType = $this->getApplicationStateVariable('objBusinessActor');

            if ('PLUSNET_STAFF' != $userType->getUserType()) {
                $arrReturn['strTermsAndConditions'] = $this->getSignupAppTermsAndConditionsText(
                    $strIsp,
                    $objWlrProduct,
                    $objConnectivityProduct
                );
            } else {
                $arrReturn['strTermsAndConditions'] = false;
            }

            $objResult = $this->getApplicationStateVariable('objLineCheckResult');
            $arrReturn['arrLineCheckResults'] = $objResult->getResultAsArray();

            // Collect the current product speed ranges
            $currentProductSpeedRanges = $this->getMinAndMaxSpeedRanges();
            $arrReturn['currentProductDownloadSpeedRangeMin'] = $currentProductSpeedRanges['downloadSpeedRangeMin'];
            $arrReturn['currentProductDownloadSpeedRangeMax'] = $currentProductSpeedRanges['downloadSpeedRangeMax'];

            $broadbandServiceProvider = $arrReturn['arrLineCheckResults']['strSpName'];

            if (strpos($broadbandServiceProvider, "PLUSNET") === false) {
                $arrReturn['bolBroadbandTakeover'] = true;
            } else {
                $arrReturn['bolBroadbandTakeover'] = false;
            }

            $arrReturn['bolChangesMade'] = true;

            $arrReturn['bolAppointmentBooked'] = $this->isApplicationStateVariable('appointment')
                || $this->isApplicationStateVariable('appointmentdate1');

            if ($this->isApplicationStateVariable('arrSelectedBroadband')) {
                $arrReturn['selectedBroadband'] = $this->getSelectedBroadband();
                $arrReturn['estimatedSpeed'] = $arrReturn['selectedBroadband']['maxDownstreamSpeed'];
            }

            if (isset($arrValidatedApplicationData['appointingType'])) {
                if ($arrValidatedApplicationData['appointingType']['serviceHandle'] == 'FTTC') {
                    $arrReturn['fibreAppointments'] = $this->getFttcAppointmentData($arrValidatedApplicationData);
                }
            }

            $arrReturn['hasOneOffCharges'] = $this->hasOneOffCharges($objBusinessActor);

            $arrReturn['isp'] = $strIsp;

            $arrReturn = $this->setPostageAndPackagingCosts($arrReturn);

            $contractDurationHelper = AccountChange_ServiceManager::getService('ContractDurationHelper');

            if ($contractDurationHelper->shouldDisplayContractDuration(
                $intOldSdi,
                $intNewSdi,
                $arrValidatedApplicationData,
                $objCoreService
            )) {
                $arrReturn[self::SELECTED_CONTRACT_DURATION_KEY] = $contractDurationHelper->getContractDuration(
                    $strIsp,
                    $intNewSdi,
                    $arrValidatedApplicationData
                );
            }

            if (!$arrReturn['promoCodeValid']) {
                $this->removePromotion($arrReturn, $arrValidatedApplicationData);
            }
        } else {
            $arrReturn['bolChangesMade'] = false;
        }

        $arrReturn['uxtScheduledChangeDate'] = $this->getScheduledChangeDate($objCoreService->getServiceId());

        if ($arrReturn['uxtScheduledChangeDate'] === false) {
            if (isset($arrReturn['strNextInvoiceDate'])) {
                $arrReturn['uxtScheduledChangeDate'] = $arrReturn['strNextInvoiceDate'];
            } else {
                $arrReturn['uxtScheduledChangeDate'] = UserdataServiceGetNextNextInvoiceDate(
                    $objCoreService->getServiceId()
                );
            }
        }

        if ($this->isC2fToggleSet() &&
            $bolChangesMade &&
            $userType->getUserType() != 'PLUSNET_STAFF' &&
            $objCoreService->isPlusnetUser() &&
            $objCoreService->getAdslDetails()['type'] == 'residential') {
            $broadbandProductCost = null;
            $callPlanCost = null;
            $lineRentalCost = null;
            $totalLightweightBasketPrice = null;
            $arrReturn['keepCurrentBroadbandProduct'] = null;

            /**
             * If we are on a lightweight journey we still want to show all the products in the basket,
             * not just changed products
             * We then need to calculate a new total basket price
             *
             * DLIFE-219
             */
            if ($arrValidatedApplicationData['bolChangeBroadbandOnly']) {

                /**
                 * We're on a change broadband only lightweight journey
                 * Let's go and get the unchanged call plan price and line rental
                 * we'll then combine them into a total basket price
                 *
                 * DLIFE-219
                 */
                $phoneProducts = $this->getApplicationStateVariable('arrWlrProduct');
                if ($phoneProducts) {
                    $callPlanCost = $phoneProducts['intCallPlanCost']->toDecimal();
                    $lineRentalCost = $phoneProducts['intLineRentCost']->toDecimal();
                }
            }

            /**
             * Lightweight change call plan only journey
             */
            if ($arrValidatedApplicationData['bolWlrOnly']) {

                /**
                 * We're on a change call plan only journey
                 * Let's set the unchanged broadband product price
                 * Then set the new Wlr product price and new line rental price
                 *
                 * DLIFE-219
                 */
                $broadbandProductCost = $arrValidatedApplicationData['currentBroadbandPrice']->toDecimal();
                $callPlanCost = $arrNewWlrProduct['intNewCallPlanCost']->toDecimal();
                $lineRentalCost = $arrNewWlrProduct[self::NEW_LINE_RENT_COST_KEY]->toDecimal();
                $totalLightweightBasketPrice = $broadbandProductCost + $callPlanCost + $lineRentalCost;
                $arrReturn['totalLightweightBasketPrice'] = $totalLightweightBasketPrice;
            }

            /**
             * Journey where customer has kept existing broadband product
             */
            if ($arrReturn['selectedBroadband']['strNewProduct'] == null) {
                $arrReturn['keepCurrentBroadbandProduct'] = true;
                $broadbandProductCost = $arrValidatedApplicationData['currentBroadbandPrice']->toDecimal();
                $callPlanCost = $arrNewWlrProduct['intNewCallPlanCost']->toDecimal();
                $lineRentalCost = $arrNewWlrProduct[self::NEW_LINE_RENT_COST_KEY]->toDecimal();
                $totalLightweightBasketPrice = $broadbandProductCost + $callPlanCost + $lineRentalCost;
                $arrReturn['totalLightweightBasketPrice'] = $totalLightweightBasketPrice;
            }

            $salesJourneyViewHelper = $this->getSalesJourneyViewHelper();
            $arrReturn['wizardInstanceID'] = $salesJourneyViewHelper::getWizardInstanceID('AccountChange');


            if (isset($arrReturn[self::DISCOUNTED_PRODUCT_COST_KEY])) {
                $monthlySelectedProductSavingDuringDiscountedPeriod = $arrReturn['intSelectedProductCost']->toDecimal()
                    - $arrReturn[self::DISCOUNTED_PRODUCT_COST_KEY]->toDecimal();

                if (isset($arrReturn[self::DISCOUNT_AMOUNT_LINE_RENTAL_KEY])
                    && $arrReturn[self::DISCOUNT_AMOUNT_LINE_RENTAL_KEY] instanceof I18n_Currency) {
                    $monthlySelectedProductSavingDuringDiscountedPeriod
                        += $arrReturn[self::DISCOUNT_AMOUNT_LINE_RENTAL_KEY]->toDecimal();
                }

                $totalMonthlySelectedProductPriceAfterDiscountPeriod =
                    $arrReturn['intSelectedProductCostTotal']->toDecimal()
                    + $monthlySelectedProductSavingDuringDiscountedPeriod;
                $arrReturn['totalMonthlySelectedProductPriceAfterDiscountPeriod'] =
                    new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $totalMonthlySelectedProductPriceAfterDiscountPeriod
                    );
                $arrReturn['discountExpiryDate'] = '';

                // Get the next invoice date, make it into a format we can accept like 22/03/2019
                $startDiscountDate = DateTime::createFromFormat(
                    'd/m/Y',
                    str_replace('-', '/', $objCoreService->getNextInvoiceDate()->fShort())
                );
                $discountLength = $arrNewBroadbandProduct['presetDiscount']['intDiscountLength'];
                $arrReturn['endDiscountDate'] = $this->addMonth($startDiscountDate, $discountLength)->format('F jS Y');

                /**
                 * Combine the prices into a basket price. This is only used for the broadband lightweight journey.
                 * The full fat journeys display all the changed products and use that
                 *
                 * DLIFE-219
                 */
                if ($arrValidatedApplicationData['bolChangeBroadbandOnly']) {
                    $totalLightweightBasketPrice = $arrReturn[self::DISCOUNTED_PRODUCT_COST_KEY]->toDecimal()
                        + $callPlanCost + $lineRentalCost;

                    if (!empty($arrReturn[self::DISCOUNT_AMOUNT_LINE_RENTAL_KEY])) {
                        $totalLightweightBasketPrice -= $arrReturn[self::DISCOUNT_AMOUNT_LINE_RENTAL_KEY]->toDecimal();
                    }

                    $arrReturn['totalLightweightBasketPriceAfterDiscountPeriod'] =
                        $totalMonthlySelectedProductPriceAfterDiscountPeriod;
                    $arrReturn['totalLightweightBasketPrice'] = $totalLightweightBasketPrice;
                }
            }

            $arrReturn['currentProduct']['canChangeHomePhonePackage'] = $_SESSION['canChangeHomePhonePackage'];
            $salesJourneyViewHelper::setCompletedViews(['C2FBroadband', 'C2FHomephone']);
            $arrReturn['completedViews'] = $salesJourneyViewHelper::getCompletedViews();

            /**
             * Special case for residential lightweight journeys.
             * This is not the ideal solution but the registry  seems to be wiped when hitting PaymentMethodDetails
             * I also can't seem to retain changes to the validatedApplicationStateData from the T&C step either to
             * PaymentMethodDetails.
             *
             * @FIXME Find a cleaner method of passing a lightweight journey basket price between this and the next step in the journey
             */
            if ($totalLightweightBasketPrice) {
                $_SESSION['totalLightweightBasketPrice'] = $totalLightweightBasketPrice;
            }
        }

        $arrReturn['oldSdi'] = $this->getApplicationStateVariable('intOldSdi');
        $arrReturn['newSdi'] = $this->getApplicationStateVariable('intNewSdi');
        $arrReturn['promoCodeInvalidated'] = $this->promoCodeInvalidated;

        return $arrReturn;
    }

    /**
     * Is the c2f toggle set
     *
     * @return bool
     */
    public function isC2fToggleSet()
    {
        return AccountChange_C2mSalesChannels::isC2fToggleSet();
    }

    /**
     * @param DateTime $date
     * @param int      $add
     *
     * @return DateTime
     */
    private function addMonth($date, $add = 0)
    {
        $newDate = clone $date;
        $newDate->add(new DateInterval('P' . $add . 'M'));
        return $newDate;
    }

    /**
     * get signup application terms and conditions text
     *
     * @param Val_ISP                 $isp              isp
     * @param Val_HomePhoneProduct    $homePhoneProduct home phone product
     * @param Val_ConnectivityProduct $broadbandProduct broadband product
     *
     * @return string Terms and Conditions text
     */
    public function getSignupAppTermsAndConditionsText($isp, $homePhoneProduct, $broadbandProduct)
    {

        $locale = I18n_LocaleManager::getCurrentLocale();
        $termsAndConditionsText = '';
        try {
            $objTermsAndConditions = new SignupApplication_TermsAndConditions(
                $isp,
                $homePhoneProduct,
                $locale,
                $broadbandProduct
            );
            $termsAndConditionsText = $objTermsAndConditions->getTermsAndConditions();
        } catch (Exception $e) {
            error_log(
                sprintf(
                    "Unable to get terms and conditions text - %s : %s\n%s\n\n",
                    $e->getFile(),
                    $e->getLine(),
                    $e->getMessage()
                )
            );
        }
        return $termsAndConditionsText;
    }

    /**
     * Returns if the old product is a fibre product
     *
     * @return bool
     */
    protected function isOldProductFibre()
    {
        $fibreHelper = new AccountChange_FibreHelper();
        return $fibreHelper->isFibreProduct($this->getApplicationStateVariable('intOldSdi'));
    }

    /**
     * Returns if the product is Dual Play
     *
     * @param int $intOldSdi Old SDI
     *
     * @return bool
     */
    protected function isDualPlay($intOldSdi)
    {
        $productFamily = ProductFamily_Factory::getFamily($intOldSdi);
        return $productFamily->isDualPlay();
    }

    /**
     * Returns if the new product is a fibre product
     *
     * @return bool
     */
    protected function isNewProductFibre()
    {
        $fibreHelper = new AccountChange_FibreHelper();
        return $fibreHelper->isFibreProduct($this->getApplicationStateVariable('intNewSdi'));
    }

    /**
     * Returns FTTC appointment data
     *
     * @param array $arrValidatedApplicationData Validated application data
     *
     * @return array
     */
    private function getFttcAppointmentData($arrValidatedApplicationData)
    {
        $arrReturn = array(
            'hasInstallationAppointment' => false
        );

        if (isset($arrValidatedApplicationData['appointment'])) {
            if (isset($arrValidatedApplicationData['appointment'])
                && preg_match('/^\d\d\/\d\d\/\d\d\d\d(AM|PM)$/', $arrValidatedApplicationData['appointment'])
            ) {
                $arrReturn['hasInstallationAppointment'] = true;
                $arrReturn['alternativeAppointmentDates'] = false;
                $arrReturn['appointmentDate'] = substr($arrValidatedApplicationData['appointment'], 0, 10);
                $arrReturn['appointmentTime'] = substr($arrValidatedApplicationData['appointment'], 10, 2);
            }
        } else {
            $datesAreSet = false;

            for ($count = 1; $count <= 3; $count++) {
                if (isset($arrValidatedApplicationData['appointmentdate' . $count])
                    && isset($arrValidatedApplicationData['appointmenttime' . $count])
                    && preg_match('/^\d{10}$/', $arrValidatedApplicationData['appointmentdate' . $count])
                    && preg_match('/^(AM|PM)$/', $arrValidatedApplicationData['appointmenttime' . $count])
                ) {
                    $arrReturn['appointmentDate' . $count]
                        = date('d/m/Y', $arrValidatedApplicationData['appointmentdate' . $count]);
                    $arrReturn['appointmentTime' . $count]
                        = $arrValidatedApplicationData['appointmenttime' . $count];
                    $datesAreSet = true;
                }
            }

            if ($datesAreSet) {
                $arrReturn['hasInstallationAppointment'] = true;
                $arrReturn['alternativeAppointmentDates'] = true;
            }
        }

        return $arrReturn;
    }

    /**
     * Get the selected broadband product from an application state variable
     *
     * @return array
     **/
    public function getSelectedBroadband()
    {
        return $this->getApplicationStateVariable('arrSelectedBroadband');
    }

    /**
     * Returns a new Val_DSLConnectivityProduct object
     *
     * @param integer $serviceDefinitionId Service Definition ID
     *
     * @return Val_DSLConnectivityProduct
     */
    protected function getValDslConnectivityProduct($serviceDefinitionId)
    {
        return new Val_DSLConnectivityProduct($serviceDefinitionId);
    }

    /**
     * Get the array of one off charges that we need to deal with
     *
     * If you are making changes to this method, or its subsequent methods,
     * then you are likely to need to change AccountChange_Controller::generateInvoiceItems
     *
     * @return array
     */
    private function getOneOffCharges()
    {
        return array_merge($this->getOneOffChargesBb(), $this->getOneOffChargesWlr(), $this->getHardwareCharges());
    }

    /**
     * Get the total cost of the one off charge
     *
     * @param array $arrOneOffCharges One of charges previously loaded
     *
     * @return I18n_Currency
     */
    private function getOneOffChargeTotal($arrOneOffCharges = null)
    {
        $objOneOffCharge = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 0);

        if ($arrOneOffCharges == null) {
            // load the one of charges
            $arrOneOffCharges = $this->getOneOffCharges();
        }

        foreach ($arrOneOffCharges as $arrOneOffCharge) {
            if (is_array($arrOneOffCharge) && isset($arrOneOffCharge[self::OUTSTANDING_FEES_KEY])) {
                $objOneOffCharge = new I18n_Currency(
                    AccountChange_Manager::CURRENCY_UNIT,
                    $objOneOffCharge->toDecimal() +
                    $arrOneOffCharge[self::OUTSTANDING_FEES_KEY]->toDecimal()
                );
            }
        }

        return $objOneOffCharge;
    }

    /**
     * Add the hardware packaging cost if hardware was selected
     *
     * @return array
     */
    public function getHardwareCharges()
    {
        $isBusiness = false;
        $charges = array();

        if ($this->isFibreUpgradeCampaign()) {
            return $charges;
        }

        if ($this->getApplicationStateVariable('waivePostage')) {
            return $charges;
        }

        $newServiceDefinitionId = $this->getApplicationStateVariable('intNewSdi');
        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $serviceDefinition = $objDatabase->getServiceDefinitionDetails($newServiceDefinitionId);
        $productIsp = $serviceDefinition['isp'];

        // [SALES-893] Plusnet residential postage is now free in signup but charged here.
        // Refer to c2m to get the price rather than the (now 0.00) db config.
        if (($productIsp === 'plus.net')
            && $this->isApplicationStateVariable('hardwareOption')
            && '' != $this->getApplicationStateVariable('hardwareOption')
        ) {
            if ($this->isApplicationStateVariable('bolIsBusiness')) {
                $isBusiness = $this->getApplicationStateVariable('bolIsBusiness');
            }

            if ($isBusiness) {
                $c2mPriceArray = $this->getAdditionalChargeHelper()->getBusinessPostageAndPackagingFromC2m();
            } else {
                $c2mPriceArray = $this->getAdditionalChargeHelper()->getResidentialPostageAndPackagingFromC2m();
            }
            $c2mCharge = $c2mPriceArray['amount'];
            $c2mChargeExVat = $c2mPriceArray['exVatAmount'];
            if (is_numeric($c2mCharge) && is_numeric($c2mChargeExVat) && $c2mCharge > 0) {
                $charges[] = array(
                    self::OUTSTANDING_CHARGES_KEY => self::POSTAGE_AND_PACKAGING,
                    self::OUTSTANDING_FEES_KEY => new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $c2mCharge
                    ),
                    self::OUTSTANDING_FEES_EX_VAT_KEY => new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $c2mChargeExVat
                    )
                );

                $this->bolProRataCharges = true;
                return $charges;
            }
        }

        if ($this->isApplicationStateVariable('hardwareOption')
            && '' != $this->getApplicationStateVariable('hardwareOption')
            && (!$this->freePartnerHardwarePostage())
        ) {
            $additionalCharge = $this->getAdditionalChargeHelper()->getAdditionalCharge(
                $this->getApplicationStateVariable('intNewSdi'),
                Financial_AdditionalChargeType::POSTAGE_AND_PACKAGING
            );
            if (!empty($additionalCharge) && ($additionalCharge->getAmount() > 0)) {
                $charges[] = array(
                    self::OUTSTANDING_CHARGES_KEY => self::POSTAGE_AND_PACKAGING,
                    self::OUTSTANDING_FEES_KEY => new I18n_Currency(
                        AccountChange_Manager::CURRENCY_UNIT,
                        $additionalCharge->getAmount() / 100
                    )
                );

                $this->bolProRataCharges = true;
            }
        }

        return $charges;
    }

    /**
     * Get the one off charges for Adsl
     *
     * If you are making changes to this method,
     * then you are likely to need to change AccountChange_Controller::generateInvoiceItems
     *
     * @return array
     * @throws Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException
     */
    private function getOneOffChargesBb()
    {
        $arrProductConfigurations = $this->createProductConfigurations();
        $intNewSdi = $this->getApplicationStateVariable('intNewSdi');
        $intOldSdi = $this->getApplicationStateVariable('intOldSdi');

        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $arrOneOffCharges = array();

        if (!isset($this->objAccountChangeManager)) {
            $this->objAccountChangeManager = $this->getAccountChangeManager($arrProductConfigurations);
        }

        // One off charges
        if (isset($arrProductConfigurations['objOldBroadband'])) {
            $arrFibreProrataEstimate = $this->getFibreProRataEstimateCosts($arrProductConfigurations);
            $arrOneOffCharges['arrFibreProrataEstimate'] = $arrFibreProrataEstimate;

            $objProductConf = $arrProductConfigurations['objOldBroadband'];

            if ($objProductConf->getAccountChangeOperation() == AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE
                && !AccountChange_Controller::isEssential($intOldSdi)
                && !AccountChange_Controller::isEssential($intNewSdi)
                && !$objProductConf->isScheduled()
            ) {
                try {
                    $arrCharges = $objProductConf->calculateProRataCharge();
                    if (!empty($arrCharges)) {
                        $arrOneOffCharges[] = $arrCharges;
                        $this->bolProRataCharges = true;
                    }
                } catch (Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException $apiEx) {
                    $message = sprintf(
                        "Message: %s, File: %s, Line: %s",
                        $apiEx->getMessage(),
                        $apiEx->getFile(),
                        $apiEx->getLine()
                    );
                    error_log("Billing API returned error: $message");
                    throw $apiEx;
                } catch (Exception $e) {
                    error_log("Failed to calculate pro-rata charge, Exception was: " . $e);
                }
            }

            // The setup fee applies of there is a charge for the upgrade - but if the customer has WLR,
            // then the fee is deferred, so it is not taken during the change.
            $setupFee = $arrProductConfigurations['objNewBroadband']->getSetupFee();
            $wlrProduct = $this->isApplicationStateVariable('arrWlrProduct')
                ? $this->getApplicationStateVariable('arrWlrProduct') : array();

            if ($setupFee
                && (!isset($wlrProduct['intInstanceId']) || $wlrProduct['intInstanceId'] == '')
                && (!$this->isApplicationStateVariable('intNewWlrId')
                    || $this->getApplicationStateVariable('intNewWlrId') == 0)
            ) {
                $arrOneOffCharges[] = array(self::OUTSTANDING_CHARGES_KEY => $setupFee['description'],
                    self::OUTSTANDING_FEES_KEY => $setupFee['charge']);

                $this->bolProRataCharges = true;
            }
        }

        return $arrOneOffCharges;
    }

    /**
     * Obtains a prorata estimate cost for switching to Fibre and additional variables
     * This only works if the product we are moving to is Fibre and we are doing live appointing
     *
     * @param array $arrProductConfigurations Product configurations to use for calculation
     *
     * @return array
     */
    private function getFibreProRataEstimateCosts($arrProductConfigurations)
    {
        $arrResult = array();

        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $intServiceId = $objCoreService->getServiceId();

        $bolIsFibre = false;
        $bolLiveAppointing = false;

        if ($this->isApplicationStateVariable('intNewSdi')) {
            $bolIsFibre = $this->isNewProductFibre();
        }

        if ($this->isApplicationStateVariable('liveAppointing')) {
            $bolLiveAppointing = $this->getApplicationStateVariable('liveAppointing');
        }

        $arrResult['hasFibre'] = $bolIsFibre;
        $arrResult['liveAppointing'] = $bolLiveAppointing;
        $arrResult['fibreProRataCharge'] = 0;

        $objProductConf = $arrProductConfigurations['objOldBroadband'];

        if ($bolIsFibre && $bolLiveAppointing && $this->isApplicationStateVariable('arrSelectedBroadband')
            && $this->isApplicationStateVariable('arrAdslProductDetails')
            && $this->isApplicationStateVariable('appointment')
        ) {
            $arrSelectedBroadband = $this->getApplicationStateVariable('arrSelectedBroadband');
            $arrAdslProductDetails = $this->getApplicationStateVariable('arrAdslProductDetails');

            $intNewTariffId
                = isset($arrSelectedBroadband) && isset($arrSelectedBroadband['intSelectedTariffID']) ?
                $arrSelectedBroadband['intSelectedTariffID'] : 0;

            $intOldTariffId
                = isset($arrAdslProductDetails) && isset($arrAdslProductDetails['intTariffID']) ?
                $arrAdslProductDetails['intTariffID'] : 0;

            $strAppointmentDate = substr($this->getApplicationStateVariable('appointment'), 0, 10);
            $arrDate = explode('/', $strAppointmentDate);
            $strAppointmentDate = $arrDate[2] . '-' . $arrDate[1] . '-' . $arrDate[0];

            if (!empty($strAppointmentDate) && !empty($intNewTariffId) && !empty($intOldTariffId)) {
                $arrEstimate = $objProductConf->getProRataEstimate(
                    $intServiceId,
                    $strAppointmentDate,
                    $intOldTariffId,
                    $intNewTariffId
                );

                if (!empty($arrEstimate)) {
                    $arrResult['fibreProRataCharge'] = bcdiv($arrEstimate['intFttcEstimateInPence'], 100, 2);
                }
            }
        }

        return $arrResult;
    }

    /**
     * Get the one off charges for Wlr
     *
     * If you are making changes to this method,
     * then you are likely to need to change AccountChange_Controller::generateInvoiceItems
     *
     * @return array
     */
    private function getOneOffChargesWlr()
    {
        $arrProductConfigurations = $this->createProductConfigurations();
        $intNewSdi = $this->getApplicationStateVariable('intNewSdi');
        $intOldSdi = $this->getApplicationStateVariable('intOldSdi');
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $arrOneOffCharges = array();

        if (isset($arrProductConfigurations['objOldWlr']) && isset($arrProductConfigurations['objNewWlr'])) {
            $objOldProductConf = $arrProductConfigurations['objOldWlr'];
            $objNewProductConf = $arrProductConfigurations['objNewWlr'];

            if ($objOldProductConf->getAccountChangeOperation() == AccountChange_Product_Manager::ACCOUNT_CHANGE_UPGRADE
                && !AccountChange_Controller::isEssential($intOldSdi)
                && !AccountChange_Controller::isEssential($intNewSdi)
                && !$objOldProductConf->isScheduled()
            ) {
                $arrCharges = $objNewProductConf->calculateProRataCharge();
                if (!empty($arrCharges)) {
                    $arrOneOffCharges[] = $arrCharges;
                    $this->bolProRataCharges = true;
                }
            }
        }

        return $arrOneOffCharges;
    }

    /**
     * Now that vaildation has passed perform any transforms needed on this requirement's data.
     * By callbacking back through the requirements container the data of
     * other requirements can be changed.
     *
     * @param Mvc_WizardReqList $objReqContainer Requirement container
     *
     * @return void
     */
    public function processDataChanges(Mvc_WizardReqList $objReqContainer)
    {
        $arrInput = $objReqContainer->getDataForComplete();
        $arrInput['bolTakeCharge'] = $this->getApplicationStateVariable('bolTakeCharge');

        $intTotalToPayToday = self::getTotalToPayToday($arrInput);

        $this->arrData['intTotalToPayToday'] = new I18n_Currency(
            AccountChange_Manager::CURRENCY_UNIT,
            $intTotalToPayToday
        );
        $this->arrData['bolOneOffCharges'] = ($intTotalToPayToday > 0);

        if ($this->promoCodeInvalidated) {
            $this->arrData['promoCodeInvalidated'] = true;
        }
    }

    /**
     * Returns a 'total to pay today' value based on invoices
     *
     * @param array $arrCollectedData Data collected so far
     *
     * @return int
     */
    protected static function getTotalToPayToday(array $arrCollectedData)
    {
        $intTotalToPay = 0.0;
        $arrInvoices = AccountChange_Controller::generateInvoiceItems($arrCollectedData);
        foreach ($arrInvoices as $arrInvoice) {
            $intTotalToPay += $arrInvoice['amount'];
        }
        return $intTotalToPay;
    }

    /**
     * Overriding Mvc_WizardRequirement::getDataForComplete
     *
     * This gets called once the requirement is in a valid state and only at the end of the wizard,
     * and allows the requirement to alter the data and return an array
     *
     * Make sure that you call $this->getData first so we get all the validated data for the requirement
     *
     * @return array
     */
    public function getDataForComplete()
    {
        $arrReturn = $this->getData();
        $arrReturn['broadBandCallerDisplay'] = $this->broadBandCallerDisplay;

        $arrProductConfigurations = $this->createProductConfigurations();
        $objAccountChangeManager = $this->getAccountChangeManager($arrProductConfigurations);

        $arrReturn['arrProductConfigurations'] = $arrProductConfigurations;
        $arrReturn['objAccountChangeManager'] = $objAccountChangeManager;

        $existingWlrProduct = $this->isApplicationStateVariable('arrWlrProduct')
            ? $this->getApplicationStateVariable('arrWlrProduct') : array();
        $newWlrCustomer = isset($arrProductConfigurations['objNewWlr']);
        $arrReturn['wlrCustomer'] = (
            (isset($existingWlrProduct['intInstanceId']) && $existingWlrProduct['intInstanceId'] != '')
            || $newWlrCustomer
        );

        $arrReturn['bolCharges'] = $this->bolCharges;
        $arrReturn['bolProRataCharges'] = $this->bolProRataCharges;

        return $arrReturn;
    }

    /**
     * Getter for the account change manager
     *
     * @param array $arrProductConfigurations Product configuration
     *
     * @return AccountChange_Manager
     */
    protected function getAccountChangeManager(array $arrProductConfigurations)
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $arrOldProducts = array();
        $arrNewProducts = array();

        if (isset($arrProductConfigurations['objOldBroadband'])) {
            $arrOldProducts[] = $arrProductConfigurations['objOldBroadband'];
        }

        if (isset($arrProductConfigurations['objNewBroadband'])) {
            $arrNewProducts[] = $arrProductConfigurations['objNewBroadband'];
        }

        if (isset($arrProductConfigurations['objOldWlr'])) {
            $arrOldProducts[] = $arrProductConfigurations['objOldWlr'];
        }

        if (isset($arrProductConfigurations['objNewWlr'])) {
            $arrNewProducts[] = $arrProductConfigurations['objNewWlr'];
        }

        // Setting up the Account Configurations
        $objOldAccountConfiguration = new AccountChange_AccountConfiguration($arrOldProducts);
        $objNewAccountConfiguration = new AccountChange_AccountConfiguration($arrNewProducts);

        // Account Change Manager
        $objAccountChangeManager = new AccountChange_Manager(
            $objCoreService->getServiceId(),
            $objOldAccountConfiguration,
            $objNewAccountConfiguration
        );

        return $objAccountChangeManager;
    }

    /**
     * Create the product configrations that are needed to get the charges associated with
     * an account change
     *
     * This will also produce product configurations that will be used in the getDataForComplete
     * method of the requirement
     *
     * @return array
     */
    protected function createProductConfigurations()
    {
        $arrReturn = array();
        // Only create Adsl products if there is a change
        if ($this->getApplicationStateVariable('bolHousemove') ||
            $this->getApplicationStateVariable('intOldSdi') != $this->getApplicationStateVariable('intNewSdi') ||
            $this->getApplicationStateVariable('bolContractResetBroadband')) {
            $this->createBroadbandConfigurations($arrReturn);
        }

        // Only create the Wlr Products if there is a new one being setup
        if ($this->isApplicationStateVariable('intNewWlrId')) {
            $this->createWlrConfigurations($arrReturn);
        }

        return $arrReturn;
    }

    /**
     * Create the broadband configurations
     *
     * We want to create all the broadband configurations based on what we have
     * collected so far throughout the journey.
     *
     * @param array $arrReturn (by reference) Result array
     *
     * @return void (array by reference)
     */
    protected function createBroadbandConfigurations(array &$arrReturn)
    {
        $this->includeLegacyFiles();
        $intOldSdi = $this->getApplicationStateVariable('intOldSdi');
        $intNewSdi = $this->getApplicationStateVariable('intNewSdi');
        $bolSchedule = $this->getApplicationStateVariable('bolSchedule');
        $objBusinessActor = $this->getApplicationStateVariable('objBusinessActor');

        // Adsl product details for the existing product (includes current contract)
        $arrAdslProductDetails = $this->getApplicationStateVariable('arrAdslProductDetails');

        // Old and new wlr configurations for BB actions that need to know what wlr
        // components we have / are adding
        $arrWlrProduct = array('intOldWlrId' => null, 'intInstanceId' => null);
        $intNewWlrId = null;

        if ($this->isApplicationStateVariable('intNewWlrId')) {
            $intNewWlrId = $this->getApplicationStateVariable('intNewWlrId');
        }

        if ($this->isApplicationStateVariable('arrWlrProduct')) {
            $arrWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');
        }

        $bolContractReset = false;
        if ($this->isApplicationStateVariable('bolContractResetBroadband')) {
            $bolContractReset = $this->getApplicationStateVariable('bolContractResetBroadband');
        }

        $objCoreService = $this->getApplicationStateVariable('objCoreService');

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $arrOldProductDetails = $objDatabase->getServiceDefinitionDetails($intOldSdi);
        $arrNewProductDetails = $objDatabase->getServiceDefinitionDetails($intNewSdi);

        $existingAdslComponentId = $this->getExistingAdslComponentId($objCoreService->getServiceId());

        // ST-950 Get all the defunct INTERNET_CONNECTION components so we can cleanup broken contracts.
        $defunctAdslComponentIds = $this->getComponentIdForInternetConnection(
            $objCoreService->getServiceId(),
            'destroyed'
        );

        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        // Easier to read if you split this into 2 if statements
        if (strtoupper($arrOldProductDetails['strContract']) == 'MONTHLY'
            && strtoupper($arrNewProductDetails['strContract']) == 'ANNUAL'
        ) {
            $bolContractReset = true;
        }

        if (strtoupper($arrOldProductDetails['strContract']) == 'ANNUAL'
            && strtoupper($arrNewProductDetails['strContract']) == 'ANNUAL'
            && $intOldSdi != $intNewSdi
            && (AccountChange_Controller::isBundle($intNewSdi)
                || AccountChange_Controller::isBundle($intOldSdi))
        ) {
            $bolContractReset = true;
        }

        // Wanting to take payment if the user is on the portal
        // or if the workplace user has checked the box
        $bolAutoTakePayment = $this->getApplicationStateVariable('bolTakeCharge');
        $bolTakePayment = false;
        if (($bolAutoTakePayment
                && isset($this->arrData['arrCharges'])
                && !empty($this->arrData['arrCharges']))
            || isset($this->arrData['arrCharges']['strCanxBb'])
        ) {
            $bolTakePayment = true;
        }

        $bolSchedule = $this->isScheduleOverrideForBroadbandNeeded($objBusinessActor, $bolSchedule);

        $arrSelectedBroadband = $this->getApplicationStateVariable('arrSelectedBroadband');

        // There's two places strProvisionOn can come from.  If it's coming
        // from the Portal, then we need to get it from the selected braodband,
        // if it's coming from Workplace, then we're passing this in from a
        // radio button..
        $strProvisionOn = $this->getApplicationStateVariable('strProvisionOn');
        if ($strProvisionOn === false && isset($arrSelectedBroadband['strProvisionOn'])) {
            $strProvisionOn = $arrSelectedBroadband['strProvisionOn'];
        }

        // Find the default market if there's no linecheck result
        $marketId = LineCheck_Market::getDefault()->getMarketId();

        if ($this->isApplicationStateVariable('objLineCheckResult')) {
            $lineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');
            $marketId = $this->getMarketIdFromLineCheckResult($lineCheckResult);
        }

        $newAdslScid = $this->getScidFromSdiAndMarket($intNewSdi, $marketId);

        $arrOptions = array(
            'bolContractReset' => $bolContractReset,
            'bolSchedule' => $bolSchedule,
            'bolScheduleDowngrade' => $this->getApplicationStateVariable('bolScheduleDowngrade'),
            'bolTakePayment' => $bolTakePayment,
            'objLineCheckResult' => $lineCheckResult,
            'bolWbcProduct' => $this->getApplicationStateVariable('bolWbcProduct'),
            'strProvisionOn' => $strProvisionOn,
            'oldWlrScid' => $arrWlrProduct['intOldWlrId'],
            'newWlrScid' => $intNewWlrId,
            'oldAdslComponentId' => $existingAdslComponentId,
            'defunctAdslComponentIds' => $defunctAdslComponentIds,
            'oldWlrComponentId' => $arrWlrProduct['intInstanceId'],
            'newAdslScid' => $newAdslScid,
            'bolHousemove' => $this->getApplicationStateVariable('bolHousemove')
        );

        // Also need to set specific Regrade options so housemove will be passed to Action_Regrade
        $arrOptions['Regrade'] = array(
            'bolHousemove' => $this->getApplicationStateVariable('bolHousemove')
        );

        // Information required for an appointment
        $appointing = array(
            'appointingType' => null,
            'intPhoneNumber' => null,
            'appointment' => null,
            'extensionKitId' => null,
            'engineeringNotes' => null,
            'addressRef' => null,
            'appointingType' => null,
            'appointmentdate1' => null,
            'appointmentdate2' => null,
            'appointmentdate3' => null,
            'appointmenttime1' => null,
            'appointmenttime2' => null,
            'appointmenttime3' => null,
        );

        foreach (array_keys($appointing) as $var) {
            if ($this->isApplicationStateVariable($var)) {
                $appointing[$var] = $this->getApplicationStateVariable($var);
            }
        }

        $arrOptions['appointing'] = $appointing;

        if ($this->isApplicationStateVariable('hardwareOption')) {
            $arrOptions['hardwareOption'] = $this->getApplicationStateVariable('hardwareOption');
        }

        $objOldBroadband = null;
        $objNewBroadband = null;
        $intBroadbandActionChange = AccountChange_Product_Manager::ACTION_NONE;

        if ($this->getApplicationStateVariable('bolHousemove') || $intOldSdi != $intNewSdi || $bolContractReset) {
            $intBroadbandActionChange = AccountChange_Product_Manager::ACTION_CHANGE;
        }

        if ($this->isApplicationStateVariable('arrSelectedWlr')) {
            $arrOptions['arrSelectedWlr'] = $this->getApplicationStateVariable('arrSelectedWlr');
        }

        // Old Product
        $arrOptions['strContract'] = strtoupper($arrAdslProductDetails['vchContract']);
        $objOldBroadband = AccountChange_Product_Manager::factory(
            $intBroadbandActionChange,
            $intOldSdi,
            AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION,
            $arrOptions
        );
        $objOldBroadband->setServiceId($objCoreService->getServiceId());

        if ($this->isApplicationStateVariable('arrComponentsNotToKeep')) {
            $objOldBroadband->setLegacyComponentNotToKeep($this->getApplicationStateVariable('arrComponentsNotToKeep'));
        }

        // New Product
        $arrOptions['strContract'] = $arrSelectedBroadband['strContractHandle'];
        $objNewBroadband = AccountChange_Product_Manager::factory(
            $intBroadbandActionChange,
            $intNewSdi,
            AccountChange_Product_Manager::PRODUCT_TYPE_SERVICE_DEFINITION,
            $arrOptions
        );

        $objNewBroadband->setServiceId($objCoreService->getServiceId());

        $objOldBroadband->setMatchingProductConfigurationManually($objNewBroadband);

        // Setting up what we want to return for this requirement
        $arrReturn['objOldBroadband'] = $objOldBroadband;
        $arrReturn['objNewBroadband'] = $objNewBroadband;
    }

    /**
     * Create the wlr configurations
     *
     * @param array $arrReturn (by reference) Result array
     *
     * @return void (array by reference)
     * @throws AccountChange_Product_ManagerException
     */
    public function createWlrConfigurations(array &$arrReturn)
    {
        // Only create the Wlr Products if there is a new one being setup
        if ($this->isApplicationStateVariable('intNewWlrId')) {
            $this->includeLegacyFiles();

            $objCoreService = $this->getApplicationStateVariable('objCoreService');
            $arrWlrProduct = $this->getApplicationStateVariable('arrWlrProduct');

            $bolSchedule = $this->getApplicationStateVariable('bolSchedule');
            $objBusinessActor = $this->getApplicationStateVariable('objBusinessActor');

            $intOldComponentId = 0;
            if (isset($arrWlrProduct['intInstanceId']) && $arrWlrProduct['intInstanceId'] != '') {
                $intOldComponentId = $arrWlrProduct['intInstanceId'];
            }

            $intNewWlrId = $this->getApplicationStateVariable('intNewWlrId');

            $existingAdslComponentId = $this->getExistingAdslComponentId($objCoreService->getServiceId());

            // ST-950 Get all the defunct INTERNET_CONNECTION components so we can cleanup broken contracts.
            $defunctAdslComponentIds = $this->getComponentIdForInternetConnection(
                $objCoreService->getServiceId(),
                'destroyed'
            );

            // Wanting to take payment if the user is on the portal
            // or if the workplace user has checked the box
            $bolTakePayment = false;
            if (($bolTakePayment && isset($this->arrData['arrCharges'])
                    && !empty($this->arrData['arrCharges']))
                || isset($this->arrData['arrCharges']['strCanxHp'])
            ) {
                $bolTakePayment = true;
            }

            $bolEssentialProduct = false;
            if (AccountChange_Controller::isWlrEssential($intNewWlrId)
                || AccountChange_Controller::isWlrEssential($arrWlrProduct['intOldWlrId'])
            ) {
                $bolEssentialProduct = true;
            }

            $bolSchedule = $this->isScheduleOverrideForWlrNeeded($objBusinessActor, $bolSchedule);

            $oldSdi = ltrim($this->getApplicationStateVariable('intOldSdi'), '0');
            $newSdi = ltrim($this->getApplicationStateVariable('intNewSdi'), '0');

            $marketId = 3;

            if ($this->isApplicationStateVariable('objLineCheckResult')) {
                $lineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');
                $marketId = $this->getMarketIdFromLineCheckResult($lineCheckResult);
            }
            $newAdslScid = $this->getScidFromSdiAndMarket($newSdi, $marketId);

            $arrOptions = array(
                'oldSdi' => $oldSdi,
                'newSdi' => $newSdi,
                'oldWlrScid' => $arrWlrProduct['intOldWlrId'],
                'newWlrScid' => $intNewWlrId,
                'oldAdslComponentId' => $existingAdslComponentId,
                'defunctAdslComponentIds' => $defunctAdslComponentIds,
                'oldWlrComponentId' => $intOldComponentId,
                'newAdslScid' => $newAdslScid,
                'strCliNumber' => $objCoreService->getCliNumber(),
                'bolSchedule' => $bolSchedule,
                'bolEssentialProduct' => $bolEssentialProduct,
                'bolTakePayment' => $bolTakePayment,
                'bolHousemove' => $this->getApplicationStateVariable('bolHousemove'),
                'newContract' => $this->needsNewContract()
            );

            $bolContractResetWlr = false;
            if ($this->isApplicationStateVariable('bolContractResetWlr')) {
                $bolContractResetWlr = $this->getApplicationStateVariable('bolContractResetWlr');
            }

            // Default for business and residential is MONTHLY
            $strWlrContract = 'MONTHLY'; // Default contracts

            $bolContractChanges = false;

            if ($this->isApplicationStateVariable('strWlrContract')) {
                $strWlrContract = $this->getApplicationStateVariable('strWlrContract');

                if ($strWlrContract != $arrWlrProduct['strContractHandle']) {
                    $bolContractChanges = true;
                }
            } elseif ($arrWlrProduct['strContractHandle'] != '') {
                // If there's a contract length set on the old product, then we need to reset this
                // to monthly as the new contract system is responsible for setting contract lengths
                $strWlrContract = 'MONTHLY';
            }

            // Default the products to null
            $objOldWlr = null;
            $objNewWlr = null;

            // Checks to see how we are creating the objects
            // This is depending on whether they are being added, removed or changed
            if ($intOldComponentId == 0 && $intNewWlrId != 0) { // We are adding wlr
                if ($this->hasBroadbandProductChanged()) {
                    $registry = AccountChange_Registry::instance();
                    $identifyAction = $this->getIdentifyAction($objCoreService->getServiceId());
                    $registry->setEntry(
                        'objLineCheckResult',
                        LineCheck_Result::getLatestResultByServiceId($objCoreService->getServiceId())
                    );
                    $registry->setEntry('intNewServiceDefinitionId', $newSdi);
                    $arrOptions['manuallyAddPhone'] = ($identifyAction->getActionRequired() === false) ? false : true;
                }

                $objNewWlr = AccountChange_Product_Manager::factory(
                    AccountChange_Product_Manager::ACTION_ADD,
                    $intNewWlrId,
                    AccountChange_Product_Manager::PRODUCT_TYPE_WLR,
                    $arrOptions
                );

                $objNewWlr->setServiceId($objCoreService->getServiceId());
                $objNewWlr->setContract(
                    new AccountChange_Product_WlrServiceComponentContract($strWlrContract, 'MONTHLY')
                );
            } elseif ($intOldComponentId != 0 && $intNewWlrId == 0) { // We are removing wlr
                $objOldWlr = AccountChange_Product_Manager::factoryUsingComponentId(
                    AccountChange_Product_Manager::ACTION_REMOVE,
                    $intOldComponentId,
                    'Wlr',
                    $arrOptions
                );
                $objOldWlr->setServiceId($objCoreService->getServiceId());
            } elseif ($intOldComponentId != 0
                && $arrWlrProduct['intOldWlrId'] != $intNewWlrId
                || ($arrWlrProduct['intOldWlrId'] == $intNewWlrId && ($bolContractResetWlr || $bolContractChanges))
            ) { // We are changing wlr
                $objOldWlr = AccountChange_Product_Manager::factoryUsingComponentId(
                    AccountChange_Product_Manager::ACTION_CHANGE,
                    $intOldComponentId,
                    'Wlr',
                    $arrOptions
                );

                $objOldWlr->setServiceId($objCoreService->getServiceId());
                $objWlrLineRental = CProductComponent::createInstanceFromComponentID(
                    $arrWlrProduct['intInstanceId'],
                    'WlrLineRent',
                    array('ACTIVE', 'QUEUED-ACTIVATE', 'UNCONFIGURED')
                );

                $uxtContractEnd = I18n_Date::fromTimestamp($objWlrLineRental->getContractEnd());
                $uxtContractStart = I18n_Date::fromTimestamp($objWlrLineRental->getContractStart());
                $objOldWlr->setContract(
                    new AccountChange_Product_WlrServiceComponentContract(
                        $arrWlrProduct['strContractHandle'],
                        'MONTHLY',
                        'SUBSCRIPTION',
                        $uxtContractEnd,
                        $uxtContractStart
                    )
                );

                $objNewWlr = AccountChange_Product_Manager::factory(
                    AccountChange_Product_Manager::ACTION_CHANGE,
                    $intNewWlrId,
                    AccountChange_Product_Manager::PRODUCT_TYPE_WLR,
                    $arrOptions
                );

                $objNewWlr->setServiceId($objCoreService->getServiceId());
                if ($bolContractResetWlr) {
                    $uxtContractEnd = null;
                    $uxtContractStart = null;
                }

                $lrsInstance = PrepaidContract_Factory::getLineRentalSaver($objCoreService->getServiceId(), 'ACTIVE');
                $tariffTypeHandle = (!is_null($lrsInstance) && $lrsInstance->hasActiveContract()) ?
                    'LINE_RENTAL_SAVER' :
                    'DEFAULT';

                $objNewWlr->setContract(
                    new AccountChange_Product_WlrServiceComponentContract(
                        $strWlrContract,
                        'MONTHLY',
                        'SUBSCRIPTION',
                        $uxtContractEnd,
                        $uxtContractStart,
                        $tariffTypeHandle
                    )
                );
            }

            if (isset($objNewWlr) && isset($objOldWlr)) {
                $objOldWlr->setMatchingProductConfigurationManually($objNewWlr);
            }

            // Setting up what we want to return for this requirement
            $arrReturn['objOldWlr'] = $objOldWlr;
            $arrReturn['objNewWlr'] = $objNewWlr;
        }
    }

    /**
     * Does the customer need a new contract?
     *
     * If it's a phone only change, no broadband product was selected
     * If coming through workplace, we check the selected contract duration (as they could retain their contract)
     * If coming through member centre, we check if they are changing to a different service definition
     *
     * @return bool
     */
    private function needsNewContract()
    {
        $selectedBroadband = $this->getApplicationStateVariable('arrSelectedBroadband');

        if (!is_array($selectedBroadband)) {
            // Phone only change
            return false;
        } elseif (array_key_exists(self::SELECTED_CONTRACT_DURATION_KEY, $selectedBroadband)) {
            // Workplace account change
            return !empty($selectedBroadband[self::SELECTED_CONTRACT_DURATION_KEY]);
        } else {
            // Member centre account change
            return !$this->stayingOnSameSdi();
        }
    }

    /**
     * Is the customer staying on the same service definition id?
     *
     * @return bool
     */
    private function stayingOnSameSdi()
    {
        return $this->getApplicationStateVariable('intOldSdi') == $this->getApplicationStateVariable('intNewSdi');
    }

    /**
     * All product changes via the portal are now scheduled
     *
     * @param Auth_BusinessActor $objBusinessActor The business actor we are working with
     * @param boolean            $bolSchedule      Are we currently wanting to schedule the change
     *
     * @return boolean
     */
    public function isScheduleOverrideForBroadbandNeeded($objBusinessActor, $bolSchedule)
    {
        if (!in_array($objBusinessActor->getUserType(), self::ALLOWED_INSTANT_CHANGE_ACTORS)) {
            return true;
        } else {
            if (!isset($bolSchedule) || empty($bolSchedule)) {
                return false;
            }

            return $bolSchedule;
        }
    }

    /**
     * WLR Account changes initiated via the portal should always be scheduled.
     *
     * This function overrides any other behavior in that case.
     *
     * @param Auth_BusinessActor $objBusinessActor The business actor we are working with
     * @param boolean            $bolSchedule      Are we currently wanting to schedule the change
     *
     * @return boolean
     */
    public function isScheduleOverrideForWlrNeeded($objBusinessActor, $bolSchedule)
    {

        if (!in_array($objBusinessActor->getUserType(), self::ALLOWED_INSTANT_CHANGE_ACTORS)) {
            return true;
        } else {
            if (!isset($bolSchedule) || empty($bolSchedule)) {
                return false;
            }

            return $bolSchedule;
        }
    }

    /**
     * Check if there are any one off payments to be taken
     *
     * @param Auth_BusinessActor $businessActor Business actor
     *
     * @return boolean
     */
    protected function hasOneOffCharges(Auth_BusinessActor $businessActor)
    {
        $hasOneOffCharges = false;
        // We have to check the user type of the business object
        // As we have two methods to check if payment is needed
        // Portal it is always needed if there are charges
        // Workplace only needed if agent ticks the charges box
        if ('PLUSNET_STAFF' != $businessActor->getUserType()) {
            if ($this->bolCharges) {
                $hasOneOffCharges = true;
            }
        } else {
            if (($this->bolCharges && $this->isApplicationStateVariable('arrCharges'))
                || $this->bolProRataCharges
            ) {
                $hasOneOffCharges = true;
            }
        }
        return $hasOneOffCharges;
    }

    /**
     * Inclusion of the legacy files in such a way so we can mock it
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/class-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWifiComponent.inc';
        require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CFinancialHelper.inc';
    }

    /**
     * return AccountChange_Action_IdentifyAction object
     *
     * @param int $serviceId serviceId
     *
     * @return AccountChange_Action_IdentifyAction
     */
    protected function getIdentifyAction($serviceId)
    {
        return new AccountChange_Action_IdentifyAction($serviceId);
    }

    /**
     * Look up the current INTERNET_CONNECTION component id for
     * the given service.
     *
     * @param int    $serviceId Service id
     * @param string $status    The service status
     *
     * @return int
     **/
    protected function getComponentIdForInternetConnection($serviceId, $status)
    {
        $db = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
        return $db->getComponentIdForTypeAndStatus($status, 'INTERNET_CONNECTION', $serviceId);
    }

    /**
     * Look up the current INTERNET_CONNECTION component id for
     * the given service.
     *
     * @return bool
     **/
    protected function freePartnerHardwarePostage()
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');

        $db = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
        return $db->freePartnerHardwarePostage(
            $objCoreService->getServiceId(),
            $this->getApplicationStateVariable('intNewSdi')
        );
    }

    /**
     * Obtain the market id from a line check result
     *
     * @param LineCheck_Result $lineCheckResult A line check result object
     *
     * @return int
     **/
    protected function getMarketIdFromLineCheckResult($lineCheckResult)
    {
        $market = LineCheck_Market::getMarketFromExchange($lineCheckResult->getExchangeCode());
        return $market->getMarketId();
    }

    /**
     * Get the new service component id given a market and service definition id
     *
     * @param int $sdi      Service defintion id
     * @param int $marketId Market id
     *
     * @return int
     **/
    protected function getScidFromSdiAndMarket($sdi, $marketId)
    {
        try {
            return ProductFamily_InternetConnectionHelper::getId($sdi, $marketId);
        } catch (Exception $e) {
            // If we've got a legacy service definition based product then the above will throw an
            // exception.  In this case it's valid to return null for the new scid
            return null;
        }
    }

    /**
     * Returns a new Financial_AdditionalChargeHelper object
     *
     * @return Financial_AdditionalChargeHelper
     */
    private function getAdditionalChargeHelper()
    {
        if ($this->additionalChargeHelper === null) {
            return new Financial_AdditionalChargeHelper();
        } else {
            return $this->additionalChargeHelper;
        }
    }

    /**
     * Unit test wrapper for AccountChange_Controller::getMinAndMaxSpeedRanges.
     *
     * @return array
     */
    public function getMinAndMaxSpeedRanges()
    {
        $objCoreService = $this->getApplicationStateVariable('objCoreService');
        $objLineCheckResult = $this->getApplicationStateVariable('objLineCheckResult');

        return AccountChange_Controller::getMinAndMaxSpeedRanges($objCoreService, $objLineCheckResult);
    }

    /**
     * Get scheduled change date.
     *
     * @param integer $intServiceId Service id
     *
     * @return string
     */
    public function getScheduledChangeDate($intServiceId)
    {
        $this->includeLegacyFiles();

        return UserdataServiceGetScheduledChangeDate($intServiceId);
    }

    /**
     * Gets the current active ADSL component ID from the user account
     *
     * @param int $serviceId the account service ID
     * @return int
     */
    private function getExistingAdslComponentId($serviceId)
    {
        $componentId = $this->getComponentIdForInternetConnection($serviceId, 'active');

        if (is_array($componentId) && count($componentId) == 1) {
            $componentId = $componentId[0];
        } else {
            $componentId = null;
        }

        return $componentId;
    }

    /**
     * Helper to check toggle status
     *
     * @param string $toggleName   Name of the toggle state required
     * @param int    $intServiceId Service ID
     *
     * @return bool
     */
    protected function checkToggleStatus($toggleName, $intServiceId)
    {
        return FeatureToggleManager::isOnFiltered($toggleName, null, null, null, $intServiceId);
    }

    /**
     * Checks if the current journey is a fibre upgrade campaign journey
     *
     * @return bool
     */
    private function isFibreUpgradeCampaign()
    {
        return in_array(
            $this->getApplicationStateVariable('campaignCode'),
            AccountChange_CampaignCodes::getAllFibreUpgradeCampaignCodes()
        );
    }

    /**
     * Get a sales journey view helper object
     *
     * @return AccountChange_SalesJourneyViewHelper
     */
    public function getSalesJourneyViewHelper()
    {
        return new AccountChange_SalesJourneyViewHelper();
    }

    /**
     * @param array $viewAttributes View attributes array
     *
     * @return array
     */
    private function setPostageAndPackagingCosts(array $viewAttributes)
    {
        if (!empty($viewAttributes[static::ONE_OFF_CHARGES_KEY])) {
            $viewAttributes['hardwareCost'] = 0;
            $viewAttributes['exVatHardwareCost'] = 0;

            foreach ($viewAttributes[static::ONE_OFF_CHARGES_KEY] as $oneOffCharge) {
                if (!empty($oneOffCharge[static::OUTSTANDING_CHARGES_KEY])
                    && $oneOffCharge[static::OUTSTANDING_CHARGES_KEY] === static::POSTAGE_AND_PACKAGING) {
                    $viewAttributes['hardwareCost'] = number_format($oneOffCharge[static::OUTSTANDING_FEES_KEY]->toDecimal(), 2);
                    $viewAttributes['exVatHardwareCost'] = number_format($oneOffCharge[static::OUTSTANDING_FEES_EX_VAT_KEY]->toDecimal(), 2);

                    break;
                }
            }
        }

        return $viewAttributes;
    }

    /**
     * Some aspects of promotion validation cannot happen until we've completed the
     * journey and therefore made all product / contract choices.  As this is the point
     * where we have all of the required information, we run some promotion validators here.
     *
     * @param int $serviceId End user service id
     *
     * @return void
     */
    public function handleC2mPromotionPostJourneyValidation($serviceId, $isp, $newSdi, $validatedApplicationData)
    {
        // $promotion (i.e not promoCode) signifies a c2m promotion code.
        $promotion = $this->getApplicationStateVariable('promoCode');

        if (!empty($promotion) && is_object($promotion) && $promotion->isPromoCodeC2mPromoCode($promotion->getPromotionCode())) {
            $contractDurationHelper = $this->getContractDurationHelper();

            if (!isset($validatedApplicationData['selectedContractDuration'])) {
                $contractDuration = $contractDurationHelper->getContractDuration(
                    $isp,
                    $newSdi,
                    $validatedApplicationData
                );
            } else {
                $contractDuration = $validatedApplicationData['selectedContractDuration'];
            }

            $additionalValidatorInformation = array(
                'C2MPromotionCode' => $promotion->getPromotionCode(),
                'ServiceId' => $serviceId,
                'contractDuration' => $contractDuration,
                'impressionOfferId' => $this->getImpressionOfferId()
            );

            $validators = array('AccountChange_PromotionCodeContractLengthPolicy');

            $validationCheck = $this->getValidationCheck(
                $serviceId,
                $validators,
                $additionalValidatorInformation
            );

            if (!$validationCheck->isAccountChangeAllowed()) {
                throw new AccountChange_InvalidAccountChangeOrderException($validationCheck->getReasonForBlockage());
            }
        }
        return true;
    }

    /**
     * Gets a validation check object
     *
     * @param int   $serviceId                      Service id of end user
     * @param array $validators                     Array of validating class names
     * @param array $additionalValidatorInformation Info to pass to validators
     *
     * @return AccountChange_ValidationCheck
     */
    protected function getValidationCheck($serviceId, $validators, $additionalValidatorInformation)
    {
        return new AccountChange_ValidationCheck(
            $this->getEndUserActor($serviceId),
            $validators,
            false,
            false,
            $additionalValidatorInformation
        );
    }


    /**
     * Pulls the impression offer id from the application state variables
     *
     * @return string
     */
    protected function getImpressionOfferId()
    {
        return $this->getApplicationStateVariable('impressionOfferId');
    }


    /**
     * Gets a contract duration helper
     *
     * @return AccountChange_ContractDurationHelper
     */
    protected function getContractDurationHelper()
    {
        return AccountChange_ServiceManager::getService('ContractDurationHelper');
    }


    /**
     * Return an end user actor for a given service id
     *
     * @param int $serviceId Service id
     *
     * @return Auth_BusinessActor
     */
    protected function getEndUserActor($serviceId)
    {
        return Auth_BusinessActor::getActorByExternalUserId(ltrim($serviceId, '0'));
    }

    /**
     * Removes promotional information from array
     *
     * @param $arrReturn
     */
    protected function removePromotion(&$arrReturn, &$arrValidatedApplicationData)
    {
        if (isset($arrReturn['promoCode'])) {
            unset($arrReturn['promoCode']);
        }

        if (isset($arrReturn['arrSelectedWlr']) || isset($arrValidatedApplicationData['arrSelectedWlr'])) {
            $arrReturn['arrSelectedWlr'] = $arrValidatedApplicationData['arrSelectedWlr'];

            if (isset($arrReturn['arrSelectedWlr']['lineRentalDiscountValue'])) {
                unset($arrReturn['arrSelectedWlr']['lineRentalDiscountValue']);
            }
            if (isset($arrReturn['arrSelectedWlr']['lineRentalDiscountAmount'])) {
                unset($arrReturn['arrSelectedWlr']['lineRentalDiscountAmount']);
            }
            if (isset($arrReturn['arrSelectedWlr']['lineRentalDiscountedPrice'])) {
                unset($arrReturn['arrSelectedWlr']['lineRentalDiscountedPrice']);
            }
        }

        if (isset($arrReturn['objDiscountedProductCost'])) {
            unset($arrReturn['objDiscountedProductCost']);
        }

        if (isset($arrReturn['intSelectedProductCostTotal'])) {
            $arrReturn['intSelectedProductCostTotal'] = $arrReturn['selectedProductCostTotalNoDiscounts'];
        }
        $this->promoCodeInvalidated = true;
    }
}
