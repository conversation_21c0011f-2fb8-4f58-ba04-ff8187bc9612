<?php

class AccountChange_CallFeature_Api
{
    const PLUSNET_CALL_PROTECT_COMPONENT_HANDLE = 'WlrCallProtect';
    const CALLER_DISPLAY_COMPONENT_HANDLE = 'WlrCallerDisplay';
    const CALL_WAITING_COMPONENT_HANDLE = 'WlrCallWaiting';
    const CALL_DIVERT_COMPONENT_HANDLE = 'WlrCallDiversion';
    const RING_BACK_COMPONENT_HANDLE = 'WlrRingBack';
    const REMINDER_CALL_COMPONENT_HANDLE = 'WlrReminderCall';
    const ANONYMOUS_CALL_COMPONENT_HANDLE = 'WlrAnonymousCall';

    const SUPPORTED_CALL_FEATURE_COMPONENT_HANDLES = array(
        self::PLUSNET_CALL_PROTECT_COMPONENT_HANDLE,
        self::CALLER_DISPLAY_COMPONENT_HANDLE,
        self::CALL_WAITING_COMPONENT_HANDLE,
        self::CALL_DIVERT_COMPONENT_HANDLE,
        self::RING_BACK_COMPONENT_HANDLE,
        self::REMINDER_CALL_COMPONENT_HANDLE,
        self::ANONYMOUS_CALL_COMPONENT_HANDLE);

    const UNCONFIGURED_COMPONENT_STATUS = 'unconfigured';
    const UNCONFIGURED_COMPONENT_STATUS_ID = 1;
    const ACTIVE_COMPONENT_STATUS = 'active';
    const ACTIVE_COMPONENT_STATUS_ID = 4;

    const USERDATA_SERVICE_ID_KEY = 'cli_number';
    const WLR_COMPONENT_HANDLE = 'WLR';
    const DEFAULT_TARIFF_ID = 0;

    private $dbAdaptor;

    public function __construct()
    {
        $this->dbAdaptor = Db_Manager::getAdaptor('AccountChange');
    }

    public function preRegisterForCallerDisplay($serviceId)
    {
        $this->requireLegacyFiles();

        if ($this->customerHasCallFeature(self::CALLER_DISPLAY_COMPONENT_HANDLE, $serviceId))
        {
            throw new AccountChange_CallFeature_ExistingCallFeatureException(
                "Customer with service ID: '". $serviceId ."' already has Caller Display.");
        }
        elseif ($this->customerHasAlreadyPreRegisteredForCallerDisplay($serviceId))
        {
            throw new AccountChange_CallFeature_ExistingPreRegistrationEntryException(
                "Customer with service ID: '". $serviceId ."' has already pre-registered for Caller Display.");
        }
        else
        {
            $this->addServiceIdToPreRegistrationTable($serviceId);
        }
    }

    public function addCallFeature($callFeatureComponentHandle, $serviceId)
    {
        $this->requireLegacyFiles();

        if (!in_array($callFeatureComponentHandle, self::SUPPORTED_CALL_FEATURE_COMPONENT_HANDLES))
        {
            throw new InvalidArgumentException("Component with handle '" . $callFeatureComponentHandle . "' is not a supported call feature.");
        }
        elseif ($this->customerHasCallFeature($callFeatureComponentHandle, $serviceId))
        {
            throw new AccountChange_CallFeature_ExistingCallFeatureException(
                "Customer with service ID: '". $serviceId ."' already has call feature with handle: '" . $callFeatureComponentHandle . "'.");
        }
        else
        {
            $this->addAndProvisionCallFeature($callFeatureComponentHandle, $serviceId);
        }
    }

    /**
     * Remove caller display feature
     *
     * @param string $callFeatureComponentHandle  Caller Display Feature handle
     * @param string $serviceId                   Service Id
     *
     * @return void
     */
    public function removeCallFeature($callFeatureComponentHandle, $serviceId)
    {
        $this->requireLegacyFiles();

        $wlrComponentId = $this->getActiveWlrComponentIdFromServiceId($serviceId);

        if (empty($wlrComponentId)) {
            throw new AccountChange_CallFeature_InactiveWlrComponentException(
                "Customer with service ID: '". $serviceId ."' does not have an active phone component.");
        }

        if (!$this->customerHasCallFeature($callFeatureComponentHandle, $serviceId)) {
            throw new AccountChange_CallFeature_ExistingCallFeatureException(
                "Customer with service ID: '". $serviceId .
                "' does not have call feature with handle: '" . $callFeatureComponentHandle . "'."
            );
        }

        /** @var CWlrCallFeature $callFeature */
        $callFeature = $this->getProductComponentInstanceFromComponentId(
            $wlrComponentId,
            $callFeatureComponentHandle,
            array('ACTIVE')
        );

        if ($callFeature) {
            $callFeature->markForRemoval();
            $productComponentInstanceId = $callFeature->getProductComponentInstanceID();

            $cli = $this->getCliFromServiceId($serviceId);
            $this->sendWlr3Order(
                AccountChange_CallFeature_Api::CALLER_DISPLAY_COMPONENT_HANDLE,
                $serviceId,
                $wlrComponentId,
                $cli,
                $productComponentInstanceId,
                false
            );
        }
    }

    /**
     * Get a product component instance object
     *
     * @param int      $componentId WLR component ID
     * @param string   $handle      Product component handle
     * @param string[] $states      Possible states
     *
     * @return CProductComponent
     */
    protected function getProductComponentInstanceFromComponentId($componentId, $handle, $states)
    {
        return CProductComponent::createInstanceFromComponentID(
            $componentId,
            $handle,
            $states
        );
    }

    public function customerHasAlreadyPreRegisteredForCallerDisplay($serviceId)
    {
        $callerDisplayPreRegistrationId =  $this->dbAdaptor->getCallerDisplayPreRegistrationIdForServiceId($serviceId);
        return !empty($callerDisplayPreRegistrationId);
    }

    /**
     * Getting call feature product instance
     *
     * @param string $callFeatureComponentHandle  Caller Display Feature handle
     * @param string $serviceId                   Service Id
     *
     * @return int
     */
    private function getCallFeatureProductComponentInstanceId($callFeatureComponentHandle, $serviceId)
    {
        $statuses = array('UNCONFIGURED', 'QUEUED_ACTIVATE', 'ACTIVE');
        return $this->dbAdaptor->getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses(
            $callFeatureComponentHandle,
            $serviceId,
            $statuses
        );
    }

    /**
     * Customer Has Caller Display Feature Or Not
     *
     * @param string $callFeatureComponentHandle  Caller Display Feature handle
     * @param string $serviceId                   Service Id
     *
     * @return int
     */
    public function customerHasCallFeature($callFeatureComponentHandle, $serviceId)
    {
        return !empty($this->getCallFeatureProductComponentInstanceId($callFeatureComponentHandle, $serviceId));
    }

    private function addServiceIdToPreRegistrationTable($serviceId)
    {
        $this->dbAdaptor->insertServiceIdIntoCallerDisplayPreRegistrationTable($serviceId);
        Db_Manager::commit();
    }

    private function addAndProvisionCallFeature($callFeatureComponentHandle, $serviceId)
    {
        $wlrComponentId = $this->getActiveWlrComponentIdFromServiceId($serviceId);

        if (empty($wlrComponentId))
        {
            throw new AccountChange_CallFeature_InactiveWlrComponentException(
                "Customer with service ID: '". $serviceId ."' does not have an active phone component.");
        }
        else
        {
            $callFeatureProductComponentInstanceId = $this->createCallFeatureComponent($callFeatureComponentHandle, $serviceId, $wlrComponentId);
            $callFeatureBundleProductComponentInstanceId = $this->createCallFeatureBundleComponentIfApplicable($serviceId, $wlrComponentId);
            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
            $this->provisionCallFeature(
                $callFeatureComponentHandle,
                $serviceId,
                $wlrComponentId,
                $callFeatureProductComponentInstanceId,
                $callFeatureBundleProductComponentInstanceId);
        }
    }

    public function provisionCallFeature($callFeatureComponentHandle, $serviceId, $wlrComponentId, $callFeatureProductComponentInstanceId = 0, $callFeatureBundleProductComponentInstanceId = 0)
    {
        try
        {
            $cli = $this->getCliFromServiceId($serviceId);
            $this->sendWlr3Order($callFeatureComponentHandle, $serviceId, $wlrComponentId, $cli);
        }
        catch (Exception $exception)
        {
            $this->destroyProductComponentInstance($callFeatureProductComponentInstanceId);
            $this->destroyProductComponentInstance($callFeatureBundleProductComponentInstanceId);

            throw new AccountChange_CallFeature_CallFeatureProvisioningException(
                "Something went wrong whilst provisioning call feature with handle: '" . $callFeatureComponentHandle . "' for customer with service ID: '". $serviceId ."'." .
                "\nCall feature product component instance with ID: '". $callFeatureProductComponentInstanceId ."' has been destroyed." .
                "\nCall feature bundle product component instance with ID: '". $callFeatureBundleProductComponentInstanceId ."' has been destroyed." .
                "\nThe error returned from Wlr3 was:" .
                "\n" . $exception->getMessage());
        }
    }

    public function getActiveWlrComponentIdFromServiceId($serviceId)
    {
        return CProduct::getComponentIDByServiceID(
            $serviceId,
            self::WLR_COMPONENT_HANDLE,
            array(self::ACTIVE_COMPONENT_STATUS));
    }

    protected function createCallFeatureComponent($callFeatureComponentHandle, $serviceId, $wlrComponentId)
    {
        $productComponentId = CProductComponent::getProductComponentIDByHandle($callFeatureComponentHandle);
        $productComponent = CProductComponent::create(
            $serviceId,
            $wlrComponentId,
            $productComponentId,
            self::DEFAULT_TARIFF_ID,
            false,
            self::WLR_COMPONENT_HANDLE
        );

        return $productComponent->getProductComponentInstanceID();
    }

    protected function createCallFeatureBundleComponentIfApplicable($serviceId, $wlrComponentId)
    {
        $wlrComponentTypeId =
            CComponent::createInstance($wlrComponentId)
                ->getComponentTypeID();

        $bundledCallFeatures =
            CWlrProduct::getSelectedProductComponentInstanceIDs(
                CWlrCallFeature::getBundleableCallFeatures($wlrComponentTypeId),
                array(
                    self::UNCONFIGURED_COMPONENT_STATUS_ID,
                    self::ACTIVE_COMPONENT_STATUS_ID),
                $wlrComponentId);

        $callFeatureBundleProductComponentId =
            CWlrCallFeaturesBundle::getTypeByAmountOfFeatures(
                count($bundledCallFeatures),
                $wlrComponentTypeId);

        if ($callFeatureBundleProductComponentId)
        {
            $productComponent = CProductComponent::create(
                $serviceId,
                $wlrComponentId,
                $callFeatureBundleProductComponentId,
                self::DEFAULT_TARIFF_ID,
                false,
                self::WLR_COMPONENT_HANDLE);

            return $productComponent->getProductComponentInstanceID();
        }
        else
        {
            return null;
        }
    }

    protected function getCliFromServiceId($serviceId)
    {
        $service = userdata_service_get($serviceId);
        return $service[self::USERDATA_SERVICE_ID_KEY];
    }

    /**
     * Place a WLR 3 order for call feature changes
     *
     * @param string $callFeatureComponentHandle  Call feature to manage
     * @param int    $serviceId                   User's service ID
     * @param int    $wlrComponentId              WLR component to update
     * @param string $cli                         CLI
     * @param int    $productComponentInstanceId  Existing call feature instance ID - optional, only relevant for remove
     * @param bool   $featureOrderTypeFlag        Which action to perform: true = ADD, false = REMOVE. Default: true
     *
     * @return void
     * @throws NumberFormatException Invalid service ID or WLR component ID
     * @throws WrongTypeException Invalid service ID or WLR component ID
     */
    protected function sendWlr3Order($callFeatureComponentHandle, $serviceId, $wlrComponentId, $cli, $productComponentInstanceId = 0, $featureOrderTypeFlag = true)
    {
        if ($featureOrderTypeFlag) {
            $featureOrderType = Wlr3_FeatureOrderTypes::ADD_FEATURE;
            $statuses = array('UNCONFIGURED');
            $featureProductComponentInstanceId =  $this->dbAdaptor->getProductComponentInstanceIdForComponentHandleServiceIdAndStatuses($callFeatureComponentHandle, $serviceId, $statuses);
            $features = array($callFeatureComponentHandle => $featureProductComponentInstanceId);
        } else {
            $featureOrderType = Wlr3_FeatureOrderTypes::REMOVE_FEATURE;
            $features = array($callFeatureComponentHandle => $productComponentInstanceId);
        }

        $orderTypeFeature = new Wlr3_OrderTypeFeature(
            new Int($serviceId),
            new Int($wlrComponentId),
            $featureOrderType,
            new String($cli),
            $features
        );

        $placeOrder = new Wlr3_PlaceOrder();
        $placeOrder->addOrder($orderTypeFeature);
        $placeOrder->sendOrder();
    }

    protected function destroyProductComponentInstance($productComponentInstanceId)
    {
        if (!is_null($productComponentInstanceId))
        {
            $productComponentInstance = $this->getProductComponentInstance($productComponentInstanceId);
            if ($productComponentInstance != null && $productComponentInstance instanceof CProductComponent) {
                $productComponentInstance->destroy();
            }
        }
    }

    protected function getProductComponentInstance($productComponentInstanceId)
    {
        return CProductComponent::createInstance($productComponentInstanceId);
    }

    protected function requireLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
    }
}
