<?php
/**
 * PaymentRedirect Workplace Test
 *
 * Testing class for the AccountChange_PaymentRedirectWorkplace
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-07-25
 */
/**
 * Controller Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 */
class AccountChange_PaymentRedirectWorkplaceTest extends PHPUnit_Framework_TestCase
{
    /**
     * Check whether the decorator specified is not changed
     *
     * @covers AccountChange_PaymentRedirectWorkplace::getDecorator
     */
    public function testGetDecorator()
    {
        $controller = $this->getMock(
            'AccountChange_Controller',
            array()
        );

        $reqList = $this->getMock(
            'Mvc_WizardReqList',
            array(), array('AccountChange', array('AccountChange_Payment'))
        );

        $paymentRedirectWorkplace = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_PaymentRedirectWorkplace', array($controller, $reqList)
        );

        $decorator = $paymentRedirectWorkplace->protected_getDecorator();

        $expected = 'CustomerDetails_Decorator';

        $this->assertEquals($expected, $decorator);
    }

    /**
     * Check whether the return view specified is not changed
     *
     * @covers AccountChange_PaymentRedirectWorkplace::getReturnView
     */
    public function testGetReturnView()
    {
        $controller = $this->getMock(
            'AccountChange_Controller',
            array()
        );

        $reqList = $this->getMock(
            'Mvc_WizardReqList',
            array(), array('AccountChange', array('AccountChange_Payment'))
        );

        $paymentRedirectWorkplace = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_PaymentRedirectWorkplace', array($controller, $reqList)
        );

        $decorator = $paymentRedirectWorkplace->protected_getReturnView();

        $expected = 'PaymentRedirectWorkplace';

        $this->assertEquals($expected, $decorator);
    }
}
