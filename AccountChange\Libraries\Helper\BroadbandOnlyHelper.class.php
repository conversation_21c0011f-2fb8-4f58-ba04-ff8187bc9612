<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_BroadbandOnlyHelper
{
    const TECHNOLOGY_TYPE_FTTP = 'FTTP';
    const TECHNOLOGY_TYPE_SOGEA = 'SOGEA';

    /**
     * @var AccountChange_Account|AccountChange_Registry
     */
    private $registry;

    /**
     * @return void
     */
    public function __construct()
    {
        $this->registry = AccountChange_Registry::instance();
        $this->adaptor = Db_Manager::getAdaptor('AccountChange');
    }

    /**
     * Returns true if the given service definition id relates to a Broadband Only product (FTTP / SOGEA).
     *
     * @param int $serviceDefinitionId Service definition id
     *
     * @return boolean
     **/
    public function isBroadbandOnlyProduct($serviceDefinitionId)
    {
        return $this->isProductType(
            'isBroadbandOnlyCache',
            [static::TECHNOLOGY_TYPE_FTTP, static::TECHNOLOGY_TYPE_SOGEA],
            $serviceDefinitionId
        );
    }

    /**
     * @param string $key                 cache key
     * @param array  $arrServiceTypes     array of service types (FTTP/SOGEA)
     * @param string $serviceDefinitionId service def
     * @return bool
     * @throws Db_TransactionException
     */
    private function isProductType($key, $arrServiceTypes, $serviceDefinitionId)
    {
        // Check cache
        $cache = $this->getCache($key);
        if (isset($cache[$serviceDefinitionId])) {
            return $cache[$serviceDefinitionId];
        }

        // Not cached, use db lookup
        $isProductType = false;
        if (isset($serviceDefinitionId)) {
            $isProductType = (bool)$this->adaptor->isProductType(
                $arrServiceTypes,
                $serviceDefinitionId
            );
            $cache[$serviceDefinitionId] = $isProductType;
            $this->setCache($key, $cache);
        }
        return $isProductType;
    }

    /**
     * Retrieves the cache from the registry
     * @param string $key cache key
     *
     * @return array
     **/
    private function getCache($key)
    {
        return $this->registry->getEntry($key);
    }

    /**
     * Sets the cache back into the registry
     * @param string $key   Cache to add to the registry
     * @param array  $cache Cache to add to the registry
     *
     * @return void
     **/
    private function setCache($key, $cache)
    {
        $this->registry->setEntry($key, $cache);
    }

    /**
     * Returns true if the given service definition id relates to a FTTP product.
     *
     * @param int $serviceDefinitionId Service definition id
     *
     * @return boolean
     **/
    public function isFttpProduct($serviceDefinitionId)
    {
        return $this->isProductType(
            'isFttpCache',
            [static::TECHNOLOGY_TYPE_FTTP],
            $serviceDefinitionId
        );
    }

    /**
     * Returns true if the given service definition id relates to a Sogea product.
     *
     * @param int $serviceDefinitionId Service definition id
     *
     * @return boolean
     **/
    public function isSogeaProduct($serviceDefinitionId)
    {
        return $this->isProductType(
            'isSogeaCache',
            [static::TECHNOLOGY_TYPE_SOGEA],
            $serviceDefinitionId
        );
    }

    /**
     * Adding this to support tactical fix for E2E-1570 & E2E-1572
     * Permanent solution will be determined later via PBBO-681
     * @param int $serviceId customer service id
     * @return bool
     */
    public function serviceHasPendingChangeToFttp($serviceId)
    {
        $changeDetails = $this->adaptor->getAdslScheduledChange($serviceId);
        if (!empty($changeDetails)) {
            return $this->isFttpProduct($changeDetails['intNewServiceDefinitionId']);
        }

        return false;
    }

    /**
     * @param int $serviceId customer service id
     * @return bool
     */
    public function serviceHasPendingChangeToBBO($serviceId)
    {
        $changeDetails = $this->adaptor->getAdslScheduledChange($serviceId);
        if (empty($changeDetails)) {
            return false;
        }

        return $this->isBroadbandOnlyProduct($changeDetails['intNewServiceDefinitionId']);
    }

    /**
     * Adding this to support tactical fix for E2E-1570 & E2E-1572
     * Permanent solution will be determined later via PBBO-681
     * @param int $serviceId customer service id
     * @return bool
     */
    public function serviceHasFttp($serviceId)
    {
        try {
            /** @var Core_Service|Core_ServiceDao $coreService */
            $coreService = $this->getCoreService($serviceId);
            return $this->isFttpProduct($coreService->getServiceDefinitionId());
        } catch (Core_Exception $e) {
            error_log(
                sprintf(
                    "Unable to determine if service has FTTP due to: %s",
                    $e->getMessage()
                )
            );
            return false;
        }
    }

    /**
     * @param int $serviceId customer service id
     * @return Core_Service
     * @throws Core_Exception
     */
    protected function getCoreService($serviceId)
    {
        return new Core_Service($serviceId);
    }
}
