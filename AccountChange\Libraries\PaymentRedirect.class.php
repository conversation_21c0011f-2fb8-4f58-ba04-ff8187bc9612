<?php
/**
 * AccountChange PaymentRedirect
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-07-19
 */
/**
 * AccountChange PaymentRedirect
 *
 * This class handles the redirection to external payment application.
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
abstract class AccountChange_PaymentRedirect extends Mvc_WizardExternalRedirect
{

    /**
     * Validation hash generated by GImP
     *
     * @var string
     */
    private $_validationHash;

    /**
     * Handover id generated by GImP after persist
     *
     * @var integer
     */
    private $_paymentHandoverId;

    /**
     * All the data collected by the wizard requirements
     *
     * @var array
     */
    private $_collectedData;

    /**
     * All the data collected for the compelet action
     *
     * @var array
     */
    private $_dataForComplete;

    /**
     * Prepare the session before redirecting
     *
     * @see Mvc_WizardExternalRedirect::prepareRedirect
     *
     * @return void
     */
    public function prepareRedirect()
    {
        // Send of the data we collected throughout the journey
        // to look for invoice items
        $arrInvoiceItems = $this->generateInvoiceItems();

        $objAccountChangeManager = $this->getDataForComplete('objAccountChangeManager');

        $amountInPence = $objAccountChangeManager->getTotalUpgradeCost($arrInvoiceItems);

        $invoiceDetails = array (
            'title' => 'Account Change',
            'items' => $arrInvoiceItems
        );

        $targetActor = $this->getApplicationStateVariable('objUserActor');

        $paymentRequestData = $this->getPaymentRequestData();

        $paymentRequestData->targetActorId = $targetActor->getActorId();
        $paymentRequestData->templateStyle = "generic";
        $paymentRequestData->paymentAmountInPence = $amountInPence * 100;
        $paymentRequestData->requestType = GenericImmediatePaymentApplication_RequestType::TAKE_PAYMENT;
        $paymentRequestData->decoratorClass = $this->getDecorator();
        $paymentRequestData->paymentMethodsAllowed = array(
            GenericImmediatePaymentApplication_PaymentMethodsAllowed::NEW_CARD,
            GenericImmediatePaymentApplication_PaymentMethodsAllowed::EXISTING_CARD
        );
        $paymentRequestData->invoiceDetails = $invoiceDetails;
        $paymentRequestData->paymentAction = 'AuthoriseAndSettle';
        # Get and set the account id for the customer
        $db = Db_Manager::getAdaptor('AccountChange');
        $accountId = $db->getAccountIdByServiceId($targetActor->getExternalUserId());
        $paymentRequestData->accountId = $accountId;

        $controller = $this->appStateCallback;
        $paymentRequestData->returnUrl = "https://"
           . $controller->getAppUri()
           . "/"
           . $controller->getWizardInstanceId()
           . "/"
           . $this->getReturnView(). "/";

        $currentUrl = "https://"
            . $controller->getAppUri()
            . "/"
            . $controller->getWizardInstanceId()
            . "/"
            . $controller->getCurrentView()
            . "/";

        $additionalTemplateVars = array(
            'previousAppUrl'   => $currentUrl
        );

        $paymentRequestData->additionalTemplateVars = $additionalTemplateVars;

        $paymentRequestHelper = new AccountChange_PaymentRequestCardholderHelper($accountId);
        $cardholderData = $paymentRequestHelper->getData();
        if (is_array($cardholderData)
            && array_key_exists('cardholderContactDetails', $cardholderData)
            && array_key_exists('cardholderAddress', $cardholderData)
            && array_key_exists('cardholderName', $cardholderData)
        ) {
            $paymentRequestData->cardholderContactDetails = $cardholderData['cardholderContactDetails'];
            $paymentRequestData->cardholderAddress = $cardholderData['cardholderAddress'];
            $paymentRequestData->cardholderName = $cardholderData['cardholderName'];
        }

        $this->_validationHash = $paymentRequestData->generateValidationHash();
        $this->_paymentHandoverId = $paymentRequestData->savePaymentRequestToDatabase($this->_validationHash);
    }

    /**
     * Get data to be parsed into the redirect url
     *
     * @see Mvc_WizardExternalRedirect::prepareRedirect
     *
     * @return void
     */
    public function getRedirectData()
    {
        $userActor = $this->getApplicationStateVariable('objUserActor');

        return array (
            'validationHash'    => $this->_validationHash,
            'paymentHandoverId' => $this->_paymentHandoverId,
            'targetActor'       => $userActor->getUsername().'@'.$userActor->getRealm()
        );
    }

    /**
     * Get the decorator specified for each application
     *
     * @return string decorator class name
     */
    abstract protected function getDecorator();

    /**
     * Get the view to return to from GImP
     *
     * @return string
     */
    abstract protected function getReturnView();

    /**
     * Wrapper function for AccountChange_Controller::generateInvoiceItems
     *
     * @return array
     */
    protected function generateInvoiceItems()
    {
        return AccountChange_Controller::generateInvoiceItems($this->getArrayDataForComplete());
    }

    /**
     * Wrapper to get the array of variables from data that has been
     * collected and available for complete
     *
     * @return array
     *               Array of all the variables from application initial data or collected data
     */
    protected function getArrayDataForComplete()
    {
        if (empty($this->_dataForComplete)) {
            $this->_dataForComplete = $this->requirementsList->getDataForComplete();
        }
        return $this->_dataForComplete;
    }

    /**
     * Wrapper to get a variable from data that has been
     * collected to be available for complete
     *
     * @param string $variableName the variable to get
     *
     * @return object
     *               Value of the variable from application initial data or collected data
     */
    protected function getDataForComplete($variableName)
    {
        $dataForComplete = $this->getArrayDataForComplete();
        return $dataForComplete[$variableName];
    }

    /**
     * Wrapper function
     *
     * @return GenericImmediatePaymentApplication_PaymentRequestData
     */
    protected function getPaymentRequestData()
    {
        return new GenericImmediatePaymentApplication_PaymentRequestData();
    }

    /**
     * Wrapper to get a variable from initial data or collected data (Wizard Application State)
     *
     * @param string $variableName the variable to get
     *
     * @return object - Value of the variable from application initial data or collected data
     */
    private function getApplicationStateVariable($variableName)
    {
        return $this->appStateCallback->getApplicationStateVar(get_class($this), $variableName);
    }
}
