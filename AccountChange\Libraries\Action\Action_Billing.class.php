<?php
/**
 * Billing action
 *
 * Action that performs all billing/financial related changes
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_Billing.class.php,v 1.2 2009-01-27 07:07:40 bselby Exp $
 * @since     File available since 2008-08-27
 */
/**
 * Billing action class
 *
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * @copyright 2008 PlusNet
 */
class AccountChange_Action_Billing extends AccountChange_Action
{
    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $this->setInvoicePeriod();
        $this->proRataCharge();
    }

    /**
     * Set the invoice period for the customer
     *
     * @param string $strPeriod the period
     *
     * @throws AccountChange_Action_ManagerException
     *
     * @return void
     */
    public function setInvoicePeriod($strPeriod = 'monthly')
    {
        $arrValidPeriods = array('monthly', 'quarterly', 'yearly', 'never', 'half-yearly');

        if (!in_array($strPeriod, $arrValidPeriods)) {
            throw new AccountChange_Action_ManagerException(
                'Invoice period is invalid. Invoice periods can only be ' .
                implode(',', $arrValidPeriods)
            );
        }

        $objDatabase = Db_Manager::getAdaptor('AccountChange');
        $objDatabase->setInvoicePeriod($strPeriod, $this->intServiceId);

        // In order to deal with the legacy and framework code locking each other it was decided to commit the
        // default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        $strTicketComment = 'Invoice Period changed from: ' . $strPeriod . ' to monthly';

        $this->raiseServiceEvent($strTicketComment);
    }

    /**
     * Gets a FibreHelper object
     *
     * @return AccountChange_FibreHelper
     **/
    public function getFibreHelper()
    {
        return new AccountChange_FibreHelper();
    }

    /**
     * This method should calculate any pro-rata charge / refund due for the service upgrade (FTTC only).
     * Instead, it raises a ticket to CSC - Billing for the pro-rata to be calculated manually.
     *
     * @return void
     */
    public function proRataCharge()
    {
        // Prior to release 2015.37, a ticket was produced to the Billing Operations - Billing, Refunds and Credits
        // pool.  This ticket is no longer generated as part of NAB-135.
    }

    /**
     * Inclusion of legacy includes so we can mock this for unit tests
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
    }
}
