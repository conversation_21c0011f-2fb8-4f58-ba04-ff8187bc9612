<?php
/**
 * AccountChange Hardware Requirement Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2009-09-14
 */
/**
 * AccountChange Hardware Requirement Test
 *
 * Test class for AccountChange_Hardware Requirement
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_Hardware_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Current data being tested with
     *
     * @var array
     */
    private $_currentData;

    /**
     * Current Product Information data being tested with
     *
     * @var array
     */
    private $_currentProductInfo;

    /**
     * Test that the hardware validator correctly returns the output for valid input
     *
     * @param array $hardware Hardware
     *
     * @covers AccountChange_Hardware::valHardwareOption
     * @dataProvider validHardwareProvider
     *
     * @return void
     */
    public function testValHardwareOptionReturnsCorrectOutputForValidInput($hardware)
    {
        $handle = 'Thomson1PortWiredBundleFree';

        $requirement = $this->getMock('AccountChange_Hardware', array('fetchAvailableHardware'));
        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        $controller
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('intNewSdi'))
            ->will($this->returnValue(123));

        $requirement->setAppStateCallback($controller);

        $requirement
            ->expects($this->once())
            ->method('fetchAvailableHardware')
            ->will($this->returnValue($hardware));

        $result = $requirement->valHardwareOption($handle);
        $errors = $requirement->getValidationErrors();

        $this->assertArrayHasKey('hardwareOption', $result);
        $this->assertArrayNotHasKey('hardwareOption', $errors);
    }

    /**
     * Test that the hardware validator correctly returns validation errors if the input is invalid
     *
     * @param array $hardware Hardware
     *
     * @covers AccountChange_Hardware::valHardwareOption
     * @dataProvider validHardwareProvider
     *
     * @return void
     */
    public function testValHardwareOptionReturnsValidationErrorsForIncorrectInput($hardware)
    {
        $badHandle = 'aBadHandle';

        $requirement = $this->getMock('AccountChange_Hardware', array('fetchAvailableHardware'));
        $controller = $this->getMock(
            'AccountChange_Controller',
            array('isApplicationStateVar', 'getApplicationStateVar')
        );

        $controller
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with($this->equalTo(get_class($requirement)), $this->equalTo('intNewSdi'))
            ->will($this->returnValue(123));

        $requirement->setAppStateCallback($controller);

        $requirement
            ->expects($this->once())
            ->method('fetchAvailableHardware')
            ->will($this->returnValue($hardware));

        $result = $requirement->valHardwareOption($badHandle);
        $errors = $requirement->getValidationErrors();

        $this->assertArrayHasKey('hardwareOption', $errors);
        $this->assertArrayNotHasKey('hardwareOption', $result);
    }

    /**
     * Provide data
     *
     * @return array
     */
    public function validHardwareProvider()
    {
        return array(
            // Data set 0
            array(
                array(
                    array('handle' => 'Thomson1PortWiredBundleFree', 'name' => 'Thomson 1-port Wired Router Bundle'),
                    array('handle' => 'NETGEAR_WNR1000_FTTC',        'name' => 'Netgear WNR1000 - FTTC')
                )
            ),
        );
    }

    /**
     * Test that the describe function returns an array with hardware as a key
     *
     * @param array $hardware Hardware
     *
     * @return void
     *
     * @throws Core_Exception
     * @throws NumberFormatException
     * @throws WrongTypeException
     *
     * @dataProvider hardwareProvider
     * @covers       AccountChange_Hardware::describe
     *
     */
    public function testDescribeReturnsAnArrayOfHardware($hardware)
    {
        $intNewSdi = 1234;
        $intServiceId = 1234;

        $mockCoreService = $this->getMock(Core_Service::class, array('getServiceId', 'getAdslDetails'));
        $mockCoreService->expects($this->any())
            ->method('getServiceId')
            ->willReturn($intServiceId);

        $adslDetails = [
            'isp' => 'plus.net',
            'type' => 'residential',
        ];
        $mockCoreService->expects($this->any())
            ->method('getAdslDetails')
            ->willReturn($adslDetails);

        $mockBusinessActor = $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->setMethods(['getUserType'])
            ->getMock();

        $mockBusinessActor->expects($this->once())
            ->method('getUserType')
            ->willReturn('PLUSNET_STAFF');

        $this->_currentData = array(
            'objCoreService' => $mockCoreService,
            'objBusinessActor' => $mockBusinessActor,
        );

        $requirement = $this->getMock(
            'AccountChange_Hardware',
            array(
                'fetchAvailableHardware',
                'getApplicationStateVariable',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getHardwareCharges',
                'isC2fToggleSet'
            )
        );

        $requirement
            ->expects($this->once())
            ->method('fetchAvailableHardware')
            ->with(new Int(1234))
            ->will($this->returnValue($hardware));

        $requirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(null));

        $requirement
            ->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $controller
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnIsApplicationStateVar')));

        $requirement
            ->expects($this->once())
            ->method('getHardwareCharges')
            ->willReturn(array('amount' => 1.2, 'exVatAmount' => 1));

        $requirement->setAppStateCallback($controller);

        $data = array('intNewSdi' => $intNewSdi);
        $result = $requirement->describe($data);
        $this->assertArrayHasKey('hardware', $result);
        $this->assertEquals(
            array(
                'hardware' => $hardware,
                'selectedBroadband' => null,
                'productDetails' => array(),
                'currentProductDownloadSpeedRangeMin' => null,
                'currentProductDownloadSpeedRangeMax' => null,
                'hardwareCost' => 1.2,
                'exVatHardwareCost' => 1,
                'campaignCode' => null,
                'routerHandle' => new String(null),
                'defaultSelectedHardware' => new String(null),
                'showWaivePostage' => false,
            ),
            $result
        );
    }

    /**
     * Provide data
     *
     * @return array
     */
    public function hardwareProvider()
    {
        return array(
            // Data set 0
            array(
                array(
                    array('handle' => 'Thomson1PortWiredBundleFree', 'name' => 'Thomson 1-port Wired Router Bundle'),
                    array('handle' => 'NETGEAR_WNR1000_FTTC',        'name' => 'Netgear WNR1000 - FTTC')
                )
            ),
            // Data set 1 (empty results from db)
            array(
                array()
            )
        );
    }

    /**
     * Test that the describe function returns an array of product information
     *
     * @param array $data        Current data for processing
     * @param array $productInfo Product information used for the test
     * @param array $expected    Expected results
     *
     * @covers AccountChange_Hardware::describe
     * @dataProvider hardwareProviderWithProductInfo
     *
     * @return void
     */
    public function testDescribeReturnsAnArrayOfProductInfo($data, $productInfo, $expected)
    {
        $this->_currentData = $data;
        $this->_currentProductInfo = $productInfo;

        $requirement = $this->getMock(
            'AccountChange_Hardware',
            array(
                'fetchAvailableHardware',
                'getSelectedBroadband',
                'getApplicationStateVariable',
                'isApplicationStateVariable',
                'getHardwareClient',
                'getFibreHelper',
                'getHardwareCharges',
                'getMinAndMaxSpeedRanges',
                'isC2fToggleSet',
            )
        );

        $isFibreCalls = array();
        if (isset($productInfo['sdi']) && is_array($productInfo['sdi'])) {
            foreach ($productInfo['sdi'] as $details) {
                if (isset($details['isFibre'])) {
                    $isFibreCalls[] = $details['isFibre'];
                }
            }
        }

        $mockHelper = $this->getMock(
            'AccountChange_FibreHelper',
            array('isFibreProduct'),
            array()
        );

        $mockHelper
            ->expects($this->any())
            ->method('isFibreProduct')
            ->will(new PHPUnit_Framework_MockObject_Stub_ConsecutiveCalls($isFibreCalls));

        $requirement
            ->expects($this->any())
            ->method('fetchAvailableHardware')
            ->will($this->returnValue(null));

        $requirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(null));

        $requirement
            ->expects($this->any())
            ->method('getFibreHelper')
            ->will($this->returnValue($mockHelper));

        $requirement
            ->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $requirement
            ->expects($this->once())
            ->method('getHardwareCharges')
            ->willReturn(array('amount' => 1.2, 'exVatAmount' => 1));

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $controller
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnIsApplicationStateVar')));

        $requirement->setAppStateCallback($controller);

        $client = $this->getMock(
            'HardwareClient_Client',
            array('getLastAddedHardwareForService')
        );

        $client
            ->expects($this->any())
            ->method('getLastAddedHardwareForService')
            ->will($this->returnValue($data['arrDummyHardware']));

        $requirement
            ->expects($this->any())
            ->method('getHardwareClient')
            ->will($this->returnValue($client));

        // Set the Db_Adaptor for the AccountChange module.
        $mockAccountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getAdslProductDetails', 'getServiceComponentDetailsForComponentType'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockAccountChangeDbAdaptor->expects($this->any())
            ->method('getAdslProductDetails')
            ->will($this->returnCallback(array($this, 'returnGetAdslProductDetails')));

        $mockAccountChangeDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForComponentType')
            ->will($this->returnValue($data['arrDummyOldHardware']));

        Db_Manager::setAdaptor('AccountChange', $mockAccountChangeDbAdaptor);

        $result = $requirement->describe($data);

        $this->assertArrayHasKey('productDetails', $result);
        $this->assertEquals(
            array(
                'hardware' => null,
                'selectedBroadband' => null,
                'productDetails' => $expected,
                'currentProductDownloadSpeedRangeMin' => null,
                'currentProductDownloadSpeedRangeMax' => null,
                'hardwareCost' => 1.2,
                'exVatHardwareCost' => 1,
                'campaignCode' => null,
                'routerHandle' => new String(null),
                'defaultSelectedHardware' => new String(null),
                'showWaivePostage' => false,
            ),
            $result
        );
    }

    /**
     * Call back function so that we can pass back different values for the test for getApplicationStateVariable
     *
     * @return array
     */
    public function returnGetApplicationStateVar()
    {
        $arrArgs = func_get_args();

        if (isset($this->_currentData[$arrArgs[1]])) {
            return $this->_currentData[$arrArgs[1]];
        } else {
            return;
        }
    }

    /**
     * Call back function so that we can pass back different values for the test for isApplicationStateVariable
     *
     * @return array
     */
    public function returnIsApplicationStateVar()
    {
        $arrArgs = func_get_args();

        if (isset($this->_currentData[$arrArgs[1]])) {
            return isset($this->_currentData[$arrArgs[1]]);
        } else {
            return false;
        }
    }

    /**
     * Call back function so that we can pass back different values for the test for isFibreProduct
     *
     * @return array
     */
    public function returnIsFibreProduct()
    {
        $bolResult = false;
        $arrArgs = func_get_args();

        if (isset($this->_currentProductInfo['sdi'])) {
            $arrSdi = $this->_currentProductInfo['sdi'];

            if (isset($arrSdi[$arrArgs[0]])) {
                $bolResult = $arrSdi[$arrArgs[0]]['isFibre'];
            }
        }
        return $bolResult;
    }

    /**
     * Call back function so that we can pass back different values for the test for getAdslProductDetails
     *
     * @return array
     */
    public function returnGetAdslProductDetails()
    {
        $bolResult = false;
        $arrArgs = func_get_args();

        if (isset($this->_currentProductInfo['sdi'])) {
            $arrSdi = $this->_currentProductInfo['sdi'];

            if (isset($arrSdi[$arrArgs[0]])) {
                $bolResult = $arrSdi[$arrArgs[0]]['productInfo'];
            }
        }

        return $bolResult;
    }

    /**
     * Provide data
     *
     * @return array
     */
    public function hardwareProviderWithProductInfo()
    {
        $oldSdi = 6754;
        $newSdi = 6784;
        $serviceId = 12345;
        $strName = "Dummy Router XYZ";

        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId', 'getAdslDetails')
        );

        $coreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        $adslDetails = [
            'isp' => 'plus.net',
            'type' => 'residential',
        ];
        $coreService->expects($this->any())
            ->method('getAdslDetails')
            ->willReturn($adslDetails);

        $mockBusinessActor = $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->setMethods(['getUserType'])
            ->getMock();

        $mockBusinessActor->expects($this->any())
            ->method('getUserType')
            ->willReturn('PLUSNET_STAFF');

        return array(
            // Data set 0 - fibre to fibre account change
            array(

                array(
                    'intServiceId'        => $serviceId,
                    'arrDummyHardware'    => array("intComponentTypeId" => 123),
                    'arrDummyOldHardware' => array("strName" => $strName),
                    'intOldSdi'           => $oldSdi,
                    'intNewSdi'           => $newSdi,
                    'objCoreService'      => $coreService,
                    'objBusinessActor'    => $mockBusinessActor,
                ),
                array(
                    'sdi' => array(
                        $oldSdi => array(
                            'isFibre' => true,
                            'productInfo' => array(
                                'intMaximumSpeed' => 40000,
                                'intMaxUploadSpeed' => 10000,
                            ),
                        ),

                        $newSdi => array(
                            'isFibre' => true,
                            'productInfo' => array(
                                'intMaximumSpeed' => 80000,
                                'intMaxUploadSpeed' => 20000,
                            ),
                        )
                    )
                ),

                array(
                    'oldProduct' => array(
                        'isFibre' => true,
                        'currentHardware' => $strName,
                        'maximumSpeed' => 40000,
                        'maxUploadSpeed' => 10000,
                    ),

                    'newProduct' => array(
                        'isFibre' => true,
                        'maximumSpeed' => 80000,
                        'maxUploadSpeed' => 20000,
                    ),
                ),

            ),
            // Data set 1 - not a fibre to fibre account change
            array(

                array(
                    'intServiceId'        => $serviceId,
                    'arrDummyHardware'    => array("intComponentTypeId" => 123),
                    'arrDummyOldHardware' => array("strName" => $strName),
                    'intOldSdi'           => $oldSdi,
                    'intNewSdi'           => $newSdi,
                    'objCoreService'      => $coreService,
                    'objBusinessActor'    => $mockBusinessActor,
                ),

                array(
                    'sdi' => array(
                        $oldSdi => array(
                            'isFibre' => false,
                            'productInfo' => array(
                                'intMaximumSpeed' => 40000,
                                'intMaxUploadSpeed' => 10000,
                            ),
                        ),

                        $newSdi => array(
                            'isFibre' => true,
                            'productInfo' => array(
                                'intMaximumSpeed' => 80000,
                                'intMaxUploadSpeed' => 20000,
                            ),
                        )
                    )
                ),
                array(
                    'oldProduct' => array(
                        'isFibre' => false,
                        'currentHardware' => $strName,
                        'maximumSpeed' => 40000,
                        'maxUploadSpeed' => 10000,
                    ),

                    'newProduct' => array(
                        'isFibre' => true,
                        'maximumSpeed' => 80000,
                        'maxUploadSpeed' => 20000,
                    ),
                ),

                array(),
            ),

            // Data set 2 (empty results from db)
            array(
                array(
                    'intServiceId'        => $serviceId,
                    'arrDummyHardware'    => null,
                    'arrDummyOldHardware' => null,
                    'intNewSdi'           => $newSdi,
                    'objCoreService'      => $coreService,
                    'objBusinessActor'    => $mockBusinessActor,
                ),

                array(),
                array(),
            )
        );
    }

    /**
     * Tests that hardware array is formatted correctly
     *
     * @param array $arrBundleData Bundle data
     * @param Int   $sdi           Service definition id
     * @param array $expected      Expected result
     *
     * @covers AccountChange_Hardware::fetchAvailableHardware
     * @dataProvider providerForTestFetchAvailableHardwareFormatsHardwareArray
     *
     * @group medium
     *
     * @return void
     */
    public function testFetchAvailableHardwareFormatsHardwareArray($arrBundleData, $sdi, $expected)
    {
        $arrBundles = new HardwareClient_BundleArray();
        foreach ($arrBundleData as $arrData) {
            $bundle = new HardwareClient_Bundle(
                new String($arrData['strName']),
                new String($arrData['strDescription']),
                new Int($arrData['intServiceComponentId']),
                new Int($arrData['intComponentHardwareBundleConfigId']),
                new String($arrData['strHandle'])
            );
            $arrBundles[] = $bundle;
        }

        $client = $this->getMock('HardwareClient_Client', [
            'getSignupBundlesForServiceDefinition'
        ]);

        $client
            ->expects($this->once())
            ->method('getSignupBundlesForServiceDefinition')
            ->with($sdi)
            ->will($this->returnValue($arrBundles));

        $requirement = $this->getMock('AccountChange_Hardware', array('getHardwareClient','getHardwareHelper'));

        $requirement
            ->expects($this->once())
            ->method('getHardwareClient')
            ->will($this->returnValue($client));

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(false);

        $requirement->expects($this->once())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $this->mockService($requirement);

        $result = $requirement->fetchAvailableHardware($sdi);
        $this->assertEquals($expected, $result);
    }

    /**
     * Data provider
     *
     * @return void
     */
    public function providerForTestFetchAvailableHardwareFormatsHardwareArray()
    {
        return array(
            // Data set 0
            array(
                array(
                    array(
                        'strName'                            => 'Thomson 4-port Wireless Router Bundle',
                        'strDescription'                     => 'Thomson 4-port Wireless Router Bundle (free)',
                        'intServiceComponentId'              => '00000653',
                        'intComponentHardwareBundleConfigId' => '46',
                        'strHandle'                          => 'Thomson4PortWirelessBundleFree',
                        'intHardwareBundleCost'              => '4000',
                    ),
                    array(
                        'strName'                            => 'Thomson 1-port Wired Router Bundle',
                        'strDescription'                     => 'Thomson 1-port Wired Router Bundle (free)',
                        'intServiceComponentId'              => '00000654',
                        'intComponentHardwareBundleConfigId' => '47',
                        'strHandle'                          => 'Thomson1PortWiredBundleFree',
                        'intHardwareBundleCost'              => '2000',
                    ),
                ),
                new Int(1234),
                array(
                    array(
                        'handle' => new String('Thomson4PortWirelessBundleFree'),
                        'name'   => new String('Thomson 4-port Wireless Router Bundle'),
                    ),
                    array(
                        'handle' => new String('Thomson1PortWiredBundleFree'),
                        'name'   => new String('Thomson 1-port Wired Router Bundle'),
                    )
                )
            )
        );
    }


    /**
     * Test the validator for extension kit
     *
     * @param mixed $input    The input from the browser
     * @param array $expected Expected output
     *
     * @dataProvider provideDataForExtensionKitValidator
     * @covers AccountChange_Hardware::valExtensionKitId
     *
     * @return void
     */
    public function testExtensionKitValidatorWithData($input, $expected)
    {
        $req = new AccountChange_Hardware();
        $actual = $req->valExtensionKitId($input);

        $this->assertEquals($expected, $actual);
    }


    /**
     * Test caching
     *
     * @param array $arrBundleData Bundle data
     * @param Int   $sdi           Service definition id
     * @param array $expected      Expected result
     *
     * @covers AccountChange_Hardware::fetchAvailableHardware
     * @dataProvider providerForTestFetchAvailableCallsDbOnlyOnceAndCachesResult
     *
     * @return void
     */
    public function testFetchAvailableCallsDbOnlyOnceAndCachesResult($arrBundleData, $sdi, $expected)
    {
        $arrBundles = new HardwareClient_BundleArray();
        foreach ($arrBundleData as $arrData) {
            $bundle = new HardwareClient_Bundle(
                new String($arrData['strName']),
                new String($arrData['strDescription']),
                new Int($arrData['intServiceComponentId']),
                new Int($arrData['intComponentHardwareBundleConfigId']),
                new String($arrData['strHandle'])
            );
            $arrBundles[] = $bundle;
        }

        $client = $this->getMock('HardwareClient_Client', [
            'getSignupBundlesForServiceDefinition',
        ]);
        $client
            ->expects($this->any())
            ->method('getSignupBundlesForServiceDefinition')
            ->with($sdi)
            ->will($this->returnValue($arrBundles));

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->any())
            ->method('shouldUseHelper')
            ->willReturn(false);

        $requirement = $this->getMock('AccountChange_Hardware', array('getHardwareClient','getHardwareHelper'));

        $requirement->expects($this->any())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $requirement
            ->expects($this->any())
            ->method('getHardwareClient')
            ->will($this->returnValue($client));

        $this->mockService($requirement);

        $result = $requirement->fetchAvailableHardware($sdi);
        $this->assertEquals($expected, $result);
        $result2 = $requirement->fetchAvailableHardware($sdi);
        $this->assertEquals($expected, $result2);
        $result3 = $requirement->fetchAvailableHardware($sdi);
        $this->assertEquals($expected, $result3);
    }

    /**
     * Provide data
     *
     * @return void
     */
    public function providerForTestFetchAvailableCallsDbOnlyOnceAndCachesResult()
    {
        return array(
            // Data set 0
            array(
                array(
                    array(
                        'strName'                            => 'Thomson 4-port Wireless Router Bundle',
                        'strDescription'                     => 'Thomson 4-port Wireless Router Bundle (free)',
                        'intServiceComponentId'              => '00000653',
                        'intComponentHardwareBundleConfigId' => '46',
                        'strHandle'                          => 'Thomson4PortWirelessBundleFree',
                        'intHardwareBundleCost'              => '4000',
                    ),
                    array(
                        'strName'                            => 'Thomson 1-port Wired Router Bundle',
                        'strDescription'                     => 'Thomson 1-port Wired Router Bundle (free)',
                        'intServiceComponentId'              => '00000654',
                        'intComponentHardwareBundleConfigId' => '47',
                        'strHandle'                          => 'Thomson1PortWiredBundleFree',
                        'intHardwareBundleCost'              => '2000',
                    ),
                ),
                new Int(1234),
                array(
                    array(
                        'handle' => new String('Thomson4PortWirelessBundleFree'),
                        'name'   => new String('Thomson 4-port Wireless Router Bundle'),
                    ),
                    array(
                        'handle' => new String('Thomson1PortWiredBundleFree'),
                        'name'   => new String('Thomson 1-port Wired Router Bundle'),
                    )
                )
            )
        );
    }

    /**
     * Data provider for testExtensionKitValidatorWithData
     *
     * @return array
     */
    public function provideDataForExtensionKitValidator()
    {
        return array(
            // #0
            array(
                1,
                array(
                    'dataExtensionKit' => false,
                    'extensionKitId'   => EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED
                )
            ),
            // #1
            array(
                null,
                array(
                    'dataExtensionKit' => false,
                    'extensionKitId'   => EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED
                )
            ),
            // #2
            array(
                true,
                array(
                    'dataExtensionKit' => false,
                    'extensionKitId'   => EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED
                )
            ),
            // #3
            array(
                'true',
                array(
                    'dataExtensionKit' => false,
                    'extensionKitId'   => EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED
                )
            ),
            // #4
            array(
                'off',
                array(
                    'dataExtensionKit' => false,
                    'extensionKitId'   => EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_NOT_REQUIRED
                )
            ),
            // #5
            array(
                'on',
                array(
                    'dataExtensionKit' => true,
                    'extensionKitId'   => EngineerAppointmentClient_Service_Fttc::EXTENSION_KIT_REQUIRED
                )
            ),
        );
    }

    /**
     * Check that getSelectedBroadband returns the application state
     * variable that we're expecting
     *
     * @covers AccountChange_Hardware::getSelectedBroadband
     *
     * @return void
     **/
    public function testGetSelectedBroadbandReturnsExpected()
    {
        $selectedBroadband = array(
            'serviceDefinitionId' => 6768
        );

        $hardware = new AccountChange_Hardware();

        // We need to mock application controller as {@link Mvc_InputRecipient::getApplicationStateVariable()}
        // and {@link Mvc_InputRecipient::isApplicationStateVariable()} are declared as final and cannot be mocked.
        // Instead we mock {@link Mvc_WizardController::getApplicationStateVar()}
        $mockController
            = $this->getMock('AccountChange_Controller', array('getApplicationStateVar'));

        $mockController
            ->expects($this->once())
            ->method('getApplicationStateVar')
            ->with('AccountChange_Hardware', 'arrSelectedBroadband')
            ->will($this->returnValue($selectedBroadband));

        // Set callback object within the requirement itself to application controller
        $hardware->setAppStateCallback($mockController);
        $actual = $hardware->getSelectedBroadband();
        $this->assertEquals($actual, $selectedBroadband);
    }

    /**
     * Test that the describe function returns an array with hardware as a key
     *
     * @param array $hardware Hardware
     *
     * @covers AccountChange_Hardware::describe
     * @dataProvider hardwareProvider
     *
     * @return void
     */
    public function testDescribeReturnsNoHardwareCostForFibreUpgradeCampaigns($hardware)
    {
        $intNewSdi = 1234;
        $intServiceId = 1234;

        $mockCoreService = $this->getMock(Core_Service::class, array('getServiceId', 'getAdslDetails'));
        $mockCoreService->expects($this->any())
            ->method('getServiceId')
            ->willReturn($intServiceId);

        $adslDetails = [
            'isp' => 'plus.net',
            'type' => 'residential',
        ];
        $mockCoreService->expects($this->any())
            ->method('getAdslDetails')
            ->willReturn($adslDetails);

        $mockBusinessActor = $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->setMethods(['getUserType'])
            ->getMock();

        $mockBusinessActor->expects($this->once())
            ->method('getUserType')
            ->willReturn('PLUSNET_STAFF');

        $this->_currentData = array(
            'campaignCode' => 'fibreUpgrade',
            'objCoreService' => $mockCoreService,
            'objBusinessActor' => $mockBusinessActor,
        );

        $requirement = $this->getMock(
            'AccountChange_Hardware',
            array(
                'fetchAvailableHardware',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'isC2fToggleSet',
            )
        );

        $requirement
            ->expects($this->once())
            ->method('fetchAvailableHardware')
            ->with(new Int(1234))
            ->will($this->returnValue($hardware));

        $requirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(null));

        $requirement
            ->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $controller = $this->getMock(
            'AccountChange_Controller',
            array('getApplicationStateVar', 'isApplicationStateVar')
        );

        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnGetApplicationStateVar')));

        $controller
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnCallback(array($this, 'returnIsApplicationStateVar')));

        $requirement->setAppStateCallback($controller);

        $data = array('intNewSdi' => $intNewSdi);
        $result = $requirement->describe($data);
        $this->assertArrayHasKey('hardware', $result);
        $this->assertEquals(
            array(
                'hardware' => $hardware,
                'selectedBroadband' => null,
                'productDetails' => array(),
                'currentProductDownloadSpeedRangeMin' => null,
                'currentProductDownloadSpeedRangeMax' => null,
                'hardwareCost' => 0,
                'exVatHardwareCost' => 0,
                'campaignCode' => 'fibreUpgrade',
                'routerHandle' => new String(null),
                'defaultSelectedHardware' => new String(null),
                'showWaivePostage' => false,
            ),
            $result
        );
    }

    private function mockService(AccountChange_Hardware $hardware)
    {
        $mockService = $this->getMockBuilder(Core_Service::class)
            ->disableOriginalConstructor()
            ->setMethods(['getType', 'getAdslDetails'])
            ->getMock();

        $mockService->expects($this->any())
            ->method('getType')
            ->willReturn(5678);

        $mockService->expects($this->any())
            ->method('getAdslDetails')
            ->willReturn([
                'isp' => 'plus.net',
                'type' => 'residential',
            ]);

        $hardware->setService($mockService);
    }

    /**
     * @return void
     */
    public function testDescribeReturnsAnArrayWithRouterHandle()
    {
        $intNewSdi = 1234;
        $intOldSdi = 5678;
        $intServiceId = 1234;
        $isp = 'plus.net';
        $type = 'residential';
        $handle = new String('ROUTER_HANDLE');

        $mockCoreService = $this->getMockBuilder(Core_Service::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'getServiceId',
                'getType',
                'getAdslDetails',
            ])
            ->getMock();

        $mockCoreService->expects($this->any())
            ->method('getServiceId')
            ->willReturn($intServiceId);

        $mockCoreService->expects($this->any())
            ->method('getAdslDetails')
            ->willReturn([
                'isp' => $isp,
                'type' => $type,
            ]);

        $mockBusinessActor = $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->setMethods(['getUserType'])
            ->getMock();

        $mockBusinessActor->expects($this->once())
            ->method('getUserType')
            ->willReturn('PLUSNET_STAFF');

        $this->_currentData = array(
            'objCoreService' => $mockCoreService,
            'objBusinessActor' => $mockBusinessActor,
        );

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(false);

        $requirement = $this->getMock(
            'AccountChange_Hardware',
            [
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getHardwareCharges',
                'isC2fToggleSet',
                'getFibreHelper',
                'getHardwareHelper'
            ]
        );

        $requirement->expects($this->once())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $controller = $this->getMockBuilder(AccountChange_Controller::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'getApplicationStateVar',
                'isApplicationStateVar',
            ])
            ->getMock();

        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback([$this, 'returnGetApplicationStateVar']));
        $controller
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnCallback([$this, 'returnIsApplicationStateVar']));

        $requirement->setAppStateCallback($controller);

        $requirement->expects($this->once())
            ->method('isC2fToggleSet')
            ->willReturn(false);

        $mockHardwareClient = $this->getMockBuilder(HardwareClient_Client::class)
            ->disableOriginalConstructor()
            ->setMethods(['getSignupBundlesForServiceDefinition'])
            ->getMock();

        $bundleArray = new HardwareClient_BundleArray();

        $hardwareArray[] = [
            'handle' => $handle,
            'name' => 'Hardware'
        ];
        $hardware = $this->getMockBuilder(HardwareClient_Bundle::class)
            ->disableOriginalConstructor()
            ->setMethods(['getHandle', 'getName'])
            ->getMock();
        $hardware->expects($this->exactly(2))
            ->method('getHandle')
            ->willReturn(new String($handle));
        $hardware->expects($this->once())
            ->method('getName')
            ->willReturn(new String('Hardware'));

        $bundleArray[] = $hardware;

        $mockHardwareClient->expects($this->once())
            ->method('getSignupBundlesForServiceDefinition')
            ->with(new Int($intNewSdi))
            ->willReturn($bundleArray);

        BusTier_BusTier::setClient('hardware', $mockHardwareClient);

        $requirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->will($this->returnValue(null));

        $requirement
            ->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->will(
                $this->returnValue(
                    array(
                        'downloadSpeedRangeMin' => null,
                        'downloadSpeedRangeMax' => null
                    )
                )
            );

        $requirement
            ->expects($this->once())
            ->method('getHardwareCharges')
            ->willReturn(['amount' => 1.2, 'exVatAmount' => 1]);

        $this->mockFibreHelper($requirement);

        $data = [
            'intNewSdi' => $intNewSdi,
        ];

        $result = $requirement->describe($data);

        $this->assertEquals(
            array(
                'hardware' => $hardwareArray,
                'selectedBroadband' => null,
                'productDetails' => array(),
                'currentProductDownloadSpeedRangeMin' => null,
                'currentProductDownloadSpeedRangeMax' => null,
                'hardwareCost' => '1.20',
                'exVatHardwareCost' => '1.00',
                'campaignCode' => null,
                'routerHandle' => $handle,
                'defaultSelectedHardware' => $handle,
                'showWaivePostage' => false,
            ),
            $result
        );
    }

    /**
     * @return void
     * @throws Db_TransactionException
     * @throws NumberFormatException
     * @throws WrongTypeException
     */
    public function testDefaultHardwareOptionIsSetCorrectlyWithHelperOff()
    {
        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper'])
            ->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(false);

        $requirement = $this->getMockBuilder(AccountChange_Hardware::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'isC2fToggleSet',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getHardwareCharges',
                'getFibreHelper',
                'getHardwareHelper'
            ])->getMock();

        $requirement->expects($this->once())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $requirement
            ->expects($this->once())
            ->method('isC2fToggleSet')
            ->willReturn(false);

        $requirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->willReturn(null);

        $requirement->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->willReturn([
                'downloadSpeedRangeMin' => null,
                'downloadSpeedRangeMax' => null,
            ]);

        $requirement->expects($this->once())
            ->method('getHardwareCharges')
            ->willReturn([
                'description' => 'Postage and Packaging',
                'amount' => 1,
                'exVatAmount' => 1,
                'gross' => true,
            ]);

        $mockFibreHelper = $this->getMockBuilder(AccountChange_FibreHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['isFibreProduct'])
            ->getMock();

        $mockFibreHelper->expects($this->atMost(2))
            ->method('isFibreProduct')
            ->willReturnOnConsecutiveCalls($isOldProductFibre, $isNewProductFibre);

        $requirement->expects($this->any())
            ->method('getFibreHelper')
            ->willReturn($mockFibreHelper);

        $this->setUpController($requirement);

        $mockService = $this->getMockBuilder(Core_Service::class)
            ->disableOriginalConstructor()
            ->setMethods(['getServiceId', 'getAdslDetails', 'getType'])
            ->getMock();

        $mockService->expects($this->once())
            ->method('getServiceId')
            ->willReturn(1234);

        $mockService->expects($this->any())
            ->method('getAdslDetails')
            ->willReturn([
                'isp' => 'plus.net',
                'type' => 'residential',
            ]);

        $mockBusinessActor = $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->setMethods(['getUserType'])
            ->getMock();

        $mockBusinessActor->expects($this->once())
            ->method('getUserType')
            ->willReturn('PLUSNET_STAFF');

        $this->_currentData = array(
            'objCoreService' => $mockService,
            'campaignCode' => null,
            'objBusinessActor' => $mockBusinessActor,
        );

        $mockHardwareClient = $this->getMockBuilder(HardwareClient_Client::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'getSignupBundlesForServiceDefinition'
            ])->getMock();

        $signupBundles = new HardwareClient_BundleArray();

        $hardware = $this->getMockBuilder(HardwareClient_Bundle::class)
            ->disableOriginalConstructor()
            ->setMethods(['getHandle', 'getName'])
            ->getMock();
        $hardware->expects($this->any())
            ->method('getHandle')
            ->willReturn(new String('PN_HUB_ONE'));
        $hardware->expects($this->once())
            ->method('getName')
            ->willReturn(new String('Hub One'));

        $signupBundles[] = $hardware;

        $mockHardwareClient->expects($this->once())
            ->method('getSignupBundlesForServiceDefinition')
            ->willReturn($signupBundles);

        BusTier_BusTier::setClient('hardware', $mockHardwareClient);

        $applicationData = ['intNewSdi' => new Int(1234)];
        $result = $requirement->describe($applicationData);

        $this->assertEquals($result['defaultSelectedHardware'], 'PN_HUB_ONE');
    }

    /**
     * @dataProvider dataHardwareDefaultWithHelper
     * @param string|nulll $defaultSelection
     * @return void
     * @throws NumberFormatException
     * @throws WrongTypeException
     */
    public function testDefaultHardwareOptionIsSetCorrectlyWithHelperOn($defaultSelection)
    {
        $hardwareArray =  [
            'handle' => 'PN_HUB_ONE',
            'name' => 'Router name',
        ];

        $mockHelper = $this->getMockBuilder(AccountChange_HardwareRequirementHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['shouldUseHelper','getHardwareOffer'])
            ->getMock();

        $mockHardwareOffer = $this->getMockBuilder(AccountChange_HardwareOffer::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'getHardwareBundleDetails',
                'getHardwareDefaultSelection',
                'getHardwareHandle'
            ])->getMock();

        $mockHelper->expects($this->once())
            ->method('shouldUseHelper')
            ->willReturn(true);

        $mockHelper->expects($this->once())
            ->method('getHardwareOffer')
            ->willReturn($mockHardwareOffer);

        $mockHardwareOffer->expects($this->once())
            ->method('getHardwareBundleDetails')
            ->willReturn($hardwareArray);

        $mockHardwareOffer->expects($this->once())
            ->method('getHardwareDefaultSelection')
            ->willReturn($defaultSelection);

        $mockHardwareOffer->expects($this->once())
            ->method('getHardwareHandle')
            ->willReturn('PN_HUB_ONE');

        $requirement = $this->getMockBuilder(AccountChange_Hardware::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'isC2fToggleSet',
                'getSelectedBroadband',
                'getMinAndMaxSpeedRanges',
                'getHardwareCharges',
                'getFibreHelper',
                'getHardwareHelper'
            ])->getMock();

        $requirement->expects($this->once())
            ->method('getHardwareHelper')
            ->will($this->returnValue($mockHelper));

        $requirement
            ->expects($this->once())
            ->method('isC2fToggleSet')
            ->willReturn(false);

        $requirement
            ->expects($this->once())
            ->method('getSelectedBroadband')
            ->willReturn(null);

        $requirement->expects($this->once())
            ->method('getMinAndMaxSpeedRanges')
            ->willReturn([
                'downloadSpeedRangeMin' => null,
                'downloadSpeedRangeMax' => null,
            ]);

        $requirement->expects($this->once())
            ->method('getHardwareCharges')
            ->willReturn([
                'description' => 'Postage and Packaging',
                'amount' => 1,
                'exVatAmount' => 1,
                'gross' => true,
            ]);

        $mockFibreHelper = $this->getMockBuilder(AccountChange_FibreHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['isFibreProduct'])
            ->getMock();

        $mockFibreHelper->expects($this->atMost(2))
            ->method('isFibreProduct')
            ->willReturnOnConsecutiveCalls(true, true);

        $requirement->expects($this->any())
            ->method('getFibreHelper')
            ->willReturn($mockFibreHelper);

        $this->setUpController($requirement);

        $mockService = $this->getMockBuilder(Core_Service::class)
            ->disableOriginalConstructor()
            ->setMethods(['getServiceId', 'getAdslDetails', 'getType'])
            ->getMock();

        $mockService->expects($this->once())
            ->method('getServiceId')
            ->willReturn(1234);

        $mockService->expects($this->any())
            ->method('getAdslDetails')
            ->willReturn([
                'isp' => 'plus.net',
                'type' => 'residential',
            ]);

        $mockBusinessActor = $this->getMockBuilder(Auth_BusinessActor::class)
            ->disableOriginalConstructor()
            ->setMethods(['getUserType'])
            ->getMock();

        $mockBusinessActor->expects($this->once())
            ->method('getUserType')
            ->willReturn('PLUSNET_STAFF');

        $this->_currentData = array(
            'objCoreService' => $mockService,
            'campaignCode' => null,
            'objBusinessActor' => $mockBusinessActor,
        );

        $applicationData = ['intNewSdi' => new Int(1234)];
        $result = $requirement->describe($applicationData);

        $this->assertEquals($defaultSelection, $result['defaultSelectedHardware']);
    }

    /**
     * @dataProvider
     * @return array[]
     */
    public function dataHardwareDefaultWithHelper()
    {
        return [
            ['PN_HUB_ONE'],
            [new String(null)]
        ];
    }

    private function setUpController($requirement)
    {
        $controller = $this->getMockBuilder(AccountChange_Controller::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'getApplicationStateVar',
                'isApplicationStateVar',
            ])
            ->getMock();

        $controller
            ->expects($this->any())
            ->method('getApplicationStateVar')
            ->will($this->returnCallback([$this, 'returnGetApplicationStateVar']));
        $controller
            ->expects($this->any())
            ->method('isApplicationStateVar')
            ->will($this->returnCallback([$this, 'returnIsApplicationStateVar']));

        $requirement->setAppStateCallback($controller);
    }

    private function mockFibreHelper($requirement)
    {
        $mockFibreHelper = $this->getMockBuilder(AccountChange_FibreHelper::class)
            ->disableOriginalConstructor()
            ->setMethods(['isFibreProduct'])
            ->getMock();

        $mockFibreHelper->expects($this->any())
            ->method('isFibreProduct')
            ->willReturn(true);

        $requirement->expects($this->any())
            ->method('getFibreHelper')
            ->willReturn($mockFibreHelper);
    }

    /**
     * @dataProvider dataForTestValWaivePostage
     * @param null|string $waivePostage       whether checkbox is checked in response from user
     * @param boolean     $shouldShowCheckbox whether the checkbox should be shown on the page
     * @param array       $expectedResult     the expected response from the validation function
     * @param array       $expectedErrors     the expected errors array
     * @return void
     */
    public function testValWaivePostage($waivePostage, $shouldShowCheckbox, $expectedResult, $expectedErrors)
    {
        $mockHardware = $this->getMockBuilder(AccountChange_Hardware::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'shouldShowWaivePostageCheckbox',
            ])->getMock();

        $mockHardware->expects($this->any())
            ->method('shouldShowWaivePostageCheckbox')
            ->willReturn($shouldShowCheckbox);

        $result = $mockHardware->valWaivePostage($waivePostage);
        $errors = $mockHardware->getValidationErrors();

        $this->assertEquals($expectedResult, $result);
        $this->assertEquals($expectedErrors, $errors);
    }

    /**
     * @return array[]
     */
    public function dataForTestValWaivePostage()
    {
        return [
            'checkboxChecked' => [
                'waivePostage' => 'on',
                'shouldShowCheckbox' => true,
                'expectedResult' => ['waivePostage' => true],
                'expectedErrors' => [],
            ],
            'checkboxCheckedButShouldNotBeShown' => [
                'waivePostage' => 'on',
                'shouldShowCheckbox' => false,
                'expectedResult' => ['waivePostage' => true],
                'expectedErrors' => [
                    'waivePostage' => [
                        'INVALID' => true,
                    ],
                ],
            ],
            'checkboxNotChecked' => [
                'waivePostage' => null,
                'shouldShowCheckbox' => true,
                'expectedResult' => ['waivePostage' => false],
                'expectedErrors' => [],
            ],
        ];
    }

    /**
     * @dataProvider dataRouterHandles
     * @param string|null $routerHandle
     * @param string|null $expectedValue
     * @return void
     */
    public function testGetSetRouterHandle($routerHandle, $expectedValue)
    {
        $mockHardware = $this->getMockBuilder(AccountChange_Hardware::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'shouldShowWaivePostageCheckbox'
            ])
            ->getMock();

        $mockHardware->setRouterHandle($routerHandle);
        $this->assertEquals($expectedValue, $mockHardware->getRouterHandle());
    }

    /**
     * @dataProvider dataRouterHandles
     * @param string|null $routerHandle
     * @param string|null $expectedValue
     * @return void
     */
    public function testGetSetDefaultSelectedHardware($routerHandle, $expectedValue)
    {
        $mockHardware = $this->getMockBuilder(AccountChange_Hardware::class)
            ->disableOriginalConstructor()
            ->setMethods([
                'shouldShowWaivePostageCheckbox'
            ])
            ->getMock();

        $mockHardware->setDefaultSelectedHardware($routerHandle);
        $this->assertEquals($expectedValue, $mockHardware->getDefaultSelectedHardware());
    }

    /**
     * @return array[]
     */
    public function dataRouterHandles()
    {
        return [
            'null' => [null, new String(null)],
            'empty' => ['', new String('')],
            'string' => ['PN_HUB_ONE', new String('PN_HUB_ONE')],
        ];
    }
}
