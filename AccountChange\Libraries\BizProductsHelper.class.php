<?php
/**
 * Class AccountChange_BizProductsHelper
 * Helper class for biz products.
 *
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_BizProductsHelper
{
    /**
     * For a given product sdi check if it's ADSL and capped.
     * Used in the template to add a message to the customer
     * to make them aware this product is capped.
     *
     * See DBIZ-209
     *
     * @param  integer $productSdi
     * @return oolean
     */
    public static function isCappedADSLProduct($productSdi)
    {
        // Sdi's for all contract lengths and with/without phone.
        $listOfCappedProducts = array(6801, 6802, 6803, 6811, 6812, 6813);
        return in_array($productSdi, $listOfCappedProducts);
    }

    /**
     * For a given product sdi check if it's Fibre and capped.
     * Used in the template to add a message to the customer
     * to make them aware this product is capped.
     *
     * See DBIZ-209
     *
     * @param  integer $productSdi
     * @return boolean
     */
    public static function isCappedFibreProduct($productSdi)
    {
        // Sdi's for all contract lengths and with/without phone.
        $listOfCappedProducts = array(6807, 6808, 6817, 6818);
        return in_array($productSdi, $listOfCappedProducts);
    }
}
