server: coredb
role: master
rows: single
statement:

SELECT
    intTariffID
FROM
    products.service_components AS sc
INNER JOIN dbProductComponents.tblProductComponentConfig AS pcc
    ON sc.service_component_id = pcc.intservicecomponentproductid
INNER JOIN dbProductComponents.tblTariff AS t
    ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
WHERE
    sc.service_component_id = :serviceComponentId;