CREATE TABLE products.service_component_config (
  service_component_config_id int(8) unsigned zerofill NOT NULL auto_increment,
  service_definition_id int(8) unsigned zerofill NOT NULL default '00000000',
  service_component_id int(8) unsigned zerofill NOT NULL default '00000000',
  points_value tinyint(3) unsigned NOT NULL default '0',
  free_quantity tinyint(3) unsigned NOT NULL default '0',
  default_quantity tinyint(3) unsigned NOT NULL default '0',
  max_quantity int(4) default NULL,
  bolAvailableInSignup tinyint(1) NOT NULL default '0',
  db_src char(4) NOT NULL default '',
  time_stamp timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (service_component_config_id),
  UNIQUE KEY service_definition_id (service_definition_id,service_component_id),
  KEY idx_service_component_id (service_component_id,service_definition_id)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 PACK_KEYS=1