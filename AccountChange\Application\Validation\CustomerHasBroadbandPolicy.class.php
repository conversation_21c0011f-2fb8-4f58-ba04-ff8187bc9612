<?php

/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_CustomerHasBroadbandPolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE = 'You cannot make this change as you do not have a broadband product with us.';
    const ERROR_CODE = 'ERROR_CUSTOMER_IS_SOLUS';

    /** @var int $serviceDefinitionId */
    private $serviceDefinitionId;

    /**
     * @param Auth_BusinessActor $actor                 Business Actor
     * @param bool               $isWorkplace           is workplace flag
     * @param bool               $isScript              is script flag
     * @param array              $additionalInformation extra params
     */
    public function __construct(
        Auth_BusinessActor $actor,
        $isWorkplace = false,
        $isScript = false,
        $additionalInformation = array()
    ) {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);

        $this->serviceDefinitionId = $this->additionalInformation['serviceDefinitionId'];
    }

    /**
     * @return bool
     */
    public function validate()
    {
        $productFamily = $this->getProductFamily();

        return !$productFamily->isPhoneOnly();
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return static::ERROR_MESSAGE;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return static::ERROR_CODE;
    }

    /**
     * @return ProductFamily_ProductFamily
     */
    protected function getProductFamily()
    {
        return ProductFamily_Factory::getFamily($this->serviceDefinitionId);
    }
}
