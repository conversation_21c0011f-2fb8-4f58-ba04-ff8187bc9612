<?php
/**
* Confirmation of the Account change process
*
* @package   AccountChange
* <AUTHOR> <arunshan<PERSON>@qburst.com>
*
* @copyright 2011 PlusNet
* @since     File available since 2011-09-14
*/
/**
 * AccountChange_ConfirmDetails class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 PlusNet
 */
class AccountChange_ConfirmDetails extends Mvc_WizardRequirement
{
    /**
     * Inputs for this requirement
     *
     * Dummy variable needed for Framework Wizard app to complete the requirement
     *
     * @var array
     */
    protected $arrInputs = array(
        'accountChangeComplete'    => 'external:string;In(1,0)'
    );

    /**
     * Describe
     *
     * Pull all the information needed for the view of this requirement
     *
     * @param array &$arrValidatedApplicationData Validated application data
     *
     * @return array
     */
    public function describe(array &$arrValidatedApplicationData)
    {
        return array(
            'selectedBroadband' => $this->getApplicationStateVariable('arrSelectedBroadband'),
            'promoCodeInvalidated' => $this->getApplicationStateVariable('promoCodeInvalidated')
        );

    }
}
