<?php
/**
 * Controller
 *
 * Testing class for the AccountChange_Controller
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-10-01
 */
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';

use Plusnet\BillingApiClient\Entity\AvailableProduct;
use Plusnet\HouseMoves\Models\HouseMoveAddress;
use Plusnet\HouseMoves\Services\ServiceManager;
use Plusnet\PriceProtected\Services\StatusService;
use Plusnet\ContractsClient\Entity\ContractTypeDto;

/**
 * Controller Test Class
 *
 * @package   AccountChange
 * <AUTHOR> <smare<PERSON>@plus.net>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_ControllerTest extends PHPUnit_Framework_TestCase
{
    /**
     * PHPUnit setUp function
     *
     * @return void
     */
    public function setUp()
    {
        date_default_timezone_set('Europe/London');

        $account = $this->getMock(
            'AccountChange_Account',
            array(
                'getWlrInformation', 'getVoipInformation', 'hasPassedAdslChangeGracePeriod',
                'getAdslGracePeriod', 'getExistingWlrChangeScheduledData',
                'getExistingContractStartDate', 'getCostLevelForServiceId', 'getCurrentBroadbandTariff',
                'getCostLevel'
            ),
            array(new Int(1))
        );

        $account->expects($this->any())
            ->method('getWlrInformation');

        $account->expects($this->any())
            ->method('getVoipInformation');

        $account->expects($this->any())
            ->method('hasPassedAdslChangeGracePeriod');

        $account->expects($this->any())
            ->method('getAdslGracePeriod');

        $account->expects($this->any())
            ->method('getExistingWlrChangeScheduledData');

        $account->expects($this->any())
            ->method('getExistingContractStartDate');

        $account->expects($this->any())
            ->method('getCurrentBroadbandTariff');

        $account->expects($this->any())
            ->method('getCostLevel');

        AccountChange_Account::setInstance($account);

        if (!defined('COMPONENT_PAPERBILLING')) {
            define('COMPONENT_PAPERBILLING', 10);
        };

        if (!defined('COMPONENT_PAPERBILLING_FREE')) {
            define('COMPONENT_PAPERBILLING_FREE', 10);
        };

        $this->turnOffNewBillingEngine();

        $logHandler = new Log_Logger();
        $logHandler->registerLogHandler(new Log_NullLogHandler());
        Log_AuditLog::registerLogger($logHandler);
    }

    /**
     * Tear down functionality
     *
     * (non-PHPdoc)
     *
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        \Db_Manager::reset();
        Mockery::close();

        Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('Core');
        Db_Manager::restoreAdaptor('FeatureSwitch');

        AccountChange_Account::setInstance(null);
    }

    /**
     * Test init does not add additional requirements if fibre upgrade campaign code is specified
     *
     * @covers AccountChange_Controller::init
     * @return void
     */
    public function testAdditionalRequirementsArrayIsEmptyIfFibreCampaignCodeSupplied()
    {
        $expected = array();

        $controller = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Controller',
            array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isResidentialPortalUserWithResidentialServiceDefinition'
            ),
            array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isC2fToggleSet'
            ),
            array()
        );

        $controller->method('isResidentialPortalUserWithResidentialServiceDefinition')->willReturn(true);
        $controller->method('isC2fToggleSet')->willReturn(false);

        $result = $controller->protected_init(true, '', 'test', 'null', 'false', 'fibreUpgrade');

        $this->assertArrayHasKey('arrAdditionalReqs', $result);
        $this->assertEquals($expected, $result['arrAdditionalReqs']);
    }

    /**
     * Test that supplying a fibre upgrade code modifies the views to be displayed in the wizard
     *
     * @covers AccountChange_Controller::init
     * @return void
     */
    public function testWizardViewsAreModifiedForFibreUpgradeJourney()
    {
        $expected = array(
            'FibreUpgradeBroadband' => array(
                'AccountChange_SelectBroadband'
            ),

            'BroadbandWorkplace' => array(
                'AccountChange_SelectBroadbandWorkplace'
            ),

            'Hardware' => array(
                'AccountChange_Hardware'
            ),

            'Homephone' => array(
                'AccountChange_SelectHomephone'
            ),

            'HomephoneWorkplace' => array(
                'AccountChange_SelectHomephoneWorkplace'
            ),

            'AddressDetails' => array(
                'AccountChange_Address'
            ),

            'Engineer' => array(
                'AccountChange_EngineerDetails'
            ),

            'Summary' => array(
                'AccountChange_TermsAndConditions'
            ),

            'PaymentRedirectWorkplace' => array(
                'AccountChange_PaymentWorkplace'
            ),

            'PaymentRedirectPortal' => array(
                'AccountChange_PaymentPortal'
            ),

            'PaymentDetails' => array(
                'AccountChange_PaymentMethodDetails',
                'AccountChange_DirectDebitDetails'
            ),

            'ConfirmDetails' => array(
                'AccountChange_ConfirmDetails'
            )
        );

        $controller = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Controller',
            array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isResidentialPortalUserWithResidentialServiceDefinition'
            ),
            array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isC2fToggleSet'
            ),
            array()
        );

        $controller->method('isResidentialPortalUserWithResidentialServiceDefinition')->willReturn(true);
        $controller->method('isC2fToggleSet')->willReturn(false);

        $controller->protected_init(true, '', 'test', 'null', 'false', 'fibreUpgrade');
        $views = $controller->getViews();
        $this->replaceMockWithTrueClassName($views, "AccountChange");
        $this->assertEquals($expected, $views);
    }

    /**
     * Test that supplying a fibre upgrade code modifies the views to be displayed in the wizard
     *
     * @covers AccountChange_Controller::init
     * @return void
     */
    public function testWizardViewsAreModifiedForC2FJourney()
    {
        $expected = array(

          'C2FBroadband' => array(
            'AccountChange_SelectBroadband'
          ),

          'BroadbandWorkplace' => array(
            'AccountChange_SelectBroadbandWorkplace'
          ),

          'C2FHardware' => array(
            'AccountChange_Hardware'
          ),

          'C2FHomephone' => array(
            'AccountChange_SelectHomephone'
          ),

          'HomephoneWorkplace' => array(
            'AccountChange_SelectHomephoneWorkplace'
          ),

          'AddressDetails' => array(
            'AccountChange_Address'
          ),

          'Engineer' => array(
            'AccountChange_EngineerDetails'
          ),

          'C2FSummary' => array(
            'AccountChange_TermsAndConditions'
          ),

          'PaymentRedirectWorkplace' => array(
            'AccountChange_PaymentWorkplace'
          ),

          'PaymentRedirectPortal' => array(
            'AccountChange_PaymentPortal'
          ),

          'PaymentDetails' => array(
            'AccountChange_PaymentMethodDetails',
            'AccountChange_DirectDebitDetails'
          ),

          'ConfirmDetails' => array(
            'AccountChange_ConfirmDetails'
          ),

        );

        $controller = TestCaseWithProxy::getPHPUnitProxy(
          'AccountChange_Controller',
          array()
        );


        $controller = $this->getMock(
          get_class($controller),
          array(
            'isResidentialPortalUserWithResidentialServiceDefinition'
          ),
          array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isC2fToggleSet'
            ),
            array()
        );

        $controller->method('isResidentialPortalUserWithResidentialServiceDefinition')->willReturn(true);
        $controller->method('isC2fToggleSet')->willReturn(true);

        $controller->protected_init(true, '', 'test', 'null', 'false', null);
        $views = $controller->getViews();
        $this->replaceMockWithTrueClassName($views, 'AccountChange');

        $this->assertEquals($expected, $views);
    }


    /**
     * Recursively iterate of the the $expected array and and replace the
     * string "Mock" with $name
     *
     * @param array $expected
     * @param $name
     */
    private function replaceMockWithTrueClassName(&$expected, $name){

        foreach($expected as $key => &$data){
            if(!is_array($data)){
                $data = str_replace("Mock", $name, $data);
            }else{
                $this->replaceMockWithTrueClassName($data, $name);
            }
        }
    }

    /**
     * Test that not supplying a fibre upgrade code leaves the array of views the same
     *
     * @covers AccountChange_Controller::init
     * @return void
     */
    public function testWizardViewsRemainTheSameIfNoFibreUpgradeCampaignCodeSupplied()
    {
        $expected = array(
            'CurrentAccount' => array(
                'AccountChange_ShowCurrentAccount',
                'AccountChange_ShowLinechecker'
            ),

            'Broadband' => array(
                'AccountChange_SelectBroadband',
            ),

            'BroadbandWorkplace' => array(
                'AccountChange_SelectBroadbandWorkplace'
            ),

            'Hardware' => array(
                'AccountChange_Hardware'
            ),

            'Homephone' => array(
                'AccountChange_SelectHomephone'
            ),

            'HomephoneWorkplace' => array(
                'AccountChange_SelectHomephoneWorkplace'
            ),

            'AddressDetails' => array(
                'AccountChange_Address'
            ),

            'Engineer' => array(
                'AccountChange_EngineerDetails'
            ),

            'Summary' => array(
                'AccountChange_TermsAndConditions'
            ),

            'PaymentRedirectWorkplace' => array(
                'AccountChange_PaymentWorkplace'
            ),

            'PaymentRedirectPortal' => array(
                'AccountChange_PaymentPortal'
            ),

            'PaymentDetails' => array(
                'AccountChange_PaymentMethodDetails',
                'AccountChange_DirectDebitDetails'
            ),

            'ConfirmDetails' => array(
                'AccountChange_ConfirmDetails'
            )
        );

        $controller = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Controller',
            array()
        );

        $controller = $this->getMock(
          get_class($controller),
          array(
            'isC2fToggleSet'
          ),
          array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isResidentialPortalUserWithResidentialServiceDefinition'
            ),
            array()
        );

        $controller->method('isResidentialPortalUserWithResidentialServiceDefinition')->willReturn(false);
        $controller->method('isC2fToggleSet')->willReturn(false);


        $controller->protected_init(true, '', 'test', false);

        $views = $controller->getViews();
        $this->replaceMockWithTrueClassName($views, "AccountChange");



        $this->assertEquals($expected, $views);
    }

    /**
     * Test init does not add workplace requirement if not logged into workplace
     *
     * @covers AccountChange_Controller::init
     *
     * @return void
     */
    public function testInitDoesNotAddWorkplaceRequirementIfNotLoggedIntoWorkplace()
    {
        $arrExpected = array(
            'AccountChange_ShowCurrentAccount',
            'AccountChange_ShowLinechecker'
        );

        $objController = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Controller',
            array('getAdslComponentExists')
        );

        $controller = $this->getMock(
          get_class($objController),
          array(
            'getAdslComponentExists',
              'isC2fToggleSet'
          ),
          array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isResidentialPortalUserWithResidentialServiceDefinition'
            ),
            array()
        );

        $controller->method('isResidentialPortalUserWithResidentialServiceDefinition')->willReturn(false);

        $controller->method('isC2fToggleSet')->willReturn(false);

        $arrResult = $controller->protected_init(true, '', 'test', false);

        $this->assertArrayHasKey('arrAdditionalReqs', $arrResult);
        $this->assertEquals($arrExpected, $arrResult['arrAdditionalReqs']);
    }

    /**
     * Test init correctly adds workplace requirement if logged into workplace as
     * Plusnet staff
     *
     * @covers AccountChange_Controller::init
     *
     * @return void
     */
    public function testInitCorrectlyAddsWorkplaceRequirementIfLoggedIntoWorkplaceAsPlusnetStaff()
    {
        $arrExpected = array(
            'AccountChange_ShowCurrentAccount',
            'AccountChange_ShowLinechecker',
            'AccountChange_SelectBroadbandWorkplace'
        );

        $objBusinessActor = new Auth_BusinessActor(null);
        $objBusinessActor->setRealm('workplace.plus.net');
        $objBusinessActor->setUserType('PLUSNET_STAFF');
        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);

        $objController = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Controller', array());

        $controller = $this->getMock(
          get_class($objController),
          array(
              'isC2fToggleSet'
          ),
          array()
        );

        $controller = $this->getMock(
            get_class($controller),
            array(
                'isResidentialWorkplaceUserWithResidentialServiceDefinition'
            ),
            array()
        );

        $controller->method('isResidentialWorkplaceUserWithResidentialServiceDefinition')->willReturn(true);

        $controller->method('isC2fToggleSet')->willReturn(false);


        $arrResult = $controller->protected_init(true, '', 'test', false);

        $this->assertArrayHasKey('arrAdditionalReqs', $arrResult);
        $this->assertEquals($arrExpected, $arrResult['arrAdditionalReqs']);
    }

    /**
     * Test init correctly adds workplace requirement if logged into workplace
     * not as Plusnet staff
     *
     * @covers AccountChange_Controller::init
     *
     * @return void
     */
    public function testInitCorrectlyAddsWorkplaceRequirementIfLoggedIntoWorkplaceNotAsPlusnetStaff()
    {
        $arrExpected = array(
            'AccountChange_ShowCurrentAccount',
            'AccountChange_ShowLinechecker',
            'AccountChange_SelectBroadband'
        );

        $objBusinessActor = new Auth_BusinessActor(null);
        $objBusinessActor->setRealm('workplace.plus.net');
        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);

        $objController = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Controller', array());

        $controller = $this->getMock(
          get_class($objController),
          array(
            'isC2fToggleSet'
          ),
          array()
        );

        $controller->method('isC2fToggleSet')->willReturn(false);

        $arrResult = $controller->protected_init(true, '', 'test', false);

        $this->assertArrayHasKey('arrAdditionalReqs', $arrResult);
        $this->assertEquals($arrExpected, $arrResult['arrAdditionalReqs']);
    }

    /**
     * Test init correctly adds workplace requirement if logged into workplace
     * as Plusnet end user
     *
     * @covers AccountChange_Controller::init
     * @group  medium
     *
     * @return void
     */
    public function testInitCorrectlyAddsWorkplaceRequirementIfLoggedIntoWorkplaceAsPlusnetEndUser()
    {
        $arrExpected = array(
            'AccountChange_ShowCurrentAccount',
            'AccountChange_ShowLinechecker',
            'AccountChange_SelectBroadband'
        );

        $objBusinessActor = $this->getMockBusinesActor();

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceDao',
                'getUserDao',
                'getAddressDao',
                'getServiceDefinitionDao',
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService',
                'getCostLevelForServiceId'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDao')
            ->will($this->returnValue($this->getServiceDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getUserDao')
            ->will($this->returnValue($this->getUserDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getAddressDao')
            ->will($this->returnValue($this->getAddressDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($this->getServiceDefinitionDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objFinanceDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getDdiByServiceAndStatuses'),
            array('Financial', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objFinanceDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Financial', $objFinanceDbAdaptor);

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $mode = $this->getMock(
            'AccountChange_Mode',
            array('getChangeMode'),
            array($objBusinessActor)
        );

        $mode->expects($this->any())
            ->method('getChangeMode')
            ->will($this->returnValue(AccountChange_Mode::BOTH));

        AccountChange_Mode::setInstance($mode);

        $objController = $this->getMock(
            'AccountChange_Controller',
            array(
                'getValidationCheck',
                'getProvisionedProduct',
                'matchAddress',
                'getAdslComponentExists',
                'getExistingBroadbandContractData',
                'getProvisionedService',
                'isNewBillingEngineOn',
                'getDirectDebitDetailsLegacy',
                'expectingPhoneForHouseMove',
                'getHouseMoveAddress',
                'getStatusService',
                'isC2fToggleSet',
                'getCoreService',
                'getPriceIncreasePercent'
            ),
            array()
        );

        $this->setupMockStatusService($objController);
        $this->setUpBillingApiResponseAndCoreService($objController);

        $objController->expects($this->any())
            ->method('getPriceIncreasePercent')
            ->will($this->returnValue(1));

        $objController->expects($this->once())
            ->method('getHouseMoveAddress')
            ->will($this->returnValue(new HouseMoveAddress()));

        $objController->expects($this->any())
            ->method('getValidationCheck')
            ->will($this->returnValue($validationCheck));

        $objController->expects($this->any())
            ->method('getProvisionedProduct')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('matchAddress')
            ->will($this->returnValue(new Wlr3_Addresses()));

        $objController->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(''));

        $objController->expects($this->any())
            ->method('isNewBillingEngineOn')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('expectingPhoneForHouseMove')
            ->with(12345)
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('getDirectDebitDetailsLegacy')
            ->will($this->returnValue(array()));

        $objController->method('isC2fToggleSet')->willReturn(false);

        $objAuthMock = $this->getMock(
            'Db_Adaptor',
            array('getActorByExternalUserId', 'getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAuthMock->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(array('intActorId' => 1)));

        $objAuthMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objAuthMock);

        $objAccountChangeMock = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService',
                'getCostLevelForServiceId'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue($this->getServiceDefinitionDetailsForServiceData()));

        Db_Manager::setAdaptor('AccountChange', $objAccountChangeMock);

        $arrResult = $objController->init(true, '', 'test', false, new Val_ReferralPromoCode('PROMOCODE'), true);

        $this->assertArrayHasKey('arrAdditionalReqs', $arrResult);
        $this->assertEquals($arrExpected, $arrResult['arrAdditionalReqs']);

        $this->assertArrayHasKey('promoCode', $arrResult['arrData']);
        $this->assertEquals('PROMOCODE', $arrResult['arrData']['promoCode']);
        $this->assertFalse(isset($arrResult['arrData']['promoCodeInvalid']));
    }

    /**
     * Test to make sure that the override checks are abided by
     *
     * @param boolean $accountChangeAllowed       Is account change allowed
     * @param string  $reason                     The reasons for the blockage
     * @param boolean $override                   Are we wanting to override
     * @param boolean $reasonForBlockageKeyExists Will they key for blockage exist
     * @param array   $expected                   Key pair value of data we expect to be returned
     * @param array   $notExpected                Keys we do not want to find in the return of init
     * @param bool    $isAdsl                     If is adsl
     *
     * @covers       AccountChange_Controller::init
     *
     * @dataProvider provideDataForTestInitCorrectlyDealsWithOverrideFunctionality
     *
     * @return void
     */
    public function testInitCorrectlyDealsWithOverrideFunctionality(
        $accountChangeAllowed,
        $reason,
        $override,
        $reasonForBlockageKeyExists,
        $expected,
        $notExpected,
        $isAdsl
    ) {
        $objMockAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAdaptor->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objMockAdaptor, Db_Manager::DEFAULT_TRANSACTION);

        $objBusinessActor = Auth_BusinessActor::get(1);
        $objBusinessActor->setRealm('workplace.plus.net');
        $objBusinessActor->setUserType('PLUSNET_ENDUSER');
        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceDao', 'getUserDao', 'getAddressDao',
                'getServiceDefinitionDao', 'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService', 'getCostLevelForServiceId'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDao')
            ->will($this->returnValue($this->getServiceDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getUserDao')
            ->will($this->returnValue($this->getUserDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getAddressDao')
            ->will($this->returnValue($this->getAddressDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($this->getServiceDefinitionDaoData($isAdsl)));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue($this->getServiceDefinitionDetailsForServiceData()));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objFinanceDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getDdiByServiceAndStatuses'),
            array('Financial', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objFinanceDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Financial', $objFinanceDbAdaptor);

        $mode = $this->getMock(
            'AccountChange_Mode',
            array('getChangeMode'),
            array($objBusinessActor)
        );

        $mode->expects($this->any())
            ->method('getChangeMode')
            ->will($this->returnValue(AccountChange_Mode::BOTH));

        AccountChange_Mode::setInstance($mode);

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue($accountChangeAllowed));

        $validationCheck->expects($this->any())
            ->method('getReasonForBlockage')
            ->will($this->returnValue($reason));

        $objController = $this->getMock(
            'AccountChange_Controller',
            array(
                'getValidationCheck',
                'getProvisionedProduct',
                'matchAddress',
                'getAdslComponentExists',
                'getExistingBroadbandContractData',
                'getProvisionedService',
                'isNewBillingEngineOn',
                'getDirectDebitDetailsLegacy',
                'getStatusService',
                'getCoreService' ,
                'getPriceIncreasePercent'
            ),
            array()
        );

        $this->setupMockStatusService($objController);
        $this->setUpBillingApiResponseAndCoreService($objController);

        $objController->expects($this->any())
            ->method('getPriceIncreasePercent')
            ->will($this->returnValue(1));

        $objController->expects($this->any())
            ->method('matchAddress')
            ->will($this->returnValue(new Wlr3_Addresses()));

        $objController->expects($this->any())
            ->method('getValidationCheck')
            ->will($this->returnValue($validationCheck));

        $objController->expects($this->any())
            ->method('getProvisionedProduct')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(''));

        $objController->expects($this->any())
            ->method('isNewBillingEngineOn')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('getDirectDebitDetailsLegacy')
            ->will($this->returnValue(array()));

        $objAuthMock = $this->getMock(
            'Db_Adaptor',
            array('getActorByExternalUserId', 'getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAuthMock->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(array('intActorId' => 1)));
        $objAuthMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objAuthMock);

        $objAccountChangeMock = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService',
                'getCostLevelForServiceId'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue($this->getServiceDefinitionDetailsForServiceData()));

        Db_Manager::setAdaptor('AccountChange', $objAccountChangeMock);

        $arrResult = $objController->init(true, '', $override, false);

        foreach ($expected as $key => $value) {
            $this->assertEquals($arrResult['arrData'][$key], $value);
        }

        foreach ($notExpected as $key) {
            $this->assertArrayNotHasKey($key, $arrResult['arrData']);
        }

        if ($reasonForBlockageKeyExists) {
            $this->assertArrayHasKey('reasonForBlockage', $arrResult['arrData']);
        } else {
            $this->assertArrayNotHasKey('reasonForBlockage', $arrResult['arrData']);
        }
    }

    /**
     * Provide data for testInitCorrectlyDealsWithOverrideFunctionality
     *
     * @return array
     */
    public static function provideDataForTestInitCorrectlyDealsWithOverrideFunctionality()
    {
        return array(
            array(
                true, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE, true, false,
                array(), array(), true
            ),
            array(
                true, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE, false, false, array(), array(), true
            ),
            array(
                false, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE, true, true, array(), array(), true
            ),
            array(
                false, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE, false, true, array(), array(), true
            ),

            array(
                true, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE_IN_PAST, true, false, array(), array(), true
            ),
            array(
                true, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE_IN_PAST, false, false, array(), array(), true
            ),
            array(
                false, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE_IN_PAST, true, true, array(), array(), true
            ),
            array(
                false, AccountChange_InvoiceDateCorrectPolicy::ERROR_MESSAGE_INVOICE_DATE_IN_PAST, false, true, array(), array(), true
            ),
        );
    }

    /**
     * Test getCompleteView returns complete if result was success
     *
     * @covers AccountChange_Controller::getCompleteView
     *
     * @return void
     */
    public function testGetCompleteViewReturnsCompleteIfResultWasSuccess()
    {
        $objController = new AccountChange_Controller();
        $arrVars = array();

        $this->assertEquals('Complete', $objController->getCompleteView(Mvc_ActionResult::SUCCESS, $arrVars));
    }

    /**
     * Test getCompleteView returns failed if result was not sent
     *
     * @covers AccountChange_Controller::getCompleteView
     *
     * @return void
     */
    public function testGetCompleteViewReturnsFailedIfResultWasNotSent()
    {
        $objController = new AccountChange_Controller();
        $arrVars = array();

        $this->assertEquals('Failed', $objController->getCompleteView('', $arrVars));
    }

    /**
     * Test getEntryPoint method is set to GET
     *
     * @covers AccountChange_Controller::getEntryPointMethod
     *
     * @return void
     */
    public function testGetEntryPointMethodIsSetToGET()
    {
        $objController = new AccountChange_Controller();

        $this->assertEquals(Mvc_HttpRequest::GET, $objController->getEntryPointMethod());
    }

    /**
     * Test memberAppVariables are setup correctly
     *
     * Make sure that the member variables for the application setup procedure are correct
     *
     * @return void
     */
    public function testMemberAppVariablesAreSetupCorrectly()
    {
        $arrMinimumReqs = array('TermsAndConditions');
        $arrViews = array(
            'CurrentAccount'           => array(
                'AccountChange_ShowCurrentAccount',
                'AccountChange_ShowLinechecker',
            ),
            'Broadband'                => array(
                'AccountChange_SelectBroadband',
            ),
            'BroadbandWorkplace'       => array(
                'AccountChange_SelectBroadbandWorkplace'
            ),
            'Hardware'                 => array(
                'AccountChange_Hardware'
            ),
            'Homephone'                => array(
                'AccountChange_SelectHomephone'
            ),
            'HomephoneWorkplace'       => array(
                'AccountChange_SelectHomephoneWorkplace'
            ),
            'AddressDetails'           => array(
                'AccountChange_Address'
            ),
            'Engineer'                 => array(
                'AccountChange_EngineerDetails'
            ),
            'Summary'                  => array(
                'AccountChange_TermsAndConditions'
            ),
            'PaymentRedirectWorkplace' => array(
                'AccountChange_PaymentWorkplace'
            ),
            'PaymentRedirectPortal'    => array(
                'AccountChange_PaymentPortal'
            ),
            'PaymentDetails'           => array(
                'AccountChange_PaymentMethodDetails',
                'AccountChange_DirectDebitDetails'
            ),
            'ConfirmDetails'           => array(
                'AccountChange_ConfirmDetails'
            )
        );

        $arrRedirectUrls = array(
            'PaymentRedirectWorkplace'
            => 'https://workplace.plus.net/apps/{targetActor}/paymentdetails/takepayment?bolWizReset=1&paymentHandoverId={paymentHandoverId}&validationHash={validationHash}',
            'PaymentRedirectPortal'
            => 'https://portal.plus.net/apps/paymentdetails/takepayment?bolWizReset=1&paymentHandoverId={paymentHandoverId}&validationHash={validationHash}'
        );

        $objController = new AccountChange_Controller();

        $this->assertAttributeEquals(true, 'bolSecure', $objController);
        $this->assertAttributeEquals(true, 'bolAutoLoginAllowed', $objController);
        $this->assertAttributeEquals(7200, 'intSecsToMaintainAppState', $objController);
        $this->assertAttributeEquals(50, 'intExpectedSecsDuration', $objController);
        $this->assertAttributeEquals(array('PLUSNET_ENDUSER'), 'arrTargetActorTypes', $objController);
        $this->assertAttributeEquals($arrMinimumReqs, 'arrMinimumReqs', $objController);
        $this->assertAttributeEquals($arrViews, 'arrViews', $objController);
        $this->assertAttributeEquals($arrRedirectUrls, 'arrRedirectUrls', $objController);
    }

    /**
     * Test getCurrentBusinessActor returns a valid business actor
     *
     * @covers AccountChange_Controller::getCurrentBusinessActor
     *
     * @return void
     */
    public function testGetCurrentBusinessActorReturnsAValidBusinessActor()
    {
        $objBusinessActor = new Auth_BusinessActor(null);

        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->once())
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);

        $objController = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Controller', array());

        $objResult = $objController->protected_getCurrentBusinessActor();

        $this->assertEquals($objBusinessActor, $objResult);
    }

    /**
     * Test isBundle returns true if the Sdi is a bundle
     *
     * @covers AccountChange_Controller::isBundle
     *
     * @return void
     */
    public function testIsBundleReturnstrueIfTheSdiIsABundle()
    {
        $intSdi = 6704;

        $bolResult = AccountChange_Controller::isBundle($intSdi);

        $this->asserttrue($bolResult);
    }

    /**
     * Test isBundle returns false if the Sdi is not a bundle
     *
     * @covers AccountChange_Controller::isBundle
     *
     * @return void
     */
    public function testIsBundleReturnsFalseIfTheSdiIsNotABundle()
    {
        $intSdi = 1;

        $bolResult = AccountChange_Controller::isBundle($intSdi);

        $this->assertfalse($bolResult);
    }

    /**
     * Test that the getAllBundles returns correct information
     *
     * @covers AccountChange_Controller::getAllBundles
     *
     * @return void
     */
    public function testGetAllBundlesReturnsCorrectBundles()
    {
        $expected = AccountChange_Controller::$arrNonEssentialBundles + AccountChange_Controller::$arrEssentialBundles;

        $result = AccountChange_Controller::getAllBundles();

        $this->assertEquals($expected, $result);
    }

    /**
     * Test isEssential returns true if the product is essential
     *
     * @covers AccountChange_Controller::isEssential
     *
     * @return void
     */
    public function testIsEssentialReturnsTrueIfTheProductIsEssential()
    {
        $intSdiAnnual = 6704;
        $intSdiMonthly = 6705;

        $bolResultAnnual = AccountChange_Controller::isEssential($intSdiAnnual);
        $bolResultMonthly = AccountChange_Controller::isEssential($intSdiMonthly);

        $this->asserttrue($bolResultAnnual);
        $this->asserttrue($bolResultMonthly);
    }

    /**
     * Test isEssential returns false if the product isnt essential
     *
     * @covers AccountChange_Controller::isEssential
     *
     * @return void
     */
    public function testIsEssentialReturnsFalseIfTheProductIsntEssential()
    {
        $intSdi = 1;

        $bolResult = AccountChange_Controller::isEssential($intSdi);

        $this->assertfalse($bolResult);
    }

    /**
     * Test isWlrEssential
     *
     * @covers AccountChange_Controller::isWlrEssential
     *
     * @return void
     */
    public function testIsWlrEssential()
    {
        $intSci = 596;

        $bolResult = AccountChange_Controller::isWlrEssential($intSci);

        $this->asserttrue($bolResult);
    }

    /**
     * Test isNotWlrEssential
     *
     * @covers AccountChange_Controller::isWlrEssential
     *
     * @return void
     */
    public function testIsNotWlrEssential()
    {
        $intSci = 583;

        $bolResult = AccountChange_Controller::isWlrEssential($intSci);

        $this->assertfalse($bolResult);
    }

    /**
     * Test getProductNameBySdi
     *
     * @covers AccountChange_Controller::getProductNameBySdi
     *
     * @return void
     */
    public function testGetProductNameBySdi()
    {

        $fixture = 'Plusnet Essential';

        $intSdi = 6705;
        $strResult = AccountChange_Controller::getProductNameBySdi($intSdi);
        $this->assertEquals($strResult, $fixture);

        $intSdi = 6704;
        $strResult = AccountChange_Controller::getProductNameBySdi($intSdi);
        $this->assertEquals($strResult, $fixture);
    }

    /**
     * Test getWlrIdBySdi
     *
     * @covers AccountChange_Controller::getWlrIdBySdi
     *
     * @return void
     */
    public function testGetWlrIdBySdi()
    {
        $fixture = 596;

        $intSdi = 6704;
        $strResult = AccountChange_Controller::getWlrIdBySdi($intSdi);
        $this->assertEquals($strResult, $fixture);

        $intSdi = 6704;
        $strResult = AccountChange_Controller::getWlrIdBySdi($intSdi);
        $this->assertEquals($strResult, $fixture);

        $fixture = 583;
        $intSdi = 6687;
        $strResult = AccountChange_Controller::getWlrIdBySdi($intSdi);
        $this->assertEquals($strResult, $fixture);

        $intSdi = 6688;
        $strResult = AccountChange_Controller::getWlrIdBySdi($intSdi);
        $this->assertEquals($strResult, $fixture);
    }


    /**
     * Data provider for get products
     *
     * @return array
     */
    public function provideDataForGetProducts()
    {
        $mockProductFamily = $this->getMock(
            'ProductFamily_Res2012',
            array('isDualPlay'),
            array(),
            '',
            false
        );

        $mockProductFamily
            ->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $arrProductFamilyAndVariantProToPro = array(
            array(
                'productFamilyHandle'  => 'BBYW',
                'productVariantHandle' => 'PRO'
            ),
            array(
                'productFamilyHandle'  => 'VALUE',
                'productVariantHandle' => 'VALUE'
            ),
            array(
                'productFamilyHandle'  => 'VALUE',
                'productVariantHandle' => 'EXTRA'
            ),
            array(
                'productFamilyHandle'  => 'VALUE',
                'productVariantHandle' => 'PRO'
            ),
        );

        $arrProductFamilyAndVariantExtraInfo = array(
            array(
                'productFamilyHandle'  => 'VALUE',
                'productVariantHandle' => 'VALUE'
            ),
            array(
                'productFamilyHandle'  => 'VALUE',
                'productVariantHandle' => 'STANDARD'
            ),
            array(
                'productFamilyHandle'  => 'VALUE',
                'productVariantHandle' => 'PRO'
            )
        );

        $arrProductFamilyAndVariantLegacyProduct = array(
            array(
                'productFamilyHandle'  => 'VALUE',
                'productVariantHandle' => 'VALUE'
            ),
            array(
                'productFamilyHandle'  => 'LegacyProduct',
                'productVariantHandle' => null
            )
        );

        $arrServiceDefinitionDetailsProToPro = array(
            'service_definition_id' => 88,
            'name'                  => 'BBYW Pro',
            'minimum_charge'        => 10,
            'service_id'            => 9,
            'type'                  => 'Value',
            'provisioningProfile'   => '500',
            'isp'                   => 'plus.net',
            'signup_via_portal'     => 1
        );

        $arrServiceDefinitionDetailsExtraInfo = array(
            'service_definition_id' => 1,
            'type'                  => 'Anything',
            'provisioningProfile'   => '500',
            'isp'                   => 'plus.net',
            'signup_via_portal'     => 1
        );

        $arrServiceDefinitionDetailsLegacyProduct = array(
            'service_definition_id' => 98,
            'name'                  => 'Madasafish Max Plus Broadband',
            'minimum_charge'        => 10,
            'service_id'            => 9,
            'type'                  => 'Value',
            'provisioningProfile'   => '500',
            'isp'                   => 'plus.net',
            'signup_via_portal'     => 1
        );

        $arrProductsProToPro = array(
            array(
                'service_definition_id' => 1,
                'name'                  => 'Product Name 1',
                'minimum_charge'        => 10,
                'vchContract'           => 'MONTHLY',
                'vchDisplayName'        => 'Monthly',
                'intTariffID'           => 123,
                'provisioningProfile'   => '500',
                'signup_via_portal'     => 1
            ),
            array(
                'service_definition_id' => 2,
                'name'                  => 'Product Name 2',
                'minimum_charge'        => 10,
                'vchContract'           => 'MONTHLY',
                'vchDisplayName'        => 'Monthly',
                'intTariffID'           => 123,
                'provisioningProfile'   => '500',
                'signup_via_portal'     => 1
            ),
            array(
                'service_definition_id' => 3,
                'name'                  => 'Product Name 3',
                'minimum_charge'        => 10,
                'vchContract'           => 'MONTHLY',
                'vchDisplayName'        => 'Monthly',
                'intTariffID'           => 123,
                'provisioningProfile'   => '500',
                'signup_via_portal'     => 1
            ),
        );

        $arrProductsExtraInfo = array(
            array(
                'service_definition_id' => 1,
                'name'                  => 'Product Name 1',
                'minimum_charge'        => 10,
                'vchContract'           => 'MONTHLY',
                'vchDisplayName'        => 'Monthly',
                'intTariffID'           => 123,
                'provisioningProfile'   => '500',
                'signup_via_portal'     => 1
            ),
            array(
                'service_definition_id' => 2,
                'name'                  => 'Product Name 2',
                'minimum_charge'        => 10,
                'vchContract'           => 'MONTHLY',
                'vchDisplayName'        => 'Monthly',
                'intTariffID'           => 123,
                'provisioningProfile'   => '500',
                'signup_via_portal'     => 1
            ),
            array(
                'service_definition_id' => 3,
                'name'                  => 'Product Name 3',
                'minimum_charge'        => 10,
                'vchContract'           => 'MONTHLY',
                'vchDisplayName'        => 'Monthly',
                'intTariffID'           => 123,
                'provisioningProfile'   => '500',
                'signup_via_portal'     => 1
            ),
        );

        $arrProductsLegacy = array(
            array(
                'service_definition_id' => 77,
                'name'                  => 'Product Name 4',
                'minimum_charge'        => 10,
                'vchContract'           => 'MONTHLY',
                'vchDisplayName'        => 'Monthly',
                'intTariffID'           => 123,
                'costLevel'             => '',
                'provisioningProfile'   => '500',
                'signup_via_portal'     => 1
            )
        );

        $arrExpectedReturnProToPro = array(
            array(
                'intSdi'                   => $arrProductsProToPro[0]['service_definition_id'],
                'strProductName'           => $arrProductsProToPro[0]['name'],
                'intProductCost'           => new I18n_Currency('gbp', $arrProductsProToPro[0]['minimum_charge']),
                'objProductDiscountedCost' => null,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intTariffID'              => 123,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsProToPro[0]['provisioningProfile'],
                'isFibre'                  => false,
                'isDual'                   => true,
                'intContractLengthMonths'  => 1,
                'defaultContractLength'    => 'MONTHLY',
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
            ),
            array(
                'intSdi'                   => $arrProductsProToPro[1]['service_definition_id'],
                'strProductName'           => $arrProductsProToPro[1]['name'],
                'intProductCost'           => new I18n_Currency('gbp', $arrProductsProToPro[1]['minimum_charge']),
                'objProductDiscountedCost' => null,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intTariffID'              => 123,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsProToPro[1]['provisioningProfile'],
                'isFibre'                  => false,
                'isDual'                   => true,
                'intContractLengthMonths'  => 1,
                'defaultContractLength'    => 'MONTHLY',
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
            ),
            array(
                'intSdi'                   => $arrProductsProToPro[2]['service_definition_id'],
                'strProductName'           => $arrProductsProToPro[2]['name'],
                'intProductCost'           => new I18n_Currency('gbp', $arrProductsProToPro[2]['minimum_charge']),
                'objProductDiscountedCost' => null,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intTariffID'              => 123,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsProToPro[2]['provisioningProfile'],
                'isFibre'                  => false,
                'isDual'                   => true,
                'intContractLengthMonths'  => 1,
                'defaultContractLength'    => 'MONTHLY',
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
            ),
            array(
                'intSdi'                   => $arrServiceDefinitionDetailsProToPro['service_definition_id'],
                'strProductName'           => $arrServiceDefinitionDetailsProToPro['name'],
                'intProductCost'           => new I18n_Currency(
                    'gbp',
                    $arrServiceDefinitionDetailsProToPro['minimum_charge']
                ),
                'objProductDiscountedCost' => null,
                'strContract'              => null,
                'strContractLength'        => null,
                'intTariffID'              => null,
                'costLevel'                => '',
                'provisioningProfile'      => $arrServiceDefinitionDetailsProToPro['provisioningProfile'],
                'isFibre'                  => false,
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
                'isDual'                   => true,
            ),
        );

        $arrExpectedReturnExtraInfo = array(
            array(
                'intSdi'                   => $arrProductsExtraInfo[0]['service_definition_id'],
                'strProductName'           => $arrProductsExtraInfo[0]['name'],
                'intProductCost'           => new I18n_Currency('gbp', $arrProductsExtraInfo[0]['minimum_charge']),
                'objProductDiscountedCost' => null,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intTariffID'              => 123,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsExtraInfo[0]['provisioningProfile'],
                'isFibre'                  => false,
                'isDual'                   => 1,
                'intContractLengthMonths'  => 1,
                'defaultContractLength'    => 'MONTHLY',
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
                'isDual'                   => true,
                'defaultContractLength'    => 'MONTHLY'
            ),
            array(
                'intSdi'                   => $arrProductsExtraInfo[1]['service_definition_id'],
                'strProductName'           => $arrProductsExtraInfo[1]['name'],
                'intProductCost'           => new I18n_Currency('gbp', $arrProductsExtraInfo[1]['minimum_charge']),
                'objProductDiscountedCost' => null,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intTariffID'              => 123,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsExtraInfo[1]['provisioningProfile'],
                'isFibre'                  => false,
                'isDual'                   => 1,
                'intContractLengthMonths'  => 1,
                'defaultContractLength'    => 'MONTHLY',
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
                'isDual'                   => true,
                'defaultContractLength'    => 'MONTHLY'
            ),
            array(
                'intSdi'                   => $arrProductsExtraInfo[2]['service_definition_id'],
                'strProductName'           => $arrProductsExtraInfo[2]['name'],
                'intProductCost'           => new I18n_Currency('gbp', $arrProductsExtraInfo[2]['minimum_charge']),
                'objProductDiscountedCost' => null,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intTariffID'              => 123,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsExtraInfo[2]['provisioningProfile'],
                'isFibre'                  => false,
                'isDual'                   => 1,
                'intContractLengthMonths'  => 1,
                'defaultContractLength'    => 'MONTHLY',
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
                'isDual'                   => true,
                'defaultContractLength'    => 'MONTHLY'
            ),
        );

        $arrExpectedReturnLegacyProduct = array(
            array(
                'intSdi'                   => $arrProductsLegacy[0]['service_definition_id'],
                'strProductName'           => $arrProductsLegacy[0]['name'],
                'intProductCost'           => new I18n_Currency('gbp', $arrProductsLegacy[0]['minimum_charge']),
                'objProductDiscountedCost' => null,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intTariffID'              => 123,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsLegacy[0]['provisioningProfile'],
                'isFibre'                  => false,
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
                'isDual'                   => true,
                'intContractLengthMonths'  => 1,
                'defaultContractLength'    => 'MONTHLY'
            ),
            array(
                'intSdi'                   => $arrServiceDefinitionDetailsLegacyProduct['service_definition_id'],
                'strProductName'           => $arrServiceDefinitionDetailsLegacyProduct['name'],
                'intProductCost'           => new I18n_Currency(
                    'gbp',
                    $arrServiceDefinitionDetailsLegacyProduct['minimum_charge']
                ),
                'objProductDiscountedCost' => null,
                'strContract'              => null,
                'strContractLength'        => null,
                'intTariffID'              => null,
                'costLevel'                => '',
                'provisioningProfile'      => $arrProductsLegacy[0]['provisioningProfile'],
                'isFibre'                  => false,
                'maxDownloadSpeed'         => 40,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'productFamily'            => $mockProductFamily,
                'isDual'                   => true,
            ),
        );

        return array(
            array(
                $mockProductFamily,
                $arrProductFamilyAndVariantProToPro,
                $arrServiceDefinitionDetailsProToPro,
                $arrProductsProToPro,
                $arrExpectedReturnProToPro
            ),
            array(
                $mockProductFamily,
                $arrProductFamilyAndVariantExtraInfo,
                $arrServiceDefinitionDetailsExtraInfo,
                $arrProductsExtraInfo,
                $arrExpectedReturnExtraInfo
            ),
            array(
                $mockProductFamily,
                $arrProductFamilyAndVariantLegacyProduct,
                $arrServiceDefinitionDetailsLegacyProduct,
                $arrProductsLegacy,
                $arrExpectedReturnLegacyProduct
            ),
        );
    }

    /**
     * Test generateInvoiceItems returns an empty array if no products have been
     * created
     *
     * @covers AccountChange_Controller::generateInvoiceItems
     *
     * This is very unlikely to happen, in fact it may not even be possible
     * but we might as well write a quick test to cover the case
     *
     * @return void
     */
    public function testGenerateInvoiceItemsReturnsAnEmptyArrayIfNoProductsHaveBeenCreated()
    {
        $objController = TestCaseWithProxy::getPHPUnitProxy('AccountChange_Controller', array());

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('selectAccountChangeType'),
            array(),
            '',
            false
        );

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);


        $arrReturn = AccountChange_Controller::generateInvoiceItems(
            array(
                'arrProductConfigurations' => array(),
                'objAccountChangeManager'  => $objAccountManager
            )
        );

        $this->assertEquals(array(), $arrReturn);
    }

    /**
     * Test generateInvoiceItems returns an array with proRata cost if broadband
     * is upgrading and isnt essential
     *
     * @covers AccountChange_Controller::generateInvoiceItems
     *
     * @return void
     */
    public function testGenerateInvoiceItemsReturnsAnArrayWithProRataCostIfBroadbandIsUpgradingAndIsntEssential()
    {
        $intServiceId = 9999;

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('selectAccountChangeType'),
            array(),
            '',
            false
        );

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'getSetupFee'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getSetupFee')
            ->will($this->returnValue(0));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Mock Db_Adaptor
        $this->objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $a = array("strProductFamily" => 'VALUE', "strType" => 'residential');
        $this->objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($a));

        Db_Manager::setAdaptor('Core', $this->objMockDbAdaptor);

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost', 'calculateProRataCharge', 'getContract', 'getProductId'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objOldProduct->expects($this->any())
            ->method('getContract')
            ->will($this->returnValue('Monthly'));

        $objOldProduct->expects($this->any())
            ->method('getProductId')
            ->will($this->returnValue(1254));

        $objOldProduct->expects($this->once())
            ->method('calculateProRataCharge')
            ->will(
                $this->returnValue(
                    array(
                        'strOutstandingCharges' => "Outstanding Charge",
                        'intOutstandingFees'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 90)
                    )
                )
            );

        $objOldProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(10));

        $objNewProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(20));

        $objOldProduct->setServiceId($intServiceId);
        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $arrData['arrProductConfigurations']['objOldBroadband'] = $objOldProduct;
        $arrData['arrProductConfigurations']['objNewBroadband'] = $objOldProduct;
        $arrData['objAccountChangeManager'] = $objAccountManager;
        $arrData['intNewSdi'] = 123;

        $arrReturn = AccountChange_Controller::generateInvoiceItems($arrData);

        $arrExpectedInvoices = array(
            array(
                'description' => 'Outstanding Charge',
                'amount'      => 90,
                'gross'       => true,
                'handle'      => 'SUBSCRIPTION'
            )
        );

        $this->assertEquals($arrExpectedInvoices, $arrReturn);
    }

    /**
     * Test generateInvoiceItems returns an array with ProRata cost if Wlr is
     * upgrading
     *
     * @covers AccountChange_Controller::generateInvoiceItems
     *
     * @return void
     */
    public function testGenerateInvoiceItemsReturnsAnArrayWithProRataCostIfWlrIsUpgrading()
    {
        $intServiceId = 9999;

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('selectAccountChangeType'),
            array(),
            '',
            false
        );

        $objAccountManager->expects($this->never())
            ->method('selectAccountChangeType')
            ->will($this->returnValue(AccountChange_Manager::ACCOUNT_TYPE_CHANGE_DOWNGRADE));

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);


        $objContract = $this->getMock(
            'AccountChange_Product_ServiceComponentContract',
            array('getContractLengthHandle'),
            array(),
            '',
            false
        );

        $objOldProduct = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductCost', 'calculateProRataCharge', 'getContract'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_Wlr',
            array('getProductCost', 'getContract'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objOldProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(10));

        $objOldProduct->expects($this->once())
            ->method('calculateProRataCharge')
            ->will(
                $this->returnValue(
                    array(
                        'strOutstandingCharges' => "Call plan upgrade one-off fee",
                        'intOutstandingFees'    => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 90)
                    )
                )
            );

        $objOldProduct->expects($this->any())
            ->method('getContract')
            ->will($this->returnValue($objContract));

        $objNewProduct->expects($this->once())
            ->method('getProductCost')
            ->will($this->returnValue(20));

        $objNewProduct->expects($this->any())
            ->method('getContract')
            ->will($this->returnValue($objContract));

        $objOldProduct->setServiceId($intServiceId);
        $objOldProduct->setMatchingProductConfigurationManually($objNewProduct);

        $arrData['arrProductConfigurations']['objOldWlr'] = $objOldProduct;
        $arrData['arrProductConfigurations']['objNewWlr'] = $objOldProduct;
        $arrData['objAccountChangeManager'] = $objAccountManager;
        $arrData['strBroadbandContract'] = 'MONTHLY';
        $arrData['intOldSdi'] = 'Monthly';
        $arrData['intNewSdi'] = 123;

        $arrReturn = AccountChange_Controller::generateInvoiceItems($arrData);

        $arrExpectedInvoices = array(
            array(
                'description' => 'Call plan upgrade one-off fee',
                'amount'      => 90,
                'gross'       => true,
                'handle'      => 'SUBSCRIPTION'
            )
        );

        $this->assertEquals($arrExpectedInvoices, $arrReturn);
    }

    /**
     * Test generateInvoiceItems does not return an array with cancellation
     * charge if bundle is upgrading
     *
     * @covers AccountChange_Controller::generateInvoiceItems
     *
     * @return void
     */
    public function testGenerateInvoiceItemsDoesNotReturnAnArrayWithCancellationChargeIfBundleIsUpgrading()
    {
        $intServiceId = 9999;

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('selectAccountChangeType'),
            array(),
            '',
            false
        );

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'getSetupFee'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        $objMockDbAdaptor->expects($this->any())
            ->method('getSetupFee')
            ->will($this->returnValue(0));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost', 'calculateCancellationCharges'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost', 'getContract'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct->expects($this->any())
            ->method('getContract')
            ->will($this->returnValue('Monthly'));

        $objOldProduct->expects($this->never())
            ->method('calculateCancellationCharges');

        $arrData['arrProductConfigurations']['objOldBroadband'] = $objOldProduct;
        $arrData['arrProductConfigurations']['objNewBroadband'] = $objNewProduct;
        $arrData['objAccountChangeManager'] = $objAccountManager;
        $arrData['strBroadbandContract'] = 'Monthly';
        $arrData['intOldSdi'] = 321;
        $arrData['intNewSdi'] = 123;
        $arrData['bolTakeCharge'] = true;

        $arrReturn = AccountChange_Controller::generateInvoiceItems($arrData);

        $arrExpectedInvoices = array();

        $this->assertEquals($arrExpectedInvoices, $arrReturn);
    }

    /**
     * Test generateInvoiceItems returns BbSetup fee if one exists snd Wlr is
     * taken
     *
     * @covers AccountChange_Controller::generateInvoiceItems
     *
     * @return void
     */
    public function testGenerateInvoiceItemsReturnsBbSetupFeeIfOneExistsAndWlrIsTaken()
    {
        $intServiceId = 9999;

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('selectAccountChangeType'),
            array(),
            '',
            false
        );

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'getSetupFee'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        $objMockDbAdaptor->expects($this->any())
            ->method('getSetupFee')
            ->will($this->returnValue(0));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost', 'calculateCancellationCharges'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost', 'getContract', 'getSetupFee'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct->expects($this->any())
            ->method('getContract')
            ->will($this->returnValue('Monthly'));

        $objNewProduct->expects($this->once())
            ->method('getSetupFee')
            ->will(
                $this->returnValue(
                    array(
                        'description' => 'Activation Fee',
                        'charge'      => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 25),
                    )
                )
            );

        $arrData['arrProductConfigurations']['objOldBroadband'] = $objOldProduct;
        $arrData['arrProductConfigurations']['objNewBroadband'] = $objNewProduct;
        $arrData['objAccountChangeManager'] = $objAccountManager;
        $arrData['strBroadbandContract'] = 'Monthly';
        $arrData['intOldSdi'] = 321;
        $arrData['intNewSdi'] = 123;
        $arrData['bolTakeCharge'] = true;
        $arrData['wlrCustomer'] = false;

        $arrReturn = AccountChange_Controller::generateInvoiceItems($arrData);

        $arrExpectedInvoices = array(
            array(
                'description' => 'Activation Fee',
                'amount'      => 25,
                'gross'       => true,
                'handle'      => 'SETUP'
            )
        );

        $this->assertEquals($arrExpectedInvoices, $arrReturn);
    }

    /**
     * Test generateInvoiceItems sets registry flag if Wlr is taken
     *
     * @covers AccountChange_Controller::generateInvoiceItems
     *
     * @return void
     */
    public function testGenerateInvoiceItemsSetsRegistryFlagIfWlrIsTaken()
    {
        $intServiceId = 9999;

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('selectAccountChangeType'),
            array(),
            '',
            false
        );

        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails', 'getSetupFee'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        $objMockDbAdaptor->expects($this->any())
            ->method('getSetupFee')
            ->will($this->returnValue(0));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        $objOldProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost', 'calculateCancellationCharges'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct = $this->getMock(
            'AccountChange_Product_ServiceDefinition',
            array('getProductCost', 'getContract', 'getSetupFee'),
            array(1234, AccountChange_Product_Manager::ACTION_CHANGE)
        );

        $objNewProduct->expects($this->any())
            ->method('getContract')
            ->will($this->returnValue('Monthly'));

        $objNewProduct->expects($this->once())
            ->method('getSetupFee')
            ->will(
                $this->returnValue(
                    array(
                        'description' => 'Activation Fee',
                        'charge'      => new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 25),
                    )
                )
            );

        $arrData['arrProductConfigurations']['objOldBroadband'] = $objOldProduct;
        $arrData['arrProductConfigurations']['objNewBroadband'] = $objNewProduct;
        $arrData['objAccountChangeManager'] = $objAccountManager;
        $arrData['strBroadbandContract'] = 'Monthly';
        $arrData['intOldSdi'] = 321;
        $arrData['intNewSdi'] = 123;
        $arrData['bolTakeCharge'] = true;
        $arrData['wlrCustomer'] = true;

        $arrReturn = AccountChange_Controller::generateInvoiceItems($arrData);

        $arrExpectedInvoices = array();

        $this->assertEquals($arrExpectedInvoices, $arrReturn);
        $this->asserttrue(AccountChange_Registry::instance()->getEntry('bolActivationContract'));
    }


    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getServiceDaoData()
    {
        return array(
            'service_id'                => 1234,
            'user_id'                   => '12345',
            'isp'                       => 'plusnet',
            'username'                  => 'testuser',
            'password'                  => 'password',
            'cli_number'                => ************,
            'type'                      => 1,
            'status'                    => 'active',
            'startdate'                 => '2008-12-10',
            'enddate'                   => '',
            'next_invoice'              => '2009-10-10',
            'invoice_period'            => '2',
            'db_src'                    => 'test',
            'timestamp'                 => time(),
            'next_invoice_warned'       => '',
            'invoice_day'               => 3,
            'authorised_switch_payment' => '',
            'bolMailOptOut'             => ''
        );
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getBusinessActorData()
    {
        return array(
            array(
                'intActorId'        => 1,
                'strUsername'       => 'testuser',
                'strRealm'          => 'plusnet',
                'strUserType'       => '',
                'strExternalUserId' => '12345'
            )
        );
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getUserDaoData()
    {
        return array(
            'user_id'         => '1234',
            'address_id'      => '12',
            'customer_id'     => '2',
            'telephone'       => '078888888888',
            'strEveningPhone' => '019999999999',
            'fax'             => '019999999999',
            'mobile'          => '078888888888',
            'email'           => '<EMAIL>',
            'position'        => 'dev',
            'salutation'      => 'Mr',
            'forenames'       => 'testforename',
            'surname'         => 'testsurname',
            'hint_question'   => 'test',
            'hint_answer'     => 'test',
            'db_src'          => 'test',
            'timestamp'       => time()
        );
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getAddressDaoData()
    {
        return array(
            array(
                'address_id'    => 12,
                'customer_id'   => 2,
                'house'         => 'test',
                'street'        => 'test',
                'town'          => 'test',
                'county'        => 'test',
                'postcode'      => 's1 4by',
                'country'       => 'UK',
                'status'        => 'active',
                'strTypeHandle' => 'TEST',
                'strTypeName'   => 'TEST'
            )
        );
    }

    /**
     * Wrapper for data generation
     *
     * @param bool $bolAdsl If adsl
     *
     * @return array
     */
    private function getServiceDefinitionDaoData($bolAdsl = true)
    {
        return array(
            'service_definition_id'       => 1,
            'intProductVariantId'         => 12,
            'name'                        => 'Test',
            'isp'                         => 'plusnet',
            'minimum_charge'              => '',
            'date_created'                => '',
            'requires'                    => '',
            'initial_charge'              => '',
            'type'                        => '',
            'password_visible_to_support' => '',
            'end_date'                    => '',
            'signup_via_portal'           => '',
            'bt_product_id'               => '',
            'strProductVariant'           => '',
            'strProductFamily'            => '',
            'bolAdsl'                     => $bolAdsl,
            'signup_via_portal'           => 1
        );
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getServiceDefinitionDetailsForServiceData()
    {
        return array(
            'service_definition_id'       => 1,
            'name'                        => 'Test',
            'isp'                         => 'plusnet',
            'minimum_charge'              => '10',
            'initial_charge'              => '10',
            'type'                        => '',
            'password_visible_to_support' => '',
            'requires'                    => '',
            'date_created'                => '2008-12-10',
            'end_date'                    => '2009-01-10',
            'signup_via_portal'           => '1',
            'blurb'                       => '',
            'vchContract'                 => '',
            'intTariffID'                 => '2617'
        );
    }

    /**
     * Wrapper for data generation
     *
     * @return array
     */
    private function getServiceComponentDetailsForServiceData()
    {
        return array(
            'service_definition_id'       => 1,
            'name'                        => 'Test',
            'isp'                         => 'plusnet',
            'minimum_charge'              => '10',
            'initial_charge'              => '10',
            'type'                        => '',
            'password_visible_to_support' => '',
            'requires'                    => '',
            'date_created'                => '2008-12-10',
            'end_date'                    => '2009-01-10',
            'signup_via_portal'           => '1',
            'blurb'                       => '',
            'vchContract'                 => '',
            'strComponentName'            => '',
            'intTariffID'                 => '2617'
        );
    }

    /**
     * Data provider for testGetCurrentBroadbandProductCorrectlyDecoratesTheCustomersProduct
     *
     * @return array
     */
    public function provideDataForCurrentProductProduct()
    {
        return array(
            array(
                array(
                    'intTariffID'        => 1,
                    'intCostIncVatPence' => 1199,
                    'strContract'        => 'Contract',
                    'strContractLength'  => 'Contract',
                ),
                array(
                    'provisioningProfile' => '500',
                    'isFibre'             => false,
                    'maxDownloadSpeed'    => 40,
                    'maxUploadSpeed'      => null,
                    'activationFee'       => null,
                    'intTariffID'         => 1,
                    'intCostIncVatPence'  => 1199,
                    'strContract'         => 'Contract',
                    'strContractLength'   => 'Contract',
                )
            ),
            array(
                array(),
                array(
                    'provisioningProfile' => '500',
                    'isFibre'             => false,
                    'maxDownloadSpeed'    => 40,
                    'maxUploadSpeed'      => null,
                    'activationFee'       => null,
                    'intTariffID'         => null,
                    'intCostIncVatPence'  => null,
                    'strContract'         => null,
                    'strContractLength'   => null,
                )
            )
        );
    }

    /**
     * Test arrange BoS products
     *
     * @covers AccountChange_Controller::arrangeBoSProducts
     *
     * @return void
     */
    public function testArrangeBoSProducts()
    {
        $arrServiceDefinitionDetails = array(
            'service_definition_id' => 1,
            'strContractLength'     => '24MONTH',
            'costLevel'             => 'Cost Level 1',
        );

        $arrProducts = array(
            array(
                'intSdi'      => 1,
                'strContract' => '24MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
            array(
                'intSdi'      => 1,
                'strContract' => '36MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
            array(
                'intSdi'      => 2,
                'strContract' => '24MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
            array(
                'intSdi'      => 2,
                'strContract' => '36MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
        );

        $result = AccountChange_Controller::arrangeBoSProducts($arrProducts, $arrServiceDefinitionDetails);

        $expected = array(
            array(
                'intSdi'      => 2,
                'strContract' => '24MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
            array(
                'intSdi'      => 1,
                'strContract' => '24MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
            array(
                'intSdi'      => 1,
                'strContract' => '36MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
            array(
                'intSdi'      => 2,
                'strContract' => '36MONTH',
                'costLevel'   => 'Cost Level 1',
            ),
        );

        $this->assertEquals($expected, $result);
    }

    /**
     * Check that the call to SI was made correctly and that the results are interpreted correctly
     *
     * @param unknown $intHomeNumber Home number
     * @param string  $strPostcode   Post code
     * @param array   $result        Expected result
     *
     * @covers       AccountChange_Controller::matchAddress
     *
     * @dataProvider multipleAddressMatchProvider
     *
     * @return void
     */
    public function testMatchAddressWithMultipleResults(
        $intHomeNumber,
        $strPostcode,
        $result
    ) {
        $addresses = new Wlr3_Addresses();

        foreach ($result as $arrAddress) {
            $address = new Wlr3_StrategicImperatives_ValueObject_Address();
            $address->addressReference = $arrAddress['addressReference'];
            $address->cssDatabaseCode = $arrAddress['cssDatabaseCode'];
            $address->addressCategory = $arrAddress['addressCategory'];

            $addresses->addAddress($address);
        }

        $controller = $this->getMock('AccountChange_Controller', array('getWlr3Manager'));

        $mockParamsAddressSearch = $this->getMock(
            'StdClass',
            array(
                'setPostcode',
                'setHomeNumber'
            )
        );

        $mockAddressDetails = $this->getMock(
            'StdClass',
            array(
                'getAddresses'
            )
        );

        $mockAddressDetails->expects($this->once())
            ->method('getAddresses')
            ->will($this->returnValue($addresses));

        $mockWlr3Manager = $this->getMock(
            'StdClass',
            array(
                'createParams',
                'addressSearch'
            )
        );

        $mockWlr3Manager->expects($this->once())
            ->method('createParams')
            ->will($this->returnValue($mockParamsAddressSearch));

        $mockWlr3Manager->expects($this->once())
            ->method('addressSearch')
            ->will($this->returnValue($mockAddressDetails));

        $controller->expects($this->once())
            ->method('getWlr3Manager')
            ->will($this->returnValue($mockWlr3Manager));

        $output = $controller->matchAddress($intHomeNumber, $strPostcode);
        $this->assertEquals($addresses, $output);
    }

    /**
     * Provider for testMatchAddressWithMultipleResults
     *
     * @return array
     */
    public function multipleAddressMatchProvider()
    {
        return array(
            array(
                'a house',
                'IP1 4PA',
                array(
                    array(
                        'addressReference' => 'A000000000',
                        'cssDatabaseCode'  => 'DB',
                        'addressCategory'  => 'GOLD',
                    ),
                    array(
                        'addressReference' => 'A000000001',
                        'cssDatabaseCode'  => 'CC',
                        'addressCategory'  => 'SILVER',
                    ),
                    array(
                        'addressReference' => 'A000000002',
                        'cssDatabaseCode'  => 'GG',
                        'addressCategory'  => 'BRONZE',
                    ),
                ),
            ),
        );
    }

    /**
     * Test that only GB/WR customers have to be checked for paper billing
     *
     * @param string $isp Customers ISP
     *
     * @covers       AccountChange_Controller::customerHasPaperBilling
     * @dataProvider provideDataForHasCustomerGotPaperBillingVispCheck
     *
     * @return void
     */
    public function testHasCustomerGotPaperBillingReturnsfalseForNoneGbWrCustomers($isp)
    {
        $core = $this->getMock('stdClass', array('getIsp'));

        $core->expects($this->once())
            ->method('getIsp')
            ->will($this->returnValue($isp));

        $controller = new AccountChange_Controller();
        $actual = $controller->customerHasPaperBilling($core);

        $this->assertfalse($actual);
    }

    /**
     * Data Provider for testHasCustomerGotPaperBillingReturnsfalseForNoneGbWrCustomers
     *
     * @return array
     */
    public function provideDataForHasCustomerGotPaperBillingVispCheck()
    {
        return array(
            array('plus.net'),
            array('johnlewis'),
            array('partner')
        );
    }

    /**
     * Test that customerHasPaperBilling returns boolean depending on what db returns
     *
     * @param string $isp        Customers ISP
     * @param array  $components Components
     * @param bool   $expected   Expected result
     *
     * @covers       AccountChange_Controller::customerHasPaperBilling
     *
     * @dataProvider provideDataForHasCustomerGotPaperBilling
     *
     * @return void
     */
    public function testHasCustomerGotPaperBillingReturnsBoolean($isp, $components, $expected)
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('getHasPaperBilling'),
            array('Pdf', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->once())
            ->method('getHasPaperBilling')
            ->will($this->returnValue($components));

        Db_Manager::setAdaptor('Pdf', $db, Db_Manager::DEFAULT_TRANSACTION);

        $core = $this->getMock('stdClass', array('getIsp', 'getServiceId'));

        $core->expects($this->once())
            ->method('getIsp')
            ->will($this->returnValue($isp));

        $core->expects($this->once())
            ->method('getServiceId')
            ->will($this->returnValue(123));

        $controller = new AccountChange_Controller();
        $actual = $controller->customerHasPaperBilling($core);

        $this->assertEquals($expected, $actual);
    }

    /**
     * Data Provider for testHasCustomerGotPaperBillingReturnsfalseForNoneGbWrCustomers
     *
     * @return array
     */
    public function provideDataForHasCustomerGotPaperBilling()
    {
        return array(
            // Data Set 0, greenbee visp but no components
            array('greenbee', array(), false),
            // Data Set 1, greenbee visp and has components
            array('greenbee', array('hasPaperBilling' => 1), true),
            // Data Set 2, waitrose visp but no components
            array('waitrose', array(), false),
            // Data Set 3, waitrose visp and components
            array('waitrose', array('hasPaperBilling' => 1), true),
        );
    }

    /**
     * Testing init function when requested to change home phone only.
     *
     * @param AccountChangeMode $accountChangeMode Account change mode
     * @param bool              $wlrRetriction     Wlr restriction
     *
     * @covers       AccountChange_Controller::init
     *
     * @dataProvider provideDataForTestInitForHomePhoneChangeOnlyAllowedOrNot
     *
     * @return void
     */
    public function testInitForHomePhoneChangeOnlyAllowedOrNot($accountChangeMode, $wlrRetriction)
    {
        $objMockAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAdaptor->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objMockAdaptor, Db_Manager::DEFAULT_TRANSACTION);

        $objBusinessActor = Auth_BusinessActor::get(1);
        $objBusinessActor->setRealm('workplace.plus.net');
        $objBusinessActor->setUserType('PLUSNET_ENDUSER');
        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceDao', 'getUserDao', 'getAddressDao',
                'getServiceDefinitionDao', 'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService', 'getCostLevelForServiceId'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDao')
            ->will($this->returnValue($this->getServiceDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getUserDao')
            ->will($this->returnValue($this->getUserDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getAddressDao')
            ->will($this->returnValue($this->getAddressDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($this->getServiceDefinitionDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $objFinanceDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getDdiByServiceAndStatuses'),
            array('Financial', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objFinanceDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Financial', $objFinanceDbAdaptor);

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $mode = $this->getMock(
            'AccountChange_Mode',
            array('getChangeMode'),
            array($objBusinessActor)
        );

        $mode->expects($this->any())
            ->method('getChangeMode')
            ->will($this->returnValue($accountChangeMode));

        AccountChange_Mode::setInstance($mode);

        $objController = $this->getMock(
            'AccountChange_Controller',
            array(
                'getValidationCheck',
                'getProvisionedProduct',
                'matchAddress',
                'getAdslComponentExists',
                'getExistingBroadbandContractData',
                'getProvisionedService',
                'isNewBillingEngineOn',
                'getDirectDebitDetailsLegacy',
                'getStatusService',
                'getCoreService',
                'getPriceIncreasePercent'
            ),
            array()
        );

        $this->setupMockStatusService($objController);
        $this->setUpBillingApiResponseAndCoreService($objController);

        $objController->expects($this->any())
            ->method('getPriceIncreasePercent')
            ->will($this->returnValue(1));

        $objController->expects($this->any())
            ->method('getValidationCheck')
            ->will($this->returnValue($validationCheck));

        $objController->expects($this->any())
            ->method('getProvisionedProduct')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('matchAddress')
            ->will($this->returnValue(new Wlr3_Addresses()));

        $objController->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(''));

        $objController->expects($this->any())
            ->method('isNewBillingEngineOn')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('getDirectDebitDetailsLegacy')
            ->will($this->returnValue(array()));

        $objAuthMock = $this->getMock(
            'Db_Adaptor',
            array('getActorByExternalUserId', 'getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAuthMock->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(array('intActorId' => 1)));

        $objAuthMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objAuthMock);

        $objAccountChangeMock = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService',
                'getCostLevelForServiceId'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue($this->getServiceDefinitionDetailsForServiceData()));

        Db_Manager::setAdaptor('AccountChange', $objAccountChangeMock);

        $arrResult = $objController->init(true, true, 'test', false);

        $this->assertArrayHasKey('arrData', $arrResult);
        $this->assertArrayHasKey('bolWlrChangeRestriction', $arrResult['arrData']);
        $this->assertEquals($wlrRetriction, $arrResult['arrData']['bolWlrChangeRestriction']);
    }

    /**
     * Data provider for function testInitForHomePhoneChangeOnlyAllowedOrNot
     *
     * @return array
     */
    public function provideDataForTestInitForHomePhoneChangeOnlyAllowedOrNot()
    {
        return array(
            array(AccountChange_Mode::ADSL_ONLY, true),
            array(AccountChange_Mode::BOTH, false),
            array(AccountChange_Mode::WLR_ONLY, false),
        );
    }

    /**
     * Provide data to test that getProducts properly set if product is Fibre
     * proper capped max download and max upload speed and activation fees for
     * Fibre
     *
     * @return array
     */
    public function provideDataToTestGetProductsProperlySetIfFibreAndSpeedsAndActivationFee()
    {
        $tariffId = 123;
        $contract = '';
        $productCost = 10;

        $productsNames = array(
            'Extra',
            'Plusnet Value Fibre',
            'Plusnet Extra Fibre'
        );

        $mockProductFamily = $this->getMock(
            'ProductFamily_Res2012',
            array('isDualPlay'),
            array(),
            '',
            false
        );

        $mockProductFamily
            ->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $newProducts = array(
            //Plusnet Extra
            array(
                'service_definition_id' => '6754',
                'name'                  => $productsNames[0],
                'vchDisplayName'        => $productsNames[0],
                'vchDescription'        => $productsNames[0],
                'intTariffID'           => $tariffId,
                'vchContract'           => $contract,
                'minimum_charge'        => $productCost,
                'provisioningProfile'   => 'ADSL',
                'intMaximumSpeed'       => 24000,
                'intMaxUploadSpeed'     => null,
                'activationFee'         => 0.0,
                'signup_via_portal'     => 1
            ),
            //Plusnet Value Fibre
            array(
                'service_definition_id' => '6768',
                'name'                  => $productsNames[1],
                'vchDisplayName'        => $productsNames[1],
                'vchDescription'        => $productsNames[1],
                'intTariffID'           => $tariffId,
                'vchContract'           => $contract,
                'minimum_charge'        => $productCost,
                'provisioningProfile'   => 'FTTC',
                'intMaximumSpeed'       => 40000,
                'intMaxUploadSpeed'     => null,
                'activationFee'         => 25.0,
                'signup_via_portal'     => 1
            ),
            //Plusnet Extra Fibre (new)
            array(
                'service_definition_id' => '6784',
                'name'                  => $productsNames[2],
                'vchDisplayName'        => $productsNames[2],
                'vchDescription'        => $productsNames[2],
                'intTariffID'           => $tariffId,
                'vchContract'           => $contract,
                'minimum_charge'        => $productCost,
                'provisioningProfile'   => 'FTTC',
                'intMaximumSpeed'       => 80000,
                'intMaxUploadSpeed'     => 20000,
                'activationFee'         => 25.0,
                'signup_via_portal'     => 1
            ),
        );

        //Plusnet Value
        $serviceDefinitionDetails = array(
            'service_definition_id' => '6718',
            'service_id'            => '0187632',
            'name'                  => 'Current',
            'provisioningProfile'   => 'ADSL',
            'activationFee'         => 0.0,
            'isp'                   => 'plus.net',
            'type'                  => 'residential',
            'signup_via_portal'     => 1
        );

        return array(
            array(
                'serviceDefinitionDetails' => $serviceDefinitionDetails,
                'products'                 => $newProducts,
                'mockProductFamily'        => $mockProductFamily,
                'expectedResult'           => array(
                    array(
                        'intSdi'                   => '6754',
                        'strProductName'           => $productsNames[0],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[0],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[0],
                        'provisioningProfile'      => 'ADSL',
                        'isFibre'                  => false,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 24,
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 0,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6768',
                        'strProductName'           => $productsNames[1],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[1],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[1],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => 10,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 25.0,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6784',
                        'strProductName'           => $productsNames[2],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[2],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[2],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 80,
                        'maxUploadSpeed'           => 20,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 0,
                        'productFamily'            => $mockProductFamily
                    ),
                    //Current product
                    array(
                        'intSdi'                   => '6718',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => 'ADSL',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'objProductDiscountedCost' => null,
                        'isDual'                   => true,
                        'productFamily'            => $mockProductFamily
                    )
                )
            ),
            array(
                'serviceDefinitionDetails' => $serviceDefinitionDetails,
                'products'                 => $newProducts,
                'mockProductFamily'        => $mockProductFamily,
                'expectedResult'           => array(
                    array(
                        'intSdi'                   => '6754',
                        'strProductName'           => $productsNames[0],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[0],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[0],
                        'provisioningProfile'      => 'ADSL',
                        'isFibre'                  => false,
                        'isDual'                   => 1,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 24,
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6768',
                        'strProductName'           => $productsNames[1],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[1],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[1],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => 1,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => 10,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 25.0,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6784',
                        'strProductName'           => $productsNames[2],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[2],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[2],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 80,
                        'maxUploadSpeed'           => 20,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 0,
                        'productFamily'            => $mockProductFamily
                    ),
                    //Current product
                    array(
                        'intSdi'                   => '6718',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => 'ADSL',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'objProductDiscountedCost' => null,
                        'isDual'                   => true,
                        'productFamily'            => $mockProductFamily
                    )
                )
            ),
            array(
                'serviceDefinitionDetails' => $serviceDefinitionDetails,
                'products'                 => $newProducts,
                'mockProductFamily'        => $mockProductFamily,
                'expectedResult'           => array(
                    array(
                        'intSdi'                   => '6754',
                        'strProductName'           => $productsNames[0],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[0],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[0],
                        'provisioningProfile'      => 'ADSL',
                        'isFibre'                  => false,
                        'isDual'                   => 1,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 24,
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6768',
                        'strProductName'           => $productsNames[1],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[1],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[1],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => 1,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => 10,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 25.0,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6784',
                        'strProductName'           => $productsNames[2],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[2],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[2],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 80,
                        'maxUploadSpeed'           => 20,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 0,
                        'productFamily'            => $mockProductFamily
                    ),
                    //Current product
                    array(
                        'intSdi'                   => '6718',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => 'ADSL',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'objProductDiscountedCost' => null,
                        'isDual'                   => true,
                        'productFamily'            => $mockProductFamily
                    )
                )
            ),
            array(
                'serviceDefinitionDetails' => $serviceDefinitionDetails,
                'products'                 => $newProducts,
                'mockProductFamily'        => $mockProductFamily,
                'expectedResult'           => array(
                    array(
                        'intSdi'                   => '6754',
                        'strProductName'           => $productsNames[0],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[0],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[0],
                        'provisioningProfile'      => 'ADSL',
                        'isFibre'                  => false,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 24,
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6768',
                        'strProductName'           => $productsNames[1],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[1],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[1],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => 10,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 25.0,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '6784',
                        'strProductName'           => $productsNames[2],
                        'intTariffID'              => $tariffId,
                        'strContract'              => $contract,
                        'strContractLength'        => $productsNames[2],
                        'intProductCost'           => new I18n_Currency('gbp', $productCost),
                        'costLevel'                => $productsNames[2],
                        'provisioningProfile'      => 'FTTC',
                        'isFibre'                  => true,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxDownloadSpeed'         => 80,
                        'maxUploadSpeed'           => 20,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => 0,
                        'productFamily'            => $mockProductFamily
                    ),
                    //Current product
                    array(
                        'intSdi'                   => '6718',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => 'ADSL',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'objProductDiscountedCost' => null,
                        'isDual'                   => true,
                        'productFamily'            => $mockProductFamily
                    )
                )
            )
        );
    }

    /**
     * Provide data to test getFttcAppointmentData returns proper values
     *
     * @return array
     */
    public function provideDataToTestGetFttcAppointmentDataReturnsProperValues()
    {
        $productConfigurations = array(
            'objOldBroadband' => null
        );

        $appointment = '15/07/2012AM';
        $appointmentDate1 = new DateTime('2012-07-15');
        $appointmentTime1 = 'AM';
        $appointmentDate2 = new DateTime('2012-07-15');
        $appointmentTime2 = 'PM';
        $appointmentDate3 = new DateTime('2012-07-16');
        $appointmentTime3 = 'AM';

        $coreService = $this->getMock(
            'Core_Service',
            array('getIsp', 'getServiceId', 'getNextInvoiceDate')
        );

        $coreService->expects($this->any())
            ->method('getIsp')
            ->will($this->returnValue('plus.net'));

        $coreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1812332));

        $coreService->expects($this->any())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue('2012-08-01'));

        $accountConfiguration = $this->getMock(
            'AccountChange_AccountConfiguration',
            array('setTotalOneOffCharge'),
            array(array())
        );

        $accountChangeManager = $this->getMock(
            'AccountChange_Manager',
            array('changeAccount', 'getTotalUpgradeCost'),
            array(
                $coreService->getServiceId(),
                $accountConfiguration,
                $accountConfiguration
            )
        );

        $accountChangeManager->expects($this->any())
            ->method('changeAccount')
            ->will($this->returnValue(true));

        $accountChangeManager->expects($this->any())
            ->method('getTotalUpgradeCost')
            ->will($this->returnValue(0));

        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array()
        );

        $currency = new I18n_Currency('gbp', 0);
        $nextInvoiceDate = I18n_Date::fromString($coreService->getNextInvoiceDate());

        $nextInvoiceDate->fShort() == I18n_Date::now()->fShort();

        $oldLineCheck = array(
            'fttcDownSpeed' => null,
            'fttcUpSpeed'   => null
        );

        return array(
            //Live appointing has worked
            array(
                '$dataArray'           => array(
                    'objCoreService'           => $coreService,
                    'appointingType'           => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'              => $appointment,
                    'appointmentdate1'         => null,
                    'appointmenttime1'         => null,
                    'appointmentdate2'         => null,
                    'appointmenttime2'         => null,
                    'appointmentdate3'         => null,
                    'appointmenttime3'         => null,
                    'objAccountChangeManager'  => $accountChangeManager,
                    'arrProductConfigurations' => $productConfigurations,
                    'objLineCheckResult'       => $lineCheckResult,
                    'intDiscountLength'        => 0
                ),
                'expectedCompleteData' => array(
                    'bolFibreToFibre'          => true,
                    'completePage'             => true,
                    'preChangeLineCheckResult' => $oldLineCheck,
                    'fibreAppointments'        => array(
                        'hasInstallationAppointment'  => true,
                        'alternativeAppointmentDates' => false,
                        'appointmentDate'             => '15/07/2012',
                        'appointmentTime'             => 'AM'
                    ),
                    'bolPaymentTaken'          => false,
                    'intNewMonthlyCost'        => $currency,
                    'intAmountTaken'           => $currency,
                    'uxtNextInvoiceDate'       => $nextInvoiceDate,
                    'objOngoingProductCost'    => $currency,
                    'objDiscountedProductCost' => null,
                    'intDiscountLength'        => 0,
                    'bolAppointmentBooked'     => true,
                    'bolAppointmentRequired'   => true,
                    'bolIsFibre'               => true
                )
            ),
            //Live appointing has not worked
            array(
                '$dataArray'           => array(
                    'objCoreService'           => $coreService,
                    'appointingType'           => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'              => null,
                    'appointmentdate1'         => $appointmentDate1->getTimestamp(),
                    'appointmenttime1'         => $appointmentTime1,
                    'appointmentdate2'         => $appointmentDate2->getTimestamp(),
                    'appointmenttime2'         => $appointmentTime2,
                    'appointmentdate3'         => $appointmentDate3->getTimestamp(),
                    'appointmenttime3'         => $appointmentTime3,
                    'objAccountChangeManager'  => $accountChangeManager,
                    'arrProductConfigurations' => $productConfigurations,
                    'objLineCheckResult'       => $lineCheckResult,
                    'intDiscountLength'        => 0
                ),
                'expectedCompleteData' => array(
                    'bolFibreToFibre'          => true,
                    'completePage'             => true,
                    'preChangeLineCheckResult' => $oldLineCheck,
                    'fibreAppointments'        => array(
                        'hasInstallationAppointment'  => true,
                        'alternativeAppointmentDates' => true,
                        'appointmentDate1'            => '15/07/2012',
                        'appointmentTime1'            => 'AM',
                        'appointmentDate2'            => '15/07/2012',
                        'appointmentTime2'            => 'PM',
                        'appointmentDate3'            => '16/07/2012',
                        'appointmentTime3'            => 'AM'
                    ),
                    'bolPaymentTaken'          => false,
                    'intNewMonthlyCost'        => $currency,
                    'intAmountTaken'           => $currency,
                    'uxtNextInvoiceDate'       => $nextInvoiceDate,
                    'objOngoingProductCost'    => $currency,
                    'objDiscountedProductCost' => null,
                    'intDiscountLength'        => 0,
                    'bolAppointmentBooked'     => true,
                    'bolAppointmentRequired'   => true,
                    'bolIsFibre'               => true
                )
            ),
            //Without any appointment dates
            array(
                '$dataArray'           => array(
                    'objCoreService'           => $coreService,
                    'appointingType'           => array(
                        'serviceHandle' => 'FTTC'
                    ),
                    'appointment'              => null,
                    'appointmentdate1'         => null,
                    'appointmenttime1'         => null,
                    'appointmentdate2'         => null,
                    'appointmenttime2'         => null,
                    'appointmentdate3'         => null,
                    'appointmenttime3'         => null,
                    'objAccountChangeManager'  => $accountChangeManager,
                    'arrProductConfigurations' => $productConfigurations,
                    'objLineCheckResult'       => $lineCheckResult,
                    'intDiscountLength'        => 0
                ),
                'expectedCompleteData' => array(
                    'bolFibreToFibre'          => true,
                    'completePage'             => true,
                    'preChangeLineCheckResult' => $oldLineCheck,
                    'fibreAppointments'        => array(
                        'hasInstallationAppointment' => false
                    ),
                    'bolPaymentTaken'          => false,
                    'intNewMonthlyCost'        => $currency,
                    'intAmountTaken'           => $currency,
                    'uxtNextInvoiceDate'       => $nextInvoiceDate,
                    'objOngoingProductCost'    => $currency,
                    'objDiscountedProductCost' => null,
                    'intDiscountLength'        => 0,
                    'bolAppointmentBooked'     => false,
                    'bolAppointmentRequired'   => false,
                    'bolIsFibre'               => true
                )
            ),
        );
    }

    /**
     * Tests canUserPerformInstantAccountChange success case
     *
     * @covers AccountChange_Controller::canUserPerformInstantAccountChange
     *
     * @return void
     */
    public function testcanUserPerformInstantAccountChangeSuccess()
    {
        $mockController = $this->getMock(
            'AccountChange_Controller',
            array('getCurrentBusinessActor', 'isUserMemberOfPhplibGroup'),
            array()
        );

        $mockUser = $this->getMock(
            'Auth_BusinessActor',
            array(
                'getUserType', 'getExternalUserId'
            ),
            array()
        );

        $mockController->expects($this->once())
            ->method('getCurrentBusinessActor')
            ->will($this->returnValue($mockUser));

        $mockController->expects($this->once())
            ->method('isUserMemberOfPhplibGroup')
            ->with('mock_user', 'Instant Account Change')
            ->will($this->returnValue(true));

        $mockUser->expects($this->once())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_STAFF'));

        $mockUser->expects($this->once())
            ->method('getExternalUserId')
            ->will($this->returnValue('mock_user'));

        $this->assertTrue($mockController->canUserPerformInstantAccountChange());
    }

    /**
     * Tests canUserPerformInstantAccountChange failure case
     *
     * @covers AccountChange_Controller::canUserPerformInstantAccountChange
     *
     * @return void
     */
    public function testcanUserPerformInstantAccountChangeFailure()
    {
        $mockController = $this->getMock(
            'AccountChange_Controller',
            array('getCurrentBusinessActor'),
            array()
        );

        $mockUser = $this->getMock(
            'Auth_BusinessActor',
            array(
                'getUserType'
            ),
            array()
        );

        $mockController->expects($this->once())
            ->method('getCurrentBusinessActor')
            ->will($this->returnValue($mockUser));

        $mockUser->expects($this->once())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_ENDUSER'));

        $this->assertFalse($mockController->canUserPerformInstantAccountChange());
    }

    /**
     * Tests init with instant account change where user does not have permission
     *
     * @covers AccountChange_Controller::init
     *
     * @return void
     */
    public function testInitInstantAccountChangeDenied()
    {
        $mockUser = $this->getMock(
            'Auth_BusinessActor',
            array(
                'getUserType'
            ),
            array()
        );

        $mockUser->expects($this->once())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_STAFF'));

        $mockController = $this->getMock(
            'AccountChange_Controller',
            array(
                'setRestrictedValidationActions',
                'getUserBusinessActor',
                'getCurrentBusinessActor',
                'canUserPerformInstantAccountChange',
                'getProvisionedService',
                'getPriceIncreasePercent'
            ),
            array()
        );

        $mockController->expects($this->any())
            ->method('getPriceIncreasePercent')
            ->will($this->returnValue(1));

        $mockController->expects($this->once())
            ->method('setRestrictedValidationActions');

        $mockController->expects($this->once())
            ->method('getCurrentBusinessActor')
            ->will($this->returnValue($mockUser));

        $mockController->expects($this->once())
            ->method('getUserBusinessActor');

//        $mockController->expects($this->once())
//            ->method('getCurrentBusinessActor');

        $mockController->expects($this->once())
            ->method('canUserPerformInstantAccountChange')
            ->will($this->returnValue(false));

        $mockController->expects($this->any())
            ->method('getProvisionedService')
            ->will($this->returnValue(''));

        $returnValue = $mockController->init(false, false, false, false);

        $this->assertEquals($returnValue['arrData']['reasonForBlockage'], 'instantChangePermissionDenied');
    }

    /**
     * Test getProductsWithDiscount returns extra info about discount if valid for the product
     *
     * @param array   $products                   Array of products
     * @param string  $promoCode                  Promo code
     * @param boolean $isPromoCodeValidForProduct Is promo code valid for product
     * @param array   $discountDetails            Details of discount
     *
     * @covers       AccountChange_Controller::getProductsWithDiscount
     *
     * @dataProvider provideDataToTestGetProductsWithDiscount
     *
     * @return void
     */
    public function testGetProductsWithDiscount($products, $promoCode, $isPromoCodeValidForProduct, $discountDetails)
    {
        // set expected return
        $arrExpectedReturn = $products;

        if (true === $isPromoCodeValidForProduct) {
            // if the promoCode is valid for the product
            // expect presetDiscount only for the first product in the array
            // to prove that the list contains both discounted and non-discounted products
            $arrExpectedReturn[0]['presetDiscount']   = $discountDetails;
            $arrExpectedReturn[0]['currentBasePrice'] = new I18n_Currency('gbp', 0.00);
            $arrExpectedReturn[0]['discountAmount']   = new I18n_Currency('gbp', 17.99);
        }

        // mock Val DB adaptor
        $objMockValDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'isPromoCodeValidForProduct',
                'isPromoCodeValidForIp',
                'getServiceDefinition',
                'getServiceComponentConfigByProductID'
            ),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockValDbAdaptor->expects($this->any())
            ->method('isPromoCodeValidForProduct')
            // set promoCode valid for only the first product, if at all
            ->will($this->onConsecutiveCalls((true === $isPromoCodeValidForProduct) ? 1 : 0), 0);

        $objMockValDbAdaptor->expects($this->any())
            ->method('isPromoCodeValidForIp')
            ->will($this->returnValue(1));

        $objMockValDbAdaptor->expects($this->any())
            ->method('getServiceDefinition')
            ->will(
                $this->returnValue(
                    array(
                        'intProductID'                => 6754,
                        'strName'                     => 'Extra',
                        'strISP'                      => 'plus.net',
                        'strRequires'                 => 'adsl',
                        'strType'                     => 'residential',
                        'strPasswordVisibleToSupport' => 'Y',
                        'strSignupViaPortal'          => 'Y',
                        'intBTProductID'              => null,
                        'strBlurb'                    => '',
                        'floMinimumCharge'            => 11.4900,
                        'floInitialCharge'            => 0.00,
                        'uxtDateCreated'              => **********,
                        'uxtEndDate'                  => null,
                        'strVariantHandle'            => 'EXTRA',
                        'strFamilyHandle'             => 'VALUE',
                    )
                )
            );

        $objMockValDbAdaptor->expects($this->any())
            ->method('getServiceComponentConfigByProductID')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Val', $objMockValDbAdaptor);

        // mock AccountChange DB adaptor
        $objMockAccountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getInitialPresetDiscountId', 'getPresetDiscountTemplate'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getInitialPresetDiscountId')
            ->will(
                $this->returnValue(array('intPresetDiscountId' => 108))
            );

        $objMockAccountChangeDbAdaptor->expects($this->any())
            ->method('getPresetDiscountTemplate')
            ->will($this->returnValue($discountDetails));

        Db_Manager::setAdaptor('AccountChange', $objMockAccountChangeDbAdaptor);

        $arrActualProducts = AccountChange_Controller::getProductsWithDiscount($products, $promoCode);

        $this->assertEquals($arrExpectedReturn, $arrActualProducts);
    }

    /**
     * Provide data to test getProductsWithDiscount
     *
     * @return array
     */
    public function provideDataToTestGetProductsWithDiscount()
    {
        $promoCode = 'salesoffer';

        $products = array(
            array(
                'intSdi'                   => 6754,
                'strProductName'           => 'Extra',
                'intTariffID'              => 522,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intProductCost'           => new I18n_Currency('gbp', 17.99),
                'objProductDiscountedCost' => null,
                'costLevel'                => null,
                'provisioningProfile'      => '512k+ 50:1 SI',
                'isFibre'                  => null,
                'maxDownloadSpeed'         => 8,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'strVariantHandle'         => '',
                'strFamilyHandle'          => 'VALUE',
                'currentBasePrice'         => new I18n_Currency('gbp', 17.99)
            ),
            array(
                'intSdi'                   => 6768,
                'strProductName'           => 'Plusnet Value Fibre',
                'intTariffID'              => 979,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intProductCost'           => new I18n_Currency('gbp', 22.99),
                'objProductDiscountedCost' => null,
                'costLevel'                => null,
                'provisioningProfile'      => 'FTTC',
                'isFibre'                  => 1,
                'maxDownloadSpeed'         => 4,
                'maxUploadSpeed'           => 4,
                'activationFee'            => 25.00,
                'strVariantHandle'         => '',
                'strFamilyHandle'          => 'VALUE',
                'currentBasePrice'         => new I18n_Currency('gbp', 22.99),
            ),
        );

        $discountDetails = array(
            'intPresetDiscountId'     => 108,
            'intNextPresetDiscountId' => 0,
            'intDiscountLength'       => 3,
            'intDiscountTypeId'       => 2,
            'vchDescription'          => 'Sales offer 3 months free',
            'vchEmailHandle'          => 'promo_confirmati',
            'vchCreatedBy'            => 954481,
            'decValue'                => 100.00,
            'stmLastUpdate'           => '2011-08-15 10:58:11'
        );

        return array(
            // promoCode valid for the products
            array($products, $promoCode, true, $discountDetails),
            // promoCode invalid for the products
            array($products, $promoCode, false, $discountDetails),
        );
    }

    /**
     * Provide data to test that getProducts filters products based on LTC-980 and LTC-976
     *
     * @return array
     */
    public function provideDataToTestGetProductsForLtc()
    {
        $mockProductFamily = $this->getMock(
            'ProductFamily_Res2012',
            array('isDualPlay'),
            array(),
            '',
            false
        );

        $mockProductFamily
            ->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $plusnetExtraProductFamily = $this->getMock(
            'ProductFamily_Generic',
            array('getProductFamilyHandle', 'getVariantHandle', 'hasAutoContracts', 'isDualPlay'),
            array(),
            '',
            false
        );

        $plusnetExtraProductFamily->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue('VALUE'));

        $plusnetExtraProductFamily->expects($this->any())
            ->method('getVariantHandle')
            ->will($this->returnValue('EXTRA'));

        $plusnetExtraProductFamily->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));

        $plusnetExtraProductFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        // Plusnet Extra
        $plusnetExtraProduct = array(
            'service_definition_id' => '6754',
            'name'                  => 'Extra',
            'vchDisplayName'        => 'Extra',
            'vchDescription'        => 'Extra',
            'intTariffID'           => null,
            'vchContract'           => '',
            'minimum_charge'        => 11.49,
            'provisioningProfile'   => '512k+ 50:1 SI',
            'intMaximumSpeed'       => 24000,
            'intMaxUploadSpeed'     => null,
            'activationFee'         => 0.0,
            'signup_via_portal'     => 1
        );

        // Plusnet Extra Expected
        $plusnetExtraProductExpected = array(
            'intSdi'                   => '6754',
            'strProductName'           => 'Extra',
            'intTariffID'              => null,
            'strContract'              => '',
            'strContractLength'        => 'Extra',
            'intProductCost'           => new I18n_Currency('gbp', 11.49),
            'costLevel'                => 'Extra',
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'isDual'                   => true,
            'intContractLengthMonths'  => 12,
            'defaultContractLength'    => '12MONTH',
            'maxUploadSpeed'           => null,
            'objProductDiscountedCost' => null,
            'activationFee'            => null,
            'maxDownloadSpeed'         => 24,
            'productFamily'            => $plusnetExtraProductFamily
        );

        // Plusnet Essentials (Contracted)
        $plusnetEssentialsContractedProduct = array(
            'service_definition_id' => '6785',
            'name'                  => 'Plusnet Essentials (Contracted)',
            'vchDisplayName'        => 'Plusnet Essentials (Contracted)',
            'vchDescription'        => 'Plusnet Essentials (Contracted)',
            'intTariffID'           => null,
            'vchContract'           => 'Monthly',
            'minimum_charge'        => 5.99,
            'provisioningProfile'   => '512k+ 50:1 SI',
            'intMaximumSpeed'       => 21000,
            'intMaxUploadSpeed'     => null,
            'activationFee'         => 0.0,
            'signup_via_portal'     => 1
        );

        $plusnetEssentialsContractedProductFamily = $this->getMock(
            'ProductFamily_Res2012',
            array('getProductFamilyHandle', 'getVariantHandle', 'hasAutoContracts', 'isDualPlay'),
            array(),
            '',
            false
        );

        $plusnetEssentialsContractedProductFamily->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue('PNRESRPR12'));

        $plusnetEssentialsContractedProductFamily->expects($this->any())
            ->method('getVariantHandle')
            ->will($this->returnValue('ESSEN_CON'));

        $plusnetEssentialsContractedProductFamily->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(true));

        $plusnetEssentialsContractedProductFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        // Plusnet Essentials (Contracted) Expected
        $plusnetEssentialsContractedProductExpected = array(
            'intSdi'                   => '6785',
            'strProductName'           => 'Plusnet Essentials (Contracted)',
            'intTariffID'              => null,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Plusnet Essentials (Contracted)',
            'intProductCost'           => new I18n_Currency('gbp', 5.99),
            'costLevel'                => 'Plusnet Essentials (Contracted)',
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'isDual'                   => true,
            'intContractLengthMonths'  => 12,
            'defaultContractLength'    => '12MONTH',
            'maxUploadSpeed'           => null,
            'objProductDiscountedCost' => null,
            'activationFee'            => null,
            'maxDownloadSpeed'         => 21,
            'productFamily'            => $plusnetEssentialsContractedProductFamily
        );

        // Plusnet Unlimited (No Contract)
        $plusnetUnlimitedNonContractedProduct = array(
            'service_definition_id' => '6788',
            'name'                  => 'Plusnet Unlimited (No Contract)',
            'vchDisplayName'        => 'Plusnet Unlimited (No Contract)',
            'vchDescription'        => 'Plusnet Unlimited (No Contract)',
            'intTariffID'           => null,
            'vchContract'           => 'Monthly',
            'minimum_charge'        => 9.99,
            'provisioningProfile'   => '512k+ 50:1 SI',
            'intMaximumSpeed'       => 21000,
            'intMaxUploadSpeed'     => null,
            'activationFee'         => 0.0,
            'signup_via_portal'     => 1
        );

        $plusnetUnlimitedNonContractedProductFamily = $this->getMock(
            'ProductFamily_Generic',
            array('getProductFamilyHandle', 'getVariantHandle', 'hasAutoContracts', 'isDualPlay'),
            array(),
            '',
            false
        );

        $plusnetUnlimitedNonContractedProductFamily->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue('PNRESRPR12'));

        $plusnetUnlimitedNonContractedProductFamily->expects($this->any())
            ->method('getVariantHandle')
            ->will($this->returnValue('ULTD_NOCON'));

        $plusnetUnlimitedNonContractedProductFamily->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));

        $plusnetUnlimitedNonContractedProductFamily->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(false));

        // Plusnet Unlimited (No Contract) Expected
        $plusnetUnlimitedNonContractedProductExpected = array(
            'intSdi'                   => '6788',
            'strProductName'           => 'Plusnet Unlimited (No Contract)',
            'intTariffID'              => null,
            'strContract'              => 'MONTHLY',
            'strContractLength'        => 'Plusnet Unlimited (No Contract)',
            'intProductCost'           => new I18n_Currency('gbp', 9.99),
            'costLevel'                => 'Plusnet Unlimited (No Contract)',
            'provisioningProfile'      => '512k+ 50:1 SI',
            'isFibre'                  => false,
            'isDual'                   => false,
            'intContractLengthMonths'  => 12,
            'defaultContractLength'    => '12MONTH',
            'maxUploadSpeed'           => null,
            'objProductDiscountedCost' => null,
            'activationFee'            => null,
            'maxDownloadSpeed'         => 21,
            'productFamily'            => $plusnetUnlimitedNonContractedProductFamily
        );

        //Current product family
        $currentProductFamilyContracted = $this->getMock(
            'ProductFamily_Generic',
            array('getProductFamilyHandle', 'getVariantHandle', 'hasAutoContracts', 'isDualPlay'),
            array(),
            '',
            false
        );

        $currentProductFamilyContracted->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue('VALUE'));

        $currentProductFamilyContracted->expects($this->any())
            ->method('getVariantHandle')
            ->will($this->returnValue('PREMIUM'));

        $currentProductFamilyContracted->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(true));

        $currentProductFamilyContracted->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        //Current product family
        $currentProductFamilyNonContracted = $this->getMock(
            'ProductFamily_Generic',
            array('getProductFamilyHandle', 'getVariantHandle', 'hasAutoContracts', 'isDualPlay'),
            array(),
            '',
            false
        );

        $currentProductFamilyNonContracted->expects($this->any())
            ->method('getProductFamilyHandle')
            ->will($this->returnValue('VALUE'));

        $currentProductFamilyNonContracted->expects($this->any())
            ->method('getVariantHandle')
            ->will($this->returnValue('PREMIUM'));

        $currentProductFamilyNonContracted->expects($this->any())
            ->method('hasAutoContracts')
            ->will($this->returnValue(false));

        $currentProductFamilyNonContracted->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        return array(
            // Plusnet Value - portal
            array(
                // current product - Plusnet Value
                'serviceDefinitionDetails' => array(
                    'service_definition_id' => '6718',
                    'service_id'            => '0187632',
                    'name'                  => 'Current',
                    'provisioningProfile'   => '512k+ 50:1 SI',
                    'activationFee'         => 0.0,
                    'isp'                   => 'plus.net',
                    'type'                  => 'residential',
                    'signup_via_portal'     => 1
                ),
                'products'                 => array(
                    $plusnetExtraProduct,                           // Plusnet Extra
                    $plusnetEssentialsContractedProduct,            // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProduct,          // Plusnet Unlimited (No Contract)
                ),
                'productFamilies'          => array(
                    $plusnetExtraProductFamily,                     // Plusnet Extra
                    $plusnetEssentialsContractedProductFamily,      // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProductFamily,    // Plusnet Unlimited (No Contract)
                    $currentProductFamilyContracted
                ),
                'expectedResult'           => array(
                    $plusnetEssentialsContractedProductExpected,    // Plusnet Essentials (Contracted)
                    //Current product - Plusnet Value
                    array(
                        'intSdi'                   => '6718',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => '512k+ 50:1 SI',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'isDual'                   => true,
                        'productFamily'            => $currentProductFamilyNonContracted,
                        'objProductDiscountedCost' => null,
                    )
                ),
                'includeAll'               => false,
                'mockProductFamily'        => $mockProductFamily
            ),
            //Plusnet Value - workplace
            array(
                // current product - Plusnet Value
                'serviceDefinitionDetails' => array(
                    'service_definition_id' => '6718',
                    'service_id'            => 187632,
                    'name'                  => 'Current',
                    'provisioningProfile'   => '512k+ 50:1 SI',
                    'activationFee'         => 0.0,
                    'isp'                   => 'plus.net',
                    'type'                  => 'residential',
                    'signup_via_portal'     => 1
                ),
                'products'                 => array(
                    $plusnetExtraProduct,                           // Plusnet Extra
                    $plusnetEssentialsContractedProduct,            // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProduct,          // Plusnet Unlimited (No Contract)
                ),
                'productFamilies'          => array(
                    $plusnetExtraProductFamily,                     // Plusnet Extra
                    $plusnetEssentialsContractedProductFamily,      // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProductFamily,    // Plusnet Unlimited (No Contract)
                    $currentProductFamilyNonContracted
                ),
                'expectedResult'           => array(
                    $plusnetExtraProductExpected,                   // Plusnet Extra
                    $plusnetEssentialsContractedProductExpected,    // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProductExpected,  // Plusnet Unlimited (No Contract)
                    //Current product - Plusnet Value
                    array(
                        'intSdi'                   => '6718',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => '512k+ 50:1 SI',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'isDual'                   => true,
                        'productFamily'            => $currentProductFamilyNonContracted,
                        'objProductDiscountedCost' => null,
                    )
                ),
                'includeAll'               => true,
                'mockProductFamily'        => $currentProductFamilyNonContracted
            ),
            // Plusnet Unlimited (Contracted) - portal
            array(
                // current product - Plusnet Unlimited (Contracted)
                'serviceDefinitionDetails' => array(
                    'service_definition_id' => '6787',
                    'service_id'            => 187632,
                    'name'                  => 'Current',
                    'provisioningProfile'   => '512k+ 50:1 SI',
                    'activationFee'         => 0.0,
                    'isp'                   => 'plus.net',
                    'type'                  => 'residential',
                    'signup_via_portal'     => 1
                ),
                'products'                 => array(
                    $plusnetExtraProduct,                           // Plusnet Extra
                    $plusnetEssentialsContractedProduct,            // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProduct,          // Plusnet Unlimited (No Contract)
                ),
                'productFamilies'          => array(
                    // current product - Plusnet Unlimited (Contracted)
                    $plusnetExtraProductFamily,                     // Plusnet Extra
                    $plusnetEssentialsContractedProductFamily,      // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProductFamily,    // Plusnet Unlimited (No Contract)
                    $currentProductFamilyContracted
                ),
                'expectedResult'           => array(
                    $plusnetEssentialsContractedProductExpected,    // Plusnet Essentials (Contracted)
                    //Current product - Plusnet Unlimited (Contracted)
                    array(
                        'intSdi'                   => '6787',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => '512k+ 50:1 SI',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'isDual'                   => true,
                        'productFamily'            => $currentProductFamilyContracted,
                        'objProductDiscountedCost' => null,
                    )
                ),
                'includeAll'               => false,
                'mockProductFamily'        => $mockProductFamily
            ),
            // Plusnet Unlimited (Contracted) - workplace
            array(
                // current product - Plusnet Unlimited (Contracted)
                'serviceDefinitionDetails' => array(
                    'service_definition_id' => '6787',
                    'service_id'            => 87632,
                    'name'                  => 'Current',
                    'provisioningProfile'   => '512k+ 50:1 SI',
                    'activationFee'         => 0.0,
                    'isp'                   => 'plus.net',
                    'type'                  => 'residential',
                    'signup_via_portal'     => 1
                ),
                'products'                 => array(
                    $plusnetExtraProduct,                           // Plusnet Extra
                    $plusnetEssentialsContractedProduct,            // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProduct,          // Plusnet Unlimited (No Contract)
                ),
                'productFamilies'          => array(
                    // current product - Plusnet Unlimited (Contracted)
                    $plusnetExtraProductFamily,                     // Plusnet Extra
                    $plusnetEssentialsContractedProductFamily,      // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProductFamily,    // Plusnet Unlimited (No Contract)
                    $currentProductFamilyContracted
                ),
                'expectedResult'           => array(
                    $plusnetEssentialsContractedProductExpected,    // Plusnet Essentials (Contracted)
                    //Current product - Plusnet Unlimited (Contracted)
                    array(
                        'intSdi'                   => '6787',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => '512k+ 50:1 SI',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'isDual'                   => true,
                        'productFamily'            => $currentProductFamilyContracted,
                        'objProductDiscountedCost' => null,
                    )
                ),
                'includeAll'               => true,
                'mockProductFamily'        => $mockProductFamily
            ),
            //            // Plusnet Essentials (No Contract) - portal
            array(
                // current product - Plusnet Essentials (No Contract)
                'serviceDefinitionDetails' => array(
                    'service_definition_id' => '6786',
                    'service_id'            => 187632,
                    'name'                  => 'Current',
                    'provisioningProfile'   => '512k+ 50:1 SI',
                    'activationFee'         => 0.0,
                    'isp'                   => 'plus.net',
                    'type'                  => 'residential',
                    'signup_via_portal'     => 1
                ),
                'products'                 => array(
                    $plusnetExtraProduct,                           // Plusnet Extra
                    $plusnetEssentialsContractedProduct,            // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProduct,          // Plusnet Unlimited (No Contract)
                ),
                'productFamilies'          => array(
                    // current product - Plusnet Essentials (No Contract)
                    $plusnetExtraProductFamily,                     // Plusnet Extra
                    $plusnetEssentialsContractedProductFamily,      // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProductFamily,    // Plusnet Unlimited (No Contract)
                    $currentProductFamilyNonContracted
                ),
                'expectedResult'           => array(
                    $plusnetEssentialsContractedProductExpected,    // Plusnet Essentials (Contracted)
                    //Current product - Plusnet Essentials (No Contract)
                    array(
                        'intSdi'                   => '6786',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => '512k+ 50:1 SI',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'isDual'                   => true,
                        'productFamily'            => $currentProductFamilyNonContracted,
                        'objProductDiscountedCost' => null,
                    )
                ),
                'includeAll'               => false,
                'mockProductFamily'        => $mockProductFamily
            ),
            // Plusnet Essentials (No Contract) - workplace
            array(
                // current product - Plusnet Essentials (No Contract)
                'serviceDefinitionDetails' => array(
                    'service_definition_id' => '6786',
                    'service_id'            => 187632,
                    'name'                  => 'Current',
                    'provisioningProfile'   => '512k+ 50:1 SI',
                    'activationFee'         => 0.0,
                    'isp'                   => 'plus.net',
                    'type'                  => 'residential',
                    'signup_via_portal'     => 1
                ),
                'products'                 => array(
                    $plusnetExtraProduct,                           // Plusnet Extra
                    $plusnetEssentialsContractedProduct,            // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProduct,          // Plusnet Unlimited (No Contract)
                ),
                'productFamilies'          => array(
                    // current product - Plusnet Essentials (No Contract)
                    $plusnetExtraProductFamily,                     // Plusnet Extra
                    $plusnetEssentialsContractedProductFamily,      // Plusnet Essentials (Contracted)
                    $plusnetUnlimitedNonContractedProductFamily,    // Plusnet Unlimited (No Contract)
                    $currentProductFamilyContracted
                ),
                'expectedResult'           => array(
                    $plusnetEssentialsContractedProductExpected,    // Plusnet Essentials (Contracted)
                    //Current product - Plusnet Essentials (No Contract)
                    array(
                        'intSdi'                   => '6786',
                        'strProductName'           => 'Current',
                        'intTariffID'              => null,
                        'strContract'              => null,
                        'strContractLength'        => null,
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'provisioningProfile'      => '512k+ 50:1 SI',
                        'objProductDiscountedCost' => null,
                        'intCostIncVatPence'       => null,
                        'isFibre'                  => false,
                        'maxDownloadSpeed'         => 40,
                        'maxUploadSpeed'           => null,
                        'activationFee'            => null,
                        'isDual'                   => true,
                        'productFamily'            => $currentProductFamilyContracted,
                        'objProductDiscountedCost' => null,
                    )
                ),
                'includeAll'               => true,
                'mockProductFamily'        => $mockProductFamily
            ),
        );
    }

    /**
     * Data provider for testGetProductsUsesSupplierProductRules
     *
     * @return array
     */
    public function providerGetProductsUsesSupplierProductRules()
    {
        $mockProductFamily = $this->getMock(
            'ProductFamily_Res2012',
            array('isDualPlay'),
            array(),
            '',
            false
        );

        $mockProductFamily
            ->expects($this->any())
            ->method('isDualPlay')
            ->will($this->returnValue(true));

        $existingProduct = array(
            'service_definition_id' => '1',
            'service_id'            => 123,
            'name'                  => 'Current product',
            'provisioningProfile'   => '',
            'activationFee'         => 0.0,
            'isp'                   => 'plus.net',
            'type'                  => 'residential',
            'signup_via_portal'     => 1
        );

        $existingProductExpected = array(
            'intSdi'                   => '1',
            'strProductName'           => 'Current product',
            'intTariffID'              => null,
            'strContract'              => null,
            'strContractLength'        => null,
            'intProductCost'           => new I18n_Currency('gbp', 0),
            'provisioningProfile'      => '',
            'objProductDiscountedCost' => null,
            'intCostIncVatPence'       => null,
            'isFibre'                  => false,
            'maxDownloadSpeed'         => 3,
            'maxUploadSpeed'           => null,
            'activationFee'            => null,
            'productFamily'            => $mockProductFamily,
            'isDual'                   => true,
            'objProductDiscountedCost' => null,
        );

        $newProduct = array(
            'service_definition_id' => '2',
            'name'                  => 'New product',
            'vchDisplayName'        => 'New product',
            'vchDescription'        => 'New product',
            'intTariffID'           => null,
            'vchContract'           => '',
            'minimum_charge'        => 0,
            'provisioningProfile'   => '',
            'intMaximumSpeed'       => 40000,
            'intMaxUploadSpeed'     => 10000,
            'activationFee'         => 0.0,
            'signup_via_portal'     => 1
        );

        $legacyProduct = array(
            'service_definition_id' => '3',
            'name'                  => 'Legacy product',
            'vchDisplayName'        => 'Legacy product',
            'vchDescription'        => 'Legacy product',
            'intTariffID'           => null,
            'vchContract'           => '',
            'minimum_charge'        => 0,
            'provisioningProfile'   => '',
            'intMaximumSpeed'       => 8000,
            'intMaxUploadSpeed'     => 1000,
            'activationFee'         => 0.0,
            'signup_via_portal'     => 0
        );

        return array(
            array(
                'existingProduct'   => $existingProduct,
                'products'          => array(
                    $newProduct,
                    $legacyProduct
                ),
                'lineCheckData'     => array(
                    'isWbcEnabled'     => true,
                    'accessTechnology' => 'FTTC'
                ),
                'expectedResult'    => array(
                    array(
                        'intSdi'                   => '2',
                        'strProductName'           => 'New product',
                        'intTariffID'              => null,
                        'strContract'              => '',
                        'strContractLength'        => 'New product',
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'costLevel'                => 'New product',
                        'provisioningProfile'      => '',
                        'isFibre'                  => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxUploadSpeed'           => 10,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'maxDownloadSpeed'         => 40,
                        'productFamily'            => $mockProductFamily,
                        'isDual'                   => true,
                    ),
                    array(
                        'intSdi'                   => '3',
                        'strProductName'           => 'Legacy product',
                        'intTariffID'              => null,
                        'strContract'              => '',
                        'strContractLength'        => 'Legacy product',
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'costLevel'                => 'Legacy product',
                        'provisioningProfile'      => '',
                        'isFibre'                  => false,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'activationFee'            => null,
                        'productFamily'            => $mockProductFamily,
                        'maxDownloadSpeed'         => 8,
                    ),
                    $existingProductExpected
                ),
                'mockProductFamily' => $mockProductFamily
            ),
            array(
                'existingProduct'   => $existingProduct,
                'products'          => array(
                    $newProduct,
                    $legacyProduct
                ),
                'lineCheckData'     => array(
                    'isWbcEnabled'     => true,
                    'accessTechnology' => 'ADSL2+'
                ),
                'expectedResult'    => array(
                    array(
                        'intSdi'                   => '2',
                        'strProductName'           => 'New product',
                        'intTariffID'              => null,
                        'strContract'              => '',
                        'strContractLength'        => 'New product',
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'costLevel'                => 'New product',
                        'provisioningProfile'      => '',
                        'isFibre'                  => false,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'maxDownloadSpeed'         => 15,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '3',
                        'strProductName'           => 'Legacy product',
                        'intTariffID'              => null,
                        'strContract'              => '',
                        'strContractLength'        => 'Legacy product',
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'costLevel'                => 'Legacy product',
                        'provisioningProfile'      => '',
                        'isFibre'                  => false,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'maxDownloadSpeed'         => 8,
                        'productFamily'            => $mockProductFamily
                    ),
                    $existingProductExpected
                ),
                'mockProductFamily' => $mockProductFamily
            ),
            array(
                'existingProduct'   => $existingProduct,
                'products'          => array(
                    $newProduct,
                    $legacyProduct
                ),
                'lineCheckData'     => array(
                    'isWbcEnabled'     => false,
                    'accessTechnology' => 'ADSL1'
                ),
                'expectedResult'    => array(
                    array(
                        'intSdi'                   => '2',
                        'strProductName'           => 'New product',
                        'intTariffID'              => null,
                        'strContract'              => '',
                        'strContractLength'        => 'New product',
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'costLevel'                => 'New product',
                        'provisioningProfile'      => '',
                        'isFibre'                  => false,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'maxDownloadSpeed'         => 3,
                        'productFamily'            => $mockProductFamily
                    ),
                    array(
                        'intSdi'                   => '3',
                        'strProductName'           => 'Legacy product',
                        'intTariffID'              => null,
                        'strContract'              => '',
                        'strContractLength'        => 'Legacy product',
                        'intProductCost'           => new I18n_Currency('gbp', 0),
                        'costLevel'                => 'Legacy product',
                        'provisioningProfile'      => '',
                        'isFibre'                  => false,
                        'isDual'                   => true,
                        'intContractLengthMonths'  => 12,
                        'defaultContractLength'    => '12MONTH',
                        'maxUploadSpeed'           => null,
                        'objProductDiscountedCost' => null,
                        'activationFee'            => null,
                        'maxDownloadSpeed'         => 3,
                        'productFamily'            => $mockProductFamily
                    ),
                    $existingProductExpected
                ),
                'mockProductFamily' => $mockProductFamily
            )
        );
    }

    /**
     * Test getExistingBroadbandContractData when contracts not returned
     *
     * @param array  $contracts              Active contracts
     * @param array  $expiredContracts       Expired contracts
     * @param string $productFamilyClassName Product family class name
     * @param array  $expected               Expected result
     *
     * @dataProvider provideDataToTestGetExistingBroadbandContractData
     *
     * @covers       AccountChange_Controller::getExistingBroadbandContractData
     * @return void
     */
    public function testGetExistingBroadbandContractData(
        $contracts,
        $expiredContracts,
        $productFamilyClassName,
        $expected
    ) {
        // Mock \Plusnet\ContractsClient\Client
        $contractsClient = $this->getMock(
            'Plusnet\\ContractsClient\\Client',
            array('getContracts'),
            array(),
            "",
            false
        );

        if ($expected['hasAutoContracts']) {
            $contractsClient->expects($this->at(0))
                ->method('getContracts')
                ->will($this->returnValue($contracts));

            if (!empty($expiredContracts)) {
                $contractsClient->expects($this->at(1))
                    ->method('getContracts')
                    ->will($this->returnValue($expiredContracts));
            }
        }

        // Mock ProductFamily_Res2012
        $productFamily = $this->getMock(
            $productFamilyClassName,
            array('__construct', 'hasAutoContracts'),
            array(),
            '',
            false
        );

        $productFamily->expects($this->once())
            ->method('hasAutoContracts')
            ->will($this->returnValue($expected['hasAutoContracts']));

        $objController = $this->getMock(
            'AccountChange_Controller',
            array('getValidationCheck', 'getProvisionedProduct', 'matchAddress', 'getAdslComponentExists'),
            array()
        );

        $returned = $objController->getExistingBroadbandContractData(
            123,
            456,
            $contractsClient,
            $productFamily
        );

        // should return default value from the config file
        $this->assertEquals($expected, $returned);
    }

    /**
     * Provide data to test getExistingBroadbandContractData
     *
     * @return array
     */
    public function provideDataToTestGetExistingBroadbandContractData()
    {
        // set up first contract
        $contract1 = new \Plusnet\ContractsClient\Entity\Contract();

        $contract1->setId(1);
        $contract1->setStatus('ACTIVE');
        // 12 months duration
        $contract1->setDuration(12, Plusnet\ContractsClient\Entity\DurationUnit::MONTH);
        // endDate in 8 months 1 day in future
        $contract1EndDate = new DateTime();
        $contract1EndDate->add(new DateInterval('P8M1D'));
        $contract1->setEndDate($contract1EndDate->format('d-m-Y'));

        // set up second contract
        $contract2 = new \Plusnet\ContractsClient\Entity\Contract();
        $contract2->setId(2);
        $contract2->setStatus('ACTIVE');
        // 24 months duration
        $contract2->setDuration(24, Plusnet\ContractsClient\Entity\DurationUnit::MONTH);
        // endDate in 15 months and 1 day in future
        $contract2EndDate = new DateTime();
        $contract2EndDate->add(new DateInterval('P15M1D'));
        $contract2->setEndDate($contract2EndDate->format('d-m-Y'));

        // set up expired contract
        $contract3 = new \Plusnet\ContractsClient\Entity\Contract();
        $contract3->setId(1);
        $contract3->setStatus('OUT_OF_CONTRACT');
        $contract3->setStartDate('2011-10-21');
        $contract3->setEndDate('2012-10-20');

        return array(
            // old product
            array(
                null,
                null,
                'ProductFamily_Value',
                array(
                    'hasAutoContracts'    => false,
                    'isContracted'        => false,
                    'contractDuration'    => 0,
                    'remainingTime'       => 0,
                    'hasExpiredContracts' => false,
                )
            ),
            // new product without active contract
            array(
                null,
                null,
                'ProductFamily_Res2012',
                array(
                    'hasAutoContracts'    => true,
                    'isContracted'        => false,
                    'contractDuration'    => 0,
                    'remainingTime'       => 0,
                    'hasExpiredContracts' => false,
                )
            ),
            // new product with one active contract
            array(
                array($contract1),
                null,
                'ProductFamily_Res2012',
                array(
                    'hasAutoContracts'    => true,
                    'isContracted'        => true,
                    'contractDuration'    => 12,
                    'remainingTime'       => 8,
                    'hasExpiredContracts' => false,
                )
            ),
            // new product with two active contracts
            array(
                array($contract1, $contract2),
                null,
                'ProductFamily_Res2012',
                array(
                    'hasAutoContracts'    => true,
                    'isContracted'        => true,
                    'contractDuration'    => 24,
                    'remainingTime'       => 15,
                    'hasExpiredContracts' => false,
                )
            ),
            // new product with expired contract and no active contracts
            array(
                null,
                array($contract3),
                'ProductFamily_Res2012',
                array(
                    'hasAutoContracts'    => true,
                    'isContracted'        => false,
                    'contractDuration'    => 0,
                    'remainingTime'       => 0,
                    'hasExpiredContracts' => true,
                )
            ),
        );
    }

    /**
     * Test AccountChange_Controller::calculateLeadingPrice works properly
     *
     * @param array         $product              Array of products
     * @param string        $promoCode            Promo code
     * @param array         $discountDetails      Details of discount
     * @param I18n_Currency $expectedLeadingPrice Expected leading (discounted) price
     *
     * @param $expectedDiscountAmount
     * @return void
     * @covers       AccountChange_Controller::getProductsWithDiscount
     * @covers       AccountChange_DiscountHelper::calculateLeadingPrice
     *
     * @dataProvider provideDataToTestCalculateLeadingPriceWorkProperly
     *
     */
    public function testCalculateLeadingPriceWorksProperly(
        $product,
        $promoCode,
        $discountDetails,
        $expectedLeadingPrice,
        $expectedDiscountAmount
    ) {
        $expectedResult = $product;
        $expectedResult[0]['intProductCost'] = $expectedLeadingPrice;
        $expectedResult[0]['presetDiscount'] = $discountDetails;
        $expectedResult[0]['discountAmount'] = $expectedDiscountAmount;

        $valDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'isPromoCodeValidForProduct',
                'isPromoCodeValidForIp',
                'getServiceDefinition',
                'getServiceComponentConfigByProductID'
            ),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $valDbAdaptor->expects($this->any())
            ->method('isPromoCodeValidForProduct')
            ->will($this->onConsecutiveCalls(true));

        $valDbAdaptor->expects($this->any())
            ->method('isPromoCodeValidForIp')
            ->will($this->returnValue(true));

        $valDbAdaptor->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($product[0]));

        $valDbAdaptor->expects($this->any())
            ->method('getServiceComponentConfigByProductID')
            ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Val', $valDbAdaptor);

        $accountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getInitialPresetDiscountId', 'getPresetDiscountTemplate'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $accountChangeDbAdaptor->expects($this->any())
            ->method('getInitialPresetDiscountId')
            ->will($this->returnValue($discountDetails['intPresetDiscountId']));

        $accountChangeDbAdaptor->expects($this->any())
            ->method('getPresetDiscountTemplate')
            ->will($this->returnValue($discountDetails));

        Db_Manager::setAdaptor('AccountChange', $accountChangeDbAdaptor);

        $productWithDiscount = AccountChange_Controller::getProductsWithDiscount($product, $promoCode);

        $this->assertEquals($expectedResult, $productWithDiscount);
    }

    /**
     * Provide data to test AccountChange_Controller::calculateLeadingPrice works properly
     *
     * @return array
     */
    public function provideDataToTestCalculateLeadingPriceWorkProperly()
    {
        $product = array(
            array(
                'intSdi'                   => 6754,
                'strProductName'           => 'Extra',
                'intTariffID'              => 522,
                'strContract'              => 'MONTHLY',
                'strContractLength'        => 'Monthly',
                'intProductCost'           => new I18n_Currency('gbp', 17.99),
                'objProductDiscountedCost' => null,
                'costLevel'                => null,
                'provisioningProfile'      => '512k+ 50:1 SI',
                'isFibre'                  => null,
                'maxDownloadSpeed'         => 8,
                'maxUploadSpeed'           => null,
                'activationFee'            => null,
                'strVariantHandle'         => '',
                'strFamilyHandle'          => 'VALUE'
            )
        );

        return array(
            array(
                'product'                => $product,
                'promotionCode'          => 'fibre5',
                'discountDetails'        => array(
                    'intPresetDiscountId'     => 334,
                    'intNextPresetDiscountId' => 0,
                    'intDiscountLength'       => 12,
                    'intDiscountTypeId'       => AccountChange_DiscountHelper::FIXED_DISCOUNT,
                    'vchDescription'          => 'Offer code discount - Fibre5',
                    'vchEmailHandle'          => 'promo_confirmati',
                    'vchCreatedBy'            => 855862,
                    'decValue'                => 14.99,
                    'stmLastUpdate'           => '2012-10-25 09:23:12'
                ),
                'expectedLeadingPrice'   => new I18n_Currency('gbp', 3.00),
                'expectedDiscountAmount' => new I18n_Currency('gbp', 14.99),
            ),
            array(
                'product'                => $product,
                'promotionCode'          => 'halfoff12',
                'discountDetails'        => array(
                    'intPresetDiscountId'     => 323,
                    'intNextPresetDiscountId' => 0,
                    'intDiscountLength'       => 3,
                    'intDiscountTypeId'       => AccountChange_DiscountHelper::PERCENTAGE_DISCOUNT,
                    'vchDescription'          => 'Offer code discount - halfoff12',
                    'vchEmailHandle'          => 'promo_confirmati',
                    'vchCreatedBy'            => 855862,
                    'decValue'                => 50.00,
                    'stmLastUpdate'           => '2012-12-18 11:34:21'
                ),
                'expectedLeadingPrice'   => new I18n_Currency('gbp', 8.99),
                'expectedDiscountAmount' => new I18n_Currency('gbp', 9.00)
            )
        );
    }

    /**
     * @covers AccountChange_Controller::getHardwareCharges
     */
    public function testGetHardwareChargesReturnsNullIfNoHardwareIsSelected()
    {
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);
        $data = array('hardwareOption' => null);

        $charges = AccountChange_Controller::getHardwareCharges($data);

        $this->assertNull($charges);
    }

    /**
     * @covers AccountChange_Controller::getHardwareCharges
     */
    public function testGetHardwareChargesReturnsNullIfNoAdditionalChargeIsFound()
    {
        $accountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('freePartnerHardwarePostage', 'getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $accountChangeDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        $accountChangeDbAdaptor->expects($this->any())
            ->method('freePartnerHardwarePostage')
            ->will($this->returnValue(0));

        Db_Manager::setAdaptor('AccountChange', $accountChangeDbAdaptor);

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('getServiceId'),
            array(),
            '',
            false
        );

        $objAccountManager->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(0));

        $additionalChargeHelper = $this->getMock(
            'Financial_AdditionalChargeHelper',
            array('getAdditionalCharge')
        );

        $additionalChargeHelper->expects($this->once())
            ->method('getAdditionalCharge')
            ->will($this->returnValue(null));

        AccountChange_Controller::setAdditionalChargeHelper($additionalChargeHelper);

        $data = array(
            'hardwareOption'          => 'TEST_HARDWARE',
            'intNewSdi'               => 6868,
            'objAccountChangeManager' => $objAccountManager
        );
        $charges = AccountChange_Controller::getHardwareCharges($data);

        $this->assertNull($charges);
    }

    /**
     * @covers AccountChange_Controller::getHardwareCharges
     */
    public function testGetHardwareChargesReturnsCorrectCharge()
    {
        $accountChangeDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('freePartnerHardwarePostage', 'getServiceDefinitionDetails'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $accountChangeDbAdaptor->expects($this->any())
            ->method('freePartnerHardwarePostage')
            ->will($this->returnValue(0));

        $accountChangeDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array('strContract' => 'Monthly')));

        Db_Manager::setAdaptor('AccountChange', $accountChangeDbAdaptor);

        $objAccountManager = $this->getMock(
            'AccountChange_Manager',
            array('getServiceId'),
            array(),
            '',
            false
        );

        $objAccountManager->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(0));

        $additionalChargeHelper = $this->getMock(
            'Financial_AdditionalChargeHelper',
            array('getAdditionalCharge')
        );

        $additionalChargeHelper->expects($this->once())
            ->method('getAdditionalCharge')
            ->will(
                $this->returnValue(
                    new Financial_AdditionalCharge('POSTAGE_AND_PACKAGING', 'Postage and Packaging', 699)
                )
            );

        AccountChange_Controller::setAdditionalChargeHelper($additionalChargeHelper);

        $expectedResult = array(
            'description' => 'Postage and Packaging',
            'amount'      => 6.99,
            'gross'       => true
        );

        $data = array(
            'hardwareOption'          => 'TEST_HARDWARE',
            'intNewSdi'               => 6868,
            'objAccountChangeManager' => $objAccountManager
        );
        $charges = AccountChange_Controller::getHardwareCharges($data);

        $this->assertEquals($expectedResult, $charges);
    }

    /**
     * @param array $data Data provider arrat
     *
     * @dataProvider providerForTestGetMinAndMaxSpeedRanges
     */
    public function testGetMinAndMaxSpeedRanges($data)
    {
        $getServiceDefinitionDetailsForService = array(
            'service_definition_id'       => 1,
            'name'                        => $data['productName'],
            'isp'                         => 'plusnet',
            'minimum_charge'              => '10',
            'initial_charge'              => '10',
            'type'                        => '',
            'password_visible_to_support' => '',
            'requires'                    => '',
            'date_created'                => '2008-12-10',
            'end_date'                    => '2009-01-10',
            'signup_via_portal'           => '1',
            'blurb'                       => '',
            'vchContract'                 => ''
        );

        $coreAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDefinitionDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreAdaptor->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue($getServiceDefinitionDetailsForService));
        Db_Manager::setAdaptor('Core', $coreAdaptor);

        $accountChange = $this->getMock(
            'Db_Adaptor',
            array('getSetupFee', 'getServiceDefinitionDetails', 'getProvisionedServiceDetailsForService'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $accountChange->expects($this->any())
            ->method('getSetupFee')
            ->will($this->returnValue(1));
        $accountChange->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array()));
        $accountChange->expects($this->any())
            ->method('getProvisionedServiceDetailsForService')
            ->will($this->returnValue($data['ProvisionedServiceData']));
        Db_Manager::setAdaptor('AccountChange', $accountChange);

        $arrSupplierPlatform = array(
            'intSupplierPlatformId' => 1,
            'intSupplierId'         => 1,
            'strHandle'             => '1',
            'strDisplayName'        => '1'
        );

        $arrSupplier = array(
            'strHandle'      => '1',
            'strDisplayName' => '1'
        );

        $arrSupplierProduct = array(
            array(
                'intSupplierProductId'     => 6868,
                'intSupplierProductTypeId' => 1,
                'strName'                  => '1',
                'strProductCode'           => $data['strProductCode'],
                'intMinDownstream'         => 1,
                'intMaxDownstream'         => 1,
                'intRealMaxDownstream'     => $data['intRealMaxDownstream'],
                'strStability'             => '1',
                'strInterleaving'          => '1',
                'strMaxUpstream'           => '1',
                'strTrafficWeighting'      => '1',
                'strMaintenanceCategory'   => '1',
                'intInternalMaxDownstream' => 0,
                'intInternalMaxUpstream'   => 0
            )
        );

        $productAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getProduct',
                'getSupplierPlatformByHandle',
                'getSupplierData',
                'getProvisionedProfileByServiceDefinitionId',
                'getSupplierProductsByProductCode',
                'getSupplierPlatformBySupplierProductTypeId'
            ),
            array('Product', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $productAdaptor->expects($this->any())
            ->method('getProduct')
            ->will($this->returnValue($data['product']));
        $productAdaptor->expects($this->any())
            ->method('getSupplierPlatformByHandle')
            ->will($this->returnValue($arrSupplierPlatform));
        $productAdaptor->expects($this->any())
            ->method('getSupplierData')
            ->will($this->returnValue($arrSupplier));
        $productAdaptor->expects($this->any())
            ->method('getProvisionedProfileByServiceDefinitionId')
            ->will($this->returnValue($data['strProductCode']));
        $productAdaptor->expects($this->any())
            ->method('getSupplierProductsByProductCode')
            ->will($this->returnValue($arrSupplierProduct));
        $productAdaptor->expects($this->any())
            ->method('getSupplierPlatformBySupplierProductTypeId')
            ->will($this->returnValue($arrSupplierPlatform));
        Db_Manager::setAdaptor('Product', $productAdaptor);


        $lineCheckResult = $this->getMock(
            'LineCheck_Result',
            array(
                'getFttcUncappedDownSpeed',
                'getFttcUncappedUpSpeed',
                'getRangeABottomDownstreamDataBandwidth',
                'getRangeATopDownstreamDataBandwidth',
                'getRangeABottomUpstreamDataBandwidth',
                'getRangeATopUpstreamDataBandwidth',
                'getMaxSpeed',
                'getMaxSpeedRangeMin',
                'getMaxSpeedRangeMax'
            )
        );
        $lineCheckResult->expects($this->any())
            ->method('getFttcUncappedDownSpeed')
            ->will($this->returnValue($data['lineCheckData']['getFttcUncappedDownSpeed']));
        $lineCheckResult->expects($this->any())
            ->method('getFttcUncappedUpSpeed')
            ->will($this->returnValue($data['lineCheckData']['getFttcUncappedUpSpeed']));
        $lineCheckResult->expects($this->any())
            ->method('getRangeABottomDownstreamDataBandwidth')
            ->will($this->returnValue($data['lineCheckData']['getRangeABottomDownstreamDataBandwidth']));
        $lineCheckResult->expects($this->any())
            ->method('getRangeATopDownstreamDataBandwidth')
            ->will($this->returnValue($data['lineCheckData']['getRangeATopDownstreamDataBandwidth']));
        $lineCheckResult->expects($this->any())
            ->method('getRangeABottomUpstreamDataBandwidth')
            ->will($this->returnValue($data['lineCheckData']['getRangeABottomUpstreamDataBandwidth']));
        $lineCheckResult->expects($this->any())
            ->method('getRangeATopUpstreamDataBandwidth')
            ->will($this->returnValue($data['lineCheckData']['getRangeATopUpstreamDataBandwidth']));
        $lineCheckResult->expects($this->any())
            ->method('getMaxSpeed')
            ->will($this->returnValue($data['lineCheckData']['getMaxSpeed']));
        $lineCheckResult->expects($this->any())
            ->method('getMaxSpeedRangeMin')
            ->will($this->returnValue($data['lineCheckData']['getMaxSpeedRangeMin']));
        $lineCheckResult->expects($this->any())
            ->method('getMaxSpeedRangeMax')
            ->will($this->returnValue($data['lineCheckData']['getMaxSpeedRangeMax']));


        $result = AccountChange_Controller::getMinAndMaxSpeedRanges(
            $data['objCoreService'],
            $lineCheckResult,
            $data['intServiceDefinitionId']
        );


        $this->assertNotEmpty($result);
    }


    public function providerForTestGetMinAndMaxSpeedRanges()
    {
        $availableProduct = new AvailableProduct();
        $availableProduct->setPricePointId(2617);
        $availableProduct->setCurrentBasePriceInPounds(12);
        $availableProduct->setCurrentBasePriceInContractInPounds(11);
        $availableProducts = array($availableProduct);

        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('getCustomerAvailableProducts'),
            array(),
            '',
            false
        );

        $billingApiFacade
            ->expects($this->any())
            ->method('getCustomerAvailableProducts')
            ->willReturn($availableProducts);

        \Plusnet\BillingApiClient\Service\ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $coreService = $this->getMock(
            'Core_Service',
            array('getServiceId', 'isPlusnetUser', 'isPartnerUser')
        );

        $coreService->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue(1812332));

        $coreService->expects($this->any())
            ->method('isPlusnetUser')
            ->will($this->returnValue(true));

        $coreService->expects($this->any())
            ->method('isPartnerUser')
            ->will($this->returnValue(true));

        return array(
            'FTTC Extra: No Cap'                 => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => 100000,
                        'getFttcUncappedUpSpeed'                 => 30000,
                        'getRangeABottomDownstreamDataBandwidth' => 90000,
                        'getRangeATopDownstreamDataBandwidth'    => 90000,
                        'getRangeABottomUpstreamDataBandwidth'   => 20000,
                        'getRangeATopUpstreamDataBandwidth'      => 30000,
                        'getMaxSpeed'                            => null,
                        'getMaxSpeedRangeMin'                    => null,
                        'getMaxSpeedRangeMax'                    => null
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'FTTC',
                    'productName'            => 'Extra',
                    'intRealMaxDownstream'   => 1,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            ),
            'FTTC: Ranges'                       => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => 50000,
                        'getFttcUncappedUpSpeed'                 => 30000,
                        'getRangeABottomDownstreamDataBandwidth' => 60000,
                        'getRangeATopDownstreamDataBandwidth'    => 60000,
                        'getRangeABottomUpstreamDataBandwidth'   => 20000,
                        'getRangeATopUpstreamDataBandwidth'      => 30000,
                        'getMaxSpeed'                            => null,
                        'getMaxSpeedRangeMin'                    => null,
                        'getMaxSpeedRangeMax'                    => null
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'FTTC',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 1,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            ),
            'FTTC: Ranges 40/20'                 => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => 50000,
                        'getFttcUncappedUpSpeed'                 => 30000,
                        'getRangeABottomDownstreamDataBandwidth' => 60000,
                        'getRangeATopDownstreamDataBandwidth'    => 60000,
                        'getRangeABottomUpstreamDataBandwidth'   => 20000,
                        'getRangeATopUpstreamDataBandwidth'      => 30000,
                        'getMaxSpeed'                            => null,
                        'getMaxSpeedRangeMin'                    => null,
                        'getMaxSpeedRangeMax'                    => null
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'FTTC',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 1,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => 20000
                    )
                )
            ),
            'FTTC: Up/Down Ranges Null'          => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => 50000,
                        'getFttcUncappedUpSpeed'                 => 30000,
                        'getRangeABottomDownstreamDataBandwidth' => 0,
                        'getRangeATopDownstreamDataBandwidth'    => 0,
                        'getRangeABottomUpstreamDataBandwidth'   => 0,
                        'getRangeATopUpstreamDataBandwidth'      => 0,
                        'getMaxSpeed'                            => null,
                        'getMaxSpeedRangeMin'                    => null,
                        'getMaxSpeedRangeMax'                    => null
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'FTTC',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 1,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            ),
            'FTTC: RangeABottomUpstream >= 2000' => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => 50000,
                        'getFttcUncappedUpSpeed'                 => 30000,
                        'getRangeABottomDownstreamDataBandwidth' => 60000,
                        'getRangeATopDownstreamDataBandwidth'    => 60000,
                        'getRangeABottomUpstreamDataBandwidth'   => 2000,
                        'getRangeATopUpstreamDataBandwidth'      => 30000,
                        'getMaxSpeed'                            => null,
                        'getMaxSpeedRangeMin'                    => null,
                        'getMaxSpeedRangeMax'                    => null
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'FTTC',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 40000,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            ),
            'FTTC: RangeATopUpstream >= 2000'    => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => 50000,
                        'getFttcUncappedUpSpeed'                 => 30000,
                        'getRangeABottomDownstreamDataBandwidth' => 60000,
                        'getRangeATopDownstreamDataBandwidth'    => 60000,
                        'getRangeABottomUpstreamDataBandwidth'   => 0,
                        'getRangeATopUpstreamDataBandwidth'      => 2000,
                        'getMaxSpeed'                            => null,
                        'getMaxSpeedRangeMin'                    => null,
                        'getMaxSpeedRangeMax'                    => null
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'FTTC',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 40000,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            ),
            'ADSL: No service ID'                => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => null,
                        'getFttcUncappedUpSpeed'                 => null,
                        'getRangeABottomDownstreamDataBandwidth' => null,
                        'getRangeATopDownstreamDataBandwidth'    => null,
                        'getRangeABottomUpstreamDataBandwidth'   => null,
                        'getRangeATopUpstreamDataBandwidth'      => null,
                        'getMaxSpeed'                            => 8000,
                        'getMaxSpeedRangeMin'                    => 10000,
                        'getMaxSpeedRangeMax'                    => 12000
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'WBC MAX8 mb/s (ADSL1)',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 1,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            ),
            'ADSL2+: No service ID'              => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => null,
                        'getFttcUncappedUpSpeed'                 => null,
                        'getRangeABottomDownstreamDataBandwidth' => null,
                        'getRangeATopDownstreamDataBandwidth'    => null,
                        'getRangeABottomUpstreamDataBandwidth'   => null,
                        'getRangeATopUpstreamDataBandwidth'      => null,
                        'getMaxSpeed'                            => 8000,
                        'getMaxSpeedRangeMin'                    => 10000,
                        'getMaxSpeedRangeMax'                    => 12000
                    ),
                    'intServiceDefinitionId' => null,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'WBC End User Access (EUA)',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 1,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            ),
            'ADSL2+: With service ID'            => array(
                array(
                    'objCoreService'         => $coreService,
                    'lineCheckData'          => array(
                        'getFttcUncappedDownSpeed'               => null,
                        'getFttcUncappedUpSpeed'                 => null,
                        'getRangeABottomDownstreamDataBandwidth' => null,
                        'getRangeATopDownstreamDataBandwidth'    => null,
                        'getRangeABottomUpstreamDataBandwidth'   => null,
                        'getRangeATopUpstreamDataBandwidth'      => null,
                        'getMaxSpeed'                            => 8000,
                        'getMaxSpeedRangeMin'                    => 10000,
                        'getMaxSpeedRangeMax'                    => 12000
                    ),
                    'intServiceDefinitionId' => 6868,
                    'product'                => array('productId' => 6754),
                    'strProductCode'         => 'WBC End User Access (EUA)',
                    'productName'            => 'Unlimited',
                    'intRealMaxDownstream'   => 1,
                    'ProvisionedServiceData' => array(
                        'intInternalMaxUpstream' => null
                    )
                )
            )
        );
    }

    /**
     * Test get and set CRD.
     *
     * @return void
     */
    public function testSetAndGetCrd()
    {
        $expectedResult = '01/01/2015';

        AccountChange_Controller::setCrd($expectedResult);

        $this->assertEquals($expectedResult, AccountChange_Controller::getCrd());
    }

    /*
     * Get a mock business actor object
     *
     * @return Auth_BusinessActor
     */
    private function getMockBusinesActor()
    {
        $objMockAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockAdaptor->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objMockAdaptor, Db_Manager::DEFAULT_TRANSACTION);

        $objBusinessActor = Auth_BusinessActor::get(1);
        $objBusinessActor->setRealm('workplace.plus.net');
        $objBusinessActor->setUserType('PLUSNET_ENDUSER');
        $objLoginMock = $this->getMock('Auth_Login', array('getBusinessActor'), array(), '', false);

        $objLoginMock->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue($objBusinessActor));

        Auth_Auth::setCurrentLogin($objLoginMock);

        return $objBusinessActor;
    }

    /**
     * Get a mock DB Adaptor object
     *
     * @return Db_Adaptor
     */
    private function getMockDbAdaptor()
    {
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceDao',
                'getUserDao',
                'getAddressDao',
                'getServiceDefinitionDao',
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService',
                'getCostLevelForServiceId'
            ),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDao')
            ->will($this->returnValue($this->getServiceDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getUserDao')
            ->will($this->returnValue($this->getUserDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getAddressDao')
            ->will($this->returnValue($this->getAddressDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($this->getServiceDefinitionDaoData()));

        $objMockDbAdaptor->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        return $objMockDbAdaptor;
    }

    /**
     * Get a mock AccountChange_Account object
     *
     * @return AccountChange_Account
     */
    protected function getAccountChangeMock()
    {
        $objAccountChangeMock = $this->getMock(
            'Db_Adaptor',
            array(
                'getServiceComponentDetailsForService',
                'getServiceDefinitionDetailsForService',
                'getCostLevelForServiceId'
            ),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($this->getServiceComponentDetailsForServiceData()));

        $objAccountChangeMock->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue($this->getServiceDefinitionDetailsForServiceData()));

        return $objAccountChangeMock;
    }

    /**
     *
     */
    public function testInitGetsDirectDebitDetailsFromLegacyWhenNewBillingEngineIsOn()
    {
        $billingAccount = array(
            'id'                    => '1187',
            'customerId'            => '4000202',
            'serviceId'             => '4000200',
            'username'              => 'testsk40',
            'brand'                 => 'PlusnetResidential',
            'nextInvoiceDate'       => '2017-05-18',
            'accountActivationDate' => '2017-05-18',
            'billingStatus'         => 'Billable',
            'paymentDetails'        => array(
                'directDebit' => array(
                    'id'              => '1',
                    'name'            => 'forename surname',
                    'sortCode'        => '####99',
                    'accountNumber'   => '#####575',
                    'referenceNumber' => 'PNET-4000200-1',
                    'status'          => 'Pending activation'
                )
            )
        );


        $arrDirectDebitDetails = array(
            'name'          => 'forename surname',
            'accountNumber' => '#####575',
            'sortCode'      => '####99'
        );

        $objBusinessActor = $this->getMockBusinesActor();

        $objMockDbAdaptor = $this->getMockDbAdaptor();
        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $mode = $this->getMock(
            'AccountChange_Mode',
            array('getChangeMode'),
            array($objBusinessActor)
        );

        $mode->expects($this->any())
            ->method('getChangeMode')
            ->will($this->returnValue(AccountChange_Mode::BOTH));

        AccountChange_Mode::setInstance($mode);

        $objController = $this->getMock(
            'AccountChange_Controller',
            array(
                'getValidationCheck',
                'getProvisionedProduct',
                'matchAddress',
                'getAdslComponentExists',
                'getExistingBroadbandContractData',
                'getProvisionedService',
                'isNewBillingEngineOn',
                'getBillingAccount',
                'isBillingToday',
                'getStatusService',
                'getCoreService',
                'getPriceIncreasePercent'
            ),
            array()
        );

        $this->setupMockStatusService($objController);
        $this->setUpBillingApiResponseAndCoreService($objController);

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $objController->expects($this->any())
            ->method('getPriceIncreasePercent')
            ->will($this->returnValue(1));

        $objController->expects($this->once())
            ->method('isBillingToday')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('getValidationCheck')
            ->will($this->returnValue($validationCheck));

        $objController->expects($this->any())
            ->method('matchAddress')
            ->will($this->returnValue(new Wlr3_Addresses()));

        $objController->expects($this->once())
            ->method('getBillingAccount')
            ->will($this->returnValue($billingAccount));

        $objController->expects($this->once())
            ->method('isNewBillingEngineOn')
            ->will($this->returnValue(true));

        $objAccountChangeMock = $this->getAccountChangemock();
        Db_Manager::setAdaptor('AccountChange', $objAccountChangeMock);

        $arrResult = $objController->init(true, true, 'test', false);

        $this->assertEquals($arrDirectDebitDetails, $arrResult['arrData']['arrDirectDebitDetails']);
    }

    /**
     * Test that the expected direct debit details are returned from Coredb when
     * the new billing engine is switchd off
     *
     * @covers AccountChange_Controller::init
     * @covers AccountChange_Controller::getDirectDebitDetails
     * @covers AccountChange_Controller::getDirectDebitDetailsLegacy
     *
     * @return void
     */
    public function testInitGetsDirectDebitDetailsFromLegacyWhenNewBillingEngineIsOff()
    {
        $legacyDirectDebitDetails = array(
            'name'          => 'Mr. Fake',
            'accountNumber' => '********',
            'sortCode'      => '998877'
        );

        $arrDirectDebitDetails = array(
            'name'          => 'Mr. Fake',
            'accountNumber' => '*****321',
            'sortCode'      => '****77'
        );

        $objBusinessActor = $this->getMockBusinesActor();

        $objMockDbAdaptor = $this->getMockDbAdaptor();
        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $mode = $this->getMock(
            'AccountChange_Mode',
            array('getChangeMode'),
            array($objBusinessActor)
        );

        $mode->expects($this->any())
            ->method('getChangeMode')
            ->will($this->returnValue(AccountChange_Mode::BOTH));

        AccountChange_Mode::setInstance($mode);

        $mockfinancialDdDetails = $this->getMockBuilder('Financial_DirectDebitDetails')
            ->setMethods(array('getName', 'getDecryptedBankAccountNumber', 'getBankSortCode'))
            ->getMock();

        $mockfinancialDdDetails->expects($this->once())
            ->method('getName')
            ->willReturn($legacyDirectDebitDetails['name']);

        $mockfinancialDdDetails->expects($this->once())
            ->method('getDecryptedBankAccountNumber')
            ->willReturn($legacyDirectDebitDetails['accountNumber']);

        $mockfinancialDdDetails->expects($this->once())
            ->method('getBankSortCode')
            ->willReturn($legacyDirectDebitDetails['sortCode']);

        $objController = $this->getMock(
            'AccountChange_Controller',
            array(
                'getValidationCheck',
                'getProvisionedProduct',
                'matchAddress',
                'getAdslComponentExists',
                'getExistingBroadbandContractData',
                'getProvisionedService',
                'isNewBillingEngineOn',
                'getFinancialDirectDebitDetails',
                'isBillingToday',
                'getStatusService',
                'getCoreService',
                'getPriceIncreasePercent'
            ),
            array()
        );

        $this->setupMockStatusService($objController);
        $this->setUpBillingApiResponseAndCoreService($objController);

        $validationCheck = $this->getMock(
            'AccountChange_ValidationCheck',
            array('isAccountChangeAllowed', 'getReasonForBlockage'),
            array($objBusinessActor)
        );

        $validationCheck->expects($this->any())
            ->method('isAccountChangeAllowed')
            ->will($this->returnValue(true));

        $objController->expects($this->any())
            ->method('getPriceIncreasePercent')
            ->will($this->returnValue(1));

        $objController->expects($this->once())
            ->method('isBillingToday')
            ->will($this->returnValue(false));

        $objController->expects($this->any())
            ->method('getValidationCheck')
            ->will($this->returnValue($validationCheck));

        $objController->expects($this->any())
            ->method('matchAddress')
            ->will($this->returnValue(new Wlr3_Addresses()));

        $objController->expects($this->once())
            ->method('getFinancialDirectDebitDetails')
            ->will($this->returnValue($mockfinancialDdDetails));

        $objController->expects($this->once())
            ->method('isNewBillingEngineOn')
            ->will($this->returnValue(false));

        $objAccountChangeMock = $this->getAccountChangemock();
        Db_Manager::setAdaptor('AccountChange', $objAccountChangeMock);

        $arrResult = $objController->init(true, true, 'test', false);

        $this->assertEquals($arrDirectDebitDetails, $arrResult['arrData']['arrDirectDebitDetails']);
    }

    public function testGetValidationCheckWithHousemove()
    {
        /** @var AccountChange_Controller $objController */
        $objController = $this->getMockBuilder('AccountChange_Controller')
            ->disableOriginalConstructor()
            ->setMethods(null)
            ->getMock();

        $policies = $objController->getValidationPolicies(true);

        $this->assertFalse(in_array(AccountChange_HouseMovePolicy::class, $policies));

    }

    public function testGetValidationCheckWithoutHousemove()
    {
        /** @var AccountChange_Controller $objController */
        $objController = $this->getMockBuilder('AccountChange_Controller')
            ->disableOriginalConstructor()
            ->setMethods(null)
            ->getMock();

        $policies = $objController->getValidationPolicies(false);

        $this->assertTrue(in_array(AccountChange_HouseMovePolicy::class, $policies));

    }

    /**
     * The method to off the new billing engine.
     * Rather than mocking for isBillingToday in Core_service, new billing engine is off
     *
     * @return void
     */
    private function turnOffNewBillingEngine()
    {
        $dbFeatureSwitchMock = $this->getMockBuilder('\Db_Adaptor')
            ->setMethods(
                array(
                    'getFeatureToggleByName',
                    'isServiceIdExcludedFromFeatureSwitch',
                    'getUsernameFromServiceId',
                    'isUsernameExcludedFromFeatureSwitch'
                )
            )
            ->setConstructorArgs(array('FeatureSwitch', \Db_Manager::DEFAULT_TRANSACTION, true))
            ->getMock();

        $dbFeatureSwitchMock->expects($this->any())
            ->method('getFeatureToggleByName')
            ->will(
                $this->returnValue(
                    array(
                        'onDateTime'  => '2010-01-01',
                        'offDateTime' => '2010-01-01'
                    )
                )
            );

        $dbFeatureSwitchMock->expects($this->any())
            ->method('isServiceIdExcludedFromFeatureSwitch')
            ->will($this->returnValue(false));

        $dbFeatureSwitchMock->expects($this->any())
            ->method('getUsernameFromServiceId')
            ->will($this->returnValue(false));

        $dbFeatureSwitchMock->expects($this->any())
            ->method('isUsernameExcludedFromFeatureSwitch')
            ->will($this->returnValue(false));

        \Db_Manager::setAdaptor('FeatureSwitch', $dbFeatureSwitchMock);
    }


    /**
     * @dataProvider testCompletePassProperContractDurationToUpdateHouseMoveMethodAccountDataProvider
     *
     * @param string $status                status
     * @param string $serviceDefinitionIsp  the ISP
     * @param string $serviceDefinitionType service definition type
     *
     * @return void
     */
    public function testCompletePassProperContractDurationToUpdateHouseMoveMethodAccount(
        $serviceDefinitionIsp,
        $serviceDefinitionType,
        $contractDuration,
        $expectedContractDuration,
        $contractDefinitionLengthFromSdi
    ) {
        $serviceId = 1234321;
        $oldServiceDefinitionId = 6813;
        $newServiceDefinitionId = 6819;

        $serviceDefinitionDetails = array(
            'service_definition_id'       => $newServiceDefinitionId,
            'intProductVariantId'         => '110',
            'name'                        => 'Unlimited Business Fibre Broadband and phone (12 Month Contract)',
            'isp'                         => $serviceDefinitionIsp,
            'minimum_charge'              => '42.0000',
            'date_created'                => '2013-03-15',
            'time_stamp'                  => '2013-05-28 23:34:17',
            'requires'                    => 'adsl',
            'initial_charge'              => '0.00',
            'decUpgradeCharge'            => '0.00',
            'type'                        => $serviceDefinitionType,
            'creator_id'                  => 'mstarbuck',
            'password_visible_to_support' => 'Y',
            'end_date'                    => 'NULL',
            'signup_via_portal'           => 'Y',
            'bt_product_id'               => 'NULL',
            'blurb'                       => '',
        );

        $supplierProducts = array(
            'intSupplierProductId'     => $serviceDefinitionDetails['service_definition_id'],
            'intSupplierProductTypeId' => 1,
            'strName'                  => '1',
            'strProductCode'           => 'FTTC',
            'intMinDownstream'         => 1,
            'intMaxDownstream'         => 1,
            'intRealMaxDownstream'     => 40000,
            'strStability'             => '1',
            'strInterleaving'          => '1',
            'strMaxUpstream'           => '1',
            'strTrafficWeighting'      => '1',
            'strMaintenanceCategory'   => '1',
            'intInternalMaxDownstream' => 0,
            'intInternalMaxUpstream'   => 0
        );

        $arrSupplierPlatform = array(
            'intSupplierPlatformId' => 1,
            'intSupplierId'         => 1,
            'strHandle'             => '1',
            'strDisplayName'        => '1'
        );

        $arrSupplier = array(
            'strHandle'      => '1',
            'strDisplayName' => '1'
        );

        /** @var \LineCheck_Result | \PHPUnit_Framework_MockObject_MockObject $lineCheckResultMock */
        $lineCheckResultMock = $this->getMockBuilder('LineCheck_Result')
            ->getMock();

        /** @var \AccountChange_Manager | \PHPUnit_Framework_MockObject_MockObject $accountManagerMock */
        $accountManagerMock = $this->getMockBuilder('AccountChange_Manager')
            ->setMethods(
                array(
                    'selectAccountChangeType',
                    'changeAccount',
                    'sendConfirmationEmails',
                    'sendAppointmentEmail'
                )
            )
            ->disableOriginalConstructor()
            ->getMock();

        $accountManagerMock->expects($this->once())
            ->method('changeAccount')
            ->will($this->returnValue(true));

        /** @var Core_Service | \PHPUnit_Framework_MockObject_MockObject $coreServiceMock */
        $coreServiceMock = $this->getMockBuilder('Core_Service')
            ->setMethods(array('getNextInvoiceDate'))
            ->getMock();

        $coreServiceMock->expects($this->once())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue('2018-01-01'));

        $arrayData = array(
            'arrProductConfigurations' => array(),
            'objAccountChangeManager'  => $accountManagerMock,
            'objLineCheckResult'       => $lineCheckResultMock,
            'objCoreService'           => $coreServiceMock,
            'bolHousemove'             => true,
            'bolSchedule'              => true,
            'selectedContractDuration' => $expectedContractDuration
        );

        /** @var \Db_Adaptor | \PHPUnit_Framework_MockObject_MockObject $coreAdaptorMock */
        $coreAdaptorMock = $this->getMockBuilder('\Db_Adaptor')
            ->setMethods(array('getServiceDao', 'getServiceDefinitionDetailsForService', 'getServiceDefinitionDao'))
            ->setConstructorArgs(array('Core', Db_Manager::DEFAULT_TRANSACTION, false))
            ->getMock();

        $coreAdaptorMock->expects($this->any())
            ->method('getServiceDao')
            ->will($this->returnValue(array('service_id' => $serviceId)));

        $coreAdaptorMock->expects($this->any())
            ->method('getServiceDefinitionDetailsForService')
            ->will($this->returnValue($serviceDefinitionDetails));

        $coreAdaptorMock->expects($this->once())
            ->method('getServiceDefinitionDao')
            ->will($this->returnValue($serviceDefinitionDetails));

        Db_Manager::setAdaptor('Core', $coreAdaptorMock);

        /** @var \Db_Adaptor | \PHPUnit_Framework_MockObject_MockObject $productAdaptorMock */
        $productAdaptorMock = $this->getMockBuilder('\Db_Adaptor')
            ->setMethods(array(
                             'getProduct',
                             'getSupplierPlatformByHandle',
                             'getSupplierData',
                             'getProvisionedProfileByServiceDefinitionId',
                             'getSupplierProductsByProductCode',
                             'getSupplierPlatformBySupplierProductTypeId'
                         ))
            ->setConstructorArgs(array('Product', Db_Manager::DEFAULT_TRANSACTION, false))
            ->getMock();

        $productAdaptorMock->expects($this->any())
            ->method('getProduct')
            ->will($this->returnValue(array('product_id' => $serviceDefinitionDetails['service_definition_id'])));

        $productAdaptorMock->expects($this->any())
            ->method('getSupplierPlatformByHandle')
            ->will($this->returnValue($arrSupplierPlatform));

        $productAdaptorMock->expects($this->any())
            ->method('getSupplierData')
            ->will($this->returnValue($arrSupplier));

        $productAdaptorMock->expects($this->any())
            ->method('getProvisionedProfileByServiceDefinitionId')
            ->will($this->returnValue(array('strProductCode' => $supplierProducts[0]['strProductCode'])));

        $productAdaptorMock->expects($this->any())
            ->method('getSupplierProductsByProductCode')
            ->will($this->returnValue(array($supplierProducts)));

        $productAdaptorMock->expects($this->any())
            ->method('getSupplierPlatformBySupplierProductTypeId')
            ->will($this->returnValue($arrSupplierPlatform));

        Db_Manager::setAdaptor('Product', $productAdaptorMock);

        /** @var \Db_Adaptor | \PHPUnit_Framework_MockObject_MockObject $accountChangeAdaptorMock */
        $accountChangeAdaptorMock = $this->getMockBuilder('\Db_Adaptor')
            ->setMethods(
                array(
                    'getSetupFee',
                    'getServiceDefinitionDetails',
                    'getProvisionedServiceDetailsForService',
                    'getBlockedAccountChangeEmailProductNames',
                    'getContractDefinitionLengthFromSdi',
                    'getScheduleIdForBroadband',
                    'getContractTypeBySdi'
                )
            )
            ->setConstructorArgs(array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false))
            ->getMock();

        $accountChangeAdaptorMock->expects($this->any())
            ->method('getSetupFee')
            ->will($this->returnValue(1));

        $accountChangeAdaptorMock->expects($this->any())
            ->method('getServiceDefinitionDetails')
            ->will($this->returnValue(array()));

        $accountChangeAdaptorMock->expects($this->any())
            ->method('getProvisionedServiceDetailsForService')
            ->will($this->returnValue(array('intInternalMaxUpstream' => null)));

        $accountChangeAdaptorMock->expects($this->any())
            ->method('getBlockedAccountChangeEmailProductNames')
            ->will($this->returnValue(array()));

        $contractLengthCallCount = $serviceDefinitionIsp == 'johnlewis' ? $this->once() : $this->never();
        $accountChangeAdaptorMock->expects($contractLengthCallCount)
            ->method('getContractDefinitionLengthFromSdi')
            ->will($this->returnValue($contractDefinitionLengthFromSdi));

        $accountChangeAdaptorMock->expects($this->any())
            ->method('getContractTypeBySdi')
            ->will($this->returnValue('F'));

        Db_Manager::setAdaptor('AccountChange', $accountChangeAdaptorMock);

        /** @var \Auth_BusinessActor | \PHPUnit_Framework_MockObject_MockObject $businessActorMock */
        $businessActorMock = $this->getMockBuilder('Auth_BusinessActor')
            ->setMethods(array('getExternalUserId', 'getUserType'))
            ->getMock();

        $businessActorMock->expects($this->exactly(2))
            ->method('getExternalUserId')
            ->will($this->returnValue($serviceId));

        $businessActorMock->expects($this->once())
            ->method('getUserType')
            ->will($this->returnValue('PLUSNET_STAFF'));

        /** @var \AccountChange_Controller | \PHPUnit_Framework_MockObject_MockObject $controllerMock */
        $controllerMock = $this->getMockBuilder('AccountChange_Controller')
            ->setMethods(
                array(
                    'getUserBusinessActor',
                    'getLegacyFiles',
                    'getApplicationStateVar',
                    'isFibreProduct',
                    'getScheduledChangeDate',
                    'sendEmailMSN',
                    'expectingPhoneForHouseMove',
                    'getChargeableComponentsFromServiceId',
                    'createWlr3Component',
                    'writeLinecheckResult',
                    'registerOneOffChargeGroup',
                    'determineContractType',
                    'getHouseMovePostScheduledChangeHelper'
                )
            )
            ->getMock();

        $houseMoveHelperMock = $this->getMockBuilder('AccountChange_HouseMovePostScheduledChangeHelper')
            ->setMethods(array('execute'))
            ->disableOriginalConstructor()
            ->getMock();

        $controllerMock->expects($this->once())
            ->method('writeLinecheckResult')
            ->will($this->returnValue(false));

        $controllerMock->expects($this->any())
            ->method('getUserBusinessActor')
            ->will($this->returnValue($businessActorMock));

        $controllerMock->expects($this->once())
            ->method('getHouseMovePostScheduledChangeHelper')
            ->with($serviceId, $expectedContractDuration)
            ->will($this->returnValue($houseMoveHelperMock));

        $houseMoveHelperMock->expects($this->once())
            ->method('execute');

        $controllerMock->expects($this->exactly(4))
            ->method('getApplicationStateVar')
            ->withConsecutive(
                array(AccountChange_Controller::class, 'intNewSdi'),
                array(AccountChange_Controller::class, 'intOldSdi'),
                array(AccountChange_Controller::class, 'objLineCheckResult'),
                array(AccountChange_Controller::class, 'objBusinessActor')
            )
            ->willReturnOnConsecutiveCalls(
                $newServiceDefinitionId,
                $oldServiceDefinitionId,
                $lineCheckResultMock,
                $businessActorMock
            );

        $controllerMock->expects($this->exactly(2))
            ->method('isFibreProduct')
            ->withConsecutive(
                array($oldServiceDefinitionId),
                array($newServiceDefinitionId)
            )
            ->willReturnOnConsecutiveCalls(
                false,
                true
            );

        $contractTypeDtoMock = $this->getMockBuilder(ContractTypeDto::class)
            ->setMethods(array('getContractType', 'getContractSubType'))
            ->disableOriginalConstructor()
            ->getMock();

        $contractTypeDtoMock->expects($this->once())
            ->method('getContractType')
            ->will($this->returnValue('F'));

        $contractTypeDtoMock->expects($this->once())
            ->method('getContractSubType')
            ->will($this->returnValue(null));

        $controllerMock->expects($this->once())
            ->method('determineContractType')
            ->will($this->returnValue($contractTypeDtoMock));

        $controllerMock->complete(array(), $arrayData);

        ServiceManager::reset();
    }

    /**
     * @return array
     */
    public static function testCompletePassProperContractDurationToUpdateHouseMoveMethodAccountDataProvider()
    {
        return array(
            array(
                'plus.net',
                'business',
                '24',
                '24',
                null
            ),
            array(
                'plus.net',
                'residential',
                null,
                null,
                null
            ),
            array(
                'johnlewis',
                'residential',
                null,
                '12',
                '12'
            ),
        );
    }

    public function testBusinessPricingVariables()
    {
        $authBusinessActor = $this->getMockBusinesActor();
        $objMockDbAdaptor = $this->getMockDbAdaptor();
        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        $validationCheck = $this->getMock(
            AccountChange_ValidationCheck::class,
            array('isAccountChangeAllowed'),
            array(),
            '',
            false
        );
        $validationCheck->method('isAccountChangeAllowed')->willReturn(true);

        $controller = TestCaseWithProxy::getPHPUnitProxy(
            'AccountChange_Controller',
            array()
        );
        $controller = $this->getMock(
            get_class($controller),
            array(
                'getPricingDate',
                'getUserBusinessActor',
                'getCoreService',
                'getCoreServiceDefinition',
                'getDirectDebitDetails',
                'getValidationCheck',
                'customerHasPaperBilling',
                'getProvisionedService',
                'performLineCheckIfFibreUpgradeCampaign',
                'getAdslComponentExists',
                'getExistingBroadbandContractData',
                'getStatusService',
                'getShowBusinessPricingNotification',
                'getProvisionedProduct',
                'getPriceIncreasePercent'
            ),
            array()
        );

        $currentDate = Date('Y-m-d');
        $controller->method('getUserBusinessActor')->willReturn($authBusinessActor);
        $controller->method('getDirectDebitDetails')->willReturn(null);
        $controller->method('getValidationCheck')->willReturn($validationCheck);
        $controller->method('customerHasPaperBilling')->willReturn(null);
        $controller->method('getProvisionedService')->willReturn(null);
        $controller->method('performLineCheckIfFibreUpgradeCampaign')->willReturn(null);
        $controller->method('getAdslComponentExists')->willReturn(null);
        $controller->method('getExistingBroadbandContractData')->willReturn(null);
        $controller->method('getPricingDate')->willReturn($currentDate);
        $controller->method('getShowBusinessPricingNotification')->willReturn(false);
        $controller->method('getProvisionedProduct')->willReturn(false);
        $controller->method('getPriceIncreasePercent')->willReturn(false);

        $this->setupMockStatusService($controller);
        $this->setUpBillingApiResponseAndCoreService($controller);

        $result = $controller->protected_init(true, '', 'test', 'null', 'false');
        $arrData = $result['arrData'];

        self::assertEquals(false, $arrData['bolShowBusinessPricingNotification']);
        self::assertEquals($currentDate, $arrData['businessPricingDate']);
    }

    /**
     * Common setup for status service.
     *
     * @param $mockController
     */
    protected function setupMockStatusService($mockController)
    {
        $mockStatusService = $this->getMock(
            'PriceProtected_StatusService',
            ['isServicePriceProtected']
        );

        $mockStatusService->expects($this->any())
            ->method('isServicePriceProtected')
            ->with('12345')
            ->willReturn(true);

        $mockController->expects($this->any())
            ->method('getStatusService')
            ->willReturn($mockStatusService);
    }

    /**
     * Common setup for core service and billing api response.
     *
     * @param $mockController
     */
    protected function setUpBillingApiResponseAndCoreService($mockController)
    {
        $billingApiFacade = $this->getMock(
            '\Plusnet\BillingApiClient\Facade\BillingApiFacade',
            array('getCustomerAvailableProducts'),
            array(),
            '',
            false
        );

        $availableProduct = new AvailableProduct();
        $availableProduct->setProductOfferingId(123);
        $availableProduct->setPricePointId(456);
        $availableProduct->setCurrentBasePriceInContractInPounds(5);
        $availableProduct->setCurrentBasePriceInPounds(10);
        $availableProducts = array($availableProduct);

        $billingApiFacade
            ->expects($this->any())
            ->method('getCustomerAvailableProducts')
            ->willReturn($availableProducts);

        \Plusnet\BillingApiClient\Service\ServiceManager::setService('BillingApiFacade', $billingApiFacade);

        $coreService = $this->getMock(
            'Core_Service',
            array(
                'getType',
                'getServiceComponentId',
                'getUserId',
                'getAdslDetails',
                'getIsp',
                'isBillingToday',
                'getCliNumber')
        );

        $coreService->expects($this->any())
            ->method('getType')
            ->will($this->returnValue('123'));

        $coreService->expects($this->any())
            ->method('getServiceComponentId')
            ->will($this->returnValue('123'));

        $coreService->expects($this->any())
            ->method('getUserId')
            ->will($this->returnValue('123'));

        $coreService->expects($this->any())
            ->method('getAdslDetails')
            ->will($this->returnValue(array('intTariffID' => '456')));

        $coreService->expects($this->any())
            ->method('getIsp')
            ->will($this->returnValue('plus.net'));

        $coreService->expects($this->any())
            ->method('isBillingToday')
            ->will($this->returnValue(false));

        $coreService->expects($this->any())
            ->method('getCliNumber')
            ->will($this->returnValue('01142778959'));

        $mockController->expects($this->any())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));
    }
}
