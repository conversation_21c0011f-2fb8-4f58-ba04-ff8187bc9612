<?php

/**
 * <AUTHOR>
 */

interface AccountChange_ValidationPolicy
{
    const STAFF_USER_TYPE = 'PLUSNET_STAFF';

    /**
     * @param Auth_BusinessActor $actor       The business actor
     * @param bool               $isWorkplace Flag to determine WP
     * @param bool               $isScript    Flag to determine is script
     */
    public function __construct(Auth_BusinessActor $actor, $isWorkplace = false, $isScript = false, $additionalInformation = array());

    /**
     * @return bool
     */
    public function validate();

    /**
     * @return string
     */
    public function getFailure();

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode();
}
