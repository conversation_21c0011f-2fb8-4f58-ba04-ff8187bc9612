<?php
/**
 * Helper functions that allow us format speed estimates in a consistent way.
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @link      https://jira.int.plus.net/browse/SM-335
 */
class AccountChange_LineCheckerCriteria
{
    /** Method to return array for line check criteria
     *
     * @param object $postCode           postcode object that supports __toString
     * @param string $thoroughFareNumber house name or number
     *
     * @return array
     */
    public static function buildLineCheckCriteria($postCode, $thoroughFareNumber)
    {
        return array(
            'postcode' => strval($postCode),
            'thoroughfareNumber' => $thoroughFareNumber
        );
    }
}
