<?php

/***
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_AccountChangeAddress_Test extends \PHPUnit_Framework_TestCase
{
    public function testArrayIsReturnedCorrectly()
    {
        $addressCategory = "";
        $addressReference = "AA12338292";
        $country = "";
        $county = "";
        $cssDatabaseCode = "AT";
        $organisationName = "The Balance";
        $poBox = "";
        $postTown = "Sheffield";
        $postCode = "S1 2GU";
        $premisesName = "";
        $subPremises = "";
        $thoroughfareName = "The Street";
        $thoroughfareNumber = "1";
        $buildingName = "The House";
        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory($addressCategory);
        $address->setAddressReference($addressReference);
        $address->setBuildingName($buildingName);
        $address->setCou<PERSON>ry($country);
        $address->setCounty($county);
        $address->setPostTown($postTown);
        $address->setCssDatabaseCode($cssDatabaseCode);
        $address->setOrganisationName($organisationName);
        $address->setPoBox($poBox);
        $address->setPostCode($postCode);
        $address->setPremisesName($premisesName);
        $address->setSubPremises($subPremises);
        $address->setThoroughfareName($thoroughfareName);
        $address->setThoroughfareNumber($thoroughfareNumber);

        $expectedArray = array(
            "addressCategory" => $addressCategory,
            "addressReference" => $addressReference,
            "country" => $country,
            "county" => $county,
            "cssDatabaseCode" => $cssDatabaseCode,
            "organisationName" => $organisationName,
            "poBox" => $poBox,
            "postTown" => $postTown,
            "postCode" => $postCode,
            "premisesName" => $premisesName,
            "subPremises" => $subPremises,
            "thoroughfareName" => $thoroughfareName,
            "thoroughfareNumber" => $thoroughfareNumber,
            "buildingName" => $buildingName
        );
        $this->assertEquals($expectedArray, $address->getAddressArray());
    }

    public function testSettersAndGetters()
    {
        $addressCategory = "";
        $addressReference = "AA12338292";
        $country = "";
        $county = "";
        $cssDatabaseCode = "AT";
        $organisationName = "The Balance";
        $poBox = "";
        $postTown = "Sheffield";
        $postCode = "S1 2GU";
        $premisesName = "";
        $subPremises = "";
        $thoroughfareName = "The Street";
        $thoroughfareNumber = "1";
        $buildingName = "The House";
        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory($addressCategory);
        $address->setAddressReference($addressReference);
        $address->setBuildingName($buildingName);
        $address->setCountry($country);
        $address->setCounty($county);
        $address->setPostTown($postTown);
        $address->setCssDatabaseCode($cssDatabaseCode);
        $address->setOrganisationName($organisationName);
        $address->setPoBox($poBox);
        $address->setPostCode($postCode);
        $address->setPremisesName($premisesName);
        $address->setSubPremises($subPremises);
        $address->setThoroughfareName($thoroughfareName);
        $address->setThoroughfareNumber($thoroughfareNumber);
        $this->assertEquals($addressCategory, $address->getAddressCategory());
        $this->assertEquals($addressReference, $address->getAddressReference());
        $this->assertEquals($buildingName, $address->getBuildingName());
        $this->assertEquals($country, $address->getCountry());
        $this->assertEquals($county, $address->getCounty());
        $this->assertEquals($postTown, $address->getPostTown());
        $this->assertEquals($cssDatabaseCode, $address->getCssDatabaseCode());
        $this->assertEquals($organisationName, $address->getOrganisationName());
        $this->assertEquals($poBox, $address->getPoBox());
        $this->assertEquals($postCode, $address->getPostCode());
        $this->assertEquals($premisesName, $address->getPremisesName());
        $this->assertEquals($subPremises, $address->getSubPremises());
        $this->assertEquals($thoroughfareName, $address->getThoroughfareName());
        $this->assertEquals($thoroughfareNumber, $address->getThoroughfareNumber());
    }


    /**
     * @param $cssValue
     * @param $addressRefValue
     * @param $saveAddressCall
     * @dataProvider getDataForSaveAddress
     */
    public function testSaveAddressDetails($cssValue, $addressRefValue, $saveAddressCall)
    {
        $mockAddress = $this->getMock(
            'AccountChange_AccountChangeAddress',
            array(
                'saveAddress',
                'getCssDatabaseCode',
                'getAddressReference'
            ),
            array()
        );

        $mockAddress->expects($this->atMost(1))
            ->method('getCssDatabaseCode')
            ->will($this->returnValue($cssValue));

        $mockAddress->expects($this->atMost(1))
            ->method('getAddressReference')
            ->will($this->returnValue($addressRefValue));

        $mockAddress->expects($this->exactly($saveAddressCall))
            ->method('saveAddress');

        $mockAddress->saveAddressRefAndDatabaseCode(1);
    }

    /**
     * @return array
     */
    public function getDataForSaveAddress()
    {
        return array(
            array('1', null, 1),
            array(null, 'f', 1),
            array(null, null, 0)
        );
    }

    public function testIsValid()
    {
        $addressCategory = "";
        $addressReference = "AA12338292";
        $country = "";
        $cssDatabaseCode = "AT";
        $organisationName = "The Balance";
        $poBox = "";
        $postTown = "Sheffield";
        $postCode = "S1 2GU";
        $premisesName = "";
        $subPremises = "";
        $thoroughfareName = "The Street";
        $thoroughfareNumber = "1";
        $buildingName = "The House";
        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory($addressCategory);
        $address->setAddressReference($addressReference);
        $address->setBuildingName($buildingName);
        $address->setCountry($country);
        $address->setPostTown($postTown);
        $address->setCssDatabaseCode($cssDatabaseCode);
        $address->setOrganisationName($organisationName);
        $address->setPoBox($poBox);
        $address->setPostCode($postCode);
        $address->setPremisesName($premisesName);
        $address->setSubPremises($subPremises);
        $address->setThoroughfareName($thoroughfareName);
        $address->setThoroughfareNumber($thoroughfareNumber);
        $this->assertTrue($address->isValid());
    }

    public function testIsNotValid()
    {
        $addressCategory = "";
        $addressReference = "AA12338292";
        $country = "";
        $cssDatabaseCode = "AT";
        $organisationName = "The Balance";
        $poBox = "";
        $postTown = "Sheffield";
        $postCode = NULL;
        $premisesName = "";
        $subPremises = "";
        $thoroughfareName = "The Street";
        $thoroughfareNumber = "1";
        $buildingName = "The House";
        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressCategory($addressCategory);
        $address->setAddressReference($addressReference);
        $address->setBuildingName($buildingName);
        $address->setCountry($country);
        $address->setPostTown($postTown);
        $address->setCssDatabaseCode($cssDatabaseCode);
        $address->setOrganisationName($organisationName);
        $address->setPoBox($poBox);
        $address->setPostCode($postCode);
        $address->setPremisesName($premisesName);
        $address->setSubPremises($subPremises);
        $address->setThoroughfareName($thoroughfareName);
        $address->setThoroughfareNumber($thoroughfareNumber);
        $this->assertFalse($address->isValid());
    }
}
