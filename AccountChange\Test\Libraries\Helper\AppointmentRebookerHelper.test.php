<?php
/**
 * <AUTHOR> <<EMAIL>>
 */


class AccountChange_AppointmentRebookerHelperTest extends PHPUnit_Framework_TestCase
{
    const ARR_ADDRESS = [
        "addressReference" => "A123456434",
        "cssDatabaseCode" => "SL"
    ];

    const BOOKED_APPOINTMENT_ARRAY = [
        "date" => "21-01-2022",
        "slot" => "AM",
        "refNum" => "AR3234"
    ];

    const MANUAL_APPOINTMENT_ARRAY = [
        [
            "date" => "21/01/2022",
            "timeSlot" => "AM"
        ],
        [
            "date" => "21/01/2022",
            "timeSlot" => "PM"
        ],
        [
            "date" => "22/01/2022",
            "timeSlot" => "AM"
        ]
    ];

    /**
     * @throws Db_TransactionException
     * @dataProvider getDataForManualAppointment
     */
    public function testAttemptToRebookFromManualAppointment(
        $addressRaw,
        $bookedAppointmentArray,
        $expectedLiveAppointment,
        $expectedManualAppointment
    ) {
        $arrAppointment = [
            'manual' => self::MANUAL_APPOINTMENT_ARRAY,
            'notes' => "some engineer notes"
        ];
        $appointment = new AccountChange_AccountChangeAppointment($arrAppointment);
        $address = new AccountChange_AccountChangeAddress();
        $address->setAddressReference($addressRaw['addressReference']);
        $address->setCssDatabaseCode($addressRaw['cssDatabaseCode']);
        $serviceId = 1234;

        $mockDb = $this->getMockBuilder(Db_Adaptor::class)
            ->setMethods(array('getAddressFromServiceId'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockDb->expects($this->exactly(is_null($address) ? 1 : 0))
            ->method('getAddressFromServiceId')
            ->will($this->returnValue(self::ARR_ADDRESS));

        Db_Manager::setAdaptor('AccountChange', $mockDb);

        $mockServiceContainer = $this->getMockBuilder('getEngineerServiceContainer')
            ->setMethods(array('add'))
            ->getMock();

        $mockEnginerAppointmentData = $this->getMockBuilder(
            EngineerAppointmentClient_FttpAccountChangeAppointmentData::class
        )
            ->setMethods(array('toArray'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockEnginerAppointmentData->expects($this->once())
            ->method('toArray')
            ->will($this->returnValue(array()));

        $mockRebooker = $this->getMockBuilder('AccountChange_AppointmentRebookerHelper')
            ->setMethods(array(
                "getEngineerAppointmentData",
                "getEngineerAppointmentClient",
                "getEngineerServiceContainer"))
            ->setConstructorArgs(array($address, $appointment, $serviceId))
            ->getMock();

        $mockRebooker->expects($this->once())
            ->method('getEngineerAppointmentData')
            ->will($this->returnValue($mockEnginerAppointmentData));

        $mockRebooker->expects($this->once())
            ->method('getEngineerServiceContainer')
            ->will($this->returnValue($mockServiceContainer));

        $mockEnginerClient = $this->getMockBuilder(EngineerAppointmentClient_Client::class)
            ->setMethods(array('bookAppointmentFromArray','getServiceBookedAppointmentData'))
            ->disableOriginalConstructor()
            ->getMock();

        $mockEnginerClient->expects($this->once())
            ->method('getServiceBookedAppointmentData')
            ->will($this->returnValue($bookedAppointmentArray));

        $mockRebooker->expects($this->once())
            ->method('getEngineerAppointmentClient')
            ->will($this->returnValue($mockEnginerClient));

        $returned = $mockRebooker->attemptToRebookFromManualAppointment();

        $this->assertEquals($expectedLiveAppointment, $returned->getLiveAppointment());
        $this->assertEquals($expectedManualAppointment, $returned->getManualAppointment());
        $this->assertEquals("some engineer notes", $returned->getNotes());
    }

    public function getDataForManualAppointment()
    {
        return array(
            array(
                null,
                self::BOOKED_APPOINTMENT_ARRAY,
                self::BOOKED_APPOINTMENT_ARRAY,
                null
            ),
            array(
                self::ARR_ADDRESS,
                self::BOOKED_APPOINTMENT_ARRAY,
                self::BOOKED_APPOINTMENT_ARRAY,
                null
            ),
            array(
                null,
                null,
                null,
                self::MANUAL_APPOINTMENT_ARRAY
            ),
            array(
                self::ARR_ADDRESS,
                null,
                null,
                self::MANUAL_APPOINTMENT_ARRAY
            )
        );
    }
}
