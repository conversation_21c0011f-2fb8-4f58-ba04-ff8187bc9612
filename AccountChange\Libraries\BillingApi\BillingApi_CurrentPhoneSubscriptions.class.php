<?php

use Plusnet\BillingApiClient\Entity\CustomerProductSubscription;

class AccountChange_BillingApi_CurrentPhoneSubscriptions
{
    /* @var CustomerProductSubscription */
    private $currentLineRentalSubscription;

    /* @var CustomerProductSubscription */
    private $currentCallPlanSubscription;

    /**
     * AccountChange_AccountChangeApi constructor.
     *
     * @param CustomerProductSubscription $currentLineRentalSubscription
     * @param CustomerProductSubscription $currentCallPlanSubscription
     */
    public function __construct($currentLineRentalSubscription, $currentCallPlanSubscription)
    {
        if ($currentLineRentalSubscription !== null &&
            !($currentLineRentalSubscription instanceof Plusnet\BillingApiClient\Entity\CustomerProductSubscription)) {
            throw new AccountChange_BillingApi_SubscriptionHelperException(
                'currentLineRentalSubscription is not of type CustomerProductSubscription');
        }

        if ($currentCallPlanSubscription !== null &&
            !($currentCallPlanSubscription instanceof Plusnet\BillingApiClient\Entity\CustomerProductSubscription)) {
            throw new AccountChange_BillingApi_SubscriptionHelperException(
                'currentCallPlanSubscription is not of type CustomerProductSubscription');
        }

        $this->currentLineRentalSubscription = $currentLineRentalSubscription;
        $this->currentCallPlanSubscription = $currentCallPlanSubscription;
    }

    /**
     * @return CustomerProductSubscription
     */
    public function getCurrentLineRentalSubscription()
    {
        return $this->currentLineRentalSubscription;
    }

    /**
     * @return CustomerProductSubscription
     */
    public function getCurrentCallPlanSubscription()
    {
        return $this->currentCallPlanSubscription;
    }
}
