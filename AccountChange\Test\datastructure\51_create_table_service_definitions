CREATE TABLE `products`.`service_definitions` (
  `service_definition_id` int(8) unsigned zerofill NOT NULL auto_increment,
  `intProductVariantId` smallint(5) unsigned default NULL,
  `name` varchar(200) NOT NULL default '',
  `isp` varchar(10) NOT NULL default '',
  `minimum_charge` decimal(8,4) default NULL,
  `date_created` date NOT NULL default '0000-00-00',
  `db_src` varchar(4) NOT NULL default '',
  `time_stamp` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  `requires` varchar(100) default '',
  `initial_charge` decimal(8,2) default '0.00',
  `decUpgradeCharge` decimal(8,2) NOT NULL default '0.00',
  `type` enum('other','free','residential','business','colease','non-corporate','hostmaster') NOT NULL default 'other',
  `creator_id` varchar(32) NOT NULL default '',
  `password_visible_to_support` enum('Y','N') NOT NULL default 'Y',
  `end_date` datetime default NULL,
  `signup_via_portal` enum('default','Y','N') default NULL,
  `bt_product_id` int(10) unsigned default NULL,
  `blurb` text NOT NULL,
  PRIMARY KEY  (`service_definition_id`),
  KEY `name` (`name`,`isp`),
  KEY `type` (`type`),
  KEY `bt_product_id` (`bt_product_id`),
  KEY `isp` (`isp`),
  KEY `idxProductVariantId` (`intProductVariantId`)
)