<?php

use Plusnet\BillingApiClient\Entity\Request\ProductOfferingPricePointPair;
use AccountChange_BillingApi_BasePriceHelper as BasePriceHelper;
use AccountChange_BillingApi_ProductOfferingPricePointIdHelper as ProductOfferingPricePointIdHelper;

class AccountChange_EmailHandler_PriceHelper
{
    /**
     * Handles used to get component information for line rental
     *
     * @var string
     */
    const MONTHLY_CONTRACT_HANDLE       = 'MONTHLY';
    const MONTHLY_PAYMENT_HANDLE        = 'MONTHLY';
    const SUBSCRIPTION_COMPONENT_HANDLE = 'SUBSCRIPTION';

    /**
     * Keys used multiple times to retrieve and add values from/to arrays
     *
     * @var string
     */
    const SERVICE_COMPONENT_ID_KEY = 'intServiceComponentID';
    const TARIFF_ID_KEY            = 'intTariffID';

    /**
     * Keys for base price and base price in contract values returned from Billing API getCustomerAvailableProducts()
     *
     * @var string
     */
    const CURRENT_BASE_PRICE = 'currentBasePrice';

    /**
     * Array of component statuses.
     * Call features with these statuses are currently being charged for so we need to get the prices for them.
     *
     * @var array
     */
    const ACTIVE_STATUS = 4;
    const QUEUED_DEACTIVATE_STATUS = 5;
    const CALL_FEATURE_COMPONENT_STATUSES = array(self::ACTIVE_STATUS, self::QUEUED_DEACTIVATE_STATUS);

    private $serviceId;
    private $marketId;
    private $serviceDefinitionId;
    private $wlrServiceComponentId;
    private $promotionCode;
    private $wlrComponentInstanceId;

    /**
     * Format of the returned array
     *
     * @var array
     */
    private $newPackagePrices = array(
        'broadband'        => null,
        'lineRental'       => null,
        'callPlan'         => null,
        'callFeatures'     => null,
        'total'            => null,
        'discountDuration' => null,
        'discountAmount'   => null
    );

    /**
     * AccountChange_EmailHandlerPriceHelper constructor.
     *
     * @param $serviceId
     * @param $marketId
     * @param $serviceDefinitionId
     * @param $wlrServiceComponentId
     * @param $promotionCode
     * @param $wlrComponentInstanceId
     */
    public function __construct(
        $serviceId,
        $marketId,
        $serviceDefinitionId,
        $wlrServiceComponentId,
        $promotionCode,
        $wlrComponentInstanceId
    )
    {
        $this->serviceId              = $serviceId;
        $this->marketId               = $marketId;
        $this->serviceDefinitionId    = $serviceDefinitionId;
        $this->wlrServiceComponentId  = $wlrServiceComponentId;
        $this->promotionCode          = $promotionCode;
        $this->wlrComponentInstanceId = $wlrComponentInstanceId;
    }

    /**
     * Get new package prices to display in the email
     *
     * @return array
     */
    public function getNewPackagePrices()
    {
        $broadbandPrice    = $this->getBroadbandPrice();
        $lineRentalPrice   = $this->getLineRentalPrice();
        $callPlanPrice     = $this->getCallPlanPrice();
        $callFeaturesPrice = $this->getCallFeaturesPrice();
        $totalPrice        =
            $broadbandPrice
            + $lineRentalPrice
            + $callPlanPrice
            + $callFeaturesPrice;

        $this->newPackagePrices['broadband']    = $broadbandPrice;
        $this->newPackagePrices['lineRental']   = $lineRentalPrice;
        $this->newPackagePrices['callPlan']     = $callPlanPrice;
        $this->newPackagePrices['callFeatures'] = $callFeaturesPrice;
        $this->newPackagePrices['total']        = $totalPrice;

        if (!empty($this->promotionCode)) {

            $this->getDiscountData($broadbandPrice);
        }

        return $this->newPackagePrices;
    }

    private function getBroadbandPrice()
    {
        if (empty($this->serviceDefinitionId)) {

            return 0;
        }

        $productOfferingId = $this->getServiceComponentIdFromServiceDefinitionIdAndMarketId(
            $this->serviceDefinitionId,
            $this->marketId);

        $pricePointId = $this->getTariffIdFromMarketIdAndServiceDefinitionId(
            $this->marketId,
            $this->serviceDefinitionId);

        $productOfferingPricePointPair = BasePriceHelper::createProductOfferingPricePointPairFromIds(
            $this->serviceDefinitionId,
            $productOfferingId,
            $pricePointId);

        $productOfferingPricePointId = $this->generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

        $basePrices = $this->getBasePrices($this->serviceId, array($productOfferingPricePointPair));
        return $basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE];
    }

    private function getLineRentalPrice()
    {
        if (empty($this->wlrServiceComponentId)) {

            return 0;
        }

        $pricePointId = $this->getProductComponentTariffIdForWlrServiceComponent();
        $productOfferingPricePointPair = new ProductOfferingPricePointPair($this->wlrServiceComponentId, $pricePointId);
        $productOfferingPricePointId = $this->generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

        $basePrices = $this->getBasePrices($this->serviceId, array($productOfferingPricePointPair));
        return $basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE];
    }

    private function getCallPlanPrice()
    {
        if (empty($this->wlrServiceComponentId)) {

            return 0;
        }

        $callPlanDetails = $this->getCallPlanDetailsByWlrServiceComponentId($this->wlrServiceComponentId);

        $productOfferingId = $callPlanDetails[self::SERVICE_COMPONENT_ID_KEY];
        $pricePointId      = $callPlanDetails[self::TARIFF_ID_KEY];

        $productOfferingPricePointPair = new ProductOfferingPricePointPair($productOfferingId, $pricePointId);
        $productOfferingPricePointId   = $this->generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);

        $basePrices = $this->getBasePrices($this->serviceId, array($productOfferingPricePointPair));
        return $basePrices[$productOfferingPricePointId][self::CURRENT_BASE_PRICE];
    }

    private function getCallFeaturesPrice()
    {
        if (empty($this->wlrComponentInstanceId)) {

            return 0;
        }

        $wlrComponent = $this->createComponentInstance($this->wlrComponentInstanceId);

        $productOfferingPricePointPairs = array();
        $productOfferingPricePointPairs = $this->getBundleableCallFeaturesProductOfferingPricePointPairs(
            $productOfferingPricePointPairs,
            $wlrComponent
        );
        $productOfferingPricePointPairs = $this->getNonBundleableCallFeaturesProductOfferingPricePointPairs(
            $productOfferingPricePointPairs,
            $wlrComponent
        );

        $callFeaturesPrice = 0;
        $basePrices = $this->getBasePrices($this->serviceId, $productOfferingPricePointPairs);

        foreach ($basePrices as $basePrice) {

            $callFeaturesPrice += $basePrice[self::CURRENT_BASE_PRICE];
        }

        return $callFeaturesPrice;
    }

    private function getBundleableCallFeaturesProductOfferingPricePointPairs($productOfferingPricePointPairs, $wlrComponent)
    {
        $callFeaturesBundle = $wlrComponent->getActiveCallFeaturesBundle();
        if (!empty($callFeaturesBundle)) {

            $productOfferingId = $callFeaturesBundle->getServiceComponentID();
            $pricePointId      = $callFeaturesBundle->getTariffID();

            $productOfferingPricePointPairs[] = new ProductOfferingPricePointPair($productOfferingId, $pricePointId);
        } else {

            $individualCallFeatures = $wlrComponent->getChargableBundlableCallFeatures();
            foreach ($individualCallFeatures as $id) {

                $callFeature       = $this->createProductComponentInstance($id);
                $productOfferingId = $callFeature->getServiceComponentID();
                $pricePointId      = $callFeature->getTariffID();

                $productOfferingPricePointPairs[] = new ProductOfferingPricePointPair($productOfferingId, $pricePointId);
            }
        }

        return $productOfferingPricePointPairs;
    }

    private function getNonBundleableCallFeaturesProductOfferingPricePointPairs($productOfferingPricePointPairs, $wlrComponent)
    {
        $nonBundledCallFeatures = $wlrComponent->getSelectedProductComponentInstanceIDs(
            $this->getNoBundleableCallFeatures($wlrComponent->getComponentTypeID()),
            self::CALL_FEATURE_COMPONENT_STATUSES);

        if (is_array($nonBundledCallFeatures)) {

            foreach ($nonBundledCallFeatures as $id) {

                $callFeature       = $this->createProductComponentInstance($id);
                $productOfferingId = $callFeature->getServiceComponentID();
                $pricePointId      = $callFeature->getTariffID();

                $productOfferingPricePointPairs[] = new ProductOfferingPricePointPair($productOfferingId, $pricePointId);
            }
        }

        return $productOfferingPricePointPairs;
    }

    protected function getNoBundleableCallFeatures($componentTypeId)
    {
        return CWlrCallFeature::getNoBundleableCallFeatures($componentTypeId);
    }

    protected function getProductComponentTariffIdForWlrServiceComponent()
    {
        $databaseAdaptor = Db_Manager::getAdaptor('AccountChange');
        return $databaseAdaptor->getProductComponentTariffId(
            self::MONTHLY_CONTRACT_HANDLE,
            self::MONTHLY_PAYMENT_HANDLE,
            $this->wlrServiceComponentId,
            self::SUBSCRIPTION_COMPONENT_HANDLE
        );
    }

    /**
     * Wrapper function for unit testing
     *
     * @param string $id
     *
     * @return CComponent
     */
    protected function createComponentInstance($id)
    {
        return CComponent::createInstance($id);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param string $id
     *
     * @return CProductComponent
     */
    protected function createProductComponentInstance($id)
    {
        return CProductComponent::createInstance($id);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param string $marketId
     * @param string $serviceDefinitionId
     *
     * @return string
     */
    protected function getTariffIdFromMarketIdAndServiceDefinitionId($marketId, $serviceDefinitionId)
    {
        $databaseAdaptor = Db_Manager::getAdaptor('AccountChange');
        return $databaseAdaptor->getTariffId($marketId, $serviceDefinitionId);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param string $serviceDefinitionId
     * @param string $marketId
     *
     * @return string|null
     */
    protected function getServiceComponentIdFromServiceDefinitionIdAndMarketId($serviceDefinitionId, $marketId)
    {
        try {
            return ProductFamily_InternetConnectionHelper::getId((int) $serviceDefinitionId, (int) $marketId);
        } catch (Exception $e) {
            /**
             * Legacy service definition based products cause a generic exception to be thrown.
             *
             * In this case it is valid to return null for the new service component ID.
             **/
            Dbg_Dbg::write(
                "Could not get service component id from Service Definition ID [$serviceDefinitionId] and Market ID 
                [$marketId].  The exception message was: " . $e->getMessage(), 'AccountChange');

            return null;
        }
    }

    /**
     * Wrapper function for unit testing
     *
     * @param string $serviceId
     * @param array  $callPlanPricePointPairs
     *
     * @return array
     */
    protected function getBasePrices($serviceId, $callPlanPricePointPairs)
    {
        return BasePriceHelper::getBasePrices($serviceId, $callPlanPricePointPairs);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param string $serviceComponentId
     *
     * @return array
     */
    protected function getCallPlanDetailsByWlrServiceComponentId($serviceComponentId)
    {
        return CWlrProduct::getCallPlanDetailsFromServiceComponentId($serviceComponentId);
    }

    /**
     * Wrapper function for unit testing
     *
     * @param $broadbandPrice
     *
     * @return AccountChange_EmailHandler_PromotionHelper
     */
    protected function getPromotionHelper($broadbandPrice)
    {
        return new AccountChange_EmailHandler_PromotionHelper(
            $this->serviceId,
            $this->promotionCode,
            $broadbandPrice,
            $this->serviceDefinitionId);
    }

    /**
     * Wrapper function for unit testing - merges the result of PromotionHelper::getDiscountData with the price array
     *
     * @param integer $broadbandPrice
     *
     * @return void
     */
    private function getDiscountData($broadbandPrice)
    {
        $promotionHelper = $this->getPromotionHelper($broadbandPrice);

        $discountData = $promotionHelper->getDiscountData();
        if ($discountData !== null) {

            $this->newPackagePrices = array_merge($this->newPackagePrices, $discountData);
        }
    }

    private function generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair)
    {
        return ProductOfferingPricePointIdHelper::generateIdFromProductOfferingPricePointPair($productOfferingPricePointPair);
    }
}