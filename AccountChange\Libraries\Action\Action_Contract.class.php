<?php
/**
 * Contract Action
 *
 * Action that performs the resetting of the 12 month contract for broadband (currently)
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2008 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
/**
 * AccountChange_Action_Contract class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2008 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Action_Contract extends AccountChange_Action
{
    /**
     * Boolean to check Account Change is Schedule or Immediate
     *
     * @var boolean
     **/
    private $bolSchedule;

    /**
     * JohnLewis visp
     *
     * @var string
     */
    const JOHNLEWIS_VISP = 'johnlewis';

    /**
     * @var string
     */
    const CONTRACT_DEFINITION_ID = 'contractDefinitionId';

    /**
     * @var string
     */
    const AGREEMENT_DATE = 'agreementDate';

    /**
     * @var string
     */
    const CONTRACT_TYPE = 'contractType';

    /**
     * @var string
     */
    const CONTRACT_SUB_TYPE = 'contractSubType';

    const BOL_SCHEDULE = 'bolSchedule';
    const BOL_HOUSE_MOVE = 'bolHousemove';
    const PROVISIONING_PROFILE = 'provisioningProfile';

    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $registry = AccountChange_Registry::instance();
        $this->bolSchedule = $registry->getEntry(self::BOL_SCHEDULE);

        if (!empty($this->arrOptions[self::BOL_HOUSE_MOVE])) {
            return;
        }

        // Get the visp
        $objDatabase = Db_Manager::getAdaptor('Core');
        $arrDetails = $objDatabase->getServiceDefinitionDetailsForService($this->intServiceId);

        if ($arrDetails['isp'] != self::JOHNLEWIS_VISP) {
            $this->restartDeferredContracts($arrDetails);
        }
    }

    /**
     * Restart deferred contracts for Greenbee and Waitrose Customers
     *
     * @param array $arrDetails Service Definition Detail
     *
     * @return void
     */
    protected function restartDeferredContracts(array $arrDetails)
    {
        // In order to deal with the legacy and framework code locking each other
        // it was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        if (!empty($arrDetails[self::PROVISIONING_PROFILE]) && $arrDetails[self::PROVISIONING_PROFILE] === 'FTTC') {
            $this->createFibreContract($this->intServiceId);
        }
    }

    /**
     * Replace existing profit forgone contracts with new ones during account change.
     *
     * This is currently only being used by ('greenbee', 'waitrose', 'johnlewis') as per declarations
     * in restartDeferredContracts()
     *
     * @param int $serviceId Service id of the customer
     *
     * @return void
     */
    protected function createContract($serviceId)
    {
        /** @var Contract $contractsClient */
        $contractsClient = BusTier_BusTier::getClient('contracts')
            ->setServiceId($serviceId);

        $availableContracts = $contractsClient->getAllActiveContracts();

        $registry = AccountChange_Registry::instance();

        foreach ($availableContracts as $contract) {
            foreach ($contract->getContractedServices() as $contractedService) {
                if (\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_CANCELLATION
                    === $contractedService->getSubjectHandle()
                ) {
                    $contractsClient->withdrawContractedService(
                        $contract,
                        $contractedService,
                        'Withdrawn as part of Account Change'
                    );
                }
            }
        }

        $allowedContractDefinitions = $contractsClient->getAllContractDefinitions();

        foreach ($allowedContractDefinitions as $contractDefinition) {
            if (\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_CANCELLATION
                === $contractDefinition->getTypeHandle()
            ) {
                $options = [
                    self::CONTRACT_DEFINITION_ID => (int)$contractDefinition->getId(),
                    self::AGREEMENT_DATE => $registry->getEntry(self::AGREEMENT_DATE),
                    self::CONTRACT_TYPE => $registry->getEntry(self::CONTRACT_TYPE),
                    self::CONTRACT_SUB_TYPE => $registry->getEntry(self::CONTRACT_SUB_TYPE)
                ];

                $contractsClient->createActiveContract($options, null, 'ACCOUNT_CHANGE', $this->bolSchedule);
            }
        }
    }

    /**
     * Create the deferred contract for Fibre products
     *
     * @param int $intServiceId Service Id
     *
     * @return void
     */
    protected function createFibreContract($intServiceId)
    {
        $contractsClient = BusTier_BusTier::getClient('contracts')
            ->setServiceId($intServiceId)
            ->setRaiseServiceNotice(false);

        $allowedContractDefinitions = $contractsClient->getAllContractDefinitions();

        $registry = AccountChange_Registry::instance();

        if (!empty($allowedContractDefinitions)) {
            $bolActivationContract = $registry->getEntry('bolActivationContract');

            foreach ($allowedContractDefinitions as $contractDefinition) {
                if (\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_CANCELLATION
                    === $contractDefinition->getTypeHandle()
                ) {
                    $options = [
                        self::CONTRACT_DEFINITION_ID => (int)$contractDefinition->getId()
                    ];

                    $contractsClient->createActiveContract($options, null, 'ACCOUNT_CHANGE', $this->bolSchedule);
                }

                // Some business logic: Only add the Fibre activation contract if the customer has
                // WLR, either existing or new.  If they don't have WLR, then the fee is taken up-front.
                if ((\Plusnet\ContractsClient\Entity\ContractTypeHandle::DEFERRED_ACTIVATION
                        === $contractDefinition->getTypeHandle()) && $bolActivationContract
                ) {
                    $options = [
                        self::CONTRACT_DEFINITION_ID => (int)$contractDefinition->getId(),
                        self::AGREEMENT_DATE => $registry->getEntry(self::AGREEMENT_DATE),
                        self::CONTRACT_TYPE => $registry->getEntry(self::CONTRACT_TYPE)
                    ];

                    $contractsClient->createActiveContract($options, null, 'ACCOUNT_CHANGE', $this->bolSchedule);
                }
            }
        }
    }
}
