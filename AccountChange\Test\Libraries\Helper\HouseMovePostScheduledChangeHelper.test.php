<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

use Plusnet\HouseMoves\Models\HouseMove;
use Plusnet\HouseMoves\Services\ComponentsService;
use Plusnet\HouseMoves\Services\HouseMoveScheduledAccountChangeService;
use Plusnet\HouseMoves\Services\HouseMoveService;
use Plusnet\HouseMoves\Services\ServiceManager;
use Plusnet\HouseMoves\Services\ServiceNoteService;

class AccountChange_HouseMovePostScheduledChangeHelperTest extends PHPUnit_Framework_TestCase
{
    const MOCK_SERVICE_ID = ********;
    const MOCK_COMPONENT_TYPE_ID = 1234;
    const MOCK_TARIFF_ID = 4321;

    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|HouseMoveService
     */
    private $mockHouseMoveService;

    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|HouseMoveScheduledAccountChangeService
     */
    private $mockHouseMoveScheduledChangeService;

    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|ServiceNoteService
     */
    private $mockServiceNoteService;

    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|ComponentsService
     */
    private $mockComponentsService;
    /**
     * @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|HouseMove
     */
    private $mockHouseMove;


    /**
     * setUp
     */
    public function setUp()
    {
        $this->mockHouseMoveService = Mockery::mock(HouseMoveService::class);
        $this->mockHouseMoveScheduledChangeService = Mockery::mock(HouseMoveScheduledAccountChangeService::class);
        $this->mockServiceNoteService = Mockery::mock(ServiceNoteService::class);
        $this->mockComponentsService = Mockery::mock(ComponentsService::class);
        $this->mockHouseMove = Mockery::mock(HouseMove::class);
        ServiceManager::setService('ComponentsService', $this->mockComponentsService);
        ServiceManager::setService('HouseMoveService', $this->mockHouseMoveService);
        ServiceManager::setService('HouseMoveScheduledAccountChangeService', $this->mockHouseMoveScheduledChangeService);
        ServiceManager::setService('ServiceNoteService', $this->mockServiceNoteService);
    }

    /**
     * tearDown
     */
    public function tearDown()
    {
        ServiceManager::reset();
        \Mockery::close();
    }

    /**
     * @test
     * @dataProvider wlrAndHouseMoveProvider
     */
    public function shouldCreateWlrComponentForHouseMoveAndUpdateHouseMoveCorrectly(
        $createPhoneReturn,
        $contractDuration,
        $shouldCreatePhone,
        $expectedRecontractValue,
        $siOrderReference
    ) {
        $productComponentOptionsReturn = ['productComponentOptions' => 'a value'];

        $this->mockHouseMoveService
            ->shouldReceive('getHouseMoveByServiceId')
            ->once()
            ->with(static::MOCK_SERVICE_ID)
            ->andReturn($this->mockHouseMove);

        $this->mockHouseMove
            ->shouldReceive('getSiOrderReference')
            ->andReturn($siOrderReference);

        if ($shouldCreatePhone) {
            $this->mockComponentsService
                ->shouldReceive('getProductComponentOptionsByServiceId')
                ->once()
                ->with(static::MOCK_COMPONENT_TYPE_ID, static::MOCK_SERVICE_ID)
                ->andReturn($productComponentOptionsReturn);
        } else {
            $this->mockComponentsService
                ->shouldReceive('getProductComponentOptionsByServiceId')
                ->never();
        }

        $this->mockHouseMoveScheduledChangeService
            ->shouldReceive('insertHouseMoveScheduledAccountChange')
            ->once()
            ->with(static::MOCK_SERVICE_ID);

        $this->mockServiceNoteService->shouldReceive('create')
            ->with(static::MOCK_SERVICE_ID);

        if ($contractDuration !== null) {
            $this->mockHouseMove->shouldReceive('setContractDuration')
                ->once()
                ->with((int)$contractDuration);

            $this->mockHouseMove->shouldReceive('setHasRecontract')
                ->once()
                ->with($expectedRecontractValue);

            $this->mockHouseMoveService->shouldReceive('save')
                ->once()
                ->with($this->mockHouseMove);

        }

        $test = $this->getTest($contractDuration);

        $test->shouldReceive('includeLegacyFiles')
            ->once();

        $test
            ->shouldReceive('getChargeableComponents')
            ->once()
            ->andReturn($createPhoneReturn);

        if ($shouldCreatePhone) {
            $test
                ->shouldReceive('createWlr3Component')
                ->once()
                ->with(static::MOCK_COMPONENT_TYPE_ID, static::MOCK_TARIFF_ID, $productComponentOptionsReturn);
        } else {
            $test
                ->shouldReceive('createWlr3Component')
                ->never();
        }

        $test->execute();
    }

    /**
     * @return array
     */
    public function wlrAndHouseMoveProvider()
    {
        $activePhoneComponent = [
            [
                'vchComponentTypeHandle' => 'WLR',
                'status' => 'active',
                'vchHandle' => 'SUBSCRIPTION',
                'component_type_id' => static::MOCK_COMPONENT_TYPE_ID,
                'intTariffID' => static::MOCK_TARIFF_ID,

            ]
        ];

        $unconfiguredPhoneComponent = [
            [
                'vchComponentTypeHandle' => 'WLR',
                'status' => 'unconfigured',
                'vchHandle' => 'SUBSCRIPTION',
                'component_type_id' => static::MOCK_COMPONENT_TYPE_ID,
                'intTariffID' => static::MOCK_TARIFF_ID,

            ]
        ];

        $queuedActivatePhoneComponent = [
            [
                'vchComponentTypeHandle' => 'WLR',
                'status' => 'queued-activate',
                'vchHandle' => 'SUBSCRIPTION',
                'component_type_id' => static::MOCK_COMPONENT_TYPE_ID,
                'intTariffID' => static::MOCK_TARIFF_ID,

            ]
        ];

        $activeNotSubscription = [
            [
                'vchComponentTypeHandle' => 'WLR',
                'status' => 'active',
                'vchHandle' => 'Something else',
                'component_type_id' => static::MOCK_COMPONENT_TYPE_ID,
                'intTariffID' => static::MOCK_TARIFF_ID,

            ]
        ];

        $activeNoCompId = [
            [
                'vchComponentTypeHandle' => 'WLR',
                'status' => 'active',
                'vchHandle' => 'SUBSCRIPTION',
                'component_type_id' => null,
                'intTariffID' => static::MOCK_TARIFF_ID,
            ]
        ];

        return [
            'WLR Create, Has Recontract' => [$activePhoneComponent,'12',true, true, true, '9876'],
            'WLR Create, No Recontract' => [$activePhoneComponent,'0',true, false, '9876'],
            'WLR Create null contract value' => [$activePhoneComponent, null, true, null, '9876'],
            'No WLR Create, unconfigured wlr, Has Recontract' => [$unconfiguredPhoneComponent, '12', false, true, null],
            'No WLR Create, queued wlr, Has Recontract' => [$queuedActivatePhoneComponent, '12', false, true, null],
            'No WLR Create, active wlr no sub, Has Recontract' => [$activeNotSubscription, '12', false, true, null],
            'No WLR Create, active wlr no compid, Has Recontract' => [$activeNoCompId, '12', false, true, '9876'],
            'No WLR Create, active wlr, no si order, Has Recontract' => [$activeNoCompId, '12', false, true, '9876'],
            'No WLR Create, unconfigured wlr, No recontact' => [$unconfiguredPhoneComponent, '0', false, false, null],
            'No WLR Create, queued wlr, No recontact' => [$queuedActivatePhoneComponent, '0', false, false, null],
            'No WLR Create, active wlr no sub, No recontact' => [$activeNotSubscription, '0', false, false, null],
            'No WLR Create, active wlr no compid, No recontact' => [$activeNoCompId, '0', false, false, '9876'],
            'No WLR Create, active wlr, no si order, No recontact' => [$activeNoCompId, '0', false, false, '9876'],
            'No WLR Create, unconfigured wlr, null contract' => [$unconfiguredPhoneComponent, null, false, null, null],
            'No WLR Create, queued wlr, null contract' => [$queuedActivatePhoneComponent, null, false, null, null],
            'No WLR Create, active wlr no sub, null contract' => [$activeNotSubscription, null, false, null, null],
            'No WLR Create, active wlr no compid, null contract' => [$activeNoCompId, null, false, null, '9876']
        ];
    }

    /**
     * @param int $contractDuration contract duration
     * @return AccountChange_Action_RegisterCharges|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private function getTest($contractDuration)
    {
        return \Mockery::mock(
            'AccountChange_HouseMovePostScheduledChangeHelper[getChargeableComponents,createWlr3Component,includeLegacyFiles]',
            [static::MOCK_SERVICE_ID, $contractDuration]
        )
            ->shouldAllowMockingProtectedMethods();
    }
}
