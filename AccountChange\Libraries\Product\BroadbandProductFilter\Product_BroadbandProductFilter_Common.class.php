<?php
/**
 * AccountChange_Product_BroadbandProductFilter_Common.php
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Defines a broadband product filter
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 */
abstract class AccountChange_Product_BroadbandProductFilter_Common
{
    /**
     * @var bool Force dual play products to be allowed or disallowed
     */
    protected $forceAllowDualPlayProducts = null;

    /**
     * @var bool Force solus products to be allowed or disallowed
     */
    protected $forceAllowSolusProducts = null;

    /**
     * Filter available products to the ones the user is allowed to select.
     * Implementations should call commonFilter, normally first
     *
     * @param LineCheck_Result $lineCheck         object representing the line check
     *                                            Some methods are in LineCheck_ResultDao, called using __call magic
     * @param array            $availableProducts All available products
     * @param array            $currentProduct    User's current product
     * @param string           $campaignCode      Campaign code indicating which landing page this journey originated from, if any
     *
     * @return array Filtered and categorised products
     */
    abstract public function filter(LineCheck_Result $lineCheck, array $availableProducts, array $currentProduct, $campaignCode);

    /**
     * Method to apply common filters to available broadband products for Portal and Workplace Account Change tool.
     *
     * @param LineCheck_Result $lineCheck         object representing the line check
     *                                            Some methods are in LineCheck_ResultDao, called using __call magic
     * @param array            $availableProducts All available products
     * @param array            $currentProduct    current product
     *
     * @return array Returns filtered set of products in the following format
     */
    protected function commonFilter(LineCheck_Result $lineCheck, array $availableProducts, array $currentProduct)
    {
        $currentProductId = intval($currentProduct['intSdi']);

        $availableBizGoLargeProducts = self::getAvailableBizGoLargeProducts($availableProducts);
        if (!empty($availableBizGoLargeProducts)) {
            return $availableBizGoLargeProducts;
        }

        // Don't show fibre extra products if the line's max speed is less than 40000kbps
        // unless the current product is already fibre extra
        if ($currentProductId != AccountChange_Product_BroadbandProductFilter::APR_14_FIBRE_EXTRA_WITH_PHONE
            && $currentProductId != AccountChange_Product_BroadbandProductFilter::APR_14_FIBRE_EXTRA_NO_PHONE) {
            if ($lineCheck->getFttcUncappedDownSpeed() != null && $lineCheck->getFttcUncappedDownSpeed() <= 40000) {
                foreach ($availableProducts as $key => $availableProduct) {
                    $sidId = intval($availableProduct['intSdi']);
                    if ($sidId == AccountChange_Product_BroadbandProductFilter::APR_14_FIBRE_EXTRA_WITH_PHONE
                        || $sidId == AccountChange_Product_BroadbandProductFilter::APR_14_FIBRE_EXTRA_NO_PHONE) {
                        unset($availableProducts[$key]);
                    }
                }
            }
        }
        return $availableProducts;
    }

    /**
     * remove solus products
     *
     * @param array $products products
     *
     * @return array products
     */
    protected static function removeSolusProducts(array $products, $exceptSdi = null)
    {
        foreach ($products as $key => $product) {
            if ($exceptSdi !== null && $exceptSdi == $product['intSdi']) {
                continue;
            }

            if (isset($product['productFamily'])
                && $product['productFamily']->isSolus()
            ) {
                unset($products[$key]);
            }
        }

        return $products;
    }

    /**
     * removes all unlimited product from account change
     *
     * @param array $products               All available products
     * @param array $currentProductVariants ProductVariants to remove
     *
     * @return array All available products
     */
    protected static function removeUnlimitedProducts(array $products, $currentProductVariants)
    {
        foreach ($products as $key => $product) {
            if (isset($product['productFamily'])
                && ($product['productFamily']->isCbcProduct() == false)
                && (!in_array($product['intSdi'], $currentProductVariants))
            ) {
                unset($products[$key]);
            }
        }

        return $products;
    }

    protected static function removeUnlimitedFibreExtraProducts(array $availableProducts)
    {
        foreach ($availableProducts as $key => $availableProduct) {
            $serviceDefinitionId = intval($availableProduct['intSdi']);
            if ($serviceDefinitionId == AccountChange_Product_BroadbandProductFilter::APR_14_FIBRE_EXTRA_WITH_PHONE ||
                $serviceDefinitionId == AccountChange_Product_BroadbandProductFilter::APR_14_FIBRE_EXTRA_NO_PHONE) {
                unset($availableProducts[$key]);
            }
        }

        return $availableProducts;
    }

    /**
     * Should legacy products be displayed
     *
     * This is a tactical fix. A technical debt ticket has been raised to remove hardcoded product families and ISPs.
     * http://jira.internal.plus.net/browse/TECHDEBT-11
     *
     * @param boolean                     $isCurrentProductLegacy Is current product a legacy one
     * @param ProductFamily_ProductFamily $currentProductFamily   Current product family
     * @param ProductFamily_ProductFamily $newProductFamily       New product family
     * @param string                      $isp                    Current isp
     *
     * @return bool
     */
    protected function shouldAllowLegacyProducts(
        $isCurrentProductLegacy,
        $currentProductFamily,
        $newProductFamily,
        $isp
    ) {
        $isCurrentFamilyBBYW = ($currentProductFamily instanceof ProductFamily_BroadbandYourWay);
        $isNewFamilyBBYW = ($newProductFamily instanceof ProductFamily_BroadbandYourWay);
        $isCurrentIspMaaf = ($isp == 'madasafish');

        return ($isCurrentProductLegacy && (($isCurrentFamilyBBYW && $isNewFamilyBBYW) || $isCurrentIspMaaf));
    }

    /**
     * Should solus products be displayed
     * Defaults to true if and only if the current product is solus
     *
     * @param array $currentProduct The current product
     *
     * @return bool
     */
    protected function shouldAllowSolusProducts(array $currentProduct)
    {
        if (isset($this->forceAllowSolusProducts)) {
            return $this->forceAllowSolusProducts;
        }

        return (!$currentProduct['hasPhone']);
    }

    /**
     * Should dual play products be displayed
     *
     * @param array $currentProduct The current product
     *
     * @return bool Default true
     */
    protected function shouldAllowDualPlayProducts(array $currentProduct)
    {
        if (isset($this->forceAllowDualPlayProducts)) {
            return $this->forceAllowDualPlayProducts;
        }

        return true;
    }

    /**
     * Should JLBB products be displayed
     *
     * @param array $currentProduct The current product
     *
     * @return bool Default true
     */
    protected function shouldAllowJohnLewisProducts(array $currentProduct)
    {
        return true;
    }

    /**
     * Calls into product change plan client to get the solus version of a dual play product
     * and vice versa
     *
     * @param int $serviceDefinitionId Service definition id
     *
     * @return array
     **/
    public function getServiceDefinitionVariants($serviceDefinitionId)
    {
        $client = \BusTier_BusTier::getClient('productChangePlan');
        return $client->getServiceDefinitionVariants($serviceDefinitionId);
    }

    /**
     * Force dual play products to be displayed or hidden, regardless of other requirements
     *
     * @param bool $forceAllowDualPlayProducts True to display all DP products, false to hide them, null to reset
     *
     * @return void
     */
    public function setForceAllowDualPlayProducts($forceAllowDualPlayProducts)
    {
        $this->forceAllowDualPlayProducts = $forceAllowDualPlayProducts;
    }

    /**
     * Force solus products to be displayed or hidden, regardless of other requirements
     *
     * @param bool $forceAllowSolusProducts True to display all solus products, false to hide them, null to reset
     *
     * @return void
     */
    public function setForceAllowSolusProducts($forceAllowSolusProducts)
    {
        $this->forceAllowSolusProducts = $forceAllowSolusProducts;
    }

    /**
     * @param array $availableProducts available products
     * @return array
     */
    final public static function getAvailableBizGoLargeProducts($availableProducts)
    {
        foreach ($availableProducts as $key => $product) {
            if (!empty($product['productFamily']) && !($product['productFamily'] instanceof ProductFamily_BizGoLarge)) {
                unset($availableProducts[$key]);
            }
        }
        return $availableProducts;
    }
}
