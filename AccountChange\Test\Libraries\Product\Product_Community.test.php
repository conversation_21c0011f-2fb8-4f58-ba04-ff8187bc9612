<?php
/**
 * Community Product Configuration
 *
 * Testing class for the AccountChange_Product_Community class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Product_Community.test.php,v 1.2 2009-01-27 09:07:53 bselby Exp $
 * @since      File available since 2008-08-28
 */
/**
 * Community Product Configuration Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Product_Community_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for Service Id
     *
     * @var int
     */
    private $intServiceId;

    /**
     * Fixture for the options
     *
     * @var array
     */
    private $arrOptions;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        $this->intServiceId = 9999;
        $this->arrOptions = array('bolFreeProduct' => true);
    }

    /**
     * @covers AccountChange_Product_Community::isKeyProduct
     */
    public function testKeyProductIsFalse()
    {
        $objCommunity = new AccountChange_Product_Community(1, AccountChange_Product_Manager::ACTION_NONE);

        $this->assertFalse($objCommunity->isKeyProduct());
    }

    /**
     * @covers AccountChange_Product_Community::refresh
     */
    public function testExecuteCallsRefreshWhenTheActionIsRefresh()
    {
        $objCommunity = $this->getMock('AccountChange_Product_Community',
                                       array('refresh'),
                                       array(1, AccountChange_Product_Manager::ACTION_REFRESH));

        $objCommunity->expects($this->once())
                     ->method('refresh');

        $objCommunity->execute();
    }

    /**
     * @covers AccountChange_Product_Community::initialise
     *
     */
    public function testInitialiseSetsUpTheOptionsCorrectly()
    {
        $objProduct = new AccountChange_Product_Community(1,
                                                         AccountChange_Product_Manager::ACTION_NONE,
                                                         $this->arrOptions);

        $this->assertAttributeEquals($this->arrOptions['bolFreeProduct'],
                                     'bolFreeProduct',
                                     $objProduct);
    }

    /**
     * @covers AccountChange_Product_Community::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getServiceDefinitionDao'),
                                           array('Core', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_Community(1, AccountChange_Product_Manager::ACTION_CHANGE);
        $objProduct2 = new AccountChange_Product_Community(2, AccountChange_Product_Manager::ACTION_CHANGE);

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct1));
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct2));

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertEquals($objProduct2, $objProduct1->getNewProductConfiguration());
    }

    /**
     * @covers AccountChange_Product_Community::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenNotChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getServiceDefinitionDao'),
                                           array('Core', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_Community(1, AccountChange_Product_Manager::ACTION_NONE);
        $objProduct2 = new AccountChange_Product_Community(2, AccountChange_Product_Manager::ACTION_NONE);

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct1));
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct2));

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }
}
