<?php
/**
 * Testing class for the AccountChange_Ticket
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 Plusnet
 * @since      File available since 2009-01-19
 */
/**
 * Ticket Test Class
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 Plusnet
 */
class AccountChange_Ticket_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test the constructor and getters
     *
     * @covers AccountChange_Ticket::__construct
     * @covers AccountChange_Ticket::getComment
     * @covers AccountChange_Ticket::getPool
     * @covers AccountChange_Ticket::isClosedContact
     *
     * @dataProvider provideDataForConstructor
     *
     * @return void
     */
    public function testConstructorWithData(
        $comment, $pool, $isClosedContact, $expectedComment, $expectedPool, $expectedIsClosedContact
    )
    {
        $ticket = new AccountChange_Ticket($comment, $pool, $isClosedContact);
        $actualComment = $ticket->getComment();
        $actualPool = $ticket->getPool();
        $actualIsClosedContact = $ticket->isClosedContact();

        $this->assertEquals($expectedComment, $actualComment);
        $this->assertEquals($expectedPool, $actualPool);
        $this->assertEquals($expectedIsClosedContact, $actualIsClosedContact);
    }

    /**
     * Data provider for testConstructorWithData
     *
     * @return array
     */
    public static function provideDataForConstructor()
    {
        return array(
            array('Testing is fun', 'Some Pool', true, 'Testing is fun', 'Some Pool', true)
        );
    }

    /**
     * Test the setters
     *
     * @covers AccountChange_Ticket::__construct
     * @covers AccountChange_Ticket::setComment
     * @covers AccountChange_Ticket::setPool
     * @covers AccountChange_Ticket::setClosedContact
     *
     * @dataProvider provideDataForSetters
     *
     * @return void
     */
    public function testSettersWithData(
        $comment, $pool, $isClosedContact, $expectedComment, $expectedPool, $expectedIsClosedContact
    )
    {
        $ticket = new AccountChange_Ticket();

        $this->assertAttributeEquals('', '_comment', $ticket);
        $this->assertAttributeEquals('CSC - Account change', '_pool', $ticket);
        $this->assertAttributeEquals(false, '_isClosedContact', $ticket);

        $ticket->setComment($comment);
        $ticket->setPool($pool);
        $ticket->setClosedContact($isClosedContact);

        $this->assertAttributeEquals($expectedComment, '_comment', $ticket);
        $this->assertAttributeEquals($expectedPool, '_pool', $ticket);
        $this->assertAttributeEquals($expectedIsClosedContact, '_isClosedContact', $ticket);
    }

    /**
     * Data provider for testSettersWithData
     *
     * @return array
     */
    public static function provideDataForSetters()
    {
        return array(
            array('Testing is fun', 'Some Pool', true, 'Testing is fun', 'Some Pool', true),
            array('Testing is fun', 'Some Pool', false, 'Testing is fun', 'Some Pool', false)
        );
    }
}