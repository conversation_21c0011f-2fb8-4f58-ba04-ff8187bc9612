<?php

/**
 * <AUTHOR>
 */

class AccountChange_MopManagedOrderPolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE =
        'You can\'t change your products for the moment, you have an order which is currently being processed. Please try again later.';

    const ERROR_CODE = 'ERROR_OPEN_ORDER';

    /**
     * @return bool
     */
    public function validate()
    {
        require_once '/local/data/mis/database/class_libraries/F2capiOrders.php';
        require_once '/local/www/database-admin/admin_document_root/customers/customerdetails/functions.inc';
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/www/database-admin/config/config.inc';

        $mopOrderId = F2capiOrders::lastOpenOrderId($this->actor->getExternalUserId(), getCommonSessionId());

        return $mopOrderId === false;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return self::ERROR_MESSAGE;
    }

    /**
     * Return the Error code of the Policy
     *
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }
}
