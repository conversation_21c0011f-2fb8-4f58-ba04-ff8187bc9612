<?php

/**
 * <AUTHOR> <<EMAIL>>
 */
class AccountChange_DuplicateHardwarePolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE = 'You already have this hardware component active on your account.';
    const ERROR_CODE = 'ERROR_CUSTOMER_ALREADY_HAS_COMPONENT';

    const COMPONENT_ACTIVE_STATES = [
        'unconfigured',
        'queued-activate',
        'active',
    ];

    /** @var HardwareClient_Client $hardwareClient */
    private $hardwareClient;

    /** @var int $serviceId */
    private $serviceId;

    /** @var int $serviceComponentId */
    private $serviceComponentId;

    /**
     * @param Auth_BusinessActor $actor                 Business Actor
     * @param bool               $isWorkplace           is workplace flag
     * @param bool               $isScript              is script flag
     * @param array              $additionalInformation extra params
     */
    public function __construct(
        Auth_BusinessActor $actor,
        $isWorkplace = false,
        $isScript = false,
        $additionalInformation = array()
    ) {
        parent::__construct($actor, $isWorkplace, $isScript, $additionalInformation);

        $this->hardwareClient = BusTier_BusTier::getClient('hardware');

        $this->serviceComponentId = (int)$this->additionalInformation['serviceComponentId'];
        $this->serviceId = (int)$this->additionalInformation['serviceId'];
    }

    /**
     * Returns false if a given service component ID is already active on the account, otherwise true
     * @return bool
     */
    public function validate()
    {
        $activeHardware = $this->hardwareClient->getHardwareForService(new Int($this->serviceId));
        if ($activeHardware) {
            return !$this->doesAnActiveComponentAlreadyExist($activeHardware);
        }

        return true;
    }

    /**
     * @param array $activeHardware Active hardware array returned from HardwareClient
     * @return bool
     */
    private function doesAnActiveComponentAlreadyExist($activeHardware)
    {
        foreach ($activeHardware as $hardware) {
            if ((int)$hardware['intComponentTypeId'] === $this->serviceComponentId
                && in_array($hardware['strStatus'], static::COMPONENT_ACTIVE_STATES)
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return static::ERROR_MESSAGE;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return static::ERROR_CODE;
    }
}
