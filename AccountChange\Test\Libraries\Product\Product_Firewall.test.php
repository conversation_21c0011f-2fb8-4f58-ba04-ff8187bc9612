<?php
/**
 * Firewall Product Configuration
 *
 * Testing class for the AccountChange_Product_Firewall class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Product_Firewall.test.php,v 1.2 2009-01-27 09:07:53 bselby Exp $
 * @since      File available since 2008-08-28
 */
/**
 * Firewall Product Configuration Test Class
 *
 * @package    AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2008 PlusNet
 */
class AccountChange_Product_Firewall_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Fixture for Service Id
     *
     * @var int
     */
    private $intServiceId;

    /**
     * PHPUnit setup function
     *
     */
    public function setup()
    {
        $this->intServiceId = 9999;
    }

    /**
     * @covers AccountChange_Product_Firewall::isKeyProduct
     */
    public function testKeyProductIsFalse()
    {
        $objFirewall = new AccountChange_Product_Firewall($this->intServiceId, AccountChange_Product_Manager::ACTION_NONE);

        $this->assertFalse($objFirewall->isKeyProduct());
    }

    /**
     * @covers AccountChange_Product_Firewall::refresh
     */
    public function testExecuteCallsRefreshIfTheActionIsRefresh()
    {
        $objFirewall = $this->getMock('AccountChange_Product_Firewall',
                                       array('includeLegacyFiles', 'refresh'),
                                       array($this->intServiceId, AccountChange_Product_Manager::ACTION_REFRESH));

        $objFirewall->expects($this->any())
                    ->method('includeLegacyFiles');

        $objFirewall->expects($this->once())
                    ->method('refresh');

        $objFirewall->execute();
    }

    /**
     * @covers AccountChange_Product_Firewall::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getServiceDefinitionDao'),
                                           array('Core', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_Firewall(1, AccountChange_Product_Manager::ACTION_CHANGE);
        $objProduct2 = new AccountChange_Product_Firewall(2, AccountChange_Product_Manager::ACTION_CHANGE);

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct1));
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct2));

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertEquals($objProduct2, $objProduct1->getNewProductConfiguration());
    }

    /**
     * @covers AccountChange_Product_Firewall::setMatchingProductConfiguration
     *
     */
    public function testSetMatchingProductConfigurationWhenNotChangingAProduct()
    {
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('getServiceDefinitionDao'),
                                           array('Core', Db_Manager::DEFAULT_TRANSACTION, false));
        $objMockDbAdaptor->expects($this->any())
                         ->method('getServiceDefinitionDao')
                         ->will($this->returnValue(array('strProductFamily'=>'VALUE')));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);

        // Create mock object for the database
        $objMockDbAdaptor = $this->getMock('Db_Adaptor',
                                           array('isServiceComponentAllowedOnServiceDefinition'),
                                           array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false));

        $objMockDbAdaptor->expects($this->any())
                            ->method('isServiceComponentAllowedOnServiceDefinition')
                            ->will($this->returnValue(1));

        // Set up the DB adaptor
        Db_Manager::setAdaptor('AccountChange', $objMockDbAdaptor);

        // Create a service definition product
        $objProductServiceDefinition = new AccountChange_Product_ServiceDefinition(1, AccountChange_Product_Manager::ACTION_NONE);

        // Create the products we want to test
        $objProduct1 = new AccountChange_Product_Firewall(1, AccountChange_Product_Manager::ACTION_NONE);
        $objProduct2 = new AccountChange_Product_Firewall(2, AccountChange_Product_Manager::ACTION_NONE);

        // Setup the Account configurations
        $objAccountConfiguration1 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct1));
        $objAccountConfiguration2 = new AccountChange_AccountConfiguration(array($objProductServiceDefinition, $objProduct2));

        // Match the product up
        $objProduct1->setMatchingProductConfiguration($objAccountConfiguration2);

        $this->assertNull($objProduct1->getNewProductConfiguration());
    }
}
