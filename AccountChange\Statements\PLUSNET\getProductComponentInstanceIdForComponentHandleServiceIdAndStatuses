server: coredb
role: slave
rows: single
statement:

SELECT
    pci.intProductComponentInstanceID
FROM
    userdata.tblProductComponentInstance pci
INNER JOIN userdata.components c
    ON c.component_id = pci.intComponentID
INNER JOIN dbProductComponents.tblProductComponent pc
    ON pc.intProductComponentID = pci.intProductComponentID
INNER JOIN dbProductComponents.tblStatus s
    ON s.intStatusID = pci.intStatusID
WHERE
    pc.vchHandle = :callFeatureComponentHandle
AND
    c.service_id = :serviceId
AND
    s.vchHandle IN (:statuses)