CREATE TABLE products.tblServiceComponentProduct (
  intServiceComponentProductID int(10) unsigned NOT NULL auto_increment,
  intServiceComponentProductTypeID int(10) unsigned NOT NULL default '0',
  intServiceComponentID int(10) unsigned NOT NULL default '0',
  intCustomerSectorID int(10) unsigned NOT NULL default '0',
  stmLastUpdate timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (intServiceComponentProductID),
  KEY intServiceComponentProductTypeID (intServiceComponentProductTypeID),
  KEY intServiceComponentID (intServiceComponentID)
) ENGINE=InnoDB DEFAULT CHARSET=latin1