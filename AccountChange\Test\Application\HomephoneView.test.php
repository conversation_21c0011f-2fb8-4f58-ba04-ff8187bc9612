<?php
/**
 * AccountChange Homephone View Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <d<PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright 2012 Plusnet
 * @since     File available since 2012-01-26
 */

/**
 * AccountChange Homephone View Test
 *
 * Test class for AccountChange_HomephoneViewTest
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <dcal<PERSON><EMAIL>>
 *
 * @copyright 2012 Plusnet
 */
class AccountChange_HomephoneViewTest extends PHPUnit_Framework_TestCase
{
    /**
     * Test that the view reorders Homephone products on Call Plan
     * JLP, Greenbee and Waitrose Call Plans should be sorted low to high
     * Anything else should be left alone
     *
     * @covers       AccountChange_HomephoneView::processInput
     *
     * @dataProvider provideDataForProcessInputSortsHomephoneOnCallPlan
     *
     * @return void
     */
    public function testProcessInputSortsHomephoneOnCallPlan($isp, $data, $expectedResult)
    {
        $input = array(
            'productIsp' => $isp,
            'arrNewProducts' => $data
        );

        $expected = array(
            'productIsp' => $isp,
            'arrNewProducts' => $expectedResult
        );

        $view = $this->getMock('AccountChange_HomephoneView', array(), array(), '', false);
        $output = $view->testProcessInput($this, $input);

        $this->assertEquals($output, $expected);
    }

    /**
     * Data Provider for Sorting Home Phone Call plans
     *
     * @return    array    containing the data for sorting via the view
     */
    public function provideDataForProcessInputSortsHomephoneOnCallPlan()
    {
        return array(

            // dummy
            array(
                'dummy',
                $this->getDummyArrayForSorting(),
                $this->getDummyArrayForSorting()
            ),

            // plus.net
            array(
                'plus.net',
                $this->getDummyArrayForSorting(),
                $this->getDummyArrayForSorting()
            ),

            // greenbee
            array(
                'greenbee',
                $this->getJohnLewisDummyArrayForSorting(),
                $this->getJohnLewisDummySortedArrayForSorting()
            ),

            // waitrose
            array(
                'waitrose',
                $this->getJohnLewisDummyArrayForSorting(),
                $this->getJohnLewisDummySortedArrayForSorting()
            ),

            // johnlewis
            array(
                'johnlewis',
                $this->getJohnLewisDummyArrayForSorting(),
                $this->getJohnLewisDummySortedArrayForSorting()
            ),
        );
    }

    /**
     * Creates a dummy Call Plan used for testing
     *
     * @return    array     a dummy Call Plan array for sorting
     */
    private function getDummyArrayForSorting()
    {
        return array(
            array(
                'strProductName' => 'Dummy Call Plan 1',
                'intCallPlanCost' => new I18n_Currency('gbp', 10)
            ),

            array(
                'strProductName' => 'Dummy Call Plan 2',
                'intCallPlanCost' => new I18n_Currency('gbp', 0)
            ),

            array(
                'strProductName' => 'Dummy Call Plan 3',
                'intCallPlanCost' => new I18n_Currency('gbp', 5)
            )
        );
    }

    /**
     * Creates a dummy Call Plan used for testing
     *
     * @return    array     a dummy Call Plan array for sorting
     */
    private function getJohnLewisDummyArrayForSorting()
    {
        return array(
            array(
                'strProductName' => 'Talk Evenings and Weekends',
                'intCallPlanCost' => new I18n_Currency('gbp', 0),
                'intNewWlrId' => '995'
            ),

            array(
                'strProductName' => 'Talk Anytime International',
                'intCallPlanCost' => new I18n_Currency('gbp', 7),
                'intNewWlrId' => '996'
            ),

            array(
                'strProductName' => 'Talk Anytime',
                'intCallPlanCost' => new I18n_Currency('gbp', 5),
                'intNewWlrId' => '997'
            ),

            array(
                'strProductName' => 'Talk Anytime International with Mobile',
                'intCallPlanCost' => new I18n_Currency('gbp', 12),
                'intNewWlrId' => '1979'
            ),

            array(
                'strProductName' => 'Talk Anytime with Mobile',
                'intCallPlanCost' => new I18n_Currency('gbp', 10),
                'intNewWlrId' => '1980'
            )

        );
    }

    /**
     * Creates a dummy sorted Call Plan used for testing
     *
     * @return     array     a sorted version of the dummy Call Plan array
     */
    private function getJohnLewisDummySortedArrayForSorting()
    {
        return array(
            array(
                'strProductName' => 'Talk Evenings and Weekends',
                'intCallPlanCost' => new I18n_Currency('gbp', 0),
                'intNewWlrId' => '995',
                'intSort' => 0
            ),

            array(
                'strProductName' => 'Talk Anytime',
                'intCallPlanCost' => new I18n_Currency('gbp', 5),
                'intNewWlrId' => '997',
                'intSort' => 1
            ),

            array(
                'strProductName' => 'Talk Anytime with Mobile',
                'intCallPlanCost' => new I18n_Currency('gbp', 10),
                'intNewWlrId' => '1980',
                'intSort' => 2
            ),

            array(
                'strProductName' => 'Talk Anytime International',
                'intCallPlanCost' => new I18n_Currency('gbp', 7),
                'intNewWlrId' => '996',
                'intSort' => 3
            ),

            array(
                'strProductName' => 'Talk Anytime International with Mobile',
                'intCallPlanCost' => new I18n_Currency('gbp', 12),
                'intNewWlrId' => '1979',
                'intSort' => 4
            )
        );
    }
}
