<?php

/**
 * <AUTHOR>
 */

use Plusnet\BillingApiClient\Exceptions\BillingApiClientBillingServiceException;
use Plusnet\BillingApiClient\Exceptions\BillingApiClientPartnerResellerAccessException;

class AccountChange_InvoiceDateCorrectPolicy extends AccountChange_AbstractValidationPolicy
{
    const ERROR_MESSAGE_INVOICE_DATE =
        'You can\'t change your products for the moment, there is a problem with your next billing date. Please try again later.';
    const ERROR_MESSAGE_INVOICE_DATE_IN_PAST =
        'You can\'t change your products for the moment, our system may be updating. Please try again later.';
    const ERROR_BILLING_API_EXCEPTION = '';
    const ERROR_BILLING_API_RESELLER_EXCEPTION = '';

    private $failureReason;

    /**
     * Error code used to map this policy's failure with a template
     */
    const ERROR_CODE = 'ERROR_BILLING_NEXT_INVOICE_DATE_INVALID';

    /**
     * @return bool
     */
    public function validate()
    {
        $hasError = false;
        try {
            $coreService = $this->getCoreService($this->actor->getExternalUserId());
            $nextInvoice = $coreService->getNextInvoiceDate();

            if (empty($nextInvoice) || !($nextInvoice instanceof I18n_Date)) {
                $this->failureReason = self::ERROR_MESSAGE_INVOICE_DATE;
                $hasError = true;
            } else {
                $uxtNextInvoice = $nextInvoice->getTimestamp();

                $uxtToday = mktime(0, 0, 0, date("m"), date("d"), date("Y"));

                if ($uxtNextInvoice < $uxtToday) {
                    $this->failureReason = self::ERROR_MESSAGE_INVOICE_DATE_IN_PAST;
                    $hasError = true;
                }
            }
        } catch (InvalidArgumentException $e) {
            $this->failureReason = self::ERROR_MESSAGE_INVOICE_DATE;
            return false;
        } catch (BillingApiClientBillingServiceException $e) {
            $this->failureReason = self::ERROR_BILLING_API_EXCEPTION;
            return false;
        } catch (BillingApiClientPartnerResellerAccessException $e) {
            $this->failureReason = self::ERROR_BILLING_API_RESELLER_EXCEPTION;
            return false;
        }

        return !$hasError;
    }

    /**
     * @return string
     */
    public function getFailure()
    {
        return $this->failureReason;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return self::ERROR_CODE;
    }

    /**
     * Wrapper to get Core Service
     *
     * @param int $serviceId Service Id
     *
     * @return Core_Service
     */
    protected function getCoreService($serviceId)
    {
        return new Core_Service($serviceId);
    }
}
