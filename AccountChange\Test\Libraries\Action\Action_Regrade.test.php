<?php
/**
 * Account Change Regrade Action Test
 *
 * Testing class for AccountChange_Action_Regrade
 *
 * @category  AccountChange_Action_Test
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */

class AccountChange_Action_Regrade_Test extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     */
    public function tearDown()
    {
        Mockery::close();
    }

    /**
    * Fixture for service id
    *
    * @var int
    */
    private $_serviceId = 123;

    /**
     * Test the public execute method calls correct sub actions
     *
     * @covers AccountChange_Action_Regrade::execute
     * @covers AccountChange_Action_Regrade::getLineCheckResult
     * @dataProvider adsl2OrdersProvideDataProvider
     * @return void
     */
    public function testExecuteProvidePfmUsingAdsl2Orders($orderPlaced)
    {
        $action = $this->getMock(
            'AccountChange_Action_Regrade',
            array(
                'getOrderService',
                'getAccountChangeRegistry'
            ),
            array($this->_serviceId)
        );

        $dbMock = $this->getMock(
            Db_Adaptor::class,
            ['markAccountChangeAsRequiringBroadbandOrderByServiceId'],
            [],
            '',
            false
        );

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $mockRegisrty = $this->getMock(
            'AccountChange_Registry',
            array('getEntry'),
            array(),
            '',
            false
        );

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            array('getLineCheckId'),
            array(),
            '',
            false
        );

        $mockLineCheck
            ->expects($this->any())
            ->method('getLineCheckId')
            ->will($this->returnValue("3"));

        $appointmentData = array (
            "notes" => "test notes",
            "live" => array(
                "date" => "10-09-2021",
                "timeSlot" => "AM",
                "ref" => "Andy"
            )
        );
        $registry = new AccountChange_Registry();
        $registry->setEntry('objLineCheckResult', $mockLineCheck);
        $registry->setEntry('intOldServiceDefinitionId', 1);
        $registry->setEntry('intNewServiceDefinitionId', 2);
        $registry->setEntry('appointmentData', $appointmentData);
        $registry->setEntry('bolHousemove', false);


        $action->expects($this->once())
            ->method('getAccountChangeRegistry')
            ->will($this->returnValue($registry));

        $mockOrderService = $this->getMock(
            'Adsl2Orders_OrderService',
            array('provision','isOrderRequired'),
            array(),
            '',
            false
        );

        $mockOrderService->expects($this->once())
            ->method('provision')
            ->with(
                $this->_serviceId,
                "3",
                1,
                2,
                array(
                "notes" => "test notes",
                "live" => array(
                   "date" => "10-09-2021",
                   "timeSlot" => "AM",
                   "ref" => "Andy"
                )
                ),
                false
            );

        $mockOrderService->expects($this->once())
            ->method('isOrderRequired')
            ->willReturn($orderPlaced);

        $expectsMatcher = $orderPlaced ? $this->once() : $this->never();

        $dbMock->expects($expectsMatcher)
            ->method('markAccountChangeAsRequiringBroadbandOrderByServiceId')
            ->with($this->_serviceId);

        $action->expects($this->once())
            ->method('getOrderService')
            ->will($this->returnValue($mockOrderService));

        $action->execute();
        $this->assertEquals($registry->getEntry('bolOrderNeeded'), $orderPlaced ? true : null);
    }

    /**
     * Test the public execute method calls correct sub actions
     *
     * @covers AccountChange_Action_Regrade::execute
     * @covers AccountChange_Action_Regrade::getLineCheckResult
     * @dataProvider adsl2OrdersProvideDataProvider
     * @return void
     */
    public function testExecuteProvidePfmUsingAdsl2OrdersWithoutAppointment($orderPlaced)
    {
        $action = $this->getMock(
            'AccountChange_Action_Regrade',
            array(
                'getOrderService',
                'getAccountChangeRegistry',
            ),
            array($this->_serviceId)
        );

        $dbMock = $this->getMock(
            Db_Adaptor::class,
            ['markAccountChangeAsRequiringBroadbandOrderByServiceId'],
            [],
            '',
            false
        );

        Db_Manager::setAdaptor('AccountChange', $dbMock);

        $mockRegisrty = $this->getMock(
            'AccountChange_Registry',
            array('getEntry'),
            array(),
            '',
            false
        );

        $mockLineCheck = $this->getMock(
            'LineCheck_Result',
            array('getLineCheckId'),
            array(),
            '',
            false
        );

        $mockLineCheck
            ->expects($this->any())
            ->method('getLineCheckId')
            ->will($this->returnValue("3"));

        $mockRegisrty->expects($this->exactly(6))
            ->method('getEntry')
            ->withConsecutive(
                ['objLineCheckResult'],
                ['intOldServiceDefinitionId'],
                ['intNewServiceDefinitionId'],
                ['appointmentData'],
                ['bolHousemove'],
                ['currentWlrComponentId']
            )
            ->willReturnOnConsecutiveCalls(
                $mockLineCheck,
                1,
                2,
                null,
                true,
                54321
            );

        $action->expects($this->once())
            ->method('getAccountChangeRegistry')
            ->will($this->returnValue($mockRegisrty));

        $mockOrderService = $this->getMock(
            'Adsl2Orders_OrderService',
            array('provision','isOrderRequired'),
            array(),
            '',
            false
        );

        $mockOrderService->expects($this->once())
            ->method('provision')
            ->with(
                $this->_serviceId,
                "3",
                1,
                2,
                array(),
                true
            );

        $mockOrderService->expects($this->once())
            ->method('isOrderRequired')
            ->willReturn($orderPlaced);

        $expectsMatcher = $orderPlaced ? $this->once() : $this->never();

        $dbMock->expects($expectsMatcher)
            ->method('markAccountChangeAsRequiringBroadbandOrderByServiceId')
            ->with($this->_serviceId);

        $action->expects($this->once())
            ->method('getOrderService')
            ->will($this->returnValue($mockOrderService));

        $action->execute();
    }

    /**
     * @return array
     */
    public function adsl2OrdersProvideDataProvider()
    {
        return [
            'order placed' => [true],
            'order not' => [false]
        ];
    }

    /**
     * @covers AccountChange_Action_Regrade::checkScheduleChangeCompleted
     * @return void
     */
    public function testCheckScheduleChangeCompleted()
    {
        $action = $this->getMock(
            'AccountChange_Action_Regrade',
            array(
                'getAdaptor'
            ),
            array($this->_serviceId)
        );

        $dbAdapterMock = $this->getMockBuilder('\Db_Adaptor')
            ->setMethods(
                array(
                    'getScheduleChangeCompleted'
                )
            )
            ->setConstructorArgs(array('AccountChange', \Db_Manager::DEFAULT_TRANSACTION, true))
            ->getMock();

        \Db_Manager::setAdaptor('AccountChange', $dbAdapterMock, \Db_Manager::DEFAULT_TRANSACTION);

        $dbAdapterMock->expects($this->once())
                 ->method('getScheduleChangeCompleted')
                 ->with($this->_serviceId)
                 ->will($this->returnValue(true));

        $action->checkScheduleChangeCompleted($this->_serviceId);
    }
}
