<?php
require_once(__DIR__.'/PromotionRule.php');
require_once(__DIR__.'/PromotionRuleBase.php');

use Plusnet\C2mApiClient\Entity\Promotion;

class RemoveExpiredPromotions extends PromotionRuleBase implements PromotionRule
{
    /**
     * Date for comparing functions against.
     *
     * @var \DateTime
     */
    private $comparator;

    /**
     * RemoveExpiredPromotions constructor.
     */
    public function __construct()
    {
        $this->comparator = new DateTime();
    }

    /**
     * Ensure the promotion is valid on the date assigned to the
     * comparator property.
     *
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     *
     * @return bool
     */
    private function isActivePromotion(Promotion $promotion)
    {
        $promoStartDate = DateTime::createFromFormat(
            'Ymd',
            $promotion->getActiveFrom()
        );
        $promoEndDate = DateTime::createFromFormat(
            'Ymd',
            $this->getPersonalisedOrGeneralEndDate($promotion)
        );

        return ($promoStartDate <= $this->comparator && $this->comparator <= $promoEndDate);

    }

    /**
     * Returns either the promotion end date or the personalised promotion end date
     * if applicable.
     *
     * Returns the date as a string Ymd
     *
     * @param \Plusnet\C2mApiClient\Entity\Promotion $promotion
     *
     * @return string
     */
    protected function getPersonalisedOrGeneralEndDate($promotion)
    {
        $endDate = $promotion->getActiveTo();

        if ($promotion->getIsPersonalisedOffer() && !empty($promotion->getPersonalisedEndDate())) {
            $endDate = $promotion->getPersonalisedEndDate();
        }

        return $endDate;
    }

    /**
     * Remove inactive Promotion objects. Inactive promotions are those not
     * active on the date provided by the $comparator.
     *
     * @param \DateTime $comparator
     *
     * @return  array<Promotion>
     */
    public function handle(array $promotions)
    {
        $promotions = array_filter(
          $promotions,
          [$this, 'isActivePromotion']
        );
        return $promotions;

    }

}
