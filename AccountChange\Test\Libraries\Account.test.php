<?php

/**
 * AccountChange Account Test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 * @since     File available since 2009-09-14
 */

use Plusnet\BillingApiClient\Entity\CustomerProductSubscription;

require_once '/local/data/mis/database/database_libraries/userdata-access.inc';

/**
 * AccountChange Account Test
 *
 * Test class for AccountChange_Account
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 Plusnet
 */
class AccountChange_Account_Test extends PHPUnit_Framework_TestCase
{
    const LINE_RENTAL = 'Line Rental';
    const CALL_PLAN = 'Call Plan';
    const CALL_PLAN_NAME = 'Anytime';

    /**
     * Tear down functionality
     *
     * (non-PHPdoc)
     * @return void
     * @throws Db_TransactionException
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     */
    public function tearDown()
    {
        AccountChange_Account::setInstance(null);
        Db_Manager::restoreAdaptor('AccountChange');
    }

    /**
     * Test that the constructor correctly sets up the object
     *
     * @covers AccountChange_Account::__construct
     *
     * @return void
     */
    public function testConstructor()
    {
        $serviceId = new Int(1);

        $account = new AccountChange_Account($serviceId);

        $this->assertAttributeEquals($serviceId, 'serviceId', $account);
    }

    /**
     * Test that the factory instance methods returns new object if one isn't set
     *
     * @covers AccountChange_Account::instance
     *
     * @return void
     */
    public function testInstanceReturnsNewObjectIfNotOneSet()
    {
        $serviceId = new Int(1);

        $account = AccountChange_Account::instance($serviceId);

        $this->assertAttributeEquals($serviceId, 'serviceId', $account);
    }

    /**
     * Test that the instance methods returns the object original set
     *
     * @covers AccountChange_Account::instance
     * @covers AccountChange_Account::setInstance
     *
     * @return void
     */
    public function testInstanceReturnsObjectAlreadySetIfThereIsOne()
    {
        $realServiceId = new Int(1);
        $fakeServiceId = new Int(999);

        $account = new AccountChange_Account($realServiceId);

        AccountChange_Account::setInstance($account);

        $account = AccountChange_Account::instance($fakeServiceId);

        $this->assertAttributeEquals($realServiceId, 'serviceId', $account);
    }

    /**
     * Test that the grace period returns false if the correct date has not passed
     *
     * @covers AccountChange_Account::hasPassedAdslChangeGracePeriod
     *
     * @return void
     */
    public function testhasGracePeriodPassedForBroadbandChangeReturnsFalseIf30DaysHaveNotPassed()
    {
        $serviceId = new Int(9999);
        $uxtNowMinusXDays = time() - (60 * 60 * 24 * 10); // 10 Days

        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_service_events_find'), array(), '', false);

        $objUserdataSplitter->expects($this->once())
            ->method('userdata_service_events_find')
            ->will($this->returnValue(array(array('event_date' => date('Y-m-d', $uxtNowMinusXDays)))));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $account = $this->getMock(
            'AccountChange_Account',
            array('includeLegacyFiles'),
            array($serviceId)
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $bolResult = $account->hasPassedAdslChangeGracePeriod();

        $this->assertFalse($bolResult);
    }

    /**
     * Test that the grace period returns true if the correct date has passed
     *
     * @covers AccountChange_Account::hasPassedAdslChangeGracePeriod
     *
     * @return void
     */
    public function testHasGracePeriodPassedForBroadbandChangeReturnsTrueIf30DaysHavePassed()
    {
        $serviceId = new Int(9999);
        $uxtNowMinusXDays = time() - (60 * 60 * 24 * 31); // 31 Days

        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_service_events_find'), array(), '', false);

        $objUserdataSplitter->expects($this->once())
            ->method('userdata_service_events_find')
            ->will($this->returnValue(array(array('event_date' => date('Y-m-d', $uxtNowMinusXDays)))));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $account = $this->getMock(
            'AccountChange_Account',
            array('includeLegacyFiles'),
            array($serviceId)
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $bolResult = $account->hasPassedAdslChangeGracePeriod();

        $this->assertTrue($bolResult);
    }

    /**
     * Test that the grace period returns true if the correct date has passed
     *
     * @covers AccountChange_Account::hasPassedAdslChangeGracePeriod
     *
     * @return void
     */
    public function testHasGracePeriodPassedForBroadbandChangeReturnsTrueIfThereAreNoEventsReturned()
    {
        $serviceId = new Int(9999);

        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_service_events_find'), array(), '', false);

        $objUserdataSplitter->expects($this->once())
            ->method('userdata_service_events_find')
            ->will($this->returnValue(array()));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $account = $this->getMock(
            'AccountChange_Account',
            array('includeLegacyFiles'),
            array($serviceId)
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $bolResult = $account->hasPassedAdslChangeGracePeriod();

        $this->assertTrue($bolResult);
    }

    /**
     * Test that getAdslGracePeriod returns null if 30 days have passed
     *
     * @covers AccountChange_Account::getAdslGracePeriod
     *
     * @return void
     */
    public function testGetGracePeriodPassedForBroadbandChangeReturnsNullIf30DaysHavePassed()
    {
        $serviceId = new Int(9999);
        $uxtNowMinusXDays = strtotime("-30 days", time()); // 10 Days

        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_service_events_find'), array(), '', false);

        $objUserdataSplitter->expects($this->once())
            ->method('userdata_service_events_find')
            ->will($this->returnValue(array(array('event_date' => date('Y-m-d', $uxtNowMinusXDays)))));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $account = $this->getMock(
            'AccountChange_Account',
            array('includeLegacyFiles'),
            array($serviceId)
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $unkResult = $account->getAdslGracePeriod();

        $this->assertNull($unkResult);
    }

    /**
     * Test that getAdslGracePeriod returns the date plus 30 days if grace period has not passed
     *
     * @covers AccountChange_Account::getAdslGracePeriod
     *
     * @return void
     */
    public function testGetGracePeriodPassedForBroadbandChangeReturnsTheTheDatePlus30DaysIf30DaysHaveNotPassed()
    {
        $serviceId = new Int(9999);
        $uxtNow = strtotime(date('Y-m-d', time()));

        $uxtNowMinusXDays = strtotime("-10 days", $uxtNow); // 10 Days
        $uxtNowPlusXDays = strtotime("+30 days", $uxtNowMinusXDays); // 20 Days

        // Userdata Mock
        $objUserdataSplitter = $this->getMock('Lib_Userdata', array('userdata_service_events_find'), array(), '', false);

        $objUserdataSplitter->expects($this->once())
            ->method('userdata_service_events_find')
            ->will($this->returnValue(array(array('event_date' => date('Y-m-d', $uxtNowMinusXDays)))));

        Lib_Userdata::setInstance($objUserdataSplitter);

        $account = $this->getMock(
            'AccountChange_Account',
            array('includeLegacyFiles'),
            array($serviceId)
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $unkResult = $account->getAdslGracePeriod();

        $this->assertEquals($unkResult, I18n_Date::fromTimestamp($uxtNowPlusXDays));
    }

    /**
     * Test that getCurrentBroadbandTariff returns null when tariff id is not set
     *
     * @covers AccountChange_Account::getCurrentBroadbandTariff
     *
     * @return void
     */
    public function testGetCurrentBroadbandTariffReturnsFalseIfTariffIdIsNotSet()
    {
        $serviceId = new Int(9999);
        $db = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForService'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->once())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue(array()));

        Db_Manager::setAdaptor('AccountChange', $db);

        $account = AccountChange_Account::instance($serviceId);
        $result = $account->getCurrentBroadbandTariff();

        $this->assertNull($result);
    }

    /**
     * Test that getCurrentBroadbandTariff returns null when tariff id is not set
     *
     * @covers AccountChange_Account::getCurrentBroadbandTariff
     *
     * @return void
     */
    public function testGetCurrentBroadbandTariffReturnsTariffIdIfSet()
    {
        $tariffId = 123;

        $serviceId = new Int(9999);
        $db = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForService'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->once())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue(array('intTariffID' => $tariffId)));

        Db_Manager::setAdaptor('AccountChange', $db);

        $account = AccountChange_Account::instance($serviceId);
        $result = $account->getCurrentBroadbandTariff();

        $this->assertEquals($result, $tariffId);
    }

    /**
     * @covers AccountChange_Account::getMonthlyBroadbandPrice
     * @covers AccountChange_Account::getBroadbandDetails
     */
    public function testGetMonthlyBroadbandPrice()
    {
        $db = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $broadbandDetails = array(
            'service_definition_id' => 6791,
            'minimum_charge' => 12.99
        );

        $db->expects($this->once())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($broadbandDetails));

        Db_Manager::setAdaptor('Core', $db);

        $productFamily = $this->getMock(
            'ProductFamily_Generic',
            array('isCbcProduct'),
            array(),
            '',
            false
        );

        $account = $this->getMock(
            'AccountChange_Account',
            array('getProductFamily'),
            array(new Int(1234))
        );

        $account
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($productFamily));

        $this->assertEquals(12.99, $account->getMonthlyBroadbandPrice());
    }

    /**
     * @covers AccountChange_Account::getMonthlyPhonePrice
     */
    public function testGetMonthlyPhonePriceReturnsZeroesForAccountsWithoutPhone()
    {
        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone'),
            array(new Int(1234))
        );

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->throwException(new Exception()));

        $expectedResult = array(
            'lineRental' => 0,
            'subscription' => 0
        );

        $this->assertEquals($expectedResult, $account->getMonthlyPhonePrice());
    }

    /**
     * @covers AccountChange_Account::getMonthlyPhonePrice
     */
    public function testGetMonthlyPhonePriceReturnsCorrectPrices()
    {
        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->once())
            ->method('getServiceComponentId')
            ->will($this->returnValue(1351));

        $db = $this->getMock(
            'Db_Adaptor',
            array('getProductComponentPrice'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $db->expects($this->at(0))
            ->method('getProductComponentPrice')
            ->will($this->returnValue(1450));

        $db->expects($this->at(1))
            ->method('getProductComponentPrice')
            ->will($this->returnValue(500));

        Db_Manager::setAdaptor('AccountChange', $db);

        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone'),
            array(new Int(1234))
        );

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $expectedResult = array(
            'lineRental' => 14.50,
            'subscription' => 5.00
        );

        $this->assertEquals($expectedResult, $account->getMonthlyPhonePrice());
    }

    /**
     * @covers AccountChange_Account::getPromoCodeDetails
     */
    public function testGetPromoCodeDetails()
    {
        $coreDb = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForService'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $broadbandDetails = array(
            'service_definition_id' => 6791,
            'minimum_charge' => 12.99
        );

        $coreDb->expects($this->once())
            ->method('getServiceComponentDetailsForService')
            ->will($this->returnValue($broadbandDetails));

        Db_Manager::setAdaptor('Core', $coreDb);

        $accountChangeDb = $this->getMock(
            'Db_Adaptor',
            array('getPromoCodeDetails'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $promoCodeDetails = array(
            'promoCodeId' => 100,
            'discountValue' => 50,
            'discountType' => 'percentage',
            'discountLength' => 3
        );

        $accountChangeDb->expects($this->once())
            ->method('getPromoCodeDetails')
            ->will($this->returnValue($promoCodeDetails));

        Db_Manager::setAdaptor('AccountChange', $accountChangeDb);

        $productFamily = $this->getMock(
            'ProductFamily_Generic',
            array('isCbcProduct'),
            array(),
            '',
            false
        );

        $account = $this->getMock(
            'AccountChange_Account',
            array('getProductFamily'),
            array(new Int(1234))
        );

        $account
            ->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($productFamily));

        $this->assertEquals($promoCodeDetails, $account->getPromoCodeDetails('ctr3MHP'));
    }

    /**
     * @covers AccountChange_Account::getPaymentDetails
     */
    public function testGetPaymentDetails()
    {
        $account = $this->getMock(
            'AccountChange_Account',
            array('getMonthlyBroadbandPrice', 'getMonthlyPhonePrice', 'getPromoCodeDetails'),
            array(new Int(1234))
        );

        $monthlyBroadbandPrice = 6.99;

        $monthlyPhonePrice = array(
            'lineRental' => 14.99,
            'subscription' => 5.00
        );

        $promoCodeDetails = array(
            'discountValue' => 50,
            'discountType' => 'percentage',
            'discountLength' => 3
        );

        $account
            ->expects($this->once())
            ->method('getMonthlyBroadbandPrice')
            ->will($this->returnValue($monthlyBroadbandPrice));

        $account
            ->expects($this->once())
            ->method('getMonthlyPhonePrice')
            ->will($this->returnValue($monthlyPhonePrice));

        $account
            ->expects($this->once())
            ->method('getPromoCodeDetails')
            ->will($this->returnValue($promoCodeDetails));

        $expectedResult = array(
            'broadbandPrice' => 6.99,
            'phonePrice' => array(
                'lineRental' => 14.99,
                'subscription' => 5.00
            ),
            'totalPackagePrice' => 26.98,
            'specialOffer' => true,
            'offerPrice' => 23.48,
            'offerDuration' => 3
        );

        $this->assertEquals($expectedResult, $account->getPaymentDetails('ctr3MHP'));
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithSplitChargeAndBothLineRentalAndCallPlan()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(20.99)
            ->setCurrentSubscriptionPriceInPounds(18.99)
            ->setNextSubscriptionPriceInPounds(20.99);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5)
            ->setNextSubscriptionPriceInPounds(6);

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions($subscriptionLineRental, $subscriptionCallPlan))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr',
                'hasActiveLineRentalSaver'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));


        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '20.99');
        $expectedResult['intLineRentCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '18.99');
        $expectedResult['intProductCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '25.99');
        $expectedResult['intProductCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '23.99');
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithSplitChargeAndLineRentalNoCallPlan()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(20.99)
            ->setCurrentSubscriptionPriceInPounds(18.99)
            ->setNextSubscriptionPriceInPounds(20.99);

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions($subscriptionLineRental, null))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr',
                'hasActiveLineRentalSaver'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(false));

        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '20.99');
        $expectedResult['intLineRentCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '18.99');
        $expectedResult['intProductCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '20.99');
        $expectedResult['intProductCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '18.99');
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = '';
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = false;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithSplitChargeAndCallPlanNoLineRental()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5)
            ->setNextSubscriptionPriceInPounds(6);

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions(null, $subscriptionCallPlan))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr'
                ),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(true));

        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = '';
        $expectedResult['intLineRentCostInContract'] = '';
        $expectedResult['intProductCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['intProductCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithSplitChargeAndNoLineRentalNoCallPlan()
    {
        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions(null, null))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(true));

        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = '';
        $expectedResult['intLineRentCostInContract'] = '';
        $expectedResult['intProductCost'] = '';
        $expectedResult['intProductCostInContract'] = '';
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = '';
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithSplitChargeAndLineRentalZeroDueToLineRentalSaverAndCallPlan()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(0)
            ->setCurrentSubscriptionPriceInPounds(0)
            ->setNextSubscriptionPriceInPounds(0);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5)
            ->setNextSubscriptionPriceInPounds(6);

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions($subscriptionLineRental, $subscriptionCallPlan))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr',
                'hasActiveLineRentalSaver'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
        ->expects($this->once())
        ->method('canAddWlr')
        ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));

        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['intLineRentCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['intProductCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['intProductCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithSplitChargeAndLineRentalAndCallPlanZero()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(18.99)
            ->setCurrentSubscriptionPriceInPounds(17.99)
            ->setNextSubscriptionPriceInPounds(18.99);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(0)
            ->setCurrentSubscriptionPriceInPounds(0)
            ->setNextSubscriptionPriceInPounds(0);

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions($subscriptionLineRental, $subscriptionCallPlan))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr',
                'hasActiveLineRentalSaver'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));


        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '18.99');
        $expectedResult['intLineRentCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '17.99');
        $expectedResult['intProductCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '18.99');
        $expectedResult['intProductCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '17.99');
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithSplitChargeAndLineRentalZeroDueToLineRentalSaverAndCallPlanZero()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(0)
            ->setCurrentSubscriptionPriceInPounds(0)
            ->setNextSubscriptionPriceInPounds(0);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(0)
            ->setCurrentSubscriptionPriceInPounds(0)
            ->setNextSubscriptionPriceInPounds(0);

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions($subscriptionLineRental, $subscriptionCallPlan))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr',
                'hasActiveLineRentalSaver'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));

        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['intLineRentCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['intProductCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['intProductCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '0');
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWithNoSplitChargeAndBothLineRentalAndCallPlan()
    {
        $pastDate = date('Y-m-d', strtotime('-3 days'));
        $futureDate = date('Y-m-d', strtotime('+3 days'));

        $subscriptionLineRental = new CustomerProductSubscription();
        $subscriptionLineRental
            ->setTariffName(self::LINE_RENTAL)
            ->setProductType(self::LINE_RENTAL)
            ->setProductName(self::LINE_RENTAL)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(20.99)
            ->setCurrentSubscriptionPriceInPounds(18.99)
            ->setNextSubscriptionPriceInPounds(20.99);

        $subscriptionCallPlan = new CustomerProductSubscription();
        $subscriptionCallPlan
            ->setTariffName(self::CALL_PLAN_NAME)
            ->setProductType(self::CALL_PLAN)
            ->setProductName(self::CALL_PLAN_NAME)
            ->setStartDate($pastDate)
            ->setEndDate($futureDate)
            ->setCurrentBasePriceInPounds(6)
            ->setCurrentSubscriptionPriceInPounds(5)
            ->setNextSubscriptionPriceInPounds(6);

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->returnValue(
                new AccountChange_BillingApi_CurrentPhoneSubscriptions($subscriptionLineRental, $subscriptionCallPlan))
            );

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(false));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr',
                'hasActiveLineRentalSaver'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('hasActiveLineRentalSaver')
            ->will($this->returnValue(false));

        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '20.99');
        $expectedResult['intLineRentCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '18.99');
        $expectedResult['intProductCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '20.99');
        $expectedResult['intProductCostInContract'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '18.99');
        $expectedResult['bolSplitPrice'] = false;
        $expectedResult['intCallPlanCost'] = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, '5');
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }

    /**
     * @covers AccountChange_Account::getWlrInformation
     */
    public function testGetWlrInformationWhenBillingSubscriptionsThrowsError()
    {
        $billingException = new Exception('Billing exception');

        $currentPhoneSubscriptions = $this->getMock(
            'AccountChange_BillingApi_CurrentPhoneSubscriptionsHelper',
            array('getCurrentPhoneSubscriptionData')
        );

        $currentPhoneSubscriptions
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionData')
            ->will($this->throwException($billingException));

        $homePhone = $this->getMock(
            'HomePhone',
            array('getServiceComponentId',
                'getHomePhoneSubscriptionContractEnd',
                'isSplitCharge',
                'getHomePhoneProduct',
                'getHomePhoneSubscriptionContractLength',
                'getHomePhoneSubscriptionContractHandle',
                'getWlrComponentId',
                'getHomePhoneStatus'),
            array(),
            '',
            false
        );

        $homePhone
            ->expects($this->exactly(2))
            ->method('getServiceComponentId')
            ->will($this->returnValue('1351'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractEnd')
            ->will($this->returnValue('**********'));

        $homePhone
            ->expects($this->once())
            ->method('isSplitCharge')
            ->will($this->returnValue(true));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneProduct')
            ->will($this->returnValue(self::CALL_PLAN_NAME));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractLength')
            ->will($this->returnValue('Monthly'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneSubscriptionContractHandle')
            ->will($this->returnValue('MONTHLY'));

        $homePhone
            ->expects($this->once())
            ->method('getWlrComponentId')
            ->will($this->returnValue('********'));

        $homePhone
            ->expects($this->once())
            ->method('getHomePhoneStatus')
            ->will($this->returnValue('active'));


        $account = $this->getMock(
            'AccountChange_Account',
            array('getHomePhone',
                'getCurrentPhoneSubscriptionsHelper',
                'includeLegacyFiles',
                'getExistingWlrChangeScheduledData',
                'getActiveCallFeatures',
                'canChangeWlr',
                'canAddWlr'),
            array(new Int(1234))
        );

        $account->expects($this->once())
            ->method('includeLegacyFiles');

        $account
            ->expects($this->once())
            ->method('getHomePhone')
            ->will($this->returnValue($homePhone));

        $account
            ->expects($this->once())
            ->method('getCurrentPhoneSubscriptionsHelper')
            ->will($this->returnValue($currentPhoneSubscriptions));

        $account
            ->expects($this->once())
            ->method('getExistingWlrChangeScheduledData')
            ->will($this->returnValue('xyz'));

        $account
            ->expects($this->once())
            ->method('getActiveCallFeatures')
            ->will($this->returnValue('callDivert'));

        $account
            ->expects($this->once())
            ->method('canChangeWlr')
            ->will($this->returnValue(true));

        $account
            ->expects($this->once())
            ->method('canAddWlr')
            ->will($this->returnValue(true));

        $expectedResult = array();
        $expectedResult['strProductName'] = self::CALL_PLAN_NAME;
        $expectedResult['strCallPlanName'] = '';
        $expectedResult['intLineRentCost'] = '';
        $expectedResult['intLineRentCostInContract'] = '';
        $expectedResult['intProductCost'] = '';
        $expectedResult['intProductCostInContract'] = '';
        $expectedResult['bolSplitPrice'] = true;
        $expectedResult['intCallPlanCost'] = '';
        $expectedResult['intOldWlrId'] = '1351';
        $expectedResult['strContractLength'] = 'Monthly';
        $expectedResult['strContractHandle'] = 'MONTHLY';
        $expectedResult['uxtContractEnd'] = I18n_Date::fromTimestamp('**********');
        $expectedResult['intInstanceId'] = '********';
        $expectedResult['strComponentStatus'] = 'active';
        $expectedResult['arrExistingChange'] = 'xyz';
        $expectedResult['activeCallFeatures'] = 'callDivert';
        $expectedResult['bolWlrChangeAllowed'] = true;
        $expectedResult['bolWlrAddAllowed'] = true;

        $this->assertEquals($expectedResult, $account->getWlrInformation());
    }
}
