<?php

require_once '/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php';

class AddCallerDisplayToPreregisteredUsers
{
    private $successfullyAddedServiceIds = array();
    private $unsuccessfullyAddedServiceIds = array();

    /**
     * Run the script
     *
     * @return void
     */
    public function run()
    {
        echo "Logging to file: ".$this->getGeneralLogFileLocation()."\n";

        // START THE SCRIPT
        $this->log("SCRIPT STARTED AT: ".date("j.n.Y H:i:s") .  "\n\n");

        try
        {
            $dbAdaptor = Db_Manager::getAdaptor('AccountChange');

            // GET LIST OF PREREGISTERED USERS' SERVICE IDS
            $this->log("ATTEMPTING TO: Get list of service ids from caller display preregistration table.\n");
            $serviceIds = $dbAdaptor->getServiceIdsFromCallerDisplayPreRegistrationTable();
            $this->log("RECEIVED: " . json_encode($serviceIds) . "\n");
            $this->log("SUCCESS: Retrieved service ids from caller display preregistration table.");

            // ADD CALLER DISPLAY TO PREREGISTERED USERS
            foreach ($serviceIds as $serviceId)
            {
                $this->addCallerDisplayToAccount($serviceId);
            }
        }
        catch (\Exception $exception)
        {
            $this->log("FAILURE: An unexpected exception occurred - aborting the script. The exception was:\n");
            $this->log($exception);
        }
        finally
        {
            $this->logSuccessfulServiceIds();
            $this->logUnsuccessfulServiceIds();
            $this->logLineBreak();
            $this->log("SCRIPT ENDED AT: ".date("j.n.Y H:i:s") .  "\n\n");
            exit;
        }
    }

    /**
     * Adds caller display to an account given its service ID
     *
     * @param int $serviceId Service ID of the account we want to add caller display to
     *
     * @return void
     */
    private function addCallerDisplayToAccount($serviceId)
    {
        $callFeatureApi = new AccountChange_CallFeature_Api();

        try
        {
            $this->logLineBreak();
            $this->log("ATTEMPTING TO: Add caller display to account with service id: " . $serviceId . ".\n");
            $callFeatureApi->addCallFeature($callFeatureApi::CALLER_DISPLAY_COMPONENT_HANDLE, $serviceId);
            $this->successfullyAddedServiceIds[] = $serviceId;
            $this->log("SUCCESS: Added caller display to account.");
        }
        catch (AccountChange_CallFeature_ExistingCallFeatureException $exception)
        {
            // USER HAS CALLER DISPLAY
            $this->unsuccessfullyAddedServiceIds[$serviceId] = $exception->getMessage();
            $this->log("FAILURE: Could not add caller display to the account because they already have this component.");
        }
        catch (AccountChange_CallFeature_InactiveWlrComponentException $exception)
        {
            // USER DOES NOT HAVE AN ACTIVE WLR COMPONENT
            $this->unsuccessfullyAddedServiceIds[$serviceId] = $exception->getMessage();
            $this->log("FAILURE: Could not add caller display to the account because they do not have an active WLR component.");
        }
        catch (AccountChange_CallFeature_CallFeatureProvisioningException $exception)
        {
            // CALLER DISPLAY COULD NOT BE PROVISIONED - COMPONENT CREATION WAS ROLLED BACK
            $this->unsuccessfullyAddedServiceIds[$serviceId] = $exception->getMessage();
            $this->log("FAILURE: Could not add caller display to the account because there was an error with provisioning call feature provisioning.");
        }
    }

    /**
     * Logs all service ids of the accounts that have had caller display added successfully
     *
     * @return void
     */
    private function logSuccessfulServiceIds()
    {
        $this->log(
            "PRINTING SERVICE IDS OF ACCOUNTS THAT HAVE HAD CALLER DISPLAY ADDED SUCCESSFULLY",
            $this->getSuccessfullyAddedLogFileLocation());

        $this->logLineBreak(
            $this->getSuccessfullyAddedLogFileLocation());

        foreach ($this->successfullyAddedServiceIds as $serviceId)
        {
            $this->log(
                $serviceId,
                $this->getSuccessfullyAddedLogFileLocation());

            if($serviceId != end($this->successfullyAddedServiceIds))
            {
                $this->log(
                    "\n",
                    $this->getSuccessfullyAddedLogFileLocation());
            }
        }

        $this->logLineBreak(
            $this->getSuccessfullyAddedLogFileLocation());
    }

    /**
     * Logs all service ids of the accounts that did not have the caller display added successfully along with the reason
     *
     * @return void
     */
    private function logUnsuccessfulServiceIds()
    {
        $this->log(
            "PRINTING SERVICE IDS OF ACCOUNTS THAT FAILED TO HAVE CALLER DISPLAY ADDED AND THE REASON WHY",
            $this->getUnsuccessfullyAddedLogFileLocation());

        $this->logLineBreak(
            $this->getUnsuccessfullyAddedLogFileLocation());

        foreach ($this->unsuccessfullyAddedServiceIds as $serviceId => $reason)
        {
            $this->log(
                $serviceId . "\n",
                $this->getUnsuccessfullyAddedLogFileLocation());

            $this->log(
                $reason,
                $this->getUnsuccessfullyAddedLogFileLocation());

            $this->logLineBreak(
                $this->getUnsuccessfullyAddedLogFileLocation());
        }
    }

    /**
     * Adds a message to the log
     *
     * @param string $message Message to add to the log file
     * @param null $logLocation Location of the log - set to the general log by default
     *
     * @return void
     */
    private function log($message, $logLocation = null)
    {
        if(is_null($logLocation))
        {
            $logLocation = $this->getGeneralLogFileLocation();
        }

        file_put_contents($logLocation, $message, FILE_APPEND);
    }

    /**
     * Prints an asterisk line break to the log file - useful for separating information in the log
     *
     * @param null $logLocation Location of the log - set to the general log by default
     *
     * @return void
     */
    private function logLineBreak($logLocation = null)
    {
        $this->log(
            "\n\n*******************************************************************************************************\n\n",
            $logLocation);
    }

    /**
     * Gets the general log file location
     **/
    private function getGeneralLogFileLocation()
    {
        return "/tmp/AddCallerDisplayToPreregisteredUsers_log_".date("j.n.Y").".txt";
    }

    /**
     * Gets the successfully added log file location
     **/
    private function getSuccessfullyAddedLogFileLocation()
    {
        return "/tmp/SUCCESS_AddCallerDisplayToPreregisteredUsers_log_".date("j.n.Y").".txt";
    }

    /**
     * Gets the unsuccessfully added log file location
     **/
    private function getUnsuccessfullyAddedLogFileLocation()
    {
        return "/tmp/FAILED_AddCallerDisplayToPreregisteredUsers_log_".date("j.n.Y").".txt";
    }
}

$script = new AddCallerDisplayToPreregisteredUsers();
$script->run();