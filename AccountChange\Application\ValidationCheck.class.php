<?php
/**
 * AccountChange_ValidationCheck
 *
 * This class represents the internal checks necessary to determine if an
 * account change is going to be allowed to continue.
 *
 * @package    AccountChange
 * @subpackage Application
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 Plusnet
 * @link      http://documentation.plus.net/index.php/Account_Change
 */

/**
 * Class AccountChange_ValidationCheck
 */
class AccountChange_ValidationCheck
{
    /**
     * Business Actor Id
     *
     * @var Auth_BusinessActor
     */
    protected $actor;

    /**
     * Reason for the account change not being allowed
     *
     * @var string
     */
    protected $reasonForBlockage = false;

    private $validationPolicies;
    private $isWorkplace = false;
    private $isScript = false;
    private $errorCode;
    private $additionalInformation = array();

    /**
     * Constructor
     *
     * @param Auth_BusinessActor               $actor                 The business actor we are performing checks for
     * @param AccountChange_ValidationPolicy[] $validationPolicies    Validation policies
     * @param bool                             $isWorkplace           Flag to determine if WP
     * @param bool                             $isScript              Is script
     * @param array                            $additionalInformation Any additional information to pass through to validators.
     *
     * @return AccountChange_ValidationCheck
     */
    public function __construct(Auth_BusinessActor $actor, $validationPolicies = [], $isWorkplace = false, $isScript = false, $additionalInformation = array())
    {
        $this->actor = $actor;
        $this->validationPolicies = $validationPolicies;
        $this->isWorkplace = $isWorkplace;
        $this->isScript = $isScript;
        $this->requireLegacyLibraries();
        $this->additionalInformation = $additionalInformation;
    }

    /**
     * Based on the initial checks are we happy for the account change to take place
     *
     * @return boolean
     * @throws AccountChange_InvalidValidatorException
     */
    public function isAccountChangeAllowed()
    {
        if ($this->isScript === true) {
            return true;
        }

        foreach ($this->validationPolicies as $validationPolicyClass) {
            try {
                /** @var AccountChange_ValidationPolicy $validationPolicy */
                $validationPolicy = new $validationPolicyClass($this->actor, $this->isWorkplace, $this->isScript, $this->additionalInformation);
            } catch (Exception $e) {
                $validationPolicy = null;
            }

            if (!$validationPolicy instanceof AccountChange_ValidationPolicy) {
                throw new AccountChange_InvalidValidatorException("Invalid validator ($validationPolicyClass)");
            }
            try {
                if (!$validationPolicy->validate()) {
                    $this->reasonForBlockage = $validationPolicy->getFailure();
                    $this->errorCode = $validationPolicy->getErrorCode();
                    return false;
                }
            } catch (\Exception $e) {
                error_log($e->getMessage());
            }
        }

        return true;
    }

    /**
     * Returns a list of default validation policies to apply.
     *
     * @return array
     */
    public static function getDefaultPolicies()
    {
        // Policies added here will be executed at the start of the account change process and
        // stop the changes with an error message if they do not validate.
        return [
            AccountChange_HouseMovePolicy::class,
            AccountChange_PromotionCodePolicy::class,
            AccountChange_InvoiceDateCorrectPolicy::class,
            AccountChange_ExistingScheduledProductChangePolicy::class,
            AccountChange_MopManagedOrderPolicy::class,
            AccountChange_AuthSessionPolicy::class,
            AccountChange_BillingAccountSuspendedPolicy::class,
            AccountChange_BillingAccountStatusPolicy::class,
            AccountChange_BrandMigrationInProgressPolicy::class,
            AccountChange_WlrOrderStatusPolicy::class
        ];
    }

    /**
     * Return the array of reasons of why the account change is not allowed
     *
     * @return string|bool
     */
    public function getReasonForBlockage()
    {
        return $this->reasonForBlockage;
    }

    /**
     * Return error code for the validation policy that failed
     *
     * @return string
     */
    public function getErrorCode()
    {
        return $this->errorCode;
    }

    /**
     * @return void
     */
    protected function requireLegacyLibraries()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
    }
}
