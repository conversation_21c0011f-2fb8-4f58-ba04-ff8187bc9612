<?php

/**
 * Abstract Scheduled Change File
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 * @link      link
 */
/**
 * Abstract Scheduled Change class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 * @link      link
 */
abstract class AccountChange_ScheduledChange_Abstract implements AccountChange_IScheduledChange
{
    /**
     * Data from userdata.service_change_schedule
     *
     * @var array
     */
    protected $data = array();

    /**
     * Return the internal bag of data
     *
     * @return array
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * Is there a change scheduled in the database for this product
     *
     * @return boolean
     */
    public function hasScheduledChange()
    {
        return !empty($this->data);
    }

    /**
     * Get the date of the sheduled change
     *
     * @return I18n_Date|null
     */
    public function getChangeDate()
    {
        if ($this->hasScheduledChange()) {
            return $this->data['dteChangeDate'];
        }

        return null;
    }

    /**
     * Get the name of the new product
     *
     * @return string
     */
    public function getNewProductName()
    {
        if ($this->hasScheduledChange()) {
            return $this->data['strNewProductName'];
        }

        return null;
    }

    /**
     * Creates and returns a RetentionsTool_Manager object
     *
     * @param Auth_BusinessActor $targetActor Target business actor
     *
     * @return RetentionsTool_Manager
     */
    public function getRetentionManager($targetActor)
    {
        $retentionManager = null;

        if ($this->hasScheduledChange()) {

            $data = $this->getData();

            $scheduleType = null;

            switch ($this->getProductType()) {

                case 'Broadband':
                    $scheduleType = RetentionsTool_Manager::SCHEDULE_TYPE_ADSL;
                    break;
                case 'Phone':
                    $scheduleType = RetentionsTool_Manager::SCHEDULE_TYPE_WLR;
                    break;
            }

            if (!empty($scheduleType)) {

                $retentionManager = RetentionsTool_Manager::getByScheduledEvent(
                    $scheduleType,
                    $data['intScheduleId'],
                    $targetActor
                );
            }
        }

        return $retentionManager;
    }
}