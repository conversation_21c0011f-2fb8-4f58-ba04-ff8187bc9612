<?php
/**
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 */

class AccountChange_EmailHandler_Completion extends AccountChange_EmailHandler
{
    private $broadbandProduct;

    private $estimatedDownloadSpeedRangeLower;

    private $estimatedDownloadSpeedRangeHigher;

    private $strUserRealm;

    private $intContractLengthInMonths;

    private $isContractRetained;

    private $strContractEndDate;

    private $isNewProductDualPlay;

    private $floDiscountedProductCost;

    private $floOngoingProductCost;

    private $intDiscountLength;

    private $bolOptOutMessage;

    private $bolHomePhone;

    private $installationTypeFTTP;

    /**
     * AccountChange_EmailHandler_Confirmation constructor.
     * @param AccountChange_PerformChangeApi         $performChangeApi perform account change api
     * @param AccountChange_AccountChangeOrder       $order            order
     * @param AccountChange_EmailHandler_SpeedHelper $speedHelper      speed helper
     * @param AccountChange_EmailHandler_PriceHelper $priceHelper      price helper
     * @param AccountChange_CompleteContractHelper   $contractHelper   contract helper
     */
    public function __construct(
        AccountChange_PerformChangeApi   $performChangeApi,
        AccountChange_AccountChangeOrder $order,
        AccountChange_EmailHandler_SpeedHelper $speedHelper,
        AccountChange_EmailHandler_PriceHelper $priceHelper,
        AccountChange_CompleteContractHelper $contractHelper
    ) {
        $serviceId = $order->getServiceId();
        $this->setBroadbandProduct($performChangeApi->getNewProductName());
        $speedData = $speedHelper->getSpeedData();
        $this->setEstimatedDownloadSpeedRangeLower($speedData['minimumEstimatedDownloadSpeedMbs']);
        $this->setEstimatedDownloadSpeedRangeHigher($speedData['maximumEstimatedDownloadSpeedMbs']);
        $this->setUserRealm($this->adslGetUserRealm($serviceId));
        $priceData = $priceHelper->getNewPackagePrices();
        $this->setDiscountLength(isset($priceData['intDiscountLength']) ? $priceData['intDiscountLength'] : null);
        $this->setOngoingProductCost($priceData['total']);
        $this->setDiscountedProductCost(isset($priceData['discountAmount']) ? $priceData['discountAmount'] : null);
        $contractDetails = $contractHelper->getContractDetailsForEmail();
        $this->setContractLengthInMonths($contractDetails['intContractLengthInMonths']);
        $this->setContractRetained($contractDetails['isContractRetained']);
        $this->setContractEndDate($contractDetails['strContractEndDate']);
        $this->setIsNewProductDualPlay($contractDetails['isNewProductDualPlay']);
        $this->setBolHomePhone($performChangeApi->getNewWlrComponentId());
        $this->setOptoutMessage($speedHelper->getOptOut($order->getLineCheckId()));
        $technologyType = $performChangeApi->getTechnologyType();
        $this->setInstallationType(($technologyType === 'FTTP') ?
            $performChangeApi->getLineCheckResults()->getFttpInstallProcess() : null);
    }

    /**
     * @return boolean
     */
    public function getBolHomePhone()
    {
        return $this->bolHomePhone;
    }

    /**
     * @param boolean $bolHomePhone home phone
     * @return void
     */
    public function setBolHomePhone($bolHomePhone)
    {
        $this->bolHomePhone = $bolHomePhone;
    }

    /**
     * @return boolean
     */
    public function getOptOutMessage()
    {
        return $this->bolOptOutMessage;
    }

    /**
     * @param boolean $bolOptoutMessage output message
     * @return void
     */
    public function setOptoutMessage($bolOptoutMessage)
    {
        $this->bolOptOutMessage = $bolOptoutMessage;
    }

    /**
     * @return int
     */
    public function getIntContractLengthInMonths()
    {
        return $this->intContractLengthInMonths;
    }

    /**
     * @param int $intContractLengthInMonths contract length
     * @return void
     */
    public function setContractLengthInMonths($intContractLengthInMonths)
    {
        $this->intContractLengthInMonths = $intContractLengthInMonths;
    }

    /**
     * @return boolean
     */
    public function isContractRetained()
    {
        return $this->isContractRetained;
    }

    /**
     * @param boolean $isContractRetained isContractRetained
     * @return void
     */
    public function setContractRetained($isContractRetained)
    {
        $this->isContractRetained = $isContractRetained;
    }

    /**
     * @return string
     */
    public function getContractEndDate()
    {
        return $this->strContractEndDate;
    }

    /**
     * @param string $strContractEndDate Contract End Date
     * @return void
     */
    public function setContractEndDate($strContractEndDate)
    {
        $this->strContractEndDate = $strContractEndDate;
    }

    /**
     * @return boolean
     */
    public function getisNewProductDualPlay()
    {
        return $this->isNewProductDualPlay;
    }

    /**
     * @param boolean $isNewProductDualPlay isNewProductDualPlay
     * @return void
     */
    public function setIsNewProductDualPlay($isNewProductDualPlay)
    {
        $this->isNewProductDualPlay = $isNewProductDualPlay;
    }

    /**
     * @return float
     */
    public function getDiscountedProductCost()
    {
        return $this->floDiscountedProductCost;
    }

    /**
     * @param float $floDiscountedProductCost DiscountedProductCost
     * @return void
     */
    public function setDiscountedProductCost($floDiscountedProductCost)
    {
        if ($floDiscountedProductCost != null) {
            $this->floDiscountedProductCost = $this->getOngoingProductCost() - $floDiscountedProductCost;
        }
    }

    /**
     * @return float
     */
    public function getOngoingProductCost()
    {
        return $this->floOngoingProductCost;
    }

    /**
     * @param float $floOngoingProductCost OngoingProductCost
     * @return void
     */
    public function setOngoingProductCost($floOngoingProductCost)
    {
        $this->floOngoingProductCost = $floOngoingProductCost;
    }

    /**
     * @return int
     */
    public function getDiscountLength()
    {
        return $this->intDiscountLength;
    }

    /**
     * @param int $intDiscountLength DiscountLength
     * @return void
     */
    public function setDiscountLength($intDiscountLength)
    {
        $this->intDiscountLength = $intDiscountLength;
    }


    /**
     * @param string $strUserRealm UserRealm
     * @return void
     */
    protected function setUserRealm($strUserRealm)
    {
        $this->strUserRealm = $strUserRealm;
    }

    /**
     * @return String
     */
    protected function getUserRealm()
    {
        return $this->strUserRealm;
    }

    /**
     * @param string $broadbandProduct broadband product
     * @return void
     */
    protected function setBroadbandProduct($broadbandProduct)
    {
        $this->broadbandProduct = $broadbandProduct;
    }

    /**
     * @param string $estimatedDownloadSpeedRangeLower estimatedDownloadSpeedRangeLower
     * @return void
     */
    public function setEstimatedDownloadSpeedRangeLower($estimatedDownloadSpeedRangeLower)
    {
        $this->estimatedDownloadSpeedRangeLower = $estimatedDownloadSpeedRangeLower;
    }

    /**
     * @param string $estimatedDownloadSpeedRangeHigher estimatedDownloadSpeedRangeHigher
     * @return void
     */
    public function setEstimatedDownloadSpeedRangeHigher($estimatedDownloadSpeedRangeHigher)
    {
        $this->estimatedDownloadSpeedRangeHigher = $estimatedDownloadSpeedRangeHigher;
    }

    /**
     * @param string $installType install type
     * @return void
     */
    public function setInstallationType($installType)
    {
        $this->installationTypeFTTP = $installType;
    }

    /**
     * Transfers local variables into an array ready to be used
     *
     * @return array email variables
     */
    protected function populateEmailVariables()
    {
        return array(
            "strProduct" => $this->getBroadbandProduct(),
            "estimatedSpeedRangeLow" => $this->getEstimatedDownloadSpeedRangeLower(),
            "estimatedSpeedRangeHigh" => $this->getEstimatedDownloadSpeedRangeHigher(),
            "bolDisplayUsageInfo" => 0,
            "strUserRealm" => $this->getUserRealm(),
            "intContractLengthInMonths" => $this->formatForMsn($this->getIntContractLengthInMonths()),
            "isContractRetained" => $this->formatForMsn($this->isContractRetained()),
            "objContractEndDate" => $this->formatForMsn($this->getContractEndDate()),
            "isNewProductDualPlay" => $this->formatForMsn($this->getisNewProductDualPlay()),
            "floDiscountedProductCost" => $this->formatForMsn($this->getDiscountedProductCost()),
            "floOngoingProductCost" => $this->getOngoingProductCost(),
            "intDiscountLength" => $this->formatForMsn($this->getDiscountLength()),
            "bolOptoutMessage" => $this->formatForMsn($this->getOptOutMessage()),
            "bolHomePhone" => $this->formatForMsn($this->getBolHomePhone()),
            "strAuthenticationRealm" => $this->getUserRealm(),
            "bolSpeedChecked" => $this->formatForMsn(!$this->getOptOutMessage()),
            "installationTypeFTTP" => $this->formatForMsn($this->getInstallationTypeFTTP())
        );
    }

    /**
     * Wrapper for {@link adslGetUserRealm()}
     *
     * @param int $intServiceId the service id
     *
     * @return int
     */
    protected function adslGetUserRealm($intServiceId)
    {
        $this->includeLegacyFiles();
        return adslGetUserRealm($intServiceId);
    }

    /**
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/www/database-admin/config/config.inc';
    }

    /**
     * @param mixed $var variable
     * @return string|int
     */
    protected function formatForMsn($var)
    {
        if (is_null($var)) {
            return 0;
        }
        if (is_bool($var)) {
            return $var ? 1 : 0;
        }
        return $var;
    }

    /**
     * @return string
     */
    public function getEmailName()
    {
        return 'CUSTOMER_PRODUCT_CHANGE_COMPLETE';
    }

    /**
     * @return string
     */
    public function getBroadbandProduct()
    {
        return $this->broadbandProduct;
    }

    /**
     * @return string
     */
    public function getEstimatedDownloadSpeedRangeLower()
    {
        return $this->estimatedDownloadSpeedRangeLower;
    }

    /**
     * @return string
     */
    public function getEstimatedDownloadSpeedRangeHigher()
    {
        return $this->estimatedDownloadSpeedRangeHigher;
    }

    /**
     * @return string
     */
    public function getInstallationTypeFTTP()
    {
        return $this->installationTypeFTTP;
    }

    /**
     * @param int $serviceId serviceId
     * @return void
     */
    public function sendEmail($serviceId)
    {
        $this->sendEmailMSN($serviceId, $this->getEmailName(), $this->populateEmailVariables(), 'COMPLETION');
    }
}
