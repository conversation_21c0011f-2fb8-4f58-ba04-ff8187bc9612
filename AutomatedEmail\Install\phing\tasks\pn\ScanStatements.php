<?php
include_once 'phing/Task.php';
class ScanStatements extends Task {

    private $_strDir = null;
    private $_strUser = null;
    private $_strReturnName;
    private $_arrFoundServers;

    public function setBaseDir($str) {
        $this->_strDir = $str;
    }
    
    public function setUser($strUser) {
        $this->_strUser = $strUser;
    }

    public function setReturnName($str) {
        $this->_strReturnName = $str;
    }    

    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		}  
		
		if(!is_dir($this->_strDir)) {
			throw new BuildException("$this->_strDir is not a valid directory", $this->getLocation());		
		}

		$this->_scanDirForStatements($this->_strDir);

		//filter duplicates
		$this->_arrFoundServers = array_unique($this->_arrFoundServers);
		
		//query servers
		$this->_queryServers();
		
    }
    
    private function _scanDirForStatements($strPath)
    {
		if(is_dir($strPath)) {
			
			if ($handle = opendir($strPath)) {
				$this->log("Scanning directory: $strPath");
				while (false !== ($file = readdir($handle))) {
			       
					if ($file=='.' or $file=='..' or $file=='CVS') continue;
					   
					$strTarget = $strPath.'/'.$file;
					$this->_scanDirForStatements($strTarget);
		   		}
			}
		} else {
			//it's a file, scan for server name and role
			$fileContents = file_get_contents($strPath);

			$strServer = '';
			$strRole = '';

			// get server name
			$arrMatches = array();
			if(preg_match('/server\s*:\s*(\w+)/', $fileContents, $arrMatches))
			{
				$strServer = $arrMatches[1];
			}

			// get server role
			$arrMatches = array();
			if(preg_match('/role\s*:\s*(\w+)/', $fileContents, $arrMatches))
			{
				$strRole = $arrMatches[1];
			}

			if(($strServer != '') && ($strRole != ''))
			{
				$this->_arrFoundServers[] = strtolower($strServer . '_' . $strRole);
			}
		}
    }

	private function _queryServers()
	{	
		$frameworkDir = $this->project->getProperty('frameworkDir');
		if(!@include_once($frameworkDir.'/Libraries/bootstrap.inc.php')) {
			throw new BuildException('Missing ClassLoader', $this->getLocation());
		}	

		foreach ($this->_arrFoundServers as $strServerName) {
			try {
				Db_Manager::getConnection($strServerName);
				$this->log("Querying server: $strServerName ... OK");	
			} catch(Exception $e){
				$this->log("Querying server: $strServerName ... FAILED!");
				$this->project->setProperty($this->_strReturnName, "Server $strServerName failed to respond - maybe you need to run 'cd /local/codebase2005/modules/Framework/Install && php generateDbFiles.php'");
			}
		}
	}
}
