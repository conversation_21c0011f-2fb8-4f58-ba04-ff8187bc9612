<?php

class AccountChange_EmailHandler_PromotionHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Test that discount data is retrieved from a c2m promotion
     *
     * @covers AccountChange_EmailHandler_PromotionHelper::getDiscountData()
     */
    public function testThatDiscountDataIsRetrievedFromC2M()
    {
        $broadbandPrice = 15;

        $promotionHelper = $this->getMock(
            'AccountChange_EmailHandler_PromotionHelper',
            array('getC2MPromotionFromPromotionCode'),
            array(null, null, $broadbandPrice, null)
        );

        $discountValue = new Plusnet\C2mApiClient\Entity\DiscountValue();
        $discountValue->setValue('10');
        $discountValue->setDuration('12');
        $discountValue->setDiscountValueType('FIXED_AMOUNT');

        $discount = new Plusnet\C2mApiClient\Entity\Discount();
        $discount->setDiscountValues(array($discountValue));
        $discount->setType('ON_GOING');

        $promotion = new Plusnet\C2mApiClient\Entity\Promotion();
        $promotion->setDiscounts(array($discount));

        $promotionHelper
            ->expects($this->once())
            ->method('getC2MPromotionFromPromotionCode')
            ->willReturn($promotion);

        $expected = array(
            'discountAmount'   => 10,
            'discountDuration' => '12'
        );

        $actual = $promotionHelper->getDiscountData();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that discount data is retrieved from a c2m promotion
     * Also tests that having a cash-back discount type does not take that discount into account when
     * calculating duration and amount of discount.
     *
     * @covers AccountChange_EmailHandler_PromotionHelper::getDiscountData()
     */
    public function testThatDiscountDataIsRetrievedFromC2MAndInterpretedCorrectlyWhenThereIsANonOngoingPromotion()
    {
        $broadbandPrice = 15;

        $promotionHelper = $this->getMock(
            'AccountChange_EmailHandler_PromotionHelper',
            array('getC2MPromotionFromPromotionCode'),
            array(null, null, $broadbandPrice, null)
        );

        $discountValue = new Plusnet\C2mApiClient\Entity\DiscountValue();
        $discountValue->setValue('10');
        $discountValue->setDuration('12');
        $discountValue->setDiscountValueType('FIXED_AMOUNT');

        $discount = new Plusnet\C2mApiClient\Entity\Discount();
        $discount->setDiscountValues(array($discountValue));
        $discount->setDiscountValues(array($discountValue));
        $discount->setType('CASH_BACK');

        $discount2 = new Plusnet\C2mApiClient\Entity\Discount();
        $discount2->setDiscountValues(array($discountValue));
        $discount2->setDiscountValues(array($discountValue));
        $discount2->setType('ON_GOING');

        $promotion = new Plusnet\C2mApiClient\Entity\Promotion();
        $promotion->setDiscounts(array($discount, $discount2));

        $promotionHelper
            ->expects($this->once())
            ->method('getC2MPromotionFromPromotionCode')
            ->willReturn($promotion);

        $expected = array(
            'discountAmount'   => 10,
            'discountDuration' => '12'
        );

        $actual = $promotionHelper->getDiscountData();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test that discount data is retrieved from a legacy preset discount
     *
     * @covers AccountChange_EmailHandler_PromotionHelper::getDiscountData()
     */
    public function testThatDiscountDataIsRetrievedFromLegacyPresetDiscountTables()
    {
        $broadbandPrice = 15;

        $promotionHelper = $this->getMock(
            'AccountChange_EmailHandler_PromotionHelper',
            array(
                'getC2MPromotionFromPromotionCode',
                'getLegacyPresetDiscountFromPromotionCode'),
            array(null, null, $broadbandPrice, null)
        );

        $promotionHelper
            ->expects($this->once())
            ->method('getC2MPromotionFromPromotionCode')
            ->willReturn(null);

        $promotionHelper
            ->expects($this->once())
            ->method('getLegacyPresetDiscountFromPromotionCode')
            ->willReturn(array(
                'decValue'          => 10,
                'intDiscountLength' => 12,
                'promoDiscountType' => 1
            ));

        $expected = array(
            'discountAmount'   => 10,
            'discountDuration' => '12'
        );

        $actual = $promotionHelper->getDiscountData();

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test exception is thrown when the promotion retrieved from C2M contains discounts with different durations
     *
     * @expectedException AccountChange_EmailHandler_Exception
     * @covers AccountChange_EmailHandler_PromotionHelper::getDiscountData()
     */
    public function testThatExceptionIsThrownWhenDiscountsHaveDifferentDurations()
    {
        $broadbandPrice = 15;

        $promotionHelper = $this->getMock(
            'AccountChange_EmailHandler_PromotionHelper',
            array('getC2MPromotionFromPromotionCode'),
            array(null, null, $broadbandPrice, null)
        );

        $discountValue1 = new Plusnet\C2mApiClient\Entity\DiscountValue();
        $discountValue1->setValue('10');
        $discountValue1->setDuration('12');
        $discountValue1->setDiscountValueType('FIXED_AMOUNT');

        $discount1 = new Plusnet\C2mApiClient\Entity\Discount();
        $discount1->setDiscountValues(array($discountValue1));

        $discountValue2 = new Plusnet\C2mApiClient\Entity\DiscountValue();
        $discountValue2->setValue('10');
        $discountValue2->setDuration('6');
        $discountValue2->setDiscountValueType('FIXED_AMOUNT');

        $discount2 = new Plusnet\C2mApiClient\Entity\Discount();
        $discount2->setDiscountValues(array($discountValue2));

        $promotion = new Plusnet\C2mApiClient\Entity\Promotion();
        $promotion->setDiscounts(array($discount1, $discount2));

        $promotionHelper
            ->expects($this->once())
            ->method('getC2MPromotionFromPromotionCode')
            ->willReturn($promotion);

        $promotionHelper->getDiscountData();
    }
}
