<?php
include_once 'phing/Task.php';
class HandlesCheck extends Task {

    private $_strDir;
    
    private $_strReturnName;
    
    private $_arrHandles;
    
    public function setDir($strDir) {
        $this->_strDir = $strDir;
    }

    public function setReturnName($strName) {
        $this->_strReturnName = $strName;
    }
    
    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {

		if (!$this->_strReturnName) {
		    throw new BuildException('You must specify the returnName attribute', $this->getLocation());
		}
		
		if (!$this->_strDir) {
		    throw new BuildException('You must specify the dir attribute', $this->getLocation());
		}

		if (!is_dir($this->_strDir)) {
		    throw new BuildException("$this->_strDir is not a directory", $this->getLocation());
		}
		
		//grep modules for handles
		$this->_scanDirForHandles($this->_strDir);

		if(is_array($this->_arrHandles) && (count($this->_arrHandles) > 0))
		{
			//get session ids objects for partners found in statements directory
			$arrSessionIDs = $this->_getSessionIDs();

			foreach ($this->_arrHandles as $strHandleType => $arrHandle) {

				//choose adaptor
				$objSoapClient = $this->_getSoapAdaptor($strHandleType);

				//check all handles of type $strHandleType
				foreach ($arrHandle as $strHandle => $strFilename) {
					
					if($objSoapClient instanceof SoapClient) { 

						//make the same check for each partner
						foreach ($arrSessionIDs as $objSessionID) {
							$bolHandleCheck = $objSoapClient->doesHandleExist($objSessionID, 1,  $strHandle);
							if(!$bolHandleCheck) {
								$this->log("Invalid handle $strHandle partner:$objSessionID->partnerDataset file:$strFilename");
								$failed = true;	
							}
						}
					}
				}
			}
		}
		if(!isset($failed)) $this->project->setProperty($this->_strReturnName, true);
    }

	/**
	 * Scans module directory for handles in use
	 */
    private function _scanDirForHandles($strPath)
    {
		if(is_dir($strPath)) {
			
			if ($handle = opendir($strPath)) {
				while (false !== ($file = readdir($handle))) {
			       
					if ($file=='.' or $file=='..' or $file=='CVS') continue;
					   
					$strTarget = $strPath.'/'.$file;
					$this->_scanDirForHandles($strTarget);
		   		}
			}
		} else {
			//scan for handles
			if (substr($strPath,-3) == 'php') {
				$fileContents = php_strip_whitespace($strPath);
				$arrMatches = array();
				if(preg_match_all('/[\'|"]([A-Z]+)_([A-Z0-9]+)_HANDLE[\'|"]/',$fileContents,$arrMatches)) {
					
					foreach ($arrMatches[0] as $key => $strHandle) {
						//strip quotes
						$strHandle = str_replace(array('\'','"'),'',$strHandle);

						$this->_arrHandles[$arrMatches[1][$key]][$strHandle] = $strPath;	
					}
				}
			}
		}
    }    
    
    /**
     * Returns session id objects for each partner used by the module,
     * based on the content of Statements directory
     */
    private function _getSessionIDs()
    {
		$arrSessionIDs = array();
		$strPath =  $this->_strDir.'/Statements';
		$objSessionManager = new Auth_SessionManager();
		
		if ($handle = opendir($strPath)) {
			while (false !== ($file = readdir($handle))) {
		       
				if ($file=='.' or $file=='..' or $file=='CVS') continue;
				$arrSessionIDs[] = $objSessionManager->getBusinessTierSession('script', 'script', 'portal.plus.net', $file);
	   		}
		}
		return $arrSessionIDs;
    }    
    
    /**
     * Returns soap adaptor instance for each handle type
     */
    private function _getSoapAdaptor($strHandleType)
    {
		
		$arrOptions = array("trace" => 1, "exceptions" => 1, 'classmap' => $arrClassMap);
		
		if($strHandleType == 'PROB') {
			$objSoapClient = new SoapClient('http://adaptors.faults.plus.net/SoapServices/LegacyAdaptors/ProblemAdaptor/ProblemAdaptor.php?wsdl', $arrOptions);	
		} elseif ($strHandleType == 'MAIL') {
			$objSoapClient = new SoapClient('http://adaptors.faults.plus.net/SoapServices/LegacyAdaptors/SendMailAdaptor/SendMailAdaptor.php?wsdl', $arrOptions);	
		} elseif ($strHandleType == 'TICKET') {
			$objSoapClient = new SoapClient('http://adaptors.faults.plus.net/SoapServices/LegacyAdaptors/TicketsAdaptor/TicketAdaptor.php?wsdl', $arrOptions);	
		}

		return $objSoapClient;
    }
    
}
