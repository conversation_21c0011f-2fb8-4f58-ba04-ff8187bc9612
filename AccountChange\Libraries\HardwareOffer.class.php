<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_HardwareOffer
{
    private $hardwareBundle;
    private $shouldOfferHardware = false;
    private $hardwareDefaultSelection = false;

    /**
     * AccountChange_HardwareOffer constructor.
     *
     * @param HardwareClient_Bundle $hardwareBundle hardware bundle
     */
    public function __construct($hardwareBundle)
    {
        $this->hardwareBundle = $hardwareBundle;
    }

    /**
     * @param bool $shouldOfferHardware are we offering the bundle to the customer
     * @return void
     */
    public function setShouldOfferHardware($shouldOfferHardware)
    {
        $this->shouldOfferHardware = $shouldOfferHardware;
    }

    /**
     * @return bool
     */
    public function shouldOfferHardware()
    {
        return $this->shouldOfferHardware;
    }

    /**
     * @param bool $hardwareDefaultSelection when presented, is the hardware selected by default
     * @return void
     */
    public function setHardwareSelectedByDefault($hardwareDefaultSelection)
    {
        $this->hardwareDefaultSelection = $hardwareDefaultSelection;
    }

    /**
     * @return string|null
     */
    public function getHardwareDefaultSelection()
    {
        if ($this->hardwareDefaultSelection) {
            return $this->hardwareBundle->getHandle();
        }

        return null;
    }

    /**
     * @return array
     */
    public function getHardwareBundleDetails()
    {
        return [
            'handle' => $this->hardwareBundle->getHandle(),
            'name' => $this->hardwareBundle->getName(),
        ];
    }

    /**
     * @return String
     */
    public function getHardwareHandle()
    {
        return $this->hardwareBundle->getHandle();
    }
}
