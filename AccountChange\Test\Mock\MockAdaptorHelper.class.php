<?php
/**
 * AccountChange Mock Helper
 *
 * Helper class which provides mocks for account change unit tests
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @since     File available since
 */
/**
 * AccountChange Mock Helper
 *
 * Helper class which provides mocks for account change unit tests
 *
 * @category  AccountChange
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 */
class AccountChange_MockAdaptorHelper extends PHPUnit_Framework_TestCase
{

    /**
     *
     * Function to provide return for getApplicationStateVariable() mock
     */
    public static function getDataForGetApplicationStateVariable()
    {
        $arrArgs = func_get_args();
        $Result = '';

        if (isset($arrArgs[1])) {

            switch($arrArgs[1]) {
                case 'intOldSdi':
                          $Result = '21';
                    break;
                case 'intNewSdi':
                    $Result = '22';
                    break;
                case 'arrWlrProduct':
                    $intValidWlrId   = 1111;
                    $strValidName    = 'Test Wlr Product';
                    $intProductCost  = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 10);
                    $intLineRentCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 20);
                    $intCallPlanCost = new I18n_Currency(AccountChange_Manager::CURRENCY_UNIT, 30);
                    $bolSplitPrice   = 1;

                    $arrProducts =  array(
                                'intNewWlrId'       => $intValidWlrId,
                                'strContractHandle' => 'ANNUAL',
                                'strProductName'    => $strValidName,
                                'intProductCost'    => $intProductCost,
                                'intLineRentCost'   => $intLineRentCost,
                                'intCallPlanCost'   => $intCallPlanCost,
                                'bolSplitPrice'     => $bolSplitPrice,
                                'intOldWlrId'       => '122'
                                );
                    $Result = $arrProducts;
                    break;
                case 'objCoreService':

                    $objCoreService = new Core_Service(1234);
                    $Result = $objCoreService;
                    break;
                case 'intNewWlrId':
                    $Result = 12;
                    break;
                case 'objUserActor':
                    $Result= new Auth_BusinessActor(null);
                    break;
                case 'objBusinessActor':
                    $objMockAdaptor = new AccountChange_MockAdaptorHelper();
                    $Result = $objMockAdaptor->mockAuthBusinessActorProvider();
                    break;
                case 'objLineCheckResult':
                    $objMockAdaptor = new AccountChange_MockAdaptorHelper();
                    $Result = $objMockAdaptor->lineCheckResultProvider();
                    break;
            }

        }

        return $Result;
    }

    /**
     * get a mock Auth_BusinessActor object
     *
     * @return object mock Auth_BusinessActor object
     */
    public function mockAuthBusinessActorProvider()
    {
        $mockBusActor = $this->getMockBuilder('Auth_BusinessActor')
            ->disableOriginalConstructor()
            ->setMethods(array('getUserType'))
            ->getMock();

        $mockBusActor->expects($this->any())
            ->method('getUserType')
            ->will($this->returnValue('NOT_PLUSNET_STAFF'));
        return $mockBusActor;
    }

    /**
     * Function to provide Linecheck result.
     */
    public function lineCheckResultProvider()
    {
        $result =
                array
                    (
                        'intLineCheckId' => 1,
                        'intLineCheckStatusId' => 1,
                        'intServiceId' => 1,
                        'intServiceDefinitionId' => 1,
                        'intErrorId' => 0,
                        'vchLineCheckInput' => '***********',
                        'ch.dtmLineCheckDate' => '2009-03-26 18:42:43',
                        'chrReasonCode' => 'L',
                        'vchExchangeName' => 'LSSID',
                        'vchExchangeCode' => 'LSSID',
                        'chrFixedRateRag' => 'G',
                        'dteFixedRateReadyDate' => '',
                        'chrFixedRateExchState' => 'E',
                        'chrRateAdaptiveRag' => 'G',
                        'dteRateAdaptiveReadyDate' => '',
                        'chrRateAdaptiveExchState' => 'E',
                        'chrMaxRag' => 'G',
                        'intMaxSpeed' => 4500,
                        'dteMaxReadyDate' => '',
                        'chrMaxExchState' => 'E',
                        'chrWbcRag' => 'G',
                        'intWbcSpeed' => 6000,
                        'dteWbcReadyDate' => '',
                        'chrWbcExchState' => 'E',
                        'vchPostCode' => 'DA5 3EQ'
                    );
        // Mock Db_Adaptor
        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getPendingLineCheckResult', 'getHighestAvailableSpeed', 'getExchangeDetailsByCode'),
            array('LineCheck', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objMockDbAdaptor->expects($this->any())
                 ->method('getPendingLineCheckResult')
                 ->will($this->returnValue($result));

        $objMockDbAdaptor->expects($this->any())
                 ->method('getHighestAvailableSpeed')
                 ->will($this->returnValue('2MB'));

        $objMockDbAdaptor->expects($this->any())
                 ->method('getExchangeDetailsByCode')
                 ->will($this->returnValue(array()));

        Db_Manager::setAdaptor('LineCheck', $objMockDbAdaptor);

        $linkCheck = $this->getMock(
            'LineCheck_Result',
            array('getHighestAvailableSpeed', 'getLineCheckType', 'getErrorId', 'buildExchangeFromCode'),
            array()
        );

        $linkCheck->expects($this->any())
                  ->method('getHighestAvailableSpeed')
                  ->will($this->returnValue('2000'));

        $linkCheck->expects($this->any())
                  ->method('getLineCheckType')
                  ->will($this->returnValue(LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO));

        $linkCheck->expects($this->any())
                  ->method('getErrorId')
                  ->will($this->returnValue(0));

        $linkCheck
            ->expects($this->once())
            ->method('buildExchangeFromCode')
            ->will($this->returnValue(null));

        return $linkCheck;
    }

    /**
     *
     * Function to provide AccountChange_Manager Object
     */
    public function accountChangeManagerObjectProvider()
    {
        $this->setCoreMockAdaptor();
        $this->setValMockAdaptor();

        $intServiceId = 1234;

        $objOldServiceDefinition = new AccountChange_Product_ServiceDefinition(1, true);
        $objOldServiceComponent  = new AccountChange_Product_ServiceComponent(1, true);

        $objOldConfiguration = new AccountChange_AccountConfiguration(
            array($objOldServiceDefinition),
            array($objOldServiceComponent)
        );

        $objNewServiceDefinition = new AccountChange_Product_ServiceDefinition(2, true);
        $objNewServiceComponent  = new AccountChange_Product_ServiceComponent(2, true);
        $objNewConfiguration = new AccountChange_AccountConfiguration(
            array($objNewServiceDefinition),
            array($objNewServiceComponent)
        );

        $objActionManager = $this->getMock(
            'AccountChange_Action_Manager',
            array('initialise', 'execute'),
            array($intServiceId)
        );

        $objActionManager->expects($this->any())
                 ->method('initialise');

        $objActionManager->expects($this->any())
                 ->method('execute');

        $objAccountChangeManager = new AccountChange_Manager(
            $intServiceId,
            $objOldConfiguration,
            $objNewConfiguration,
            $objActionManager
        );

        return $objAccountChangeManager;

    }


    /**
     *
     *Function mock db_adaptor for Core module.
     */
    public function setCoreMockAdaptor()
    {

        $objMockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceDao', 'getUserDao', 'getAddressDao', 'getServiceDefinitionDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objMockDbAdaptor->expects($this->any())
                 ->method('getServiceDao')
                 ->will($this->returnValue($this->getServiceDaoData()));

        $objMockDbAdaptor->expects($this->any())
                 ->method('getUserDao')
                 ->will($this->returnValue($this->getUserDaoData()));

        $objMockDbAdaptor->expects($this->any())
                 ->method('getAddressDao')
                 ->will($this->returnValue($this->getAddressDaoData()));

        $objMockDbAdaptor->expects($this->any())
                 ->method('getServiceDefinitionDao')
                 ->will($this->returnValue($this->getServiceDefinitionDaoData()));

        Db_Manager::setAdaptor('Core', $objMockDbAdaptor);
    }

    /**
     *
     * Function to mock the db_adaptor for the finance module.
     */
    public function setFinaceMockAdaptor()
    {

        $objFinanceDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getDdiByServiceAndStatuses'),
            array('Financial', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objFinanceDbAdaptor->expects($this->any())
                    ->method('getServiceDefinitionDao')
                    ->will($this->returnValue(null));

        Db_Manager::setAdaptor('Financial', $objFinanceDbAdaptor);
    }

    /**
     *
     * Function to mock the db_adaptor for Auth module.
     */
    public function setAuthMockAdaptor()
    {

        $objAuthMockAdaptor = $this->getMock(
            'Db_Adaptor',
            array('geBusinessActor'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $objAuthMockAdaptor->expects($this->any())
                   ->method('getBusinessActor')
                   ->will($this->returnValue($this->getBusinessActorData()));

        Db_Manager::setAdaptor('Auth', $objAuthMockAdaptor);
    }

    /**
     *
     * Function to mock db_adaptor for Val Module
     */
    public function setValMockAdaptor()
    {
        $objMockValAdaptor = $this->getMock(
            'Db_Adaptor',
            array(
                'getISPConfig',
                'getServiceComponentDetails',
                'getServiceComponentConfigByProductID',
                'getServiceDefinition'
            ),
            array('Val', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $objMockValAdaptor->expects($this->any())
            ->method('getISPConfig')
            ->will($this->returnValue(array('intISPDefinitionID' => 515)));

         $objMockValAdaptor->expects($this->any())
                  ->method('getServiceComponentConfigByProductID')
                   ->will(
                       $this->returnValue(
                           array(
                               "intServiceComponentConfigID" => 12,
                               "intServiceComponentID" => 41,
                               "intPointsValue" => 2
                           )
                       )
                   );

        $objMockValAdaptor->expects($this->any())
                  ->method('getServiceComponentDetails')
                  ->will(
                      $this->returnValue(
                          array(
                              'strName' => 'testName',
                              'strDescription' => 'testDescription',
                              'strAvailable' => 'testAvailable'
                          )
                      )
                  );
        $objMockValAdaptor
            ->expects($this->any())
            ->method('getServiceDefinition')
            ->will($this->returnValue($this->getServiceDefinitionDaoData()));

        Db_Manager::setAdaptor('Val', $objMockValAdaptor);

    }

    /**
     *
     * Function to provide data for getServiceDao statement mock
     */
    private function getServiceDaoData()
    {
        return array(
                'service_id'=>1234,
                'user_id'=>'12345',
                'isp' => 'plusnet',
                'username' => 'testuser',
                'password' => 'password',
                'cli_number' => 011111111111,
                'type' => 1,
                'status' => 'active',
                'startdate' => '2008-12-10',
                'enddate' => '',
                'next_invoice' => '2009-10-10',
                'invoice_period' => '2',
                'db_src' => 'test',
                'timestamp' => time(),
                'next_invoice_warned' => '',
                'invoice_day' => 3,
                'authorised_switch_payment' => '',
                'bolMailOptOut' => '',
                );
    }

    /**
     *
     * Function to provide data for getBusinessActor statement mock
     */
    private function getBusinessActorData()
    {
        return array(
                'intActorId' => 1,
                'strUsername' => 'testuser',
                'strRealm' => 'plusnet',
                'strUserType' => '',
                'strExternalUserId' => '12345'
                );
    }

    /**
     *
     * Function to provide data for getUserDao statement mock
     */
    private function getUserDaoData()
    {
        return array(
                'user_id' => '1234',
                'address_id' => '12',
                'customer_id' => '2',
                'telephone' => '078888888888',
                'strEveningPhone' => '019999999999',
                'fax' => '019999999999',
                'mobile' => '078888888888',
                'email' => '<EMAIL>',
                'position' => 'dev',
                'salutation' => 'Mr',
                'forenames' => 'testforename',
                'surname' => 'testsurname',
                'hint_question' => 'test',
                'hint_answer' => 'test',
                'db_src' => 'test',
                'timestamp' => time()
               );
    }

    /**
     *
     * Function to provide data for getAddressDao statement mock
     */
    private function getAddressDaoData()
    {
        return array(
                array(
                    'address_id' => 12,
                    'customer_id' => 2,
                    'house' => 'test',
                    'street' => 'test',
                    'town' => 'test',
                    'county' => 'test',
                    'postcode' => 's1 4by',
                    'country' => 'UK',
                    'status' => 'active',
                    'strTypeHandle' => 'TEST',
                    'strTypeName' => 'TEST'
                     )
                );
    }

    /**
     *
     * Function to provide data for getServiceDefinitionDao statement mock
     */
    private function getServiceDefinitionDaoData()
    {
        return array(
                'service_definition_id' => 1,
                'intProductVariantId' => 12,
                'name' => 'Test',
                'isp' => 'plusnet',
                'minimum_charge' => '',
                'date_created' => '',
                'requires' => '',
                'initial_charge' => '',
                'type' => '',
                'password_visible_to_support' => '',
                'end_date' => '',
                'signup_via_portal' => '',
                'bt_product_id' => '',
                'strProductVariant' => '' ,
                'strProductFamily' => '',
                'bolAdsl' => '',
                'strVariantHandle' => '',
                'strFamilyHandle' => '',
                );
    }
}
