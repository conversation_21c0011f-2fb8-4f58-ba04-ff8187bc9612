<?php

/**
 * Class AccountChange_EmailHandler_PhoneOnlyChangeTest
 * <AUTHOR> <<EMAIL>>
 */

class AccountChange_EmailHandler_PhoneOnlyChangeTest extends PHPUnit_Framework_TestCase
{

    public function invokeMethod(&$object, $methodName, array $parameters = array())
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }

    /**
     * @throws Db_TransactionException
     * @dataProvider getDataForTestPopulateEmailVars
     */
    public function testPopulateEmailVars(
        $data,
        $expectedEmailVars
    ) {
        $pricesArray = array("total" => $data['price']);
        $wlrInformationArray = array();
        $callPlanArray = array("strDisplayName" => $data['callPlan']);

        $mockPerformAccountChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(
                'getAppointment',
                'getServiceId',
            ),
            array(),
            '',
            false
        );


        $mockOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getProducts'),
            array(),
            '',
            false
        );

        $mockOrderProducts = $this->getMock(
            'AccountChange_AccountChangeOrderProducts',
            array('getPhoneComponentId'),
            array(),
            '',
            false
        );

        $mockOrderProducts->expects($this->once())
            ->method('getPhoneComponentId')
            ->will($this->returnValue($data['phoneComp']));

        $mockOrder->expects($this->any())
            ->method('getProducts')
            ->will($this->returnValue($mockOrderProducts));

        $mockPriceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(),
            '',
            false
        );

        $mockPriceHelper->expects($this->once())
            ->method('getNewPackagePrices')
            ->will($this->returnValue($pricesArray));

        $mockAccount = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation'),
            array(),
            '',
            false
        );

        $mockAccount->expects($this->once())
            ->method('getWlrInformation')
            ->will($this->returnValue($wlrInformationArray));

        $mockDataHelper = $this->getMock(
            'AccountChange_EmailHandler_DataHelper',
            array('getCallPlanDetailsByWlrServiceComponentId'),
            array(),
            '',
            false
        );


        $mockDataHelper->expects($this->exactly(is_null($data['phoneComp']) ? 0 : 1))
            ->method('getCallPlanDetailsByWlrServiceComponentId')
            ->with($data['phoneComp'])
            ->will($this->returnValue($callPlanArray));


        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForComponentType'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );


        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $workplaceEmailHandler = new AccountChange_EmailHandler_PhoneOnlyChange(
            $mockPerformAccountChangeApi,
            $mockOrder,
            $mockAccount,
            $mockPriceHelper,
            $mockDataHelper
        );

        $returnedArray = $this->invokeMethod($workplaceEmailHandler, "populateEmailVariables");

        $this->assertEquals($expectedEmailVars, $returnedArray);
    }

    public function getDataForTestPopulateEmailVars()
    {

        return array(
            array(
                "data" => array(
                    'strProduct' => 'Unlimited',
                    "strOldProductName" => 'Full Fibre 36',
                    'strContractLength' => 'Monthly',
                    'hardwareCompId' => 2343,
                    'phoneComp' => 23,
                    'callPlan' => 'Unlimited',
                    "price" => '22.34',
                    "startDate" => '',
                ),
                "expectedArray" => array(
                    "strProduct" => 'Unlimited',
                    "strOldProductName" => '',
                    "strContractLength" => 'Monthly',
                    "monthlyCost" => "22.34",
                    "uxtStartDate" => '',
                )
            ),
            array(
                "data" => array(
                    'strProduct' => 'Full Fibre 40',
                    "strOldProductName" => 'Full Fibre 36',
                    'strContractLength' => 'Monthly',
                    'hardwareCompId' => 2343,
                    'phoneComp' => 1,
                    'callPlan' => 'Unlimited',
                    "price" => '22.34',
                    "startDate" => '',
                ),
                "expectedArray" => array(
                    "strProduct" => 'Unlimited',
                    "strOldProductName" => '',
                    "strContractLength" => 'Monthly',
                    "monthlyCost" => "22.34",
                    "uxtStartDate" => '',
                )
            ),
        );
    }

    public function testGetEmailName()
    {
        $expectedEmailName = 'CUSTOMER_PHONE_CHANGE';

        $mockPerformAccountChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getNewProductName',
                'getAppointment',
                'getServiceId',
                'calculateScheduledChangeDate'),
            array(),
            '',
            false
        );

        $mockOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getProducts'),
            array(),
            '',
            false
        );

        $mockOrderProducts = $this->getMock(
            'AccountChange_AccountChangeOrderProducts',
            array('getPhoneComponentId'),
            array(),
            '',
            false
        );

        $mockOrder->expects($this->any())
            ->method('getProducts')
            ->will($this->returnValue($mockOrderProducts));

        $mockAccount = $this->getMock(
            'AccountChange_Account',
            array('getWlrInformation','getNextInvoiceDate'),
            array(),
            '',
            false
        );

        $mockPriceHelper = $this->getMock(
            'AccountChange_EmailHandler_PriceHelper',
            array('getNewPackagePrices'),
            array(),
            '',
            false
        );

        $mockDataHelper = $this->getMock(
            'AccountChange_EmailHandler_DataHelper',
            array('getCallPlanDetailsByWlrServiceComponentId'),
            array(),
            '',
            false
        );

        $mockDbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getServiceComponentDetailsForComponentType'),
            array('AccountChange', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        Db_Manager::setAdaptor('AccountChange', $mockDbAdaptor);

        $workplaceEmailHandler = new AccountChange_EmailHandler_PhoneOnlyChange(
            $mockPerformAccountChangeApi,
            $mockOrder,
            $mockAccount,
            $mockPriceHelper,
            $mockDataHelper
        );

        $returnedEmailName = $workplaceEmailHandler->getEmailName();

        $this->assertEquals($expectedEmailName, $returnedEmailName);
    }
}
