CREATE TABLE `userdata`.`components` (
  `component_id` int(8) unsigned zerofill NOT NULL auto_increment,
  `service_id` int(8) unsigned NOT NULL default '0',
  `component_type_id` int(8) NOT NULL default '0',
  `config_id` int(8) NOT NULL default '0',
  `description` varchar(255) NOT NULL default '',
  `status` enum('unconfigured','queued-activate','queued-reactivate','active','queued-deactivate','queued-deconfigure','deactive','queued-destroy','destroyed','invalid') NOT NULL default 'invalid',
  `db_src` varchar(4) NOT NULL default '',
  `creationdate` datetime NOT NULL default '0000-00-00 00:00:00',
  `timestamp` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  PRIMARY KEY  (`component_id`),
  KEY `service_id` (`service_id`,`component_type_id`),
  KEY `config_id` (`config_id`),
  KEY `component_type_id` (`component_type_id`,`status`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 PACK_KEYS=1