<?php
include_once 'phing/Task.php';
class CheckModulePath extends Task {

    private $_strDir;
    private $_strTarget;
        
    public function setDir($str) {
        $this->_strDir = $str;
    }

    public function setTarget($str) {
        $this->_strTarget = $str;
    }
    
	public function setReturnName($str) {
        $this->_strReturnName = $str;
    } 

    public function init() {
      // nothing to do here
    }

    /**
     * The main entry point method.
     */
    public function main() {
		if (!$this->_strReturnName) {
		    throw new BuildException("You must specify the returnName attribute", $this->getLocation());
		} 
		if (!$this->_strTarget) {
		    throw new BuildException("You must specify the target attribute", $this->getLocation());
		}
		
		if($this->project->getProperty('project.basedir') == realpath($this->_strTarget)) {
			$this->project->setProperty($this->_strReturnName, true);
		}
		
		 		 
    }
}