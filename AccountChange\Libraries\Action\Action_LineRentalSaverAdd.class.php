<?php
/**
 * Account Change Line rental saver add action
 *
 * @category  AccountChange_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-10-28
 */

/**
 * Account Change Line rental saver add action
 *
 * This class represents the logic required to add a pending line rental
 * saver contract
 *
 * @category  AccountChange_Action
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class AccountChange_Action_LineRentalSaverAdd extends AccountChange_Action_LineRentalSaver
{

    /**
     * Main execute of the action
     *
     * @return void
     */
    public function execute()
    {

        // Adding an explicit commit to f/w db top level transaction to make
        // the status of lrs component available in the subsequent process
        // involving legacy code
        Db_Manager::commit();

        $lrsComponentStatus = 'ACTIVE';

        // Create a prepaid contract instance for line rental saver.
        $this->createLrsInstance($lrsComponentStatus);

        $existingLrsEnd = $this->getEndDateOfActiveLrs();

        // If we've got an existing active line rental saver, then we need
        // to transfer it to the new product.
        if (false !== $existingLrsEnd) {
            $this->transferLrs($existingLrsEnd);
        }
    }


    /**
     * Transfer line rental saver to a new component
     *
     * @param I18n_Date $existingEndDate The end date of the existing contract
     *
     * @return void
     **/
    public function transferLrs($existingEndDate)
    {
        $registry = AccountChange_Registry::instance();
        $schedule = $registry->getEntry('bolSchedule');

        $serviceId = $this->getServiceId();

        $tariffId = null;
        if ($schedule) {
            $newComponentInstance = $this->getLineRentalSaver($serviceId, 'UNCONFIGURED');
            $oldInstance = $this->getLrsInstance();

            if (!$newComponentInstance instanceof PrepaidContract_Instance) {
                // No new product to transfer to
                return;
            }

            $tariffId = $this->getTariffIdFromInstanceId(
                $oldInstance->getProductComponentInstanceId()
            );
        } else {
            $newComponentInstance = $this->getLineRentalSaver($serviceId, 'ACTIVE');
        }

        // Fall back to annual tariff if we cannot find one on the old
        // instance or during instant account change..
        if (is_null($tariffId)) {
            $tariff = $newComponentInstance->getTariffByDuration('ANNUAL');
            $tariffId = $tariff['intTariffID'];
        }

        if ($schedule) {
            $newComponentInstance->transfer($tariffId, $existingEndDate);
        } else {
            // contract is already created - just make sure proper tariffId is set
            Log_AuditLog::write(
                sprintf(
                    '[AccountChange] %s - Updating LRS tariff (tariffId=%s) for productComponentInstanceId=%s'
                    . ' on instant product change',
                    __CLASS__ . '::' . __METHOD__,
                    $tariffId,
                    $newComponentInstance->getProductComponentInstanceId()
                )
            );

            try {
                $dbAdaptor = Db_Manager::getAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION, true);
                $dbAdaptor->updateProductComponentInstanceTariffId(
                    $tariffId,
                    $newComponentInstance->getProductComponentInstanceId()
                );
                $dbAdaptor->updateProductComponentContractTariffId(
                    $tariffId,
                    $newComponentInstance->getProductComponentInstanceId()
                );
                Db_Manager::commit();
            } catch (Exception $e) {
                Log_AuditLog::write(
                    sprintf(
                        '[AccountChange] %s - LRS tariff update for productComponentInstanceId=%s failed due to: %s',
                        __CLASS__ . '::' . __METHOD__,
                        $newComponentInstance->getProductComponentInstanceId(),
                        $e->getMessage()
                    )
                );

                Db_Manager::rollback();
            }
        }
    }
}
