<?php

/**
 * Helper functions that allow us to maintain which exchange a user is on
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 PlusNet
 * @link      http://jira.internal.plus.net/browse/PROF-126
 */

/**
 * Helper functions that allow us to maintain which exchange a user is on
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 PlusNet
 * @link      http://jira.internal.plus.net/browse/PROF-126
 */
class AccountChange_ExchangeHelper
{

    /**
     * Maintains userdata.tblCustomerExchange, adding a new record for the service if one
     * isn't set or updating it if there is.
     * Note: this could be achieved with one query, but a mysql bug would mean that breaks
     *       the auto-increment id on the PK.
     *
     * @param int $serviceId  Service id
     * @param int $marketId   Market id the customer is on
     * @param int $exchangeId Exchange id the customer is on
     *
     * @return boolean
     **/
    public function maintainCustomerExhangeData($serviceId, $marketId, $exchangeId)
    {

        if ($this->serviceHasExhangeDataSet($serviceId, $marketId, $exchangeId)) {
            return $this->updateCurrentMarketAndExchange($serviceId, $marketId, $exchangeId);
        } else {
            return $this->insertCurrentMarketAndExhange($serviceId, $marketId, $exchangeId);
        }
    }

    /**
     * Updates userdata.tblCustomerExchange with the given market and exchange ids
     *
     * @param int $serviceId  Service id
     * @param int $marketId   Market id the customer is on
     * @param int $exchangeId Exchange id the customer is on
     *
     * @return boolean
     **/
    public function updateCurrentMarketAndExchange($serviceId, $marketId, $exchangeId)
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        return $db->updateCurrentMarketAndExchange($exchangeId, $marketId, $serviceId);
    }

    /**
     * Inserts a new record into userdata.tblCustomerExchange
     *
     * @param int $serviceId  Service id
     * @param int $marketId   Market id the customer is on
     * @param int $exchangeId Exchange id the customer is on
     *
     * @return bool
     **/
    public function insertCurrentMarketAndExhange($serviceId, $marketId, $exchangeId)
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        return $db->insertCurrentMarketAndExhange($serviceId, $exchangeId, $marketId);
    }

    /**
     * Checks if the given service has a market and exchange id set
     *
     * @param int $serviceId Service id
     *
     * @return boolean
     **/
    public function serviceHasExhangeDataSet($serviceId)
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        return $db->serviceHasExhangeDataSet($serviceId);
    }

    /**
     * Extracts the exchange id and market id from the line check results in the registry
     *
     * @return array
     **/
    public function getMarketAndExchangeIdFromRegistry()
    {
        $lineCheckResult = AccountChange_Registry::instance()->getEntry('lineCheckResult');

        if ($lineCheckResult instanceof LineCheck_Result) {
            return $lineCheckResult->getExchangeAndMarketId();
        }

        return false;
    }
}
