<?php
/**
 * AutomatedEmail TemplateConfig test
 *
 * @category  AutomatedEamil
 * @package   AutomatedEmail
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @version   git
 * @link      Projects/AutomatedEmail/PDD
 * @since     File available since 2009-09-28
 */

use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamWrapper;
use org\bovigo\vfs\vfsStreamDirectory;

/**
 * AutomatedEmail TemplateConfig test
 *
 * Tests for configuring template handling for automated emails
 *
 * @category  AutomatedEamil
 * @package   AutomatedEmail
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @link      Projects/AutomatedEmail/PDD
 */
class testAutomatedEmailTemplateConfig extends PHPUnit_Framework_TestCase
{
	/**
	 * (non-PHPdoc)
	 * @see Framework/PHPUnit_Framework_TestCase#setUp()
	 */
	public function setUp()
	{
		vfsStreamWrapper::register();
		vfsStreamWrapper::setRoot(new vfsStreamDirectory('base'));
	}

	/**
	 * (non-PHPdoc)
	 * @see Framework/PHPUnit_Framework_TestCase#tearDown()
	 */
	public function tearDown()
	{
		AutomatedEmail_TemplateConfig::reset();
	}

	/**
	 * @test
	 */
	public function testGetCompilePathDefault()
	{
		$this->assertEquals(
				new String(realpath(Auto_ClassLoader::getModulesRoot() . '/../shared/AutomatedEmail/CompiledSmartyTemplates')),
				AutomatedEmail_TemplateConfig::getCompilePath()
		);
	}

	/**
	 * @test
	 */
	public function testSetBadCompilePathThrowsException()
	{
		$this->setExpectedException(
				'AutomatedEmail_Exception',
				'The provided path did not appear to be a valid directory',
				AutomatedEmail_Exception::INVALID_COMPILE_PATH
		);

		AutomatedEmail_TemplateConfig::setCompilePath(new String('/no/path/to/here'));
	}

	/**
	 * @test
	 */
	public function testSetAndGetCompilePath()
	{
		$setPath = new String('vfs://base/compile');

		vfsStreamWrapper::getRoot()->addChild(vfsStream::newDirectory('compile'));

		AutomatedEmail_TemplateConfig::setCompilePath($setPath);

		$this->assertEquals($setPath, AutomatedEmail_TemplateConfig::getCompilePath());
	}

	/**
	 * @covers AutomatedEmail_TemplateConfig::getContentBasePath
	 *
	 * @return null
	 */
	public function testGetContentBasePathDefault()
	{
		$this->assertEquals(
				new String(realpath(Auto_ClassLoader::getModulesRoot() . '/../content/templatedemails')),
				AutomatedEmail_TemplateConfig::getContentBasePath()
		);
	}

	/**
	 * @test
	 */
	public function testSetBadContentBasePathThrowsException()
	{
		$this->setExpectedException(
				'AutomatedEmail_Exception',
				'The provided path did not appear to be a valid directory',
				AutomatedEmail_Exception::INVALID_CONTENT_BASE_PATH
		);

		AutomatedEmail_TemplateConfig::setContentBasePath(new String('/no/path/to/here'));
	}

	/**
	 * @test
	 */
	public function testSetAndGetContentBasePath()
	{
		$setPath = new String('vfs://base/templates');

		vfsStreamWrapper::getRoot()->addChild(vfsStream::newDirectory('templates'));

		AutomatedEmail_TemplateConfig::setContentBasePath($setPath);

		$this->assertEquals($setPath, AutomatedEmail_TemplateConfig::getContentBasePath());
	}

	/**
	 * @test
	 */
	public function testResetWorks()
	{
		// This is a repeat of the SetAndGet tests above, but we need to verify they set correctly before we can be
		// certain that the reset is working correctly
		vfsStreamWrapper::getRoot()->addChild(vfsStream::newDirectory('templates'));
		vfsStreamWrapper::getRoot()->addChild(vfsStream::newDirectory('compile'));
		AutomatedEmail_TemplateConfig::setCompilePath(new String('vfs://base/compile'));
		AutomatedEmail_TemplateConfig::setContentBasePath(new String('vfs://base/templates'));
		$this->assertEquals(new String('vfs://base/compile'), AutomatedEmail_TemplateConfig::getCompilePath());
		$this->assertEquals(new String('vfs://base/templates'), AutomatedEmail_TemplateConfig::getContentBasePath());

		AutomatedEmail_TemplateConfig::reset();

		$this->assertEquals(
				new String(realpath(Auto_ClassLoader::getModulesRoot() . '/../shared/AutomatedEmail/CompiledSmartyTemplates')),
				AutomatedEmail_TemplateConfig::getCompilePath()
		);
		$this->assertEquals(
				new String(realpath(Auto_ClassLoader::getModulesRoot() . '/../content/templatedemails')),
				AutomatedEmail_TemplateConfig::getContentBasePath()
		);
	}
}