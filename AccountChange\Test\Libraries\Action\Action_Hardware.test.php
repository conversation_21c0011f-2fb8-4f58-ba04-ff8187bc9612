<?php
/**
 * Account Change Hardware Action Test
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2009 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */

use Plusnet\C2mApiClient\V5\Client;
use Plusnet\ContractsClient\Entity\Contract;
use Plusnet\Hardware\Action\Api\ManualOrderSubmit;

/**
 * Account Change Hardware Action Test
 *
 * Testing class for AccountChange_Action_Hardware
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 * @copyright  2009 Plusnet
 * @link       http://documentation.plus.net/index.php/Account_Change
 */
class AccountChange_Action_Hardware_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Service Id fixture
     *
     * @var int
     */
    private $serviceId = 123;

    /** @var Db_Adaptor $mockDbAdaptor */
    private $mockDbAdaptor;

    /** @var Client $mockC2mApiClient */
    private $mockC2mApiClient;

    public function setUp()
    {
        $this->mockDbAdaptor = $this->mockDbAdaptor();
        $this->mockC2mApiClient = $this->mockC2mApiClient();
    }

    public function tearDown()
    {
        unset($this->mockDbAdaptor, $this->mockC2mApiClient);
    }

    /**
     * Test we do not try and create a hardware component if it was selected
     *
     * @covers AccountChange_Action_Hardware::execute
     *
     * @return void
     */
    public function testExecuteDoesNotTryAndCreateComponentIfItWasntSelected()
    {
        $action = $this->getMock(
            'AccountChange_Action_Hardware',
            array('getHardwareComponentIdFromOption', 'createHardwareComponent'),
            array($this->serviceId)
        );

        $action->expects($this->once())
            ->method('getHardwareComponentIdFromOption')
            ->will($this->returnValue(null));

        $action->expects($this->never())
            ->method('createHardwareComponent');

        $action->execute();
    }

    /**
     * Test that we do try and create a hardware component if selected
     *
     * @covers AccountChange_Action_Hardware::execute
     * @covers AccountChange_Action_Hardware::setupHardwareComponent
     * @covers AccountChange_Action_Hardware::configureContract
     *
     * @return void
     */
    public function testExecuteDoesTryAndCreateComponentIfSelected()
    {
        $valueFamily = $this->getMock(
            '\ProductFamily_Value',
            array(),
            array(),
            '',
            false
        );

        $componentId = 123;

        $contractDefinition = new \Plusnet\ContractsClient\Entity\ContractDefinition();
        $contractedServiceDefinition = new \Plusnet\ContractsClient\Entity\ContractedServiceDefinition();
        $contractDefinition->addContractedServiceDefinition($contractedServiceDefinition);

        $contract = new \Plusnet\ContractsClient\Entity\Contract();
        $contractedService = new \Plusnet\ContractsClient\Entity\ContractedService();
        $contract->addContractedService($contractedService);

        $contractsClient = $this->getMock(
            '\Plusnet\ContractsClient\Client',
            array(
                'setServiceId',
                'getContracts',
                'withdrawContractedService',
                'activateContractedService'
            )
        );

        $contractsClient->expects($this->once())
            ->method('setServiceId')
            ->will($this->returnValue($contractsClient));

        $contractsClient->expects($this->once())
            ->method('getContracts')
            ->will($this->returnValue(array($contract)));

        $contractsClient->expects($this->once())
            ->method('withdrawContractedService');

        $contractsClient->expects($this->once())
            ->method('activateContractedService');

        BusTier_BusTier::setClient('contracts', $contractsClient);

        $action = $this->getMock(
            'AccountChange_Action_Hardware',
            array(
                'getHardwareComponentIdFromOption',
                'includeLegacyFiles',
                'configureHardware',
                'getAllowedContractDefinitions',
                'createHardwareComponent',
                'createHardwareContract',
                'getProductFamily'
            ),
            array($this->serviceId)
        );

        $action->expects($this->once())
            ->method('getHardwareComponentIdFromOption')
            ->will($this->returnValue(new Int($componentId)));

        $action->expects($this->once())
            ->method('includeLegacyFiles');

        $action->expects($this->once())
            ->method('configureHardware');

        $action->expects($this->once())
            ->method('getAllowedContractDefinitions')
            ->will($this->returnValue(array($contractDefinition)));

        $action->expects($this->once())
            ->method('createHardwareComponent')
            ->will($this->returnValue(1234));

        $action->expects($this->once())
            ->method('createHardwareContract')
            ->will($this->returnValue($contract));

        $action->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($valueFamily));

        $this->mockTechnologyChanging(null, null);

        $action->execute();
    }

    /**
     * Test that we return null if the hardware option is not set
     *
     * @covers AccountChange_Action_Hardware::getHardwareComponentIdFromOption
     *
     * @return void
     */
    public function testGetHardwareComponentIdFromOptionReturnsNullIfNoOptionPassed()
    {
        $action = new AccountChange_Action_Hardware($this->serviceId);

        $result = $action->getHardwareComponentIdFromOption();

        $this->assertNull($result);
    }

    /**
     * Test that we return correct value if the hardware option is set
     *
     * @covers AccountChange_Action_Hardware::getHardwareComponentIdFromOption
     *
     * @return void
     */
    public function testGetHardwareComponentIdFromOptionReturnsCorrectValueIfOptionIsPassedIn()
    {
        $componentId = 123;

        $bundle = $this->getMock(
            'HardwareClient_Bundle',
            array('getServiceComponentId'),
            array(new String('name'), new String('description'), new Int(1), new Int(1), new String('handle'))
        );

        $bundle->expects($this->once())
            ->method('getServiceComponentId');

        $client = $this->getMock(
            'HardwareClient_Client',
            array('getBundleFromServiceDefinitionAndHandle'),
            array()
        );

        $client->expects($this->once())
            ->method('getBundleFromServiceDefinitionAndHandle')
            ->will($this->returnValue($bundle));

        BusTier_BusTier::setClient('hardware', $client);

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intNewServiceDefinitionId', 123);
        $registry->setEntry('hardwareOption', 'hardware');

        $action = new AccountChange_Action_Hardware($this->serviceId);

        $action->execute();
    }

    public function testHardwareOrderGetsPlacedWhenItIsLikeForLikeRecontract()
    {
        $testServiceDefinitionId = 2048;
        $testHardwareOption = 'PN_HUB_TWO_BUSINESS_DSL';
        $testIsRecontract = true;
        $testServiceComponentId = 1952;
        $testComponentId = ********;
        $testAllowedContractDefinitions = [0,1,2];
        $testProductFamily = 'productFamily';
        $hardwareOrderReasonId = ManualOrderSubmit::MANUAL_ORDER_REASON_RECONTRACT_POSTAGE;

        $mockBundle = $this->getMockBuilder(HardwareClient_Bundle::class)
            ->disableOriginalConstructor()
            ->setMethods(['getServiceComponentId'])
            ->getMock();
        $mockHardwareClient = $this->getMockBuilder(HardwareClient_Client::class)
            ->disableOriginalConstructor()
            ->setMethods(['getBundleFromServiceDefinitionAndHandle'])
            ->getMock();
        $mockContract = $this->getMockBuilder(Contract::class)
            ->disableOriginalConstructor()
            ->getMock();
        $mockPlaceOrderAction = $this->getMockBuilder(ManualOrderSubmit::class)
            ->disableOriginalConstructor()
            ->setMethods(['execute'])
            ->getMock();

        BusTier_BusTier::setClient('hardware', $mockHardwareClient);

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intNewServiceDefinitionId', $testServiceDefinitionId)
            ->setEntry('intOldServiceDefinitionId', $testServiceDefinitionId)
            ->setEntry('hardwareOption', $testHardwareOption)
            ->setEntry('isRecontract', $testIsRecontract);

        $action = $this->getMockBuilder(AccountChange_Action_Hardware::class)
            ->setConstructorArgs([$this->serviceId])
            ->setMethods([
                'includeLegacyFiles',
                'getAllowedContractDefinitions',
                'createHardwareComponent',
                'configureHardware',
                'getProductFamily',
                'createHardwareContract',
                'configureContract',
                'buildHardwareOrderAction',
            ])
            ->getMock();

        $mockHardwareClient->expects($this->once())
            ->method('getBundleFromServiceDefinitionAndHandle')
            ->will($this->returnValue($mockBundle));
        $mockBundle->expects($this->once())
            ->method('getServiceComponentId')
            ->willReturn(new Int($testServiceComponentId));
        $action->expects($this->once())
            ->method('includeLegacyFiles');
        $action->expects($this->once())
            ->method('getAllowedContractDefinitions')
            ->willReturn($testAllowedContractDefinitions);
        $action->expects($this->once())
            ->method('createHardwareComponent')
            ->with($testServiceComponentId)
            ->willReturn($testComponentId);
        $action->expects($this->once())
            ->method('configureHardware')
            ->with($testServiceComponentId, $testComponentId);
        $action->expects($this->once())
            ->method('getProductFamily')
            ->with($testServiceDefinitionId)
            ->willReturn($testProductFamily);
        $action->expects($this->once())
            ->method('createHardwareContract')
            ->with($testServiceComponentId, $testAllowedContractDefinitions)
            ->willReturn($mockContract);
        $action->expects($this->once())
            ->method('configureContract')
            ->with($mockContract, $testComponentId);
        $action->expects($this->once())
            ->method('buildHardwareOrderAction')
            ->with($testComponentId, $hardwareOrderReasonId)
            ->willReturn($mockPlaceOrderAction);
        $mockPlaceOrderAction->expects($this->once())
            ->method('execute');

        $action->execute();
    }

    public function testHardwareOrderGetsPlacedWhenThereIsNoTechnologyChange()
    {
        $testOldServiceDefinitionId = 2048;
        $testNewServiceDefinitionId = 4096;
        $testHardwareOption = 'PN_HUB_TWO_BUSINESS_DSL';
        $testIsRecontract = true;
        $testServiceComponentId = 1952;
        $testComponentId = ********;
        $testAllowedContractDefinitions = [0,1,2];
        $testProductFamily = 'productFamily';
        $hardwareOrderReasonId = ManualOrderSubmit::MANUAL_ORDER_REASON_RECONTRACT_POSTAGE;

        $mockBundle = $this->getMockBuilder(HardwareClient_Bundle::class)
            ->disableOriginalConstructor()
            ->setMethods(['getServiceComponentId'])
            ->getMock();
        $mockHardwareClient = $this->getMockBuilder(HardwareClient_Client::class)
            ->disableOriginalConstructor()
            ->setMethods(['getBundleFromServiceDefinitionAndHandle'])
            ->getMock();
        $mockContract = $this->getMockBuilder(Contract::class)
            ->disableOriginalConstructor()
            ->getMock();
        $mockPlaceOrderAction = $this->getMockBuilder(ManualOrderSubmit::class)
            ->disableOriginalConstructor()
            ->setMethods(['execute'])
            ->getMock();

        BusTier_BusTier::setClient('hardware', $mockHardwareClient);

        $registry = AccountChange_Registry::instance();
        $registry->setEntry('intNewServiceDefinitionId', $testNewServiceDefinitionId)
            ->setEntry('intOldServiceDefinitionId', $testOldServiceDefinitionId)
            ->setEntry('hardwareOption', $testHardwareOption)
            ->setEntry('isRecontract', $testIsRecontract);

        $action = $this->getMockBuilder(AccountChange_Action_Hardware::class)
            ->setConstructorArgs([$this->serviceId])
            ->setMethods([
                'includeLegacyFiles',
                'getAllowedContractDefinitions',
                'createHardwareComponent',
                'configureHardware',
                'getProductFamily',
                'createHardwareContract',
                'configureContract',
                'buildHardwareOrderAction',
            ])
            ->getMock();

        $mockHardwareClient->expects($this->once())
            ->method('getBundleFromServiceDefinitionAndHandle')
            ->will($this->returnValue($mockBundle));
        $mockBundle->expects($this->once())
            ->method('getServiceComponentId')
            ->willReturn(new Int($testServiceComponentId));
        $action->expects($this->once())
            ->method('includeLegacyFiles');
        $action->expects($this->once())
            ->method('getAllowedContractDefinitions')
            ->willReturn($testAllowedContractDefinitions);
        $action->expects($this->once())
            ->method('createHardwareComponent')
            ->with($testServiceComponentId)
            ->willReturn($testComponentId);
        $action->expects($this->once())
            ->method('configureHardware')
            ->with($testServiceComponentId, $testComponentId);
        $action->expects($this->once())
            ->method('getProductFamily')
            ->with($testNewServiceDefinitionId)
            ->willReturn($testProductFamily);
        $action->expects($this->once())
            ->method('createHardwareContract')
            ->with($testServiceComponentId, $testAllowedContractDefinitions)
            ->willReturn($mockContract);
        $action->expects($this->once())
            ->method('configureContract')
            ->with($mockContract, $testComponentId);
        $action->expects($this->once())
            ->method('buildHardwareOrderAction')
            ->with($testComponentId, $hardwareOrderReasonId)
            ->willReturn($mockPlaceOrderAction);
        $mockPlaceOrderAction->expects($this->once())
            ->method('execute');

        $this->mockTechnologyChanging($testOldServiceDefinitionId, $testNewServiceDefinitionId, false);

        $action->execute();
    }

    private function mockTechnologyChanging($oldsid, $newsid, $isTechnologyChanging = true)
    {
        $testOldServiceComponentId = 1;
        $testNewServiceComponentId = 2;

        $oldClassOfService = $newClassOfService = 'FTTP_40';

        if ($isTechnologyChanging) {
            $newClassOfService = 'FTTP_80';
        }

        $this->mockDbAdaptor->expects($this->exactly(2))
            ->method('getProductDetailsByServiceDefinitionIdAndMarket')
            ->withConsecutive(
                [$oldsid, AccountChange_Action_Hardware::MARKET_ID],
                [$newsid, AccountChange_Action_Hardware::MARKET_ID]
            )->willReturnOnConsecutiveCalls(
                ['intServiceComponentId' => $testOldServiceComponentId],
                ['intServiceComponentId' => $testNewServiceComponentId]
            );

        $this->mockC2mApiClient->expects($this->exactly(2))
            ->method('getProductOfferings')
            ->withConsecutive(
                [$this->isType('string'), null, null, $testOldServiceComponentId],
                [$this->isType('string'), null, null, $testNewServiceComponentId]
            )->willReturnOnConsecutiveCalls(
                [$this->mockProductOffering($oldClassOfService)],
                [$this->mockProductOffering($newClassOfService)]
            );
    }

    private function mockDbAdaptor()
    {
        $mockAdaptor = $this->getMockBuilder(Db_Adaptor::class)
            ->disableOriginalConstructor()
            ->setMethods(['getProductDetailsByServiceDefinitionIdAndMarket'])
            ->getMock();

        Db_Manager::setAdaptor('AccountChange', $mockAdaptor);

        return $mockAdaptor;
    }

    private function mockC2mApiClient()
    {
        $mockClient = $this->getMockBuilder(Client::class)
            ->disableOriginalConstructor()
            ->setMethods(['getProductOfferings'])
            ->getMock();

        BusTier_BusTier::setClient('c2mapi.v5', $mockClient);

        return $mockClient;
    }

    private function mockProductOffering($classOfService)
    {
        $mockOffering = $this->getMockBuilder(Plusnet\C2mApiClient\Entity\ProductOffering::class)
            ->disableOriginalConstructor()
            ->setMethods(['getClassOfService'])
            ->getMock();

        $mockOffering->expects($this->once())
            ->method('getClassOfService')
            ->willReturn($classOfService);

        return $mockOffering;
    }

    /**
     *
     * @covers AccountChange_Action_Hardware::execute
     * @covers AccountChange_Action_Hardware::setupHardwareComponent
     * @covers AccountChange_Action_Hardware::configureContract
     *
     * @return void
     */
    public function testHardwareIsntOrderedWhenOrderIsRequired()
    {
        $valueFamily = $this->getMock(
            '\ProductFamily_Value',
            array(),
            array(),
            '',
            false
        );

        $componentId = 123;

        $contractDefinition = new \Plusnet\ContractsClient\Entity\ContractDefinition();
        $contractedServiceDefinition = new \Plusnet\ContractsClient\Entity\ContractedServiceDefinition();
        $contractDefinition->addContractedServiceDefinition($contractedServiceDefinition);

        $contract = new \Plusnet\ContractsClient\Entity\Contract();
        $contractedService = new \Plusnet\ContractsClient\Entity\ContractedService();
        $contract->addContractedService($contractedService);

        $contractsClient = $this->getMock(
            '\Plusnet\ContractsClient\Client',
            array(
                'setServiceId',
                'getContracts',
                'withdrawContractedService',
                'activateContractedService'
            )
        );

        $contractsClient->expects($this->once())
            ->method('setServiceId')
            ->will($this->returnValue($contractsClient));

        $contractsClient->expects($this->once())
            ->method('getContracts')
            ->will($this->returnValue(array($contract)));

        $contractsClient->expects($this->once())
            ->method('withdrawContractedService');

        $contractsClient->expects($this->once())
            ->method('activateContractedService');

        BusTier_BusTier::setClient('contracts', $contractsClient);

        $action = $this->getMock(
            'AccountChange_Action_Hardware',
            array(
                'getHardwareComponentIdFromOption',
                'includeLegacyFiles',
                'configureHardware',
                'getAllowedContractDefinitions',
                'createHardwareComponent',
                'createHardwareContract',
                'getProductFamily',
                'submitManualHardwareOrder'
            ),
            array($this->serviceId)
        );

        $action->expects($this->never())
            ->method('submitManualHardwareOrder');

        $action->expects($this->once())
            ->method('getHardwareComponentIdFromOption')
            ->will($this->returnValue(new Int($componentId)));

        $action->expects($this->once())
            ->method('includeLegacyFiles');

        $action->expects($this->once())
            ->method('configureHardware');

        $action->expects($this->once())
            ->method('getAllowedContractDefinitions')
            ->will($this->returnValue(array($contractDefinition)));

        $action->expects($this->once())
            ->method('createHardwareComponent')
            ->will($this->returnValue(1234));

        $action->expects($this->once())
            ->method('createHardwareContract')
            ->will($this->returnValue($contract));

        $action->expects($this->once())
            ->method('getProductFamily')
            ->will($this->returnValue($valueFamily));

        $this->mockDbAdaptor->expects($this->exactly(1))
            ->method('getProductDetailsByServiceDefinitionIdAndMarket')
            ->willReturn(null);

        $registry = new AccountChange_Registry();
        $registry->setEntry('bolOrderNeeded', true);

        AccountChange_Registry::setInstance($registry);

        $action->execute();
    }
}
