<?php
/**
 * AccountChange_EmailHandler_Confirmation
 *
 * @package AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 */

class AccountChange_EmailHandler_Confirmation extends AccountChange_EmailHandler
{
    private $broadbandProduct;

    private $phoneProduct;

    private $contractLength;

    private $hardware;

    private $houseMove;

    private $newMonthlyCost;

    private $installationTypeFTTP;

    private $targetActivationDate;

    private $nextBillingDate;

    private $advertisedDownloadSpeed;

    private $advertisedUploadSpeed;

    private $estimatedDownloadSpeedAvailable;

    private $minimumGuaranteedSpeedAvailable;

    private $estimatedDownloadSpeedRangeLower;

    private $estimatedDownloadSpeedRangeHigher;

    private $estimatedUploadSpeedRangeLower;

    private $estimatedUploadSpeedRangeHigher;

    private $minUpSpeed;

    private $maxUpSpeed;

    private $maxDownloadSpeed;

    private $minimumGuaranteedSpeed;

    private $accessTechnologyChanging;

    private $isSogea;

    /**
     * AccountChange_EmailHandler_Confirmation constructor.
     * @param AccountChange_PerformChangeApi         $performChangeApi perform account change api
     * @param AccountChange_AccountChangeOrder       $order            order
     * @param AccountChange_EmailHandler_PriceHelper $priceHelper      price helper
     * @param AccountChange_Account                  $account          account
     * @param AccountChange_EmailHandler_SpeedHelper $speedHelper      speed helper
     * @param AccountChange_EmailHandler_DataHelper  $dataHelper       data helper
     */
    public function __construct(
        \AccountChange_PerformChangeApi   $performChangeApi,
        \AccountChange_AccountChangeOrder $order,
        AccountChange_EmailHandler_PriceHelper $priceHelper,
        AccountChange_Account $account,
        AccountChange_EmailHandler_SpeedHelper $speedHelper,
        AccountChange_EmailHandler_DataHelper $dataHelper
    ) {
        $this->setBroadbandProduct($performChangeApi->getNewProductName());
        $this->setNewMonthlyCost($priceHelper->getNewPackagePrices()['total']);
        $currentWlrData = $account->getWlrInformation();
        $this->setPhoneProduct($order, $currentWlrData, $dataHelper);
        $speedData = $speedHelper->getSpeedData();
        $this->setMinimumGuaranteedSpeed($speedData['guaranteedSpeedValue']);
        $this->setMaxDownloadSpeed($speedData['maxDownSpeed']);
        $this->setMinUpSpeed($speedData['minUpSpeed']);
        $this->setMaxUpSpeed($speedData['maxUpSpeed']);
        $this->setEstimatedUploadSpeedRangeHigher($speedData['maximumEstimatedUploadSpeedMbs']);
        $this->setEstimatedUploadSpeedRangeLower($speedData['minimumEstimatedUploadSpeedMbs']);
        $this->setEstimatedDownloadSpeedRangeLower($speedData['minimumEstimatedDownloadSpeedMbs']);
        $this->setEstimatedDownloadSpeedRangeHigher($speedData['maximumEstimatedDownloadSpeedMbs']);
        $this->setEstimatedDownloadSpeedAvailable(
            isset($speedData['minimumEstimatedDownloadSpeedMbs']) &&
            isset($speedData['maximumEstimatedDownloadSpeedMbs'])
        );
        $this->setMinimumGuaranteedSpeedAvailable(isset($speedData['guaranteedSpeedValue']));
        $this->setAdvertisedDownloadSpeed($speedData['advertisedDownloadSpeedMbs']);
        $this->setAdvertisedUploadSpeed($speedData['advertisedUploadSpeedMbs']);
        $this->setHouseMove($performChangeApi->isProductChangeForHouseMove());
        $technologyType = $performChangeApi->getTechnologyType();
        $this->setInstallationType(($technologyType === 'FTTP') ?
            $performChangeApi->getLineCheckResults()->getFttpInstallProcess() : null);
        $this->setIsSogea($performChangeApi->getNewAccessTechnology());
        $this->setAccessTechnologyChanging($performChangeApi);
        if (!$order->isRetainCurrentContracts()) {
            $this->setContractLength($order->getContract()->getLength());
        }
        $this->setHardware($order->getProducts()->getHardwareComponentId());
        $this->setNextBillingDate($account->getNextInvoiceDate('Y-m-d'));
        $this->targetActivationDate = $performChangeApi->getScheduledChangeDate();
    }

    /**
     * @param int $hardwareServiceCompId hardwareServiceCompId
     * @throws Db_TransactionException
     * @return void
     */
    private function setHardware($hardwareServiceCompId)
    {
        $db = Db_Manager::getAdaptor('AccountChange');
        $hardwareArray = $db->getServiceComponentDetailsForComponentType($hardwareServiceCompId);
        $this->hardware = $hardwareArray['strName'];
    }

    /**
     * @return string
     */
    public function getMinimumGuaranteedSpeed()
    {
        return $this->minimumGuaranteedSpeed;
    }

    /**
     * @param string $minimumGuaranteedSpeed minimumGuaranteedSpeed
     * @return void
     */
    public function setMinimumGuaranteedSpeed($minimumGuaranteedSpeed)
    {
        $this->minimumGuaranteedSpeed = $minimumGuaranteedSpeed;
    }

    /**
     * @return string
     */
    public function getMinUpSpeed()
    {
        return $this->minUpSpeed;
    }

    /**
     * @param string $minUpSpeed minUpSpeed
     * @return void
     */
    public function setMinUpSpeed($minUpSpeed)
    {
        $this->minUpSpeed = $minUpSpeed;
    }

    /**
     * @param string $newAccessTechnology
     * @return void
     */
    public function setIsSogea($newAccessTechnology)
    {
        $this->isSogea = ($newAccessTechnology == 'SOGEA');
    }

    /**
     * @return boolean
     */
    public function getIsSogea()
    {
        return $this->isSogea;
    }

    /**
     * @return string
     */
    public function getMaxUpSpeed()
    {
        return $this->maxUpSpeed;
    }

    /**
     * @param string $maxUpSpeed maxUpSpeed
     * @return void
     */
    public function setMaxUpSpeed($maxUpSpeed)
    {
        $this->maxUpSpeed = $maxUpSpeed;
    }

    /**
     * @return string
     */
    public function getMaxDownloadSpeed()
    {
        return $this->maxDownloadSpeed;
    }

    /**
     * @param string $maxDownloadSpeed maxDownloadSpeed
     * @return void
     */
    public function setMaxDownloadSpeed($maxDownloadSpeed)
    {
        $this->maxDownloadSpeed = $maxDownloadSpeed;
    }

    /**
     * @return string
     */
    public function getEstimatedUploadSpeedRangeLower()
    {
        return $this->estimatedUploadSpeedRangeLower;
    }

    /**
     * @param mixed $estimatedUploadSpeedRangeLower estimatedUploadSpeedRangeLower
     * @return void
     */
    public function setEstimatedUploadSpeedRangeLower($estimatedUploadSpeedRangeLower)
    {
        $this->estimatedUploadSpeedRangeLower = $estimatedUploadSpeedRangeLower;
    }

    /**
     * @return string
     */
    public function getEstimatedUploadSpeedRangeHigher()
    {
        return $this->estimatedUploadSpeedRangeHigher;
    }

    /**
     * @param string $estimatedUploadSpeedRangeHigher est upload speed
     * @return void
     */
    public function setEstimatedUploadSpeedRangeHigher($estimatedUploadSpeedRangeHigher)
    {
        $this->estimatedUploadSpeedRangeHigher = $estimatedUploadSpeedRangeHigher;
    }

    /**
     * @param boolean $houseMoveFlag houseMoveFlag
     *
     * @return void
     */
    private function setHouseMove($houseMoveFlag)
    {
        $this->houseMove = $houseMoveFlag;
    }

    /**
     * @param int $contractLength contract length
     * @return void
     */
    private function setContractLength($contractLength)
    {
        $this->contractLength = $contractLength;
    }

    /**
     * @param string $nextBillingDate next billing date
     * @return void
     */
    private function setNextBillingDate($nextBillingDate)
    {
        $this->nextBillingDate = $nextBillingDate;
    }

    /**
     * @param string $installType install type
     * @return void
     */
    private function setInstallationType($installType)
    {
        $this->installationTypeFTTP = $installType;
    }

    /**
     * @param string $newMonthlyCost new monthly cost
     * @return void
     */
    private function setNewMonthlyCost($newMonthlyCost)
    {
        $this->newMonthlyCost = number_format($newMonthlyCost, 2);
    }

    /**
     * @param string $broadbandProduct broadband product
     * @return void
     */
    private function setBroadbandProduct($broadbandProduct)
    {
        $this->broadbandProduct = $broadbandProduct;
    }

    /**
     * @param AccountChange_AccountChangeOrder      $order          order
     * @param array                                 $currentWlrData array of wlr data
     * @param AccountChange_EmailHandler_DataHelper $dataHelper     data helper
     * @return void
     */
    private function setPhoneProduct($order, $currentWlrData, $dataHelper)
    {
        $newPhoneComponentId = $order->getProducts()->getPhoneComponentId();
        if (isset($this->currentWlrData['intOldWlrId'])) {
            $oldPhoneComponentId = $currentWlrData['intOldWlrId'];
        } else {
            $oldPhoneComponentId = null;
        }
        $phoneCompId = !empty($newPhoneComponentId) ? $newPhoneComponentId : $oldPhoneComponentId;
        $callPlanName = null;
        if (!empty($phoneCompId)) {
            $callPlan = $dataHelper->getCallPlanDetailsByWlrServiceComponentId($phoneCompId);
            $callPlanName = $callPlan['strDisplayName'];
        }
        $this->phoneProduct = $callPlanName;
    }

    /**
     * @param string $advertisedDownloadSpeed advertisedDownloadSpeed
     * @return void
     */
    public function setAdvertisedDownloadSpeed($advertisedDownloadSpeed)
    {
        $this->advertisedDownloadSpeed = $advertisedDownloadSpeed;
    }

    /**
     * @param string $advertisedUploadSpeed advertisedUploadSpeed
     * @return void
     */
    public function setAdvertisedUploadSpeed($advertisedUploadSpeed)
    {
        $this->advertisedUploadSpeed = $advertisedUploadSpeed;
    }

    /**
     * @param string $estimatedDownloadSpeedAvailable estimatedDownloadSpeedAvailable
     * @return void
     */
    public function setEstimatedDownloadSpeedAvailable($estimatedDownloadSpeedAvailable)
    {
        $this->estimatedDownloadSpeedAvailable = $estimatedDownloadSpeedAvailable;
    }

    /**
     * @param string $minimumGuaranteedSpeedAvailable minimumGuaranteedSpeedAvailable
     * @return void
     */
    public function setMinimumGuaranteedSpeedAvailable($minimumGuaranteedSpeedAvailable)
    {
        $this->minimumGuaranteedSpeedAvailable = $minimumGuaranteedSpeedAvailable;
    }

    /**
     * @param string $estimatedDownloadSpeedRangeLower estimatedDownloadSpeedRangeLower
     * @return void
     */
    public function setEstimatedDownloadSpeedRangeLower($estimatedDownloadSpeedRangeLower)
    {
        $this->estimatedDownloadSpeedRangeLower = $estimatedDownloadSpeedRangeLower;
    }

    /**
     * @param string $estimatedDownloadSpeedRangeHigher estimatedDownloadSpeedRangeHigher
     * @return void
     */
    public function setEstimatedDownloadSpeedRangeHigher($estimatedDownloadSpeedRangeHigher)
    {
        $this->estimatedDownloadSpeedRangeHigher = $estimatedDownloadSpeedRangeHigher;
    }

    /**
     * Tranfers local variables into an array ready to be used
     *
     * @return array email variables
     */
    protected function populateEmailVariables()
    {
        return array(
            "broadbandProduct" => $this->getBroadbandProduct(),
            "contractLength" => $this->formatForMsn($this->getContractLength()),
            "phoneProduct" => $this->formatForMsn($this->getPhoneProduct()),
            "hardware" => $this->formatForMsn($this->getHardware()),
            "houseMove" => $this->formatForMsn($this->getHouseMove()),
            "newMonthlyCost" => $this->getNewMonthlyCost(),
            "installationTypeFTTP" => $this->formatForMsn($this->getInstallationTypeFTTP()),
            "targetActivationDate" => $this->formatForMsn($this->getTargetActivationDate()),
            "nextBillingDate" => $this->getNextBillingDate(),
            "advertisedDownloadSpeed" => $this->getAdvertisedDownloadSpeed(),
            "advertisedUploadSpeed" => $this->getAdvertisedUploadSpeed(),
            "estimatedDownloadSpeedAvailable" => $this->formatForMsn($this->getEstimatedDownloadSpeedAvailable()),
            "minimumGuaranteedSpeedAvailable" => $this->formatForMsn($this->getMinimumGuaranteedSpeedAvailable()),
            "estimatedDownloadSpeedRangeLower" => $this->getEstimatedDownloadSpeedRangeLower(),
            "estimatedDownloadSpeedRangeHigher" => $this->getEstimatedDownloadSpeedRangeHigher(),
            "minimumUploadSpeed" => $this->getMinUpSpeed(),
            "maximumUploadSpeed" => $this->getMaxUpSpeed(),
            "maximumDownloadSpeed" => $this->getMaxDownloadSpeed(),
            "estimatedUploadSpeedRangeHigher" => $this->getEstimatedUploadSpeedRangeHigher(),
            "estimatedUploadSpeedRangeLower" => $this->getEstimatedUploadSpeedRangeLower(),
            "minimumGuaranteedSpeed" => $this->getMinimumGuaranteedSpeed(),
            "accessTechnologyChanging" => $this->formatForMsn($this->accessTechnologyChanging),
            "isSogea" => $this->formatForMsn($this->getIsSogea()),
        );
    }

    /**
     * @param mixed $var variable
     * @return string|int
     */
    private function formatForMsn($var)
    {
        if (is_null($var)) {
            return 0;
        }
        if (is_bool($var)) {
            return $var ? 1 : 0;
        }
        return $var;
    }

    /**
     * @return string
     */
    public function getEmailName()
    {
        return 'CUSTOMER_PRODUCT_CHANGE';
    }

    /**
     * @return string
     */
    public function getBroadbandProduct()
    {
        return $this->broadbandProduct;
    }

    /**
     * @return int
     */
    public function getContractLength()
    {
        return $this->contractLength;
    }

    /**
     * @return string
     */
    public function getPhoneProduct()
    {
        return $this->phoneProduct;
    }

    /**
     * @return string
     */
    public function getHardware()
    {
        return $this->hardware;
    }

    /**
     * @return boolean
     */
    public function getHouseMove()
    {
        return $this->houseMove;
    }

    /**
     * @return int
     */
    public function getNewMonthlyCost()
    {
        return $this->newMonthlyCost;
    }

    /**
     * @return string
     */
    public function getInstallationTypeFTTP()
    {
        return $this->installationTypeFTTP;
    }

    /**
     * @return string
     */
    public function getTargetActivationDate()
    {
        return $this->targetActivationDate;
    }

    /**
     * @return string
     */
    public function getNextBillingDate()
    {
        return $this->nextBillingDate;
    }

    /**
     * @return string
     */
    public function getAdvertisedDownloadSpeed()
    {
        return $this->advertisedDownloadSpeed;
    }

    /**
     * @return string
     */
    public function getAdvertisedUploadSpeed()
    {
        return $this->advertisedUploadSpeed;
    }

    /**
     * @return string
     */
    public function getEstimatedDownloadSpeedAvailable()
    {
        return $this->estimatedDownloadSpeedAvailable;
    }

    /**
     * @return string
     */
    public function getMinimumGuaranteedSpeedAvailable()
    {
        return $this->minimumGuaranteedSpeedAvailable;
    }

    /**
     * @return string
     */
    public function getEstimatedDownloadSpeedRangeLower()
    {
        return $this->estimatedDownloadSpeedRangeLower;
    }

    /**
     * @return string
     */
    public function getEstimatedDownloadSpeedRangeHigher()
    {
        return $this->estimatedDownloadSpeedRangeHigher;
    }

    /**
     * @param int $serviceId serviceId
     * @return void
     */
    public function sendEmail($serviceId)
    {
        $this->sendEmailMSN($serviceId, $this->getEmailName(), $this->populateEmailVariables(), 'CONFIRMATION');
    }

    /**
     * @param AccountChange_PerformChangeApi $performChangeApi perform change api
     * @return void
     */
    private function setAccessTechnologyChanging(AccountChange_PerformChangeApi $performChangeApi)
    {
        $accessTechnologyChanging = false;
        $oldAccessTechnology = $performChangeApi->getCurrentAccessTechnology();
        $newAccessTechnology = $performChangeApi->getNewAccessTechnology();
        if (!empty($newAccessTechnology)) {
            $accessTechnologyChanging =
                $oldAccessTechnology !== $newAccessTechnology;
        }

        $this->accessTechnologyChanging = $accessTechnologyChanging;
    }
}
