<?php
require_once(__DIR__.'/PromotionRule.php');
require_once(__DIR__.'/PromotionRuleBase.php');

use \Plusnet\C2mApiClient\Entity\Promotion;

/**
 * Class BumpPromotionToTop
 */
class BumpPromotionToTop extends PromotionRuleBase implements PromotionRule
{

    /**
     * @var string
     */
    private $PromotionToBump;

    /**
     * BumpPromotionToTop constructor.
     *
     * @param string $promoCodeToBump
     */
    public function __construct(Promotion $Promotion)
    {
        $this->PromotionToBump = $Promotion;
    }

    /**
     * @param \AccountChange_C2mPromotionsHelper $promotionHelper
     */
    public function handle(array $promotions)
    {
        array_unshift($promotions, $this->PromotionToBump);
        return $promotions;
    }

}
