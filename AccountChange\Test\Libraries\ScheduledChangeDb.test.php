<?php
/**
 * Scheduled Change Test File
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @link       link
 * @since      File available since 2011-09-30
 */
/**
 * Test file for AccountChange_ScheduledChange
 *
 * @package    AccountChange
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 Plusnet
 * @link       link
 */
class AccountChange_ScheduledChangeDbTest extends Plusnet_Database_TestCase
{
    protected $arrServers = array(
        'Coredb_master' => array(),
        'Coredb_slave' => array()
    );

    protected $arrDataStructureDirectories = array('/local/codebase2005/modules/AccountChange/Test/datastructure/');

    protected $dbName = 'userdata';

    protected $dataSet = '/local/codebase2005/modules/AccountChange/Test/dataset/ScheduledChangeData.xml';

    /**
     * PHPUnit tearDown functionality
     *
     * (non-PHPdoc)
     *
     * @see Plusnet_Database_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        parent::tearDown();

        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('AccountChange', Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Test that the internal classes are generated
     *
     * @covers AccountChange_ScheduledChange::__construct
     * @covers AccountChange_ScheduledChange::getScheduledChanges
     * @covers AccountChange_ScheduledChange_Adsl::__construct
     *
     * @return void
     */
    public function testConstructorInstantiatesInternalChangeClasses()
    {
        $serviceId = 919;

        $change = new AccountChange_ScheduledChange($serviceId);
        $actual = $change->getScheduledChanges();

        $expected = array(
            new AccountChange_ScheduledChange_Adsl($serviceId),
            new AccountChange_ScheduledChange_Wlr($serviceId),
        );

        $this->assertEquals($expected, $actual);
    }

    /**
     * Test getData() contains all the data for each change from the database
     *
     * @covers AccountChange_ScheduledChange_Adsl::getData
     * @covers AccountChange_ScheduledChange_Wlr::getData
     *
     * @return void
     */
    public function testGetDataForEachScheduledChange()
    {
        $serviceId = 919;

        $change = new AccountChange_ScheduledChange($serviceId);
        $actual = $change->getScheduledChanges();

        $expected = array(
            new AccountChange_ScheduledChange_Adsl($serviceId),
            new AccountChange_ScheduledChange_Wlr($serviceId),
        );

        $expectedAdslData = array(
            'intScheduleId' => '1',
            'dteChangeDate' => I18n_Date::fromString('01-09-2011', 'UTC'),
            'strNewProductName' => 'Product Two',
            'strProvisioningProfile' => null,
            'intNewSdi' => '2',
            'tariffId' => '1',
            'decCostPound' => '20.0000',
            'serviceId' => $serviceId,
            'intOldServiceDefinitionId' => '1',
            'intNewServiceDefinitionId' => '2',
            'intPromoCode' => 1,
            'strPromoCode' => 'ctr12mhp'
        );

        $expectedWlrData = array(
            'intScheduleId' => '1',
            'dteChangeDate' => I18n_Date::fromString('01-09-2030', 'UTC'),
            'strNewProductName' => 'Product Two',
            'decCostPound'  => '12.9900',
            'intProductChangeId' => '1',
            'serviceId' => $serviceId,
            'intOldComponentId' => '1',
            'intNewComponentId' => '2'
        );

        $this->assertEquals($expected, $actual);
        $this->assertEquals($expectedAdslData, $expected[0]->getData());
        $this->assertEquals($expectedWlrData, $expected[1]->getData());
    }

    /**
     * Tests for 'cancelChange'
     *
     * @covers AccountChange_ScheduledChange::cancelChange
     * @covers AccountChange_ScheduledChange::raiseServiceNotice
     * @covers AccountChange_ScheduledChange::getBusinessActor
     *
     * @return void
     */
    public function testCancelChange()
    {
        $serviceId = 919;

        $mockAutheDb = $this->getMock(
            'Db_Adaptor',
            array(
                'getActorByExternalUserId',
                'getBusinessActor'
            ),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $mockAutheDb->expects($this->any())
            ->method('getActorByExternalUserId')
            ->will($this->returnValue(2222));

        $mockAutheDb->expects($this->any())
            ->method('getBusinessActor')
            ->will($this->returnValue(array(array('intActorId' => 999, 'strUsername' => 'TestUser'))));

        Db_Manager::setAdaptor('Auth', $mockAutheDb);

        $serviceNotice = $this->getMock(
            'ServiceNoticeClient_ServiceNotice',
            array('write')
        );

        $serviceNoticeClient = $this->getMock(
            'ServiceNoticeClient_ServiceNoticeClient',
            array('createServiceNotice')
        );

        $serviceNoticeClient->expects($this->once())
            ->method('createServiceNotice')
            ->will($this->returnValue($serviceNotice));

        BusTier_BusTier::setClient('serviceNotices', $serviceNoticeClient);

        $adslScheduledChange = $this->getMock(
            'AccountChange_ScheduledChange_Adsl',
            array('cancelRetentionOffer'),
            array($serviceId)
        );

        $adslScheduledChange->expects($this->once())
            ->method('cancelRetentionOffer');

        $wlrScheduledChange = $this->getMock(
            'AccountChange_ScheduledChange_Wlr',
            array('cancelRetentionOffer'),
            array($serviceId)
        );

        $wlrScheduledChange->expects($this->once())
            ->method('cancelRetentionOffer');

        $scheduledChange = $this->getMock(
            'AccountChange_ScheduledChange',
            array('getScheduledChanges'),
            array(),
            '',
            false
        );

        $changesToCancel = array(
            $adslScheduledChange,
            $wlrScheduledChange
        );

        $scheduledChange->cancelChange(new Auth_BusinessActor(), $changesToCancel);

        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
    }
}
