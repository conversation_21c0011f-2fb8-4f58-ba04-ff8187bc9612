<?php
/**
 * Account Change Action Manager
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_Manager.class.php,v 1.3 2009-02-04 17:07:19 bselby Exp $
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 * @since     File available since 2008-09-01
 */
/**
 * Account Change Action Manager class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      http://documentation.plus.net/index.php/AccountChange_Module
 */
class AccountChange_Action_Manager
{
    /**
     * Array of actions that need to be performed
     *
     * @var array
     */
    protected $arrActions = array();

    const ACTION_PRODUCTS = 'Products';
    const ACTION_LEGACY_COMPONENTS = 'LegacyComponents';
    const ACTION_ELLACOYA = 'Ellacoya';
    const ACTION_CBC = 'Cbc';
    const ACTION_RADIUS = 'Radius';
    const ACTION_STATIC_IP = 'StaticIp';
    const ACTION_BILLING = 'Billing';
    const ACTION_CONTRACT = 'Contract';
    const ACTION_HARDWARE = 'Hardware';
    const ACTION_APPOINTMENT = 'Appointment';
    const ACTION_REGRADE = 'Regrade';
    const ACTION_UPDATE_SUPPLIER_PRODUCT = 'UpdateSupplierProduct';
    const ACTION_GB_WLR_PRODUCT_SET_CHANGE_JL = 'GbWrProductSetChangeJl';
    const ACTION_GB_WLR_BULLGUARD_JL = 'GbWrBullGuardJl';
    const ACTION_LINE_RENTAL_SAVER_ADD = 'LineRentalSaverAdd';
    const ACTION_DISCOUNTS = 'Discounts';
    const ACTION_LTC_CONTRACTS = 'LtcContracts';
    const ACTION_CALLER_DISPLAY = 'CallerDisplay';
    const ACTION_CONSENT = 'Consent';
    const ACTION_REGISTER_CHARGES = 'RegisterCharges';
    const ACTION_BROADBAND_ONLY_CLI = 'BroadbandOnlyCli';

    /**
     * Valid Action Names
     *
     * @var array
     */
    public static $arrValidActions = array(
        self::ACTION_PRODUCTS,
        self::ACTION_LEGACY_COMPONENTS,
        self::ACTION_ELLACOYA,
        self::ACTION_CBC,
        self::ACTION_CALLER_DISPLAY,
        self::ACTION_RADIUS,
        self::ACTION_STATIC_IP,
        self::ACTION_BILLING,
        self::ACTION_CONTRACT,
        self::ACTION_HARDWARE,
        self::ACTION_APPOINTMENT,
        self::ACTION_REGRADE,
        self::ACTION_UPDATE_SUPPLIER_PRODUCT,
        self::ACTION_GB_WLR_PRODUCT_SET_CHANGE_JL,
        self::ACTION_GB_WLR_BULLGUARD_JL,
        self::ACTION_LINE_RENTAL_SAVER_ADD,
        self::ACTION_DISCOUNTS,
        self::ACTION_LTC_CONTRACTS,
        self::ACTION_CONSENT,
        self::ACTION_REGISTER_CHARGES,
        self::ACTION_BROADBAND_ONLY_CLI
    );

    /**
     * Array of options for each action. Associative array
     *
     * @var array
     */
    private $_arrActionOptions = array();

    /**
     * Service Id
     *
     * @var int
     */
    private $_intServiceId;

    /**
     * Constructor
     *
     * @param int   $intServiceId     Service id
     * @param array $arrActionNames   Action names
     * @param array $arrActionOptions Action options
     */
    public function __construct($intServiceId, array $arrActionNames = array(), array $arrActionOptions = array())
    {
        if (!preg_match('/^[0-9]*$/', $intServiceId)) {
            throw new AccountChange_Action_ManagerException(
                'Non numeric service id',
                AccountChange_Action_ManagerException::ERR_INVALID_SERVICE_ID_TYPE
            );
        }

        $this->_intServiceId     = (int)$intServiceId;
        $this->_arrActionOptions = $arrActionOptions;

        $this->initialise($arrActionNames);
    }

    /**
     * Initialise the object correctly
     *
     * @param array $arrActionNames Action names
     *
     * @return void
     */
    public function initialise(array $arrActionNames = array())
    {
        $this->validateActionNames($arrActionNames);
        $this->arrActions = $this->createActions($arrActionNames);
    }

    /**
     * Getter for the actions
     *
     * @return array
     */
    public function getActions()
    {
        return $this->arrActions;
    }

    /**
     * Validate that the action names are valid actions
     *
     * @param array $arrActionNames Action names
     *
     * @return void
     */
    protected function validateActionNames(array $arrActionNames)
    {
        foreach ($arrActionNames as $strActionName) {
            if (!in_array($strActionName, self::$arrValidActions)) {
                throw new AccountChange_Action_ManagerException(
                    'Invalid action name provided - ' . print_r($strActionName, 1),
                    AccountChange_Action_ManagerException::ERR_INVALID_ACTION_NAME
                );
            }
        }

        return true;
    }

    /**
     * Create the actions from the array of names sent in
     *
     * @param array $arrActionNames Action names
     *
     * @return array
     */
    public function createActions(array $arrActionNames = array())
    {
        Dbg_Dbg::write(
            'AccountChange_Action_Manager::createActions has created the following actions ' .
            implode(', ', $arrActionNames),
            'AccountChange'
        );

        $arrActions = array();
        $arrOptions = array();

        foreach ($arrActionNames as $strActionName) {
            $strClassName = 'AccountChange_Action_' . $strActionName;

            if (!class_exists($strClassName)) {
                throw new AccountChange_Action_ManagerException(
                    'Invalid action name provided - ' . print_r($strActionName, 1),
                    AccountChange_Action_ManagerException::ERR_INVALID_ACTION_NAME
                );
            }

            if (isset($this->_arrActionOptions[$strActionName])) {
                $arrOptions = $this->_arrActionOptions[$strActionName];
            }

            $objAction = new $strClassName($this->_intServiceId, $arrOptions);

            if (!$objAction instanceof AccountChange_Action) {
                throw new AccountChange_Action_ManagerException(
                    'Invalid action object created',
                    AccountChange_Action_ManagerException::ERR_INVALID_ACTION_OBJECT
                );
            }

            $arrActions[] = $objAction;
        }

        return $arrActions;
    }

    /**
     * Call the execute method on all the actions
     *
     * @return boolean
     */
    public function execute()
    {
        foreach ($this->arrActions as $objAction) {
            Dbg_Dbg::write(get_class($objAction) . '::execute has been fired up', 'AccountChange');
            $objAction->execute();
        }

        return true;
    }
}
