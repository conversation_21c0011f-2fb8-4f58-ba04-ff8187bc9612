<?php
/**
 * AccountChange Scheduling Helper test
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 Plusnet
 */
/**
 * AccountChange Scheduling Helper test
 *
 * Test class for AccountChange_SchedulingHelper
 *
 * @category  AccountChange
 * @package   AccountChange_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2014 Plusnet
 */
class AccountChange_SchedulingHelper_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Tear down functionality
     *
     * @see PHPUnit/Framework/PHPUnit_Framework_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        $registry = AccountChange_Registry::instance();
        $registry->reset();
    }

    /**
     * Test that getOveriddenChangeDate returns the date if a valid change date is
     * set in the registry
     *
     * @covers AccountChange_SchedulingHelper::getOveriddenChangeDate
     *
     * @return void
     **/
    public function testGetOveriddenChangeDateValidCase()
    {
        $changeDate = '1981-07-11';
        AccountChange_Registry::instance()->setEntry('overiddenChangeDate', $changeDate);
        $helper = new AccountChange_SchedulingHelper();
        $changeDateActual = $helper->getOveriddenChangeDate();
        $this->assertEquals($changeDate, $changeDateActual);
    }

    /**
     * Test that getOveriddenChangeDate returns the date if a valid change date is
     * set in the registry
     *
     * @covers AccountChange_SchedulingHelper::getOveriddenChangeDate
     *
     * @return void
     **/
    public function testGetOveriddenChangeDateValidCaseWithDateTime()
    {
        $expectedDate = '2022-01-03';
        $changeDate = DateTime::createFromFormat('Y-m-d', '2022-01-03');
        AccountChange_Registry::instance()->setEntry('overiddenChangeDate', $changeDate);
        $helper = new AccountChange_SchedulingHelper();
        $changeDateActual = $helper->getOveriddenChangeDate();
        $this->assertEquals($expectedDate, $changeDateActual);
    }

    /**
     * Test that getOveriddenChangeDate returns null if there's no date set in the
     * registry
     *
     * @covers AccountChange_SchedulingHelper::getOveriddenChangeDate
     *
     * @return void
     **/
    public function testGetOveriddenChangeDateNoDateSet()
    {
        $helper = new AccountChange_SchedulingHelper();
        $changeDateActual = $helper->getOveriddenChangeDate();
        $this->assertNull($changeDateActual);
    }

    /**
     * Test that getOveriddenChangeDate returns null if the change date stored in the
     * registry isn't valid
     *
     * @covers AccountChange_SchedulingHelper::getOveriddenChangeDate
     *
     * @return void
     **/
    public function testGetOveriddenChangeDateInvalidDateInRegistry()
    {
        $changeDate = "This doesn't look like a date at all..";
        AccountChange_Registry::instance()->setEntry('overiddenChangeDate', $changeDate);
        $helper = new AccountChange_SchedulingHelper();
        $changeDateActual = $helper->getOveriddenChangeDate();
        $this->assertNull($changeDateActual);
    }

    /**
     * Test that calculateScheduledChangeDate calculates the date correctly when there's an engineer
     * appointment set (i.e. the date is returned as null)
     *
     * @covers AccountChange_SchedulingHelper::calculateScheduledChangeDate
     *
     * @return void
     **/
    public function testCalculateScheduledChangeDateHasEngineerAppointment()
    {
        $sid = 123456;

        $helper = new AccountChange_SchedulingHelper();

        $result = $helper->calculateScheduledChangeDate($sid, true);

        $this->assertNull($result);
    }

    /**
     * Test that calculateScheduledChangeDate calculates the date correctly when there's an overidden
     * change date
     * @dataProvider overrideChangeDateProvider
     * @covers AccountChange_SchedulingHelper::calculateScheduledChangeDate
     *
     * @return void
     **/
    public function testCalculateScheduledChangeDateOveriddenChangeDate($isRecontract, $isInstantChange, $expectedDate)
    {
        $sid = 123456;
        $overiddenChangeDate = '1981-07-11';
        $nextInvoice = '2014-07-01';

        AccountChange_Registry::instance()->setEntry('overiddenChangeDate', $overiddenChangeDate);

        if ($isRecontract) {
            AccountChange_Registry::instance()->setEntry('isRecontract', $isRecontract);
        }

        if ($isInstantChange) {
            AccountChange_Registry::instance()->setEntry('instantRecontract', true);
        }

        $coreService = $this->getCoreServiceMock($nextInvoice, $sid);

        $mockHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('includeLegacyFiles', 'getCoreService'),
            array()
        );

        $mockHelper
            ->expects($this->once())
            ->method('includeLegacyFiles');

        $mockHelper
            ->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));

        $result = $mockHelper->calculateScheduledChangeDate($sid, false);

        $this->assertEquals($expectedDate, $result);
    }

    /**
     * @return array
     */
    public function overrideChangeDateProvider()
    {
        return [
            'is recontract, not instant' => [true, false, '2014-07-01 00:00:00'],
            'is recontract, is instant' => [true, true, '1981-07-11'],
            'not recontract, not instant' => [false, false, '1981-07-11']
        ];
    }

    /**
     * Test that calculateScheduledChangeDate calculates the date correctly when the change date
     * is today
     *
     * @covers AccountChange_SchedulingHelper::calculateScheduledChangeDate
     *
     * @return void
     **/
    public function testCalculateScheduledChangeDateNextInvoiceIsToday()
    {
        $sid = 123456;
        $nextInvoice = I18n_Date::fromString('2014-07-01');
        $today = '01-07-2014';
        $nextBillingDate = '2014-08-01 00:00:00';
        $nextNextDate = I18n_Date::fromString($nextBillingDate);

        $coreService = $this->getCoreServiceMock($nextInvoice, $sid);
        $coreService
            ->expects($this->any())
            ->method('getNextNextInvoiceDate')
            ->will($this->returnValue($nextNextDate));


        $mockHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array(
                'includeLegacyFiles',
                'getCoreService',
                'getToday'
            ),
            array()
        );

        $mockHelper
            ->expects($this->once())
            ->method('includeLegacyFiles');

        $mockHelper
            ->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));

        $mockHelper
            ->expects($this->once())
            ->method('getToday')
            ->will($this->returnValue($today));

        $result = $mockHelper->calculateScheduledChangeDate($sid, false);

        $this->assertEquals($nextBillingDate, $result);
    }

    /**
     * Test that calculateScheduledChangeDate calculates the date correctly when there's no special
     * conditions (i.e. should return the next invoice date on the account)
     *
     * @covers AccountChange_SchedulingHelper::calculateScheduledChangeDate
     *
     * @return void
     **/
    public function testCalculateScheduledChangeDateNextInvoiceIsNotToday()
    {
        $sid = 123456;
        $nextInvoice = I18n_Date::fromString('2014-07-01');
        $today = '15-06-2014';

        $coreService = $this->getCoreServiceMock($nextInvoice, $sid);

        $mockHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array(
                'includeLegacyFiles',
                'getCoreService',
                'getToday'
            ),
            array()
        );

        $mockHelper
            ->expects($this->once())
            ->method('includeLegacyFiles');

        $mockHelper
            ->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));

        $mockHelper
            ->expects($this->once())
            ->method('getToday')
            ->will($this->returnValue($today));

        $result = $mockHelper->calculateScheduledChangeDate($sid, false);

        $this->assertEquals($nextInvoice->toMysql(), $result);
    }

    /**
     * Setup method for the core service mock
     *
     * @param string $nextInvoiceDate Return value of getNextInvoiceDate
     *
     * @return Core_Service
     **/
    protected function getCoreServiceMock($nextInvoiceDate, $serviceId, $nextNextInvoiceDate = null)
    {
        $coreService = $this->getMock(
            'Core_Service',
            array('getNextInvoiceDate', 'getNextNextInvoiceDate', 'getServiceId'),
            array()
        );

        $coreService
            ->expects($this->any())
            ->method('getNextInvoiceDate')
            ->will($this->returnValue(I18n_Date::fromString($nextInvoiceDate)));

        if (!is_null($nextNextInvoiceDate)) {
            $coreService
                ->expects($this->any())
                ->method('getNextNextInvoiceDate')
                ->will(
                    $this->returnValue(
                        I18n_Date::fromString($nextNextInvoiceDate)
                    )
                );
        }

        $coreService
            ->expects($this->any())
            ->method('getServiceId')
            ->will($this->returnValue($serviceId));

        return $coreService;
    }

    /**
     * Tests that calculateScheduledChangeDate returns null if there's an engineers appointment
     *
     * @return void
     **/
    public function testCalculateScheduledChangeDateReturnsFalseIfAppointmentSet()
    {
        $helper = new AccountChange_SchedulingHelper();
        $this->assertNull($helper->calculateScheduledChangeDate($sid, true));
    }

    /**
     * [testCorrectServiceNoteIsRaised description]
     *
     * @param string $currentProductName  [description]
     * @param string $newProductName      [description]
     * @param I18n_Date $overiddenChangeDate [description]
     * @param I18n_Date $changeDate          [description]
     * @param string $expected            [description]
     *
     * @return void
     *
     * @covers AccountChange_SchedulingHelper::buildProductChangeServiceNotice
     *
     * @dataProvider prodiveDataFromTestCorrectServiceNoteIsRaised
     */
    public function testCorrectServiceNoteIsRaised($currentProductName, $newProductName, I18n_Date $overiddenChangeDate = null, I18n_Date $changeDate = null, $expected = null)
    {
        $helper = new AccountChange_SchedulingHelper();

        $actual = $helper->buildProductChangeServiceNotice($currentProductName, $newProductName, $overiddenChangeDate, $changeDate);

        $this->assertContains($expected, $actual->getComment());
    }

    /**
     * @return array[]
     */
    public function prodiveDataFromTestCorrectServiceNoteIsRaised()
    {
        return array(
            array('foo', 'bar', null, I18n_Date::fromString('2013-08-01'), 'billing date, 01-08-2013'),
            array('foo', 'bar', I18n_Date::fromString('2014-08-01'), I18n_Date::fromString('2013-08-01'), 'place on 01-08-2014'),
            array('foo', 'bar', null, null, 'engineer appointment')
        );
    }

    public function testCalculateChangeDateByOrderRemoteActivation()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $date = I18n_Date::fromString(2021-12-13)->getTimestamp();

        $mockSchedulingHelper->expects($this->once())
            ->method('now')
            ->will($this->returnValue($date));

        $expectedDate = I18n_Date::fromString("2021-12-16");

        $mockSchedulingHelper->expects($this->once())
            ->method('addWorkingDays')
            ->with($date, 3)
            ->will($this->returnValue($expectedDate->getTimestamp()));

        $mockFttpHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->exactly(2))
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockFttpHelper));

        $mockFttpHelper->expects($this->exactly(2))
            ->method('isFttpProduct')
            ->withConsecutive([6666], [5555])
            ->willReturnOnConsecutiveCalls(true, false);

        $lineCheckMock = $this->getLineCheckMock();

        $mockSchedulingHelper->expects($this->once())
            ->method('getLineCheck')
            ->will($this->returnValue($lineCheckMock));

        $lineCheckMock->expects($this->once())
            ->method('getFttpInstallProcess')
            ->will($this->returnValue(LineCheck_Result::REMOTE_ACTIVATION));

        $lineCheckMock->expects($this->once())
            ->method('getFttpLeadTime')
            ->will($this->returnValue(3));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi', 'getCurrentSdi'),
            array()
        );

        $mockChangeApi->expects($this->once())
            ->method('getCurrentSdi')
            ->will($this->returnValue(5555));

        $mockChangeApi->expects($this->once())
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals("2021-12-16", $returned);
    }

    public function testCalculateChangeDateByOrderFttpAppointment()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockFttpHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->once())
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockFttpHelper));

        $mockFttpHelper->expects($this->once())
            ->method('isFttpProduct')
            ->with(6666)
            ->will($this->returnValue(true));

        $lineCheckMock = $this->getLineCheckMock();

        $mockSchedulingHelper->expects($this->once())
            ->method('getLineCheck')
            ->will($this->returnValue($lineCheckMock));

        $lineCheckMock->expects($this->once())
            ->method('getFttpInstallProcess')
            ->will($this->returnValue(LineCheck_Result::STAGE_1));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi'),
            array()
        );

        $mockChangeApi->expects($this->once())
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $arrAappointment = array(
            "live" => array("date" => "2021-12-16"),
            "notes" => "some notes");

        $accountChangeAppointment = new AccountChange_AccountChangeAppointment($arrAappointment);

        $mockChangeOrder->setAppointment($accountChangeAppointment);

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals("2021-12-16", $returned);
    }

    public function testCalculateChangeDateByOrderFttpWithoutAppointment()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockFttpHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->once())
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockFttpHelper));

        $mockFttpHelper->expects($this->once())
            ->method('isFttpProduct')
            ->with(6666)
            ->will($this->returnValue(true));

        $lineCheckMock = $this->getLineCheckMock();

        $mockSchedulingHelper->expects($this->once())
            ->method('getLineCheck')
            ->will($this->returnValue($lineCheckMock));

        $lineCheckMock->expects($this->once())
            ->method('getFttpInstallProcess')
            ->will($this->returnValue(LineCheck_Result::STAGE_1));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi'),
            array()
        );

        $mockChangeApi->expects($this->exactly(1))
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $arrAappointment = array(
            "manual" => array(),
            "notes" => "some notes");

        $accountChangeAppointment = new AccountChange_AccountChangeAppointment($arrAappointment);

        $mockChangeOrder->setAppointment($accountChangeAppointment);

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals(null, $returned);
    }

    public function testCalculateChangeDateByOrderFttpToFttp()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $date = I18n_Date::fromString(2021-12-13)->getTimestamp();

        $mockSchedulingHelper->expects($this->once())
            ->method('now')
            ->will($this->returnValue($date));

        $expectedDate = I18n_Date::fromString("2021-12-14");

        $mockSchedulingHelper->expects($this->once())
            ->method('addWorkingDays')
            ->with($date, 1)
            ->will($this->returnValue($expectedDate->getTimestamp()));

        $mockFttpHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->exactly(3))
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockFttpHelper));

        $mockFttpHelper->expects($this->exactly(3))
            ->method('isFttpProduct')
            ->withConsecutive([6666], [5555], [6666])
            ->willReturnOnConsecutiveCalls(true, true, true);

        $lineCheckMock = $this->getLineCheckMock();

        $mockSchedulingHelper->expects($this->once())
            ->method('getLineCheck')
            ->will($this->returnValue($lineCheckMock));

        $lineCheckMock->expects($this->once())
            ->method('getFttpInstallProcess')
            ->will($this->returnValue(LineCheck_Result::REMOTE_ACTIVATION));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi', 'getCurrentSdi'),
            array()
        );

        $mockChangeApi->expects($this->exactly(2))
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $mockChangeApi->expects($this->once())
            ->method('getCurrentSdi')
            ->will($this->returnValue(5555));

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals("2021-12-14", $returned);
    }

    public function testCalculateChangeDateByOrderInstantChange()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array(),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(false));

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals(date('Y-m-d'), $returned);
    }

    public function testCalculateChangeDateByOrderFttcSelfInstall()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockFttpHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct','isSogeaProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->any())
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockFttpHelper));

        $mockSchedulingHelper->expects($this->once())
            ->method('calculateSelfInstallDate')
            ->will($this->returnValue("2021-12-16"));

        $mockSchedulingHelper->expects($this->once())
            ->method('isFibreProduct')
            ->will($this->returnValue(true));

        $mockFttpHelper->expects($this->once())
            ->method('isFttpProduct')
            ->with(6666)
            ->will($this->returnValue(false));

        $mockFttpHelper->expects($this->once())
            ->method('isSogeaProduct')
            ->with(6666)
            ->will($this->returnValue(false));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi',
                'getCurrentSdi'),
            array()
        );

        $mockChangeApi->expects($this->any())
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $mockChangeApi->expects($this->once())
            ->method('getCurrentSdi')
            ->will($this->returnValue(6666));

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals("2021-12-16", $returned);
    }

    public function testCalculateChangeDateByOrderAdsl()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockFttpHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct','isSogeaProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->any())
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockFttpHelper));

        $mockSchedulingHelper->expects($this->once())
            ->method('isFibreProduct')
            ->will($this->returnValue(false));

        $mockFttpHelper->expects($this->once())
            ->method('isFttpProduct')
            ->with(6666)
            ->will($this->returnValue(false));

        $mockFttpHelper->expects($this->once())
            ->method('isSogeaProduct')
            ->with(6666)
            ->will($this->returnValue(false));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi'),
            array()
        );

        $mockChangeApi->expects($this->any())
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $coreService = $this->getCoreServiceMock("2021-12-16", 123);

        $mockSchedulingHelper->expects($this->once())
            ->method('getCoreService')
            ->will($this->returnValue($coreService));

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals("2021-12-16 00:00:00", $returned);
    }

    /**
     * @dataProvider recontractChangeDateProvider
     * @return void
     */
    public function testCalculateChangeDateByOrderRecontract($next, $nextNext, $expected)
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            ['getIsScheduledChange','getBackDatedDate'],
            []
        );

        $mockChangeOrder->expects($this->once())
            ->method('getBackDatedDate')
            ->willReturn(null);

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->willReturn(true);

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            ['isRecontract'],
            []
        );

        $mockChangeApi->expects($this->once())
            ->method('isRecontract')
            ->willReturn(true);

        $coreService = $this->getCoreServiceMock($next, 123, $nextNext);

        $mockSchedulingHelper->expects($this->once())
            ->method('getCoreService')
            ->willReturn($coreService);

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals($expected, $returned);
    }


    private function getLineCheckMock()
    {
        return $this->getMock(
            'LineCheck_Result',
            array('getFttpLeadTime',
                'getFttpInstallProcess'),
            array()
        );
    }

    private function getSchedulingMock()
    {
        $mockHelper = $this->getMock(
            'AccountChange_SchedulingHelper',
            array('includeLegacyFiles',
                'getCoreService',
                'getBroadbandOnlyHelper',
                'getLineCheck',
                'now',
                'addWorkingDays',
                'isFibreProduct',
                'calculateSelfInstallDate'),
            array()
        );

        $mockHelper
            ->expects($this->atMost(1))
            ->method('includeLegacyFiles');

        return $mockHelper;
    }

    /**
     * @return array
     */
    public function recontractChangeDateProvider()
    {
        $today = new DateTime('today midnight');
        $tomorrow = new DateTime('tomorrow midnight');
        $nextMonth = new DateTime('next month midnight');
        $inputFormat = 'Y-m-d';
        $expectedResultFormat = 'Y-m-d H:i:s';

        return [
            'Tomorrow' => [
                'next' => $tomorrow->format($inputFormat),
                'nextnext' => null,
                'expected' => $tomorrow->format($expectedResultFormat)
            ],
            'Today' => [
                'next' => $today->format($inputFormat),
                'nextnext' => $nextMonth->format($inputFormat),
                'expected' => $nextMonth->format($expectedResultFormat)
            ]
        ];
    }

    /**
     * @return void
     */
    public function testCalculateChangeDateByOrderBackdatedDate()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            ['getBackDatedDate'],
            []
        );

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            [],
            []
        );

        $backDatedDate = new DateTime('yesterday midnight');
        $mockChangeOrder->expects($this->exactly(2))
            ->method('getBackDatedDate')
            ->willReturn($backDatedDate);

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals($backDatedDate, $returned);
    }

    public function testCalculateChangeDateByOrderDoesNotReturnValueIfOneStageAndAppointmentNotSuppliedForAccountChangeHouseMoveScenario()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $mockFttpHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->once())
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockFttpHelper));

        $mockFttpHelper->expects($this->once())
            ->method('isFttpProduct')
            ->with(6666)
            ->will($this->returnValue(true));

        $lineCheckMock = $this->getLineCheckMock();

        $mockSchedulingHelper->expects($this->once())
            ->method('getLineCheck')
            ->will($this->returnValue($lineCheckMock));

        $lineCheckMock->expects($this->once())
            ->method('getFttpInstallProcess')
            ->will($this->returnValue(LineCheck_Result::STAGE_1));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi'),
            array()
        );

        $mockChangeApi->expects($this->exactly(1))
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $accountChangeAppointment = array();

        $mockChangeOrder->setAppointment($accountChangeAppointment);

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals(null, $returned);
    }

    public function testCalculateChangeDateByOrderSogeaToSogea()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $date = I18n_Date::fromString(2021-12-13)->getTimestamp();

        $mockSchedulingHelper->expects($this->once())
            ->method('now')
            ->will($this->returnValue($date));

        $expectedDate = I18n_Date::fromString("2021-12-14");

        $mockSchedulingHelper->expects($this->once())
            ->method('addWorkingDays')
            ->with($date, 1)
            ->will($this->returnValue($expectedDate->getTimestamp()));

        $mockBBOHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct','isSogeaProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->any())
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockBBOHelper));

        $mockBBOHelper->expects($this->once())
            ->method('isFttpProduct')
            ->with(6666)
            ->will($this->returnValue(false));

        $mockBBOHelper->expects($this->any())
            ->method('isSogeaProduct')
            ->with(6666)
            ->will($this->returnValue(true));

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi',
                'getCurrentSdi'),
            array()
        );

        $mockChangeApi->expects($this->exactly(2))
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $mockChangeApi->expects($this->once())
            ->method('getCurrentSdi')
            ->will($this->returnValue(6666));

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals("2021-12-14 02:00:00", $returned);
    }

    public function testCalculateChangeDateByOrderNonSogeaToSogea()
    {
        $mockSchedulingHelper = $this->getSchedulingMock();

        $date = I18n_Date::fromString(2021-12-13)->getTimestamp();

        $mockSchedulingHelper->expects($this->once())
            ->method('now')
            ->will($this->returnValue($date));

        $expectedDate = I18n_Date::fromString("2021-12-16");

        $mockSchedulingHelper->expects($this->once())
            ->method('addWorkingDays')
            ->with($date, 3)
            ->will($this->returnValue($expectedDate->getTimestamp()));

        $mockBBOHelper = $this->getMock(
            'AccountChange_BroadbandOnlyHelper',
            array('isFttpProduct','isSogeaProduct'),
            array()
        );

        $mockSchedulingHelper->expects($this->any())
            ->method('getBroadbandOnlyHelper')
            ->will($this->returnValue($mockBBOHelper));

        $mockBBOHelper->expects($this->once())
            ->method('isFttpProduct')
            ->with(6666)
            ->will($this->returnValue(false));

        $mockBBOHelper->expects($this->exactly(2))
            ->method('isSogeaProduct')
            ->withConsecutive([6666], [5555])
            ->willReturnOnConsecutiveCalls(true, false, true);

        $mockChangeOrder = $this->getMock(
            'AccountChange_AccountChangeOrder',
            array('getIsScheduledChange'),
            array()
        );

        $mockChangeOrder->expects($this->once())
            ->method('getIsScheduledChange')
            ->will($this->returnValue(true));

        $mockChangeApi = $this->getMock(
            'AccountChange_PerformChangeApi',
            array('getToSdi',
                'getCurrentSdi'),
            array()
        );

        $mockChangeApi->expects($this->exactly(2))
            ->method('getToSdi')
            ->will($this->returnValue(6666));

        $mockChangeApi->expects($this->once())
            ->method('getCurrentSdi')
            ->will($this->returnValue(5555));

        $returned = $mockSchedulingHelper->calculateChangeDateByOrder($mockChangeOrder, $mockChangeApi);
        $this->assertEquals("2021-12-16 02:00:00", $returned);
    }
}
