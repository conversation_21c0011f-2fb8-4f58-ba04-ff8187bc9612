<?php
/**
 * Ellacoya action
 *
 * Action that performs all ellacoya related changes
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @version   CVS: $Id: Action_Ellacoya.class.php,v 1.2 2009-01-27 07:07:43 bselby Exp $
 * @since     File available since 2008-09-02
 */
/**
 * Ellacoya action class
 *
 * @package   AccountChange
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 */
class AccountChange_Action_Ellacoya extends AccountChange_Action
{
    /**
     * Service Definition Id
     *
     * @var int
     */
    private $intServiceDefinitionId = null;

    /**
     * Execute the action
     *
     * @return void
     */
    public function execute()
    {
        $registry = AccountChange_Registry::instance();
        $this->intServiceDefinitionId = $registry->getEntry('intNewServiceDefinitionId');

        $this->updateEllacoya();
        $this->setConnectionProfiles();
    }

    /**
     * Update the ellacoya data
     *
     * @return void
     */
    protected function updateEllacoya()
    {
        $this->includeLegacyFiles();
        adslUpdateEllacoya($this->intServiceId, 'Update Subscriber (ADSL to ADSL Regrade)');
    }

    /**
     * Set the connection profiles
     *
     * @return void
     */
    protected function setConnectionProfiles()
    {
        // This code has been lifted from the original process. It could do with refactoring
        // Once refactoring has occurred we can cover it in unit tests
        $this->includeLegacyFiles();

        $arrNewAccountHardware = adslGetAccountHardware($this->intServiceDefinitionId);

        $objAdslService = new C_Core_ADSLService(null, null, $this->intServiceId);
        $objProvidedService = $objAdslService->getProvidedService();

        // New profiles
        $objNewConnProfileGroup = CConnectionProfileGroup::getInstanceForSdi($this->intServiceDefinitionId);

        if (null != $objNewConnProfileGroup) {
            $arrNewConnProfileGroup = $objNewConnProfileGroup->getProfiles();

            // Fix for problem 41889
            if (!$objAdslService->isOnMaxProduct()) {

                if ($objProvidedService != null) {

                    // Current profiles
                    $objCurrConnProfileGroup = CConnectionProfileGroup::getInstanceForSdi($objAdslService->getServiceDefinitionID());
                    $arrCurrConnProfileGroup = $objCurrConnProfileGroup->getProfiles();
                    $objCurrConnProfile = $objProvidedService->getConnectionProfile();

                    // Don't alter if custom profile
                    if (!$objCurrConnProfile->isCustom()) {

                        // Preserve timeout option if both conenction groups have > 1 profiles
                        if (count($arrCurrConnProfileGroup) > 1 && count($arrNewAccountHardware) > 1) {

                            foreach ($arrNewConnProfileGroup as $objProfile) {

                                if ($objCurrConnProfile->isTimeout() == $objProfile->isTimeout()) {

                                    $objNewProfile = $objProfile;
                                }
                            }
                        } else { // Assign default profile for that group

                            $objNewProfile = $objNewConnProfileGroup->getDefaultProfile();
                        }

                        $objAdslService->setConnectionProfile($objNewProfile, ADSL_PROVIDED_EVENT_PRODUCT_CHANGE);
                    }
                } else {

                    $objNewProfile = $objNewConnProfileGroup->getDefaultProfile();
                    $objAdslService->setConnectionProfile($objNewProfile, ADSL_PROVIDED_EVENT_PRODUCT_CHANGE);
                }
            }
        }
    }

    /**
     * Include all the legacy files we need, so we can also mock this for unit tests
     *
     * @return void
     */
    public function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/adsl-access.inc';
        require_once '/local/data/mis/common_library_functions/common_database_libraries/common-adsl-access.inc';
        require_once '/local/data/mis/database/class_libraries/Customers/C_Core_Service.php';
        require_once '/local/data/mis/database/class_libraries/Customers/C_Core_Service.php';
        require_once '/local/data/mis/database/class_libraries/Customers/C_Core_ADSLService.php';
        require_once '/local/data/mis/database/class_libraries/Products/CConnectionProfile.php';
        require_once '/local/data/mis/database/class_libraries/Products/CConnectionProfileGroup.php';
    }
}
